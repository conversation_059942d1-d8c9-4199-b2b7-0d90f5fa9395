Phase 3B Form Component Migration Inventory
===========================================

Generated: $(date)
Total Components Found: 24 instances across 8 files

COMPONENT BREAKDOWN:
===================

DxComboBox: 12 instances
- ConnectionManager.razor: 3 instances (lines 110, 121, 129)
- ModelTab.razor: 2 instances (lines 12, 26)
- DeviceModelsManagerView.razor: 1 instance (line 138)
- DeviceConnectionsPanel.razor: 3 instances (lines 298, 328, 351)

DxTextBox: 3 instances
- BasicInfoTab.razor: 1 instance (line 12)
- BarretTextBox.razor: 1 instance (line 3) - WRAPPER COMPONENT
- DeviceRoleSelector.razor: 1 instance (line 26)

DxSpinEdit: 1 instance
- BarretNumberBox.razor: 1 instance (line 3) - WRAPPER COMPONENT

DxCheckBox: 1 instance
- BarretCheckBox.razor: 1 instance (line 3) - WRAPPER COMPONENT

MIGRATION PRIORITY ORDER:
========================

1. DxTextBox (3 instances) - Lowest complexity
2. DxCheckBox (1 instance) - Simple boolean binding
3. DxSpinEdit (1 instance) - Numeric validation
4. DxComboBox (12 instances) - Highest complexity

SPECIAL CONSIDERATIONS:
======================

Wrapper Components:
- BarretTextBox.razor - Custom wrapper around DxTextBox
- BarretCheckBox.razor - Custom wrapper around DxCheckBox  
- BarretNumberBox.razor - Custom wrapper around DxSpinEdit

These wrapper components will need special handling to maintain their abstraction layer.

FILES TO MIGRATE:
================

Direct Component Usage:
- ./Barret.Web.Server/Shared/Components/DeviceManagers/ConnectionManager.razor
- ./Barret.Web.Server/Shared/Components/DeviceEditors/Tabs/BasicInfoTab.razor
- ./Barret.Web.Server/Shared/Components/DeviceEditors/Tabs/ModelTab.razor
- ./Barret.Web.Server/Features/Admin/Components/DeviceModelsManagerView.razor
- ./Barret.Web.Server/Features/Vehicles/Editor/Components/Devices/Components/DeviceConnectionsPanel.razor
- ./Barret.Web.Server/Features/Vehicles/Editor/Components/Devices/Components/Common/DeviceRoleSelector.razor

Wrapper Components:
- ./Barret.Web.Server/Features/Shared/Components/BarretDevExpress/BarretTextBox.razor
- ./Barret.Web.Server/Features/Shared/Components/BarretDevExpress/BarretCheckBox.razor
- ./Barret.Web.Server/Features/Shared/Components/BarretDevExpress/BarretNumberBox.razor
