{"name": "barret-vehicle-configurator", "version": "1.0.0", "description": "Barret Vehicle Configurator", "scripts": {"build:css": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --minify", "build:css:dev": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css", "watch:css": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --watch", "watch:css:poll": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --watch --poll", "purge:css": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --minify --purge", "analyze:css": "tailwindcss -i ./Barret.Web.Server/wwwroot/css/Styles/main.css -o ./Barret.Web.Server/wwwroot/css/Styles/dist.css --content './Barret.Web.Server/**/*.{razor,html,cshtml}' --verbose"}, "dependencies": {"tailwindcss": "^3.4.17"}}