# Component Development Guidelines

## Overview

This document establishes consistent patterns and best practices for developing components in the Barret Vehicle Configurator Blazor application. All new components should follow these guidelines to ensure architectural consistency and maintainability.

## Architecture Principles

### MVVM Pattern Implementation

#### ViewModel Structure
All ViewModels must inherit from `ViewModelBase` and follow these patterns:

```csharp
public class ExampleViewModel : ViewModelBase, IDisposable
{
    // Use [Reactive] attributes for properties that trigger UI updates
    [Reactive] public string Name { get; set; } = string.Empty;
    [Reactive] public bool IsEnabled { get; set; } = true;
    
    // Use ReactiveCommand for user actions
    public ReactiveCommand<Unit, Unit> SaveCommand { get; }
    public ReactiveCommand<Unit, Unit> CancelCommand { get; }
    
    public ExampleViewModel(ILogger<ExampleViewModel> logger) : base(logger)
    {
        // Create commands using the base class helpers
        SaveCommand = CreateCommand(SaveAsync, 
            this.WhenAnyValue(x => x.Name).Select(name => !string.IsNullOrEmpty(name)),
            "Failed to save");
            
        CancelCommand = CreateCommand(CancelAsync, errorMessage: "Failed to cancel");
    }
    
    private async Task SaveAsync()
    {
        // Implementation here
    }
    
    private async Task CancelAsync()
    {
        // Implementation here
    }
}
```

#### View Structure
All Views must inherit from `ViewBase<TViewModel>` and follow these patterns:

```razor
@inherits ViewBase<ExampleViewModel>
@implements IDisposable

<div class="example-container">
    <!-- Use Radzen components exclusively -->
    <RadzenFormField Text="Name" Variant="Variant.Outlined">
        <RadzenTextBox @bind-Value="@ViewModel.Name" 
                       Placeholder="Enter name..." 
                       class="w-full" />
    </RadzenFormField>
    
    <div class="flex gap-2 mt-4">
        <RadzenButton Text="Save" 
                      Icon="save"
                      ButtonStyle="ButtonStyle.Primary"
                      Click="@(() => ViewModel.SaveCommand.Execute())"
                      Disabled="@(!ViewModel.SaveCommand.CanExecute.FirstOrDefault())" />
                      
        <RadzenButton Text="Cancel" 
                      Icon="cancel"
                      ButtonStyle="ButtonStyle.Light"
                      Click="@(() => ViewModel.CancelCommand.Execute())" />
    </div>
</div>
```

```csharp
// ExampleView.razor.cs
public partial class ExampleView
{
    protected override void SetupSubscriptions()
    {
        // Subscribe to ViewModel properties that should trigger UI updates
        SubscribeToProperty(vm => vm.Name);
        SubscribeToProperty(vm => vm.IsEnabled);
        
        // Subscribe to command CanExecute changes
        this.WhenAnyValue(x => x.ViewModel.SaveCommand)
            .Where(cmd => cmd != null)
            .SelectMany(cmd => cmd.CanExecute)
            .Subscribe(_ => SafeStateHasChanged())
            .DisposeWith(Disposables);
    }
}
```

## Component Library Standards

### Radzen Blazor Components

**Use Radzen components exclusively for all new development:**

#### Form Components
```razor
<!-- Text Input -->
<RadzenFormField Text="Label" Variant="Variant.Outlined">
    <RadzenTextBox @bind-Value="@Value" Placeholder="Placeholder..." class="w-full" />
</RadzenFormField>

<!-- Dropdown -->
<RadzenFormField Text="Label" Variant="Variant.Outlined">
    <RadzenDropDown @bind-Value="@SelectedValue" 
                    Data="@Items" 
                    TextProperty="Name" 
                    ValueProperty="Id"
                    Placeholder="Select..." 
                    class="w-full" />
</RadzenFormField>

<!-- Numeric Input -->
<RadzenFormField Text="Label" Variant="Variant.Outlined">
    <RadzenNumeric @bind-Value="@NumericValue" 
                   Min="0" 
                   Max="100" 
                   class="w-full" />
</RadzenFormField>

<!-- Checkbox -->
<RadzenCheckBox @bind-Value="@BoolValue" Name="checkbox1" />
<RadzenLabel Text="Label" Component="checkbox1" class="ml-2" />
```

#### Data Display Components
```razor
<!-- Data Grid -->
<RadzenDataGrid Data="@Items" 
                TItem="ItemDto"
                AllowFiltering="true"
                AllowPaging="true"
                PageSize="20"
                class="barret-data-grid">
    <Columns>
        <RadzenDataGridColumn TItem="ItemDto" Property="Name" Title="Name" />
        <RadzenDataGridColumn TItem="ItemDto" Property="Description" Title="Description" />
        <RadzenDataGridColumn TItem="ItemDto" Title="Actions" Sortable="false" Filterable="false">
            <Template Context="item">
                <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Light" Size="ButtonSize.Small" />
                <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Danger" Size="ButtonSize.Small" />
            </Template>
        </RadzenDataGridColumn>
    </Columns>
</RadzenDataGrid>
```

#### Button Components

**Use centralized button styling classes for consistency. See [Button Styling Guide](button-styling-guide.md) for complete documentation.**

```razor
<!-- Form Footer Buttons -->
<div class="barret-btn-group justify-end">
    <RadzenButton Text="Cancel"
                  Icon="cancel"
                  ButtonStyle="ButtonStyle.Light"
                  Click="@(() => ViewModel.CancelCommand.Execute())"
                  class="barret-btn barret-form-btn" />
    <RadzenButton Text="Save"
                  Icon="save"
                  ButtonStyle="ButtonStyle.Primary"
                  Click="@(() => ViewModel.SaveCommand.Execute())"
                  Disabled="@(!ViewModel.SaveCommand.CanExecute.FirstOrDefault())"
                  class="barret-btn barret-form-btn" />
</div>

<!-- Page Header Buttons -->
<RadzenButton Text="Add Device"
              Icon="add"
              ButtonStyle="ButtonStyle.Primary"
              Click="@(() => ViewModel.AddCommand.Execute())"
              class="barret-btn barret-header-btn" />

<!-- Grid Action Buttons -->
<div class="barret-btn-group-sm">
    <RadzenButton Icon="edit"
                  Variant="Variant.Outlined"
                  ButtonStyle="ButtonStyle.Primary"
                  Size="ButtonSize.Small"
                  Click="@(() => EditItem(item))"
                  title="Edit"
                  class="barret-btn barret-action-btn" />
    <RadzenButton Icon="delete"
                  Variant="Variant.Outlined"
                  ButtonStyle="ButtonStyle.Danger"
                  Size="ButtonSize.Small"
                  Click="@(() => DeleteItem(item))"
                  title="Delete"
                  class="barret-btn barret-action-btn" />
</div>
```

#### Dialog Components
```csharp
// Inject DialogService
[Inject] public DialogService DialogService { get; set; } = null!;

// Confirmation Dialog
var result = await DialogService.Confirm(
    "Are you sure you want to delete this item?",
    "Confirm Deletion",
    new ConfirmOptions()
    {
        OkButtonText = "Delete",
        CancelButtonText = "Cancel"
    });

// Custom Dialog
var result = await DialogService.OpenAsync<CustomDialogComponent>(
    "Dialog Title",
    new Dictionary<string, object>()
    {
        { "Parameter1", value1 },
        { "Parameter2", value2 }
    });
```

## Styling Guidelines

### Tailwind CSS Usage

**Use Tailwind CSS classes exclusively for styling:**

#### Layout Classes
```html
<!-- Container -->
<div class="max-w-[1400px] mx-auto px-6 py-8">

<!-- Grid Layout -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

<!-- Flexbox Layout -->
<div class="flex items-center justify-between gap-4">

<!-- Responsive Design -->
<div class="hidden md:block lg:flex">
```

#### Spacing and Sizing
```html
<!-- Padding and Margin -->
<div class="p-4 m-2 px-6 py-8 mt-4 mb-6">

<!-- Width and Height -->
<div class="w-full h-64 max-w-md min-h-screen">

<!-- Responsive Spacing -->
<div class="p-2 md:p-4 lg:p-6">
```

#### Colors and Typography
```html
<!-- Text Colors -->
<h1 class="text-gray-900 text-2xl font-medium">
<p class="text-gray-600 text-sm">

<!-- Background Colors -->
<div class="bg-white bg-gray-50 bg-blue-500">

<!-- Borders -->
<div class="border border-gray-200 rounded-lg">
```

#### Component-Specific Classes
```html
<!-- Custom component classes (defined in CSS) -->
<div class="barret-data-grid">
<div class="barret-form-section">
<div class="barret-card">
```

### CSS Organization

#### File Structure
```
/wwwroot/css/Styles/
├── main.css              # Main import file
├── _variables.css        # Design tokens and CSS variables
├── _base.css            # Base styles and resets
├── _components.css      # Component-specific styles
├── _layout.css          # Layout utilities
├── _utilities.css       # Custom utility classes
└── dist.css            # Compiled output (generated)
```

#### Custom Component Styles
When Tailwind utilities are insufficient, create component-specific styles:

```css
/* _components.css */
.barret-data-grid {
    @apply border border-gray-200 rounded-lg overflow-hidden;
}

.barret-data-grid .rz-datatable-data td {
    @apply px-4 py-3 border-b border-gray-100;
}

.barret-form-section {
    @apply bg-white rounded-lg border border-gray-200 p-6 mb-6;
}

.barret-card {
    @apply bg-white rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow;
}
```

## ReactiveUI Best Practices

### Property Implementation
```csharp
// Use [Reactive] attribute for simple properties
[Reactive] public string Name { get; set; } = string.Empty;

// Use ObservableAsPropertyHelper for derived properties
private readonly ObservableAsPropertyHelper<bool> _canSave;
public bool CanSave => _canSave.Value;

// Set up derived properties in constructor
this.WhenAnyValue(x => x.Name, x => x.IsValid)
    .Select((name, isValid) => !string.IsNullOrEmpty(name) && isValid)
    .ToProperty(this, x => x.CanSave, out _canSave)
    .DisposeWith(Disposables);
```

### Command Implementation
```csharp
// Simple command
SaveCommand = CreateCommand(SaveAsync, errorMessage: "Failed to save");

// Command with can-execute logic
SaveCommand = CreateCommand(
    SaveAsync,
    this.WhenAnyValue(x => x.CanSave),
    "Failed to save");

// Command with parameter
DeleteCommand = CreateCommand<ItemDto>(
    DeleteItemAsync,
    this.WhenAnyValue(x => x.IsLoading).Select(loading => !loading),
    "Failed to delete item");
```

### Subscription Management
```csharp
// In ViewModel constructor
this.WhenAnyValue(x => x.SearchText)
    .Throttle(TimeSpan.FromMilliseconds(300))
    .DistinctUntilChanged()
    .Subscribe(async text => await SearchAsync(text))
    .DisposeWith(Disposables);

// In View SetupSubscriptions method
SubscribeToProperty(vm => vm.Items, items => 
{
    // Handle items change
});
```

## File Organization

### Feature-Based Structure
```
/Features/
├── FeatureName/
│   ├── Views/
│   │   ├── FeatureView.razor
│   │   └── FeatureView.razor.cs
│   ├── ViewModels/
│   │   ├── FeatureViewModel.cs
│   │   └── FeatureViewModelFactory.cs
│   ├── Components/
│   │   ├── FeatureCard.razor
│   │   └── FeatureForm.razor
│   └── Services/
│       └── FeatureService.cs
```

### Naming Conventions
- **Views**: `{FeatureName}View.razor`
- **ViewModels**: `{FeatureName}ViewModel.cs`
- **Components**: `{ComponentName}.razor`
- **Services**: `{FeatureName}Service.cs`

## Error Handling

### ViewModel Error Handling
```csharp
// Use base class methods for consistent error handling
var result = await ExecuteWithLoadingAsync(
    async () => await SomeOperationAsync(),
    "Operation failed");

if (!result)
{
    // Handle failure - error is already set in base class
    return;
}
```

### View Error Display
```razor
@if (ViewModel.HasError)
{
    <RadzenAlert AlertStyle="AlertStyle.Danger" 
                 Variant="Variant.Flat" 
                 class="mb-4">
        @ViewModel.ErrorMessage
    </RadzenAlert>
}
```

## Performance Considerations

### Lazy Loading
```csharp
// Use lazy loading for heavy components
@if (showHeavyComponent)
{
    <LazyComponent>
        <HeavyComponent />
    </LazyComponent>
}
```

### Virtualization
```razor
<!-- Use virtualization for large lists -->
<RadzenDataGrid Data="@LargeDataSet" 
                TItem="ItemDto"
                AllowVirtualization="true"
                PageSize="50">
    <!-- Columns -->
</RadzenDataGrid>
```

## Testing Guidelines

### Unit Testing ViewModels
```csharp
[Test]
public async Task SaveCommand_WhenNameIsEmpty_ShouldNotExecute()
{
    // Arrange
    var viewModel = new ExampleViewModel(Mock.Of<ILogger<ExampleViewModel>>());
    viewModel.Name = string.Empty;
    
    // Act & Assert
    Assert.That(viewModel.SaveCommand.CanExecute.FirstOrDefault(), Is.False);
}
```

### Integration Testing Views
```csharp
[Test]
public void ExampleView_WhenViewModelHasError_ShouldDisplayErrorAlert()
{
    // Arrange
    var viewModel = new ExampleViewModel(Mock.Of<ILogger<ExampleViewModel>>());
    viewModel.SetError("Test error");
    
    // Act
    var component = RenderComponent<ExampleView>(parameters => 
        parameters.Add(p => p.ViewModel, viewModel));
    
    // Assert
    Assert.That(component.Find(".rz-alert-danger"), Is.Not.Null);
}
```

## Migration from Legacy Components

### DevExpress to Radzen Migration
When migrating existing DevExpress components:

1. **Replace component tags** using the mapping in the refactoring plan
2. **Update styling** from DevExpress classes to Tailwind CSS
3. **Refactor event handlers** to use ReactiveCommand patterns
4. **Move components** to feature-based locations
5. **Test thoroughly** to ensure functionality is preserved

### Example Migration
```razor
<!-- Before (DevExpress) -->
<DxButton Text="Save" 
          IconCssClass="bi bi-save"
          Click="@SaveAsync"
          RenderStyle="ButtonRenderStyle.Primary" />

<!-- After (Radzen) -->
<RadzenButton Text="Save"
              Icon="save"
              ButtonStyle="ButtonStyle.Primary"
              Click="@(() => ViewModel.SaveCommand.Execute())" />
```

This migration should be accompanied by moving the save logic from the view's code-behind to the ViewModel as a ReactiveCommand.
