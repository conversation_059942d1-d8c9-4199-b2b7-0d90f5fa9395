# DevExpress to Radzen Component Migration Mapping

## Phase 2: Radzen Component Research & Property Mappings

This document provides detailed property mappings and migration strategies for replacing DevExpress components with Radzen equivalents.

## Available Radzen Components (2024)

Based on the official Radzen Blazor documentation, **90+ components** are available including:

### **Form Components**
- `RadzenButton`, `RadzenTextBox`, `RadzenDropDown`, `RadzenNumeric`
- `RadzenCheckBox`, `<PERSON><PERSON>zenFormField`, `RadzenTextArea`, `RadzenPassword`
- `RadzenAutoComplete`, `RadzenColorPicker`, `RadzenDatePicker`
- `RadzenFileInput`, `<PERSON><PERSON><PERSON>Mask`, `RadzenRating`, `RadzenSlider`
- `RadzenSwitch`, `RadzenToggleButton`, `RadzenUpload`

### **Data Components**
- `RadzenDataGrid` (Advanced with filtering, paging, sorting, grouping)
- `Ra<PERSON>zenDataList`, `RadzenTree`, `RadzenPager`, `<PERSON><PERSON>zenScheduler`

### **Layout Components**
- `<PERSON><PERSON><PERSON>Stack`, `<PERSON><PERSON><PERSON>Row`, `RadzenColumn`, `RadzenCard`
- `RadzenDialog`, `RadzenTabs`, `RadzenAccordion`, `RadzenPanel`

### **Navigation Components**
- `RadzenMenu`, `RadzenBreadCrumb`, `RadzenSteps`, `RadzenContextMenu`

## Component Migration Mappings

### 1. **DxButton → RadzenButton** (Priority: HIGH)

#### **Property Mapping**
| DevExpress Property | Radzen Property | Notes |
|-------------------|-----------------|-------|
| `Text` | `Text` | Direct mapping |
| `IconCssClass="bi bi-save"` | `Icon="save"` | Use Radzen's built-in Material icons |
| `RenderStyle="ButtonRenderStyle.Primary"` | `ButtonStyle="ButtonStyle.Primary"` | Direct mapping |
| `RenderStyle="ButtonRenderStyle.Secondary"` | `ButtonStyle="ButtonStyle.Secondary"` | Direct mapping |
| `Click` | `Click` | Direct mapping |
| `Enabled` | `Disabled` | **Inverted logic** |
| `CssClass` | `class` | Use Tailwind CSS classes |

#### **Migration Example**
```razor
<!-- Before (DevExpress) -->
<DxButton Text="Save" 
          IconCssClass="bi bi-save"
          Click="@SaveAsync"
          RenderStyle="ButtonRenderStyle.Primary"
          Enabled="@(!IsLoading)" />

<!-- After (Radzen) -->
<RadzenButton Text="Save"
              Icon="save"
              ButtonStyle="ButtonStyle.Primary"
              Click="@(() => ViewModel.SaveCommand.Execute())"
              Disabled="@ViewModel.IsLoading"
              class="px-4 py-2" />
```

### 2. **DxTextBox → RadzenTextBox** (Priority: HIGH)

#### **Property Mapping**
| DevExpress Property | Radzen Property | Notes |
|-------------------|-----------------|-------|
| `Text` | `@bind-Value` | Use two-way binding |
| `TextChanged` | `ValueChanged` | Event handler |
| `NullText` | `Placeholder` | Direct mapping |
| `ReadOnly` | `ReadOnly` | Direct mapping |
| `Enabled` | `Disabled` | **Inverted logic** |
| `ClearButtonDisplayMode` | `ShowClearButton` | Boolean instead of enum |
| `SizeMode` | `Size` | Use Radzen size enum |

#### **Migration Example**
```razor
<!-- Before (DevExpress) -->
<DxTextBox @bind-Text="@Model.Name"
           NullText="Enter name..."
           ReadOnly="@IsReadOnly"
           ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />

<!-- After (Radzen) -->
<RadzenFormField Text="Name" Variant="Variant.Outlined">
    <RadzenTextBox @bind-Value="@Model.Name"
                   Placeholder="Enter name..."
                   ReadOnly="@IsReadOnly"
                   ShowClearButton="true"
                   class="w-full" />
</RadzenFormField>
```

### 3. **DxComboBox → RadzenDropDown** (Priority: HIGH)

#### **Property Mapping**
| DevExpress Property | Radzen Property | Notes |
|-------------------|-----------------|-------|
| `Data` | `Data` | Direct mapping |
| `Value` | `@bind-Value` | Use two-way binding |
| `ValueChanged` | `ValueChanged` | Event handler |
| `TextFieldName` | `TextProperty` | Direct mapping |
| `ValueFieldName` | `ValueProperty` | Direct mapping |
| `NullText` | `Placeholder` | Direct mapping |
| `ClearButtonDisplayMode` | `AllowClear` | Boolean instead of enum |
| `ItemTemplate` | `Template` | Custom template |

#### **Migration Example**
```razor
<!-- Before (DevExpress) -->
<DxComboBox Data="@Manufacturers"
            @bind-Value="@SelectedManufacturerId"
            TextFieldName="Name"
            ValueFieldName="Id"
            NullText="Select manufacturer..."
            ClearButtonDisplayMode="DataEditorClearButtonDisplayMode.Auto" />

<!-- After (Radzen) -->
<RadzenFormField Text="Manufacturer" Variant="Variant.Outlined">
    <RadzenDropDown @bind-Value="@SelectedManufacturerId"
                    Data="@Manufacturers"
                    TextProperty="Name"
                    ValueProperty="Id"
                    Placeholder="Select manufacturer..."
                    AllowClear="true"
                    class="w-full" />
</RadzenFormField>
```

### 4. **DxGrid → RadzenDataGrid** (Priority: CRITICAL)

#### **Property Mapping**
| DevExpress Property | Radzen Property | Notes |
|-------------------|-----------------|-------|
| `Data` | `Data` | Direct mapping |
| `ShowFilterRow` | `AllowFiltering` | Direct mapping |
| `ShowPager` | `AllowPaging` | Direct mapping |
| `PageSize` | `PageSize` | Direct mapping |
| `KeyFieldName` | `KeyProperty` | Direct mapping |
| `RowClick` | `RowSelect` | Event handler |
| `SelectionEnabled` | `AllowRowSelectOnRowClick` | Boolean |
| `ShowLoadingPanel` | `IsLoading` | Boolean |

#### **Column Mapping**
| DevExpress | Radzen | Notes |
|-----------|--------|-------|
| `DxGridDataColumn` | `RadzenDataGridColumn<T>` | Generic column |
| `FieldName` | `Property` | Lambda expression |
| `Caption` | `Title` | Direct mapping |
| `Width` | `Width` | Direct mapping |
| `CellDisplayTemplate` | `Template` | Custom template |

#### **Migration Example**
```razor
<!-- Before (DevExpress) -->
<DxGrid Data="@Devices"
        ShowFilterRow="true"
        ShowPager="true"
        PageSize="10"
        KeyFieldName="Id"
        RowClick="@OnRowClick">
    <Columns>
        <DxGridDataColumn FieldName="Name" Caption="Device Name" Width="200px" />
        <DxGridDataColumn FieldName="Model.Name" Caption="Model" Width="150px" />
    </Columns>
</DxGrid>

<!-- After (Radzen) -->
<RadzenDataGrid @ref="grid"
                Data="@Devices"
                AllowFiltering="true"
                AllowPaging="true"
                PageSize="10"
                RowSelect="@OnRowSelect"
                TItem="DeviceDto"
                class="w-full">
    <Columns>
        <RadzenDataGridColumn TItem="DeviceDto" Property="Name" Title="Device Name" Width="200px" />
        <RadzenDataGridColumn TItem="DeviceDto" Property="Model.Name" Title="Model" Width="150px" />
    </Columns>
</RadzenDataGrid>
```

### 5. **DxMessageBox → DialogService** (Priority: MEDIUM)

#### **Migration Strategy**
Replace component-based message boxes with service-based dialogs.

#### **Migration Example**
```razor
<!-- Before (DevExpress) -->
<DxMessageBox @bind-Visible="@showDeleteConfirm"
              Type="MessageBoxType.Confirmation"
              Title="Confirm Deletion"
              Text="@($"Are you sure you want to delete {deviceName}?")"
              OkButtonText="Delete"
              CancelButtonText="Cancel"
              Closed="@OnDeleteConfirmClosed" />

<!-- After (Radzen) -->
@inject DialogService DialogService

@code {
    private async Task ShowDeleteConfirmation()
    {
        var result = await DialogService.Confirm(
            $"Are you sure you want to delete {deviceName}?",
            "Confirm Deletion",
            new ConfirmOptions()
            {
                OkButtonText = "Delete",
                CancelButtonText = "Cancel"
            });
            
        if (result == true)
        {
            await DeleteDevice();
        }
    }
}
```

### 6. **DxPopup → RadzenDialog** (Priority: MEDIUM)

#### **Property Mapping**
| DevExpress Property | Radzen Property | Notes |
|-------------------|-----------------|-------|
| `Visible` | `Visible` | Direct mapping |
| `HeaderText` | `Title` | Direct mapping |
| `Width` | `Width` | Direct mapping |
| `CloseOnEscape` | `CloseOnEscape` | Direct mapping |
| `CloseOnOutsideClick` | `CloseOnOverlayClick` | Direct mapping |
| `ShowCloseButton` | `ShowClose` | Direct mapping |

#### **Migration Example**
```razor
<!-- Before (DevExpress) -->
<DxPopup @bind-Visible="@showEditor"
         HeaderText="Edit Device"
         Width="600px"
         CloseOnEscape="true"
         CloseOnOutsideClick="false">
    <Content>
        <!-- Editor content -->
    </Content>
</DxPopup>

<!-- After (Radzen) -->
<RadzenDialog @bind-Visible="@showEditor"
              Title="Edit Device"
              Width="600px"
              CloseOnEscape="true"
              CloseOnOverlayClick="false"
              ShowClose="true">
    <ChildContent>
        <!-- Editor content -->
    </ChildContent>
</RadzenDialog>
```

### 7. **DxTabs → RadzenTabs** (Priority: LOW)

#### **Property Mapping**
| DevExpress Property | Radzen Property | Notes |
|-------------------|-----------------|-------|
| `ActiveTabIndex` | `SelectedIndex` | Direct mapping |
| `ActiveTabIndexChanged` | `SelectedIndexChanged` | Event handler |
| `TabClick` | `Change` | Event handler |

#### **Migration Example**
```razor
<!-- Before (DevExpress) -->
<DxTabs @bind-ActiveTabIndex="@activeTab">
    <DxTabPage Text="General" TabIconCssClass="bi bi-gear">
        <!-- Content -->
    </DxTabPage>
    <DxTabPage Text="Connections" TabIconCssClass="bi bi-link">
        <!-- Content -->
    </DxTabPage>
</DxTabs>

<!-- After (Radzen) -->
<RadzenTabs @bind-SelectedIndex="@activeTab">
    <Tabs>
        <RadzenTabsItem Text="General" Icon="settings">
            <!-- Content -->
        </RadzenTabsItem>
        <RadzenTabsItem Text="Connections" Icon="link">
            <!-- Content -->
        </RadzenTabsItem>
    </Tabs>
</RadzenTabs>
```

### 8. **DxToastProvider → NotificationService** (Priority: LOW)

#### **Migration Strategy**
Replace provider component with service-based notifications.

#### **Migration Example**
```razor
<!-- Before (DevExpress) -->
<DxToastProvider AnimationType="ToastAnimationType.Fade"
                 HorizontalAlignment="HorizontalAlignment.Right"
                 VerticalAlignment="VerticalEdge.Bottom"
                 DisplayTime="@TimeSpan.FromSeconds(5)" />

<!-- After (Radzen) -->
@inject NotificationService NotificationService

@code {
    private void ShowNotification(string message, NotificationSeverity severity = NotificationSeverity.Info)
    {
        NotificationService.Notify(new NotificationMessage
        {
            Severity = severity,
            Summary = message,
            Duration = 5000
        });
    }
}
```

## Migration Implementation Strategy

### **Phase 3A: High-Priority Components (Week 1-2)**
1. **DxButton** (15+ locations) - Start here for quick wins
2. **DxTextBox** (3 wrapper components)
3. **DxComboBox** (5+ locations)

### **Phase 3B: Critical Components (Week 3-4)**
1. **DxGrid** (8 locations) - Most complex, highest impact
2. **DxGridDataColumn** (20+ locations)

### **Phase 3C: Medium Priority (Week 5)**
1. **DxPopup** (4 locations)
2. **DxMessageBox** (3 locations)

### **Phase 3D: Low Priority (Week 6)**
1. **DxTabs** (2 locations)
2. **DxToastProvider** (1 location)

## Styling Integration

### **Tailwind CSS Classes**
- Use Tailwind for spacing: `px-4 py-2`, `m-2`, `mb-4`
- Use Tailwind for sizing: `w-full`, `h-auto`, `max-w-md`
- Use Tailwind for layout: `flex`, `grid`, `items-center`

### **Radzen Form Fields**
Wrap form components in `RadzenFormField` for consistent styling:
```razor
<RadzenFormField Text="Label" Variant="Variant.Outlined">
    <RadzenTextBox @bind-Value="@Value" class="w-full" />
</RadzenFormField>
```

## Next Steps

1. **Begin with DxButton migration** - Lowest complexity, high impact
2. **Update wrapper components** in `/Features/Shared/Components/BarretDevExpress/`
3. **Test each component type** before proceeding to next
4. **Document patterns** for team consistency
5. **Remove DevExpress dependencies** after all components migrated
