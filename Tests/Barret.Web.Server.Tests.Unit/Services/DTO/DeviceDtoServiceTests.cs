using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices.Cameras;
using Barret.Web.Server.Services.DTO;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace Barret.Web.Server.Tests.Unit.Services.DTO
{
    [TestClass]
    public class DeviceDtoServiceTests
    {
        private Mock<ILogger<DeviceDtoService>>? _mockLogger;
        private DeviceDtoService? _deviceDtoService;

        [TestInitialize]
        public void Initialize()
        {
            _mockLogger = new Mock<ILogger<DeviceDtoService>>();
            _deviceDtoService = new DeviceDtoService(_mockLogger.Object);
        }

        #region CopyDevice Tests

        [TestMethod]
        public void CopyDevice_WithCameraDto_CopiesShowVideoProperty()
        {
            // Arrange
            var sourceCamera = new CameraDto
            {
                Id = Guid.NewGuid(),
                Name = "Test Camera",
                DeviceRole = DeviceRole.Camera,
                ShowVideo = true // Set to true to test copying
            };

            // Act
            var copiedCamera = _deviceDtoService!.CopyDevice(sourceCamera) as CameraDto;

            // Assert
            Assert.IsNotNull(copiedCamera);
            Assert.AreEqual(sourceCamera.ShowVideo, copiedCamera.ShowVideo);
            Assert.IsTrue(copiedCamera.ShowVideo, "ShowVideo property should be copied correctly");
        }

        [TestMethod]
        public void CopyDevice_WithCameraDtoShowVideoFalse_CopiesShowVideoProperty()
        {
            // Arrange
            var sourceCamera = new CameraDto
            {
                Id = Guid.NewGuid(),
                Name = "Test Camera",
                DeviceRole = DeviceRole.Camera,
                ShowVideo = false // Set to false to test copying
            };

            // Act
            var copiedCamera = _deviceDtoService!.CopyDevice(sourceCamera) as CameraDto;

            // Assert
            Assert.IsNotNull(copiedCamera);
            Assert.AreEqual(sourceCamera.ShowVideo, copiedCamera.ShowVideo);
            Assert.IsFalse(copiedCamera.ShowVideo, "ShowVideo property should be copied correctly");
        }

        #endregion

        #region UpdateDevice Tests

        [TestMethod]
        public void UpdateDevice_WithCameraDto_UpdatesShowVideoProperty()
        {
            // Arrange
            var targetCamera = new CameraDto
            {
                Id = Guid.NewGuid(),
                Name = "Target Camera",
                DeviceRole = DeviceRole.Camera,
                ShowVideo = false // Initially false
            };

            var sourceCamera = new CameraDto
            {
                Id = Guid.NewGuid(),
                Name = "Source Camera",
                DeviceRole = DeviceRole.Camera,
                ShowVideo = true // Source has true
            };

            // Act
            var updatedCamera = _deviceDtoService!.UpdateDevice(targetCamera, sourceCamera) as CameraDto;

            // Assert
            Assert.IsNotNull(updatedCamera);
            Assert.AreEqual(sourceCamera.ShowVideo, updatedCamera.ShowVideo);
            Assert.IsTrue(updatedCamera.ShowVideo, "ShowVideo property should be updated from source");
        }

        [TestMethod]
        public void UpdateDevice_WithCameraDtoShowVideoFalse_UpdatesShowVideoProperty()
        {
            // Arrange
            var targetCamera = new CameraDto
            {
                Id = Guid.NewGuid(),
                Name = "Target Camera",
                DeviceRole = DeviceRole.Camera,
                ShowVideo = true // Initially true
            };

            var sourceCamera = new CameraDto
            {
                Id = Guid.NewGuid(),
                Name = "Source Camera",
                DeviceRole = DeviceRole.Camera,
                ShowVideo = false // Source has false
            };

            // Act
            var updatedCamera = _deviceDtoService!.UpdateDevice(targetCamera, sourceCamera) as CameraDto;

            // Assert
            Assert.IsNotNull(updatedCamera);
            Assert.AreEqual(sourceCamera.ShowVideo, updatedCamera.ShowVideo);
            Assert.IsFalse(updatedCamera.ShowVideo, "ShowVideo property should be updated from source");
        }

        #endregion
    }
}
