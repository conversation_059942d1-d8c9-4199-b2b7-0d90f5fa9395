using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Vehicles.Models;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.Vehicles.Repositories;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Tests.Unit.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Barret.Services.Tests.Unit.Areas.Vehicles.Repositories
{
    [TestClass]
    public class VehicleRepositoryTests
    {
        private Mock<IBarretDbContext> _mockDbContext;
        private Mock<DbSet<Vessel>> _mockVesselsDbSet;
        private Mock<DbSet<GenericDevice>> _mockDevicesDbSet;
        private Mock<ILogger<VehicleRepository>> _mockLogger;
        private VehicleRepository _vehicleRepository;
        private List<Vessel> _vessels;
        private List<GenericDevice> _devices;

        [TestInitialize]
        public void Setup()
        {
            // Initialize test data
            var vessel1Id = Guid.NewGuid();
            var vessel2Id = Guid.NewGuid();

            // Create vessels using constructor instead of property initializer for read-only properties
            var vessel1 = new Vessel("Test Vessel 1");
            var vessel2 = new Vessel("Test Vessel 2");

            // Use reflection to set the Id since it's a read-only property
            typeof(Vessel).GetProperty("Id").SetValue(vessel1, vessel1Id);
            typeof(Vessel).GetProperty("Id").SetValue(vessel2, vessel2Id);

            _vessels = new List<Vessel>
            {
                vessel1,
                vessel2
            };

            // Create devices using constructor instead of property initializer for read-only properties
            var device1 = new GenericDevice("Device 1 for Vessel 1");
            device1.VehicleId = vessel1Id;
            // Use reflection to set the DeviceRole since it's a read-only property
            typeof(GenericDevice).GetField("_deviceRole", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .SetValue(device1, DeviceRole.Camera);

            var device2 = new GenericDevice("Device 2 for Vessel 1");
            device2.VehicleId = vessel1Id;
            typeof(GenericDevice).GetField("_deviceRole", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .SetValue(device2, DeviceRole.Engine);

            var device3 = new GenericDevice("Device 1 for Vessel 2");
            device3.VehicleId = vessel2Id;
            typeof(GenericDevice).GetField("_deviceRole", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .SetValue(device3, DeviceRole.Radar);

            _devices = new List<GenericDevice>
            {
                device1,
                device2,
                device3
            };

            // Setup mock DbSets
            _mockVesselsDbSet = _vessels.AsQueryable().BuildMockDbSet();
            _mockDevicesDbSet = _devices.AsQueryable().BuildMockDbSet();

            // Setup mock DbContext
            _mockDbContext = new Mock<IBarretDbContext>();
            _mockDbContext.Setup(c => c.Vessels).Returns(_mockVesselsDbSet.Object);
            _mockDbContext.Setup(c => c.Devices).Returns(_mockDevicesDbSet.Object);

            // Setup mock Logger
            _mockLogger = new Mock<ILogger<VehicleRepository>>();

            // Create repository
            _vehicleRepository = new VehicleRepository(_mockDbContext.Object, _mockLogger.Object);
        }

        [TestMethod]
        public async Task GetVesselByIdAsync_ExistingId_ReturnsVesselWithDevices()
        {
            // Arrange
            var vesselId = _vessels[0].Id;
            var vesselDevices = _devices.Where(d => d.VehicleId == vesselId).ToList();

            // Setup mock to return filtered devices
            var mockFilteredDbSet = vesselDevices.AsQueryable().BuildMockDbSet();
            _mockDbContext.Setup(c => c.Devices.Where(It.IsAny<System.Linq.Expressions.Expression<Func<GenericDevice, bool>>>()))
                .Returns(mockFilteredDbSet.Object);

            // Act
            var result = await _vehicleRepository.GetVesselByIdAsync(vesselId);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(vesselId, result.Id);
            Assert.AreEqual("Test Vessel 1", result.Name);

            // Verify that devices were loaded
            _mockDbContext.Verify(c => c.Devices.Where(It.IsAny<System.Linq.Expressions.Expression<Func<GenericDevice, bool>>>()), Times.Once);
        }

        [TestMethod]
        public async Task GetVesselByIdAsync_NonExistingId_ReturnsNull()
        {
            // Arrange
            var nonExistingId = Guid.NewGuid();

            // Act
            var result = await _vehicleRepository.GetVesselByIdAsync(nonExistingId);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetAllVesselsAsync_ReturnsAllVessels()
        {
            // Act
            var result = await _vehicleRepository.GetAllVesselsAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count());
        }

        [TestMethod]
        public async Task GetDevicesForVehicleAsync_ExistingVehicleId_ReturnsDevicesForVehicle()
        {
            // Arrange
            var vesselId = _vessels[0].Id;
            var vesselDevices = _devices.Where(d => d.VehicleId == vesselId).ToList();

            // Setup mock to return filtered devices
            var mockFilteredDbSet = vesselDevices.AsQueryable().BuildMockDbSet();
            _mockDbContext.Setup(c => c.Devices.Where(It.IsAny<System.Linq.Expressions.Expression<Func<GenericDevice, bool>>>()))
                .Returns(mockFilteredDbSet.Object);

            // Act
            var result = await _vehicleRepository.GetDevicesForVehicleAsync(vesselId);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count());
            Assert.IsTrue(result.All(d => d.VehicleId == vesselId));
        }

        [TestMethod]
        public async Task AddAsync_ValidVessel_AddsToContext()
        {
            // Arrange
            var newVessel = new Vessel("New Test Vessel");
            // Use reflection to set the Id since it's a read-only property
            typeof(Vessel).GetProperty("Id").SetValue(newVessel, Guid.NewGuid());

            // Act
            await _vehicleRepository.AddAsync(newVessel);

            // Assert
            _mockDbContext.Verify(c => c.Vessels.AddAsync(newVessel, default), Times.Once);
        }

        [TestMethod]
        public void Update_ValidVessel_UpdatesInContext()
        {
            // Arrange
            var vessel = _vessels[0];
            vessel.Name = "Updated Vessel Name";

            // Act
            _vehicleRepository.Update(vessel);

            // Assert
            _mockDbContext.Verify(c => c.Vessels.Update(vessel), Times.Once);
        }

        [TestMethod]
        public async Task DeleteAsync_ExistingId_RemovesFromContext()
        {
            // Arrange
            var vesselId = _vessels[0].Id;
            var vessel = _vessels[0];

            _mockDbContext.Setup(c => c.Vessels.FindAsync(vesselId))
                .ReturnsAsync(vessel);

            // Act
            await _vehicleRepository.DeleteAsync(vesselId);

            // Assert
            _mockDbContext.Verify(c => c.Vessels.Remove(vessel), Times.Once);
        }
    }
}
