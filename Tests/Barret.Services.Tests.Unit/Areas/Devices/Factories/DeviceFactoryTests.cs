using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.Antennas;
using Barret.Core.Areas.Devices.Models.Autopilots;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.Engines;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Horns;
using Barret.Core.Areas.Devices.Models.Lights;
using Barret.Core.Areas.Devices.Models.Radars;
using Barret.Core.Areas.Devices.Models.Rudders;
using Barret.Core.Areas.Devices.Models.Thrusters;
using Barret.Services.Areas.Devices.Factories;
using Microsoft.Extensions.Logging;
using Moq;

namespace Barret.Services.Tests.Unit.Areas.Devices.Factories
{
    [TestClass]
    public class DeviceFactoryTests
    {
        private Mock<ILogger<DeviceFactory>>? _mockLogger;
        private DeviceFactory? _deviceFactory;

        [TestInitialize]
        public void Initialize()
        {
            _mockLogger = new Mock<ILogger<DeviceFactory>>();
            _deviceFactory = new DeviceFactory(_mockLogger.Object);
        }

        #region CreateDevice Tests

        [TestMethod]
        public void CreateDevice_WithRole_CreatesDeviceWithCorrectId()
        {
            // Arrange
            var deviceRole = DeviceRole.Camera;

            // Act
            var device = _deviceFactory!.CreateDevice(deviceRole);

            // Assert
            Assert.IsNotNull(device);
            Assert.AreNotEqual(Guid.Empty, device.Id);
        }

        [TestMethod]
        public void CreateDevice_WithRole_CreatesDeviceWithDefaultPosition()
        {
            // Arrange
            var deviceRole = DeviceRole.Camera;

            // Act
            var device = _deviceFactory!.CreateDevice(deviceRole);

            // Assert
            Assert.IsNotNull(device);
            Assert.IsNotNull(device.Position);
            Assert.AreEqual(0, device.Position.X);
            Assert.AreEqual(0, device.Position.Y);
            Assert.AreEqual(0, device.Position.Z);
        }



        [TestMethod]
        [DataRow(DeviceRole.Camera, typeof(Camera))]
        [DataRow(DeviceRole.Engine, typeof(Engine))]
        [DataRow(DeviceRole.Thruster, typeof(Thruster))]
        [DataRow(DeviceRole.Radar, typeof(Radar))]
        [DataRow(DeviceRole.Light, typeof(Light))]
        [DataRow(DeviceRole.Rudder, typeof(Rudder))]
        [DataRow(DeviceRole.Antenna, typeof(Antenna))]
        [DataRow(DeviceRole.Autopilot, typeof(Autopilot))]
        [DataRow(DeviceRole.Horn, typeof(Horn))]
        // [DataRow(DeviceRole.Framegrabber, typeof(Radar))] // Framegrabber is implemented as Radar
        [DataRow(DeviceRole.Generic, typeof(GenericDevice))] // Generic is implemented as GenericDevice
        public void CreateDevice_WithRoleOnly_ReturnsCorrectDeviceType(DeviceRole role, Type expectedType)
        {
            // Act
            var device = _deviceFactory!.CreateDevice(role);

            // Assert
            Assert.IsNotNull(device);
            Assert.IsInstanceOfType(device, expectedType);
            Assert.AreEqual($"New {role}", device.Name); // Role-specific default name
            Assert.AreEqual(role, device.DeviceRole);
        }

        [TestMethod]
        public void CreateDevice_WithUnsupportedRole_UsesGenericDeviceWithDefaultName()
        {
            // Arrange
            var deviceRole = (DeviceRole)999; // Unsupported role

            // Act
            var device = _deviceFactory!.CreateDevice(deviceRole);

            // Assert
            Assert.IsNotNull(device);
            Assert.IsInstanceOfType(device, typeof(GenericDevice));
            Assert.AreEqual($"New {deviceRole}", device.Name); // Default name based on role
        }

        #endregion
    }
}
