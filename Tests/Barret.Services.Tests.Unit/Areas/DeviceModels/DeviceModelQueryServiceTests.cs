using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Manufacturers.Models;
using Barret.Services.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.DeviceModels.Repositories;
using Barret.Services.Tests.Unit.Helpers;
using Barret.Shared.DTOs.Devices;
using Microsoft.Extensions.Logging;
using Moq;

namespace Barret.Services.Tests.Unit.Areas.DeviceModels
{
    [TestClass]
    public class DeviceModelQueryServiceTests
    {
        private Mock<IDeviceModelRepository> _mockDeviceModelRepository;
        private Mock<ILogger<DeviceModelQueryService>> _mockLogger;
        private DeviceModelQueryService _deviceModelQueryService;

        [TestInitialize]
        public void Initialize()
        {
            _mockDeviceModelRepository = new Mock<IDeviceModelRepository>();
            _mockLogger = new Mock<ILogger<DeviceModelQueryService>>();

            _deviceModelQueryService = new DeviceModelQueryService(
                _mockDeviceModelRepository.Object,
                _mockLogger.Object
            );
        }

        [TestMethod]
        public async Task GetDeviceModelByIdAsync_WhenDeviceModelExists_ReturnsDeviceModelInfo()
        {
            // Arrange
            var deviceModelId = Guid.NewGuid();
            var manufacturerId = Guid.NewGuid();
            var manufacturer = new Manufacturer("Test Manufacturer");
            
            // Set manufacturer Id using reflection
            var manufacturerIdField = typeof(Manufacturer).GetField("<Id>k__BackingField", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            manufacturerIdField.SetValue(manufacturer, manufacturerId);

            var deviceModel = new DeviceModel("Test Model", DeviceRole.Camera);
            
            // Set device model Id and ManufacturerId using reflection
            var deviceModelIdField = typeof(DeviceModel).GetField("<Id>k__BackingField", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            deviceModelIdField.SetValue(deviceModel, deviceModelId);
            
            var manufacturerIdPropInfo = typeof(DeviceModel).GetProperty("ManufacturerId");
            manufacturerIdPropInfo.SetValue(deviceModel, manufacturerId);
            
            // Set Manufacturer property using reflection
            var manufacturerPropInfo = typeof(DeviceModel).GetProperty("Manufacturer");
            manufacturerPropInfo.SetValue(deviceModel, manufacturer);

            _mockDeviceModelRepository.Setup(repo => repo.GetByIdAsync(deviceModelId))
                .ReturnsAsync(deviceModel);

            // Act
            var result = await _deviceModelQueryService.GetDeviceModelByIdAsync(deviceModelId);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(deviceModelId, result.Id);
            Assert.AreEqual("Test Model", result.Name);
            Assert.AreEqual(DeviceRole.Camera, result.DeviceRole);
            Assert.AreEqual(manufacturerId, result.ManufacturerId);
            Assert.AreEqual("Test Manufacturer", result.ManufacturerName);
        }

        [TestMethod]
        public async Task GetDeviceModelByIdAsync_WhenDeviceModelDoesNotExist_ReturnsNull()
        {
            // Arrange
            var deviceModelId = Guid.NewGuid();
            _mockDeviceModelRepository.Setup(repo => repo.GetByIdAsync(deviceModelId))
                .ReturnsAsync((DeviceModel)null);

            // Act
            var result = await _deviceModelQueryService.GetDeviceModelByIdAsync(deviceModelId);

            // Assert
            Assert.IsNull(result);
        }

        [TestMethod]
        public async Task GetAllDeviceModelsAsync_ReturnsAllDeviceModels()
        {
            // Arrange
            var manufacturerId = Guid.NewGuid();
            var manufacturer = new Manufacturer("Test Manufacturer");
            
            // Set manufacturer Id using reflection
            var manufacturerIdField = typeof(Manufacturer).GetField("<Id>k__BackingField", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            manufacturerIdField.SetValue(manufacturer, manufacturerId);

            var deviceModels = new List<DeviceModel>
            {
                new DeviceModel("Model 1", DeviceRole.Camera),
                new DeviceModel("Model 2", DeviceRole.Radar)
            };
            
            // Set device model Ids and ManufacturerId using reflection
            for (int i = 0; i < deviceModels.Count; i++)
            {
                var deviceModelId = Guid.NewGuid();
                var deviceModelIdField = typeof(DeviceModel).GetField("<Id>k__BackingField", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                deviceModelIdField.SetValue(deviceModels[i], deviceModelId);
                
                var manufacturerIdPropInfo = typeof(DeviceModel).GetProperty("ManufacturerId");
                manufacturerIdPropInfo.SetValue(deviceModels[i], manufacturerId);
                
                var manufacturerPropInfo = typeof(DeviceModel).GetProperty("Manufacturer");
                manufacturerPropInfo.SetValue(deviceModels[i], manufacturer);
            }

            _mockDeviceModelRepository.Setup(repo => repo.GetAllAsync())
                .ReturnsAsync(deviceModels);

            // Act
            var result = await _deviceModelQueryService.GetAllDeviceModelsAsync();

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count);
            Assert.AreEqual("Model 1", result[0].Name);
            Assert.AreEqual(DeviceRole.Camera, result[0].DeviceRole);
            Assert.AreEqual("Model 2", result[1].Name);
            Assert.AreEqual(DeviceRole.Radar, result[1].DeviceRole);
        }

        [TestMethod]
        public async Task GetDeviceModelsByManufacturerIdAsync_ReturnsDeviceModelsForManufacturer()
        {
            // Arrange
            var manufacturerId = Guid.NewGuid();
            var manufacturer = new Manufacturer("Test Manufacturer");
            
            // Set manufacturer Id using reflection
            var manufacturerIdField = typeof(Manufacturer).GetField("<Id>k__BackingField", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            manufacturerIdField.SetValue(manufacturer, manufacturerId);

            var deviceModels = new List<DeviceModel>
            {
                new DeviceModel("Model 1", DeviceRole.Camera),
                new DeviceModel("Model 2", DeviceRole.Radar)
            };
            
            // Set device model Ids and ManufacturerId using reflection
            for (int i = 0; i < deviceModels.Count; i++)
            {
                var deviceModelId = Guid.NewGuid();
                var deviceModelIdField = typeof(DeviceModel).GetField("<Id>k__BackingField", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                deviceModelIdField.SetValue(deviceModels[i], deviceModelId);
                
                var manufacturerIdPropInfo = typeof(DeviceModel).GetProperty("ManufacturerId");
                manufacturerIdPropInfo.SetValue(deviceModels[i], manufacturerId);
                
                var manufacturerPropInfo = typeof(DeviceModel).GetProperty("Manufacturer");
                manufacturerPropInfo.SetValue(deviceModels[i], manufacturer);
            }

            _mockDeviceModelRepository.Setup(repo => repo.GetDeviceModelsByManufacturerIdAsync(manufacturerId))
                .ReturnsAsync(deviceModels);

            // Act
            var result = await _deviceModelQueryService.GetDeviceModelsByManufacturerIdAsync(manufacturerId);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(2, result.Count);
            Assert.AreEqual("Model 1", result[0].Name);
            Assert.AreEqual(DeviceRole.Camera, result[0].DeviceRole);
            Assert.AreEqual("Model 2", result[1].Name);
            Assert.AreEqual(DeviceRole.Radar, result[1].DeviceRole);
        }

        [TestMethod]
        public async Task GetDeviceModelsForManufacturerWithRoleAsync_ReturnsDeviceModelsForManufacturerAndRole()
        {
            // Arrange
            var manufacturerId = Guid.NewGuid();
            var manufacturer = new Manufacturer("Test Manufacturer");
            
            // Set manufacturer Id using reflection
            var manufacturerIdField = typeof(Manufacturer).GetField("<Id>k__BackingField", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
            manufacturerIdField.SetValue(manufacturer, manufacturerId);

            var deviceModels = new List<DeviceModel>
            {
                new DeviceModel("Camera Model", DeviceRole.Camera)
            };
            
            // Set device model Ids and ManufacturerId using reflection
            for (int i = 0; i < deviceModels.Count; i++)
            {
                var deviceModelId = Guid.NewGuid();
                var deviceModelIdField = typeof(DeviceModel).GetField("<Id>k__BackingField", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);
                deviceModelIdField.SetValue(deviceModels[i], deviceModelId);
                
                var manufacturerIdPropInfo = typeof(DeviceModel).GetProperty("ManufacturerId");
                manufacturerIdPropInfo.SetValue(deviceModels[i], manufacturerId);
                
                var manufacturerPropInfo = typeof(DeviceModel).GetProperty("Manufacturer");
                manufacturerPropInfo.SetValue(deviceModels[i], manufacturer);
            }

            _mockDeviceModelRepository.Setup(repo => repo.GetDeviceModelsForManufacturerWithRoleAsync(manufacturerId, DeviceRole.Camera))
                .ReturnsAsync(deviceModels);

            // Act
            var result = await _deviceModelQueryService.GetDeviceModelsForManufacturerWithRoleAsync(manufacturerId, DeviceRole.Camera);

            // Assert
            Assert.IsNotNull(result);
            Assert.AreEqual(1, result.Count);
            Assert.AreEqual("Camera Model", result[0].Name);
            Assert.AreEqual(DeviceRole.Camera, result[0].DeviceRole);
        }
    }
}
