using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Moq;

namespace Barret.Services.Tests.Unit.Helpers
{
    public static class AsyncMockDbSetExtensions
    {
        public static Mock<DbSet<T>> BuildMockDbSet<T>(IEnumerable<T> data) where T : class
        {
            var queryableData = data.AsQueryable();
            var mockSet = new Mock<DbSet<T>>();

            mockSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(new TestAsyncQueryProvider<T>(queryableData.Provider));
            mockSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(queryableData.Expression);
            mockSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(queryableData.ElementType);
            mockSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(() => queryableData.GetEnumerator());

            mockSet.As<IAsyncEnumerable<T>>()
                .Setup(m => m.GetAsyncEnumerator(It.IsAny<CancellationToken>()))
                .Returns(new TestAsyncEnumerator<T>(data.GetEnumerator()));

            mockSet.Setup(m => m.FindAsync(It.IsAny<object[]>()))
                .Returns((object[] ids) =>
                {
                    var id = ids[0];
                    return ValueTask.FromResult(data.FirstOrDefault(d =>
                        d.GetType().GetProperty("Id").GetValue(d).Equals(id)));
                });

            mockSet.Setup(m => m.AddAsync(It.IsAny<T>(), It.IsAny<CancellationToken>()))
                .ReturnsAsync(EntityEntry<T>.Create);

            return mockSet;
        }

        private class TestAsyncQueryProvider<TEntity>(IQueryProvider inner) : IAsyncQueryProvider
        {
            private readonly IQueryProvider _inner = inner;

            public IQueryable CreateQuery(Expression expression)
            {
                return new TestAsyncEnumerable<TEntity>(expression);
            }

            public IQueryable<TElement> CreateQuery<TElement>(Expression expression)
            {
                return new TestAsyncEnumerable<TElement>(expression);
            }

            public object Execute(Expression expression)
            {
                return _inner.Execute(expression);
            }

            public TResult Execute<TResult>(Expression expression)
            {
                return _inner.Execute<TResult>(expression);
            }

            public TResult ExecuteAsync<TResult>(Expression expression, CancellationToken cancellationToken = default)
            {
                // Check if we're dealing with a Task
                if (typeof(TResult).IsGenericType && typeof(TResult).GetGenericTypeDefinition() == typeof(Task<>))
                {
                    // Get the actual result type
                    var resultType = typeof(TResult).GetGenericArguments()[0];

                    // Create a method call to the generic Execute<> method
                    var result = typeof(IQueryProvider)
                        .GetMethods()
                        .First(m => m.Name == "Execute" && m.IsGenericMethod)
                        .MakeGenericMethod(resultType)
                        .Invoke(_inner, new[] { expression });

                    // Create a Task with the result
                    var taskResult = typeof(Task).GetMethod("FromResult")
                        .MakeGenericMethod(resultType)
                        .Invoke(null, new[] { result });

                    return (TResult)taskResult;
                }

                // For non-task results, just execute directly
                return Execute<TResult>(expression);
            }
        }

        private class TestAsyncEnumerable<T> : TestEnumerable<T>, IAsyncEnumerable<T>, IQueryable<T>
        {
            public TestAsyncEnumerable(IEnumerable<T> enumerable) : base(enumerable) { }
            public TestAsyncEnumerable(Expression expression) : base(expression) { }

            public IAsyncEnumerator<T> GetAsyncEnumerator(CancellationToken cancellationToken = default)
            {
                return new TestAsyncEnumerator<T>(this.AsEnumerable().GetEnumerator());
            }

            IQueryProvider IQueryable.Provider => new TestAsyncQueryProvider<T>((this as IQueryable<T>).Provider);
        }

        private class TestEnumerable<T> : EnumerableQuery<T>, IQueryable<T>
        {
            public TestEnumerable(IEnumerable<T> enumerable) : base(enumerable) { }
            public TestEnumerable(Expression expression) : base(expression) { }
        }

        private class TestAsyncEnumerator<T>(IEnumerator<T> inner) : IAsyncEnumerator<T>
        {
            private readonly IEnumerator<T> _inner = inner;

            public T Current => _inner.Current;

            public ValueTask DisposeAsync()
            {
                _inner.Dispose();
                return ValueTask.CompletedTask;
            }

            public ValueTask<bool> MoveNextAsync()
            {
                return ValueTask.FromResult(_inner.MoveNext());
            }
        }

        private static class EntityEntry<T> where T : class
        {
            public static Microsoft.EntityFrameworkCore.ChangeTracking.EntityEntry<T> Create()
            {
                return null; // This is a simplification for the mock
            }
        }
    }
}