using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using Moq;
using static Barret.Services.Tests.Unit.Helpers.AsyncQueryableExtensions;

namespace Barret.Services.Tests.Unit.Helpers
{
    public static class MockDbSetExtensions
    {
        /// <summary>
        /// Builds a mock DbSet with both synchronous and asynchronous behaviors
        /// </summary>
        public static Mock<DbSet<T>> BuildMockDbSet<T>(this IQueryable<T> data) where T : class
        {
            var mockSet = new Mock<DbSet<T>>();

            // Setup for synchronous operations
            mockSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(data.Provider);
            mockSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(data.Expression);
            mockSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(data.ElementType);
            mockSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(() => data.GetEnumerator());

            // Setup for asynchronous operations
            mockSet.As<IAsyncEnumerable<T>>()
                .Setup(m => m.GetAsyncEnumerator(default))
                .Returns(new TestAsyncEnumerator<T>(data.GetEnumerator()));

            // Setup FirstOrDefaultAsync
            mockSet.Setup(m => m.FirstOrDefaultAsync(It.IsAny<Expression<Func<T, bool>>>(), It.IsAny<CancellationToken>()))
                .Returns((Expression<Func<T, bool>> predicate, CancellationToken token) =>
                    Task.FromResult(data.FirstOrDefault(predicate.Compile())));

            // Setup ToListAsync
            mockSet.Setup(m => m.ToListAsync(It.IsAny<CancellationToken>()))
                .Returns((CancellationToken token) => Task.FromResult(data.ToList()));

            // Setup Include
            mockSet.Setup(m => m.Include(It.IsAny<string>()))
                .Returns(mockSet.Object);

            // Setup Where
            mockSet.Setup(m => m.Where(It.IsAny<Expression<Func<T, bool>>>()))
                .Returns((Expression<Func<T, bool>> predicate) =>
                    data.Where(predicate.Compile()).AsQueryable());

            return mockSet;
        }

        /// <summary>
        /// Sets up a mock DbSet with both synchronous and asynchronous behaviors
        /// </summary>
        public static Mock<DbSet<T>> SetupDbSetMock<T>(this IEnumerable<T> data) where T : class
        {
            var queryableData = data.AsQueryable();
            var mockSet = new Mock<DbSet<T>>();

            // Setup for synchronous operations
            mockSet.As<IQueryable<T>>().Setup(m => m.Provider).Returns(queryableData.Provider);
            mockSet.As<IQueryable<T>>().Setup(m => m.Expression).Returns(queryableData.Expression);
            mockSet.As<IQueryable<T>>().Setup(m => m.ElementType).Returns(queryableData.ElementType);
            mockSet.As<IQueryable<T>>().Setup(m => m.GetEnumerator()).Returns(() => queryableData.GetEnumerator());

            // Setup for asynchronous operations
            mockSet.As<IAsyncEnumerable<T>>()
                .Setup(m => m.GetAsyncEnumerator(default))
                .Returns(new TestAsyncEnumerator<T>(queryableData.GetEnumerator()));

            // Setup Find method
            mockSet.Setup(m => m.Find(It.IsAny<object[]>()))
                .Returns<object[]>(ids => queryableData.FirstOrDefault(e => EqualityComparer<T>.Default.Equals((T)e, (T)ids[0])));

            mockSet.Setup(m => m.FindAsync(It.IsAny<object[]>()))
                .ReturnsAsync((object[] ids) => queryableData.FirstOrDefault(e => EqualityComparer<T>.Default.Equals((T)e, (T)ids[0])));

            // Setup Add methods
            mockSet.Setup(x => x.Add(It.IsAny<T>())).Callback<T>(data.ToList().Add);
            mockSet.Setup(x => x.AddAsync(It.IsAny<T>(), default)).ReturnsAsync((T entity, CancellationToken token) => {
                data.ToList().Add(entity);
                return null; // EntityEntry<T> would be returned here in real EF Core
            });

            return mockSet;
        }
    }
}