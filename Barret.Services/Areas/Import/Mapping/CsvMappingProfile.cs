using Barret.Shared.DTOs.Import;
using CsvHelper.Configuration;

namespace Barret.Services.Areas.Import.Mapping
{
    /// <summary>
    /// Mapping profile for CSV device records.
    /// </summary>
    public sealed class CsvMappingProfile : ClassMap<CsvDeviceRecord>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="CsvMappingProfile"/> class.
        /// Maps only the columns we need from the CSV.
        /// </summary>
        public CsvMappingProfile()
        {
            // Map only the columns we need from the CSV
            // These are case-insensitive by default
            Map(m => m.Name).Name("name");
            Map(m => m.DeviceType).Name("type");
            Map(m => m.Manufacturer).Name("manufacturer");
            Map(m => m.Model).Name("model");
            Map(m => m.IPAddress).Name("ip");
            Map(m => m.DeviceGroup).Name("site"); // Site column is used for VehicleId
        }
    }
}
