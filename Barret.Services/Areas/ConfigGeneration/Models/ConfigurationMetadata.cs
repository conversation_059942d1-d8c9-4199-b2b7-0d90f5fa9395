using System.Text.Json.Serialization;

namespace Barret.Services.Areas.ConfigGeneration.Models
{
    /// <summary>
    /// Represents metadata for configuration templates
    /// </summary>
    public class ConfigurationMetadata
    {
        /// <summary>
        /// Gets or sets the list of template entries
        /// </summary>
        [JsonPropertyName("templates")]
        public List<TemplateEntry> Templates { get; set; } = [];
    }

    /// <summary>
    /// Represents a template entry in the configuration metadata
    /// </summary>
    public class TemplateEntry
    {
        /// <summary>
        /// Gets or sets the path to the template file
        /// </summary>
        [JsonPropertyName("path")]
        public string Path { get; set; } = string.Empty;

        // Handler property removed as it's no longer needed

        /// <summary>
        /// Gets or sets the output path for the generated configuration
        /// </summary>
        [JsonPropertyName("outputPath")]
        public string OutputPath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the description of the template
        /// </summary>
        [JsonPropertyName("description")]
        public string? Description { get; set; }

        /// <summary>
        /// Gets or sets whether the template is required for a valid configuration
        /// </summary>
        [JsonPropertyName("isRequired")]
        public bool? IsRequired { get; set; }

        /// <summary>
        /// Gets or sets the dependencies of this template
        /// </summary>
        [JsonPropertyName("dependencies")]
        public List<string>? Dependencies { get; set; }
    }
}
