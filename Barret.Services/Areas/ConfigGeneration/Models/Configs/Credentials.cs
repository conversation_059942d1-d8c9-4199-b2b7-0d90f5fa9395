using System;
using System.Text.Json.Serialization;

namespace Barret.Services.Areas.ConfigGeneration.Models.Configs
{
    /// <summary>
    /// Represents credentials for authentication
    /// </summary>
    public class Credentials
    {
        /// <summary>
        /// Gets or sets the username
        /// </summary>
        [JsonPropertyName("username")]
        public string Username { get; set; }

        /// <summary>
        /// Gets or sets the password
        /// </summary>
        [JsonPropertyName("password")]
        public string Password { get; set; }

        /// <summary>
        /// Initializes a new instance of the Credentials class
        /// </summary>
        public Credentials()
        {
            Username = string.Empty;
            Password = string.Empty;
        }

        /// <summary>
        /// Initializes a new instance of the Credentials class with the specified username and password
        /// </summary>
        /// <param name="username">The username</param>
        /// <param name="password">The password</param>
        public Credentials(string username, string password)
        {
            if (string.IsNullOrWhiteSpace(username))
                throw new ArgumentException("Username cannot be empty", nameof(username));

            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("Password cannot be empty", nameof(password));

            Username = username;
            Password = password;
        }
    }
}
