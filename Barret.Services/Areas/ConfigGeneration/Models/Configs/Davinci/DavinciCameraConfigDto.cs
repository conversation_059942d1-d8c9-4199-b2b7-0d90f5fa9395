using System.Text.Json.Serialization;

namespace Barret.Services.Areas.ConfigGeneration.Models.Configs.Davinci
{
    /// <summary>
    /// Represents a Davinci-specific camera configuration
    /// </summary>
    public class DavinciCameraConfigDto
    {
        /// <summary>
        /// Gets or sets the name of the camera
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the IP address of the camera
        /// </summary>
        [JsonPropertyName("ip")]
        public string IpAddress { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the brand of the camera
        /// </summary>
        [JsonPropertyName("brand")]
        public string Brand { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the technical component ID
        /// </summary>
        [JsonPropertyName("technicalComponentId")]
        public string TechnicalComponentId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether to show video for this camera
        /// </summary>
        [JsonPropertyName("showVideo")]
        public bool ShowVideo { get; set; }

        /// <summary>
        /// Gets or sets the credentials for the camera
        /// </summary>
        [JsonPropertyName("credentials")]
        public CredentialsDto Credentials { get; set; } = new CredentialsDto();
    }

    /// <summary>
    /// Represents credentials for a camera
    /// </summary>
    public class CredentialsDto
    {
        /// <summary>
        /// Gets or sets the username
        /// </summary>
        [JsonPropertyName("username")]
        public string Username { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the password
        /// </summary>
        [JsonPropertyName("password")]
        public string Password { get; set; } = string.Empty;
    }
}
