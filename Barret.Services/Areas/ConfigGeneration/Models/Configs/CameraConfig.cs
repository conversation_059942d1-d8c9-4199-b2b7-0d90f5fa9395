using System;
using System.Text.Json.Serialization;

namespace Barret.Services.Areas.ConfigGeneration.Models.Configs
{
    /// <summary>
    /// Represents a camera configuration
    /// </summary>
    public class CameraConfig
    {
        /// <summary>
        /// Gets or sets the name of the camera
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the IP address of the camera
        /// </summary>
        [JsonPropertyName("ip")]
        public string IpAddress { get; set; }

        /// <summary>
        /// Gets or sets the port of the camera
        /// </summary>
        [JsonPropertyName("port")]
        public int Port { get; set; }

        /// <summary>
        /// Gets or sets the credentials for the camera
        /// </summary>
        [JsonPropertyName("credentials")]
        public Credentials Credentials { get; set; }

        /// <summary>
        /// Gets or sets whether the camera is enabled
        /// </summary>
        [JsonPropertyName("enabled")]
        public bool Enabled { get; set; }

        /// <summary>
        /// Gets or sets the camera's technical component ID
        /// </summary>
        [JsonPropertyName("technicalComponentId")]
        public string TechnicalComponentId { get; set; }

        /// <summary>
        /// Initializes a new instance of the CameraConfig class
        /// </summary>
        public CameraConfig()
        {
            Name = string.Empty;
            IpAddress = string.Empty;
            Port = 80;
            Credentials = new Credentials();
            Enabled = true;
            TechnicalComponentId = string.Empty;
        }

        /// <summary>
        /// Initializes a new instance of the CameraConfig class with the specified parameters
        /// </summary>
        /// <param name="name">The name of the camera</param>
        /// <param name="ipAddress">The IP address of the camera</param>
        /// <param name="port">The port of the camera</param>
        /// <param name="username">The username for authentication</param>
        /// <param name="password">The password for authentication</param>
        /// <param name="technicalComponentId">The camera's technical component ID</param>
        public CameraConfig(string name, string ipAddress, int port, string username, string password, string technicalComponentId)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Name cannot be empty", nameof(name));

            if (string.IsNullOrWhiteSpace(ipAddress))
                throw new ArgumentException("IP address cannot be empty", nameof(ipAddress));

            Name = name;
            IpAddress = ipAddress;
            Port = port;
            Credentials = new Credentials(username, password);
            Enabled = true;
            TechnicalComponentId = technicalComponentId ?? string.Empty;
        }
    }
}
