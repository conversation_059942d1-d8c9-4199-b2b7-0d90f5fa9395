using System;
using System.IO;
using Microsoft.Extensions.Logging;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;

namespace Barret.Services.Areas.ConfigGeneration.Handlers.Davinci
{
    /// <summary>
    /// Specialized handler for Davinci camera configuration files
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the DavinciCameraConfigHandler class
    /// </remarks>
    /// <param name="logger">The logger</param>
    /// <param name="cameraConfigMapper">The camera configuration mapper</param>
    public class DavinciCameraConfigHandler(
        ILogger<DavinciCameraConfigHandler> logger,
        ICameraConfigMapper cameraConfigMapper) : CameraConfigHandler(logger, cameraConfigMapper)
    {

        /// <summary>
        /// Gets the handler type identifier
        /// </summary>
        public override string HandlerType => "CameraConfig";

        /// <summary>
        /// Gets the system type this handler supports
        /// </summary>
        public override SystemType SupportedSystemType => SystemType.Davinci;

        /// <summary>
        /// Gets the config type this handler supports
        /// </summary>
        public override string SupportedConfigType => "Camera";
    }
}
