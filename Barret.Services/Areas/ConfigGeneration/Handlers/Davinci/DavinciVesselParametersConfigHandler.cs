using System;
using Microsoft.Extensions.Logging;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;

namespace Barret.Services.Areas.ConfigGeneration.Handlers.Davinci
{
    /// <summary>
    /// Specialized handler for Davinci vessel parameters configuration files
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the DavinciVesselParametersConfigHandler class
    /// </remarks>
    /// <param name="vesselParametersConfigMapper">The vessel parameters config mapper</param>
    /// <param name="logger">The logger</param>
    public class DavinciVesselParametersConfigHandler(
        IVesselParametersConfigMapper vesselParametersConfigMapper,
        ILogger<DavinciVesselParametersConfigHandler> logger) : VesselParametersConfigHandler(vesselParametersConfigMapper, logger)
    {

        /// <summary>
        /// Gets the system type this handler supports
        /// </summary>
        public override SystemType SupportedSystemType => SystemType.Davinci;
    }
}
