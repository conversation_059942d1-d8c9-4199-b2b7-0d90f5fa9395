using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Mappers;
using Barret.Shared.DTOs.DaVinciConfig.DriverConfig;

namespace Barret.Services.Areas.ConfigGeneration.Handlers.Davinci
{
    /// <summary>
    /// Handler for Davinci driver configuration files
    /// </summary>
    public class DavinciDriverConfigHandler : ConfigurationHandlerBase
    {
        private readonly IDriverConfigMapper _driverConfigMapper;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Initializes a new instance of the DavinciDriverConfigHandler class
        /// </summary>
        /// <param name="logger">The logger</param>
        /// <param name="driverConfigMapper">The driver configuration mapper</param>
        public DavinciDriverConfigHandler(
            ILogger<DavinciDriverConfigHandler> logger,
            IDriverConfigMapper driverConfigMapper)
            : base(logger)
        {
            _driverConfigMapper = driverConfigMapper ?? throw new ArgumentNullException(nameof(driverConfigMapper));

            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = null, // Use PascalCase (default .NET naming)
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.Never, // Never ignore properties
                IgnoreReadOnlyProperties = false, // Don't ignore read-only properties
                PropertyNameCaseInsensitive = false, // Case-sensitive property names
                IncludeFields = true, // Include fields in serialization
                Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() } // Properly handle enums
            };
        }

        /// <summary>
        /// Gets the handler type identifier
        /// </summary>
        public override string HandlerType => "DriverConfig";

        /// <summary>
        /// Gets the system type this handler supports
        /// </summary>
        public override SystemType SupportedSystemType => SystemType.Davinci;

        /// <summary>
        /// Gets the config type this handler supports
        /// </summary>
        public override string SupportedConfigType => "DriverConfigs";

        /// <summary>
        /// Generates a driver configuration for a vessel and writes it to the output path
        /// </summary>
        /// <param name="context">The configuration context</param>
        /// <param name="template">The template to use</param>
        /// <returns>The configuration output</returns>
        public override async Task<ConfigurationOutput> GenerateConfigurationAsync(ConfigurationContext context, ConfigurationTemplate template)
        {
            try
            {
                var vessel = context.Vessel;
                var outputPath = context.GetOutputFullPath(template.OutputPath);

                _logger.LogInformation("DAVINCIDRIVERCONFIGHANDLER: Generating Davinci driver configuration for vessel {VesselId} to {OutputPath}",
                    vessel.Id, outputPath);

                // Check for cancellation
                if (context.CancellationToken.IsCancellationRequested)
                {
                    _logger.LogWarning("DAVINCIDRIVERCONFIGHANDLER: Configuration generation cancelled for vessel {VesselId}", vessel.Id);
                    return ConfigurationOutput.Failure(template, "Configuration generation cancelled");
                }

                // Get all devices from the vessel and validate
                var devices = vessel.GetAllDevices().ToList();
                _logger.LogInformation("DAVINCIDRIVERCONFIGHANDLER: Found {DeviceCount} devices for vessel {VesselId}",
                    devices.Count, vessel.Id);

                // Check for duplicate devices at handler level
                var deviceIds = devices.Select(d => d.Id).ToList();
                var uniqueDeviceIds = deviceIds.Distinct().ToList();

                if (deviceIds.Count != uniqueDeviceIds.Count)
                {
                    var duplicateCount = deviceIds.Count - uniqueDeviceIds.Count;
                    _logger.LogError("DAVINCIDRIVERCONFIGHANDLER: CRITICAL ISSUE - Vessel {VesselId} has {DuplicateCount} duplicate devices at handler level! Total: {TotalCount}, Unique: {UniqueCount}",
                        vessel.Id, duplicateCount, deviceIds.Count, uniqueDeviceIds.Count);
                }
                else
                {
                    _logger.LogInformation("DAVINCIDRIVERCONFIGHANDLER: VALIDATION PASSED - No duplicate devices detected at handler level");
                }

                // Load the template file as the base configuration (contains static PLC data)
                var baseConfigContainer = await LoadTemplateAsBaseConfigurationAsync(context, template);

                // Generate dynamic driver configurations using the mapper
                // This includes GenericNmea0183, PingSweeper, and other driver configurations
                _logger.LogInformation("Generating dynamic driver configurations for vessel {VesselId}", vessel.Id);
                var dynamicConfigContainer = _driverConfigMapper.MapToDriverConfigContainer(vessel);

                _logger.LogInformation("[DavinciDriverConfigHandler] Generated dynamic driver configuration container with {GenericNmea0183Count} GenericNmea0183 configs, {EmtrackA100Count} EMtrackA100 configs, and {PingSweeperCount} PingSweeper configs",
                    dynamicConfigContainer.GenericNmea0183.Count, dynamicConfigContainer.EmtrackA100.Count, dynamicConfigContainer.PingSweeper.Count);

                // Merge the dynamic configurations with the base template configuration
                var finalConfigContainer = MergeConfigurations(baseConfigContainer, dynamicConfigContainer);

                // Log final configuration details
                LogFinalConfiguration(finalConfigContainer);

                // Generate JSON configuration
                string driverConfigJson = JsonSerializer.Serialize(finalConfigContainer, _jsonOptions);

                // Ensure the output directory exists and write the configuration
                EnsureOutputDirectoryExists(outputPath);
                await File.WriteAllTextAsync(outputPath, driverConfigJson);

                _logger.LogInformation("Successfully generated Davinci driver configuration for vessel {VesselId} at {OutputPath}",
                    vessel.Id, outputPath);

                return ConfigurationOutput.CreateSuccess(template, outputPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating Davinci driver configuration for vessel {VesselId}",
                    context.Vessel.Id);
                return ConfigurationOutput.Failure(template, $"Error generating Davinci driver configuration: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads the template file as the base configuration containing static data (like PLC configurations)
        /// </summary>
        /// <param name="context">The configuration context</param>
        /// <param name="template">The template to load</param>
        /// <returns>The base configuration container from the template</returns>
        private async Task<DriverConfigContainerDto> LoadTemplateAsBaseConfigurationAsync(ConfigurationContext context, ConfigurationTemplate template)
        {
            try
            {
                // Get the full path to the template file
                var templatePath = Path.Combine(context.TemplateBaseDirectory, template.Path);

                _logger.LogInformation("[DavinciDriverConfigHandler] Loading template as base configuration from {TemplatePath}", templatePath);

                // Check if the template file exists
                if (!File.Exists(templatePath))
                {
                    _logger.LogWarning("[DavinciDriverConfigHandler] Template file not found at {TemplatePath}, using empty base configuration", templatePath);
                    return new DriverConfigContainerDto();
                }

                // Read and deserialize the template file
                var templateContent = await File.ReadAllTextAsync(templatePath);
                var baseConfig = JsonSerializer.Deserialize<DriverConfigContainerDto>(templateContent, _jsonOptions);

                if (baseConfig == null)
                {
                    _logger.LogWarning("[DavinciDriverConfigHandler] Failed to deserialize template file, using empty base configuration");
                    return new DriverConfigContainerDto();
                }

                _logger.LogInformation("[DavinciDriverConfigHandler] Successfully loaded base configuration with {PlcCount} PLC configs from template",
                    baseConfig.Plc?.Count ?? 0);

                return baseConfig;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "[DavinciDriverConfigHandler] Error loading template as base configuration, using empty base configuration");
                return new DriverConfigContainerDto();
            }
        }

        /// <summary>
        /// Merges dynamic configurations with the base template configuration
        /// </summary>
        /// <param name="baseConfig">The base configuration from the template (contains static data like PLC)</param>
        /// <param name="dynamicConfig">The dynamic configuration generated from vessel data</param>
        /// <returns>The merged configuration container</returns>
        private DriverConfigContainerDto MergeConfigurations(DriverConfigContainerDto baseConfig, DriverConfigContainerDto dynamicConfig)
        {
            _logger.LogInformation("[DavinciDriverConfigHandler] Merging base configuration (static) with dynamic configuration");

            // Create a new container starting with the base configuration
            var mergedConfig = new DriverConfigContainerDto
            {
                // Preserve static configurations from template (like PLC)
                Plc = baseConfig.Plc ?? new List<FullDriverConfigDto>(),

                // Use dynamic configurations for device-based drivers
                GenericNmea0183 = dynamicConfig.GenericNmea0183 ?? new List<FullDriverConfigDto>(),
                EmtrackA100 = dynamicConfig.EmtrackA100 ?? new List<FullDriverConfigDto>(),
                PingSweeper = dynamicConfig.PingSweeper ?? new List<PingSweeperFullConfigDto>(),
                Radar = dynamicConfig.Radar ?? new List<FullDriverConfigDto>(),

                // For other driver types, use dynamic if available, otherwise preserve base
                SickLidar = dynamicConfig.SickLidar?.Any() == true ? dynamicConfig.SickLidar : (baseConfig.SickLidar ?? new List<FullDriverConfigDto>()),
                Can = dynamicConfig.Can?.Any() == true ? dynamicConfig.Can : (baseConfig.Can ?? new List<FullDriverConfigDto>()),
                XSens = dynamicConfig.XSens?.Any() == true ? dynamicConfig.XSens : (baseConfig.XSens ?? new List<FullDriverConfigDto>())
            };

            _logger.LogInformation("[DavinciDriverConfigHandler] Merge completed - Final config has {PlcCount} PLC, {GenericNmea0183Count} GenericNmea0183, {EmtrackA100Count} EmtrackA100, {PingSweeperCount} PingSweeper configs",
                mergedConfig.Plc.Count, mergedConfig.GenericNmea0183.Count, mergedConfig.EmtrackA100.Count, mergedConfig.PingSweeper.Count);

            return mergedConfig;
        }

        /// <summary>
        /// Logs the final configuration details for debugging
        /// </summary>
        /// <param name="finalConfig">The final merged configuration</param>
        private void LogFinalConfiguration(DriverConfigContainerDto finalConfig)
        {
            _logger.LogInformation("[DavinciDriverConfigHandler] Final configuration summary:");
            _logger.LogInformation("  - PLC configs: {PlcCount}", finalConfig.Plc?.Count ?? 0);
            _logger.LogInformation("  - GenericNmea0183 configs: {GenericNmea0183Count}", finalConfig.GenericNmea0183?.Count ?? 0);
            _logger.LogInformation("  - EmtrackA100 configs: {EmtrackA100Count}", finalConfig.EmtrackA100?.Count ?? 0);
            _logger.LogInformation("  - PingSweeper configs: {PingSweeperCount}", finalConfig.PingSweeper?.Count ?? 0);
            _logger.LogInformation("  - Radar configs: {RadarCount}", finalConfig.Radar?.Count ?? 0);
            _logger.LogInformation("  - SickLidar configs: {SickLidarCount}", finalConfig.SickLidar?.Count ?? 0);
            _logger.LogInformation("  - Can configs: {CanCount}", finalConfig.Can?.Count ?? 0);
            _logger.LogInformation("  - XSens configs: {XSensCount}", finalConfig.XSens?.Count ?? 0);

            // Log PLC configurations in detail (since they're static and important)
            if (finalConfig.Plc?.Any() == true)
            {
                foreach (var plcConfig in finalConfig.Plc)
                {
                    _logger.LogInformation("[DavinciDriverConfigHandler] PLC config: {Name}, DriverId: {DriverId}",
                        plcConfig.DriverConfig?.DriverIdentification?.Name ?? "Unknown",
                        plcConfig.DriverConfig?.DriverIdentification?.DriverId ?? "Unknown");
                }
            }

            // Log dynamic configurations for debugging
            if (finalConfig.GenericNmea0183?.Any() == true)
            {
                foreach (var config in finalConfig.GenericNmea0183)
                {
                    _logger.LogInformation("[DavinciDriverConfigHandler] GenericNmea0183 config: {Name}, IP: {IP}, Port: {Port}",
                        config.DriverConfig?.DriverIdentification?.Name ?? "Unknown",
                        config.ConnectionHandlerConfig?.ConnectionAddress ?? "Unknown",
                        config.ConnectionHandlerConfig?.ConnectionAddressOption.ToString() ?? "Unknown");
                }
            }

            if (finalConfig.EmtrackA100?.Any() == true)
            {
                foreach (var config in finalConfig.EmtrackA100)
                {
                    _logger.LogInformation("[DavinciDriverConfigHandler] EmtrackA100 config: {Name}, IP: {IP}, Port: {Port}",
                        config.DriverConfig?.DriverIdentification?.Name ?? "Unknown",
                        config.ConnectionHandlerConfig?.ConnectionAddress ?? "Unknown",
                        config.ConnectionHandlerConfig?.ConnectionAddressOption.ToString() ?? "Unknown");
                }
            }

            if (finalConfig.PingSweeper?.Any() == true)
            {
                foreach (var config in finalConfig.PingSweeper)
                {
                    _logger.LogInformation("[DavinciDriverConfigHandler] PingSweeper config with {DestinationCount} destinations",
                        config.DriverConfig?.PingDestinations?.Count ?? 0);
                }
            }
        }

        // The MapDeviceToDriverConfig method has been removed as it will be implemented later
        // when the GenericNmea0183 driver configuration is properly implemented
    }
}
