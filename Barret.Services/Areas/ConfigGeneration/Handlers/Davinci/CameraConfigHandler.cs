using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.DTOs.DaVinciConfig;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.ConfigGeneration.Handlers
{
    /// <summary>
    /// Handler for camera configuration files that require processing with vessel data
    /// </summary>
    public class CameraConfigHandler : ConfigurationHandlerBase
    {
        private readonly ICameraConfigMapper _cameraConfigMapper;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Initializes a new instance of the CameraConfigHandler class
        /// </summary>
        /// <param name="logger">The logger</param>
        /// <param name="cameraConfigMapper">The camera configuration mapper</param>
        public CameraConfigHandler(
            ILogger<CameraConfigHandler> logger,
            ICameraConfigMapper cameraConfigMapper)
            : base(logger)
        {
            _cameraConfigMapper = cameraConfigMapper ?? throw new ArgumentNullException(nameof(cameraConfigMapper));

            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = null, // Use PascalCase (default .NET naming)
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.Never, // Never ignore properties
                IgnoreReadOnlyProperties = false, // Don't ignore read-only properties
                PropertyNameCaseInsensitive = false, // Case-sensitive property names
                IncludeFields = true, // Include fields in serialization
                Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() } // Properly handle enums
            };
        }

        /// <summary>
        /// Gets the handler type identifier
        /// </summary>
        public override string HandlerType => "CameraConfig";

        /// <summary>
        /// Gets the config type this handler supports
        /// </summary>
        public override string SupportedConfigType => "Camera";

        /// <summary>
        /// Generates a camera configuration for a vessel and writes it to the output path
        /// </summary>
        /// <param name="context">The configuration context</param>
        /// <param name="template">The template to use</param>
        /// <returns>The configuration output</returns>
        public override async Task<ConfigurationOutput> GenerateConfigurationAsync(ConfigurationContext context, ConfigurationTemplate template)
        {
            try
            {
                var vessel = context.Vessel;
                var outputPath = context.GetOutputFullPath(template.OutputPath);

                _logger.LogInformation("Generating camera configuration for vessel {VesselId} to {OutputPath}",
                    vessel.Id, outputPath);

                // Check for cancellation
                if (context.CancellationToken.IsCancellationRequested)
                {
                    _logger.LogWarning("Configuration generation cancelled for vessel {VesselId}", vessel.Id);
                    return ConfigurationOutput.Failure(template, "Configuration generation cancelled");
                }

                // Get only Camera devices from the vessel using the domain method
                var cameraDevices = vessel.GetDevicesByRole(DeviceRole.Camera).Cast<Camera>().ToList();
                _logger.LogInformation("Found {CameraCount} camera devices for vessel {VesselId}",
                    cameraDevices.Count, vessel.Id);

                // Log detailed information about each camera for debugging
                foreach (var camera in cameraDevices)
                {
                    _logger.LogInformation("[CameraConfigHandler] Camera {CameraId} details - Name: {Name}, ShowVideo: {ShowVideo}, HasConnection: {HasConnection}, HasModel: {HasModel}",
                        camera.Id, camera.Name, camera.ShowVideo, camera.HasConnection(), camera.Model != null);

                    if (camera.Connection != null)
                    {
                        _logger.LogInformation("[CameraConfigHandler] Camera {CameraId} connection - IP: {IP}, Port: {Port}",
                            camera.Id, camera.Connection.IPAddress, camera.Connection.Port);
                    }
                    else
                    {
                        _logger.LogWarning("[CameraConfigHandler] Camera {CameraId} has no connection!", camera.Id);
                    }

                    if (camera.Model != null)
                    {
                        _logger.LogInformation("[CameraConfigHandler] Camera {CameraId} model - Name: {ModelName}, Manufacturer: {Manufacturer}",
                            camera.Id, camera.Model.Name, camera.Model.Manufacturer?.Name ?? "NULL");
                    }
                    else
                    {
                        _logger.LogWarning("[CameraConfigHandler] Camera {CameraId} has no model!", camera.Id);
                    }
                }

                // Map cameras to configuration DTOs
                var cameraConfigs = MapCamerasToConfigs(cameraDevices);

                // Generate JSON configuration
                string cameraConfigJson = GenerateJsonConfiguration(cameraConfigs);

                // Ensure the output directory exists and write the configuration
                EnsureOutputDirectoryExists(outputPath);
                await File.WriteAllTextAsync(outputPath, cameraConfigJson);

                _logger.LogInformation("Successfully generated camera configuration for vessel {VesselId} at {OutputPath}",
                    vessel.Id, outputPath);

                return ConfigurationOutput.CreateSuccess(template, outputPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating camera configuration for vessel {VesselId}",
                    context.Vessel.Id);
                return ConfigurationOutput.Failure(template, $"Error generating camera configuration: {ex.Message}");
            }
        }

        /// <summary>
        /// Maps a list of Camera entities to CameraConfigDto objects
        /// </summary>
        /// <param name="cameras">The list of Camera entities to map</param>
        /// <returns>A list of CameraConfigDto objects</returns>
        private List<CameraConfigDto> MapCamerasToConfigs(List<Camera> cameras)
        {
            if (cameras.Count == 0)
            {
                _logger.LogWarning("No camera devices to map");
                return new List<CameraConfigDto>();
            }

            var cameraConfigs = new List<CameraConfigDto>();

            foreach (var camera in cameras)
            {
                _logger.LogInformation("[CameraConfigHandler] Processing Camera {CameraId} with ShowVideo={ShowVideo}",
                    camera.Id, camera.ShowVideo);

                // Map the camera to a configuration DTO
                var cameraConfig = _cameraConfigMapper.MapToCameraConfig(camera);

                _logger.LogInformation("[CameraConfigHandler] Mapped Camera {CameraId} to config with ShowVideo={ShowVideo}",
                    camera.Id, cameraConfig.ShowVideo);

                // Ensure the ShowVideo property is correctly set
                cameraConfig.ShowVideo = camera.ShowVideo;

                cameraConfigs.Add(cameraConfig);
            }

            return cameraConfigs;
        }

        /// <summary>
        /// Generates a JSON configuration from a list of CameraConfigDto objects
        /// </summary>
        /// <param name="cameraConfigs">The list of CameraConfigDto objects</param>
        /// <returns>A JSON string representation of the camera configurations</returns>
        private string GenerateJsonConfiguration(List<CameraConfigDto> cameraConfigs)
        {
            if (cameraConfigs.Count == 0)
            {
                _logger.LogWarning("No camera configurations to serialize");
                return "[]";
            }

            try
            {
                // Serialize the camera configurations to JSON
                var json = JsonSerializer.Serialize(cameraConfigs, _jsonOptions);
                _logger.LogDebug("Successfully serialized {Count} camera configurations", cameraConfigs.Count);
                return json;
            }
            catch (Exception ex)
            {
                _logger.LogError("Error serializing camera configurations: {Error}", ex.Message);

                // Fallback to manual JSON creation
                return GenerateFallbackJson(cameraConfigs);
            }
        }

        /// <summary>
        /// Generates a fallback JSON configuration when serialization fails
        /// </summary>
        /// <param name="cameraConfigs">The list of CameraConfigDto objects</param>
        /// <returns>A manually created JSON string</returns>
        private string GenerateFallbackJson(List<CameraConfigDto> cameraConfigs)
        {
            if (cameraConfigs.Count == 0)
            {
                return "[]";
            }

            var jsonBuilder = new System.Text.StringBuilder();
            jsonBuilder.Append("[\n");

            for (int i = 0; i < cameraConfigs.Count; i++)
            {
                var config = cameraConfigs[i];
                jsonBuilder.Append("  {\n");
                jsonBuilder.Append($"    \"Name\": \"{config.Name}\",\n");
                jsonBuilder.Append($"    \"Ip\": \"{config.Ip}\",\n");
                jsonBuilder.Append($"    \"Brand\": \"{config.Brand}\",\n");
                jsonBuilder.Append($"    \"TechnicalComponentId\": \"{config.TechnicalComponentId}\",\n");
                jsonBuilder.Append($"    \"ShowVideo\": {config.ShowVideo.ToString().ToLowerInvariant()},\n");
                jsonBuilder.Append("    \"Credentials\": {\n");
                jsonBuilder.Append($"      \"UserName\": \"{config.Credentials.UserName}\",\n");
                jsonBuilder.Append($"      \"Password\": \"{config.Credentials.Password}\"\n");
                jsonBuilder.Append("    }\n");

                if (i < cameraConfigs.Count - 1)
                {
                    jsonBuilder.Append("  },\n");
                }
                else
                {
                    jsonBuilder.Append("  }\n");
                }
            }

            jsonBuilder.Append("]\n");
            _logger.LogWarning("Used fallback JSON generation due to serialization error");

            return jsonBuilder.ToString();
        }
    }
}
