using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Interfaces;

namespace Barret.Services.Areas.ConfigGeneration.Handlers.Shared
{
    /// <summary>
    /// Base handler for vessel parameters configuration files
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the VesselParametersConfigHandlerBase class
    /// </remarks>
    /// <param name="vesselParametersConfigMapper">The vessel parameters config mapper</param>
    /// <param name="logger">The logger</param>
    public abstract class VesselParametersConfigHandlerBase(
        IVesselParametersConfigMapper vesselParametersConfigMapper,
        ILogger logger) : ConfigurationHandlerBase(logger)
    {
        private readonly IVesselParametersConfigMapper _vesselParametersConfigMapper = vesselParametersConfigMapper ?? throw new ArgumentNullException(nameof(vesselParametersConfigMapper));

        /// <summary>
        /// Gets the handler type identifier
        /// </summary>
        public override string HandlerType => "VesselParametersConfig";

        /// <summary>
        /// Gets the config type this handler supports
        /// </summary>
        public override string SupportedConfigType => "VesselParameters";

        /// <summary>
        /// Generates a vessel parameters configuration for a vessel and writes it to the output path
        /// </summary>
        /// <param name="context">The configuration context</param>
        /// <param name="template">The template to use</param>
        /// <returns>The configuration output</returns>
        public override async Task<ConfigurationOutput> GenerateConfigurationAsync(ConfigurationContext context, ConfigurationTemplate template)
        {
            try
            {
                var vessel = context.Vessel;
                var outputPath = context.GetOutputFullPath(template.OutputPath);

                _logger.LogInformation("Generating vessel parameters configuration for vessel {VesselId} to {OutputPath}",
                    vessel.Id, outputPath);

                // Check for cancellation
                if (context.CancellationToken.IsCancellationRequested)
                {
                    _logger.LogWarning("Configuration generation cancelled for vessel {VesselId}", vessel.Id);
                    return ConfigurationOutput.Failure(template, "Configuration generation cancelled");
                }

                // Map vessel to configuration DTO
                var vesselParametersConfig = _vesselParametersConfigMapper.MapToConfig(vessel);

                // Generate JSON configuration and write to file
                await WriteOutputAsJsonAsync(outputPath, vesselParametersConfig);

                _logger.LogInformation("Successfully generated vessel parameters configuration for vessel {VesselId} at {OutputPath}",
                    vessel.Id, outputPath);

                return ConfigurationOutput.CreateSuccess(template, outputPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating vessel parameters configuration from {TemplatePath} for vessel {VesselId}",
                    template.Path, context.Vessel.Id);
                return ConfigurationOutput.Failure(template, $"Error generating vessel parameters configuration: {ex.Message}");
            }
        }
    }
}
