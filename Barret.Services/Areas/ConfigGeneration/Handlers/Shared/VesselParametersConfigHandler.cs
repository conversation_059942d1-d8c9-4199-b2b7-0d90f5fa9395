using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.DTOs.DaVinciConfig;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.ConfigGeneration.Handlers
{
    /// <summary>
    /// Handler for vessel parameters configuration files that require processing with vessel data
    /// </summary>
    public class VesselParametersConfigHandler : ConfigurationHandlerBase
    {
        private readonly IVesselParametersConfigMapper _vesselParametersConfigMapper;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Gets the handler type identifier
        /// </summary>
        public override string HandlerType => "VesselParametersConfig";

        /// <summary>
        /// Gets the config type this handler supports
        /// </summary>
        public override string SupportedConfigType => "VesselParameters";

        /// <summary>
        /// Initializes a new instance of the VesselParametersConfigHandler class
        /// </summary>
        /// <param name="vesselParametersConfigMapper">The vessel parameters config mapper</param>
        /// <param name="logger">The logger</param>
        public VesselParametersConfigHandler(
            IVesselParametersConfigMapper vesselParametersConfigMapper,
            ILogger<VesselParametersConfigHandler> logger)
            : base(logger)
        {
            _vesselParametersConfigMapper = vesselParametersConfigMapper ?? throw new ArgumentNullException(nameof(vesselParametersConfigMapper));

            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
        }

        /// <summary>
        /// Generates a vessel parameters configuration for a vessel and writes it to the output path
        /// </summary>
        /// <param name="context">The configuration context</param>
        /// <param name="template">The template to use</param>
        /// <returns>The configuration output</returns>
        public override async Task<ConfigurationOutput> GenerateConfigurationAsync(ConfigurationContext context, ConfigurationTemplate template)
        {
            try
            {
                var vessel = context.Vessel;
                var outputPath = context.GetOutputFullPath(template.OutputPath);

                _logger.LogInformation("Generating vessel parameters configuration for vessel {VesselId} to {OutputPath}",
                    vessel.Id, outputPath);

                // Check for cancellation
                if (context.CancellationToken.IsCancellationRequested)
                {
                    _logger.LogWarning("Configuration generation cancelled for vessel {VesselId}", vessel.Id);
                    return ConfigurationOutput.Failure(template, "Configuration generation cancelled");
                }

                // Map vessel to configuration DTO
                var vesselParametersConfig = _vesselParametersConfigMapper.MapToConfig(vessel);

                // Generate JSON configuration
                string vesselParametersConfigJson = JsonSerializer.Serialize(vesselParametersConfig, _jsonOptions);

                // Ensure the output directory exists and write the configuration
                EnsureOutputDirectoryExists(outputPath);
                await File.WriteAllTextAsync(outputPath, vesselParametersConfigJson);

                _logger.LogInformation("Successfully generated vessel parameters configuration for vessel {VesselId} at {OutputPath}",
                    vessel.Id, outputPath);

                return ConfigurationOutput.CreateSuccess(template, outputPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating vessel parameters configuration from {TemplatePath} for vessel {VesselId}",
                    template.Path, context.Vessel.Id);
                return ConfigurationOutput.Failure(template, $"Error generating vessel parameters configuration: {ex.Message}");
            }
        }
    }
}
