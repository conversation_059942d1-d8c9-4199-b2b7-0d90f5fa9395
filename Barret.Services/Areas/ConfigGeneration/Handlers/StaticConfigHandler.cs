using System;
using System.IO;
using System.Threading.Tasks;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.Results;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.ConfigGeneration.Handlers
{
    /// <summary>
    /// Handler for static configuration files that don't require any processing
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the StaticConfigHandler class
    /// </remarks>
    /// <param name="logger">The logger</param>
    public class StaticConfigHandler(ILogger<StaticConfigHandler> logger) : ConfigurationHandlerBase(logger)
    {

        /// <summary>
        /// Gets the handler type identifier
        /// </summary>
        public override string HandlerType => "StaticConfig";

        /// <summary>
        /// Gets the system type this handler supports
        /// </summary>
        public override SystemType SupportedSystemType => SystemType.Unknown;

        /// <summary>
        /// Gets the config type this handler supports
        /// </summary>
        public override string SupportedConfigType => string.Empty;

        /// <summary>
        /// Generates a configuration by copying the template file to the output path without modification
        /// </summary>
        /// <param name="context">The configuration context</param>
        /// <param name="template">The template to use</param>
        /// <returns>The configuration output</returns>
        public override async Task<ConfigurationOutput> GenerateConfigurationAsync(ConfigurationContext context, ConfigurationTemplate template)
        {
            try
            {
                _logger.LogInformation("Generating static configuration from template {TemplatePath} for vessel {VesselId}",
                    template.Path, context.Vessel.Id);

                // Check if the template file exists
                var templatePath = context.GetTemplateFullPath(template.Path);
                if (!File.Exists(templatePath))
                {
                    _logger.LogError("Template file not found: {TemplatePath}", templatePath);
                    return ConfigurationOutput.Failure(template, $"Template file not found: {templatePath}");
                }

                // Check for cancellation
                if (context.CancellationToken.IsCancellationRequested)
                {
                    _logger.LogWarning("Configuration generation cancelled for template {TemplatePath}", template.Path);
                    return ConfigurationOutput.Failure(template, "Configuration generation cancelled");
                }

                // Get the output path
                var outputPath = context.GetOutputFullPath(template.OutputPath);

                // Ensure the output directory exists
                EnsureOutputDirectoryExists(outputPath);

                // Copy the template file to the output path
                await CopyFileAsync(templatePath, outputPath);

                _logger.LogInformation("Successfully generated static configuration for vessel {VesselId} at {OutputPath}",
                    context.Vessel.Id, outputPath);

                return ConfigurationOutput.CreateSuccess(template, outputPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating static configuration from {TemplatePath} for vessel {VesselId}",
                    template.Path, context.Vessel.Id);
                return ConfigurationOutput.Failure(template, $"Error generating static configuration: {ex.Message}");
            }
        }
    }
}
