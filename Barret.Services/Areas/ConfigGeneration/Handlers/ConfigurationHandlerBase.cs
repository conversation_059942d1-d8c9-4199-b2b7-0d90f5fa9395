using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Utilities;

namespace Barret.Services.Areas.ConfigGeneration.Handlers
{
    /// <summary>
    /// Base class for configuration handlers
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the ConfigurationHandlerBase class
    /// </remarks>
    /// <param name="logger">The logger</param>
    public abstract class ConfigurationHandlerBase(ILogger logger) : IConfigurationHandler
    {
        protected readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Gets the handler type identifier
        /// </summary>
        public abstract string HandlerType { get; }

        /// <summary>
        /// Gets the system type this handler supports
        /// </summary>
        public virtual SystemType SupportedSystemType => SystemType.Unknown;

        /// <summary>
        /// Gets the config type this handler supports
        /// </summary>
        public virtual string SupportedConfigType => string.Empty;

        /// <summary>
        /// Generates a configuration from a template and writes it to the output path
        /// </summary>
        /// <param name="context">The configuration context</param>
        /// <param name="template">The template to use</param>
        /// <returns>The configuration output</returns>
        public abstract Task<ConfigurationOutput> GenerateConfigurationAsync(ConfigurationContext context, ConfigurationTemplate template);

        /// <summary>
        /// Checks if this handler can handle the specified template
        /// </summary>
        /// <param name="template">The template to check</param>
        /// <returns>True if this handler can handle the template, false otherwise</returns>
        /// <remarks>
        /// This method is kept for interface compatibility but is no longer used for handler resolution.
        /// The ConfigurationHandlerProvider now determines the appropriate handler based on file path patterns.
        /// Override this method only if needed for backward compatibility with existing code.
        /// </remarks>
        public virtual bool CanHandle(ConfigurationTemplate template)
        {
            return false;
        }

        /// <summary>
        /// Checks if this handler can handle the specified template path
        /// </summary>
        /// <param name="templatePath">The path to the template</param>
        /// <returns>True if this handler can handle the template path, false otherwise</returns>
        /// <remarks>
        /// This default implementation checks if the path matches the supported system and config type.
        /// Override this method in derived classes to provide custom path-based handler resolution logic.
        /// </remarks>
        public virtual bool CanHandlePath(string templatePath)
        {
            if (string.IsNullOrEmpty(templatePath))
                return false;

            // If this is a system-specific handler, check if the path matches the system and config type
            if (SupportedSystemType != SystemType.Unknown && !string.IsNullOrEmpty(SupportedConfigType))
            {
                return TemplatePathParser.MatchesSystemAndConfigType(
                    templatePath, SupportedSystemType, SupportedConfigType);
            }

            return false;
        }

        /// <summary>
        /// Ensures the output directory exists
        /// </summary>
        /// <param name="outputPath">The output path</param>
        protected void EnsureOutputDirectoryExists(string outputPath)
        {
            var outputDirectory = Path.GetDirectoryName(outputPath);
            if (!string.IsNullOrEmpty(outputDirectory) && !Directory.Exists(outputDirectory))
            {
                _logger.LogDebug("Creating directory: {Directory}", outputDirectory);
                Directory.CreateDirectory(outputDirectory);
            }
        }

        /// <summary>
        /// Copies a file asynchronously
        /// </summary>
        /// <param name="sourcePath">The source path</param>
        /// <param name="destinationPath">The destination path</param>
        /// <returns>A task representing the asynchronous operation</returns>
        protected async Task CopyFileAsync(string sourcePath, string destinationPath)
        {
            EnsureOutputDirectoryExists(destinationPath);

            using var sourceStream = new FileStream(sourcePath, FileMode.Open, FileAccess.Read, FileShare.Read, 4096, true);
            using var destinationStream = new FileStream(destinationPath, FileMode.Create, FileAccess.Write, FileShare.None, 4096, true);

            await sourceStream.CopyToAsync(destinationStream);
        }

        /// <summary>
        /// Writes an object as JSON to the specified output path
        /// </summary>
        /// <typeparam name="T">The type of object to write</typeparam>
        /// <param name="outputPath">The output path</param>
        /// <param name="value">The object to write</param>
        /// <returns>A task representing the asynchronous operation</returns>
        protected async Task WriteOutputAsJsonAsync<T>(string outputPath, T value)
        {
            EnsureOutputDirectoryExists(outputPath);

            var jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };

            string json = JsonSerializer.Serialize(value, jsonOptions);
            await File.WriteAllTextAsync(outputPath, json);
        }
    }
}
