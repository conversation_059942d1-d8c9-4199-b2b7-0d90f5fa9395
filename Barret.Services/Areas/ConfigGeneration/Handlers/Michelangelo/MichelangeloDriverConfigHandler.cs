using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Devices.Models;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;

namespace Barret.Services.Areas.ConfigGeneration.Handlers.Michelangelo
{
    /// <summary>
    /// Handler for Michelangelo driver configuration files
    /// </summary>
    public class MichelangeloDriverConfigHandler : ConfigurationHandlerBase
    {
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Initializes a new instance of the MichelangeloDriverConfigHandler class
        /// </summary>
        /// <param name="logger">The logger</param>
        public MichelangeloDriverConfigHandler(
            ILogger<MichelangeloDriverConfigHandler> logger)
            : base(logger)
        {
            _jsonOptions = new JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = null, // Use PascalCase (default .NET naming)
                DefaultIgnoreCondition = System.Text.Json.Serialization.JsonIgnoreCondition.Never, // Never ignore properties
                IgnoreReadOnlyProperties = false, // Don't ignore read-only properties
                PropertyNameCaseInsensitive = false, // Case-sensitive property names
                IncludeFields = true, // Include fields in serialization
                Converters = { new System.Text.Json.Serialization.JsonStringEnumConverter() } // Properly handle enums
            };
        }

        /// <summary>
        /// Gets the handler type identifier
        /// </summary>
        public override string HandlerType => "DriverConfig";

        /// <summary>
        /// Gets the system type this handler supports
        /// </summary>
        public override SystemType SupportedSystemType => SystemType.Michelangelo;

        /// <summary>
        /// Gets the config type this handler supports
        /// </summary>
        public override string SupportedConfigType => "Driver";

        /// <summary>
        /// Generates a driver configuration for a vessel and writes it to the output path
        /// </summary>
        /// <param name="context">The configuration context</param>
        /// <param name="template">The template to use</param>
        /// <returns>The configuration output</returns>
        public override async Task<ConfigurationOutput> GenerateConfigurationAsync(ConfigurationContext context, ConfigurationTemplate template)
        {
            try
            {
                var vessel = context.Vessel;
                var outputPath = context.GetOutputFullPath(template.OutputPath);

                _logger.LogInformation("Generating Michelangelo driver configuration for vessel {VesselId} to {OutputPath}",
                    vessel.Id, outputPath);

                // Check for cancellation
                if (context.CancellationToken.IsCancellationRequested)
                {
                    _logger.LogWarning("Configuration generation cancelled for vessel {VesselId}", vessel.Id);
                    return ConfigurationOutput.Failure(template, "Configuration generation cancelled");
                }

                // Get all devices from the vessel
                var devices = vessel.GetAllDevices().ToList();
                _logger.LogInformation("Found {DeviceCount} devices for vessel {VesselId}",
                    devices.Count, vessel.Id);

                // For now, just copy the template as-is
                // In a real implementation, we would process the devices and generate a Michelangelo-specific driver configuration
                var templateContent = await File.ReadAllTextAsync(template.Path);
                
                // Ensure the output directory exists and write the configuration
                EnsureOutputDirectoryExists(outputPath);
                await File.WriteAllTextAsync(outputPath, templateContent);

                _logger.LogInformation("Successfully generated Michelangelo driver configuration for vessel {VesselId} at {OutputPath}",
                    vessel.Id, outputPath);

                return ConfigurationOutput.CreateSuccess(template, outputPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating Michelangelo driver configuration for vessel {VesselId}",
                    context.Vessel.Id);
                return ConfigurationOutput.Failure(template, $"Error generating Michelangelo driver configuration: {ex.Message}");
            }
        }
    }
}
