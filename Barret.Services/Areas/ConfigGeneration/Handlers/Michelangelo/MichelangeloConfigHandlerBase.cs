using Microsoft.Extensions.Logging;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Handlers;

namespace Barret.Services.Areas.ConfigGeneration.Handlers.Michelangelo
{
    /// <summary>
    /// Base class for Michel<PERSON> configuration handlers
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the MichelangeloConfigHandlerBase class
    /// </remarks>
    /// <param name="logger">The logger</param>
    public abstract class MichelangeloConfigHandlerBase(ILogger logger) : ConfigurationHandlerBase(logger)
    {

        /// <summary>
        /// Gets the system type this handler supports
        /// </summary>
        public override SystemType SupportedSystemType => SystemType.Michelangelo;
    }
}
