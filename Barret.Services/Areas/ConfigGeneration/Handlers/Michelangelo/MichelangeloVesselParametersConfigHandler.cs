using System;
using Microsoft.Extensions.Logging;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;

namespace Barret.Services.Areas.ConfigGeneration.Handlers.Michelangelo
{
    /// <summary>
    /// Specialized handler for Michel<PERSON> vessel parameters configuration files
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the MichelangeloVesselParametersConfigHandler class
    /// </remarks>
    /// <param name="vesselParametersConfigMapper">The vessel parameters config mapper</param>
    /// <param name="logger">The logger</param>
    public class MichelangeloVesselParametersConfigHandler(
        IVesselParametersConfigMapper vesselParametersConfigMapper,
        ILogger<MichelangeloVesselParametersConfigHandler> logger) : VesselParametersConfigHandler(vesselParametersConfigMapper, logger)
    {

        /// <summary>
        /// Gets the system type this handler supports
        /// </summary>
        public override SystemType SupportedSystemType => SystemType.Michelangelo;
    }
}
