using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using Microsoft.Extensions.Logging;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Interfaces;

namespace Barret.Services.Areas.ConfigGeneration.Services
{
    /// <summary>
    /// Registry for configuration handlers with explicit path pattern registration
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the HandlerRegistry class
    /// </remarks>
    /// <param name="logger">The logger</param>
    /// <param name="staticHandler">The static handler to use as fallback</param>
    public class HandlerRegistry(
        ILogger<HandlerRegistry> logger,
        IConfigurationHandler staticHandler) : IHandlerRegistry
    {
        private readonly ILogger<HandlerRegistry> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly IConfigurationHandler _staticHandler = staticHandler ?? throw new ArgumentNullException(nameof(staticHandler));
        private readonly List<(string Pattern, Regex Regex, IConfigurationHandler Handler)> _handlers = [];
        
        /// <summary>
        /// Registers a handler with a path pattern
        /// </summary>
        /// <param name="pathPattern">The path pattern to match (supports * wildcard)</param>
        /// <param name="handler">The handler to register</param>
        public void RegisterHandler(string pathPattern, IConfigurationHandler handler)
        {
            if (string.IsNullOrEmpty(pathPattern))
                throw new ArgumentException("Path pattern cannot be null or empty", nameof(pathPattern));
            
            if (handler == null)
                throw new ArgumentNullException(nameof(handler));
            
            // Convert the pattern to a regex
            // Replace * with .* and escape other special characters
            var regexPattern = "^" + Regex.Escape(pathPattern).Replace("\\*", ".*") + "$";
            var regex = new Regex(regexPattern, RegexOptions.IgnoreCase);
            
            _handlers.Add((pathPattern, regex, handler));
            
            _logger.LogInformation("Registered handler {HandlerType} for path pattern {PathPattern}",
                handler.HandlerType, pathPattern);
        }
        
        /// <summary>
        /// Gets a handler for a template path
        /// </summary>
        /// <param name="templatePath">The template path</param>
        /// <returns>The handler, or null if no handler is found</returns>
        public IConfigurationHandler? GetHandler(string templatePath)
        {
            if (string.IsNullOrEmpty(templatePath))
                return null;
            
            // Find the first handler with a matching pattern
            foreach (var (pattern, regex, handler) in _handlers)
            {
                if (regex.IsMatch(templatePath))
                {
                    _logger.LogDebug("Found handler {HandlerType} for template {TemplatePath} using pattern {Pattern}",
                        handler.HandlerType, templatePath, pattern);
                    return handler;
                }
            }
            
            // Fall back to the static handler
            _logger.LogInformation("Using static handler for template {TemplatePath}", templatePath);
            return _staticHandler;
        }
        
        /// <summary>
        /// Gets all registered handlers
        /// </summary>
        /// <returns>The registered handlers</returns>
        public IReadOnlyList<IConfigurationHandler> GetAllHandlers()
        {
            return _handlers.Select(h => h.Handler).Distinct().ToList();
        }
    }
}
