using System;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.Results;

namespace Barret.Services.Areas.ConfigGeneration.Services
{
    /// <summary>
    /// Service for exporting configurations using the configuration system
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the ConfigExportService class
    /// </remarks>
    /// <param name="logger">The logger</param>
    /// <param name="configGenerationService">The configuration generation service</param>
    /// <param name="outputService">The configuration output service</param>
    public class ConfigExportService(
        ILogger<ConfigExportService> logger,
        IConfigGenerationService configGenerationService,
        IConfigurationOutputService outputService) : IConfigExportService
    {
        private readonly ILogger<ConfigExportService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly IConfigGenerationService _configGenerationService = configGenerationService ?? throw new ArgumentNullException(nameof(configGenerationService));
        private readonly IConfigurationOutputService _outputService = outputService ?? throw new ArgumentNullException(nameof(outputService));

        /// <summary>
        /// Exports configurations for a vessel to a ZIP file
        /// </summary>
        /// <param name="vessel">The vessel to export configurations for</param>
        /// <returns>A result containing the path to the ZIP file</returns>
        public async Task<Result<string>> ExportConfigurationsToZipAsync(Vessel vessel)
        {
            if (vessel == null)
            {
                return Result.Failure<string>("Vessel cannot be null");
            }

            string? tempDir = null;
            try
            {
                _logger.LogInformation("Exporting configurations for vessel {VesselId}", vessel.Id);

                // Create a temporary directory for the configs
                var tempDirResult = await _outputService.CreateTemporaryDirectoryAsync(vessel);
                if (!tempDirResult.Success)
                {
                    return Result.Failure<string>($"Failed to create temporary directory: {tempDirResult.ErrorMessage}");
                }

                tempDir = tempDirResult.Data;

                // Use a progress reporter to track generation progress
                var progress = new Progress<int>(percent =>
                {
                    _logger.LogDebug("Configuration generation progress: {Percent}%", percent);
                });

                // Generate all configurations to the output directory
                var result = await _configGenerationService.GenerateConfigurationsAsync(
                    vessel,
                    tempDir,
                    progress,
                    CancellationToken.None);

                if (!result.Success)
                {
                    return Result.Failure<string>($"Failed to generate configurations: {result.ErrorMessage}");
                }

                // Create a ZIP file from the temporary directory
                var zipResult = await _outputService.CreateZipFileAsync(tempDir, vessel);
                if (!zipResult.Success)
                {
                    return Result.Failure<string>($"Failed to create ZIP file: {zipResult.ErrorMessage}");
                }

                var zipPath = zipResult.Data;
                _logger.LogInformation("Successfully exported configurations for vessel {VesselId} to {ZipPath}", vessel.Id, zipPath);

                return Result.Success<string>(zipPath);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting configurations for vessel {VesselId}", vessel.Id);
                return Result.Failure<string>($"Error exporting configurations: {ex.Message}");
            }
            finally
            {
                // Clean up the temporary directory
                if (!string.IsNullOrEmpty(tempDir))
                {
                    try
                    {
                        await _outputService.CleanupAsync(tempDir);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Error cleaning up temporary directory {TempDir}", tempDir);
                    }
                }
            }
        }
    }
}
