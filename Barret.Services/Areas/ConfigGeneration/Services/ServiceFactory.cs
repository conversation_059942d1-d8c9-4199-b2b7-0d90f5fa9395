using System;
using Microsoft.Extensions.DependencyInjection;
using Barret.Services.Areas.ConfigGeneration.Interfaces;

namespace Barret.Services.Areas.ConfigGeneration.Services
{
    /// <summary>
    /// Implementation of the service factory for creating scoped services
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the ServiceFactory class
    /// </remarks>
    /// <param name="serviceProvider">The service provider</param>
    public class ServiceFactory(IServiceProvider serviceProvider) : IServiceFactory
    {
        private readonly IServiceProvider _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

        /// <summary>
        /// Creates a scoped instance of the specified service type
        /// </summary>
        /// <typeparam name="T">The service type to create</typeparam>
        /// <returns>A scoped instance of the service</returns>
        public T CreateScope<T>() where T : class
        {
            var scope = _serviceProvider.CreateScope();
            return scope.ServiceProvider.GetRequiredService<T>();
        }
        
        /// <summary>
        /// Creates a scope and returns the service provider for that scope
        /// </summary>
        /// <returns>A service scope</returns>
        public IServiceScope CreateServiceScope()
        {
            return _serviceProvider.CreateScope();
        }
    }
}
