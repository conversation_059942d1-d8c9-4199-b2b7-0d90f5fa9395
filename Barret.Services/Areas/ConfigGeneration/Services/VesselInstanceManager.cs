using System;
using System.Collections.Concurrent;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Interfaces;

namespace Barret.Services.Areas.ConfigGeneration.Services
{
    /// <summary>
    /// Manages vessel instances to ensure single loading and consistent data throughout configuration generation
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the VesselInstanceManager class
    /// </remarks>
    /// <param name="vehicleDataProvider">The vehicle data provider</param>
    /// <param name="logger">The logger</param>
    public class VesselInstanceManager(
        IVehicleDataProvider vehicleDataProvider,
        ILogger<VesselInstanceManager> logger) : IVesselInstanceManager
    {
        private readonly IVehicleDataProvider _vehicleDataProvider = vehicleDataProvider ?? throw new ArgumentNullException(nameof(vehicleDataProvider));
        private readonly ILogger<VesselInstanceManager> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        // Thread-safe cache for vessel instances
        private readonly ConcurrentDictionary<Guid, Vessel> _vesselCache = new();
        
        /// <summary>
        /// Gets a vessel instance, loading it once and caching it for subsequent requests
        /// </summary>
        /// <param name="vesselId">The ID of the vessel</param>
        /// <returns>The vessel instance</returns>
        public async Task<Vessel> GetVesselInstanceAsync(Guid vesselId)
        {
            _logger.LogInformation("VESSELINSTANCEMANAGER: Requesting vessel instance for {VesselId}", vesselId);

            // Check if vessel is already cached
            if (_vesselCache.TryGetValue(vesselId, out var cachedVessel))
            {
                _logger.LogInformation("VESSELINSTANCEMANAGER: Returning cached vessel {VesselId} with {DeviceCount} devices",
                    vesselId, cachedVessel.GetAllDevices().Count());
                return cachedVessel;
            }

            _logger.LogInformation("VESSELINSTANCEMANAGER: Vessel {VesselId} not in cache, loading from data provider", vesselId);

            // Load vessel from data provider
            var vessel = await _vehicleDataProvider.GetVesselWithAllDataAsync(vesselId);

            // Validate vessel before caching
            var deviceCount = vessel.GetAllDevices().Count();
            _logger.LogInformation("VESSELINSTANCEMANAGER: Loaded vessel {VesselId} with {DeviceCount} devices from data provider",
                vessel.Id, deviceCount);

            // Check for duplicate devices before caching
            var deviceList = vessel.GetAllDevices().ToList();
            var deviceIds = deviceList.Select(d => d.Id).ToList();
            var uniqueDeviceIds = deviceIds.Distinct().ToList();
            
            if (deviceIds.Count != uniqueDeviceIds.Count)
            {
                var duplicateCount = deviceIds.Count - uniqueDeviceIds.Count;
                _logger.LogError("VESSELINSTANCEMANAGER: CRITICAL ISSUE - Vessel {VesselId} has {DuplicateCount} duplicate devices before caching! Total: {TotalCount}, Unique: {UniqueCount}",
                    vessel.Id, duplicateCount, deviceIds.Count, uniqueDeviceIds.Count);
                
                // Log duplicate device IDs
                var duplicateIds = deviceIds.GroupBy(id => id).Where(g => g.Count() > 1).Select(g => g.Key).ToList();
                _logger.LogError("VESSELINSTANCEMANAGER: Duplicate device IDs: {DuplicateIds}", string.Join(", ", duplicateIds));
            }
            else
            {
                _logger.LogInformation("VESSELINSTANCEMANAGER: VALIDATION PASSED - No duplicate devices detected before caching");
            }

            // Cache the vessel instance
            _vesselCache.TryAdd(vesselId, vessel);
            _logger.LogInformation("VESSELINSTANCEMANAGER: Cached vessel {VesselId} for future requests", vesselId);

            return vessel;
        }

        /// <summary>
        /// Clears the vessel cache for a specific vessel
        /// </summary>
        /// <param name="vesselId">The ID of the vessel to remove from cache</param>
        public void ClearVesselCache(Guid vesselId)
        {
            if (_vesselCache.TryRemove(vesselId, out var removedVessel))
            {
                _logger.LogInformation("VESSELINSTANCEMANAGER: Removed vessel {VesselId} from cache", vesselId);
            }
            else
            {
                _logger.LogDebug("VESSELINSTANCEMANAGER: Vessel {VesselId} was not in cache", vesselId);
            }
        }

        /// <summary>
        /// Clears all vessel instances from the cache
        /// </summary>
        public void ClearAllCache()
        {
            var cacheCount = _vesselCache.Count;
            _vesselCache.Clear();
            _logger.LogInformation("VESSELINSTANCEMANAGER: Cleared all {CacheCount} vessel instances from cache", cacheCount);
        }

        /// <summary>
        /// Gets the current cache statistics
        /// </summary>
        /// <returns>Cache statistics</returns>
        public VesselCacheStatistics GetCacheStatistics()
        {
            return new VesselCacheStatistics
            {
                CachedVesselCount = _vesselCache.Count,
                CachedVesselIds = _vesselCache.Keys.ToList()
            };
        }
    }

    /// <summary>
    /// Statistics about the vessel cache
    /// </summary>
    public class VesselCacheStatistics
    {
        /// <summary>
        /// Number of vessels currently cached
        /// </summary>
        public int CachedVesselCount { get; set; }

        /// <summary>
        /// List of vessel IDs currently cached
        /// </summary>
        public List<Guid> CachedVesselIds { get; set; } = new();
    }
}
