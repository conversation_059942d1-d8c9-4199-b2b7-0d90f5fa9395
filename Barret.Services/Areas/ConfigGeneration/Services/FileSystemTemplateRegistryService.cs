using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.Results;

namespace Barret.Services.Areas.ConfigGeneration.Services
{
    /// <summary>
    /// Service for discovering configuration templates from the file system
    /// </summary>
    public class FileSystemTemplateRegistryService : ITemplateRegistryService
    {
        private readonly ILogger<FileSystemTemplateRegistryService> _logger;
        private readonly string _templateBasePath;
        private readonly SemaphoreSlim _lock = new SemaphoreSlim(1, 1);
        private IReadOnlyList<ConfigurationTemplate> _cachedTemplates;
        private bool _isInitialized = false;

        /// <summary>
        /// Initializes a new instance of the FileSystemTemplateRegistryService class
        /// </summary>
        /// <param name="logger">The logger</param>
        /// <param name="templateBasePath">The base path for configuration templates</param>
        public FileSystemTemplateRegistryService(
            ILogger<FileSystemTemplateRegistryService> logger,
            string templateBasePath)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            if (string.IsNullOrWhiteSpace(templateBasePath))
                throw new ArgumentException("Template base path cannot be empty", nameof(templateBasePath));

            _templateBasePath = templateBasePath;
        }

        /// <summary>
        /// Gets the template base path
        /// </summary>
        /// <returns>The template base path</returns>
        public string GetTemplateBasePath() => _templateBasePath;

        /// <summary>
        /// Gets all templates
        /// </summary>
        /// <returns>A result containing all templates</returns>
        public async Task<Result<IReadOnlyList<ConfigurationTemplate>>> GetAllTemplatesAsync()
        {
            try
            {
                await EnsureInitializedAsync();
                return Result.Success<IReadOnlyList<ConfigurationTemplate>>(_cachedTemplates);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all templates");
                return Result.Failure<IReadOnlyList<ConfigurationTemplate>>($"Error getting all templates: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets a template by its path
        /// </summary>
        /// <param name="path">The path to the template</param>
        /// <returns>A result containing the template</returns>
        public async Task<Result<ConfigurationTemplate>> GetTemplateByPathAsync(string path)
        {
            try
            {
                await EnsureInitializedAsync();

                var template = _cachedTemplates.FirstOrDefault(t => string.Equals(t.Path, path, StringComparison.OrdinalIgnoreCase));
                if (template == null)
                {
                    return Result.Failure<ConfigurationTemplate>($"Template not found: {path}");
                }

                return Result.Success(template);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting template by path: {Path}", path);
                return Result.Failure<ConfigurationTemplate>($"Error getting template: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets templates by handler type (legacy method, returns empty list)
        /// </summary>
        /// <param name="handlerType">The handler type</param>
        /// <returns>A result containing an empty list of templates</returns>
        /// <remarks>
        /// This method is kept for backward compatibility but returns an empty list
        /// since templates no longer have explicit handler types. Use the ConfigurationHandlerProvider
        /// to determine the appropriate handler for a template.
        /// </remarks>
        public Task<Result<IReadOnlyList<ConfigurationTemplate>>> GetTemplatesByHandlerTypeAsync(string handlerType)
        {
            // Simply return an empty list since we no longer associate templates with specific handlers
            _logger.LogDebug("GetTemplatesByHandlerTypeAsync called with handler type {HandlerType}, returning empty list", handlerType);
            return Task.FromResult(Result.Success<IReadOnlyList<ConfigurationTemplate>>(new List<ConfigurationTemplate>()));
        }

        /// <summary>
        /// Reloads the template registry
        /// </summary>
        /// <returns>A result indicating success or failure</returns>
        public async Task<Result<bool>> ReloadAsync()
        {
            try
            {
                await _lock.WaitAsync();
                try
                {
                    _cachedTemplates = null;
                    _isInitialized = false;
                    await DiscoverTemplatesAsync();
                    return Result.Success(true);
                }
                finally
                {
                    _lock.Release();
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error reloading template registry");
                return Result.Failure<bool>($"Error reloading template registry: {ex.Message}");
            }
        }

        /// <summary>
        /// Ensures the service is initialized
        /// </summary>
        private async Task EnsureInitializedAsync()
        {
            if (_isInitialized)
                return;

            await _lock.WaitAsync();
            try
            {
                if (_isInitialized)
                    return;

                await DiscoverTemplatesAsync();
                _isInitialized = true;
            }
            finally
            {
                _lock.Release();
            }
        }

        /// <summary>
        /// Discovers templates from the file system
        /// </summary>
        private Task DiscoverTemplatesAsync()
        {
            _logger.LogInformation("Discovering templates from directory: {TemplateBasePath}", _templateBasePath);

            if (!Directory.Exists(_templateBasePath))
            {
                _logger.LogWarning("Template directory not found: {TemplateBasePath}", _templateBasePath);
                _cachedTemplates = new List<ConfigurationTemplate>();
                return Task.CompletedTask;
            }

            try
            {
                var templates = new List<ConfigurationTemplate>();

                // Get all JSON files in the ConfigTemplates directory and subdirectories
                var jsonFiles = Directory.GetFiles(_templateBasePath, "*.json", SearchOption.AllDirectories)
                    .Where(file => !file.EndsWith("config-metadata.json", StringComparison.OrdinalIgnoreCase));

                foreach (var file in jsonFiles)
                {
                    // Get the relative path from the template base path
                    var relativePath = file.Substring(_templateBasePath.Length).TrimStart('\\', '/');

                    // Normalize path separators
                    relativePath = relativePath.Replace('\\', '/');

                    // Create a template entry
                    var template = new ConfigurationTemplate
                    {
                        Path = relativePath,
                        OutputPath = relativePath,
                        Description = $"Auto-discovered template: {relativePath}",
                        IsRequired = true
                    };

                    templates.Add(template);
                }

                _cachedTemplates = templates;
                _logger.LogInformation("Discovered {TemplateCount} templates", templates.Count);

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error discovering templates from directory: {TemplateBasePath}", _templateBasePath);
                throw;
            }
        }
    }
}
