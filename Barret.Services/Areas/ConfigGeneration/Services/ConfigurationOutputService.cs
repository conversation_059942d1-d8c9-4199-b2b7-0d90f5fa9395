using System;
using System.IO;
using System.IO.Compression;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.Results;

namespace Barret.Services.Areas.ConfigGeneration.Services
{
    /// <summary>
    /// Service for managing configuration output
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the ConfigurationOutputService class
    /// </remarks>
    /// <param name="logger">The logger</param>
    public class ConfigurationOutputService(ILogger<ConfigurationOutputService> logger) : IConfigurationOutputService
    {
        private readonly ILogger<ConfigurationOutputService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Creates a temporary directory for configuration output
        /// </summary>
        /// <param name="vessel">The vessel for which configurations are being generated</param>
        /// <returns>A result containing the path to the temporary directory</returns>
        public Task<Result<string>> CreateTemporaryDirectoryAsync(Vessel vessel)
        {
            try
            {
                _logger.LogInformation("Creating temporary directory for vessel {VesselId}", vessel.Id);

                // Create a unique directory name based on the vessel ID and a GUID
                var dirName = $"VesselConfigs_{vessel.VehicleId}_{Guid.NewGuid()}";
                var tempDir = Path.Combine(Path.GetTempPath(), dirName);

                // Create the directory
                Directory.CreateDirectory(tempDir);

                _logger.LogInformation("Created temporary directory: {TempDir}", tempDir);
                return Task.FromResult(Result.Success<string>(tempDir));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating temporary directory for vessel {VesselId}", vessel.Id);
                return Task.FromResult(Result.Failure<string>($"Error creating temporary directory: {ex.Message}"));
            }
        }

        /// <summary>
        /// Creates a ZIP file from a directory
        /// </summary>
        /// <param name="sourceDirectory">The source directory</param>
        /// <param name="vessel">The vessel for which configurations are being generated</param>
        /// <returns>A result containing the path to the ZIP file</returns>
        public Task<Result<string>> CreateZipFileAsync(string sourceDirectory, Vessel vessel)
        {
            try
            {
                _logger.LogInformation("Creating ZIP file for vessel {VesselId} from directory {SourceDirectory}",
                    vessel.Id, sourceDirectory);

                if (!Directory.Exists(sourceDirectory))
                {
                    _logger.LogError("Source directory not found: {SourceDirectory}", sourceDirectory);
                    return Task.FromResult(Result.Failure<string>($"Source directory not found: {sourceDirectory}"));
                }

                // Create a unique ZIP file name based on the vessel ID
                var zipFileName = $"VesselConfigs_{vessel.VehicleId}.zip";
                var zipPath = Path.Combine(Path.GetTempPath(), zipFileName);

                // Delete the ZIP file if it already exists
                if (File.Exists(zipPath))
                {
                    File.Delete(zipPath);
                }

                // Create the ZIP file
                ZipFile.CreateFromDirectory(sourceDirectory, zipPath);

                _logger.LogInformation("Created ZIP file: {ZipPath}", zipPath);
                return Task.FromResult(Result.Success<string>(zipPath));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating ZIP file for vessel {VesselId} from directory {SourceDirectory}",
                    vessel.Id, sourceDirectory);
                return Task.FromResult(Result.Failure<string>($"Error creating ZIP file: {ex.Message}"));
            }
        }

        /// <summary>
        /// Cleans up temporary files
        /// </summary>
        /// <param name="path">The path to clean up</param>
        /// <returns>A result indicating success or failure</returns>
        public Task<Result<bool>> CleanupAsync(string path)
        {
            try
            {
                _logger.LogInformation("Cleaning up path: {Path}", path);

                if (string.IsNullOrEmpty(path))
                {
                    _logger.LogWarning("Path is null or empty, nothing to clean up");
                    return Task.FromResult(Result.Success(true));
                }

                // Check if the path is a file or directory
                if (File.Exists(path))
                {
                    // Delete the file
                    File.Delete(path);
                    _logger.LogInformation("Deleted file: {Path}", path);
                }
                else if (Directory.Exists(path))
                {
                    // Delete the directory and all its contents
                    Directory.Delete(path, true);
                    _logger.LogInformation("Deleted directory: {Path}", path);
                }
                else
                {
                    _logger.LogWarning("Path not found, nothing to clean up: {Path}", path);
                }

                return Task.FromResult(Result.Success(true));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error cleaning up path: {Path}", path);
                return Task.FromResult(Result.Failure<bool>($"Error cleaning up: {ex.Message}"));
            }
        }
    }
}
