using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.Results;

namespace Barret.Services.Areas.ConfigGeneration.Services
{
    /// <summary>
    /// Service for generating configuration files for vessels
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the ConfigurationGenerator class
    /// </remarks>
    /// <param name="logger">The logger</param>
    /// <param name="templateRegistry">The template registry service</param>
    /// <param name="handlerRegistry">The handler registry</param>
    public class ConfigurationGenerator(
        ILogger<ConfigurationGenerator> logger,
        ITemplateRegistryService templateRegistry,
        IHandlerRegistry handlerRegistry) : IConfigurationGenerator
    {
        private readonly ILogger<ConfigurationGenerator> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly ITemplateRegistryService _templateRegistry = templateRegistry ?? throw new ArgumentNullException(nameof(templateRegistry));
        private readonly IHandlerRegistry _handlerRegistry = handlerRegistry ?? throw new ArgumentNullException(nameof(handlerRegistry));

        /// <summary>
        /// Generates all configurations for a vessel and writes them to the specified base directory
        /// </summary>
        /// <param name="vessel">The vessel to generate configurations for</param>
        /// <param name="baseDirectory">The base directory to write the configurations to</param>
        /// <param name="progress">The progress reporter</param>
        /// <param name="cancellationToken">The cancellation token</param>
        /// <returns>A result containing the list of configuration outputs</returns>
        public async Task<Result<IReadOnlyList<ConfigurationOutput>>> GenerateConfigurationsAsync(
            Vessel vessel,
            string baseDirectory,
            IProgress<int>? progress = null,
            CancellationToken cancellationToken = default)
        {
            if (vessel == null)
            {
                return Result.Failure<IReadOnlyList<ConfigurationOutput>>("Vessel cannot be null");
            }

            if (string.IsNullOrEmpty(baseDirectory))
            {
                return Result.Failure<IReadOnlyList<ConfigurationOutput>>("Base directory cannot be null or empty");
            }

            try
            {
                _logger.LogInformation("Generating configurations for vessel {VesselId} to directory {BaseDirectory}",
                    vessel.Id, baseDirectory);

                // Create the base output directory if it doesn't exist
                if (!Directory.Exists(baseDirectory))
                {
                    Directory.CreateDirectory(baseDirectory);
                }

                // Load the configuration templates
                var templatesResult = await _templateRegistry.GetAllTemplatesAsync();
                if (!templatesResult.Success)
                {
                    return Result.Failure<IReadOnlyList<ConfigurationOutput>>(
                        $"Failed to load configuration templates: {templatesResult.ErrorMessage}");
                }

                var templates = templatesResult.Data;
                if (templates.Count == 0)
                {
                    _logger.LogWarning("No configuration templates found");
                    return Result.Success<IReadOnlyList<ConfigurationOutput>>(new List<ConfigurationOutput>());
                }

                // Create a cancellation token source that can be cancelled from the provided token
                using var cts = CancellationTokenSource.CreateLinkedTokenSource(cancellationToken);

                // Create the configuration context
                var context = new ConfigurationContext(
                    vessel,
                    _templateRegistry.GetTemplateBasePath(),
                    baseDirectory,
                    cts);

                // Process each template
                var outputs = new List<ConfigurationOutput>();
                var totalTemplates = templates.Count;
                var processedTemplates = 0;

                foreach (var template in templates)
                {
                    // Check for cancellation
                    if (cts.Token.IsCancellationRequested)
                    {
                        _logger.LogWarning("Configuration generation cancelled for vessel {VesselId}", vessel.Id);
                        break;
                    }

                    // Get a handler for this template
                    var handler = _handlerRegistry.GetHandler(template.Path);
                    if (handler == null)
                    {
                        _logger.LogWarning("No handler found for template {TemplatePath}", template.Path);

                        var failureOutput = ConfigurationOutput.Failure(template, "No handler found for template");
                        outputs.Add(failureOutput);

                        processedTemplates++;
                        progress?.Report((int)((float)processedTemplates / totalTemplates * 100));
                        continue;
                    }

                    _logger.LogInformation("Processing template {TemplatePath} with handler {HandlerType}",
                        template.Path, handler.HandlerType);

                    // Generate the configuration
                    var output = await handler.GenerateConfigurationAsync(context, template);
                    outputs.Add(output);

                    if (output.Success)
                    {
                        _logger.LogInformation("Generated configuration at {OutputPath}", output.OutputPath);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to generate configuration for vessel {VesselId}: {ErrorMessage}",
                            vessel.Id, output.ErrorMessage);
                    }

                    // Update progress
                    processedTemplates++;
                    progress?.Report((int)((float)processedTemplates / totalTemplates * 100));
                }

                var successCount = outputs.Count(o => o.Success);
                var failureCount = outputs.Count(o => !o.Success);

                _logger.LogInformation("Configuration generation completed. Success: {SuccessCount}, Failure: {FailureCount}",
                    successCount, failureCount);

                return Result.Success<IReadOnlyList<ConfigurationOutput>>(outputs);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating configurations for vessel {VesselId} to directory {BaseDirectory}",
                    vessel.Id, baseDirectory);
                return Result.Failure<IReadOnlyList<ConfigurationOutput>>($"Error generating configurations: {ex.Message}");
            }
        }
    }
}
