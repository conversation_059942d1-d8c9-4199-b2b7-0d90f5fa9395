using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Core.Areas.Vehicles.Repositories;

namespace Barret.Services.Areas.ConfigGeneration.Services
{
    /// <summary>
    /// Provider for vehicle data with all necessary relationships for configuration generation
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the VehicleDataProvider class
    /// </remarks>
    /// <param name="vesselRepository">The vessel repository</param>
    /// <param name="logger">The logger</param>
    public class VehicleDataProvider(
        IVesselRepository vesselRepository,
        ILogger<VehicleDataProvider> logger) : IVehicleDataProvider
    {
        private readonly IVesselRepository _vesselRepository = vesselRepository ?? throw new ArgumentNullException(nameof(vesselRepository));
        private readonly ILogger<VehicleDataProvider> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Gets a vessel with all data needed for configuration generation
        /// </summary>
        /// <param name="vesselId">The ID of the vessel</param>
        /// <returns>The vessel with all necessary data</returns>
        public async Task<Vessel> GetVesselWithAllDataAsync(Guid vesselId)
        {
            _logger.LogInformation("VEHICLEDATAPROVIDER: Loading vessel {VesselId} with all data for configuration generation", vesselId);

            // Get the vessel with devices and connections
            var vessel = await _vesselRepository.GetWithDeviceTreeAsync(vesselId)
                ?? throw new ArgumentException($"Vessel with ID {vesselId} not found", nameof(vesselId));

            // Get device count immediately after loading to detect any issues
            var deviceCount = vessel.GetAllDevices().Count();
            _logger.LogInformation("VEHICLEDATAPROVIDER: Loaded vessel {VesselId} with {DeviceCount} devices",
                vessel.Id, deviceCount);

            // Log each device with detailed information to track duplicates
            var deviceList = vessel.GetAllDevices().ToList();
            var deviceIds = new HashSet<Guid>();
            var duplicateDeviceIds = new List<Guid>();

            foreach (var device in deviceList)
            {
                // Check for duplicate device IDs
                if (deviceIds.Contains(device.Id))
                {
                    duplicateDeviceIds.Add(device.Id);
                    _logger.LogError("VEHICLEDATAPROVIDER: DUPLICATE DEVICE DETECTED! Device {DeviceId} ({DeviceName}) appears multiple times in vessel {VesselId}",
                        device.Id, device.Name, vessel.Id);
                }
                else
                {
                    deviceIds.Add(device.Id);
                }

                _logger.LogInformation("VEHICLEDATAPROVIDER: Device {DeviceId} ({DeviceName}) - Role: {DeviceRole}, HasModel: {HasModel}, HasConnection: {HasConnection}",
                    device.Id, device.Name, device.DeviceRole, device.Model != null, device.HasConnection());

                // Log detailed information for cameras
                if (device.DeviceRole == DeviceRole.Camera && device is Camera camera)
                {
                    _logger.LogInformation("VEHICLEDATAPROVIDER: Camera {CameraId} details - ShowVideo: {ShowVideo}", camera.Id, camera.ShowVideo);

                    if (camera.Model != null)
                    {
                        _logger.LogInformation("VEHICLEDATAPROVIDER: Camera {CameraId} model - Name: {ModelName}, Manufacturer: {Manufacturer}",
                            camera.Id, camera.Model.Name, camera.Model.Manufacturer?.Name ?? "NULL");
                    }

                    if (camera.HasConnection() && camera.Connection != null)
                    {
                        _logger.LogInformation("VEHICLEDATAPROVIDER: Camera {CameraId} connection - IP: {IP}, Port: {Port}",
                            camera.Id, camera.Connection.IPAddress, camera.Connection.Port);
                    }
                }
            }

            // Report duplicate detection results
            if (duplicateDeviceIds.Count > 0)
            {
                _logger.LogError("VEHICLEDATAPROVIDER: CRITICAL ISSUE - Found {DuplicateCount} duplicate devices in vessel {VesselId}: {DuplicateDeviceIds}",
                    duplicateDeviceIds.Count, vessel.Id, string.Join(", ", duplicateDeviceIds));
            }
            else
            {
                _logger.LogInformation("VEHICLEDATAPROVIDER: VALIDATION PASSED - No duplicate devices detected in vessel {VesselId}", vessel.Id);
            }

            // Validate device count consistency
            var uniqueDeviceCount = deviceIds.Count;
            if (deviceCount != uniqueDeviceCount)
            {
                _logger.LogError("VEHICLEDATAPROVIDER: DEVICE COUNT MISMATCH - Total devices: {TotalCount}, Unique devices: {UniqueCount}, Duplicates: {DuplicateCount}",
                    deviceCount, uniqueDeviceCount, deviceCount - uniqueDeviceCount);
            }
            else
            {
                _logger.LogInformation("VEHICLEDATAPROVIDER: DEVICE COUNT VALIDATION PASSED - {DeviceCount} unique devices loaded", deviceCount);
            }

            return vessel;
        }
    }
}
