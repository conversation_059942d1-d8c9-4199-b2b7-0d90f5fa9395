using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using Microsoft.Extensions.Logging;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Enums;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Utilities;

namespace Barret.Services.Areas.ConfigGeneration.Services
{
    /// <summary>
    /// Provider for configuration handlers that determines the appropriate handler based on file path patterns
    /// </summary>
    /// <remarks>
    /// This provider uses the following strategy to find a handler:
    /// 1. Try to find a handler based on system type and config type
    /// 2. Try to find a handler based on path
    /// 3. Try to find a generic handler based on config type
    /// 4. Fall back to StaticConfigHandler if no specific handler is found
    /// </remarks>
    /// <remarks>
    /// Initializes a new instance of the ConfigurationHandlerProvider class
    /// </remarks>
    /// <param name="handlers">The available configuration handlers</param>
    /// <param name="logger">The logger</param>
    public class ConfigurationHandlerProvider(
        IEnumerable<IConfigurationHandler> handlers,
        ILogger<ConfigurationHandlerProvider> logger) : IConfigurationHandlerProvider
    {
        private readonly IEnumerable<IConfigurationHandler> _handlers = handlers ?? throw new ArgumentNullException(nameof(handlers));
        private readonly ILogger<ConfigurationHandlerProvider> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Gets a handler for the specified template
        /// </summary>
        /// <param name="template">The template to get a handler for</param>
        /// <returns>The handler, or null if no handler is available</returns>
        public IConfigurationHandler GetHandlerForTemplate(ConfigurationTemplate template)
        {
            if (template == null)
                throw new ArgumentNullException(nameof(template));

            // Extract the system type and config type from the path
            SystemType systemType = TemplatePathParser.ExtractSystemType(template.Path);
            string configType = TemplatePathParser.ExtractConfigType(template.Path);

            // Try to find a handler based on system type and config type
            if (systemType != SystemType.Unknown && !string.IsNullOrEmpty(configType))
            {
                var systemHandler = _handlers.FirstOrDefault(h =>
                    h.SupportedSystemType == systemType &&
                    string.Equals(h.SupportedConfigType, configType, StringComparison.OrdinalIgnoreCase));

                if (systemHandler != null)
                {
                    _logger.LogDebug("Found system-specific handler {HandlerType} for template {TemplatePath}",
                        systemHandler.HandlerType, template.Path);
                    return systemHandler;
                }
            }

            // Try to find a handler based on path
            var pathHandler = _handlers.FirstOrDefault(h => h.CanHandlePath(template.Path));
            if (pathHandler != null)
            {
                _logger.LogDebug("Found path-based handler {HandlerType} for template {TemplatePath}",
                    pathHandler.HandlerType, template.Path);
                return pathHandler;
            }

            // Try to find a generic handler based on config type
            if (!string.IsNullOrEmpty(configType))
            {
                var genericHandler = _handlers.FirstOrDefault(h =>
                    h.SupportedSystemType == SystemType.Unknown &&
                    string.Equals(h.SupportedConfigType, configType, StringComparison.OrdinalIgnoreCase));

                if (genericHandler != null)
                {
                    _logger.LogDebug("Found generic handler {HandlerType} for template {TemplatePath}",
                        genericHandler.HandlerType, template.Path);
                    return genericHandler;
                }
            }

            // Try to find a specific handler based on filename
            var fileName = Path.GetFileNameWithoutExtension(template.Path);
            var handler = _handlers.FirstOrDefault(h =>
                string.Equals(h.HandlerType, fileName, StringComparison.OrdinalIgnoreCase));

            if (handler != null)
            {
                _logger.LogDebug("Found specific handler {HandlerType} for template {TemplatePath}",
                    handler.HandlerType, template.Path);
                return handler;
            }

            // Try to find a handler based on directory name
            var directoryName = Path.GetDirectoryName(template.Path)?.Split('/', '\\').LastOrDefault();
            if (!string.IsNullOrEmpty(directoryName))
            {
                // If directory is "XXXConfigs", try "XXXConfig" as handler
                if (directoryName.EndsWith("Configs", StringComparison.OrdinalIgnoreCase))
                {
                    var dirConfigType = directoryName.Substring(0, directoryName.Length - "s".Length);
                    handler = _handlers.FirstOrDefault(h =>
                        string.Equals(h.HandlerType, dirConfigType, StringComparison.OrdinalIgnoreCase));

                    if (handler != null)
                    {
                        _logger.LogDebug("Found directory-based handler {HandlerType} for template {TemplatePath}",
                            handler.HandlerType, template.Path);
                        return handler;
                    }
                }
            }

            // Fall back to StaticConfigHandler
            var staticHandler = _handlers.FirstOrDefault(h => h.HandlerType == "StaticConfig");
            if (staticHandler != null)
            {
                _logger.LogInformation("Using StaticConfigHandler as fallback for template {TemplatePath}",
                    template.Path);
                return staticHandler;
            }

            _logger.LogWarning("No handler found for template {TemplatePath} and no StaticConfigHandler available",
                template.Path);
            return null;
        }
    }
}
