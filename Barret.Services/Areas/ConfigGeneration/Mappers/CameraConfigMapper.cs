using System;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Utilities;
using Barret.Shared.DTOs.DaVinciConfig;

namespace Barret.Services.Areas.ConfigGeneration.Mappers
{
    /// <summary>
    /// Implementation of ICameraConfigMapper for mapping Camera domain entities to DaVinci camera configuration DTOs
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the CameraConfigMapper class
    /// </remarks>
    /// <param name="logger">Logger for the mapper</param>
    public class CameraConfigMapper(ILogger<CameraConfigMapper> logger) : ICameraConfigMapper
    {
        private readonly ILogger<CameraConfigMapper> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Maps a Camera domain entity to a configuration DTO (implementation of IConfigMapper)
        /// </summary>
        /// <param name="entity">The camera domain entity to map</param>
        /// <returns>A camera configuration DTO</returns>
        public CameraConfigDto MapToConfig(Camera entity)
        {
            return MapToCameraConfig(entity);
        }

        /// <summary>
        /// Maps a Camera domain entity to a DaVinci camera configuration DTO
        /// </summary>
        /// <param name="camera">The camera domain entity to map</param>
        /// <returns>A DaVinci camera configuration DTO</returns>
        public CameraConfigDto MapToCameraConfig(Camera camera)
        {
            if (camera == null)
            {
                throw new ArgumentNullException(nameof(camera));
            }

            _logger.LogInformation("[CameraConfigMapper] Starting mapping for camera {CameraId} (Name: {Name})", camera.Id, camera.Name);

            // Log detailed camera information for debugging
            _logger.LogInformation("[CameraConfigMapper] Camera details - ShowVideo: {ShowVideo}, HasConnection: {HasConnection}, HasModel: {HasModel}",
                camera.ShowVideo, camera.HasConnection(), camera.Model != null);

            if (camera.Connection != null)
            {
                _logger.LogInformation("[CameraConfigMapper] Camera connection - IP: {IP}, Port: {Port}, Protocol: {Protocol}",
                    camera.Connection.IPAddress, camera.Connection.Port, camera.Connection.Protocol);
            }
            else
            {
                _logger.LogWarning("[CameraConfigMapper] Camera {CameraId} has no connection!", camera.Id);
            }

            if (camera.Model != null)
            {
                _logger.LogInformation("[CameraConfigMapper] Camera model - Name: {ModelName}, Manufacturer: {Manufacturer}",
                    camera.Model.Name, camera.Model.Manufacturer?.Name ?? "NULL");
            }
            else
            {
                _logger.LogWarning("[CameraConfigMapper] Camera {CameraId} has no model!", camera.Id);
            }

            // Create default credentials based on manufacturer
            var credentials = GetDefaultCredentials(camera.Model);

            // Format the camera name according to DaVinci standards
            string formattedName = CameraNameFormatter.FormatCameraName(camera.Name);

            // Format the IP address (remove subnet mask if present)
            string formattedIp = CameraNameFormatter.FormatIpAddress(camera.Connection?.IPAddress ?? "*************");

            // Get the brand
            string brand = MapCameraBrand(camera.Model);

            // Log the ShowVideo value before mapping
            _logger.LogInformation("[CameraConfigMapper] Camera {CameraId} ShowVideo value: {ShowVideo}", camera.Id, camera.ShowVideo);

            // Create the camera config DTO with explicit ShowVideo value
            bool showVideoValue = camera.ShowVideo;
            _logger.LogInformation("[CameraConfigMapper] Using explicit ShowVideo value: {ShowVideo}", showVideoValue);

            // Create a new CameraConfigDto with the explicit ShowVideo value
            var config = new CameraConfigDto(
                name: formattedName,
                ip: formattedIp,
                brand: brand,
                technicalComponentId: "", // Empty string as requested
                showVideo: showVideoValue, // Use the explicit ShowVideo value
                credentials: credentials // Use the credentials from GetDefaultCredentials
            );

            // Explicitly set the ShowVideo property again to ensure it's set correctly
            config.ShowVideo = showVideoValue;

            // Log the final mapped values
            _logger.LogInformation("[CameraConfigMapper] Final mapped values - Name: {Name}, IP: {IP}, Brand: {Brand}, ShowVideo: {ShowVideo}",
                config.Name, config.Ip, config.Brand, config.ShowVideo);

            // Double-check the ShowVideo property
            if (config.ShowVideo != showVideoValue)
            {
                _logger.LogWarning("ShowVideo property mismatch! Expected: {Expected}, Actual: {Actual}",
                    showVideoValue, config.ShowVideo);
                // Force the correct value
                config.ShowVideo = showVideoValue;
            }

            return config;
        }

        /// <summary>
        /// Maps a camera model to a DaVinci camera brand string
        /// </summary>
        /// <param name="model">The camera model</param>
        /// <returns>The corresponding DaVinci camera brand as a string</returns>
        private string MapCameraBrand(DeviceModel? model)
        {
            if (model == null || model.Manufacturer == null)
            {
                return "Undefined";
            }

            // Map manufacturer name to camera brand string
            return model.Manufacturer.Name.ToLowerInvariant() switch
            {
                "axis" => "Axis",
                "hikvision" => "Hikvision",
                "dacom" => "Dacom",
                _ => "Undefined"
            };
        }

        /// <summary>
        /// Gets default credentials for a camera based on its model
        /// </summary>
        /// <param name="model">The camera model</param>
        /// <returns>Default credentials for the camera</returns>
        private CameraApiCredentialsDto GetDefaultCredentials(DeviceModel? model)
        {
            if (model == null || model.Manufacturer == null)
            {
                return new CameraApiCredentialsDto("admin", CameraPasswordConstants.DefaultEncryptedPassword);
            }

            // Get the brand
            string brand = MapCameraBrand(model);

            // Get the username based on brand
            string username = CameraNameFormatter.GetDefaultUsername(brand);

            // Set default credentials based on manufacturer with pre-encrypted passwords
            return brand.ToLowerInvariant() switch
            {
                "axis" => new CameraApiCredentialsDto(username, CameraPasswordConstants.AxisEncryptedPassword),
                "hikvision" => new CameraApiCredentialsDto(username, CameraPasswordConstants.DefaultEncryptedPassword), // Using default for now
                "dacom" => new CameraApiCredentialsDto(username, CameraPasswordConstants.DacomEncryptedPassword),
                _ => new CameraApiCredentialsDto(username, CameraPasswordConstants.DefaultEncryptedPassword)
            };
        }


    }
}
