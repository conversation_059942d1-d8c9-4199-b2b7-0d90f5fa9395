using System;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Utilities;
using Barret.Shared.DTOs.DaVinciConfig;

namespace Barret.Services.Areas.ConfigGeneration.Mappers
{
    /// <summary>
    /// Implementation of IConnectionHandlerConfigMapper for mapping device domain entities to DaVinci connection handler configuration DTOs
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the ConnectionHandlerConfigMapper class
    /// </remarks>
    /// <param name="logger">Logger for the mapper</param>
    public class ConnectionHandlerConfigMapper(ILogger<ConnectionHandlerConfigMapper> logger) : IConnectionHandlerConfigMapper
    {
        private readonly ILogger<ConnectionHandlerConfigMapper> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Maps a device domain entity to a connection handler configuration DTO
        /// </summary>
        /// <param name="device">The device domain entity to map</param>
        /// <returns>A connection handler configuration DTO</returns>
        public ConnectionHandlerConfigDto MapToConfig(GenericDevice device)
        {
            if (device == null)
            {
                throw new ArgumentNullException(nameof(device));
            }

            _logger.LogDebug("Mapping device {DeviceId} to connection handler config", device.Id);

            // Check if the device has a connection
            if (device.Connection == null)
            {
                _logger.LogWarning("Device {DeviceId} has no connection handler. Using default values.", device.Id);

                // Return a configuration with default values
                return new ConnectionHandlerConfigDto
                {
                    Protocol = "tcpclient",
                    ConnectionAddress = "127.0.0.1",
                    ConnectionAddressOption = 9100
                };
            }

            // Map device protocol to DaVinci protocol
            string protocol = device.Connection.Protocol switch
            {
                Protocol.TcpClient => "tcpclient",
                Protocol.RtspClient => "rtspclient",
                _ => "tcpclient"
            };

            // Format the IP address (remove subnet mask if present)
            string formattedIp = CameraNameFormatter.FormatIpAddress(device.Connection.IPAddress);

            // Get the port from the device connection
            int port = device.Connection.Port;

            return new ConnectionHandlerConfigDto
            {
                Protocol = protocol,
                ConnectionAddress = formattedIp,
                ConnectionAddressOption = port
            };
        }
    }
}
