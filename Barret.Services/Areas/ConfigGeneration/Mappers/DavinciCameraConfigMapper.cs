using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Models.Configs.Davinci;

namespace Barret.Services.Areas.ConfigGeneration.Mappers
{
    /// <summary>
    /// Implementation of IDavinciCameraConfigMapper
    /// </summary>
    public class DavinciCameraConfigMapper(ILogger<DavinciCameraConfigMapper> logger) : IDavinciCameraConfigMapper
    {
        private readonly ILogger<DavinciCameraConfigMapper> _logger = logger;

        /// <summary>
        /// Maps a Camera entity to a Davinci camera configuration DTO
        /// </summary>
        /// <param name="camera">The Camera entity to map</param>
        /// <returns>A Davinci camera configuration DTO</returns>
        public DavinciCameraConfigDto MapToDavinciCameraConfig(Camera camera)
        {
            ArgumentNullException.ThrowIfNull(camera);

            _logger.LogDebug("Mapping camera {CameraId} to Davinci camera config", camera.Id);

            var config = new DavinciCameraConfigDto
            {
                Name = camera.Name,
                IpAddress = camera.Connection?.IPAddress ?? string.Empty,
                Brand = camera.Model?.Manufacturer?.Name ?? string.Empty,
                TechnicalComponentId = string.Empty, // As per requirements
                ShowVideo = camera.ShowVideo,
                Credentials = new CredentialsDto
                {
                    Username = "admin", // Default username
                    Password = "admin"  // Default password
                }
            };

            _logger.LogDebug("Mapped camera {CameraId} to Davinci camera config with name {Name}, IP {IP}, ShowVideo {ShowVideo}",
                camera.Id, config.Name, config.IpAddress, config.ShowVideo);

            return config;
        }
    }
}
