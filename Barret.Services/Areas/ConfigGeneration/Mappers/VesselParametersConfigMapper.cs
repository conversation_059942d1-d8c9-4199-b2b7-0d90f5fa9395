using System;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.DTOs.DaVinciConfig;

namespace Barret.Services.Areas.ConfigGeneration.Mappers
{
    /// <summary>
    /// Implementation of IVesselParametersConfigMapper for mapping vessel domain entities to DaVinci vessel parameters configuration DTOs
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the VesselParametersConfigMapper class
    /// </remarks>
    /// <param name="logger">Logger for the mapper</param>
    public class VesselParametersConfigMapper(ILogger<VesselParametersConfigMapper> logger) : IVesselParametersConfigMapper
    {
        private readonly ILogger<VesselParametersConfigMapper> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Maps a vessel domain entity to a vessel parameters configuration DTO
        /// </summary>
        /// <param name="vessel">The vessel domain entity to map</param>
        /// <returns>A vessel parameters configuration DTO</returns>
        public VesselParametersConfigDto MapToConfig(Vessel vessel)
        {
            if (vessel == null)
            {
                throw new ArgumentNullException(nameof(vessel));
            }

            _logger.LogDebug("Mapping vessel {VesselId} to vessel parameters config", vessel.Id);

            return new VesselParametersConfigDto
            {
                VesselMmsi = vessel.MMSI,
                VesselName = vessel.Name,
                SeqApiKey = string.Empty // Always leave empty as per requirements
            };
        }
    }
}
