using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Radars;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Utilities;
using Barret.Shared.DTOs.DaVinciConfig;
using Barret.Shared.DTOs.DaVinciConfig.DriverConfig;

namespace Barret.Services.Areas.ConfigGeneration.Mappers
{
    /// <summary>
    /// Implementation of IDriverConfigMapper for mapping device domain entities to DaVinci driver configuration DTOs
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the DriverConfigMapper class
    /// </remarks>
    /// <param name="logger">Logger for the mapper</param>
    public class DriverConfigMapper(
        ILogger<DriverConfigMapper> logger) : IDriverConfigMapper
    {
        private readonly ILogger<DriverConfigMapper> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Maps a device domain entity to a driver configuration DTO
        /// </summary>
        /// <param name="device">The device domain entity to map</param>
        /// <returns>A driver configuration DTO</returns>
        public Barret.Shared.DTOs.DaVinciConfig.DriverConfigDto MapToConfig(GenericDevice device)
        {
            if (device == null)
            {
                throw new ArgumentNullException(nameof(device));
            }

            _logger.LogDebug("Mapping device {DeviceId} to driver config", device.Id);

            // Generate a unique technical component ID and driver ID
            var techComponentId = GetTechnicalComponentId(device);
            var driverId = Guid.NewGuid().ToString();

            return new Barret.Shared.DTOs.DaVinciConfig.DriverConfigDto
            {
                DriverIdentification = new Barret.Shared.DTOs.DaVinciConfig.DriverConfig.DriverIdentificationDto
                {
                    Name = device.Name,
                    TechnicalComponentId = techComponentId,
                    DriverId = driverId
                },
                RebootIntervalOnFailMs = 1000,
                HeartbeatIntervalMs = 5000
            };
        }

        /// <summary>
        /// Maps a device to a connection handler configuration DTO
        /// </summary>
        /// <param name="device">The device to map</param>
        /// <returns>A connection handler configuration DTO</returns>
        public Barret.Shared.DTOs.DaVinciConfig.DriverConfig.ConnectionHandlerConfigDto MapToConnectionHandlerConfig(GenericDevice device)
        {
            if (device == null)
            {
                throw new ArgumentNullException(nameof(device));
            }

            _logger.LogDebug("Mapping device {DeviceId} to connection handler config", device.Id);

            // Check if the device has a connection
            if (device.Connection == null)
            {
                _logger.LogWarning("Device {DeviceId} has no connection handler. Using default values.", device.Id);

                // Return a configuration with default values
                return new Barret.Shared.DTOs.DaVinciConfig.DriverConfig.ConnectionHandlerConfigDto
                {
                    Protocol = "tcpclient",
                    ConnectionAddress = "127.0.0.1",
                    ConnectionAddressOption = 9100
                };
            }

            // Map device protocol to DaVinci protocol
            string protocol = device.Connection.Protocol switch
            {
                Protocol.TcpClient => "tcpclient",
                Protocol.RtspClient => "rtspclient",
                _ => "tcpclient"
            };

            // Format the IP address (remove subnet mask if present)
            string formattedIp = CameraNameFormatter.FormatIpAddress(device.Connection.IPAddress);

            // Get the port from the device connection
            int port = device.Connection.Port;

            return new Barret.Shared.DTOs.DaVinciConfig.DriverConfig.ConnectionHandlerConfigDto
            {
                Protocol = protocol,
                ConnectionAddress = formattedIp,
                ConnectionAddressOption = port
            };
        }

        /// <summary>
        /// Maps a device to a full driver configuration DTO
        /// </summary>
        /// <param name="device">The device to map</param>
        /// <param name="pipelines">The pipelines to include in the configuration</param>
        /// <returns>A full driver configuration DTO</returns>
        public FullDriverConfigDto MapToFullDriverConfig(GenericDevice device, string[] pipelines = null)
        {
            if (device == null)
            {
                throw new ArgumentNullException(nameof(device));
            }

            _logger.LogDebug("Mapping device {DeviceId} to full driver config", device.Id);

            // Map the device to a driver config
            var driverConfig = MapToConfig(device);

            // Map the device to a connection handler config
            var connectionHandlerConfig = MapToConnectionHandlerConfig(device);

            // Use default pipelines if none provided
            var devicePipelines = pipelines ?? GetDefaultPipelines(device.DeviceRole);

            return new FullDriverConfigDto
            {
                DriverConfig = driverConfig,
                ConnectionHandlerConfig = connectionHandlerConfig,
                Pipelines = devicePipelines.ToList()
            };
        }

        /// <summary>
        /// Maps a vessel to a PingSweeper configuration DTO
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <param name="processedDeviceIds">Optional set of device IDs that have already been processed</param>
        /// <returns>A PingSweeper configuration DTO</returns>
        public PingSweeperConfigDto MapToPingSweeperConfig(Vessel vessel, HashSet<Guid> processedDeviceIds = null)
        {
            if (vessel == null)
            {
                throw new ArgumentNullException(nameof(vessel));
            }

            // Initialize the processed device IDs if not provided
            processedDeviceIds ??= new HashSet<Guid>();

            _logger.LogDebug("PINGSWEEPER: Mapping vessel {VesselId} to PingSweeper config", vessel.Id);

            // Create a PingSweeper config
            var pingSweeperConfig = new PingSweeperConfigDto
            {
                DriverIdentification = new DriverIdentificationDto
                {
                    Name = "PingSweeper",
                    TechnicalComponentId = "",
                    DriverId = Guid.NewGuid().ToString()
                },
                PingSettings = new PingSettingsDto
                {
                    PingIntervalMs = 5000,
                    PingTimeoutMs = 1000
                }
            };

            // Get all devices from the vessel
            var allDevices = vessel.GetAllDevices().ToList();

            _logger.LogInformation("PINGSWEEPER: Found {TotalDeviceCount} total devices in vessel {VesselId}",
                allDevices.Count, vessel.Id);

            // Log all devices for debugging
            foreach (var device in allDevices)
            {
                var hasDirectConnection = device.HasConnection();
                var ipAddress = hasDirectConnection ? device.Connection?.IPAddress : "NO_CONNECTION";
                _logger.LogDebug("PINGSWEEPER: Device in vessel: {DeviceId} ({DeviceName}) - Role: {DeviceRole}, HasDirectConnection: {HasConnection}, IP: {IPAddress}",
                    device.Id, device.Name, device.DeviceRole, hasDirectConnection, ipAddress);
            }

            // Track device IPs to avoid duplicates and devices that have been processed
            var addedIps = new HashSet<string>();
            var processedDevices = new HashSet<Guid>();

            // PHASE 1: Add devices with direct ConnectionHandler (IP connections)
            _logger.LogInformation("PINGSWEEPER: PHASE 1 - Processing devices with direct ConnectionHandler");

            // Find devices that have direct IP connections using HasConnection() method
            var devicesWithDirectConnections = allDevices
                .Where(d => d.HasConnection()) // This checks ConnectionHandler with valid IP
                .DistinctBy(d => d.Id)
                .ToList();

            _logger.LogInformation("PINGSWEEPER: Found {DevicesWithConnectionsCount} devices with direct ConnectionHandler for PingSweeper config",
                devicesWithDirectConnections.Count);

            // Log each device with a direct connection for debugging
            foreach (var device in devicesWithDirectConnections)
            {
                _logger.LogDebug("PINGSWEEPER: Device with direct ConnectionHandler: {DeviceId} ({DeviceName}) - Role: {DeviceRole}, IP: {IPAddress}, Port: {Port}, Protocol: {Protocol}",
                    device.Id, device.Name, device.DeviceRole,
                    device.Connection?.IPAddress,
                    device.Connection?.Port,
                    device.Connection?.Protocol);
            }

            // Add devices with direct ConnectionHandler to the PingSweeper config
            foreach (var device in devicesWithDirectConnections)
            {
                try
                {
                    // Double-check the device has a connection (should always be true due to HasConnection() filter)
                    if (!device.HasConnection())
                    {
                        _logger.LogWarning("PINGSWEEPER: Device {DeviceId} ({DeviceName}) has no connection despite being in devicesWithDirectConnections list",
                            device.Id, device.Name);
                        continue;
                    }

                    var formattedIp = CameraNameFormatter.FormatIpAddress(device.Connection!.IPAddress);

                    // Skip if this IP has already been added (avoid duplicate IPs)
                    if (addedIps.Contains(formattedIp))
                    {
                        _logger.LogDebug("PINGSWEEPER: Skipping device {DeviceId} ({DeviceName}) with duplicate IP {Ip}",
                            device.Id, device.Name, formattedIp);
                        continue;
                    }

                    // Add the device to the PingSweeper config
                    pingSweeperConfig.PingDestinations.Add(new PingDestinationDto
                    {
                        Name = device.Name,
                        Ip = formattedIp,
                        TechnicalComponentId = ""  // Leave empty as requested
                    });

                    // Track this IP and device to avoid duplicates
                    addedIps.Add(formattedIp);
                    processedDevices.Add(device.Id);

                    _logger.LogInformation("PINGSWEEPER: Added device {DeviceId} ({DeviceName}) with direct IP {Ip} to PingSweeper config",
                        device.Id, device.Name, formattedIp);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "PINGSWEEPER: Error adding device {DeviceId} ({DeviceName}) to PingSweeper config",
                        device.Id, device.Name);
                }
            }

            // PHASE 2: Add devices connected through interface devices (DeviceConnection relationships)
            _logger.LogInformation("PINGSWEEPER: PHASE 2 - Processing devices connected through interface devices");

            // For each device with a direct connection, find devices connected TO it through DeviceConnection relationships
            foreach (var interfaceDevice in devicesWithDirectConnections)
            {
                try
                {
                    // Use the correct Vehicle domain method to get devices connected to this interface device
                    var connectedDevices = vessel.GetDevicesConnectedToInterface(interfaceDevice.Id).ToList();

                    _logger.LogDebug("PINGSWEEPER: Interface device {DeviceId} ({DeviceName}) has {ConnectedDeviceCount} devices connected to it",
                        interfaceDevice.Id, interfaceDevice.Name, connectedDevices.Count);

                    // Log each connected device for debugging
                    foreach (var connectedDevice in connectedDevices)
                    {
                        _logger.LogDebug("PINGSWEEPER: Device {DeviceId} ({DeviceName}) - Role: {DeviceRole} is connected to interface {InterfaceDeviceId} ({InterfaceDeviceName})",
                            connectedDevice.Id, connectedDevice.Name, connectedDevice.DeviceRole, interfaceDevice.Id, interfaceDevice.Name);
                    }

                    // Add each connected device to PingSweeper using the interface device's IP
                    foreach (var connectedDevice in connectedDevices)
                    {
                        // Skip if already processed
                        if (processedDevices.Contains(connectedDevice.Id))
                        {
                            _logger.LogDebug("PINGSWEEPER: Skipping already processed connected device {DeviceId} ({DeviceName})",
                                connectedDevice.Id, connectedDevice.Name);
                            continue;
                        }

                        // Use the interface device's ConnectionHandler IP for the connected device
                        var formattedIp = CameraNameFormatter.FormatIpAddress(interfaceDevice.Connection!.IPAddress);

                        // Skip if this IP has already been added (should not happen since we're using interface device IPs)
                        if (addedIps.Contains(formattedIp))
                        {
                            _logger.LogDebug("PINGSWEEPER: Skipping connected device {DeviceId} ({DeviceName}) - IP {Ip} already added",
                                connectedDevice.Id, connectedDevice.Name, formattedIp);
                            continue;
                        }

                        // Add the connected device to PingSweeper with interface device's IP
                        pingSweeperConfig.PingDestinations.Add(new PingDestinationDto
                        {
                            Name = $"{connectedDevice.Name} (via {interfaceDevice.Name})",
                            Ip = formattedIp,
                            TechnicalComponentId = ""  // Leave empty as requested
                        });

                        // Track this device as processed (but don't add IP to avoid blocking other devices on same interface)
                        processedDevices.Add(connectedDevice.Id);

                        _logger.LogInformation("PINGSWEEPER: Added connected device {DeviceId} ({DeviceName}) via interface {InterfaceDeviceId} ({InterfaceDeviceName}) with IP {Ip} to PingSweeper config",
                            connectedDevice.Id, connectedDevice.Name, interfaceDevice.Id, interfaceDevice.Name, formattedIp);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "PINGSWEEPER: Error processing interface device {DeviceId} ({DeviceName}) for connected devices",
                        interfaceDevice.Id, interfaceDevice.Name);
                }
            }

            // Log the final state of the PingDestinations array
            _logger.LogInformation("PINGSWEEPER: Final PingDestinations count: {Count}",
                pingSweeperConfig.PingDestinations.Count);

            // Validation: Check if any devices with direct connections were not processed
            var devicesWithConnectionsNotAdded = devicesWithDirectConnections
                .Where(d => !processedDevices.Contains(d.Id))
                .ToList();

            if (devicesWithConnectionsNotAdded.Count > 0)
            {
                _logger.LogWarning("PINGSWEEPER: {Count} devices with direct connections were not added to the PingDestinations array",
                    devicesWithConnectionsNotAdded.Count);

                foreach (var device in devicesWithConnectionsNotAdded)
                {
                    _logger.LogWarning("PINGSWEEPER: Device with direct connection not added: {DeviceId} ({DeviceName}) - Role: {DeviceRole}",
                        device.Id, device.Name, device.DeviceRole);
                }
            }

            // Log final ping destinations for debugging
            if (pingSweeperConfig.PingDestinations.Count > 0)
            {
                _logger.LogInformation("PINGSWEEPER: Successfully created PingSweeper config with {Count} destinations",
                    pingSweeperConfig.PingDestinations.Count);

                foreach (var destination in pingSweeperConfig.PingDestinations)
                {
                    _logger.LogDebug("PINGSWEEPER: Final destination: {Name} -> {IP}",
                        destination.Name, destination.Ip);
                }
            }
            else
            {
                _logger.LogWarning("PINGSWEEPER: No ping destinations found for vessel {VesselId} - this may indicate no devices have connections",
                    vessel.Id);
            }

            return pingSweeperConfig;
        }

        /// <summary>
        /// Maps a vessel to a PingSweeper full driver configuration DTO
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <param name="processedDeviceIds">Optional set of device IDs that have already been processed</param>
        /// <returns>A full driver configuration DTO for PingSweeper</returns>
        public PingSweeperFullConfigDto MapToPingSweeperFullConfig(Vessel vessel, HashSet<Guid> processedDeviceIds = null)
        {
            if (vessel == null)
            {
                throw new ArgumentNullException(nameof(vessel));
            }

            // Initialize the processed device IDs if not provided
            processedDeviceIds ??= new HashSet<Guid>();

            _logger.LogDebug("Mapping vessel {VesselId} to PingSweeper full config", vessel.Id);

            // Map the vessel to a PingSweeper config
            // We're not passing processedDeviceIds because we want to include ALL devices with connections
            // in the PingSweeper configuration, regardless of whether they've been processed for other drivers
            var pingSweeperConfig = MapToPingSweeperConfig(vessel);

            // Create a custom full driver configuration for PingSweeper
            // This is different from other drivers because PingSweeper is not a device
            var fullConfig = new PingSweeperFullConfigDto
            {
                // Set the PingSweeper config directly as the DriverConfig
                DriverConfig = pingSweeperConfig,

                // Add the Qos pipeline
                Pipelines = ["Qos"]
            };

            return fullConfig;
        }

        /// <summary>
        /// Checks if a device has a specific device model name
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <param name="modelName">The model name to check for</param>
        /// <returns>True if the device has the specified model name, false otherwise</returns>
        private bool HasDeviceModel(GenericDevice device, string modelName)
        {
            if (device?.Model?.Name == null)
            {
                _logger.LogDebug("Device {DeviceId} ({DeviceName}) has no model or model name is null",
                    device?.Id, device?.Name);
                return false;
            }

            var hasModel = string.Equals(device.Model.Name, modelName, StringComparison.OrdinalIgnoreCase);
            _logger.LogDebug("Device {DeviceId} ({DeviceName}) model '{ModelName}' {MatchResult} '{ExpectedModel}'",
                device.Id, device.Name, device.Model.Name, hasModel ? "matches" : "does not match", modelName);

            return hasModel;
        }

        /// <summary>
        /// Maps a vessel to GenericNmea0183 driver configurations for all sensor devices (excluding A100 sensors)
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <returns>A list of full driver configuration DTOs for GenericNmea0183</returns>
        public List<FullDriverConfigDto> MapToGenericNmea0183Configs(Vessel vessel)
        {
            if (vessel == null)
            {
                throw new ArgumentNullException(nameof(vessel));
            }

            _logger.LogDebug("Mapping vessel {VesselId} to GenericNmea0183 configs", vessel.Id);

            var configs = new List<FullDriverConfigDto>();

            // Get all devices from the vessel
            var allDevices = vessel.GetAllDevices().ToList();

            _logger.LogDebug("GenericNmea0183: Processing {TotalDeviceCount} total devices in vessel {VesselId}",
                allDevices.Count, vessel.Id);

            // Filter for sensor devices only
            var allSensorDevices = allDevices
                .Where(d => d.DeviceRole == DeviceRole.Sensor)
                .ToList();

            _logger.LogDebug("GenericNmea0183: Found {AllSensorDeviceCount} total sensor devices in vessel {VesselId}",
                allSensorDevices.Count, vessel.Id);

            // Log all sensor devices with their model information
            foreach (var sensor in allSensorDevices)
            {
                var modelName = sensor.Model?.Name ?? "NULL";
                _logger.LogDebug("GenericNmea0183: Sensor device {DeviceId} ({DeviceName}) has model: '{ModelName}'",
                    sensor.Id, sensor.Name, modelName);
            }

            // Filter out A100 sensors (they go to EMtrackA100)
            var nonA100SensorDevices = allSensorDevices
                .Where(d => !HasDeviceModel(d, "A100")) // Exclude A100 sensors
                .ToList();

            _logger.LogInformation("GenericNmea0183: Found {NonA100SensorDeviceCount} non-A100 sensor devices (excluding {A100Count} A100 sensors) for GenericNmea0183 configuration in vessel {VesselId}",
                nonA100SensorDevices.Count, allSensorDevices.Count - nonA100SensorDevices.Count, vessel.Id);

            // Log which sensors are being included
            foreach (var sensor in nonA100SensorDevices)
            {
                var modelName = sensor.Model?.Name ?? "NULL";
                _logger.LogDebug("GenericNmea0183: Including sensor device {DeviceId} ({DeviceName}) with model '{ModelName}'",
                    sensor.Id, sensor.Name, modelName);
            }

            // Create a configuration for each non-A100 sensor device
            foreach (var sensorDevice in nonA100SensorDevices)
            {
                try
                {
                    _logger.LogDebug("Processing sensor device {DeviceId} ({DeviceName}) for GenericNmea0183 config",
                        sensorDevice.Id, sensorDevice.Name);

                    // Map the sensor device to a full driver configuration with "Generic" pipeline
                    var config = MapToFullDriverConfig(sensorDevice, ["Generic"]);

                    configs.Add(config);

                    _logger.LogInformation("Added GenericNmea0183 configuration for sensor device {DeviceId} ({DeviceName})",
                        sensorDevice.Id, sensorDevice.Name);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating GenericNmea0183 configuration for sensor device {DeviceId} ({DeviceName})",
                        sensorDevice.Id, sensorDevice.Name);
                }
            }

            _logger.LogInformation("Generated {ConfigCount} GenericNmea0183 configurations for vessel {VesselId}",
                configs.Count, vessel.Id);

            return configs;
        }

        /// <summary>
        /// Maps a vessel to Radar driver configurations for all radar devices
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <returns>A list of full driver configuration DTOs for Radar</returns>
        public List<FullDriverConfigDto> MapToRadarConfigs(Vessel vessel)
        {
            if (vessel == null)
            {
                throw new ArgumentNullException(nameof(vessel));
            }

            _logger.LogDebug("Mapping vessel {VesselId} to Radar configs", vessel.Id);

            var configs = new List<FullDriverConfigDto>();

            // Get all devices from the vessel
            var allDevices = vessel.GetAllDevices().ToList();

            _logger.LogDebug("RADAR: Processing {TotalDeviceCount} total devices in vessel {VesselId}",
                allDevices.Count, vessel.Id);

            // Filter for radar devices only
            var radarDevices = allDevices
                .Where(d => d.DeviceRole == DeviceRole.Radar)
                .ToList();

            _logger.LogInformation("RADAR: Found {RadarDeviceCount} radar devices in vessel {VesselId}",
                radarDevices.Count, vessel.Id);

            // Log all radar devices with their information
            foreach (var radar in radarDevices)
            {
                _logger.LogInformation("RADAR: Radar device {DeviceId} ({DeviceName}) - HasConnection: {HasConnection}",
                    radar.Id, radar.Name, radar.HasConnection());

                if (radar.HasConnection())
                {
                    _logger.LogInformation("RADAR: Radar device {DeviceId} connection - IP: {IP}, Port: {Port}",
                        radar.Id, radar.Connection?.IPAddress ?? "NULL", radar.Connection?.Port.ToString() ?? "NULL");
                }
            }

            // Create a configuration for each radar device
            foreach (var radarDevice in radarDevices)
            {
                try
                {
                    _logger.LogDebug("Processing radar device {DeviceId} ({DeviceName}) for Radar config",
                        radarDevice.Id, radarDevice.Name);

                    // Cast to Radar to access MaritimePosition property
                    var radar = radarDevice as Radar;
                    var radarLocation = radar?.MaritimePosition?.ToConfigurationString() ?? "Bow"; // Default to "Bow" for DaVinci config when position is not set

                    _logger.LogDebug("RADAR: Radar device {DeviceId} maritime position: {MaritimePosition}, config location: {RadarLocation}",
                        radarDevice.Id,
                        radar?.MaritimePosition?.ToDisplayString() ?? "Not specified",
                        radarLocation);

                    // Create a radar-specific driver configuration
                    var radarDriverConfig = new RadarDriverConfigDto
                    {
                        DriverIdentification = new DriverIdentificationDto
                        {
                            Name = radarDevice.Name,
                            TechnicalComponentId = GetTechnicalComponentId(radarDevice),
                            DriverId = Guid.NewGuid().ToString()
                        },
                        RebootIntervalOnFailMs = 1000,
                        HeartbeatIntervalMs = 5000,
                        RadarLocation = radarLocation
                    };

                    // Map the radar device to a connection handler config
                    var connectionHandlerConfig = MapToConnectionHandlerConfig(radarDevice);

                    // Create the full driver configuration with "Radar" pipeline
                    var config = new FullDriverConfigDto
                    {
                        DriverConfig = radarDriverConfig,
                        ConnectionHandlerConfig = connectionHandlerConfig,
                        Pipelines = ["Radar"]
                    };

                    configs.Add(config);

                    _logger.LogInformation("Added Radar configuration for radar device {DeviceId} ({DeviceName}) with location {RadarLocation}",
                        radarDevice.Id, radarDevice.Name, radarDriverConfig.RadarLocation);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing radar device {DeviceId} ({DeviceName}) for Radar config",
                        radarDevice.Id, radarDevice.Name);
                }
            }

            _logger.LogInformation("RADAR: Generated {ConfigCount} Radar configurations for vessel {VesselId}",
                configs.Count, vessel.Id);

            return configs;
        }

        /// <summary>
        /// Maps a vessel to EMtrackA100 driver configurations for A100 sensor devices
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <returns>A list of full driver configuration DTOs for EMtrackA100</returns>
        public List<FullDriverConfigDto> MapToEMtrackA100Configs(Vessel vessel)
        {
            if (vessel == null)
            {
                throw new ArgumentNullException(nameof(vessel));
            }

            _logger.LogDebug("Mapping vessel {VesselId} to EMtrackA100 configs", vessel.Id);

            var configs = new List<FullDriverConfigDto>();

            // Get all devices from the vessel
            var allDevices = vessel.GetAllDevices().ToList();

            _logger.LogDebug("EMtrackA100: Processing {TotalDeviceCount} total devices in vessel {VesselId}",
                allDevices.Count, vessel.Id);

            // Filter for sensor devices only
            var allSensorDevices = allDevices
                .Where(d => d.DeviceRole == DeviceRole.Sensor)
                .ToList();

            _logger.LogDebug("EMtrackA100: Found {AllSensorDeviceCount} total sensor devices in vessel {VesselId}",
                allSensorDevices.Count, vessel.Id);

            // Log all sensor devices with their model information
            foreach (var sensor in allSensorDevices)
            {
                var modelName = sensor.Model?.Name ?? "NULL";
                _logger.LogDebug("EMtrackA100: Sensor device {DeviceId} ({DeviceName}) has model: '{ModelName}'",
                    sensor.Id, sensor.Name, modelName);
            }

            // Filter for A100 sensor devices only
            var a100SensorDevices = allSensorDevices
                .Where(d => HasDeviceModel(d, "A100")) // Only A100 sensors
                .ToList();

            _logger.LogInformation("EMtrackA100: Found {A100SensorDeviceCount} A100 sensor devices (out of {TotalSensorCount} total sensors) for EMtrackA100 configuration in vessel {VesselId}",
                a100SensorDevices.Count, allSensorDevices.Count, vessel.Id);

            // Log which sensors are being included
            foreach (var sensor in a100SensorDevices)
            {
                var modelName = sensor.Model?.Name ?? "NULL";
                _logger.LogDebug("EMtrackA100: Including A100 sensor device {DeviceId} ({DeviceName}) with model '{ModelName}'",
                    sensor.Id, sensor.Name, modelName);
            }

            // Create a configuration for each A100 sensor device
            foreach (var a100SensorDevice in a100SensorDevices)
            {
                try
                {
                    _logger.LogDebug("Processing A100 sensor device {DeviceId} ({DeviceName}) for EMtrackA100 config",
                        a100SensorDevice.Id, a100SensorDevice.Name);

                    // Map the A100 sensor device to a full driver configuration with "Generic" pipeline
                    var config = MapToFullDriverConfig(a100SensorDevice, ["Generic"]);

                    configs.Add(config);

                    _logger.LogInformation("Added EMtrackA100 configuration for A100 sensor device {DeviceId} ({DeviceName})",
                        a100SensorDevice.Id, a100SensorDevice.Name);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating EMtrackA100 configuration for A100 sensor device {DeviceId} ({DeviceName})",
                        a100SensorDevice.Id, a100SensorDevice.Name);
                }
            }

            _logger.LogInformation("Generated {ConfigCount} EMtrackA100 configurations for vessel {VesselId}",
                configs.Count, vessel.Id);

            return configs;
        }

        /// <summary>
        /// Maps a vessel to a driver configuration container DTO
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <returns>A driver configuration container DTO</returns>
        public DriverConfigContainerDto MapToDriverConfigContainer(Vessel vessel)
        {
            if (vessel == null)
            {
                throw new ArgumentNullException(nameof(vessel));
            }

            _logger.LogDebug("Mapping vessel {VesselId} to driver config container", vessel.Id);

            var driverConfigContainer = new DriverConfigContainerDto();

            // Get all devices from the vessel
            var devices = vessel.GetAllDevices().ToList();
            var sensorDevices = devices.Where(d => d.DeviceRole == DeviceRole.Sensor).ToList();

            _logger.LogInformation("CONTAINER: Found {DeviceCount} total devices ({SensorCount} sensors) in vessel {VesselId}",
                devices.Count, sensorDevices.Count, vessel.Id);

            // Log all sensor devices with their models for debugging
            foreach (var sensor in sensorDevices)
            {
                var modelName = sensor.Model?.Name ?? "NULL";
                var hasConnection = sensor.HasConnection();
                var ipAddress = hasConnection ? sensor.Connection?.IPAddress : "NO_CONNECTION";
                _logger.LogDebug("CONTAINER: Sensor device {DeviceId} ({DeviceName}) - Model: '{ModelName}', HasConnection: {HasConnection}, IP: {IPAddress}",
                    sensor.Id, sensor.Name, modelName, hasConnection, ipAddress);
            }

            // Generate GenericNmea0183 driver configurations for non-A100 sensor devices
            _logger.LogInformation("CONTAINER: Generating GenericNmea0183 driver configurations for vessel {VesselId}", vessel.Id);
            var genericNmea0183Configs = MapToGenericNmea0183Configs(vessel);
            driverConfigContainer.GenericNmea0183.AddRange(genericNmea0183Configs);

            _logger.LogInformation("CONTAINER: Added {ConfigCount} GenericNmea0183 configurations for vessel {VesselId}",
                genericNmea0183Configs.Count, vessel.Id);

            // Log which sensors went to GenericNmea0183
            foreach (var config in genericNmea0183Configs)
            {
                _logger.LogDebug("CONTAINER: GenericNmea0183 config added for device: {DeviceName}",
                    config.DriverConfig.DriverIdentification.Name);
            }

            // Generate EMtrackA100 driver configurations for A100 sensor devices
            _logger.LogInformation("CONTAINER: Generating EMtrackA100 driver configurations for vessel {VesselId}", vessel.Id);
            var emtrackA100Configs = MapToEMtrackA100Configs(vessel);
            driverConfigContainer.EmtrackA100.AddRange(emtrackA100Configs);

            _logger.LogInformation("CONTAINER: Added {ConfigCount} EMtrackA100 configurations for vessel {VesselId}",
                emtrackA100Configs.Count, vessel.Id);

            // Log which sensors went to EMtrackA100
            foreach (var config in emtrackA100Configs)
            {
                _logger.LogDebug("CONTAINER: EMtrackA100 config added for device: {DeviceName}",
                    config.DriverConfig.DriverIdentification.Name);
            }

            // Generate Radar driver configurations for radar devices
            _logger.LogInformation("CONTAINER: Generating Radar driver configurations for vessel {VesselId}", vessel.Id);
            var radarConfigs = MapToRadarConfigs(vessel);
            driverConfigContainer.Radar.AddRange(radarConfigs);

            _logger.LogInformation("CONTAINER: Added {ConfigCount} Radar configurations for vessel {VesselId}",
                radarConfigs.Count, vessel.Id);

            // Log which radars were configured
            foreach (var config in radarConfigs)
            {
                _logger.LogDebug("CONTAINER: Radar config added for device: {DeviceName}",
                    config.DriverConfig.DriverIdentification.Name);
            }

            // VALIDATION: Check for sensor duplication across configurations
            var genericSensorNames = genericNmea0183Configs.Select(c => c.DriverConfig.DriverIdentification.Name).ToList();
            var emtrackSensorNames = emtrackA100Configs.Select(c => c.DriverConfig.DriverIdentification.Name).ToList();
            var duplicatedSensors = genericSensorNames.Intersect(emtrackSensorNames).ToList();

            if (duplicatedSensors.Any())
            {
                _logger.LogError("CONTAINER: DUPLICATION DETECTED! The following sensors appear in both GenericNmea0183 and EMtrackA100 configurations: {DuplicatedSensors}",
                    string.Join(", ", duplicatedSensors));

                foreach (var duplicatedSensor in duplicatedSensors)
                {
                    _logger.LogError("CONTAINER: Duplicated sensor: {SensorName}", duplicatedSensor);
                }
            }
            else
            {
                _logger.LogInformation("CONTAINER: VALIDATION PASSED - No sensor duplication detected between configurations");
            }

            // Log summary of sensor device routing
            var totalSensorConfigs = genericNmea0183Configs.Count + emtrackA100Configs.Count;
            _logger.LogInformation("CONTAINER: SUMMARY - Routed {TotalSensorConfigs} sensor devices - {GenericCount} to GenericNmea0183, {A100Count} to EMtrackA100 for vessel {VesselId}",
                totalSensorConfigs, genericNmea0183Configs.Count, emtrackA100Configs.Count, vessel.Id);

            // Verify total sensor count matches
            if (totalSensorConfigs != sensorDevices.Count)
            {
                _logger.LogWarning("CONTAINER: SENSOR COUNT MISMATCH! Expected {ExpectedCount} sensors, but routed {ActualCount} sensors",
                    sensorDevices.Count, totalSensorConfigs);

                var missingSensorCount = sensorDevices.Count - totalSensorConfigs;
                if (missingSensorCount > 0)
                {
                    _logger.LogWarning("CONTAINER: {MissingSensorCount} sensors were not routed to any configuration", missingSensorCount);
                }
                else
                {
                    _logger.LogWarning("CONTAINER: {ExtraSensorCount} extra sensor configurations were created", Math.Abs(missingSensorCount));
                }
            }
            else
            {
                _logger.LogInformation("CONTAINER: SENSOR COUNT VALIDATION PASSED - All {SensorCount} sensors were properly routed", sensorDevices.Count);
            }

            // Always add PingSweeper config regardless of whether devices have direct connections
            // Our enhanced PingSweeper config will handle devices with and without direct connections
            _logger.LogInformation("CONTAINER: Generating PingSweeper config for vessel {VesselId}", vessel.Id);

            // Count devices with connections for validation
            var devicesWithConnections = devices.Where(d => d.HasConnection()).ToList();
            _logger.LogInformation("CONTAINER: Found {DevicesWithConnectionsCount} devices with connections for PingSweeper", devicesWithConnections.Count);

            // Log devices with connections
            foreach (var device in devicesWithConnections)
            {
                _logger.LogDebug("CONTAINER: Device with connection: {DeviceId} ({DeviceName}) - Role: {DeviceRole}, IP: {IPAddress}",
                    device.Id, device.Name, device.DeviceRole, device.Connection?.IPAddress);
            }

            // Map the vessel to a PingSweeper full config
            // We don't need to pass processedDeviceIds because PingSweeper should include ALL devices
            var pingSweeperFullConfig = MapToPingSweeperFullConfig(vessel);

            // Always add the PingSweeper configuration, even if there are no ping destinations
            // Clear any existing PingSweeper configs and add the new one
            // This ensures there's only one PingSweeper configuration in the container
            driverConfigContainer.PingSweeper.Clear();
            driverConfigContainer.PingSweeper.Add(pingSweeperFullConfig);

            if (pingSweeperFullConfig.DriverConfig.PingDestinations.Count > 0)
            {
                _logger.LogInformation("CONTAINER: Added PingSweeper config with {DeviceCount} ping destinations (expected {ExpectedCount} devices with connections)",
                    pingSweeperFullConfig.DriverConfig.PingDestinations.Count, devicesWithConnections.Count);

                // Log each ping destination for debugging
                foreach (var destination in pingSweeperFullConfig.DriverConfig.PingDestinations)
                {
                    _logger.LogDebug("CONTAINER: PingSweeper destination: {Name}, IP: {IP}",
                        destination.Name, destination.Ip);
                }

                // Validate PingSweeper device count
                if (pingSweeperFullConfig.DriverConfig.PingDestinations.Count != devicesWithConnections.Count)
                {
                    var missingDeviceCount = devicesWithConnections.Count - pingSweeperFullConfig.DriverConfig.PingDestinations.Count;
                    _logger.LogWarning("CONTAINER: PINGSWEEPER COUNT MISMATCH! Expected {ExpectedCount} devices with connections, but PingSweeper has {ActualCount} destinations. Missing: {MissingCount}",
                        devicesWithConnections.Count, pingSweeperFullConfig.DriverConfig.PingDestinations.Count, missingDeviceCount);
                }
                else
                {
                    _logger.LogInformation("CONTAINER: PINGSWEEPER VALIDATION PASSED - All {DeviceCount} devices with connections are included", devicesWithConnections.Count);
                }
            }
            else
            {
                _logger.LogInformation("CONTAINER: Added PingSweeper config with no ping destinations");
                if (devicesWithConnections.Count > 0)
                {
                    _logger.LogWarning("CONTAINER: PINGSWEEPER ISSUE - Expected {ExpectedCount} devices with connections, but PingSweeper has 0 destinations", devicesWithConnections.Count);
                }
            }

            return driverConfigContainer;
        }



        /// <summary>
        /// Gets the technical component ID for a device
        /// </summary>
        /// <param name="device">The device</param>
        /// <returns>The technical component ID</returns>
        private static string GetTechnicalComponentId(GenericDevice device)
        {
            // Use the device model manufacturer name if available
            if (device.Model?.Manufacturer != null)
            {
                return device.Model.Manufacturer.Name;
            }

            // Otherwise, generate a unique ID
            return Guid.NewGuid().ToString().Substring(0, 8);
        }

        /// <summary>
        /// Gets the default pipelines for a device role
        /// </summary>
        /// <param name="deviceRole">The device role</param>
        /// <returns>An array of default pipeline names</returns>
        private string[] GetDefaultPipelines(DeviceRole deviceRole)
        {
            return deviceRole switch
            {
                DeviceRole.Radar => ["Radar"],
                DeviceRole.Sensor => ["Generic"],
                _ => ["Generic"]
            };
        }
    }
}
