using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Services.Areas.ConfigGeneration.Models.Configs.Davinci;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for mapping Camera entities to Davinci camera configuration DTOs
    /// </summary>
    public interface IDavinciCameraConfigMapper
    {
        /// <summary>
        /// Maps a Camera entity to a Davinci camera configuration DTO
        /// </summary>
        /// <param name="camera">The Camera entity to map</param>
        /// <returns>A Davinci camera configuration DTO</returns>
        DavinciCameraConfigDto MapToDavinciCameraConfig(Camera camera);
    }
}
