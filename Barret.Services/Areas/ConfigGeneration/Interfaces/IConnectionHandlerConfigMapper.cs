using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Shared.DTOs.DaVinciConfig;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for mapping device domain entities to DaVinci connection handler configuration DTOs
    /// </summary>
    public interface IConnectionHandlerConfigMapper : IConfigMapper<GenericDevice, ConnectionHandlerConfigDto>
    {
    }
}
