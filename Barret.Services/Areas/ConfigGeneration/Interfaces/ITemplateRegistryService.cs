using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Shared.Results;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for the template registry service
    /// </summary>
    public interface ITemplateRegistryService
    {
        /// <summary>
        /// Gets all templates
        /// </summary>
        /// <returns>A result containing all templates</returns>
        Task<Result<IReadOnlyList<ConfigurationTemplate>>> GetAllTemplatesAsync();

        /// <summary>
        /// Gets a template by its path
        /// </summary>
        /// <param name="path">The path to the template</param>
        /// <returns>A result containing the template</returns>
        Task<Result<ConfigurationTemplate>> GetTemplateByPathAsync(string path);

        /// <summary>
        /// Gets templates by handler type (legacy method, returns empty list)
        /// </summary>
        /// <param name="handlerType">The handler type</param>
        /// <returns>A result containing an empty list of templates</returns>
        /// <remarks>
        /// This method is kept for backward compatibility but returns an empty list
        /// since templates no longer have explicit handler types. Use the ConfigurationHandlerProvider
        /// to determine the appropriate handler for a template.
        /// </remarks>
        Task<Result<IReadOnlyList<ConfigurationTemplate>>> GetTemplatesByHandlerTypeAsync(string handlerType);

        /// <summary>
        /// Gets the template base path
        /// </summary>
        /// <returns>The template base path</returns>
        string GetTemplateBasePath();

        /// <summary>
        /// Reloads the template registry
        /// </summary>
        /// <returns>A result indicating success or failure</returns>
        Task<Result<bool>> ReloadAsync();
    }
}
