using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Shared.DTOs.DaVinciConfig;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for mapping vessel domain entities to DaVinci vessel parameters configuration DTOs
    /// </summary>
    public interface IVesselParametersConfigMapper : IConfigMapper<Vessel, VesselParametersConfigDto>
    {
    }
}
