using Microsoft.Extensions.DependencyInjection;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Factory for creating scoped services from singleton contexts
    /// </summary>
    public interface IServiceFactory
    {
        /// <summary>
        /// Creates a scoped instance of the specified service type
        /// </summary>
        /// <typeparam name="T">The service type to create</typeparam>
        /// <returns>A scoped instance of the service</returns>
        T CreateScope<T>() where T : class;
        
        /// <summary>
        /// Creates a scope and returns the service provider for that scope
        /// </summary>
        /// <returns>A service scope</returns>
        IServiceScope CreateServiceScope();
    }
}
