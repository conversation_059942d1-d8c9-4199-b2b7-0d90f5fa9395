using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Shared.DTOs.DaVinciConfig;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for mapping camera domain entities to DaVinci camera configuration DTOs
    /// </summary>
    public interface ICameraConfigMapper : IConfigMapper<Camera, CameraConfigDto>
    {
        /// <summary>
        /// Maps a Camera domain entity to a DaVinci camera configuration DTO
        /// </summary>
        /// <param name="camera">The camera domain entity to map</param>
        /// <returns>A DaVinci camera configuration DTO</returns>
        CameraConfigDto MapToCameraConfig(Camera camera);
    }
}
