using System;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Generic interface for mapping domain entities to configuration DTOs
    /// </summary>
    /// <typeparam name="TEntity">The type of the domain entity</typeparam>
    /// <typeparam name="TConfig">The type of the configuration DTO</typeparam>
    public interface IConfigMapper<TEntity, TConfig>
    {
        /// <summary>
        /// Maps a domain entity to a configuration DTO
        /// </summary>
        /// <param name="entity">The domain entity to map</param>
        /// <returns>A configuration DTO</returns>
        TConfig MapToConfig(TEntity entity);
    }
}
