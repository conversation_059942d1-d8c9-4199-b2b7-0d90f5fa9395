using System.Threading.Tasks;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Enums;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for handling configuration generation from templates
    /// </summary>
    public interface IConfigurationHandler
    {
        /// <summary>
        /// Gets the handler type identifier
        /// </summary>
        string HandlerType { get; }

        /// <summary>
        /// Gets the system type this handler supports
        /// </summary>
        SystemType SupportedSystemType { get; }

        /// <summary>
        /// Gets the config type this handler supports
        /// </summary>
        string SupportedConfigType { get; }

        /// <summary>
        /// Generates a configuration from a template and writes it to the output path
        /// </summary>
        /// <param name="context">The configuration context</param>
        /// <param name="template">The template to use</param>
        /// <returns>The configuration output</returns>
        Task<ConfigurationOutput> GenerateConfigurationAsync(ConfigurationContext context, ConfigurationTemplate template);

        /// <summary>
        /// Checks if this handler can handle the specified template
        /// </summary>
        /// <param name="template">The template to check</param>
        /// <returns>True if this handler can handle the template, false otherwise</returns>
        /// <remarks>
        /// This method is kept for backward compatibility but is no longer used for handler resolution.
        /// The ConfigurationHandlerProvider now determines the appropriate handler based on file path patterns.
        /// </remarks>
        bool CanHandle(ConfigurationTemplate template);

        /// <summary>
        /// Checks if this handler can handle the specified template path
        /// </summary>
        /// <param name="templatePath">The path to the template</param>
        /// <returns>True if this handler can handle the template path, false otherwise</returns>
        /// <remarks>
        /// This method is used for path-based handler resolution, allowing handlers to be selected
        /// based on the full path of the template, including parent directories.
        /// </remarks>
        bool CanHandlePath(string templatePath);
    }
}
