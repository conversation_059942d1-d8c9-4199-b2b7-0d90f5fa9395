using System;
using System.Threading.Tasks;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Services;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for managing vessel instances to ensure single loading and consistent data
    /// </summary>
    public interface IVesselInstanceManager
    {
        /// <summary>
        /// Gets a vessel instance, loading it once and caching it for subsequent requests
        /// </summary>
        /// <param name="vesselId">The ID of the vessel</param>
        /// <returns>The vessel instance</returns>
        Task<Vessel> GetVesselInstanceAsync(Guid vesselId);

        /// <summary>
        /// Clears the vessel cache for a specific vessel
        /// </summary>
        /// <param name="vesselId">The ID of the vessel to remove from cache</param>
        void ClearVesselCache(Guid vesselId);

        /// <summary>
        /// Clears all vessel instances from the cache
        /// </summary>
        void ClearAllCache();

        /// <summary>
        /// Gets the current cache statistics
        /// </summary>
        /// <returns>Cache statistics</returns>
        VesselCacheStatistics GetCacheStatistics();
    }
}
