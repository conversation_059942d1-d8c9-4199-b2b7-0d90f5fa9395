using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Shared.Results;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for generating configuration files for vessels
    /// </summary>
    public interface IConfigurationGenerator
    {
        /// <summary>
        /// Generates all configurations for a vessel and writes them to the specified base directory
        /// </summary>
        /// <param name="vessel">The vessel to generate configurations for</param>
        /// <param name="baseDirectory">The base directory to write the configurations to</param>
        /// <param name="progress">The progress reporter</param>
        /// <param name="cancellationToken">The cancellation token</param>
        /// <returns>A result containing the list of configuration outputs</returns>
        Task<Result<IReadOnlyList<ConfigurationOutput>>> GenerateConfigurationsAsync(
            Vessel vessel,
            string baseDirectory,
            IProgress<int>? progress = null,
            CancellationToken cancellationToken = default);
    }
}
