using Barret.Services.Areas.ConfigGeneration.Domain;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for providing configuration handlers for templates
    /// </summary>
    public interface IConfigurationHandlerProvider
    {
        /// <summary>
        /// Gets a handler for the specified template
        /// </summary>
        /// <param name="template">The template to get a handler for</param>
        /// <returns>The handler, or null if no handler is available</returns>
        IConfigurationHandler GetHandlerForTemplate(ConfigurationTemplate template);
    }
}
