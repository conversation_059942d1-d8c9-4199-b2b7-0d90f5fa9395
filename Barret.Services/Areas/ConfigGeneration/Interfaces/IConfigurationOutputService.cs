using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Shared.Results;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for the configuration output service
    /// </summary>
    public interface IConfigurationOutputService
    {
        /// <summary>
        /// Creates a temporary directory for configuration output
        /// </summary>
        /// <param name="vessel">The vessel for which configurations are being generated</param>
        /// <returns>A result containing the path to the temporary directory</returns>
        Task<Result<string>> CreateTemporaryDirectoryAsync(Vessel vessel);

        /// <summary>
        /// Creates a ZIP file from a directory
        /// </summary>
        /// <param name="sourceDirectory">The source directory</param>
        /// <param name="vessel">The vessel for which configurations are being generated</param>
        /// <returns>A result containing the path to the ZIP file</returns>
        Task<Result<string>> CreateZipFileAsync(string sourceDirectory, Vessel vessel);

        /// <summary>
        /// Cleans up temporary files
        /// </summary>
        /// <param name="path">The path to clean up</param>
        /// <returns>A result indicating success or failure</returns>
        Task<Result<bool>> CleanupAsync(string path);
    }
}
