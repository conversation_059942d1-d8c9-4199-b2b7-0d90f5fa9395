using System.Threading.Tasks;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Shared.Results;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for exporting configurations
    /// </summary>
    public interface IConfigExportService
    {
        /// <summary>
        /// Exports configurations for a vessel to a ZIP file
        /// </summary>
        /// <param name="vessel">The vessel to export configurations for</param>
        /// <returns>A result containing the path to the ZIP file</returns>
        Task<Result<string>> ExportConfigurationsToZipAsync(Vessel vessel);
    }
}
