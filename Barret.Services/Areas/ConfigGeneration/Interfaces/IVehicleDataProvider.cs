using System;
using System.Threading.Tasks;
using Barret.Core.Areas.Vehicles.Models.Vessel;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for the vehicle data provider
    /// </summary>
    public interface IVehicleDataProvider
    {
        /// <summary>
        /// Gets a vessel with all data needed for configuration generation
        /// </summary>
        /// <param name="vesselId">The ID of the vessel</param>
        /// <returns>The vessel with all necessary data</returns>
        Task<Vessel> GetVesselWithAllDataAsync(Guid vesselId);
    }
}
