using System.Collections.Generic;
using Barret.Services.Areas.ConfigGeneration.Domain;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Registry for configuration handlers
    /// </summary>
    public interface IHandlerRegistry
    {
        /// <summary>
        /// Registers a handler with a path pattern
        /// </summary>
        /// <param name="pathPattern">The path pattern to match (supports * wildcard)</param>
        /// <param name="handler">The handler to register</param>
        void RegisterHandler(string pathPattern, IConfigurationHandler handler);
        
        /// <summary>
        /// Gets a handler for a template path
        /// </summary>
        /// <param name="templatePath">The template path</param>
        /// <returns>The handler, or null if no handler is found</returns>
        IConfigurationHandler? GetHandler(string templatePath);
        
        /// <summary>
        /// Gets all registered handlers
        /// </summary>
        /// <returns>The registered handlers</returns>
        IReadOnlyList<IConfigurationHandler> GetAllHandlers();
    }
}
