using System;
using System.Collections.Generic;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Shared.DTOs.DaVinciConfig.DriverConfig;

namespace Barret.Services.Areas.ConfigGeneration.Interfaces
{
    /// <summary>
    /// Interface for mapping device domain entities to DaVinci driver configuration DTOs
    /// </summary>
    public interface IDriverConfigMapper : IConfigMapper<GenericDevice, Barret.Shared.DTOs.DaVinciConfig.DriverConfigDto>
    {
        /// <summary>
        /// Maps a device to a connection handler configuration DTO
        /// </summary>
        /// <param name="device">The device to map</param>
        /// <returns>A connection handler configuration DTO</returns>
        Barret.Shared.DTOs.DaVinciConfig.DriverConfig.ConnectionHandlerConfigDto MapToConnectionHandlerConfig(GenericDevice device);

        /// <summary>
        /// Maps a device to a full driver configuration DTO
        /// </summary>
        /// <param name="device">The device to map</param>
        /// <param name="pipelines">The pipelines to include in the configuration</param>
        /// <returns>A full driver configuration DTO</returns>
        FullDriverConfigDto MapToFullDriverConfig(GenericDevice device, string[] pipelines = null);

        /// <summary>
        /// Maps a vessel to a PingSweeper configuration DTO
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <param name="processedDeviceIds">Optional set of device IDs that have already been processed</param>
        /// <returns>A PingSweeper configuration DTO</returns>
        PingSweeperConfigDto MapToPingSweeperConfig(Vessel vessel, HashSet<Guid> processedDeviceIds = null);

        /// <summary>
        /// Maps a vessel to a PingSweeper full driver configuration DTO
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <param name="processedDeviceIds">Optional set of device IDs that have already been processed</param>
        /// <returns>A full driver configuration DTO for PingSweeper</returns>
        PingSweeperFullConfigDto MapToPingSweeperFullConfig(Vessel vessel, HashSet<Guid> processedDeviceIds = null);

        /// <summary>
        /// Maps a vessel to GenericNmea0183 driver configurations for all sensor devices (excluding A100 sensors)
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <returns>A list of full driver configuration DTOs for GenericNmea0183</returns>
        List<FullDriverConfigDto> MapToGenericNmea0183Configs(Vessel vessel);

        /// <summary>
        /// Maps a vessel to EMtrackA100 driver configurations for A100 sensor devices
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <returns>A list of full driver configuration DTOs for EMtrackA100</returns>
        List<FullDriverConfigDto> MapToEMtrackA100Configs(Vessel vessel);

        /// <summary>
        /// Maps a vessel to Radar driver configurations for all radar devices
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <returns>A list of full driver configuration DTOs for Radar</returns>
        List<FullDriverConfigDto> MapToRadarConfigs(Vessel vessel);

        /// <summary>
        /// Maps a vessel to a driver configuration container DTO
        /// </summary>
        /// <param name="vessel">The vessel to map</param>
        /// <returns>A driver configuration container DTO</returns>
        DriverConfigContainerDto MapToDriverConfigContainer(Vessel vessel);
    }
}
