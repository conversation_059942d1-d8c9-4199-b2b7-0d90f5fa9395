namespace Barret.Services.Areas.ConfigGeneration.Enums
{
    /// <summary>
    /// Enum representing the different system types for configuration generation
    /// </summary>
    public enum SystemType
    {
        /// <summary>
        /// Unknown or unspecified system
        /// </summary>
        Unknown = 0,
        
        /// <summary>
        /// <PERSON><PERSON><PERSON> system
        /// </summary>
        Davinci = 1,
        
        /// <summary>
        /// Michel<PERSON> system
        /// </summary>
        <PERSON><PERSON> = 2,
        
        /// <summary>
        /// Rembrandt system
        /// </summary>
        Rembrandt = 3,
        
        /// <summary>
        /// Mondriaan system
        /// </summary>
        Mondriaan = 4,
        
        /// <summary>
        /// Deepstream system
        /// </summary>
        Deepstream = 5
    }
}
