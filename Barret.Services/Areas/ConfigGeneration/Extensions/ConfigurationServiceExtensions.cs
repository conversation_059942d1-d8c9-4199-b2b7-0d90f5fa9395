using System;
using System.IO;
using Barret.Services.Areas.ConfigGeneration.Handlers;
using Barret.Services.Areas.ConfigGeneration.Handlers.Davinci;
using Barret.Services.Areas.ConfigGeneration.Handlers.Michelangelo;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Mappers;
using Barret.Services.Areas.ConfigGeneration.Services;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.ConfigGeneration.Extensions
{
    /// <summary>
    /// Extension methods for registering configuration services
    /// </summary>
    public static class ConfigurationServiceExtensions
    {
        /// <summary>
        /// Adds configuration services to the service collection
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="templateBasePath">The base path for configuration templates</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection AddConfigurationServices(this IServiceCollection services, string templateBasePath)
        {
            if (services == null)
                throw new ArgumentNullException(nameof(services));

            if (string.IsNullOrWhiteSpace(templateBasePath))
                throw new ArgumentException("Template base path cannot be empty", nameof(templateBasePath));

            // Ensure the template base path exists
            if (!Directory.Exists(templateBasePath))
                throw new DirectoryNotFoundException($"Template base path not found: {templateBasePath}");

            // Register the template registry service
            services.AddSingleton<ITemplateRegistryService>(sp =>
                new FileSystemTemplateRegistryService(
                    sp.GetRequiredService<ILogger<FileSystemTemplateRegistryService>>(),
                    templateBasePath));

            // Register configuration handlers
            services.AddSingleton<IConfigurationHandler, StaticConfigHandler>();

            // Register camera configuration handler and its dependencies
            services.AddSingleton<ICameraConfigMapper, CameraConfigMapper>();
            services.AddSingleton<IConfigurationHandler, CameraConfigHandler>();
            services.AddSingleton<IConfigurationHandler, DavinciCameraConfigHandler>();

            // Register driver configuration handler and its dependencies
            services.AddSingleton<IDriverConfigMapper, DriverConfigMapper>();
            services.AddSingleton<IConfigurationHandler, DavinciDriverConfigHandler>();
            services.AddSingleton<IConfigurationHandler, MichelangeloDriverConfigHandler>();

            // Register vessel parameters configuration handler and its dependencies
            services.AddSingleton<IVesselParametersConfigMapper, VesselParametersConfigMapper>();
            services.AddSingleton<IConfigurationHandler, VesselParametersConfigHandler>();
            services.AddSingleton<IConfigurationHandler, DavinciVesselParametersConfigHandler>();

            // Register the configuration handler provider
            services.AddSingleton<IConfigurationHandlerProvider, ConfigurationHandlerProvider>();

            // Register the configuration output service
            services.AddSingleton<IConfigurationOutputService, ConfigurationOutputService>();

            // Register the configuration manager
            services.AddSingleton<IConfigGenerationService>(sp =>
                new ConfigurationManager(
                    sp.GetRequiredService<ILogger<ConfigurationManager>>(),
                    sp.GetRequiredService<ITemplateRegistryService>(),
                    sp.GetRequiredService<IConfigurationHandlerProvider>()));

            // Register the configuration export service
            services.AddScoped<IConfigExportService, ConfigExportService>();

            // Register the vehicle data provider for loading vessels with full data
            services.AddScoped<IVehicleDataProvider, VehicleDataProvider>();

            // Register the vessel instance manager for consistent vessel loading
            services.AddScoped<IVesselInstanceManager, VesselInstanceManager>();

            return services;
        }
    }
}
