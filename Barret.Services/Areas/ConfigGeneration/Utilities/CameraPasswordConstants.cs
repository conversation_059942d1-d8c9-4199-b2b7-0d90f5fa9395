namespace Barret.Services.Areas.ConfigGeneration.Utilities
{
    /// <summary>
    /// Constants for encrypted camera passwords by brand
    /// </summary>
    public static class CameraPasswordConstants
    {
        /// <summary>
        /// Encrypted password for Dacom cameras
        /// </summary>
        public const string DacomEncryptedPassword = "dSawBUGfKTVZm/xVhdRweA==";

        /// <summary>
        /// Encrypted password for Axis cameras
        /// </summary>
        public const string AxisEncryptedPassword = "c+xvtrFGt4afLRqg1UmGBB/v6gJ4epbzbco1b/yejXc=";

        /// <summary>
        /// Default encrypted password for unknown or generic cameras
        /// </summary>
        public const string DefaultEncryptedPassword = "dSawBUGfKTVZm/xVhdRweA=="; // Same as Dacom for now
    }
}
