using System;
using System.IO;
using Barret.Services.Areas.ConfigGeneration.Enums;

namespace Barret.Services.Areas.ConfigGeneration.Utilities
{
    /// <summary>
    /// Utility class for parsing template paths and extracting system and config information
    /// </summary>
    public static class TemplatePathParser
    {
        /// <summary>
        /// Extracts the system type from a template path
        /// </summary>
        /// <param name="templatePath">The template path to parse</param>
        /// <returns>The detected system type, or SystemType.Unknown if no system is detected</returns>
        public static SystemType ExtractSystemType(string templatePath)
        {
            if (string.IsNullOrEmpty(templatePath))
                return SystemType.Unknown;
                
            // Check if the path contains any of the system names
            if (templatePath.Contains("DavinciConfigs", StringComparison.OrdinalIgnoreCase))
                return SystemType.Davinci;
            if (templatePath.Contains("MichelangeloConfigs", StringComparison.OrdinalIgnoreCase))
                return SystemType.Michelangelo;
            if (templatePath.Contains("RembrandtConfigs", StringComparison.OrdinalIgnoreCase))
                return SystemType.Rembrandt;
            if (templatePath.Contains("MondriaanConfigs", StringComparison.OrdinalIgnoreCase))
                return SystemType.Mondriaan;
            if (templatePath.Contains("DeepstreamConfigs", StringComparison.OrdinalIgnoreCase))
                return SystemType.Deepstream;
                
            return SystemType.Unknown;
        }
        
        /// <summary>
        /// Extracts the config type from a template path
        /// </summary>
        /// <param name="templatePath">The template path to parse</param>
        /// <returns>The detected config type, or an empty string if no config type is detected</returns>
        public static string ExtractConfigType(string templatePath)
        {
            if (string.IsNullOrEmpty(templatePath))
                return string.Empty;
                
            // Extract the filename without extension
            string fileName = Path.GetFileNameWithoutExtension(templatePath);
            
            // If the filename ends with "Config", remove it to get the config type
            if (fileName.EndsWith("Config", StringComparison.OrdinalIgnoreCase))
                return fileName.Substring(0, fileName.Length - "Config".Length);
                
            return fileName;
        }
        
        /// <summary>
        /// Checks if a template path matches a specific system type and config type
        /// </summary>
        /// <param name="templatePath">The template path to check</param>
        /// <param name="systemType">The system type to match</param>
        /// <param name="configType">The config type to match</param>
        /// <returns>True if the template path matches the system type and config type, false otherwise</returns>
        public static bool MatchesSystemAndConfigType(string templatePath, SystemType systemType, string configType)
        {
            if (string.IsNullOrEmpty(templatePath) || string.IsNullOrEmpty(configType))
                return false;
                
            // Extract the system type and config type from the path
            SystemType extractedSystemType = ExtractSystemType(templatePath);
            string extractedConfigType = ExtractConfigType(templatePath);
            
            // Check if they match
            return extractedSystemType == systemType && 
                   string.Equals(extractedConfigType, configType, StringComparison.OrdinalIgnoreCase);
        }
    }
}
