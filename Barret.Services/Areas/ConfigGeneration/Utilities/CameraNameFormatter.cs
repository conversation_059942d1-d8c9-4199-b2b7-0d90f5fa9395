using System;
using System.Text.RegularExpressions;

namespace Barret.Services.Areas.ConfigGeneration.Utilities
{
    /// <summary>
    /// Utility class for formatting camera names according to DaVinci standards
    /// </summary>
    public static class CameraNameFormatter
    {
        /// <summary>
        /// Formats a camera name according to DaVinci standards
        /// </summary>
        /// <param name="cameraName">The original camera name</param>
        /// <returns>The formatted camera name</returns>
        public static string FormatCameraName(string cameraName)
        {
            if (string.IsNullOrEmpty(cameraName))
                return cameraName;

            // Remove "Camera - " prefix if present
            string formattedName = cameraName.Replace("Camera - ", "");
            
            // Replace common terms with abbreviations
            formattedName = formattedName
                .Replace("Forward looking", "FW")
                .Replace("Backward looking", "BW")
                .Replace("Port", "PS")
                .Replace("Starboard", "SB");
            
            // Remove any double spaces that might have been created
            formattedName = Regex.Replace(formattedName, @"\s+", " ").Trim();
            
            return formattedName;
        }
        
        /// <summary>
        /// Gets the default username for a camera based on its brand
        /// </summary>
        /// <param name="brand">The camera brand</param>
        /// <returns>The default username</returns>
        public static string GetDefaultUsername(string brand)
        {
            return brand.Equals("Axis", StringComparison.OrdinalIgnoreCase) ? "root" : "admin";
        }
        
        /// <summary>
        /// Formats an IP address by removing any subnet mask
        /// </summary>
        /// <param name="ipAddress">The IP address, possibly with subnet mask</param>
        /// <returns>The IP address without subnet mask</returns>
        public static string FormatIpAddress(string ipAddress)
        {
            if (string.IsNullOrEmpty(ipAddress))
                return ipAddress;
                
            // Remove subnet mask if present
            int slashIndex = ipAddress.IndexOf('/');
            if (slashIndex > 0)
            {
                return ipAddress.Substring(0, slashIndex);
            }
            
            return ipAddress;
        }
    }
}
