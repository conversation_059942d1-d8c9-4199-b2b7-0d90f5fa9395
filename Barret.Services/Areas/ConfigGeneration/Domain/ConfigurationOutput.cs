namespace Barret.Services.Areas.ConfigGeneration.Domain
{
    /// <summary>
    /// Represents the output of a configuration generation process
    /// </summary>
    public class ConfigurationOutput
    {
        /// <summary>
        /// Gets or sets the path to the generated configuration file
        /// </summary>
        public string OutputPath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the generation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets the error message if the generation failed
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the warning messages from the generation process
        /// </summary>
        public List<string> Warnings { get; set; } = new List<string>();

        /// <summary>
        /// Gets or sets the template that was used to generate this output
        /// </summary>
        public ConfigurationTemplate Template { get; set; } = null!;

        /// <summary>
        /// Creates a new instance of the ConfigurationOutput class
        /// </summary>
        public ConfigurationOutput() { }

        /// <summary>
        /// Creates a new instance of the ConfigurationOutput class with the specified template
        /// </summary>
        /// <param name="template">The template used to generate this output</param>
        public ConfigurationOutput(ConfigurationTemplate template)
        {
            Template = template;
            OutputPath = template.OutputPath;
        }

        /// <summary>
        /// Creates a successful output
        /// </summary>
        /// <param name="template">The template used to generate this output</param>
        /// <param name="outputPath">The path to the generated configuration file</param>
        /// <returns>A successful configuration output</returns>
        public static ConfigurationOutput CreateSuccess(ConfigurationTemplate template, string outputPath)
        {
            return new ConfigurationOutput(template)
            {
                OutputPath = outputPath,
                Success = true
            };
        }

        /// <summary>
        /// Creates a failed output
        /// </summary>
        /// <param name="template">The template used to generate this output</param>
        /// <param name="errorMessage">The error message</param>
        /// <returns>A failed configuration output</returns>
        public static ConfigurationOutput Failure(ConfigurationTemplate template, string errorMessage)
        {
            return new ConfigurationOutput(template)
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// Adds a warning to the output
        /// </summary>
        /// <param name="warning">The warning message</param>
        public void AddWarning(string warning)
        {
            Warnings.Add(warning);
        }
    }
}
