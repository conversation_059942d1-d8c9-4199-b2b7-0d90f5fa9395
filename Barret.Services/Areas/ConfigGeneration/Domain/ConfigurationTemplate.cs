using System.Text.Json.Serialization;

namespace Barret.Services.Areas.ConfigGeneration.Domain
{
    /// <summary>
    /// Represents a configuration template with its metadata
    /// </summary>
    public class ConfigurationTemplate
    {
        /// <summary>
        /// Gets or sets the path to the template file, relative to the template base directory
        /// </summary>
        public string Path { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the output path for the generated configuration, relative to the output base directory
        /// </summary>
        public string OutputPath { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the description of the template
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the template is required for a valid configuration
        /// </summary>
        public bool IsRequired { get; set; } = true;

        /// <summary>
        /// Gets or sets the dependencies of this template
        /// </summary>
        public List<string> Dependencies { get; set; } = new List<string>();

        /// <summary>
        /// Creates a new instance of the ConfigurationTemplate class
        /// </summary>
        public ConfigurationTemplate() { }

        /// <summary>
        /// Creates a new instance of the ConfigurationTemplate class from a template entry
        /// </summary>
        /// <param name="entry">The template entry</param>
        public ConfigurationTemplate(Models.TemplateEntry entry)
        {
            Path = entry.Path;
            OutputPath = entry.OutputPath;
            Description = entry.Description ?? string.Empty;
            IsRequired = entry.IsRequired ?? true;
            Dependencies = entry.Dependencies ?? new List<string>();
        }
    }
}
