using Barret.Core.Areas.Vehicles.Models.Vessel;

namespace Barret.Services.Areas.ConfigGeneration.Domain
{
    /// <summary>
    /// Represents the context for a configuration generation process
    /// </summary>
    /// <remarks>
    /// Creates a new instance of the ConfigurationContext class
    /// </remarks>
    /// <param name="vessel">The vessel for which configurations are being generated</param>
    /// <param name="templateBaseDirectory">The base directory for templates</param>
    /// <param name="outputBaseDirectory">The base directory for output</param>
    /// <param name="cancellationTokenSource">The cancellation token source for the generation process</param>
    public class ConfigurationContext(
        Vessel vessel,
        string templateBaseDirectory,
        string outputBaseDirectory,
        CancellationTokenSource? cancellationTokenSource = null)
    {
        /// <summary>
        /// Gets or sets the vessel for which configurations are being generated
        /// </summary>
        public Vessel Vessel { get; } = vessel ?? throw new ArgumentNullException(nameof(vessel));

        /// <summary>
        /// Gets or sets the base directory for templates
        /// </summary>
        public string TemplateBaseDirectory { get; } = templateBaseDirectory ?? throw new ArgumentNullException(nameof(templateBaseDirectory));

        /// <summary>
        /// Gets or sets the base directory for output
        /// </summary>
        public string OutputBaseDirectory { get; } = outputBaseDirectory ?? throw new ArgumentNullException(nameof(outputBaseDirectory));

        /// <summary>
        /// Gets or sets the cancellation token source for the generation process
        /// </summary>
        public CancellationTokenSource CancellationTokenSource { get; } = cancellationTokenSource ?? new CancellationTokenSource();

        /// <summary>
        /// Gets the cancellation token for the generation process
        /// </summary>
        public CancellationToken CancellationToken => CancellationTokenSource.Token;

        /// <summary>
        /// Gets the full path to a template file
        /// </summary>
        /// <param name="templatePath">The relative path to the template file</param>
        /// <returns>The full path to the template file</returns>
        public string GetTemplateFullPath(string templatePath)
        {
            return Path.Combine(TemplateBaseDirectory, templatePath);
        }

        /// <summary>
        /// Gets the full path to an output file
        /// </summary>
        /// <param name="outputPath">The relative path to the output file</param>
        /// <returns>The full path to the output file</returns>
        public string GetOutputFullPath(string outputPath)
        {
            return Path.Combine(OutputBaseDirectory, outputPath);
        }
    }
}
