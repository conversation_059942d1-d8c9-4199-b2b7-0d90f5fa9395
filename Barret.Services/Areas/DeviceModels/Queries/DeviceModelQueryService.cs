using Barret.Core.Areas.Devices.Enums;
using Barret.Services.Core.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.DeviceModels.Repositories;
using Barret.Shared.DTOs.Devices;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.DeviceModels.Queries
{
    /// <summary>
    /// Query service implementation for device models.
    /// This service provides read-only operations for device models.
    /// For write operations, use the ManufacturerService as Manufacturer is the aggregate root.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceModelQueryService"/> class.
    /// </remarks>
    /// <param name="deviceModelRepository">The device model repository</param>
    /// <param name="logger">The logger</param>
    /// <exception cref="ArgumentNullException">Thrown if any parameter is null</exception>
    public class DeviceModelQueryService(
        IDeviceModelRepository deviceModelRepository,
        ILogger<DeviceModelQueryService> logger) : IDeviceModelQueryService
    {
        private readonly IDeviceModelRepository _deviceModelRepository = deviceModelRepository ?? throw new ArgumentNullException(nameof(deviceModelRepository));
        private readonly ILogger<DeviceModelQueryService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Gets a device model by its ID.
        /// </summary>
        /// <param name="id">The ID of the device model to retrieve</param>
        /// <returns>The device model DTO if found; otherwise, null</returns>
        public async Task<DeviceModelInfo?> GetDeviceModelByIdAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Getting device model with ID {DeviceModelId}", id);

                var deviceModel = await _deviceModelRepository.GetByIdAsync(id);
                if (deviceModel == null)
                {
                    _logger.LogWarning("Device model with ID {DeviceModelId} not found", id);
                    return null;
                }

                var deviceModelInfo = new DeviceModelInfo
                {
                    Id = deviceModel.Id,
                    Name = deviceModel.Name,
                    DeviceRole = deviceModel.DeviceRole,
                    ManufacturerId = deviceModel.ManufacturerId,
                    ManufacturerName = deviceModel.Manufacturer?.Name ?? string.Empty
                };

                _logger.LogInformation("Successfully retrieved device model {DeviceModelId}", id);

                return deviceModelInfo;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device model with ID {DeviceModelId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets all device models.
        /// </summary>
        /// <returns>A list of all device model DTOs</returns>
        public async Task<List<DeviceModelInfo>> GetAllDeviceModelsAsync()
        {
            try
            {
                _logger.LogDebug("Getting all device models");

                var deviceModels = await _deviceModelRepository.GetAllAsync();
                var deviceModelInfos = deviceModels.Select(dm => new DeviceModelInfo
                {
                    Id = dm.Id,
                    Name = dm.Name,
                    DeviceRole = dm.DeviceRole,
                    ManufacturerId = dm.ManufacturerId,
                    ManufacturerName = dm.Manufacturer?.Name ?? string.Empty
                }).ToList();

                _logger.LogInformation("Successfully retrieved {Count} device models", deviceModelInfos.Count);

                return deviceModelInfos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all device models");
                throw;
            }
        }

        /// <summary>
        /// Gets device models for a specific manufacturer.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer ID</param>
        /// <returns>A collection of device model DTOs for the manufacturer</returns>
        public async Task<List<DeviceModelInfo>> GetDeviceModelsByManufacturerIdAsync(Guid manufacturerId)
        {
            try
            {
                _logger.LogDebug("Getting device models for manufacturer with ID {ManufacturerId}", manufacturerId);

                var deviceModels = await _deviceModelRepository.GetDeviceModelsByManufacturerIdAsync(manufacturerId);
                var deviceModelInfos = deviceModels.Select(dm => new DeviceModelInfo
                {
                    Id = dm.Id,
                    Name = dm.Name,
                    DeviceRole = dm.DeviceRole,
                    ManufacturerId = dm.ManufacturerId,
                    ManufacturerName = dm.Manufacturer?.Name ?? string.Empty
                }).ToList();

                _logger.LogInformation("Successfully retrieved {Count} device models for manufacturer {ManufacturerId}",
                    deviceModelInfos.Count, manufacturerId);

                return deviceModelInfos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device models for manufacturer with ID {ManufacturerId}", manufacturerId);
                throw;
            }
        }

        /// <summary>
        /// Gets device models for a specific manufacturer that match a specific device role.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer ID</param>
        /// <param name="deviceRole">The device role to filter by</param>
        /// <returns>A collection of device model DTOs that match the criteria</returns>
        public async Task<List<DeviceModelInfo>> GetDeviceModelsForManufacturerWithRoleAsync(Guid manufacturerId, DeviceRole deviceRole)
        {
            try
            {
                _logger.LogDebug("Getting device models for manufacturer {ManufacturerId} with role {DeviceRole}",
                    manufacturerId, deviceRole);

                var deviceModels = await _deviceModelRepository.GetDeviceModelsForManufacturerWithRoleAsync(manufacturerId, deviceRole);
                var deviceModelInfos = deviceModels.Select(dm => new DeviceModelInfo
                {
                    Id = dm.Id,
                    Name = dm.Name,
                    DeviceRole = dm.DeviceRole,
                    ManufacturerId = dm.ManufacturerId,
                    ManufacturerName = dm.Manufacturer?.Name ?? string.Empty
                }).ToList();

                _logger.LogInformation("Successfully retrieved {Count} device models for manufacturer {ManufacturerId} with role {DeviceRole}",
                    deviceModelInfos.Count, manufacturerId, deviceRole);

                return deviceModelInfos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device models for manufacturer {ManufacturerId} with role {DeviceRole}",
                    manufacturerId, deviceRole);
                throw;
            }
        }
    }
}
