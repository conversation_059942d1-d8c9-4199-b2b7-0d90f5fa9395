using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.DeviceModels.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.DeviceModels.Repositories
{
    public class DeviceModelRepository(IBarretDbContext dbContext, ILogger<DeviceModelRepository> logger) : IDeviceModelRepository
    {
        private readonly IBarretDbContext _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        private readonly ILogger<DeviceModelRepository> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        public async Task<DeviceModel?> GetByIdAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Getting device model with ID {ModelId}", id);

                // We want to track device models to ensure proper entity sharing
                // This ensures that all devices with the same model reference the same entity
                var deviceModel = await _dbContext.DeviceModels
                    .Include(dt => dt.Manufacturer)
                    .FirstOrDefaultAsync(dt => dt.Id == id);

                if (deviceModel == null)
                    return null;

                // When we return the deviceModel, it has the Manufacturer info for display purposes
                // but isn't tracked by EF, so won't cause identity conflicts
                return deviceModel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device model with ID {ModelId}", id);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task<DeviceModel?> GetDeviceModelByIdAsync(Guid id)
        {
            return await GetByIdAsync(id);
        }

        public async Task UpdateAsync(DeviceModel deviceModel)
        {
            ArgumentNullException.ThrowIfNull(deviceModel);

            try
            {
                _logger.LogDebug("Updating device model with ID {DeviceModelId}", deviceModel.Id);

                // Check if the entity is already being tracked
                var trackedEntity = _dbContext.DeviceModels.Local.FirstOrDefault(d => d.Id == deviceModel.Id);

                if (trackedEntity != null)
                {
                    // If entity is already tracked, detach it first to avoid conflicts
                    _logger.LogDebug("Device model {DeviceModelId} is already tracked, detaching first", deviceModel.Id);
                    _dbContext.Entry(trackedEntity).State = EntityState.Detached;
                }

                // Now attach and mark as modified
                _dbContext.DeviceModels.Update(deviceModel);

                _logger.LogInformation("Device model {DeviceModelId} marked for update (not yet saved)", deviceModel.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating device model with ID {DeviceModelId}", deviceModel.Id);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task UpdateDeviceModelAsync(DeviceModel deviceModel)
        {
            await UpdateAsync(deviceModel);
            await _dbContext.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Deleting device model with ID {DeviceModelId}", id);

                // Check if the device model is already being tracked
                var trackedDeviceModel = _dbContext.ChangeTracker.Entries<DeviceModel>()
                    .FirstOrDefault(e => e.Entity.Id == id)?.Entity;

                if (trackedDeviceModel != null)
                {
                    // Use the tracked entity for deletion
                    _logger.LogDebug("Using tracked entity for deletion of device model {DeviceModelId}", id);
                    _dbContext.DeviceModels.Remove(trackedDeviceModel);
                    _logger.LogInformation("Device model {DeviceModelId} marked for deletion (not yet saved)", id);
                    return;
                }

                // If not already tracked, check if the device model exists
                var deviceModel = await _dbContext.DeviceModels
                    .FirstOrDefaultAsync(dm => dm.Id == id);

                if (deviceModel != null)
                {
                    // Remove the device model from the context
                    _dbContext.DeviceModels.Remove(deviceModel);
                    _logger.LogInformation("Device model {DeviceModelId} marked for deletion (not yet saved)", id);
                }
                else
                {
                    _logger.LogWarning("Device model with ID {DeviceModelId} not found during delete operation", id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting device model with ID {DeviceModelId}", id);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task DeleteDeviceModelAsync(Guid id)
        {
            await DeleteAsync(id);
            await _dbContext.SaveChangesAsync();
        }

        public async Task<IEnumerable<DeviceModel>> GetAllAsync()
        {
            try
            {
                _logger.LogDebug("Getting all device models");

                var deviceModels = await _dbContext.DeviceModels
                    .Include(dt => dt.Manufacturer)
                    .ToListAsync();

                return deviceModels;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all device models");
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task<IEnumerable<DeviceModel>> GetAllDeviceModelsAsync()
        {
            return await GetAllAsync();
        }

        public async Task<IEnumerable<DeviceModel>> GetDeviceModelsByManufacturerIdAsync(Guid manufacturerId)
        {
            try
            {
                _logger.LogDebug("Getting device models for manufacturer with ID {ManufacturerId}", manufacturerId);

                var deviceModels = await _dbContext.DeviceModels
                    .Include(dt => dt.Manufacturer)
                    .Where(dt => dt.ManufacturerId == manufacturerId)
                    .ToListAsync();

                return deviceModels;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device models for manufacturer with ID {ManufacturerId}", manufacturerId);
                throw;
            }
        }

        public async Task AddAsync(DeviceModel deviceModel)
        {
            ArgumentNullException.ThrowIfNull(deviceModel);

            try
            {
                _logger.LogDebug("Adding device model with ID {DeviceModelId}", deviceModel.Id);

                // Check if the device model already exists
                var existingDeviceModel = await _dbContext.DeviceModels
                    .AsNoTracking()
                    .FirstOrDefaultAsync(dm => dm.Id == deviceModel.Id);

                if (existingDeviceModel != null)
                {
                    _logger.LogWarning("Device model with ID {DeviceModelId} already exists, updating instead", deviceModel.Id);

                    // Detach any existing entity with the same ID
                    var trackedEntity = _dbContext.DeviceModels.Local.FirstOrDefault(dm => dm.Id == deviceModel.Id);
                    if (trackedEntity != null)
                    {
                        _dbContext.Entry(trackedEntity).State = EntityState.Detached;
                    }

                    // Attach and mark as modified
                    _dbContext.DeviceModels.Attach(deviceModel);
                    _dbContext.Entry(deviceModel).State = EntityState.Modified;

                    _logger.LogInformation("Device model {DeviceModelId} marked for update (not yet saved)", deviceModel.Id);
                }
                else
                {
                    // Add the new device model
                    await _dbContext.DeviceModels.AddAsync(deviceModel);
                    _logger.LogInformation("Device model {DeviceModelId} added to context (not yet saved)", deviceModel.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding device model with ID {DeviceModelId}", deviceModel.Id);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task AddDeviceModelAsync(DeviceModel deviceModel)
        {
            await AddAsync(deviceModel);
            await _dbContext.SaveChangesAsync();
        }

        public async Task<IEnumerable<DeviceModel>> GetDeviceModelsForManufacturerWithRoleAsync(Guid manufacturerId, DeviceRole deviceRole)
        {
            try
            {
                _logger.LogDebug("Getting device models for manufacturer {ManufacturerId} with role {DeviceRole}",
                    manufacturerId, deviceRole);

                var query = _dbContext.DeviceModels
                    .Include(dm => dm.Manufacturer)
                    .Where(dm => dm.ManufacturerId == manufacturerId);

                if (deviceRole == DeviceRole.Generic)
                {
                    // For Generic role, include only Generic device models
                    query = query.Where(dm => dm.DeviceRole == DeviceRole.Generic);
                }
                else
                {
                    // For specific roles, only include models with that specific role
                    // Exclude Generic device models
                    query = query.Where(dm => dm.DeviceRole == deviceRole);
                }

                var deviceModels = await query.ToListAsync();

                _logger.LogInformation("Found {Count} device models for manufacturer {ManufacturerId} with role {DeviceRole}",
                    deviceModels.Count, manufacturerId, deviceRole);

                return deviceModels;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device models for manufacturer {ManufacturerId} with role {DeviceRole}",
                    manufacturerId, deviceRole);
                throw;
            }
        }
    }
}