using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Devices.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Devices.Repositories
{
    /// <summary>
    /// Repository implementation for devices.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceRepository"/> class.
    /// </remarks>
    /// <param name="dbContext">The database context</param>
    /// <param name="logger">The logger</param>
    /// <exception cref="ArgumentNullException">Thrown if any parameter is null</exception>
    public class DeviceRepository(IBarretDbContext dbContext, ILogger<DeviceRepository> logger) : IDeviceRepository
    {
        private readonly IBarretDbContext _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        private readonly ILogger<DeviceRepository> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Gets a device by its ID.
        /// </summary>
        /// <param name="id">The ID of the device to retrieve</param>
        /// <returns>The device if found; otherwise, null</returns>
        public async Task<GenericDevice?> GetByIdAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Retrieving device with ID {DeviceId}", id);

                // Use a clean approach to fetch the device with proper inheritance
                // Let EF Core handle the discriminator and materialization
                var device = await _dbContext.Devices
                    .AsNoTracking()
                    .Include(d => d.Model!)
                        .ThenInclude(m => m.Manufacturer)
                    .AsSplitQuery()
                    .FirstOrDefaultAsync(d => d.Id == id);

                if (device == null)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found", id);
                    return null;
                }

                // Log the device role and actual type for debugging
                _logger.LogDebug("Retrieved device {DeviceId} with role {DeviceRole} and type {DeviceType}",
                    id, device.DeviceRole, device.GetType().Name);

                return device;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving device with ID {DeviceId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets a device by its ID with all its connections eagerly loaded through the vehicle.
        /// </summary>
        /// <param name="id">The ID of the device to retrieve</param>
        /// <returns>The device with connections if found; otherwise, null</returns>
        public async Task<GenericDevice?> GetWithConnectionsAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Retrieving device with connections for ID {DeviceId}", id);

                // First, get the device with basic information
                var device = await _dbContext.Devices
                    .Include(d => d.Model!)
                        .ThenInclude(m => m.Manufacturer)
                    .AsSplitQuery()
                    .FirstOrDefaultAsync(d => d.Id == id);

                if (device == null)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found", id);
                    return null;
                }

                // If the device is not associated with a vehicle, return it as is
                if (!device.VehicleId.HasValue)
                {
                    _logger.LogWarning("Device {DeviceId} is not associated with a vehicle", id);
                    return device;
                }

                // Get the vehicle to access the device connections
                var vehicle = await _dbContext.Vehicles
                    .FirstOrDefaultAsync(v => v.Id == device.VehicleId.Value);

                if (vehicle == null)
                {
                    _logger.LogWarning("Vehicle with ID {VehicleId} not found for device {DeviceId}",
                        device.VehicleId.Value, id);
                    return device;
                }

                // Load all devices for the vehicle to ensure they're available for connections
                var vehicleDevices = await _dbContext.Devices
                    .Where(d => d.VehicleId == vehicle.Id)
                    .ToListAsync();

                // Add devices to the vehicle
                foreach (var vehicleDevice in vehicleDevices)
                {
                    vehicle.AddDevice(vehicleDevice);
                }

                // Get the connections for the device from the vehicle
                var connections = vehicle.GetDeviceConnections(id);

                _logger.LogInformation("Retrieved device {DeviceId} with {ConnectionCount} connections",
                    id, connections.Count());

                return device;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving device with connections for ID {DeviceId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets all devices.
        /// </summary>
        /// <returns>A collection of all devices</returns>
        public async Task<IEnumerable<GenericDevice>> GetAllAsync()
        {
            try
            {
                _logger.LogDebug("Retrieving all devices");

                // Simplified approach - let EF Core handle the type hierarchy
                var devices = await _dbContext.Devices
                    .AsNoTracking()
                    .Include(d => d.Model!)
                        .ThenInclude(m => m.Manufacturer)
                    .AsSplitQuery()
                    .ToListAsync();

                _logger.LogInformation("Successfully retrieved {DeviceCount} devices", devices.Count);

                // Log device types for debugging
                var deviceTypeGroups = devices.GroupBy(d => d.GetType().Name)
                    .Select(g => new { Type = g.Key, Count = g.Count() });

                foreach (var group in deviceTypeGroups)
                {
                    _logger.LogDebug("Retrieved {Count} devices of type {DeviceType}",
                        group.Count, group.Type);
                }

                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all devices");
                throw;
            }
        }

        /// <summary>
        /// Gets devices by their IDs.
        /// </summary>
        /// <param name="ids">The IDs of the devices to retrieve</param>
        /// <returns>A list of devices with the specified IDs</returns>
        public async Task<List<GenericDevice>> GetDevicesByIdsAsync(List<Guid> ids)
        {
            try
            {
                _logger.LogDebug("Retrieving devices with IDs: {DeviceIds}", string.Join(", ", ids));

                var devices = await _dbContext.Devices
                    .AsNoTracking()
                    .Where(d => ids.Contains(d.Id))
                    .Include(d => d.Model!)
                        .ThenInclude(m => m.Manufacturer)
                    .AsSplitQuery()
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} devices out of {RequestedCount} requested IDs",
                    devices.Count, ids.Count);

                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving devices by IDs");
                throw;
            }
        }

        /// <summary>
        /// Gets devices for a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle</param>
        /// <returns>A list of devices for the vehicle</returns>
        public async Task<List<GenericDevice>> GetDevicesForVehicleAsync(Guid vehicleId)
        {
            try
            {
                _logger.LogDebug("Retrieving devices for vehicle with ID {VehicleId}", vehicleId);

                var devices = await _dbContext.Devices
                    .AsNoTracking()
                    .Where(d => d.VehicleId == vehicleId)
                    .Include(d => d.Model!)
                        .ThenInclude(m => m.Manufacturer)
                    .AsSplitQuery()
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} devices for vehicle {VehicleId}",
                    devices.Count, vehicleId);

                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving devices for vehicle with ID {VehicleId}", vehicleId);
                throw;
            }
        }

        /// <summary>
        /// Gets devices by role.
        /// </summary>
        /// <param name="role">The role of the devices to retrieve</param>
        /// <returns>A list of devices with the specified role</returns>
        public async Task<List<GenericDevice>> GetDevicesByRoleAsync(DeviceRole role)
        {
            try
            {
                _logger.LogDebug("Retrieving devices with role {DeviceRole}", role);

                var devices = await _dbContext.Devices
                    .AsNoTracking()
                    .Where(d => d.DeviceRole == role)
                    .Include(d => d.Model!)
                        .ThenInclude(m => m.Manufacturer)
                    .AsSplitQuery()
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} devices with role {DeviceRole}",
                    devices.Count, role);

                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving devices with role {DeviceRole}", role);
                throw;
            }
        }

        public async Task AddAsync(GenericDevice device)
        {
            ArgumentNullException.ThrowIfNull(device);

            try
            {
                _logger.LogDebug("Adding device with ID {DeviceId}", device.Id);

                // Validate that the device has a VehicleId
                if (!device.VehicleId.HasValue)
                {
                    throw new InvalidOperationException($"Device {device.Id} must be associated with a vehicle before adding to the repository.");
                }

                // Add the device to the context
                await _dbContext.Devices.AddAsync(device);

                // Log the connection details for debugging
                _logger.LogDebug("Connection details for device {DeviceId}: {Connection}",
                    device.Id, device.Connection?.ToString() ?? "None");

                _logger.LogInformation("Successfully added device {DeviceId} to context (not yet saved)", device.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding device with ID {DeviceId}", device.Id);
                throw;
            }
        }

        public void Update(GenericDevice device)
        {
            ArgumentNullException.ThrowIfNull(device);

            try
            {
                _logger.LogDebug("Updating device with ID {DeviceId}", device.Id);

                // Validate that the device has a VehicleId
                if (!device.VehicleId.HasValue)
                {
                    throw new InvalidOperationException($"Device {device.Id} must be associated with a vehicle before updating in the repository.");
                }

                // Mark the entity as modified
                _dbContext.Entry(device).State = EntityState.Modified;

                // Log the connection details for debugging
                _logger.LogDebug("Connection details for device {DeviceId}: {Connection}",
                    device.Id, device.Connection?.ToString() ?? "None");

                _logger.LogInformation("Device with ID {DeviceId} marked as modified (not yet saved)", device.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating device with ID {DeviceId}", device.Id);
                throw;
            }
        }

        /// <summary>
        /// Updates a device in the repository asynchronously.
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <remarks>
        /// This method does not call SaveChangesAsync.
        /// </remarks>
        public async Task UpdateAsync(GenericDevice device)
        {
            ArgumentNullException.ThrowIfNull(device);

            try
            {
                _logger.LogDebug("Updating device with ID {DeviceId} asynchronously", device.Id);

                // For now, this just calls the synchronous method
                // In the future, if there's async work to be done, it can be implemented here
                Update(device);

                await Task.CompletedTask; // To make it truly async

                _logger.LogInformation("Device with ID {DeviceId} marked as modified asynchronously (not yet saved)", device.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating device with ID {DeviceId} asynchronously", device.Id);
                throw;
            }
        }

        public async Task DeleteAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Deleting device with ID {DeviceId}", id);

                var device = await _dbContext.Devices.FindAsync(id);
                if (device == null)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found during delete operation", id);
                    throw new KeyNotFoundException($"Device with ID {id} not found");
                }

                _dbContext.Devices.Remove(device);
                _logger.LogDebug("Device with ID {DeviceId} marked for deletion (not yet saved)", id);
            }
            catch (Exception ex) when (ex is not KeyNotFoundException)
            {
                _logger.LogError(ex, "Error deleting device with ID {DeviceId}", id);
                throw;
            }
        }
        public async Task<bool> ExistsAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Checking if device with ID {DeviceId} exists", id);
                var exists = await _dbContext.Devices.AnyAsync(d => d.Id == id);
                _logger.LogDebug("Device with ID {DeviceId} {Exists}", id, exists ? "exists" : "does not exist");
                return exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if device with ID {DeviceId} exists", id);
                throw;
            }
        }

        /// <summary>
        /// Gets devices by device model ID.
        /// </summary>
        /// <param name="deviceModelId">The ID of the device model</param>
        /// <returns>A list of devices with the specified device model</returns>
        public async Task<List<GenericDevice>> GetDevicesByModelIdAsync(Guid deviceModelId)
        {
            try
            {
                _logger.LogDebug("Retrieving devices with model ID {DeviceModelId}", deviceModelId);

                var devices = await _dbContext.Devices
                    .Where(d => d.DeviceModelId == deviceModelId)
                    .Include(d => d.Model!)
                        .ThenInclude(m => m.Manufacturer)
                    .AsSplitQuery()
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} devices with model ID {DeviceModelId}",
                    devices.Count, deviceModelId);

                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving devices with model ID {DeviceModelId}", deviceModelId);
                throw;
            }
        }
    }
}
