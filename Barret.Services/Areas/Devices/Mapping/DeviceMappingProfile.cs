using AutoMapper;
using Barret.Core.Areas.Common.ValueObjects;
using Barret.Core.Areas.Devices.Models.Alarms;
using Barret.Core.Areas.Devices.Models.Antennas;
using Barret.Core.Areas.Devices.Models.Audio;
using Barret.Core.Areas.Devices.Models.Autopilots;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.Engines;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Network;
using Barret.Core.Areas.Devices.Models.NVR;
using Barret.Core.Areas.Devices.Models.Radars;
using Barret.Core.Areas.Devices.Models.Radios;
using Barret.Core.Areas.Devices.Models.Sensors;
using Barret.Core.Areas.Devices.Models.Thrusters;
using Barret.Core.Areas.Devices.Models.Trackpilots;
using Barret.Core.Areas.Devices.Models.VCS;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Devices.Alarms;
using Barret.Shared.DTOs.Devices.Antennas;
using Barret.Shared.DTOs.Devices.Audio;
using Barret.Shared.DTOs.Devices.Autopilots;
using Barret.Shared.DTOs.Devices.Cameras;
using Barret.Shared.DTOs.Devices.Engines;
using Barret.Shared.DTOs.Devices.Horns;
using Barret.Shared.DTOs.Devices.Lights;
using Barret.Shared.DTOs.Devices.Network;
using Barret.Shared.DTOs.Devices.NVR;
using Barret.Shared.DTOs.Devices.Radars;
using Barret.Shared.DTOs.Devices.Radios;
using Barret.Shared.DTOs.Devices.Rudders;
using Barret.Shared.DTOs.Devices.Sensors;
using Barret.Shared.DTOs.Devices.Thrusters;
using Barret.Shared.DTOs.Devices.Trackpilots;
using Barret.Shared.DTOs.Devices.VCS;

namespace Barret.Services.Areas.Devices.Mapping
{
    /// <summary>
    /// AutoMapper profile for mapping between Device entities and DTOs
    /// </summary>
    public class DeviceMappingProfile : Profile
    {
        public DeviceMappingProfile()
        {
            // Base device mapping
            CreateMap<GenericDevice, DeviceDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.DeviceRole, opt => opt.MapFrom(src => src.DeviceRole))
                .ForMember(dest => dest.DeviceModelId, opt => opt.MapFrom(src => src.DeviceModelId))
                .ForMember(dest => dest.ModelName, opt => opt.MapFrom(src => src.Model != null ? src.Model.Name : null))
                .ForMember(dest => dest.ManufacturerId, opt => opt.MapFrom(src => src.Model != null ? src.Model.ManufacturerId : (Guid?)null))
                .ForMember(dest => dest.ManufacturerName, opt => opt.MapFrom(src => src.Model != null && src.Model.Manufacturer != null ? src.Model.Manufacturer.Name : null))
                .ForMember(dest => dest.Position, opt => opt.MapFrom(src => src.Position != null ? new RelativePositionDto
                {
                    X = src.Position.X,
                    Y = src.Position.Y,
                    Z = src.Position.Z
                } : new RelativePositionDto()))
                .ForMember(dest => dest.Connection, opt => opt.MapFrom(src => src.Connection != null ? new ConnectionHandlerDto
                {
                    IPAddress = src.Connection.IPAddress,
                    Port = src.Connection.Port,
                    Protocol = src.Connection.Protocol
                } : null))
                .ForMember(dest => dest.Connections, opt => opt.Ignore())
                .ForMember(dest => dest.Alarms, opt => opt.MapFrom(src => src.Alarms));

            // Camera mapping
            CreateMap<Camera, CameraDto>()
                .IncludeBase<GenericDevice, DeviceDto>()
                .ForMember(dest => dest.ShowVideo, opt => opt.MapFrom(src => src.ShowVideo));

            // NVR mappings
            CreateMap<NVRScreen, NVRScreenDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<NVRRecording, NVRRecordingDto>()
                .IncludeBase<GenericDevice, DeviceDto>();

            // Propulsion mappings
            CreateMap<Engine, EngineDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<Thruster, ThrusterDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            // Rudder mapping is commented out until we have the domain class
            // CreateMap<Rudder, RudderDto>()
            //     .IncludeBase<GenericDevice, DeviceDto>();

            // Navigation mappings
            CreateMap<Radar, RadarDto>()
                .IncludeBase<GenericDevice, DeviceDto>()
                .ForMember(dest => dest.MaritimePosition, opt => opt.MapFrom(src => src.MaritimePosition));
            CreateMap<Antenna, AntennaDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<Autopilot, AutopilotDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<Trackpilot, TrackpilotDto>()
                .IncludeBase<GenericDevice, DeviceDto>();

            // Radio mappings
            CreateMap<VHFMariphone, VHFMariphoneDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<VHFNetworkInterface, VHFNetworkInterfaceDto>()
                .IncludeBase<GenericDevice, DeviceDto>();

            // Audio mappings
            CreateMap<AudioHub, AudioHubDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<PAAudio, PAAudioDto>()
                .IncludeBase<GenericDevice, DeviceDto>();

            // Note: AMP and SPAP are in the Audio namespace in the domain model
            // but in the VCS namespace in the DTO model
            CreateMap<AMP, Shared.DTOs.Devices.VCS.AMPDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<SPAP, Shared.DTOs.Devices.VCS.SPAPDto>()
                .IncludeBase<GenericDevice, DeviceDto>();

            // VCS mappings
            CreateMap<HMI, HMIDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<CabinetReadoutIO, CabinetReadoutIODto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<OperatorPanelIO, OperatorPanelIODto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<GPU, GPUDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<SafetySystemHead, SafetySystemHeadDto>()
                .IncludeBase<GenericDevice, DeviceDto>();

            // Network mappings
            CreateMap<Firewall, FirewallDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<Gateway, GatewayDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<Switch, SwitchDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<Plc, PlcDto>()
                .IncludeBase<GenericDevice, DeviceDto>();

            // Sensor mappings
            CreateMap<NavData, NavDataDto>()
                .IncludeBase<GenericDevice, DeviceDto>();
            CreateMap<Sensor, SensorDto>()
                .IncludeBase<GenericDevice, DeviceDto>();

            // Other mappings
            // Light and Horn mappings are commented out until we have the domain classes
            // CreateMap<Light, LightDto>()
            //     .IncludeBase<GenericDevice, DeviceDto>();
            // CreateMap<Horn, HornDto>()
            //     .IncludeBase<GenericDevice, DeviceDto>();

            // Value object mappings
            CreateMap<RelativePosition, RelativePositionDto>()
                .ForMember(dest => dest.X, opt => opt.MapFrom(src => src.X))
                .ForMember(dest => dest.Y, opt => opt.MapFrom(src => src.Y))
                .ForMember(dest => dest.Z, opt => opt.MapFrom(src => src.Z));

            CreateMap<Position, MaritimePositionDto>()
                .ForMember(dest => dest.ForeAft, opt => opt.MapFrom(src => src.ForeAft))
                .ForMember(dest => dest.Lateral, opt => opt.MapFrom(src => src.Lateral))
                .ForMember(dest => dest.Facing, opt => opt.MapFrom(src => src.Facing));

            CreateMap<ConnectionHandler, ConnectionHandlerDto>()
                .ForMember(dest => dest.IPAddress, opt => opt.MapFrom(src => src.IPAddress))
                .ForMember(dest => dest.Port, opt => opt.MapFrom(src => src.Port))
                .ForMember(dest => dest.Protocol, opt => opt.MapFrom(src => src.Protocol));

            CreateMap<Alarm, AlarmDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(src => src.Description))
                .ForMember(dest => dest.NotificationType, opt => opt.MapFrom(src => src.NotificationType))
                .ForMember(dest => dest.Message, opt => opt.MapFrom(src => src.Message))
                .ForMember(dest => dest.NotificationGroupId, opt => opt.MapFrom(src => src.NotificationGroupId))
                .ForMember(dest => dest.EntityId, opt => opt.MapFrom(src => src.EntityId))
                .ForMember(dest => dest.WarningId, opt => opt.MapFrom(src => src.WarningId));
        }
    }
}
