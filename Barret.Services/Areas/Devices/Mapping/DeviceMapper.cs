using AutoMapper;
using Barret.Core.Areas.Common.ValueObjects;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.Alarms;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Services.Core.Areas.Devices.Factories;
using Barret.Services.Core.Areas.Devices.Mapping;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Devices.Cameras;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Devices.Mapping
{
    /// <summary>
    /// Maps between Device entities and DeviceDtos with proper domain logic
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the DeviceMapper class
    /// </remarks>
    /// <param name="logger">The logger</param>
    /// <param name="deviceFactory">The device factory</param>
    /// <param name="mapper">The AutoMapper instance</param>
    public class DeviceMapper(
        ILogger<DeviceMapper> logger,
        IDeviceFactory deviceFactory,
        IMapper mapper) : IDeviceMapper
    {
        private readonly ILogger<DeviceMapper> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly IDeviceFactory _deviceFactory = deviceFactory ?? throw new ArgumentNullException(nameof(deviceFactory));
        private readonly IMapper _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));

        /// <summary>
        /// Maps a device DTO to an entity
        /// </summary>
        /// <param name="dto">The device DTO to map</param>
        /// <param name="existingEntity">Optional existing entity to update</param>
        /// <returns>A device entity</returns>
        public GenericDevice ToEntity(DeviceDto dto, GenericDevice existingEntity = null)
        {
            if (dto == null)
            {
                return null;
            }

            _logger.LogDebug("Mapping DTO to {DeviceRole} entity", dto.DeviceRole);

            GenericDevice device;

            if (existingEntity != null)
            {
                // Update existing entity
                device = existingEntity;
                UpdateEntityFromDto(device, dto);
            }
            else
            {
                // Create new entity
                device = _deviceFactory.CreateDevice(dto.DeviceRole, dto.Name);

                // Set the ID if provided
                if (dto.Id != Guid.Empty)
                {
                    // Use reflection to set the ID since it's normally read-only
                    var idProperty = device.GetType().GetProperty("Id");
                    if (idProperty != null && idProperty.CanWrite)
                    {
                        idProperty.SetValue(device, dto.Id);
                    }
                }

                // Update other properties
                UpdateEntityFromDto(device, dto);

                // Note: Device connections are now handled at the Vehicle/Vessel level, not here
            }

            return device;
        }

        /// <summary>
        /// Maps a device entity to a DTO
        /// </summary>
        /// <param name="entity">The device entity to map</param>
        /// <returns>A device DTO</returns>
        public DeviceDto ToDto(GenericDevice entity)
        {
            if (entity == null)
            {
                return null;
            }

            _logger.LogTrace("Mapping {DeviceType} with ID {DeviceId} to DTO", entity.GetType().Name, entity.Id);

            // Map to the correct DTO type based on the device role
            DeviceDto deviceDto;

            // Special handling for Camera to log ShowVideo property
            if (entity.DeviceRole == DeviceRole.Camera)
            {
                var camera = entity as Camera;
                _logger.LogTrace("[DeviceMapper.ToDto] Mapping Camera {CameraId} with ShowVideo = {ShowVideo}",
                    entity.Id, camera?.ShowVideo);
                var dto = _mapper.Map<Shared.DTOs.Devices.Cameras.CameraDto>(entity);
                _logger.LogTrace("[DeviceMapper.ToDto] Mapped to CameraDto with ShowVideo = {ShowVideo}",
                    dto?.ShowVideo);
                deviceDto = dto;
            }
            else
            {
                // Use switch expression for other device types
                deviceDto = entity.DeviceRole switch
                {
                    // NVR
                    DeviceRole.NVRScreen => _mapper.Map<Shared.DTOs.Devices.NVR.NVRScreenDto>(entity),
                    DeviceRole.NVRRecording => _mapper.Map<Shared.DTOs.Devices.NVR.NVRRecordingDto>(entity),
                    DeviceRole.Framegrabber => _mapper.Map<Shared.DTOs.Devices.Radars.RadarDto>(entity),

                    // Propulsion
                    DeviceRole.Engine => _mapper.Map<Shared.DTOs.Devices.Engines.EngineDto>(entity),
                    DeviceRole.Thruster => _mapper.Map<Shared.DTOs.Devices.Thrusters.ThrusterDto>(entity),

                    // Navigation
                    DeviceRole.Radar => _mapper.Map<Shared.DTOs.Devices.Radars.RadarDto>(entity),
                    DeviceRole.Antenna => _mapper.Map<Shared.DTOs.Devices.Antennas.AntennaDto>(entity),
                    DeviceRole.Autopilot => _mapper.Map<Shared.DTOs.Devices.Autopilots.AutopilotDto>(entity),
                    DeviceRole.Trackpilot => _mapper.Map<Shared.DTOs.Devices.Trackpilots.TrackpilotDto>(entity),

                    // Radios
                    DeviceRole.VHFMariphone => _mapper.Map<Shared.DTOs.Devices.Radios.VHFMariphoneDto>(entity),
                    DeviceRole.VHFNetworkInterface => _mapper.Map<Shared.DTOs.Devices.Radios.VHFNetworkInterfaceDto>(entity),

                    // Audio
                    DeviceRole.AudioHub => _mapper.Map<Shared.DTOs.Devices.Audio.AudioHubDto>(entity),
                    DeviceRole.PAAudio => _mapper.Map<Shared.DTOs.Devices.Audio.PAAudioDto>(entity),
                    DeviceRole.AMP => _mapper.Map<Shared.DTOs.Devices.VCS.AMPDto>(entity),
                    DeviceRole.SPAP => _mapper.Map<Shared.DTOs.Devices.VCS.SPAPDto>(entity),

                    // VCS
                    DeviceRole.HMI => _mapper.Map<Shared.DTOs.Devices.VCS.HMIDto>(entity),
                    DeviceRole.CabinetReadoutIO => _mapper.Map<Shared.DTOs.Devices.VCS.CabinetReadoutIODto>(entity),
                    DeviceRole.OperatorPanelIO => _mapper.Map<Shared.DTOs.Devices.VCS.OperatorPanelIODto>(entity),
                    DeviceRole.GPU => _mapper.Map<Shared.DTOs.Devices.VCS.GPUDto>(entity),
                    DeviceRole.SafetySystemHead => _mapper.Map<Shared.DTOs.Devices.VCS.SafetySystemHeadDto>(entity),

                    // Network
                    DeviceRole.Firewall => _mapper.Map<Shared.DTOs.Devices.Network.FirewallDto>(entity),
                    DeviceRole.Gateway => _mapper.Map<Shared.DTOs.Devices.Network.GatewayDto>(entity),
                    DeviceRole.Switch => _mapper.Map<Shared.DTOs.Devices.Network.SwitchDto>(entity),
                    DeviceRole.Plc => _mapper.Map<Shared.DTOs.Devices.Network.PlcDto>(entity),

                    // Sensors
                    DeviceRole.NavData => _mapper.Map<Shared.DTOs.Devices.Sensors.NavDataDto>(entity),
                    DeviceRole.Sensor => _mapper.Map<Shared.DTOs.Devices.Sensors.SensorDto>(entity),

                    // Other devices
                    DeviceRole.Light => _mapper.Map<DeviceDto>(entity),
                    DeviceRole.Horn => _mapper.Map<DeviceDto>(entity),
                    DeviceRole.Rudder => _mapper.Map<DeviceDto>(entity),

                    // Default fallback
                    _ => _mapper.Map<DeviceDto>(entity),
                };
            }

            // Ensure the device role is correctly set in the DTO
            // TODO: PROBABLY UNNECESARRY
            deviceDto.DeviceRole = entity.DeviceRole;

            // Initialize the Connections collection
            deviceDto.Connections = new List<DeviceConnectionDto>();

            // Ensure manufacturer and model names are correctly populated
            if (entity.DeviceModelId.HasValue && entity.Model != null)
            {
                deviceDto.DeviceModelId = entity.DeviceModelId;
                deviceDto.ModelName = entity.Model.Name;

                if (entity.Model.Manufacturer != null)
                {
                    deviceDto.ManufacturerId = entity.Model.ManufacturerId;
                    deviceDto.ManufacturerName = entity.Model.Manufacturer.Name;
                }
            }

            _logger.LogTrace("Successfully mapped device {DeviceId} to {DtoType}", entity.Id, deviceDto.GetType().Name);

            return deviceDto;
        }

        /// <summary>
        /// Updates an existing entity with data from a DTO
        /// </summary>
        /// <param name="device">The entity to update</param>
        /// <param name="dto">The DTO containing the data</param>
        public void UpdateEntityFromDto(GenericDevice device, DeviceDto dto)
        {
            ArgumentNullException.ThrowIfNull(device);

            ArgumentNullException.ThrowIfNull(dto);

            _logger.LogDebug("Updating {DeviceType} from DeviceDto", device.GetType().Name);

            // Handle Camera-specific properties
            if (device is Camera camera && dto is CameraDto cameraDto)
            {
                _logger.LogInformation("[DeviceMapper] Updating Camera {CameraId} ShowVideo from {OldValue} to {NewValue}",
                    camera.Id, camera.ShowVideo, cameraDto.ShowVideo);

                // Force the ShowVideo property to match the DTO
                camera.ShowVideo = cameraDto.ShowVideo;

                // Verify the update was successful
                _logger.LogInformation("[DeviceMapper] After update: Camera {CameraId} ShowVideo = {ShowVideo}",
                    camera.Id, camera.ShowVideo);

                // Double-check that the value was actually set
                if (camera.ShowVideo != cameraDto.ShowVideo)
                {
                    _logger.LogWarning("[DeviceMapper] ShowVideo property mismatch after update! Expected: {Expected}, Actual: {Actual}",
                        cameraDto.ShowVideo, camera.ShowVideo);
                    // Force it again
                    camera.ShowVideo = cameraDto.ShowVideo;
                    _logger.LogInformation("[DeviceMapper] Forced update: Camera {CameraId} ShowVideo = {ShowVideo}",
                        camera.Id, camera.ShowVideo);
                }
            }

            // Update basic properties
            if (!string.IsNullOrEmpty(dto.Name))
            {
                device.Name = dto.Name;
            }

            // Update device model ID if provided
            if (dto.DeviceModelId.HasValue)
            {
                device.SetDeviceModelId(dto.DeviceModelId);
                _logger.LogDebug("Set device model ID to {DeviceModelId}", dto.DeviceModelId);

                // If we have model name information in the DTO, store it for later use
                // This is important when the model object isn't loaded but we have the name
                if (!string.IsNullOrEmpty(dto.ModelName))
                {
                    _logger.LogDebug("Storing model name {ModelName} for device {DeviceId}", dto.ModelName, device.Id);
                }

                // Same for manufacturer name
                if (!string.IsNullOrEmpty(dto.ManufacturerName))
                {
                    _logger.LogDebug("Storing manufacturer name {ManufacturerName} for device {DeviceId}", dto.ManufacturerName, device.Id);
                }
            }
            else if (dto.DeviceModelId == null && device.DeviceModelId.HasValue)
            {
                // Clear device model if explicitly set to null
                device.SetDeviceModelId(null);
                _logger.LogDebug("Cleared device model ID");
            }

            // Position is immutable, so we need to create a new one using the correct namespace
            if (dto.Position != null)
            {
                device.WithPosition(new RelativePosition(
                    dto.Position.X,
                    dto.Position.Y,
                    dto.Position.Z
                ));
            }

            // Handle connection updates
            if (dto.Connection != null)
            {
                // Check if the connection has valid data
                if (!string.IsNullOrWhiteSpace(dto.Connection.IPAddress) &&
                    dto.Connection.Port > 0 &&
                    dto.Connection.Protocol != Protocol.Undefined)
                {
                    // Create a new connection handler (it's an immutable value object)
                    device.WithConnection(new ConnectionHandler(
                        dto.Connection.IPAddress,
                        dto.Connection.Port,
                        dto.Connection.Protocol
                    ));
                }
                else if (dto.Connection.IPAddress == null && dto.Connection.Port == 0)
                {
                    // If the connection data is empty, remove the connection
                    device.RemoveConnection();
                    _logger.LogDebug("Removed connection from device {DeviceId}", device.Id);
                }
            }

            // Handle alarm updates using the proper WithAlarms pattern
            if (dto.Alarms != null)
            {
                _logger.LogDebug("Updating {AlarmCount} alarms for device {DeviceId}", dto.Alarms.Count, device.Id);

                // Create new immutable alarm instances from DTOs
                var newAlarms = new List<Alarm>();

                foreach (var alarmDto in dto.Alarms)
                {
                    // Skip alarms with undefined notification types to prevent validation errors
                    if (alarmDto.NotificationType == NotificationType.Undefined)
                    {
                        _logger.LogWarning("Skipping alarm '{Description}' with undefined notification type for device {DeviceId}",
                            alarmDto.Description, device.Id);
                        continue;
                    }

                    // Create alarm instance - use existing ID if provided, otherwise create new
                    var alarm = alarmDto.Id == Guid.Empty
                        ? new Alarm(alarmDto.Description, alarmDto.NotificationType, alarmDto.Message,
                                   alarmDto.NotificationGroupId, alarmDto.EntityId, alarmDto.WarningId)
                        : new Alarm(alarmDto.Id, alarmDto.Description, alarmDto.NotificationType, alarmDto.Message,
                                   alarmDto.NotificationGroupId, alarmDto.EntityId, alarmDto.WarningId);

                    newAlarms.Add(alarm);
                }

                // Use the proper WithAlarms pattern following established conventions
                device.WithAlarms(newAlarms);

                _logger.LogDebug("Successfully updated {AlarmCount} alarms for device {DeviceId}", newAlarms.Count, device.Id);
            }

            // Note: Specific device properties would need to be handled
            // by type-specific mapper extensions
        }
    }
}
