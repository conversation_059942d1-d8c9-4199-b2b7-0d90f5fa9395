using Barret.Core.Areas.Devices.Enums;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Devices.Mapping;
using Barret.Services.Core.Areas.Devices.Queries;
using Barret.Services.Core.Areas.Devices.Repositories;
using Barret.Services.Core.Areas.Vehicles.Factories;
using Barret.Shared.DTOs.Devices;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Devices.Queries
{
    /// <summary>
    /// Query service implementation for devices.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceQueryService"/> class.
    /// </remarks>
    /// <param name="deviceRepository">The device repository</param>
    /// <param name="deviceMapper">The device mapper for converting between device entities and DTOs</param>
    /// <param name="logger">The logger</param>
    /// <exception cref="ArgumentNullException">Thrown if any parameter is null</exception>
    public class DeviceQueryService(
        IDeviceRepository deviceRepository,
        IDeviceMapper deviceMapper,
        IVehicleFactory vehicleFactory,
        ILogger<DeviceQueryService> logger) : IDeviceQueryService
    {
        private readonly IDeviceRepository _deviceRepository = deviceRepository ?? throw new ArgumentNullException(nameof(deviceRepository));
        private readonly IDeviceMapper _deviceMapper = deviceMapper ?? throw new ArgumentNullException(nameof(deviceMapper));
        private readonly IVehicleFactory _vehicleFactory = vehicleFactory ?? throw new ArgumentNullException(nameof(vehicleFactory));
        private readonly ILogger<DeviceQueryService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Gets a device with its connections.
        /// </summary>
        /// <param name="id">The ID of the device to retrieve</param>
        /// <returns>The device DTO with its connections if found; otherwise, null</returns>
        public async Task<DeviceDto?> GetDeviceWithConnectionsAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Getting device with connections for ID {DeviceId}", id.ToString());

                // Get device with connections
                var device = await _deviceRepository.GetWithConnectionsAsync(id);
                if (device == null)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found", id.ToString());
                    return null;
                }

                // Map to DTO
                var deviceDto = _deviceMapper.ToDto(device);

                // If the device is associated with a vehicle, get its connections
                if (device.VehicleId.HasValue)
                {
                    // Get the connections for this device
                    var connections = await GetDeviceConnectionsAsync(id);
                    deviceDto.Connections = connections ?? [];

                    _logger.LogDebug("Added {ConnectionCount} connections to device {DeviceId}",
                        connections?.Count ?? 0, id.ToString());
                }

                _logger.LogInformation("Successfully retrieved device {DeviceId} with connections", id.ToString());

                return deviceDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device with connections for ID {DeviceId}", id.ToString());
                throw;
            }
        }

        /// <summary>
        /// Gets devices by role.
        /// </summary>
        /// <param name="role">The role of the devices to retrieve</param>
        /// <returns>A list of device DTOs with the specified role</returns>
        public async Task<List<DeviceDto>> GetDevicesByRoleAsync(DeviceRole role)
        {
            try
            {
                _logger.LogDebug("Getting devices with role {DeviceRole}", role);

                // Get devices by role
                var devices = await _deviceRepository.GetDevicesByRoleAsync(role);

                // Map to DTOs
                var deviceDtos = devices.Select(_deviceMapper.ToDto).ToList();

                _logger.LogInformation("Successfully retrieved {Count} devices with role {DeviceRole}",
                    deviceDtos.Count, role);

                return deviceDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting devices with role {DeviceRole}", role);
                throw;
            }
        }

        /// <summary>
        /// Gets devices for a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle</param>
        /// <returns>A list of device DTOs for the vehicle</returns>
        public async Task<List<DeviceDto>> GetDevicesForVehicleAsync(Guid vehicleId)
        {
            try
            {
                _logger.LogDebug("Getting devices for vehicle {VehicleId}", vehicleId);

                // Get devices for vehicle
                var devices = await _deviceRepository.GetDevicesForVehicleAsync(vehicleId);

                // Map to DTOs
                var deviceDtos = devices.Select(_deviceMapper.ToDto).ToList();

                _logger.LogInformation("Successfully retrieved {Count} devices for vehicle {VehicleId}",
                    deviceDtos.Count, vehicleId);

                return deviceDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting devices for vehicle {VehicleId}", vehicleId);
                throw;
            }
        }

        /// <summary>
        /// Gets a device by its ID.
        /// </summary>
        /// <param name="id">The ID of the device to retrieve</param>
        /// <returns>The device DTO if found; otherwise, null</returns>
        public async Task<DeviceDto?> GetDeviceByIdAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Getting device with ID {DeviceId}", id);

                // Get device
                var device = await _deviceRepository.GetByIdAsync(id);
                if (device == null)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found", id);
                    return null;
                }

                // Map to DTO
                var deviceDto = _deviceMapper.ToDto(device);

                _logger.LogInformation("Successfully retrieved device {DeviceId}", id);

                return deviceDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device with ID {DeviceId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets all devices.
        /// </summary>
        /// <returns>A list of all device DTOs</returns>
        public async Task<List<DeviceDto>> GetAllDevicesAsync()
        {
            try
            {
                _logger.LogDebug("Getting all devices");

                // Get all devices
                var devices = await _deviceRepository.GetAllAsync();

                // Map to DTOs
                var deviceDtos = devices.Select(_deviceMapper.ToDto).ToList();

                _logger.LogInformation("Successfully retrieved {Count} devices", deviceDtos.Count);

                return deviceDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all devices");
                throw;
            }
        }

        /// <summary>
        /// Gets all connected devices for a device.
        /// </summary>
        /// <param name="deviceId">The ID of the device</param>
        /// <returns>A list of device DTOs that are connected to the specified device</returns>
        public async Task<List<DeviceDto>> GetConnectedDevicesAsync(Guid deviceId)
        {
            try
            {
                _logger.LogDebug("Getting connected devices for device {DeviceId}", deviceId.ToString());

                // Get device with connections
                var device = await _deviceRepository.GetWithConnectionsAsync(deviceId);
                if (device == null || !device.VehicleId.HasValue)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found or not associated with a vehicle", deviceId.ToString());
                    return [];
                }

                // Get the vehicle ID
                var vehicleId = device.VehicleId.Value;

                // Get all devices for the vehicle
                var allDevices = await _deviceRepository.GetDevicesForVehicleAsync(vehicleId);
                if (!allDevices.Any())
                {
                    _logger.LogWarning("No devices found for vehicle {VehicleId}", vehicleId.ToString());
                    return [];
                }

                // Create a temporary vehicle to access the connections
                // This is a read-only operation, so we're not modifying any data
                var vehicle = _vehicleFactory.CreateVesselWithDevices(vehicleId, allDevices);

                // Get device connections
                var connections = vehicle.GetDeviceConnections(deviceId).ToList();
                if (!connections.Any())
                {
                    _logger.LogInformation("No connections found for device {DeviceId}", deviceId.ToString());
                    return [];
                }

                // Get the IDs of all connected devices
                var connectedDeviceIds = connections
                    .Select(c => c.ConnectedDeviceId == deviceId ? c.InterfaceDeviceId : c.ConnectedDeviceId)
                    .Distinct()
                    .ToList();

                // Get all devices for the vehicle
                var allVehicleDevices = await _deviceRepository.GetDevicesForVehicleAsync(device.VehicleId.Value);

                // Filter to only the connected devices
                var connectedDevices = allVehicleDevices
                    .Where(d => connectedDeviceIds.Contains(d.Id))
                    .ToList();

                // Map to DTOs
                var connectedDeviceDtos = connectedDevices.Select(_deviceMapper.ToDto).ToList();

                _logger.LogInformation("Successfully retrieved {Count} connected devices for device {DeviceId}",
                    connectedDeviceDtos.Count, deviceId.ToString());

                return connectedDeviceDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting connected devices for device {DeviceId}", deviceId.ToString());
                throw;
            }
        }

        /// <summary>
        /// Gets all device connections for a device.
        /// </summary>
        /// <param name="deviceId">The ID of the device</param>
        /// <returns>A list of device connection DTOs for the device</returns>
        public async Task<List<DeviceConnectionDto>> GetDeviceConnectionsAsync(Guid deviceId)
        {
            try
            {
                _logger.LogDebug("Getting device connections for device {DeviceId}", deviceId.ToString());

                // Get device with connections
                var device = await _deviceRepository.GetWithConnectionsAsync(deviceId);
                if (device == null || !device.VehicleId.HasValue)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found or not associated with a vehicle", deviceId.ToString());
                    return [];
                }

                // Get the vehicle ID
                var vehicleId = device.VehicleId.Value;

                // Get all devices for the vehicle
                var allDevices = await _deviceRepository.GetDevicesForVehicleAsync(vehicleId);
                if (!allDevices.Any())
                {
                    _logger.LogWarning("No devices found for vehicle {VehicleId}", vehicleId.ToString());
                    return [];
                }

                // Create a temporary vehicle to access the connections
                // This is a read-only operation, so we're not modifying any data
                var vehicle = _vehicleFactory.CreateVesselWithDevices(vehicleId, allDevices);

                // Get device connections from the vehicle
                var connections = vehicle.GetDeviceConnections(deviceId).ToList();

                // Map to DTOs
                var connectionDtos = connections.Select(c => new DeviceConnectionDto
                {
                    ConnectedDeviceId = c.ConnectedDeviceId,
                    InterfaceDeviceId = c.InterfaceDeviceId,
                    Type = c.Type,
                    Direction = c.Direction
                }).ToList();

                _logger.LogInformation("Successfully retrieved {Count} connections for device {DeviceId}",
                    connectionDtos.Count, deviceId.ToString());

                return connectionDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device connections for device {DeviceId}", deviceId.ToString());
                throw;
            }
        }

        /// <summary>
        /// Gets devices by device model ID.
        /// </summary>
        /// <param name="deviceModelId">The ID of the device model</param>
        /// <returns>A list of device DTOs with the specified device model</returns>
        public async Task<List<DeviceDto>> GetDevicesByModelIdAsync(Guid deviceModelId)
        {
            try
            {
                _logger.LogDebug("Getting devices with model ID {DeviceModelId}", deviceModelId);

                // Get devices by model ID
                var devices = await _deviceRepository.GetDevicesByModelIdAsync(deviceModelId);

                // Map to DTOs
                var deviceDtos = devices.Select(_deviceMapper.ToDto).ToList();

                _logger.LogInformation("Successfully retrieved {Count} devices with model ID {DeviceModelId}",
                    deviceDtos.Count, deviceModelId);

                return deviceDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting devices with model ID {DeviceModelId}", deviceModelId);
                throw;
            }
        }

        /// <summary>
        /// Gets all compatible devices for a device that can be connected to it.
        /// </summary>
        /// <param name="deviceId">The ID of the device</param>
        /// <returns>A list of device DTOs that are compatible with the device</returns>
        public async Task<List<DeviceDto>> GetCompatibleDevicesAsync(Guid deviceId)
        {
            try
            {
                _logger.LogDebug("Getting compatible devices for device {DeviceId}", deviceId.ToString());

                // Get device
                var device = await _deviceRepository.GetByIdAsync(deviceId);
                if (device == null || !device.VehicleId.HasValue)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found or not associated with a vehicle", deviceId.ToString());
                    return [];
                }

                // Get all devices for the same vehicle
                var allDevices = await _deviceRepository.GetDevicesForVehicleAsync(device.VehicleId.Value);
                if (!allDevices.Any())
                {
                    _logger.LogWarning("No devices found for vehicle {VehicleId}", device.VehicleId?.ToString() ?? "null");
                    return [];
                }

                // Create a temporary vehicle to access the connections
                var vehicle = _vehicleFactory.CreateVesselWithDevices(device.VehicleId.Value, allDevices);

                // Get device connections
                var connections = vehicle.GetDeviceConnections(deviceId).ToList();

                // Get the IDs of all connected devices
                var connectedDeviceIds = connections
                    .Select(c => c.ConnectedDeviceId == deviceId ? c.InterfaceDeviceId : c.ConnectedDeviceId)
                    .Distinct()
                    .ToList();

                // Filter compatible devices - all devices in the same vehicle except the current one and already connected ones
                var compatibleDevices = allDevices
                    .Where(d => d.Id != deviceId && !connectedDeviceIds.Contains(d.Id))
                    .ToList();

                // Map to DTOs
                var compatibleDeviceDtos = compatibleDevices.Select(_deviceMapper.ToDto).ToList();

                _logger.LogInformation("Successfully retrieved {Count} compatible devices for device {DeviceId}",
                    compatibleDeviceDtos.Count, deviceId.ToString());

                return compatibleDeviceDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compatible devices for device {DeviceId}", deviceId.ToString());
                throw;
            }
        }
    }
}
