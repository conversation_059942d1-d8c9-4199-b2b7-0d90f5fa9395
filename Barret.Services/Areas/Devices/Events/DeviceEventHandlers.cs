using Barret.Core.Areas.Devices.Events;
using Barret.Services.Areas.Common.Events;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Devices.Events
{
    /// <summary>
    /// Handler for DeviceCreatedEvent.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceCreatedEventHandler"/> class.
    /// </remarks>
    /// <param name="logger">The logger.</param>
    public class DeviceCreatedEventHandler(ILogger<DeviceCreatedEventHandler> logger) : IDomainEventHandler<DeviceCreatedEvent>
    {
        private readonly ILogger<DeviceCreatedEventHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <inheritdoc/>
        public Task HandleAsync(DeviceCreatedEvent domainEvent)
        {
            _logger.LogInformation("Device created event handled for device {DeviceId} ({DeviceName}) of type {DeviceType}",
                domainEvent.DeviceId,
                domainEvent.Device.Name,
                domainEvent.Device.GetType().Name);

            // Example: Here you could add notifications, update statistics, trigger integrations, etc.

            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Handler for DeviceConnectionAddedEvent.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceConnectionAddedEventHandler"/> class.
    /// </remarks>
    /// <param name="logger">The logger.</param>
    public class DeviceConnectionAddedEventHandler(ILogger<DeviceConnectionAddedEventHandler> logger) : IDomainEventHandler<DeviceConnectionAddedEvent>
    {
        private readonly ILogger<DeviceConnectionAddedEventHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <inheritdoc/>
        public Task HandleAsync(DeviceConnectionAddedEvent domainEvent)
        {
            _logger.LogInformation("Device connection added event handled: Source {ConnectedDeviceId} -> Target {InterfaceDeviceId}",
                domainEvent.DeviceId,
                domainEvent.InterfaceDeviceId);

            // Example: Here you could update connection graphs, trigger notifications, etc.

            return Task.CompletedTask;
        }
    }

    /// <summary>
    /// Handler for DeviceConnectionRemovedEvent.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceConnectionRemovedEventHandler"/> class.
    /// </remarks>
    /// <param name="logger">The logger.</param>
    public class DeviceConnectionRemovedEventHandler(ILogger<DeviceConnectionRemovedEventHandler> logger) : IDomainEventHandler<DeviceConnectionRemovedEvent>
    {
        private readonly ILogger<DeviceConnectionRemovedEventHandler> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <inheritdoc/>
        public Task HandleAsync(DeviceConnectionRemovedEvent domainEvent)
        {
            _logger.LogInformation("Device connection removed event handled: Source {ConnectedDeviceId} -> Target {InterfaceDeviceId}",
                domainEvent.DeviceId,
                domainEvent.InterfaceDeviceId);

            // Example: Here you could update connection graphs, trigger notifications, etc.

            return Task.CompletedTask;
        }
    }
}