using Barret.Core.Areas.DeviceGroups;
using Barret.Core.Areas.Vehicles.Models;
using Barret.Services.Core.Areas.Devices.Mapping;
using Barret.Shared.DTOs.Devices;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Devices.Services
{
    /// <summary>
    /// Service for mapping device groups from domain entities to DTOs
    /// </summary>
    /// <remarks>
    /// Creates a new instance of DeviceGroupMappingService
    /// </remarks>
    /// <param name="deviceMapper">The device mapper</param>
    /// <param name="logger">The logger</param>
    public class DeviceGroupMappingService(
        IDeviceMapper deviceMapper,
        ILogger<DeviceGroupMappingService> logger) : IDeviceGroupMappingService
    {
        private readonly IDeviceMapper _deviceMapper = deviceMapper ?? throw new ArgumentNullException(nameof(deviceMapper));
        private readonly ILogger<DeviceGroupMappingService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Maps device groups from a vehicle entity to DTOs (asynchronous)
        /// </summary>
        /// <param name="vehicle">The vehicle entity</param>
        /// <returns>A dictionary of device group DTOs keyed by DeviceGroups enum value</returns>
        public Task<Dictionary<DeviceGroups, DeviceGroupDto>> MapToDeviceGroupDtosAsync(Vehicle vehicle)
        {
            if (vehicle == null)
            {
                _logger.LogWarning("Cannot map device groups from null vehicle");
                return Task.FromResult(new Dictionary<DeviceGroups, DeviceGroupDto>());
            }

            _logger.LogTrace("Mapping device groups from vehicle {VehicleId}", vehicle.Id);

            var result = new Dictionary<DeviceGroups, DeviceGroupDto>();

            // Get all device groups from the vehicle
            var deviceGroups = vehicle.GetAllDeviceGroups().ToList();
            _logger.LogTrace("Found {GroupCount} device groups in vehicle", deviceGroups.Count);

            foreach (var deviceGroup in deviceGroups)
            {
                try
                {
                    // Get allowed roles directly from the device group
                    var allowedRoles = deviceGroup.GetAllowedRoles().ToList();

                    // Create fresh device DTOs to prevent reference sharing issues
                    var deviceDtos = new List<DeviceDto>();
                    var deviceIds = new HashSet<Guid>(); // Track device IDs to prevent duplicates

                    foreach (var device in deviceGroup.Devices)
                    {
                        // Only add devices with valid IDs and no duplicates
                        if (device.Id != Guid.Empty && deviceIds.Add(device.Id))
                        {
                            var deviceDto = _deviceMapper.ToDto(device);
                            if (deviceDto != null)
                            {
                                deviceDtos.Add(deviceDto);
                            }
                        }
                        else if (device.Id == Guid.Empty)
                        {
                            // For new devices without IDs, always add them
                            var deviceDto = _deviceMapper.ToDto(device);
                            if (deviceDto != null)
                            {
                                deviceDtos.Add(deviceDto);
                            }
                        }
                        else
                        {
                            _logger.LogWarning("Skipping duplicate device with ID {DeviceId} in group {GroupType} during mapping",
                                device.Id, deviceGroup.Type);
                        }
                    }

                    // Create the DTO
                    var dto = new DeviceGroupDto
                    {
                        Type = deviceGroup.Type, // Set the Type property from the domain entity
                        AllowedRoles = allowedRoles,
                        Devices = deviceDtos
                    };

                    result[deviceGroup.Type] = dto;
                    _logger.LogTrace("Mapped device group {GroupType} with {DeviceCount} devices (filtered from {OriginalCount})",
                        dto.Type, dto.Devices.Count, deviceGroup.Devices.Count);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error mapping device group {GroupType}", deviceGroup.Type);
                }
            }

            return Task.FromResult(result);
        }

        /// <summary>
        /// Maps device groups from a vehicle entity to DTOs (synchronous)
        /// </summary>
        /// <param name="vehicle">The vehicle entity</param>
        /// <returns>A dictionary of device group DTOs keyed by DeviceGroups enum value</returns>
        public Dictionary<DeviceGroups, DeviceGroupDto> MapToDeviceGroupDtos(Vehicle vehicle)
        {
            _logger.LogDebug("MapToDeviceGroupDtos called for vehicle {VehicleId}", vehicle?.Id);
            var result = MapToDeviceGroupDtosAsync(vehicle).GetAwaiter().GetResult();
            _logger.LogDebug("MapToDeviceGroupDtos completed with {GroupCount} groups", result.Count);
            return result;
        }


    }
}
