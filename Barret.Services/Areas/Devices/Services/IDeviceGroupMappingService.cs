using Barret.Core.Areas.DeviceGroups;
using Barret.Core.Areas.Vehicles.Models;
using Barret.Shared.DTOs.Devices;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Barret.Services.Areas.Devices.Services
{
    /// <summary>
    /// Service interface for mapping device groups from domain entities to DTOs
    /// </summary>
    public interface IDeviceGroupMappingService
    {
        /// <summary>
        /// Maps device groups from a vehicle entity to DTOs (asynchronous)
        /// </summary>
        /// <param name="vehicle">The vehicle entity</param>
        /// <returns>A dictionary of device group DTOs keyed by DeviceGroups enum value</returns>
        Task<Dictionary<DeviceGroups, DeviceGroupDto>> MapToDeviceGroupDtosAsync(Vehicle vehicle);

        /// <summary>
        /// Maps device groups from a vehicle entity to DTOs (synchronous)
        /// </summary>
        /// <param name="vehicle">The vehicle entity</param>
        /// <returns>A dictionary of device group DTOs keyed by DeviceGroups enum value</returns>
        Dictionary<DeviceGroups, DeviceGroupDto> MapToDeviceGroupDtos(Vehicle vehicle);
    }
}
