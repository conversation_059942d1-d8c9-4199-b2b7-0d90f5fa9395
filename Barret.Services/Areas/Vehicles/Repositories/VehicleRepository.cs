using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Vehicles.Models;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Vehicles.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Vehicles.Repositories
{
    public class VehicleRepository(
        IBarretDbContext dbContext,
        ILogger<VehicleRepository> logger) : IVehicleRepository<Vessel>
    {
        private readonly IBarretDbContext _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        private readonly ILogger<VehicleRepository> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        public async Task<Vessel?> GetByIdAsync(Guid id)
        {
            return await GetVesselByIdAsync(id);
        }

        public async Task<Vessel?> GetWithDevicesAsync(Guid id)
        {
            return await GetVesselByIdAsync(id);
        }

        public async Task<IEnumerable<Vessel>> GetAllAsync()
        {
            return await GetAllVesselsAsync();
        }

        public async Task AddAsync(Vessel vessel)
        {
            await AddAsync((Vehicle)vessel);
        }

        public void Update(Vessel vessel)
        {
            Update((Vehicle)vessel);
        }

        public async Task<IEnumerable<Vehicle>> GetAllVehiclesAsync()
        {
            try
            {
                _logger.LogDebug("Retrieving all vehicles");
                var vehicles = await _dbContext.Vehicles.AsNoTracking().ToListAsync();
                _logger.LogInformation("Retrieved {Count} vehicles", vehicles.Count);
                return vehicles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all vehicles");
                throw;
            }
        }

        public async Task<IEnumerable<Vessel>> GetAllVesselsAsync()
        {
            try
            {
                _logger.LogDebug("Retrieving all vessels with devices");

                // Get all vessels
                var vessels = await _dbContext.Vessels.AsNoTracking().ToListAsync();

                // For each vessel, load its devices
                foreach (var vessel in vessels)
                {
                    // Load devices for the vessel
                    var devices = await _dbContext.Devices
                        .Where(d => d.VehicleId == vessel.Id)
                        .AsNoTracking()
                        .ToListAsync();

                    // Add devices to vessel
                    foreach (var device in devices)
                    {
                        vessel.AddDevice(device);
                    }
                }

                _logger.LogInformation("Retrieved {Count} vessels with their devices", vessels.Count);
                return vessels;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all vessels");
                throw;
            }
        }

        public async Task<Vehicle?> GetVehicleByIdAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Retrieving vehicle with ID {VehicleId}", id);
                var vehicle = await _dbContext.Vehicles.FindAsync(id);

                if (vehicle == null)
                {
                    _logger.LogWarning("Vehicle with ID {VehicleId} not found", id);
                }
                else
                {
                    _logger.LogDebug("Successfully retrieved vehicle {VehicleId}", id);
                }

                return vehicle;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vehicle with ID {VehicleId}", id);
                throw;
            }
        }

        public async Task<Vessel?> GetVesselByIdAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Retrieving vessel with ID {VesselId}", id);

                var vessel = await _dbContext.Vessels
                    .FirstOrDefaultAsync(v => v.Id == id);

                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", id);
                    return null;
                }

                _logger.LogDebug("Successfully retrieved vessel {VesselId}", id);

                // Load devices for the vessel using the explicit VehicleId property
                // Load devices in a single query with their models and manufacturers
                var devices = await _dbContext.Devices
                    .Where(d => d.VehicleId == id)
                    .Include(d => d.Model!)
                        .ThenInclude(m => m!.Manufacturer)
                    .AsSplitQuery()
                    .ToListAsync();

                // Add devices to the vessel's device groups
                foreach (var device in devices)
                {
                    vessel.AddDevice(device);
                }

                _logger.LogInformation("Loaded vessel {VesselId} with {DeviceCount} devices",
                    id, devices.Count);

                return vessel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vessel with ID {VesselId}", id);
                throw;
            }
        }

        public async Task AddAsync(Vehicle vehicle)
        {
            ArgumentNullException.ThrowIfNull(vehicle);

            try
            {
                _logger.LogDebug("Adding vehicle with ID {VehicleId}", vehicle.Id);
                await _dbContext.Vehicles.AddAsync(vehicle);
                _logger.LogDebug("Vehicle with ID {VehicleId} added to context (not yet saved)", vehicle.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding vehicle with ID {VehicleId}", vehicle.Id);
                throw;
            }
        }

        public void Update(Vehicle vehicle)
        {
            ArgumentNullException.ThrowIfNull(vehicle);

            try
            {
                _logger.LogDebug("Updating vehicle with ID {VehicleId}", vehicle.Id);

                // Mark the entity as modified
                _dbContext.Entry(vehicle).State = EntityState.Modified;

                _logger.LogDebug("Vehicle with ID {VehicleId} marked as modified (not yet saved)", vehicle.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating vehicle with ID {VehicleId}", vehicle.Id);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public void UpdateAsync(Vehicle vehicle)
        {
            Update(vehicle);
        }

        public async Task DeleteAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Deleting vehicle with ID {VehicleId}", id);

                var vehicle = await _dbContext.Vehicles.FindAsync(id);
                if (vehicle == null)
                {
                    _logger.LogWarning("Vehicle with ID {VehicleId} not found during delete operation", id);
                    throw new KeyNotFoundException($"Vehicle with ID {id} not found");
                }

                _dbContext.Vehicles.Remove(vehicle);
                _logger.LogDebug("Vehicle with ID {VehicleId} marked for deletion (not yet saved)", id);
            }
            catch (Exception ex) when (ex is not KeyNotFoundException)
            {
                _logger.LogError(ex, "Error deleting vehicle with ID {VehicleId}", id);
                throw;
            }
        }

        public async Task<bool> ExistsAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Checking if vehicle with ID {VehicleId} exists", id);
                var exists = await _dbContext.Vehicles.AnyAsync(v => v.Id == id);
                _logger.LogDebug("Vehicle with ID {VehicleId} {Exists}", id, exists ? "exists" : "does not exist");
                return exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if vehicle with ID {VehicleId} exists", id);
                throw;
            }
        }

        public async Task<IEnumerable<GenericDevice>> GetDevicesForVehicleAsync(Guid vehicleId)
        {
            try
            {
                _logger.LogDebug("Retrieving devices for vehicle with ID {VehicleId}", vehicleId);
                var devices = await _dbContext.Devices
                    .Where(d => d.VehicleId == vehicleId)
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} devices for vehicle {VehicleId}", devices.Count, vehicleId);
                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving devices for vehicle with ID {VehicleId}", vehicleId);
                throw;
            }
        }


    }
}
