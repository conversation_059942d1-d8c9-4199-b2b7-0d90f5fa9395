using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Vehicles.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Vehicles.Repositories
{
    /// <summary>
    /// Repository implementation for vessels.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="VesselRepository"/> class.
    /// </remarks>
    /// <param name="dbContext">The database context</param>
    /// <param name="logger">The logger</param>
    /// <exception cref="ArgumentNullException">Thrown if any parameter is null</exception>
    public class VesselRepository(IBarretDbContext dbContext, ILogger<VesselRepository> logger) : IVesselRepository
    {
        private readonly IBarretDbContext _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        private readonly ILogger<VesselRepository> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Gets a vessel by its ID.
        /// </summary>
        /// <param name="id">The ID of the vessel to retrieve</param>
        /// <returns>The vessel if found; otherwise, null</returns>
        public async Task<Vessel?> GetByIdAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Retrieving vessel with ID {VesselId}", id);

                var vessel = await _dbContext.Vessels
                    .AsNoTracking()
                    .FirstOrDefaultAsync(v => v.Id == id);

                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", id);
                }
                else
                {
                    _logger.LogDebug("Successfully retrieved vessel {VesselId}", id);
                }

                return vessel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vessel with ID {VesselId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets a vessel by its ID with all its devices eagerly loaded.
        /// </summary>
        /// <param name="id">The ID of the vessel to retrieve</param>
        /// <returns>The vessel with devices if found; otherwise, null</returns>
        public async Task<Vessel?> GetWithDevicesAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Retrieving vessel with devices for ID {VesselId}", id);

                var vessel = await _dbContext.Vessels
                    .FirstOrDefaultAsync(v => v.Id == id);

                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", id);
                    return null;
                }

                // Load devices for the vessel with their models and manufacturers
                var devices = await _dbContext.Devices
                    .Where(d => d.VehicleId == id)
                    .Include(d => d.Model!)
                        .ThenInclude(m => m!.Manufacturer)
                    .AsSplitQuery()
                    .ToListAsync();

                // Add devices to vessel
                foreach (var device in devices)
                {
                    vessel.AddDevice(device);
                }

                _logger.LogInformation("Loaded vessel {VesselId} with {DeviceCount} devices",
                    id, devices.Count);

                return vessel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vessel with devices for ID {VesselId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets a vessel by its ID with the full device tree and all device connections eagerly loaded.
        /// </summary>
        /// <param name="id">The ID of the vessel to retrieve</param>
        /// <returns>The vessel with the full device tree and connections if found; otherwise, null</returns>
        public async Task<Vessel?> GetWithDeviceTreeAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Retrieving vessel with full device tree for ID {VesselId}", id);

                var vessel = await _dbContext.Vessels
                    .FirstOrDefaultAsync(v => v.Id == id);

                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", id);
                    return null;
                }

                // Load all devices for the vessel with all related data
                _logger.LogDebug("VESSELREPOSITORY: Querying database for devices with VehicleId = {VesselId}", id);

                var devices = await _dbContext.Devices
                    .Where(d => d.VehicleId == id)
                    .Include(d => d.Model!)
                        .ThenInclude(m => m!.Manufacturer)
                    .AsSplitQuery()
                    .ToListAsync();

                _logger.LogInformation("VESSELREPOSITORY: Loaded {DeviceCount} devices from database for vessel {VesselId}", devices.Count, id);

                // Check for duplicate device IDs in the database query result
                var deviceIds = devices.Select(d => d.Id).ToList();
                var uniqueDeviceIds = deviceIds.Distinct().ToList();
                if (deviceIds.Count != uniqueDeviceIds.Count)
                {
                    var duplicateIds = deviceIds.GroupBy(id => id).Where(g => g.Count() > 1).Select(g => g.Key).ToList();
                    _logger.LogError("VESSELREPOSITORY: CRITICAL DATABASE ISSUE - Found duplicate device IDs in database query result: {DuplicateIds}",
                        string.Join(", ", duplicateIds));
                }
                else
                {
                    _logger.LogDebug("VESSELREPOSITORY: Database query validation passed - no duplicate device IDs");
                }

                // Log device details for debugging
                foreach (var device in devices)
                {
                    _logger.LogInformation("VESSELREPOSITORY: Device {DeviceId} ({DeviceName}) - Role: {Role}, HasModel: {HasModel}, HasConnection: {HasConnection}",
                        device.Id, device.Name, device.DeviceRole, device.Model != null, device.HasConnection());

                    if (device.Model != null)
                    {
                        _logger.LogInformation("VESSELREPOSITORY: Device {DeviceId} model - Name: {ModelName}, Manufacturer: {Manufacturer}",
                            device.Id, device.Model.Name, device.Model.Manufacturer?.Name ?? "NULL");
                    }

                    if (device.HasConnection())
                    {
                        _logger.LogInformation("VESSELREPOSITORY: Device {DeviceId} connection - IP: {IP}, Port: {Port}",
                            device.Id, device.Connection?.IPAddress ?? "NULL", device.Connection?.Port.ToString() ?? "NULL");
                    }
                }

                // Clear all device groups first to prevent duplication
                _logger.LogDebug("VESSELREPOSITORY: Clearing all device groups before adding devices");
                foreach (var group in vessel.GetAllDeviceGroups())
                {
                    group.Clear();
                }

                // Add devices to vessel
                _logger.LogDebug("VESSELREPOSITORY: Adding {DeviceCount} devices to vessel {VesselId}", devices.Count, id);

                var addedDeviceCount = 0;
                foreach (var device in devices)
                {
                    if (vessel.AddDevice(device))
                    {
                        addedDeviceCount++;
                        _logger.LogDebug("VESSELREPOSITORY: Successfully added device {DeviceId} ({DeviceName}) to vessel", device.Id, device.Name);
                    }
                    else
                    {
                        _logger.LogWarning("VESSELREPOSITORY: Failed to add device {DeviceId} ({DeviceName}) to vessel - no compatible device group found", device.Id, device.Name);
                    }
                }

                // Verify device count after adding to vessel
                var vesselDeviceCount = vessel.GetAllDevices().Count();
                _logger.LogInformation("VESSELREPOSITORY: Added {AddedCount} devices to vessel, vessel now contains {VesselDeviceCount} devices", addedDeviceCount, vesselDeviceCount);

                if (vesselDeviceCount != devices.Count)
                {
                    _logger.LogError("VESSELREPOSITORY: DEVICE COUNT MISMATCH AFTER ADDING - Expected: {ExpectedCount}, Actual: {ActualCount}, Added: {AddedCount}",
                        devices.Count, vesselDeviceCount, addedDeviceCount);

                    // Log detailed device information for debugging
                    _logger.LogError("VESSELREPOSITORY: Database devices: {DatabaseDevices}",
                        string.Join(", ", devices.Select(d => $"{d.Id}({d.Name})")));
                    _logger.LogError("VESSELREPOSITORY: Vessel devices: {VesselDevices}",
                        string.Join(", ", vessel.GetAllDevices().Select(d => $"{d.Id}({d.Name})")));
                }
                else
                {
                    _logger.LogDebug("VESSELREPOSITORY: Device count validation passed after adding to vessel");
                }

                // Load device connections from the database
                // Note: In EF Core, device connections are stored in the vessel entity
                // and loaded automatically when the vessel is loaded

                _logger.LogInformation("VESSELREPOSITORY: Loaded vessel {VesselId} with {DeviceCount} devices and {ConnectionCount} connections",
                    id, devices.Count, vessel.DeviceConnections.Count);

                return vessel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vessel with full device tree for ID {VesselId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets all vessels with their devices.
        /// </summary>
        /// <returns>A collection of all vessels with their devices</returns>
        public async Task<IEnumerable<Vessel>> GetAllAsync()
        {
            try
            {
                _logger.LogDebug("Retrieving all vessels with devices");

                // Get all vessels
                var vessels = await _dbContext.Vessels
                    .AsNoTracking()
                    .ToListAsync();

                // For each vessel, load its devices
                foreach (var vessel in vessels)
                {
                    // Load devices for the vessel
                    var devices = await _dbContext.Devices
                        .Where(d => d.VehicleId == vessel.Id)
                        .AsNoTracking()
                        .ToListAsync();

                    // Add devices to vessel
                    foreach (var device in devices)
                    {
                        vessel.AddDevice(device);
                    }
                }

                _logger.LogInformation("Retrieved {Count} vessels with their devices", vessels.Count);

                return vessels;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all vessels with devices");
                throw;
            }
        }

        /// <summary>
        /// Adds a vessel to the repository.
        /// </summary>
        /// <param name="vessel">The vessel to add</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        public async Task AddAsync(Vessel vessel)
        {
            ArgumentNullException.ThrowIfNull(vessel);

            try
            {
                _logger.LogDebug("Adding vessel with ID {VesselId}", vessel.Id);

                // Add the vessel to the context
                await _dbContext.Vessels.AddAsync(vessel);

                // Get all devices from the vessel
                var devices = vessel.GetAllDevices().ToList();
                _logger.LogInformation("Adding {DeviceCount} devices from vessel {VesselId}", devices.Count, vessel.Id);

                // Add each device to the context
                foreach (var device in devices)
                {
                    // Ensure the device has the correct VehicleId
                    if (!device.VehicleId.HasValue || device.VehicleId.Value != vessel.Id)
                    {
                        device.VehicleId = vessel.Id;
                    }

                    // Add the device to the context
                    await _dbContext.Devices.AddAsync(device);
                    _logger.LogDebug("Added device {DeviceId} to context", device.Id);
                }

                _logger.LogDebug("Vessel with ID {VesselId} and {DeviceCount} devices added to context (not yet saved)",
                    vessel.Id, devices.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding vessel with ID {VesselId}", vessel.Id);
                throw;
            }
        }

        /// <summary>
        /// Updates a vessel in the repository.
        /// </summary>
        /// <param name="vessel">The vessel to update</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        public void Update(Vessel vessel)
        {
            ArgumentNullException.ThrowIfNull(vessel);

            try
            {
                _logger.LogDebug("Updating vessel with ID {VesselId}", vessel.Id);

                // Mark the entity as modified
                _dbContext.Entry(vessel).State = EntityState.Modified;

                _logger.LogDebug("Vessel with ID {VesselId} marked as modified (not yet saved)", vessel.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating vessel with ID {VesselId}", vessel.Id);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public void UpdateAsync(Vessel vessel)
        {
            Update(vessel);
        }

        /// <summary>
        /// Deletes a vessel from the repository.
        /// </summary>
        /// <param name="id">The ID of the vessel to delete</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        public async Task DeleteAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Deleting vessel with ID {VesselId}", id);

                var vessel = await _dbContext.Vessels.FindAsync(id);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found during delete operation", id);
                    throw new KeyNotFoundException($"Vessel with ID {id} not found");
                }

                _dbContext.Vessels.Remove(vessel);

                _logger.LogDebug("Vessel with ID {VesselId} marked for deletion (not yet saved)", id);
            }
            catch (Exception ex) when (ex is not KeyNotFoundException)
            {
                _logger.LogError(ex, "Error deleting vessel with ID {VesselId}", id);
                throw;
            }
        }

        /// <summary>
        /// Checks if a vessel with the specified ID exists.
        /// </summary>
        /// <param name="id">The ID of the vessel to check</param>
        /// <returns>True if the vessel exists; otherwise, false</returns>
        public async Task<bool> ExistsAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Checking if vessel with ID {VesselId} exists", id);

                var exists = await _dbContext.Vessels.AnyAsync(v => v.Id == id);

                _logger.LogDebug("Vessel with ID {VesselId} {Exists}", id, exists ? "exists" : "does not exist");

                return exists;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if vessel with ID {VesselId} exists", id);
                throw;
            }
        }

        /// <summary>
        /// Gets all devices for a vessel.
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel</param>
        /// <returns>A collection of devices for the vessel</returns>
        public async Task<IEnumerable<GenericDevice>> GetDevicesForVehicleAsync(Guid vehicleId)
        {
            try
            {
                _logger.LogDebug("Retrieving devices for vessel with ID {VesselId}", vehicleId);

                var devices = await _dbContext.Devices
                    .Where(d => d.VehicleId == vehicleId)
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} devices for vessel {VesselId}", devices.Count, vehicleId);

                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving devices for vessel with ID {VesselId}", vehicleId);
                throw;
            }
        }

        /// <summary>
        /// Gets all vessels with basic information only (no devices loaded).
        /// This is optimized for list views that only need basic vessel information.
        /// </summary>
        /// <returns>A collection of vessels with basic information only</returns>
        public async Task<IEnumerable<Vessel>> GetAllBasicAsync()
        {
            try
            {
                _logger.LogDebug("Retrieving all vessels with basic information only");

                var vessels = await _dbContext.Vessels
                    .AsNoTracking()
                    .ToListAsync();

                _logger.LogInformation("Retrieved {Count} vessels with basic information", vessels.Count);

                return vessels;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving vessels with basic information");
                throw;
            }
        }

        /// <summary>
        /// Gets the device count for a specific vessel without loading the actual devices.
        /// </summary>
        /// <param name="vesselId">The ID of the vessel</param>
        /// <returns>The number of devices associated with the vessel</returns>
        public async Task<int> GetDeviceCountAsync(Guid vesselId)
        {
            try
            {
                _logger.LogDebug("Getting device count for vessel with ID {VesselId}", vesselId);

                var count = await _dbContext.Devices
                    .Where(d => d.VehicleId == vesselId)
                    .CountAsync();

                _logger.LogDebug("Vessel {VesselId} has {DeviceCount} devices", vesselId, count);

                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device count for vessel with ID {VesselId}", vesselId);
                throw;
            }
        }
    }
}
