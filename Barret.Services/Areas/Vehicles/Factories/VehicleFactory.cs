using Barret.Core.Areas.Vehicles.Models;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Services.Core.Areas.Vehicles.Factories;
using Microsoft.Extensions.Logging;
using System.Reflection;

namespace Barret.Services.Areas.Vehicles.Factories
{
    /// <summary>
    /// Implementation of the IVehicleFactory interface for creating vehicles.
    /// </summary>
    public class VehicleFactory(ILogger<Vehicle> logger) : IVehicleFactory, Barret.Services.Core.Areas.Vehicles.Factories.IVehicleFactory
    {
        private readonly ILogger<Vehicle> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <inheritdoc/>
        public Vessel CreateVessel()
        {
            try
            {
                _logger.LogDebug("Creating new vessel with default values");

                // Create vessel using the parameterless constructor which sets default values
                var vessel = new Vessel();

                _logger.LogInformation("Created vessel with ID {VesselId}, VehicleId {VehicleId}, MMSI {MMSI}, and Name {Name}",
                    vessel.Id, vessel.VehicleId, vessel.MMSI, vessel.Name);

                return vessel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error creating vessel");
                throw;
            }
        }

        /// <summary>
        /// Creates a vehicle with the specified ID and devices
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle</param>
        /// <param name="devices">The devices to add to the vehicle</param>
        /// <returns>A vehicle instance with the specified devices</returns>
        public Vehicle CreateVehicleWithDevices(Guid vehicleId, IEnumerable<GenericDevice> devices)
        {
            // For now, we'll create a Vessel as the default vehicle type
            // In the future, this could be extended to support different vehicle types
            return CreateVesselWithDevices(vehicleId, devices);
        }

        /// <summary>
        /// Creates a vessel with the specified ID and devices
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel</param>
        /// <param name="devices">The devices to add to the vessel</param>
        /// <returns>A vessel instance with the specified devices</returns>
        public Vessel CreateVesselWithDevices(Guid vehicleId, IEnumerable<GenericDevice> devices)
        {
            try
            {
                _logger.LogDebug("Creating vessel with ID {VehicleId} and {DeviceCount} devices", vehicleId, devices.Count());

                // Create a new vessel
                var vessel = new Vessel();

                // Set the ID using reflection since it's a read-only property
                // This is a temporary solution until we can refactor the Vehicle constructor
                var idProperty = typeof(Vehicle).GetProperty("Id", BindingFlags.Public | BindingFlags.Instance);
                if (idProperty != null && idProperty.CanWrite)
                {
                    idProperty.SetValue(vessel, vehicleId);
                }
                else
                {
                    // If the property is not writable, use the backing field
                    var idField = typeof(Vehicle).GetField("_id", BindingFlags.NonPublic | BindingFlags.Instance);
                    if (idField != null)
                    {
                        idField.SetValue(vessel, vehicleId);
                    }
                    else
                    {
                        // Last resort: use reflection to set the property
                        idProperty?.SetValue(vessel, vehicleId);
                    }
                }

                // Add all devices to the vessel
                foreach (var device in devices)
                {
                    vessel.AddDevice(device);
                }

                _logger.LogDebug("Successfully created vessel with ID {VehicleId} and {DeviceCount} devices", vehicleId, devices.Count());

                return vessel;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating vessel with ID {VehicleId}", vehicleId);
                throw;
            }
        }


    }
}