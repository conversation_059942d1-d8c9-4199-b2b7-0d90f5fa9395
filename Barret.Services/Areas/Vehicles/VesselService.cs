using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Services.Core.Areas.Devices.Factories;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Core.Areas.Vehicles.ValueObjects;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Devices.Mapping;
using Barret.Services.Core.Areas.Mapping;
using Barret.Services.Core.Areas.Vehicles;
using Barret.Services.Core.Areas.Vehicles.Mapping;
using Barret.Services.Core.Areas.Vehicles.Queries;
using Barret.Services.Core.Areas.Vehicles.Repositories;

using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Shared.Results;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;



namespace Barret.Services.Areas.Vehicles
{
    /// <summary>
    /// Service for vessel operations, implementing the aggregate root pattern.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="VesselService"/> class.
    /// </remarks>
    /// <param name="unitOfWork">The unit of work for transaction management</param>
    /// <param name="logger">The logger</param>
    /// <param name="mapper">The entity mapper for converting between entities and DTOs</param>
    /// <param name="deviceMapper">The device mapper for converting between device entities and DTOs</param>
    /// <param name="vesselRepository">The vessel repository</param>
    /// <param name="deviceFactory">The device factory for creating devices</param>
    /// <param name="vesselQueryService">The vessel query service for optimized queries</param>
    /// <exception cref="ArgumentNullException">Thrown if any parameter is null</exception>
    public class VesselService(
        IUnitOfWork unitOfWork,
        ILogger<VesselService> logger,
        IVehicleMapper<Vessel, VesselDto> mapper,
        IDeviceMapper deviceMapper,
        IVesselRepository vesselRepository,
        IDeviceFactory deviceFactory,
        IVesselQueryService vesselQueryService) : VehicleService<Vessel, VesselDto>(unitOfWork, logger, mapper, deviceMapper)
    {
        private readonly IVesselRepository _vesselRepository = vesselRepository ?? throw new ArgumentNullException(nameof(vesselRepository));
        private readonly IDeviceFactory _deviceFactory = deviceFactory ?? throw new ArgumentNullException(nameof(deviceFactory));
        private readonly IVesselQueryService _vesselQueryService = vesselQueryService ?? throw new ArgumentNullException(nameof(vesselQueryService));

        /// <summary>
        /// Gets a vessel by its ID.
        /// </summary>
        /// <param name="id">The ID of the vessel to retrieve</param>
        /// <returns>A service result containing the vessel DTO if found</returns>
        public override async Task<ServiceResult<VesselDto>> GetByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Getting vessel with ID {VesselId}", id);

                var vessel = await _vesselRepository.GetWithDevicesAsync(id);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", id);
                    return ServiceResult<VesselDto>.CreateFailure($"Vessel with ID {id} not found");
                }

                var vesselDto = _mapper.MapToDto(vessel);

                return ServiceResult<VesselDto>.CreateSuccess(vesselDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vessel {VesselId}", id);
                return ServiceResult<VesselDto>.CreateFailure($"Error getting vessel: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets all vessels.
        /// </summary>
        /// <returns>A service result containing a collection of vessel DTOs</returns>
        public override async Task<ServiceResult<IEnumerable<VesselDto>>> GetAllAsync()
        {
            try
            {
                _logger.LogInformation("Getting all vessels");

                var vessels = await _vesselRepository.GetAllAsync();
                var vesselDtos = vessels.Select(_mapper.MapToDto).ToList();

                return ServiceResult<IEnumerable<VesselDto>>.CreateSuccess(vesselDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all vessels");
                return ServiceResult<IEnumerable<VesselDto>>.CreateFailure($"Error getting all vessels: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets all vessels with basic information only (optimized for list views).
        /// This method doesn't load devices or device groups, making it much faster for list displays.
        /// </summary>
        /// <returns>A service result containing a collection of basic vessel DTOs</returns>
        public override async Task<ServiceResult<IEnumerable<VesselDto>>> GetAllBasicAsync()
        {
            try
            {
                _logger.LogInformation("Getting all vessels with basic information");

                // Delegate to query service for optimized query
                var vessels = await _vesselQueryService.GetAllBasicAsync();

                _logger.LogInformation("Retrieved {Count} vessels with basic information", vessels.Count);
                return ServiceResult<IEnumerable<VesselDto>>.CreateSuccess(vessels);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vessels with basic information");
                return ServiceResult<IEnumerable<VesselDto>>.CreateFailure($"Error getting vessels with basic information: {ex.Message}");
            }
        }

        /// <summary>
        /// Creates a new vessel.
        /// </summary>
        /// <param name="dto">The vessel DTO containing the data for the new vessel</param>
        /// <returns>A service result containing the ID of the created vessel</returns>
        public override async Task<ServiceResult<Guid>> CreateAsync(VesselDto dto)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Creating new vessel");

                // Create a new vessel
                var vessel = new Vessel();

                // Set basic properties
                vessel.VehicleId = dto.VehicleId;
                vessel.Name = dto.Name;
                vessel.MMSI = dto.MMSI;
                vessel.ENI = dto.ENI ?? string.Empty;

                // Set dimensions if provided
                if (dto.Dimensions != null)
                {
                    vessel.WithDimensions(new DimensionsFromGpsLocation(
                        dto.Dimensions.DistanceGpsToFront,
                        dto.Dimensions.DistanceGpsToBack,
                        dto.Dimensions.DistanceGpsToLeft,
                        dto.Dimensions.DistanceGpsToRight
                    ));
                }

                // Add devices from the DTO
                if (dto.DeviceGroups != null)
                {
                    foreach (var deviceDto in dto.GetAllDevices())
                    {
                        // Create a new device using the device factory
                        var device = _deviceFactory.CreateDevice(deviceDto.DeviceRole);

                        // Set properties from DTO using the mapper
                        _deviceMapper.UpdateEntityFromDto(device, deviceDto);

                        // Add to vessel
                        vessel.AddDevice(device);
                    }
                }

                // Add device connections from the DTO
                if (dto.DeviceConnections != null)
                {
                    foreach (var connectionDto in dto.DeviceConnections)
                    {
                        try
                        {
                            vessel.ConnectDeviceToInterface(
                                connectionDto.ConnectedDeviceId,
                                connectionDto.InterfaceDeviceId,
                                connectionDto.Type,
                                connectionDto.Direction);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogWarning(ex, "Failed to create connection from {ConnectedId} to {InterfaceId}",
                                connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);
                        }
                    }
                }

                // Log device count for debugging
                int deviceCount = vessel.GetAllDevices().Count();
                _logger.LogInformation("Vessel created with {DeviceCount} devices", deviceCount);

                // Add to repository - the repository should handle saving the entire aggregate
                await _vesselRepository.AddAsync(vessel);

                return ServiceResult<Guid>.CreateSuccess(vessel.Id);
            }, "CreateVessel");
        }

        /// <summary>
        /// Updates an existing vessel.
        /// </summary>
        /// <param name="id">The ID of the vessel to update</param>
        /// <param name="dto">The vessel DTO containing the updated data</param>
        /// <returns>A service result containing the updated vessel DTO</returns>
        public override async Task<ServiceResult<VesselDto>> UpdateAsync(Guid id, VesselDto dto)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Updating vessel with ID {VesselId}", id);

                // Get existing vessel with all devices
                var vessel = await _vesselRepository.GetWithDeviceTreeAsync(id);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", id);
                    return ServiceResult<VesselDto>.CreateFailure($"Vessel with ID {id} not found");
                }

                // Log the number of devices in the vessel before update
                var deviceCount = vessel.GetAllDevices().Count();
                _logger.LogInformation("Vessel has {DeviceCount} devices before update", deviceCount);

                // Log the number of devices in the DTO
                var dtoDeviceCount = dto.DeviceGroups?.Values.Sum(g => g.Devices?.Count ?? 0) ?? 0;
                _logger.LogInformation("DTO has {DeviceCount} devices", dtoDeviceCount);

                // Update basic properties
                vessel.VehicleId = dto.VehicleId;
                vessel.Name = dto.Name;
                vessel.MMSI = dto.MMSI;
                vessel.ENI = dto.ENI ?? string.Empty;

                // Update dimensions if provided
                if (dto.Dimensions != null)
                {
                    vessel.WithDimensions(new DimensionsFromGpsLocation(
                        dto.Dimensions.DistanceGpsToFront,
                        dto.Dimensions.DistanceGpsToBack,
                        dto.Dimensions.DistanceGpsToLeft,
                        dto.Dimensions.DistanceGpsToRight
                    ));
                }

                // Process devices from the DTO
                if (dto.DeviceGroups != null)
                {
                    // Get all devices from the DTO
                    var dtoDevices = dto.GetAllDevices();

                    _logger.LogInformation("Processing {DeviceCount} devices from DTO", dtoDevices.Count);

                    // Get existing device IDs
                    var existingDeviceIds = vessel.GetAllDevices().Select(d => d.Id).ToHashSet();

                    // Keep track of processed device IDs to avoid removing newly added devices
                    var processedDeviceIds = new HashSet<Guid>();

                    // Process each device from the DTO
                    foreach (var deviceDto in dtoDevices)
                    {
                        if (existingDeviceIds.Contains(deviceDto.Id))
                        {
                            // Device already exists, update it
                            _logger.LogDebug("Device {DeviceId} already exists, updating", deviceDto.Id);

                            // Find the device in the vessel
                            var existingDevice = vessel.GetAllDevices().First(d => d.Id == deviceDto.Id);

                            // Update the device properties
                            _deviceMapper.UpdateEntityFromDto(existingDevice, deviceDto);

                            // Update in repository
                            _unitOfWork.Devices.Update(existingDevice);

                            // Add to processed devices
                            processedDeviceIds.Add(existingDevice.Id);
                        }
                        else if (deviceDto.Id != Guid.Empty)
                        {
                            // New device with existing ID, add it
                            _logger.LogDebug("Device {DeviceId} is new with existing ID, adding to vessel", deviceDto.Id);

                            // Map the DTO to an entity
                            var newDevice = _deviceMapper.ToEntity(deviceDto);

                            // Set the vehicle ID
                            newDevice.VehicleId = vessel.Id;

                            // Add to vessel
                            if (vessel.AddDevice(newDevice))
                            {
                                // Add to repository
                                await _unitOfWork.Devices.AddAsync(newDevice);
                                _logger.LogInformation("Added new device {DeviceId} to vessel", newDevice.Id);

                                // Add to processed devices
                                processedDeviceIds.Add(newDevice.Id);
                            }
                            else
                            {
                                _logger.LogWarning("Failed to add device {DeviceId} to vessel", newDevice.Id);
                            }
                        }
                        else
                        {
                            // New device without ID, create it
                            _logger.LogDebug("Creating new device for vessel");

                            // Create a new device using the device factory
                            var newDevice = _deviceFactory.CreateDevice(deviceDto.DeviceRole);

                            // Set properties from DTO using the mapper
                            _deviceMapper.UpdateEntityFromDto(newDevice, deviceDto);

                            // Set the vehicle ID
                            newDevice.VehicleId = vessel.Id;

                            // Add to vessel
                            if (vessel.AddDevice(newDevice))
                            {
                                // Add to repository
                                await _unitOfWork.Devices.AddAsync(newDevice);
                                _logger.LogInformation("Added new device {DeviceId} to vessel", newDevice.Id);

                                // Add to processed devices
                                processedDeviceIds.Add(newDevice.Id);
                            }
                            else
                            {
                                _logger.LogWarning("Failed to add device to vessel");
                            }
                        }
                    }

                    // Check for devices to remove (in vessel but not processed)
                    var devicesToRemove = vessel.GetAllDevices()
                        .Where(d => !processedDeviceIds.Contains(d.Id))
                        .ToList();

                    foreach (var deviceToRemove in devicesToRemove)
                    {
                        _logger.LogInformation("Removing device {DeviceId} from vessel", deviceToRemove.Id);

                        // Remove from vessel
                        if (vessel.RemoveDevice(deviceToRemove))
                        {
                            // Delete from repository
                            await _unitOfWork.Devices.DeleteAsync(deviceToRemove.Id);
                            _logger.LogInformation("Removed device {DeviceId} from vessel", deviceToRemove.Id);
                        }
                        else
                        {
                            _logger.LogWarning("Failed to remove device {DeviceId} from vessel", deviceToRemove.Id);
                        }
                    }
                }

                // Process device connections from the DTO
                if (dto.DeviceConnections != null && dto.DeviceConnections.Count > 0)
                {
                    _logger.LogInformation("Processing {ConnectionCount} device connections from DTO",
                        dto.DeviceConnections.Count);

                    // Get existing connections
                    var existingConnections = vessel.DeviceConnections.ToList();

                    // Create a set of existing connection keys for quick lookup
                    var existingConnectionKeys = existingConnections
                        .Select(c => $"{c.ConnectedDeviceId}:{c.InterfaceDeviceId}")
                        .ToHashSet();

                    // Process each connection from the DTO
                    foreach (var connectionDto in dto.DeviceConnections)
                    {
                        var connectionKey = $"{connectionDto.ConnectedDeviceId}:{connectionDto.InterfaceDeviceId}";

                        // Check if both devices exist in the vessel
                        var connectedDevice = vessel.GetAllDevices().FirstOrDefault(d => d.Id == connectionDto.ConnectedDeviceId);
                        var interfaceDevice = vessel.GetAllDevices().FirstOrDefault(d => d.Id == connectionDto.InterfaceDeviceId);

                        if (connectedDevice == null || interfaceDevice == null)
                        {
                            _logger.LogWarning("Cannot create connection: device not found. Connected: {ConnectedExists}, Interface: {InterfaceExists}",
                                connectedDevice != null, interfaceDevice != null);
                            continue;
                        }

                        if (existingConnectionKeys.Contains(connectionKey))
                        {
                            // Connection already exists, update it
                            _logger.LogDebug("Connection from {ConnectedId} to {InterfaceId} already exists, updating",
                                connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);

                            vessel.UpdateDeviceInterfaceConnection(
                                connectionDto.ConnectedDeviceId,
                                connectionDto.InterfaceDeviceId,
                                connectionDto.Type,
                                connectionDto.Direction);
                        }
                        else
                        {
                            // New connection, create it
                            _logger.LogDebug("Creating new connection from {ConnectedId} to {InterfaceId}",
                                connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);

                            try
                            {
                                vessel.ConnectDeviceToInterface(
                                    connectionDto.ConnectedDeviceId,
                                    connectionDto.InterfaceDeviceId,
                                    connectionDto.Type,
                                    connectionDto.Direction);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogWarning(ex, "Failed to create connection from {ConnectedId} to {InterfaceId}",
                                    connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);
                            }
                        }
                    }

                    // Check for connections to remove (in vessel but not in DTO)
                    var dtoConnectionKeys = dto.DeviceConnections
                        .Select(c => $"{c.ConnectedDeviceId}:{c.InterfaceDeviceId}")
                        .ToHashSet();

                    foreach (var connection in existingConnections)
                    {
                        var connectionKey = $"{connection.ConnectedDeviceId}:{connection.InterfaceDeviceId}";

                        if (!dtoConnectionKeys.Contains(connectionKey))
                        {
                            // Connection exists in vessel but not in DTO, remove it
                            _logger.LogDebug("Removing connection from {ConnectedId} to {InterfaceId}",
                                connection.ConnectedDeviceId, connection.InterfaceDeviceId);

                            vessel.DisconnectDeviceFromInterface(
                                connection.ConnectedDeviceId,
                                connection.InterfaceDeviceId);
                        }
                    }
                }

                // Also process connections from individual device DTOs
                foreach (var deviceDto in dto.GetAllDevices())
                {
                    if (deviceDto.Connections != null && deviceDto.Connections.Count > 0)
                    {
                        var device = vessel.GetAllDevices().FirstOrDefault(d => d.Id == deviceDto.Id);
                        if (device != null)
                        {
                            _logger.LogInformation("Processing {ConnectionCount} connections for device {DeviceId}",
                                deviceDto.Connections.Count, deviceDto.Id);

                            // Get existing connections for this device
                            var existingConnections = vessel.GetOutgoingConnections(deviceDto.Id).ToList();
                            var existingTargetIds = existingConnections.Select(c => c.InterfaceDeviceId).ToHashSet();

                            // Process each connection
                            foreach (var connectionDto in deviceDto.Connections)
                            {
                                // Check if the interface device exists
                                var interfaceDevice = vessel.GetAllDevices().FirstOrDefault(d => d.Id == connectionDto.InterfaceDeviceId);
                                if (interfaceDevice == null)
                                {
                                    _logger.LogWarning("Interface device {InterfaceId} not found, skipping connection",
                                        connectionDto.InterfaceDeviceId);
                                    continue;
                                }

                                if (existingTargetIds.Contains(connectionDto.InterfaceDeviceId))
                                {
                                    // Connection exists, update it
                                    _logger.LogDebug("Updating connection from {ConnectedId} to {InterfaceId}",
                                        deviceDto.Id, connectionDto.InterfaceDeviceId);

                                    vessel.UpdateDeviceInterfaceConnection(
                                        deviceDto.Id,
                                        connectionDto.InterfaceDeviceId,
                                        connectionDto.Type,
                                        connectionDto.Direction);
                                }
                                else
                                {
                                    // New connection, create it
                                    _logger.LogDebug("Creating new connection from {ConnectedId} to {InterfaceId}",
                                        deviceDto.Id, connectionDto.InterfaceDeviceId);

                                    try
                                    {
                                        vessel.ConnectDeviceToInterface(
                                            deviceDto.Id,
                                            connectionDto.InterfaceDeviceId,
                                            connectionDto.Type,
                                            connectionDto.Direction);
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogWarning(ex, "Failed to create connection from {ConnectedId} to {InterfaceId}",
                                            deviceDto.Id, connectionDto.InterfaceDeviceId);
                                    }
                                }
                            }

                            // Remove connections that are not in the DTO
                            var dtoTargetIds = deviceDto.Connections.Select(c => c.InterfaceDeviceId).ToHashSet();

                            foreach (var connection in existingConnections)
                            {
                                if (!dtoTargetIds.Contains(connection.InterfaceDeviceId))
                                {
                                    // Connection exists but not in DTO, remove it
                                    _logger.LogDebug("Removing connection from {ConnectedId} to {InterfaceId}",
                                        deviceDto.Id, connection.InterfaceDeviceId);

                                    vessel.DisconnectDeviceFromInterface(
                                        deviceDto.Id,
                                        connection.InterfaceDeviceId);
                                }
                            }
                        }
                    }
                }

                // Ensure all device connections are properly synced
                SyncDeviceConnectionsWithVessel(dto, vessel);

                // Update in repository
                _vesselRepository.Update(vessel);

                // Map back to DTO
                var updatedDto = _mapper.MapToDto(vessel);

                // Log the number of devices in the DTO after mapping
                _logger.LogInformation("DTO has {DeviceCount} devices after mapping",
                    updatedDto.GetAllDevices().Count);

                // Log the number of connections
                _logger.LogInformation("DTO has {ConnectionCount} connections after mapping",
                    updatedDto.DeviceConnections.Count);

                return ServiceResult<VesselDto>.CreateSuccess(updatedDto);
            }, "UpdateVessel");
        }

        /// <summary>
        /// Deletes a vessel.
        /// </summary>
        /// <param name="id">The ID of the vessel to delete</param>
        /// <returns>A service result indicating success or failure</returns>
        public override async Task<ServiceResult> DeleteAsync(Guid id)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Deleting vessel with ID {VesselId}", id);

                // Check if vessel exists
                if (!await _vesselRepository.ExistsAsync(id))
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", id);
                    return ServiceResult.CreateFailure($"Vessel with ID {id} not found");
                }

                // Delete from repository
                await _vesselRepository.DeleteAsync(id);

                return ServiceResult.CreateSuccess();
            }, "DeleteVessel");
        }

        /// <summary>
        /// Adds a device to a vessel.
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel to add the device to</param>
        /// <param name="deviceDto">The device DTO containing the data for the new device</param>
        /// <returns>A service result containing the created device DTO</returns>
        public override async Task<ServiceResult<DeviceDto>> AddDeviceAsync(Guid vehicleId, DeviceDto deviceDto)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Adding device to vessel {VesselId}", vehicleId);

                // Get vessel
                var vessel = await _vesselRepository.GetWithDevicesAsync(vehicleId);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", vehicleId);
                    return ServiceResult<DeviceDto>.CreateFailure($"Vessel with ID {vehicleId} not found");
                }

                // Check if this is a device with connections
                if (deviceDto.Connections != null && deviceDto.Connections.Count > 0)
                {
                    _logger.LogInformation("Device has {ConnectionCount} connections, creating device with connections",
                        deviceDto.Connections.Count);

                    // Create the main device
                    var device = _deviceFactory.CreateDevice(deviceDto.DeviceRole);

                    // Set properties from DTO using the mapper
                    _deviceMapper.UpdateEntityFromDto(device, deviceDto);

                    // Set the vehicle ID
                    device.VehicleId = vessel.Id;

                    // Add to vessel
                    if (vessel.AddDevice(device))
                    {
                        // Add to repository
                        await _unitOfWork.Devices.AddAsync(device);
                        _logger.LogDebug("Added device {DeviceId} to vessel", device.Id);

                        // Process connections
                        foreach (var connectionDto in deviceDto.Connections)
                        {
                            // Create a connection in the vessel
                            vessel.ConnectDeviceToInterface(
                                device.Id,
                                connectionDto.InterfaceDeviceId,
                                connectionDto.Type,
                                connectionDto.Direction);

                            _logger.LogDebug("Added connection from {ConnectedId} to {InterfaceId}",
                                device.Id, connectionDto.InterfaceDeviceId);
                        }
                    }
                    else
                    {
                        _logger.LogWarning("Failed to add device to vessel {VesselId}", vehicleId);
                        return ServiceResult<DeviceDto>.CreateFailure($"Failed to add device to vessel");
                    }

                    var resultDto = _deviceMapper.ToDto(device);
                    return ServiceResult<DeviceDto>.CreateSuccess(resultDto);
                }
                else
                {
                    // Simple case: Create a single device without connections
                    var device = _deviceFactory.CreateDevice(deviceDto.DeviceRole);

                    // Set properties from DTO using the mapper
                    _deviceMapper.UpdateEntityFromDto(device, deviceDto);

                    // Set the vehicle ID
                    device.VehicleId = vessel.Id;

                    // Add to vessel
                    if (!vessel.AddDevice(device))
                    {
                        _logger.LogWarning("Failed to add device to vessel {VesselId}", vehicleId);
                        return ServiceResult<DeviceDto>.CreateFailure($"Failed to add device to vessel");
                    }

                    // Add to repository
                    await _unitOfWork.Devices.AddAsync(device);

                    // Map to DTO
                    var resultDto = _deviceMapper.ToDto(device);
                    return ServiceResult<DeviceDto>.CreateSuccess(resultDto);
                }
            }, "AddDeviceToVessel");
        }

        /// <summary>
        /// Updates a device in a vessel.
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel containing the device</param>
        /// <param name="deviceId">The ID of the device to update</param>
        /// <param name="deviceDto">The device DTO containing the updated data</param>
        /// <returns>A service result containing the updated device DTO</returns>
        public override async Task<ServiceResult<DeviceDto>> UpdateDeviceAsync(Guid vehicleId, Guid deviceId, DeviceDto deviceDto)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Updating device {DeviceId} for vessel {VesselId}", deviceId, vehicleId);

                // Get vessel
                var vessel = await _vesselRepository.GetWithDevicesAsync(vehicleId);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", vehicleId);
                    return ServiceResult<DeviceDto>.CreateFailure($"Vessel with ID {vehicleId} not found");
                }

                // Find device in vessel
                var device = vessel.GetAllDevices().FirstOrDefault(d => d.Id == deviceId);
                if (device == null)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found in vessel {VesselId}", deviceId, vehicleId);
                    return ServiceResult<DeviceDto>.CreateFailure($"Device with ID {deviceId} not found in vessel");
                }

                // Update basic device properties using the mapper
                _deviceMapper.UpdateEntityFromDto(device, deviceDto);

                // Handle device connections if provided
                if (deviceDto.Connections != null && deviceDto.Connections.Count > 0)
                {
                    _logger.LogInformation("Updating device connections for device {DeviceId}", deviceId);

                    // Get current connections
                    var currentConnections = vessel.GetOutgoingConnections(deviceId).ToList();
                    var currentTargetIds = currentConnections.Select(c => c.InterfaceDeviceId).ToList();

                    // Get new connections from the DTO
                    var newConnections = deviceDto.Connections
                        .Where(c => c.InterfaceDeviceId != Guid.Empty)
                        .ToList();

                    var newTargetIds = newConnections.Select(c => c.InterfaceDeviceId).ToList();

                    // Remove connections that are not in the new list
                    foreach (var connection in currentConnections)
                    {
                        if (!newTargetIds.Contains(connection.InterfaceDeviceId))
                        {
                            await DisconnectDeviceFromInterfaceAsync(vehicleId, deviceId, connection.InterfaceDeviceId);
                        }
                    }

                    // Add or update new connections
                    foreach (var connection in newConnections)
                    {
                        if (currentTargetIds.Contains(connection.InterfaceDeviceId))
                        {
                            // Update existing connection
                            await UpdateDeviceInterfaceConnectionAsync(vehicleId, deviceId, connection.InterfaceDeviceId,
                                connection.Type, connection.Direction);
                        }
                        else
                        {
                            // Add new connection
                            await ConnectDeviceToInterfaceAsync(vehicleId, deviceId, connection.InterfaceDeviceId,
                                connection.Type, connection.Direction);
                        }
                    }
                }

                // Ensure all device connections are properly synced with the vessel
                // This is important because the device connections might have been updated in the UI
                if (deviceDto.Connections != null && deviceDto.Connections.Any())
                {
                    _logger.LogInformation("Syncing device connections from device DTO to vessel");

                    // Create a list of connections for the SyncDeviceConnectionsWithVessel method
                    var connectionsToSync = deviceDto.Connections.Select(c => new DeviceConnectionDto
                    {
                        ConnectedDeviceId = c.ConnectedDeviceId,
                        InterfaceDeviceId = c.InterfaceDeviceId,
                        Type = c.Type,
                        Direction = c.Direction
                    }).ToList();

                    // Create a temporary VesselDto with just these connections
                    var tempVesselDto = new VesselDto
                    {
                        DeviceConnections = connectionsToSync
                    };

                    // Sync the connections
                    SyncDeviceConnectionsWithVessel(tempVesselDto, vessel);
                }

                // Update vessel in repository
                _vesselRepository.Update(vessel);

                // Map to DTO
                var resultDto = _deviceMapper.ToDto(device);

                return ServiceResult<DeviceDto>.CreateSuccess(resultDto);
            }, "UpdateVesselDevice");
        }

        /// <summary>
        /// Removes a device from a vessel.
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel containing the device</param>
        /// <param name="deviceId">The ID of the device to remove</param>
        /// <returns>A service result indicating success or failure</returns>
        public override async Task<ServiceResult> RemoveDeviceAsync(Guid vehicleId, Guid deviceId)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Removing device {DeviceId} from vessel {VesselId}", deviceId, vehicleId);

                // Get vessel
                var vessel = await _vesselRepository.GetWithDevicesAsync(vehicleId);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", vehicleId);
                    return ServiceResult.CreateFailure($"Vessel with ID {vehicleId} not found");
                }

                // Find device in vessel
                var device = vessel.GetAllDevices().FirstOrDefault(d => d.Id == deviceId);
                if (device == null)
                {
                    _logger.LogWarning("Device with ID {DeviceId} not found in vessel {VesselId}", deviceId, vehicleId);
                    return ServiceResult.CreateFailure($"Device with ID {deviceId} not found in vessel");
                }

                // Remove all connections for this device first
                int connectionsRemoved = vessel.RemoveAllConnectionsForDevice(deviceId);
                _logger.LogInformation("Removed {ConnectionCount} connections for device {DeviceId}",
                    connectionsRemoved, deviceId);

                // Remove the device from vessel
                if (!vessel.RemoveDevice(device))
                {
                    _logger.LogWarning("Failed to remove device {DeviceId} from vessel {VesselId}", deviceId, vehicleId);
                    return ServiceResult.CreateFailure($"Failed to remove device from vessel");
                }

                // Update vessel in repository
                _vesselRepository.Update(vessel);

                // Delete device from repository
                await _unitOfWork.Devices.DeleteAsync(deviceId);

                return ServiceResult.CreateSuccess();
            }, "RemoveDeviceFromVessel");
        }



        /// <summary>
        /// Copies a vessel.
        /// </summary>
        /// <param name="sourceId">The ID of the source vessel to copy</param>
        /// <param name="newName">The name for the new vessel</param>
        /// <returns>A service result containing the copied vessel DTO</returns>
        public async Task<ServiceResult<VesselDto>> CopyVesselAsync(Guid sourceId, string newName)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Copying vessel with ID {VesselId} with new name {NewName}", sourceId, newName);

                // Get source vessel with devices
                var sourceVessel = await _vesselRepository.GetWithDevicesAsync(sourceId);
                if (sourceVessel == null)
                {
                    _logger.LogWarning("Source vessel with ID {VesselId} not found", sourceId);
                    return ServiceResult<VesselDto>.CreateFailure($"Source vessel with ID {sourceId} not found");
                }

                // Create new vessel using copy constructor
                // Create new vessel using copy constructor
                // This will copy all devices and their connections
                var newVessel = new Vessel(sourceVessel)
                {
                    Name = newName
                };

                _logger.LogInformation("Created new vessel with {DeviceCount} devices and {ConnectionCount} connections",
                    newVessel.GetAllDevices().Count(), newVessel.DeviceConnections.Count);

                // Add vessel to repository
                await _vesselRepository.AddAsync(newVessel);

                // Add all devices to repository
                foreach (var device in newVessel.GetAllDevices())
                {
                    await _unitOfWork.Devices.AddAsync(device);
                    _logger.LogDebug("Added device {DeviceId} to repository", device.Id);
                }

                // Map to DTO
                var vesselDto = _mapper.MapToDto(newVessel);

                return ServiceResult<VesselDto>.CreateSuccess(vesselDto);
            }, "CopyVessel");
        }





        /// <summary>
        /// Gets all devices associated with a specific vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle.</param>
        /// <returns>A service result containing a collection of device DTOs.</returns>
        public async Task<ServiceResult<IEnumerable<DeviceDto>>> GetDevicesForVehicleAsync(Guid vehicleId)
        {
            try
            {
                _logger.LogInformation("Getting devices for vessel {VesselId}", vehicleId);

                // Get vessel with devices
                var vessel = await _vesselRepository.GetWithDevicesAsync(vehicleId);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", vehicleId);
                    return ServiceResult<IEnumerable<DeviceDto>>.CreateFailure($"Vessel with ID {vehicleId} not found");
                }

                // Get all devices
                var devices = vessel.GetAllDevices();

                // Map to DTOs
                var deviceDtos = devices.Select(_deviceMapper.ToDto).ToList();

                return ServiceResult<IEnumerable<DeviceDto>>.CreateSuccess(deviceDtos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting devices for vessel {VesselId}", vehicleId);
                return ServiceResult<IEnumerable<DeviceDto>>.CreateFailure($"Error getting devices: {ex.Message}");
            }
        }

        /// <summary>
        /// Connects a device to an interface device in a vessel.
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel containing the devices</param>
        /// <param name="connectedDeviceId">The ID of the connected device</param>
        /// <param name="interfaceDeviceId">The ID of the interface device</param>
        /// <param name="type">The type of connection</param>
        /// <param name="direction">The direction of data flow</param>
        /// <returns>A service result indicating success or failure</returns>
        public override async Task<ServiceResult> ConnectDeviceToInterfaceAsync(Guid vehicleId, Guid connectedDeviceId, Guid interfaceDeviceId, ConnectionType type, ConnectionDirection direction)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Adding connection from device {ConnectedDeviceId} to device {InterfaceDeviceId} in vessel {VesselId}",
                    connectedDeviceId, interfaceDeviceId, vehicleId);

                // Get vessel with devices
                var vessel = await _vesselRepository.GetWithDevicesAsync(vehicleId);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", vehicleId);
                    return ServiceResult.CreateFailure($"Vessel with ID {vehicleId} not found");
                }

                // Find connected device in vessel
                var connectedDevice = vessel.GetAllDevices().FirstOrDefault(d => d.Id == connectedDeviceId);
                if (connectedDevice == null)
                {
                    _logger.LogWarning("Connected device with ID {ConnectedDeviceId} not found in vessel {VesselId}", connectedDeviceId, vehicleId);
                    return ServiceResult.CreateFailure($"Connected device with ID {connectedDeviceId} not found in vessel");
                }

                // Find interface device in vessel
                var interfaceDevice = vessel.GetAllDevices().FirstOrDefault(d => d.Id == interfaceDeviceId);
                if (interfaceDevice == null)
                {
                    _logger.LogWarning("Interface device with ID {InterfaceDeviceId} not found in vessel {VesselId}", interfaceDeviceId, vehicleId);
                    return ServiceResult.CreateFailure($"Interface device with ID {interfaceDeviceId} not found in vessel");
                }

                // Check if connection already exists
                if (vessel.DeviceInterfaceConnectionExists(connectedDeviceId, interfaceDeviceId))
                {
                    _logger.LogInformation("Connection from {ConnectedDeviceId} to {InterfaceDeviceId} already exists", connectedDeviceId, interfaceDeviceId);
                    return ServiceResult.CreateSuccess();
                }

                // Create the connection
                try
                {
                    if (!vessel.ConnectDeviceToInterface(connectedDeviceId, interfaceDeviceId, type, direction))
                    {
                        _logger.LogWarning("Failed to create connection from {ConnectedDeviceId} to {InterfaceDeviceId}", connectedDeviceId, interfaceDeviceId);
                        return ServiceResult.CreateFailure($"Failed to create connection between devices");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating connection from {ConnectedDeviceId} to {InterfaceDeviceId}", connectedDeviceId, interfaceDeviceId);
                    return ServiceResult.CreateFailure($"Error creating connection: {ex.Message}");
                }

                // Update vessel in repository
                _vesselRepository.Update(vessel);

                _logger.LogInformation("Successfully created connection from {ConnectedDeviceId} to {InterfaceDeviceId}", connectedDeviceId, interfaceDeviceId);
                return ServiceResult.CreateSuccess();
            }, "AddDeviceConnection");
        }

        /// <summary>
        /// Disconnects a device from an interface device in a vessel.
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel containing the devices</param>
        /// <param name="connectedDeviceId">The ID of the connected device</param>
        /// <param name="interfaceDeviceId">The ID of the interface device</param>
        /// <returns>A service result indicating success or failure</returns>
        public override async Task<ServiceResult> DisconnectDeviceFromInterfaceAsync(Guid vehicleId, Guid connectedDeviceId, Guid interfaceDeviceId)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Removing connection from device {ConnectedDeviceId} to device {InterfaceDeviceId} in vessel {VesselId}",
                    connectedDeviceId, interfaceDeviceId, vehicleId);

                // Get vessel with devices
                var vessel = await _vesselRepository.GetWithDevicesAsync(vehicleId);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", vehicleId);
                    return ServiceResult.CreateFailure($"Vessel with ID {vehicleId} not found");
                }

                // Check if connection exists
                if (!vessel.DeviceInterfaceConnectionExists(connectedDeviceId, interfaceDeviceId))
                {
                    _logger.LogInformation("Connection from {ConnectedDeviceId} to {InterfaceDeviceId} does not exist", connectedDeviceId, interfaceDeviceId);
                    return ServiceResult.CreateSuccess();
                }

                // Remove the connection
                if (!vessel.DisconnectDeviceFromInterface(connectedDeviceId, interfaceDeviceId))
                {
                    _logger.LogWarning("Failed to remove connection from {ConnectedDeviceId} to {InterfaceDeviceId}", connectedDeviceId, interfaceDeviceId);
                    return ServiceResult.CreateFailure($"Failed to remove connection between devices");
                }

                // Update vessel in repository
                _vesselRepository.Update(vessel);

                _logger.LogInformation("Successfully removed connection from {ConnectedDeviceId} to {InterfaceDeviceId}", connectedDeviceId, interfaceDeviceId);
                return ServiceResult.CreateSuccess();
            }, "RemoveDeviceConnection");
        }

        /// <summary>
        /// Updates a connection between a device and an interface device in a vessel.
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel containing the devices</param>
        /// <param name="connectedDeviceId">The ID of the connected device</param>
        /// <param name="interfaceDeviceId">The ID of the interface device</param>
        /// <param name="type">The new connection type</param>
        /// <param name="direction">The new connection direction</param>
        /// <returns>A service result indicating success or failure</returns>
        public override async Task<ServiceResult> UpdateDeviceInterfaceConnectionAsync(Guid vehicleId, Guid connectedDeviceId, Guid interfaceDeviceId, ConnectionType type, ConnectionDirection direction)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Updating connection from device {ConnectedDeviceId} to device {InterfaceDeviceId} in vessel {VesselId}",
                    connectedDeviceId, interfaceDeviceId, vehicleId);

                // Get vessel with devices
                var vessel = await _vesselRepository.GetWithDevicesAsync(vehicleId);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", vehicleId);
                    return ServiceResult.CreateFailure($"Vessel with ID {vehicleId} not found");
                }

                // Check if connection exists
                if (!vessel.DeviceInterfaceConnectionExists(connectedDeviceId, interfaceDeviceId))
                {
                    _logger.LogWarning("Connection from {ConnectedDeviceId} to {InterfaceDeviceId} does not exist", connectedDeviceId, interfaceDeviceId);
                    return ServiceResult.CreateFailure($"Connection does not exist between devices");
                }

                // Update the connection
                if (!vessel.UpdateDeviceInterfaceConnection(connectedDeviceId, interfaceDeviceId, type, direction))
                {
                    _logger.LogWarning("Failed to update connection from {ConnectedDeviceId} to {InterfaceDeviceId}", connectedDeviceId, interfaceDeviceId);
                    return ServiceResult.CreateFailure($"Failed to update connection between devices");
                }

                // Update vessel in repository
                _vesselRepository.Update(vessel);

                _logger.LogInformation("Successfully updated connection from {ConnectedDeviceId} to {InterfaceDeviceId}", connectedDeviceId, interfaceDeviceId);
                return ServiceResult.CreateSuccess();
            }, "UpdateDeviceConnection");
        }

        /// <summary>
        /// Gets all connections for a device in a vessel.
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel containing the device</param>
        /// <param name="deviceId">The ID of the device to get connections for</param>
        /// <returns>A service result containing a collection of device connection DTOs</returns>
        public override async Task<ServiceResult<IEnumerable<DeviceConnectionDto>>> GetDeviceConnectionsAsync(Guid vehicleId, Guid deviceId)
        {
            _logger.LogInformation("Getting connections for device {DeviceId} in vessel {VesselId}",
                deviceId, vehicleId);

            // Get vessel with devices
            var vessel = await _vesselRepository.GetWithDevicesAsync(vehicleId);
            if (vessel == null)
            {
                _logger.LogWarning("Vessel with ID {VesselId} not found", vehicleId);
                return ServiceResult<IEnumerable<DeviceConnectionDto>>.CreateFailure($"Vessel with ID {vehicleId} not found");
            }

            // Find device in vessel
            var device = vessel.GetAllDevices().FirstOrDefault(d => d.Id == deviceId);
            if (device == null)
            {
                _logger.LogWarning("Device with ID {DeviceId} not found in vessel {VesselId}", deviceId, vehicleId);
                return ServiceResult<IEnumerable<DeviceConnectionDto>>.CreateFailure($"Device with ID {deviceId} not found in vessel");
            }

            // Get all connections for this device
            var connections = vessel.GetDeviceConnections(deviceId).ToList();
            _logger.LogInformation("Found {ConnectionCount} connections for device {DeviceId}", connections.Count, deviceId);

            // Map to DTOs
            var connectionDtos = connections.Select(c => new DeviceConnectionDto
            {
                ConnectedDeviceId = c.ConnectedDeviceId,
                InterfaceDeviceId = c.InterfaceDeviceId,
                Type = c.Type,
                Direction = c.Direction
            }).ToList();

            return ServiceResult<IEnumerable<DeviceConnectionDto>>.CreateSuccess(connectionDtos);
        }



        /// <summary>
        /// Creates a copy of an existing vessel.
        /// </summary>
        /// <param name="sourceId">The ID of the source vessel to copy</param>
        /// <param name="newName">The name for the new vessel</param>
        /// <returns>A service result containing the ID of the created vessel</returns>
        public override async Task<ServiceResult<Guid>> CopyVehicleAsync(Guid sourceId, string newName)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Creating copy of vessel {VesselId} with new name {NewName}", sourceId, newName);

                // Get source vessel with devices
                var sourceVessel = await _vesselRepository.GetWithDevicesAsync(sourceId);
                if (sourceVessel == null)
                {
                    _logger.LogWarning("Source vessel with ID {VesselId} not found", sourceId);
                    return ServiceResult<Guid>.CreateFailure($"Source vessel with ID {sourceId} not found");
                }

                // Create new vessel using the Clone method
                var newVessel = (Vessel)sourceVessel.Clone();

                // Update the name
                newVessel.Name = newName;

                _logger.LogInformation("Created new vessel with {DeviceCount} devices and {ConnectionCount} connections",
                    newVessel.GetAllDevices().Count(), newVessel.DeviceConnections.Count);

                // Add to repository
                await _vesselRepository.AddAsync(newVessel);

                return ServiceResult<Guid>.CreateSuccess(newVessel.Id);
            }, "CopyVessel");
        }

        /// <summary>
        /// Creates copies of devices by their IDs without persisting to the database.
        /// </summary>
        /// <param name="deviceIds">The IDs of the devices to copy</param>
        /// <param name="connections">Optional list of connections to maintain between copied devices</param>
        /// <returns>A service result containing the list of copied device DTOs</returns>
        public override async Task<ServiceResult<List<DeviceDto>>> CopyDevicesAsync(List<Guid> deviceIds, List<DeviceConnectionDto>? connections = null)
        {
            try
            {
                _logger.LogInformation("Copying {DeviceCount} devices", deviceIds.Count);

                // Get source devices
                var sourceDevices = await _unitOfWork.Devices.GetDevicesByIdsAsync(deviceIds);

                if (sourceDevices.Count == 0)
                {
                    _logger.LogWarning("No valid source devices found");
                    return ServiceResult<List<DeviceDto>>.CreateFailure("No valid source devices found");
                }

                _logger.LogInformation("Found {Count} out of {RequestedCount} source devices",
                    sourceDevices.Count, deviceIds.Count);

                // Create a mapping of original device IDs to copied device IDs
                var deviceMap = new Dictionary<Guid, Guid>();
                var resultDevices = new List<DeviceDto>();

                // Copy each device
                foreach (var sourceDevice in sourceDevices)
                {
                    // Create a copy of the device
                    var deviceCopy = sourceDevice.Clone();

                    // Generate a new ID for the device copy
                    var newId = Guid.NewGuid();

                    // Map to DTO
                    var deviceDto = _deviceMapper.ToDto(deviceCopy);

                    // Update the ID in the DTO
                    deviceDto.Id = newId;

                    // Determine the device group for this device
                    var groupType = GetDeviceGroupTypeForRole(deviceDto.DeviceRole);
                    deviceDto.DeviceGroupType = groupType;

                    // Add to the result list
                    resultDevices.Add(deviceDto);

                    // Add to the device map for connection handling
                    deviceMap[sourceDevice.Id] = deviceDto.Id;

                    _logger.LogDebug("Copied device {SourceId} to {TargetId}",
                        sourceDevice.Id, deviceDto.Id);
                }

                // Process connections if provided
                if (connections != null && connections.Count > 0 && deviceMap.Count > 0)
                {
                    // Create a dictionary to quickly look up devices by ID
                    var deviceDtoMap = resultDevices.ToDictionary(d => d.Id);

                    foreach (var connection in connections)
                    {
                        // Check if both connected and interface devices were copied
                        if (deviceMap.TryGetValue(connection.ConnectedDeviceId, out var newConnectedId) &&
                            deviceMap.TryGetValue(connection.InterfaceDeviceId, out var newInterfaceId))
                        {
                            // Create a new connection between the copied devices
                            var connectionDto = new DeviceConnectionDto
                            {
                                ConnectedDeviceId = newConnectedId,
                                InterfaceDeviceId = newInterfaceId,
                                Type = connection.Type,
                                Direction = connection.Direction
                            };

                            // Add to the connected device's connections
                            if (deviceDtoMap.TryGetValue(newConnectedId, out var connectedDeviceDto))
                            {
                                connectedDeviceDto.Connections.Add(connectionDto);
                            }

                            _logger.LogDebug("Created connection from {ConnectedId} to {InterfaceId}",
                                newConnectedId, newInterfaceId);
                        }
                    }
                }

                return ServiceResult<List<DeviceDto>>.CreateSuccess(resultDevices);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error copying devices");
                return ServiceResult<List<DeviceDto>>.CreateFailure($"Error copying devices: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the device group type for a specific device role using the DeviceGroups enum.
        /// </summary>
        /// <param name="role">The device role</param>
        /// <returns>The DeviceGroups enum value for this role</returns>
        private static DeviceGroups GetDeviceGroupTypeForRole(DeviceRole role)
        {
            // Find the device group that allows this role by checking all enum values
            foreach (var groupType in Enum.GetValues<DeviceGroups>())
            {
                if (groupType.AllowsRole(role))
                {
                    return groupType;
                }
            }

            // Default fallback to CameraGroup if no match found
            return DeviceGroups.CameraGroup;
        }

        /// <summary>
        /// Synchronizes device connections from the DTO to the vessel entity.
        /// This ensures that all connections in the DTO are properly reflected in the vessel.
        /// </summary>
        /// <param name="dto">The vessel DTO containing the connections</param>
        /// <param name="vessel">The vessel entity to update</param>
        private void SyncDeviceConnectionsWithVessel(VesselDto dto, Vessel vessel)
        {
            _logger.LogInformation("Syncing device connections from DTO to vessel");

            // Get existing connections in the vessel
            var existingConnections = vessel.DeviceConnections.ToList();

            // Create a lookup for quick checking
            var existingConnectionLookup = existingConnections
                .Select(c => $"{c.ConnectedDeviceId}:{c.InterfaceDeviceId}")
                .ToHashSet();

            // Create a lookup for connections in the DTO
            var dtoConnectionLookup = dto.DeviceConnections?
                .Where(c => c.ConnectedDeviceId != Guid.Empty &&
                           c.InterfaceDeviceId != Guid.Empty &&
                           c.ConnectedDeviceId != c.InterfaceDeviceId)
                .Select(c => $"{c.ConnectedDeviceId}:{c.InterfaceDeviceId}")
                .ToHashSet() ?? new HashSet<string>();

            _logger.LogInformation("Found {ExistingCount} existing connections and {DtoCount} connections in DTO",
                existingConnectionLookup.Count, dtoConnectionLookup.Count);

            // Log all existing connections for debugging
            foreach (var connection in existingConnections)
            {
                _logger.LogDebug("Existing connection: {ConnectedId} -> {InterfaceId}",
                    connection.ConnectedDeviceId, connection.InterfaceDeviceId);
            }

            // Log all DTO connections for debugging
            if (dto.DeviceConnections != null)
            {
                foreach (var connection in dto.DeviceConnections)
                {
                    _logger.LogDebug("DTO connection: {ConnectedId} -> {InterfaceId}",
                        connection.ConnectedDeviceId, connection.InterfaceDeviceId);
                }
            }

            // First, remove connections that exist in the vessel but not in the DTO
            foreach (var connection in existingConnections)
            {
                string connectionKey = $"{connection.ConnectedDeviceId}:{connection.InterfaceDeviceId}";

                if (!dtoConnectionLookup.Contains(connectionKey))
                {
                    // Connection exists in vessel but not in DTO, remove it
                    _logger.LogInformation("Removing connection from {ConnectedId} to {InterfaceId} (not in DTO)",
                        connection.ConnectedDeviceId, connection.InterfaceDeviceId);

                    vessel.DisconnectDeviceFromInterface(
                        connection.ConnectedDeviceId,
                        connection.InterfaceDeviceId);
                }
            }

            // If there are no connections in the DTO, we're done (all connections have been removed)
            if (dto.DeviceConnections == null || !dto.DeviceConnections.Any())
            {
                _logger.LogInformation("No device connections in DTO to add or update");
                return;
            }

            // Process each connection in the DTO
            foreach (var connectionDto in dto.DeviceConnections)
            {
                // Skip invalid connections
                if (connectionDto.ConnectedDeviceId == Guid.Empty ||
                    connectionDto.InterfaceDeviceId == Guid.Empty ||
                    connectionDto.ConnectedDeviceId == connectionDto.InterfaceDeviceId)
                {
                    _logger.LogWarning("Skipping invalid connection: {ConnectedId} -> {InterfaceId}",
                        connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);
                    continue;
                }

                // Check if the connection already exists
                string connectionKey = $"{connectionDto.ConnectedDeviceId}:{connectionDto.InterfaceDeviceId}";
                if (!existingConnectionLookup.Contains(connectionKey))
                {
                    // Connection doesn't exist, create it
                    try
                    {
                        _logger.LogDebug("Creating new connection from {ConnectedId} to {InterfaceId}",
                            connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);

                        vessel.ConnectDeviceToInterface(
                            connectionDto.ConnectedDeviceId,
                            connectionDto.InterfaceDeviceId,
                            connectionDto.Type,
                            connectionDto.Direction);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to create connection from {ConnectedId} to {InterfaceId}",
                            connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);
                    }
                }
                else
                {
                    // Connection exists, update it if needed
                    var existingConnection = vessel.GetConnection(
                        connectionDto.ConnectedDeviceId,
                        connectionDto.InterfaceDeviceId);

                    if (existingConnection != null &&
                        (existingConnection.Type != connectionDto.Type ||
                         existingConnection.Direction != connectionDto.Direction))
                    {
                        _logger.LogDebug("Updating existing connection from {ConnectedId} to {InterfaceId}",
                            connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);

                        vessel.UpdateDeviceInterfaceConnection(
                            connectionDto.ConnectedDeviceId,
                            connectionDto.InterfaceDeviceId,
                            connectionDto.Type,
                            connectionDto.Direction);
                    }
                }
            }
        }


    }
}
