using Barret.Core.Areas.DeviceGroups;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Devices.Mapping;
using Barret.Services.Core.Areas.Mapping;
using Barret.Services.Core.Areas.Vehicles.Mapping;
using Barret.Services.Core.Areas.Vehicles.Queries;
using Barret.Services.Core.Areas.Vehicles.Repositories;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Reflection;

namespace Barret.Services.Areas.Vehicles.Queries
{
    /// <summary>
    /// Query service implementation for vessels.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="VesselQueryService"/> class.
    /// </remarks>
    /// <param name="dbContext">The database context</param>
    /// <param name="mapper">The entity mapper for converting between entities and DTOs</param>
    /// <param name="deviceMapper">The device mapper for converting between device entities and DTOs</param>
    /// <param name="vesselRepository">The vessel repository</param>
    /// <param name="logger">The logger</param>
    /// <exception cref="ArgumentNullException">Thrown if any parameter is null</exception>
    public class VesselQueryService(
        IBarretDbContext dbContext,
        IVehicleMapper<Vessel, VesselDto> mapper,
        IDeviceMapper deviceMapper,
        IVesselRepository vesselRepository,
        ILogger<VesselQueryService> logger) : IVesselQueryService
    {
        private readonly IBarretDbContext _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        private readonly IVehicleMapper<Vessel, VesselDto> _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        private readonly IDeviceMapper _deviceMapper = deviceMapper ?? throw new ArgumentNullException(nameof(deviceMapper));
        private readonly IVesselRepository _vesselRepository = vesselRepository ?? throw new ArgumentNullException(nameof(vesselRepository));
        private readonly ILogger<VesselQueryService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Gets a vessel with device summaries.
        /// </summary>
        /// <param name="id">The ID of the vessel to retrieve</param>
        /// <returns>The vessel DTO with device summaries if found; otherwise, null</returns>
        public async Task<VesselDto?> GetVesselWithDeviceSummariesAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Getting vessel with device summaries for ID {VesselId}", id);

                // Get vessel
                var vessel = await _dbContext.Vessels
                    .AsNoTracking()
                    .FirstOrDefaultAsync(v => v.Id == id);

                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", id);
                    return null;
                }

                // Get device counts by role
                var deviceCounts = await _dbContext.Devices
                    .Where(d => d.VehicleId == id)
                    .GroupBy(d => d.DeviceRole)
                    .Select(g => new { Role = g.Key, Count = g.Count() })
                    .ToListAsync();

                // Map to DTO
                var vesselDto = _mapper.MapToDto(vessel);

                // Add device counts to DTO
                foreach (var count in deviceCounts)
                {
                    _logger.LogDebug("Vessel {VesselId} has {Count} devices of role {DeviceRole}",
                        id, count.Count, count.Role);
                }

                _logger.LogInformation("Successfully retrieved vessel {VesselId} with device summaries", id);

                return vesselDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vessel with device summaries for ID {VesselId}", id);
                throw;
            }
        }

        /// <summary>
        /// Gets a vessel with the full device tree.
        /// </summary>
        /// <param name="id">The ID of the vessel to retrieve</param>
        /// <returns>The vessel DTO with the full device tree if found; otherwise, null</returns>
        public async Task<VesselDto?> GetVesselWithDeviceTreeAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Getting vessel with full device tree for ID {VesselId}", id);

                // Get vessel with full device tree
                var vessel = await _vesselRepository.GetWithDeviceTreeAsync(id);
                if (vessel == null)
                {
                    _logger.LogWarning("Vessel with ID {VesselId} not found", id);
                    return null;
                }

                // Map to DTO
                var vesselDto = _mapper.MapToDto(vessel);

                _logger.LogInformation("Successfully retrieved vessel {VesselId} with full device tree", id);

                return vesselDto;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vessel with full device tree for ID {VesselId}", id);
                throw;
            }
        }

        /// <summary>
        /// Searches for vessels based on a filter.
        /// </summary>
        /// <param name="filter">The filter to apply</param>
        /// <returns>A list of vessel DTOs matching the filter</returns>
        public async Task<List<VesselDto>> SearchVesselsAsync(VesselFilterDto filter)
        {
            try
            {
                _logger.LogDebug("Searching for vessels with filter: {Filter}",
                    System.Text.Json.JsonSerializer.Serialize(filter));

                // Use a more explicit query to avoid loading DeviceConnections directly
                // This approach selects only the specific fields we need
                var query = _dbContext.Vessels
                    .AsNoTracking();

                // Apply filters
                if (!string.IsNullOrWhiteSpace(filter.Name))
                {
                    query = query.Where(v => v.Name.Contains(filter.Name));
                }

                if (!string.IsNullOrWhiteSpace(filter.MMSI))
                {
                    query = query.Where(v => v.MMSI.Contains(filter.MMSI));
                }

                if (!string.IsNullOrWhiteSpace(filter.ENI))
                {
                    query = query.Where(v => v.ENI.Contains(filter.ENI));
                }

                // Apply device role filter if specified
                if (filter.DeviceRole.HasValue)
                {
                    var role = filter.DeviceRole.Value;

                    // Get vessel IDs that have devices with the specified role
                    var vesselIds = await _dbContext.Devices
                        .Where(d => d.DeviceRole == role && d.VehicleId.HasValue)
                        .Select(d => d.VehicleId!.Value)
                        .Distinct()
                        .ToListAsync();

                    // Filter vessels by these IDs
                    query = query.Where(v => vesselIds.Contains(v.Id));
                }

                // Apply max results limit if specified
                if (filter.MaxResults.HasValue && filter.MaxResults.Value > 0)
                {
                    query = query.Take(filter.MaxResults.Value);
                }

                // Execute query
                var vessels = await query.ToListAsync();

                // Map to DTOs
                var vesselDtos = vessels.Select(_mapper.MapToDto).ToList();

                _logger.LogInformation("Found {Count} vessels matching filter", vesselDtos.Count);

                return vesselDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching for vessels");
                throw;
            }
        }

        /// <summary>
        /// Gets all vessels with basic information only (optimized for list views).
        /// This method doesn't load devices or device groups, making it much faster for list displays.
        /// </summary>
        /// <returns>A list of vessel DTOs with basic information</returns>
        public async Task<List<VesselDto>> GetAllBasicAsync()
        {
            try
            {
                _logger.LogInformation("Getting all vessels with basic information");

                // Get basic vessel information without loading devices
                var vessels = await _dbContext.Vessels
                    .AsNoTracking()
                    .ToListAsync();

                var vesselDtos = new List<VesselDto>();

                foreach (var vessel in vessels)
                {
                    // Get device count without loading devices
                    var deviceCount = await _dbContext.Devices
                        .Where(d => d.VehicleId == vessel.Id)
                        .CountAsync();

                    // Create basic vessel DTO without device groups and connections
                    var vesselDto = new VesselDto
                    {
                        Id = vessel.Id,
                        VehicleId = vessel.VehicleId,
                        Name = vessel.Name,
                        MMSI = vessel.MMSI,
                        ENI = vessel.ENI,
                        Dimensions = vessel.Dimensions != null ? new DimensionsDto
                        {
                            DistanceGpsToFront = vessel.Dimensions.DistanceGpsToFront,
                            DistanceGpsToBack = vessel.Dimensions.DistanceGpsToBack,
                            DistanceGpsToLeft = vessel.Dimensions.DistanceGpsToLeft,
                            DistanceGpsToRight = vessel.Dimensions.DistanceGpsToRight
                        } : null,
                        DeviceGroups = new Dictionary<DeviceGroups, DeviceGroupDto>(), // Empty for performance
                        DeviceConnections = new List<DeviceConnectionDto>(), // Empty for performance
                        CachedDeviceCount = deviceCount // Store device count for frontend use
                    };

                    vesselDtos.Add(vesselDto);
                }

                _logger.LogInformation("Retrieved {Count} vessels with basic information", vesselDtos.Count);
                return vesselDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vessels with basic information");
                throw;
            }
        }
    }
}
