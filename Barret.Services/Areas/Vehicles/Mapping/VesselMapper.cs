using AutoMapper;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Core.Areas.Vehicles.ValueObjects;
using Barret.Services.Areas.Devices.Mapping;
using Barret.Services.Areas.Devices.Services;
using Barret.Services.Core.Areas.Mapping;
using Barret.Services.Core.Areas.Vehicles.Mapping;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Vehicles.Mapping
{
    /// <summary>
    /// Maps between Vessel entities and VesselDtos with proper domain logic
    /// </summary>
    /// <remarks>
    /// Creates a new instance of VesselMapper
    /// </remarks>
    /// <param name="logger">The logger</param>
    /// <param name="deviceMapper">The device mapper</param>
    /// <param name="mapper">The AutoMapper instance</param>
    /// <param name="deviceGroupMappingService">The device group mapping service</param>
    public class VesselMapper(
        ILogger<VesselMapper> logger,
        DeviceMapper deviceMapper,
        IMapper mapper,
        IDeviceGroupMappingService deviceGroupMappingService) : IEntityMapper<VesselDto, Vessel>, IVehicleMapper<Vessel, VesselDto>
    {
        private readonly ILogger<VesselMapper> _logger = logger;
        private readonly DeviceMapper _deviceMapper = deviceMapper;
        private readonly IMapper _mapper = mapper;
        private readonly IDeviceGroupMappingService _deviceGroupMappingService = deviceGroupMappingService;

        public Vessel MapToEntity(VesselDto dto)
        {
            _logger.LogDebug("Creating new Vessel from VesselDto");

            // Create the vessel with required properties
            var vessel = new Vessel(
                dto.VehicleId,
                dto.ENI ?? string.Empty,
                dto.MMSI,
                dto.Name
            );

            // Add dimensions if provided
            if (dto.Dimensions != null)
            {
                vessel.WithDimensions(new DimensionsFromGpsLocation(
                    dto.Dimensions.DistanceGpsToFront,
                    dto.Dimensions.DistanceGpsToBack,
                    dto.Dimensions.DistanceGpsToLeft,
                    dto.Dimensions.DistanceGpsToRight
                ));
            }

            // Map devices if needed
            if (dto.DeviceGroups != null && MapDevices)
            {
                // Process each device group in the DTO
                foreach (var deviceGroupEntry in dto.DeviceGroups)
                {
                    var groupName = deviceGroupEntry.Key;
                    var deviceGroupDto = deviceGroupEntry.Value;

                    if (deviceGroupDto?.Devices != null)
                    {
                        // Map each device in the group
                        foreach (var deviceDto in deviceGroupDto.Devices)
                        {
                            // Convert the DTO to a domain entity
                            var device = _deviceMapper.ToEntity(deviceDto);

                            // Add the device to the vessel
                            // The vessel will automatically add it to the appropriate device group
                            // based on its type and role using the AddDevice method
                            vessel.AddDevice(device);
                        }
                    }
                }
            }

            // Map device connections from VesselDto.DeviceConnections
            // Since DeviceConnection is an immutable value object, we need to handle it carefully
            // First, get existing connections in the vessel
            var existingConnections = vessel.DeviceConnections.ToList();

            // Create a lookup for quick checking
            var existingConnectionLookup = existingConnections
                .Select(c => $"{c.ConnectedDeviceId}:{c.InterfaceDeviceId}")
                .ToHashSet();

            // Create a lookup for connections in the DTO
            var dtoConnectionLookup = dto.DeviceConnections?
                .Where(c => c.ConnectedDeviceId != Guid.Empty &&
                           c.InterfaceDeviceId != Guid.Empty &&
                           c.ConnectedDeviceId != c.InterfaceDeviceId)
                .Select(c => $"{c.ConnectedDeviceId}:{c.InterfaceDeviceId}")
                .ToHashSet() ?? new HashSet<string>();

            _logger.LogInformation("Mapping connections: {ExistingCount} existing, {DtoCount} in DTO",
                existingConnectionLookup.Count, dtoConnectionLookup.Count);

            // First, remove connections that exist in the vessel but not in the DTO
            foreach (var connection in existingConnections)
            {
                string connectionKey = $"{connection.ConnectedDeviceId}:{connection.InterfaceDeviceId}";

                if (dto.DeviceConnections == null || !dtoConnectionLookup.Contains(connectionKey))
                {
                    // Connection exists in vessel but not in DTO, remove it
                    _logger.LogInformation("Removing connection from {ConnectedId} to {InterfaceId} (not in DTO)",
                        connection.ConnectedDeviceId, connection.InterfaceDeviceId);

                    // Since DeviceConnection is immutable, we need to use the DisconnectDeviceFromInterface method
                    // This method removes the connection from the _deviceConnections collection
                    vessel.DisconnectDeviceFromInterface(
                        connection.ConnectedDeviceId,
                        connection.InterfaceDeviceId);
                }
            }

            // Then, add or update connections from the DTO
            if (dto.DeviceConnections != null && dto.DeviceConnections.Count > 0)
            {
                _logger.LogInformation("Processing {ConnectionCount} connections from VesselDto.DeviceConnections",
                    dto.DeviceConnections.Count);

                foreach (var connectionDto in dto.DeviceConnections)
                {
                    try
                    {
                        // Skip invalid connections
                        if (connectionDto.ConnectedDeviceId == Guid.Empty ||
                            connectionDto.InterfaceDeviceId == Guid.Empty ||
                            connectionDto.ConnectedDeviceId == connectionDto.InterfaceDeviceId)
                        {
                            _logger.LogWarning("Skipping invalid connection: {ConnectedId} -> {InterfaceId}",
                                connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);
                            continue;
                        }

                        // Check if both devices exist in the vessel
                        var connectedDevice = vessel.GetAllDevices().FirstOrDefault(d => d.Id == connectionDto.ConnectedDeviceId);
                        var interfaceDevice = vessel.GetAllDevices().FirstOrDefault(d => d.Id == connectionDto.InterfaceDeviceId);

                        if (connectedDevice == null || interfaceDevice == null)
                        {
                            _logger.LogWarning("Cannot create connection: device not found. Connected: {ConnectedExists}, Interface: {InterfaceExists}",
                                connectedDevice != null, interfaceDevice != null);
                            continue;
                        }

                        // Check if the connection already exists
                        string connectionKey = $"{connectionDto.ConnectedDeviceId}:{connectionDto.InterfaceDeviceId}";
                        if (!existingConnectionLookup.Contains(connectionKey))
                        {
                            // Connection doesn't exist, create it
                            _logger.LogDebug("Creating new connection from {ConnectedId} to {InterfaceId}",
                                connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);

                            // Create a new immutable DeviceConnection object via the ConnectDeviceToInterface method
                            // This method creates a new DeviceConnection and adds it to the _deviceConnections collection
                            vessel.ConnectDeviceToInterface(
                                connectionDto.ConnectedDeviceId,
                                connectionDto.InterfaceDeviceId,
                                connectionDto.Type,
                                connectionDto.Direction);
                        }
                        else
                        {
                            // Connection exists, update it if needed
                            var existingConnection = vessel.GetConnection(
                                connectionDto.ConnectedDeviceId,
                                connectionDto.InterfaceDeviceId);

                            if (existingConnection != null)
                            {
                                // Check if the connection properties need to be updated
                                bool typeChanged = existingConnection.Type != connectionDto.Type;
                                bool directionChanged = existingConnection.Direction != connectionDto.Direction;

                                if (typeChanged || directionChanged)
                                {
                                    _logger.LogDebug("Updating existing connection from {ConnectedId} to {InterfaceId}",
                                        connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);

                                    // Since DeviceConnection is immutable, we need to create a new one via UpdateDeviceInterfaceConnection
                                    vessel.UpdateDeviceInterfaceConnection(
                                        connectionDto.ConnectedDeviceId,
                                        connectionDto.InterfaceDeviceId,
                                        connectionDto.Type,
                                        connectionDto.Direction);
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to process connection from {ConnectedId} to {InterfaceId}",
                            connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);
                    }
                }
            }

            // Also map connections from individual DeviceDto.Connections
            if (dto.DeviceGroups != null)
            {
                foreach (var deviceGroup in dto.DeviceGroups.Values)
                {
                    if (deviceGroup?.Devices != null)
                    {
                        foreach (var deviceDto in deviceGroup.Devices)
                        {
                            if (deviceDto.Connections != null && deviceDto.Connections.Count > 0)
                            {
                                _logger.LogInformation("Mapping {ConnectionCount} connections from device {DeviceId}",
                                    deviceDto.Connections.Count, deviceDto.Id);

                                foreach (var connectionDto in deviceDto.Connections)
                                {
                                    try
                                    {
                                        // Use AutoMapper to create a DeviceConnection from the DTO
                                        var connection = _mapper.Map<DeviceConnection>(connectionDto);

                                        // Only create the connection if it doesn't already exist
                                        if (!vessel.DeviceInterfaceConnectionExists(
                                            connection.ConnectedDeviceId,
                                            connection.InterfaceDeviceId))
                                        {
                                            vessel.ConnectDeviceToInterface(
                                                connection.ConnectedDeviceId,
                                                connection.InterfaceDeviceId,
                                                connection.Type,
                                                connection.Direction);
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger.LogWarning(ex, "Failed to create connection from {ConnectedId} to {InterfaceId}",
                                            connectionDto.ConnectedDeviceId, connectionDto.InterfaceDeviceId);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            return vessel;
        }

        public void UpdateEntityFromDto(Vessel vessel, VesselDto dto)
        {
            _logger.LogDebug("Updating Vessel from VesselDto");

            // Update basic properties if changed - use direct properties instead of non-existent methods
            if (vessel.VehicleId != dto.VehicleId && !string.IsNullOrEmpty(dto.VehicleId))
            {
                vessel.VehicleId = dto.VehicleId;
            }

            if (vessel.Name != dto.Name && !string.IsNullOrEmpty(dto.Name))
            {
                vessel.Name = dto.Name;
            }

            // Update ENI if changed - direct property access
            if (vessel.ENI != dto.ENI)
            {
                vessel.ENI = dto.ENI ?? string.Empty;
            }

            // Update MMSI if changed - direct property access
            if (vessel.MMSI != dto.MMSI && !string.IsNullOrEmpty(dto.MMSI))
            {
                vessel.MMSI = dto.MMSI;
            }

            // Update dimensions if provided - using correct immutable value object pattern
            if (dto.Dimensions != null)
            {
                vessel.WithDimensions(new DimensionsFromGpsLocation(
                    dto.Dimensions.DistanceGpsToFront,
                    dto.Dimensions.DistanceGpsToBack,
                    dto.Dimensions.DistanceGpsToLeft,
                    dto.Dimensions.DistanceGpsToRight
                ));
            }

            // Note: Device updates should be handled separately by the service
            // since they require complex state tracking and database operations
        }

        /// <summary>
        /// Gets or sets whether devices should be mapped when converting DTOs to entities
        /// </summary>
        /// <remarks>
        /// This is optional because sometimes we don't want to include devices when mapping,
        /// especially when the service is going to handle device creation separately.
        /// </remarks>
        public bool MapDevices { get; set; } = true;

        /// <summary>
        /// Maps a vessel entity to a DTO
        /// </summary>
        /// <param name="entity">The vessel entity to map</param>
        /// <returns>The mapped vessel DTO</returns>
        public VesselDto MapToDto(Vessel entity)
        {
            if (entity == null)
            {
                _logger.LogWarning("Cannot map null vessel entity to DTO");
                return new VesselDto();
            }

            _logger.LogDebug("Mapping vessel entity {VesselId} to DTO", entity.Id);

            // Use AutoMapper for basic properties
            var vesselDto = _mapper.Map<VesselDto>(entity);

            // Ensure dimensions are initialized
            if (vesselDto.Dimensions == null)
            {
                vesselDto.Dimensions = new DimensionsDto
                {
                    DistanceGpsToFront = 10,
                    DistanceGpsToBack = 10,
                    DistanceGpsToLeft = 5,
                    DistanceGpsToRight = 5
                };
            }

            // Map device groups using the DeviceGroupMappingService
            // This creates fresh device DTOs to prevent reference sharing issues
            vesselDto.DeviceGroups = _deviceGroupMappingService.MapToDeviceGroupDtos(entity);

            // Log device mapping details for debugging navigation issues
            var totalDevices = vesselDto.DeviceGroups.Values.Sum(g => g.Devices.Count);
            _logger.LogDebug("Mapped vessel entity {VesselId} to DTO with {GroupCount} device groups and {DeviceCount} total devices",
                entity.Id, vesselDto.DeviceGroups.Count, totalDevices);

            // Validate device uniqueness to catch duplication issues early
            var allDeviceIds = vesselDto.DeviceGroups.Values
                .SelectMany(g => g.Devices)
                .Select(d => d.Id)
                .Where(id => id != Guid.Empty)
                .ToList();

            var uniqueDeviceIds = allDeviceIds.Distinct().ToList();
            if (allDeviceIds.Count != uniqueDeviceIds.Count)
            {
                _logger.LogWarning("Detected {DuplicateCount} duplicate devices in vessel {VesselId} during mapping. Total: {Total}, Unique: {Unique}",
                    allDeviceIds.Count - uniqueDeviceIds.Count, entity.Id, allDeviceIds.Count, uniqueDeviceIds.Count);
            }

            // Map device connections
            MapDeviceConnections(entity, vesselDto);

            return vesselDto;
        }

        /// <summary>
        /// Maps a vessel DTO to an entity
        /// </summary>
        /// <param name="dto">The vessel DTO to map</param>
        /// <param name="entity">The existing entity to update, or null to create a new one</param>
        /// <returns>The mapped vessel entity</returns>
        public Vessel MapToEntity(VesselDto dto, Vessel? entity = null)
        {
            if (dto == null)
            {
                _logger.LogWarning("Cannot map null vessel DTO to entity");
                return new Vessel();
            }

            _logger.LogDebug("Mapping vessel DTO {VesselId} to entity", dto.Id);

            if (entity == null)
            {
                // Create a new entity if one wasn't provided
                return MapToEntity(dto);
            }
            else
            {
                // Update the existing entity
                UpdateEntityFromDto(entity, dto);
                return entity;
            }
        }

        #region Private Methods

        /// <summary>
        /// Maps device connections from a vessel entity to a vessel DTO
        /// </summary>
        /// <param name="vessel">The vessel entity</param>
        /// <param name="vesselDto">The vessel DTO</param>
        private void MapDeviceConnections(Vessel vessel, VesselDto vesselDto)
        {
            _logger.LogDebug("Mapping device connections from vessel entity to DTO");

            // Ensure the connections list is initialized
            vesselDto.DeviceConnections ??= [];

            // Clear existing connections to avoid duplicates
            vesselDto.DeviceConnections.Clear();

            // Get all connections from the vessel
            var connections = vessel.DeviceConnections.ToList();
            _logger.LogInformation("Found {ConnectionCount} device connections in vessel entity to map to DTO", connections.Count);

            // Map each connection to a DTO in the VesselDto.DeviceConnections list
            foreach (var connection in connections)
            {
                try
                {
                    // Use AutoMapper to map the connection to a DTO
                    var connectionDto = _mapper.Map<DeviceConnectionDto>(connection);

                    vesselDto.DeviceConnections.Add(connectionDto);

                    _logger.LogDebug("Mapped connection from {ConnectedId} to {InterfaceId}",
                        connection.ConnectedDeviceId, connection.InterfaceDeviceId);

                    // Also add the connection to the appropriate device's Connections list
                    var connectedDevice = vesselDto.GetAllDevices().FirstOrDefault(d => d.Id == connection.ConnectedDeviceId);
                    if (connectedDevice != null)
                    {
                        // Initialize the connections list if needed
                        connectedDevice.Connections ??= [];

                        // Check if the connection already exists in the device's connections
                        if (!connectedDevice.Connections.Any(c =>
                            c.ConnectedDeviceId == connection.ConnectedDeviceId &&
                            c.InterfaceDeviceId == connection.InterfaceDeviceId))
                        {
                            // Add the connection to the device
                            connectedDevice.Connections.Add(connectionDto);

                            _logger.LogDebug("Added connection to device {DeviceId} connections list", connectedDevice.Id);
                        }
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error mapping connection from {ConnectedId} to {InterfaceId}",
                        connection.ConnectedDeviceId, connection.InterfaceDeviceId);
                }
            }

            // Ensure all device connections are also in the VesselDto.DeviceConnections list
            foreach (var device in vesselDto.GetAllDevices())
            {
                if (device.Connections != null)
                {
                    foreach (var connection in device.Connections.ToList())
                    {
                        // Check if this connection is already in the VesselDto.DeviceConnections list
                        if (!vesselDto.DeviceConnections.Any(c =>
                            c.ConnectedDeviceId == connection.ConnectedDeviceId &&
                            c.InterfaceDeviceId == connection.InterfaceDeviceId))
                        {
                            // Add it to the VesselDto.DeviceConnections list
                            vesselDto.DeviceConnections.Add(connection);

                            _logger.LogDebug("Added device connection to VesselDto.DeviceConnections list");
                        }
                    }
                }
            }
        }

        #endregion
    }
}
