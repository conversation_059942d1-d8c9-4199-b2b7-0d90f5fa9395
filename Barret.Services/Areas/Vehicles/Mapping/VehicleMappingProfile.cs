using AutoMapper;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Core.Areas.Vehicles.ValueObjects;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles;
using Barret.Shared.DTOs.Vehicles.Vessels;

namespace Barret.Services.Areas.Vehicles.Mapping
{
    /// <summary>
    /// AutoMapper profile for mapping between Vehicle entities and DTOs
    /// </summary>
    public class VehicleMappingProfile : Profile
    {
        public VehicleMappingProfile()
        {
            // Map DeviceConnection to DeviceConnectionDto
            CreateMap<DeviceConnection, DeviceConnectionDto>()
                .ForMember(dest => dest.ConnectedDeviceId, opt => opt.MapFrom(src => src.ConnectedDeviceId))
                .ForMember(dest => dest.InterfaceDeviceId, opt => opt.MapFrom(src => src.InterfaceDeviceId))
                .ForMember(dest => dest.Type, opt => opt.MapFrom(src => src.Type))
                .ForMember(dest => dest.Direction, opt => opt.MapFrom(src => src.Direction));

            // Map DeviceConnectionDto to DeviceConnection
            CreateMap<DeviceConnectionDto, DeviceConnection>()
                .ConstructUsing(src => new DeviceConnection(
                    src.ConnectedDeviceId,
                    src.InterfaceDeviceId,
                    src.Type,
                    src.Direction));

            // Map DimensionsFromGpsLocation to DimensionsDto
            CreateMap<DimensionsFromGpsLocation, DimensionsDto>()
                .ConstructUsing(src => new DimensionsDto(
                    src.DistanceGpsToFront,
                    src.DistanceGpsToBack,
                    src.DistanceGpsToLeft,
                    src.DistanceGpsToRight
                ));

            // Map from Vessel entity to VesselDto
            CreateMap<Vessel, VesselDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.VehicleId, opt => opt.MapFrom(src => src.VehicleId))
                .ForMember(dest => dest.Name, opt => opt.MapFrom(src => src.Name))
                .ForMember(dest => dest.MMSI, opt => opt.MapFrom(src => src.MMSI))
                .ForMember(dest => dest.ENI, opt => opt.MapFrom(src => src.ENI))
                .ForMember(dest => dest.Dimensions, opt => opt.MapFrom(src => src.Dimensions != null
                    ? new DimensionsDto
                    {
                        DistanceGpsToFront = src.Dimensions.DistanceGpsToFront,
                        DistanceGpsToBack = src.Dimensions.DistanceGpsToBack,
                        DistanceGpsToLeft = src.Dimensions.DistanceGpsToLeft,
                        DistanceGpsToRight = src.Dimensions.DistanceGpsToRight
                    }
                    : null))
                .ForMember(dest => dest.DeviceGroups, opt => opt.Ignore()); // Device groups are handled separately
        }
    }
}
