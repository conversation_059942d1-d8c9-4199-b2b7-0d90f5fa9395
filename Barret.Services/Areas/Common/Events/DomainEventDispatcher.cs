using Barret.Core.Areas.Common.Events;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Common.Events
{
    /// <summary>
    /// Interface for handlers of domain events.
    /// </summary>
    /// <typeparam name="TEvent">The type of event to handle.</typeparam>
    public interface IDomainEventHandler<in TEvent>
    {
        /// <summary>
        /// Handles a domain event.
        /// </summary>
        /// <param name="domainEvent">The event to handle.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task HandleAsync(TEvent domainEvent);
    }

    /// <summary>
    /// Implementation of <see cref="IDomainEventDispatcher"/> that uses the service provider to resolve handlers.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DomainEventDispatcher"/> class.
    /// </remarks>
    /// <param name="serviceProvider">The service provider used to resolve handlers.</param>
    /// <param name="logger">The logger.</param>
    public class DomainEventDispatcher(IServiceProvider serviceProvider, ILogger<DomainEventDispatcher> logger) : IDomainEventDispatcher
    {
        private readonly IServiceProvider _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        private readonly ILogger<DomainEventDispatcher> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <inheritdoc/>
        public async Task DispatchAsync<TEvent>(TEvent domainEvent)
        {
            _logger.LogDebug("Dispatching domain event {EventType}", typeof(TEvent).Name);

            try
            {
                // Resolve all handlers for this event type
                var handlers = _serviceProvider.GetServices<IDomainEventHandler<TEvent>>();

                if (!handlers.Any())
                {
                    _logger.LogDebug("No handlers registered for {EventType}", typeof(TEvent).Name);
                    return;
                }

                _logger.LogDebug("Found {HandlerCount} handlers for {EventType}", handlers.Count(), typeof(TEvent).Name);

                // Execute all handlers
                foreach (var handler in handlers)
                {
                    try
                    {
                        await handler.HandleAsync(domainEvent);
                    }
                    catch (Exception ex)
                    {
                        // Log the error but continue processing other handlers
                        _logger.LogError(ex, "Error handling domain event {EventType} in handler {HandlerType}",
                            typeof(TEvent).Name, handler.GetType().Name);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error dispatching domain event {EventType}", typeof(TEvent).Name);
                throw;
            }
        }
    }
} 