using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Manufacturers.Models;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.DeviceModels.Repositories;
using Barret.Services.Core.Areas.Manufacturers;
using Barret.Services.Core.Areas.Manufacturers.Repositories;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.Results;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System;

namespace Barret.Services.Areas.Manufacturers
{
    /// <summary>
    /// Service implementation for manufacturers.
    /// As Manufacturer is an aggregate root, this service also handles operations on DeviceModels.
    /// </summary>
    public class ManufacturerService : IManufacturerService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IManufacturerRepository _manufacturerRepository;
        private readonly IDeviceModelRepository _deviceModelRepository;
        private readonly ILogger<ManufacturerService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="ManufacturerService"/> class.
        /// </summary>
        /// <param name="unitOfWork">The unit of work for transaction management</param>
        /// <param name="logger">The logger</param>
        /// <exception cref="ArgumentNullException">Thrown if any parameter is null</exception>
        public ManufacturerService(
            IUnitOfWork unitOfWork,
            ILogger<ManufacturerService> logger)
        {
            _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
            _manufacturerRepository = _unitOfWork.Manufacturers;
            _deviceModelRepository = _unitOfWork.DeviceModels;
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Executes an operation within a transaction.
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="operationName">The name of the operation for logging</param>
        /// <returns>The result of the operation</returns>
        protected async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, string? operationName = null)
        {
            operationName ??= "ManufacturerOperation";

            try
            {
                _logger.LogInformation("Starting transaction for operation: {OperationName}", operationName);

                // Begin transaction
                await _unitOfWork.BeginTransactionAsync();

                try
                {
                    // Execute the operation
                    var result = await operation();

                    // Save changes - this should be done here, not inside the operation
                    // to ensure consistent transaction management
                    await _unitOfWork.SaveChangesAsync();

                    // Commit transaction
                    await _unitOfWork.CommitTransactionAsync();

                    _logger.LogInformation("Successfully completed transaction for operation: {OperationName}", operationName);

                    return result;
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    // Handle optimistic concurrency exception
                    await _unitOfWork.RollbackTransactionAsync();
                    _logger.LogError(ex, "Optimistic concurrency error during operation {OperationName}, transaction rolled back", operationName);

                    // Return a failure result instead of throwing
                    if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(ServiceResult<>))
                    {
                        // Create a failure result using reflection
                        var createFailureMethod = typeof(T).GetMethod("CreateFailure", new[] { typeof(string), typeof(string) });
                        if (createFailureMethod != null)
                        {
                            return (T)createFailureMethod.Invoke(null, new object[]
                            {
                                "Concurrency error: The data was modified by another user. Please try again.",
                                ex.Message
                            });
                        }
                    }
                    else if (typeof(T) == typeof(ServiceResult))
                    {
                        // Create a failure result for ServiceResult
                        var createFailureMethod = typeof(ServiceResult).GetMethod("CreateFailure", new[] { typeof(string), typeof(string) });
                        if (createFailureMethod != null)
                        {
                            return (T)createFailureMethod.Invoke(null, new object[]
                            {
                                "Concurrency error: The data was modified by another user. Please try again.",
                                ex.Message
                            });
                        }
                    }

                    // If we can't create a failure result, rethrow
                    throw;
                }
                catch (Exception ex)
                {
                    // Rollback transaction on error
                    await _unitOfWork.RollbackTransactionAsync();
                    _logger.LogError(ex, "Error during operation {OperationName}, transaction rolled back", operationName);
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing operation: {OperationName}", operationName);
                throw;
            }
        }

        public async Task<ServiceResult<IEnumerable<ManufacturerInfo>>> GetAllManufacturersAsync()
        {
            try
            {
                _logger.LogInformation("Getting all manufacturers");

                var manufacturers = await _manufacturerRepository.GetAllAsync();
                var manufacturerInfos = manufacturers.Select(m => new ManufacturerInfo
                {
                    Id = m.Id,
                    Name = m.Name
                }).ToList();

                _logger.LogInformation("Retrieved {Count} manufacturers", manufacturerInfos.Count);
                return ServiceResult<IEnumerable<ManufacturerInfo>>.CreateSuccess(manufacturerInfos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all manufacturers");
                return ServiceResult<IEnumerable<ManufacturerInfo>>.CreateFailure("Error retrieving manufacturers", ex.Message);
            }
        }

        public async Task<ServiceResult<ManufacturerInfo>> GetManufacturerByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Getting manufacturer with ID {ManufacturerId}", id);

                // Use GetByIdAsync which doesn't include device models to avoid circular references
                var manufacturer = await _manufacturerRepository.GetByIdAsync(id);
                if (manufacturer == null)
                {
                    _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", id);
                    return ServiceResult<ManufacturerInfo>.CreateFailure($"Manufacturer with ID {id} not found", "Manufacturer not found");
                }

                var manufacturerInfo = new ManufacturerInfo
                {
                    Id = manufacturer.Id,
                    Name = manufacturer.Name
                };

                _logger.LogInformation("Successfully retrieved manufacturer {ManufacturerId}", id);
                return ServiceResult<ManufacturerInfo>.CreateSuccess(manufacturerInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting manufacturer with ID {ManufacturerId}", id);
                return ServiceResult<ManufacturerInfo>.CreateFailure("Error retrieving manufacturer", ex.Message);
            }
        }

        public async Task<ServiceResult<ManufacturerInfo>> GetManufacturerWithDeviceModelsByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Getting manufacturer with ID {ManufacturerId} including device models", id);

                // Use GetWithDeviceModelsAsync which loads device models separately to avoid circular references
                var manufacturer = await _manufacturerRepository.GetWithDeviceModelsAsync(id);
                if (manufacturer == null)
                {
                    _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", id);
                    return ServiceResult<ManufacturerInfo>.CreateFailure($"Manufacturer with ID {id} not found", "Manufacturer not found");
                }

                var manufacturerInfo = new ManufacturerInfo
                {
                    Id = manufacturer.Id,
                    Name = manufacturer.Name
                };

                _logger.LogInformation("Successfully retrieved manufacturer {ManufacturerId} with device models", id);
                return ServiceResult<ManufacturerInfo>.CreateSuccess(manufacturerInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting manufacturer with ID {ManufacturerId} with device models", id);
                return ServiceResult<ManufacturerInfo>.CreateFailure("Error retrieving manufacturer with device models", ex.Message);
            }
        }

        public async Task<ServiceResult<Guid>> CreateManufacturerAsync(string manufacturerName)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Creating new manufacturer with name {ManufacturerName}", manufacturerName);

                try
                {
                    // Create manufacturer - pass null for the logger since we can't use our service logger
                    var manufacturer = new Manufacturer(manufacturerName);

                    // Log the manufacturer creation
                    _logger.LogDebug("Created manufacturer {ManufacturerId} with name {ManufacturerName}",
                        manufacturer.Id, manufacturer.Name);

                    // Add to repository
                    await _manufacturerRepository.AddAsync(manufacturer);

                    // Note: SaveChanges is now handled by ExecuteInTransactionAsync

                    return ServiceResult<Guid>.CreateSuccess(manufacturer.Id);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating manufacturer with name {ManufacturerName}", manufacturerName);
                    return ServiceResult<Guid>.CreateFailure($"Error creating manufacturer: {ex.Message}", ex.ToString());
                }
            }, "CreateManufacturer");
        }

        public async Task<ServiceResult> UpdateManufacturerAsync(Guid id, string manufacturerName)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Updating manufacturer with ID {ManufacturerId}", id);

                try
                {
                    // Get manufacturer with tracking
                    var manufacturer = await _manufacturerRepository.GetByIdAsync(id);
                    if (manufacturer == null)
                    {
                        _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", id);
                        return ServiceResult.CreateFailure($"Manufacturer with ID {id} not found", "Manufacturer not found");
                    }

                    // Log the manufacturer state before update
                    _logger.LogDebug("Manufacturer {ManufacturerId} found with name {ManufacturerName} and {DeviceModelCount} device models",
                        manufacturer.Id, manufacturer.Name, manufacturer.DeviceModels.Count);

                    // Update manufacturer using domain method
                    manufacturer.Name = manufacturerName;

                    // Log the manufacturer update
                    _logger.LogDebug("Updated manufacturer {ManufacturerId} name to {ManufacturerName}",
                        manufacturer.Id, manufacturer.Name);

                    // Update in repository
                    await _manufacturerRepository.UpdateAsync(manufacturer);

                    // Note: SaveChanges is now handled by ExecuteInTransactionAsync

                    return ServiceResult.CreateSuccess();
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating manufacturer {ManufacturerId}", id);
                    return ServiceResult.CreateFailure($"Error updating manufacturer: {ex.Message}", ex.ToString());
                }
            }, "UpdateManufacturer");
        }

        public async Task<ServiceResult> DeleteManufacturerAsync(Guid id)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Deleting manufacturer with ID {ManufacturerId}", id);

                try
                {
                    // Check if manufacturer exists
                    var manufacturer = await _manufacturerRepository.GetByIdAsync(id);
                    if (manufacturer == null)
                    {
                        _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", id);
                        return ServiceResult.CreateFailure($"Manufacturer with ID {id} not found", "Manufacturer not found");
                    }

                    // Log the manufacturer to be deleted
                    _logger.LogDebug("Deleting manufacturer {ManufacturerId} with name {ManufacturerName} and {DeviceModelCount} device models",
                        manufacturer.Id, manufacturer.Name, manufacturer.DeviceModels.Count);

                    // STEP 1: For each device model, find devices that reference it and clear their model reference
                    var deviceModelsWithReferences = new List<Guid>();
                    var totalDevicesUpdated = 0;

                    foreach (var deviceModel in manufacturer.DeviceModels.ToList())
                    {
                        var devicesWithModel = await _unitOfWork.Devices.GetDevicesByModelIdAsync(deviceModel.Id);
                        if (devicesWithModel.Count > 0)
                        {
                            deviceModelsWithReferences.Add(deviceModel.Id);

                            foreach (var device in devicesWithModel)
                            {
                                _logger.LogDebug("Clearing model reference from device {DeviceId}", device.Id);
                                device.ClearModel();
                                await _unitOfWork.Devices.UpdateAsync(device);
                                totalDevicesUpdated++;
                            }

                            _logger.LogInformation("Cleared model references from {DeviceCount} devices for model {DeviceModelId}",
                                devicesWithModel.Count, deviceModel.Id);
                        }
                    }

                    // STEP 2: Save changes to ensure device references are cleared in the database
                    // This is critical to avoid foreign key constraint violations
                    if (totalDevicesUpdated > 0)
                    {
                        _logger.LogInformation("Saving changes to clear model references from {DeviceCount} devices across {ModelCount} device models",
                            totalDevicesUpdated, deviceModelsWithReferences.Count);
                        await _unitOfWork.SaveChangesAsync();
                    }

                    // STEP 3: Delete manufacturer
                    // This will cascade delete all device models due to EF Core configuration
                    await _manufacturerRepository.DeleteAsync(id);

                    // Note: Final SaveChanges is handled by ExecuteInTransactionAsync

                    return ServiceResult.CreateSuccess();
                }
                catch (DbUpdateException ex)
                {
                    // Check if this is a foreign key constraint violation
                    if (ex.InnerException?.Message.Contains("FOREIGN KEY constraint") == true)
                    {
                        _logger.LogWarning(ex, "Cannot delete manufacturer {ManufacturerId} because it has related records", id);
                        return ServiceResult.CreateFailure(
                            "Cannot delete this manufacturer because it has devices or other related records. " +
                            "Please remove all related records first.",
                            "DbUpdateException: Foreign key constraint violation");
                    }

                    // For other database update exceptions
                    _logger.LogError(ex, "Database error when deleting manufacturer {ManufacturerId}", id);
                    return ServiceResult.CreateFailure(
                        "A database error occurred while deleting the manufacturer. Please try again.",
                        "DbUpdateException: " + ex.Message);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error deleting manufacturer {ManufacturerId}", id);
                    return ServiceResult.CreateFailure($"Error deleting manufacturer: {ex.Message}", ex.ToString());
                }
            }, "DeleteManufacturer");
        }

        public async Task<ServiceResult<IEnumerable<ManufacturerInfo>>> GetAllManufacturersForDeviceRoleAsync(DeviceRole deviceRole)
        {
            try
            {
                _logger.LogInformation("Getting manufacturers for device role {DeviceRole}", deviceRole);

                // Get manufacturers that have at least one device model matching the specified role
                var manufacturers = await _manufacturerRepository.GetManufacturersWithFilteredDeviceModelsAsync(deviceRole);

                // Convert to simple info objects
                var manufacturerInfos = manufacturers
                    .Select(m => new ManufacturerInfo
                    {
                        Id = m.Id,
                        Name = m.Name
                    })
                    .ToList();

                _logger.LogInformation("Found {Count} manufacturers for device role {DeviceRole}",
                    manufacturerInfos.Count, deviceRole);

                return ServiceResult<IEnumerable<ManufacturerInfo>>.CreateSuccess(manufacturerInfos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting manufacturers for device role {DeviceRole}", deviceRole);
                return ServiceResult<IEnumerable<ManufacturerInfo>>.CreateFailure(
                    "Error retrieving manufacturers for device role", ex.Message);
            }
        }

        public async Task<ServiceResult<IEnumerable<ManufacturerInfo>>> GetManufacturersByDeviceRoleAsync(DeviceRole deviceRole)
        {
            try
            {
                _logger.LogInformation("Getting manufacturers by device role {DeviceRole} without device models", deviceRole);

                // Get manufacturers that have at least one device model with the specified role
                var manufacturers = await _manufacturerRepository.GetManufacturersByDeviceRoleAsync(deviceRole);

                // Convert to simple info objects
                var manufacturerInfos = manufacturers.Select(m => new ManufacturerInfo
                {
                    Id = m.Id,
                    Name = m.Name
                }).ToList();

                _logger.LogInformation("Found {Count} manufacturers for device role {DeviceRole}",
                    manufacturerInfos.Count, deviceRole);

                return ServiceResult<IEnumerable<ManufacturerInfo>>.CreateSuccess(manufacturerInfos);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting manufacturers by device role {DeviceRole}", deviceRole);
                return ServiceResult<IEnumerable<ManufacturerInfo>>.CreateFailure(
                    "Error retrieving manufacturers by device role", ex.Message);
            }
        }

        public async Task<ServiceResult<Guid>> AddDeviceModelAsync(Guid manufacturerId, string modelName, DeviceRole deviceRole)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Adding device model {ModelName} with role {DeviceRole} to manufacturer {ManufacturerId}",
                    modelName, deviceRole, manufacturerId);

                try
                {
                    // Get manufacturer with tracking and its device models
                    var manufacturer = await _manufacturerRepository.GetByIdAsync(manufacturerId);
                    if (manufacturer == null)
                    {
                        _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", manufacturerId);
                        return ServiceResult<Guid>.CreateFailure($"Manufacturer with ID {manufacturerId} not found", "Manufacturer not found");
                    }

                    // Log the manufacturer state
                    _logger.LogDebug("Manufacturer {ManufacturerId} found with {DeviceModelCount} device models",
                        manufacturer.Id, manufacturer.DeviceModels.Count);

                    // Create device model using domain method
                    var deviceModel = manufacturer.CreateDeviceModel(modelName, deviceRole);

                    // Log the device model creation
                    _logger.LogDebug("Created device model {DeviceModelId} with name {ModelName} and role {DeviceRole}",
                        deviceModel.Id, deviceModel.Name, deviceModel.DeviceRole);

                    // Log the updated manufacturer state
                    _logger.LogDebug("Manufacturer now has {DeviceModelCount} device models",
                        manufacturer.DeviceModels.Count);

                    // Directly add the device model to the DbContext
                    await _deviceModelRepository.AddAsync(deviceModel);

                    // Update the manufacturer in the repository
                    await _manufacturerRepository.UpdateAsync(manufacturer);

                    // Note: SaveChanges is handled by ExecuteInTransactionAsync

                    return ServiceResult<Guid>.CreateSuccess(deviceModel.Id);
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    // Log the concurrency exception
                    _logger.LogWarning(ex, "Concurrency exception when adding device model to manufacturer {ManufacturerId}", manufacturerId);

                    // Return a more specific error message
                    return ServiceResult<Guid>.CreateFailure(
                        "The manufacturer was modified by another operation. Please try again.",
                        "DbUpdateConcurrencyException: " + ex.Message);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error adding device model to manufacturer {ManufacturerId}", manufacturerId);
                    return ServiceResult<Guid>.CreateFailure($"Error adding device model: {ex.Message}", ex.ToString());
                }
            }, "AddDeviceModel");
        }

        [Obsolete("Use AddDeviceModelAsync instead")]
        public async Task<ServiceResult<Guid>> AddDeviceModelToManufacturerAsync(Guid manufacturerId, string modelName, DeviceRole deviceRole)
        {
            return await AddDeviceModelAsync(manufacturerId, modelName, deviceRole);
        }

        public async Task<ServiceResult> RemoveDeviceModelFromManufacturerAsync(Guid manufacturerId, Guid deviceModelId)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Removing device model {DeviceModelId} from manufacturer {ManufacturerId}",
                    deviceModelId, manufacturerId);

                try
                {
                    // Get manufacturer with tracking and its device models
                    var manufacturer = await _manufacturerRepository.GetByIdAsync(manufacturerId);
                    if (manufacturer == null)
                    {
                        _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", manufacturerId);
                        return ServiceResult.CreateFailure($"Manufacturer with ID {manufacturerId} not found", "Manufacturer not found");
                    }

                    // Log the manufacturer state
                    _logger.LogDebug("Manufacturer {ManufacturerId} found with {DeviceModelCount} device models",
                        manufacturer.Id, manufacturer.DeviceModels.Count);

                    // Find device model
                    var deviceModel = manufacturer.DeviceModels.FirstOrDefault(dt => dt.Id == deviceModelId);
                    if (deviceModel == null)
                    {
                        _logger.LogWarning("DeviceModel with ID {DeviceModelId} not found on manufacturer {ManufacturerId}",
                            deviceModelId, manufacturerId);
                        return ServiceResult.CreateFailure($"DeviceModel with ID {deviceModelId} not found on manufacturer {manufacturerId}",
                            "DeviceModel not found");
                    }

                    // Log the device model to be removed
                    _logger.LogDebug("Found device model {DeviceModelId} with name {ModelName} and role {DeviceRole}",
                        deviceModel.Id, deviceModel.Name, deviceModel.DeviceRole);

                    // STEP 1: Find devices that reference this model and clear their model reference
                    var devicesWithModel = await _unitOfWork.Devices.GetDevicesByModelIdAsync(deviceModelId);
                    _logger.LogInformation("Found {DeviceCount} devices referencing model {DeviceModelId}",
                        devicesWithModel.Count, deviceModelId);

                    foreach (var device in devicesWithModel)
                    {
                        _logger.LogDebug("Clearing model reference from device {DeviceId}", device.Id);
                        device.ClearModel();
                        await _unitOfWork.Devices.UpdateAsync(device);
                    }

                    // STEP 2: Save changes to ensure device references are cleared in the database
                    // This is critical to avoid foreign key constraint violations
                    if (devicesWithModel.Count > 0)
                    {
                        _logger.LogInformation("Saving changes to clear model references from {DeviceCount} devices",
                            devicesWithModel.Count);
                        await _unitOfWork.SaveChangesAsync();
                    }

                    // STEP 3: Remove device model from manufacturer
                    manufacturer.RemoveDeviceModel(deviceModel);

                    // Log the updated manufacturer state
                    _logger.LogDebug("Manufacturer now has {DeviceModelCount} device models after removal",
                        manufacturer.DeviceModels.Count);

                    // Update the manufacturer in the repository
                    await _manufacturerRepository.UpdateAsync(manufacturer);

                    // STEP 4: Mark the device model for deletion
                    await _deviceModelRepository.DeleteAsync(deviceModel.Id);

                    // Note: Final SaveChanges is handled by ExecuteInTransactionAsync

                    return ServiceResult.CreateSuccess();
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    // Log the concurrency exception
                    _logger.LogWarning(ex, "Concurrency exception when removing device model from manufacturer {ManufacturerId}", manufacturerId);

                    // Return a more specific error message
                    return ServiceResult.CreateFailure(
                        "The manufacturer was modified by another operation. Please try again.",
                        "DbUpdateConcurrencyException: " + ex.Message);
                }
                catch (DbUpdateException ex)
                {
                    // Check if this is a foreign key constraint violation
                    if (ex.InnerException?.Message.Contains("FOREIGN KEY constraint") == true)
                    {
                        _logger.LogWarning(ex, "Foreign key constraint violation when removing device model {DeviceModelId}", deviceModelId);
                        return ServiceResult.CreateFailure(
                            "Cannot delete this device model because it is still referenced by one or more devices. " +
                            "Please remove all references to this model first.",
                            "DbUpdateException: Foreign key constraint violation");
                    }

                    // For other database update exceptions
                    _logger.LogError(ex, "Database error when removing device model {DeviceModelId}", deviceModelId);
                    return ServiceResult.CreateFailure(
                        "A database error occurred while removing the device model. Please try again.",
                        "DbUpdateException: " + ex.Message);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error removing device model {DeviceModelId} from manufacturer {ManufacturerId}",
                        deviceModelId, manufacturerId);
                    return ServiceResult.CreateFailure($"Error removing device model: {ex.Message}", ex.ToString());
                }
            }, "RemoveDeviceModel");
        }

        public async Task<IEnumerable<ManufacturerInfo>> GetManufacturersWithDeviceRoleAsync(DeviceRole deviceRole)
        {
            try
            {
                _logger.LogInformation("Getting manufacturers with device role {DeviceRole}", deviceRole);

                // Get the result from the existing method
                var result = await GetManufacturersByDeviceRoleAsync(deviceRole);

                // Return the manufacturers if successful, otherwise an empty list
                return result.Success ? result.Data ?? new List<ManufacturerInfo>() : new List<ManufacturerInfo>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting manufacturers with device role {DeviceRole}", deviceRole);
                return new List<ManufacturerInfo>();
            }
        }

        public async Task<ServiceResult<DeviceModelInfo>> GetDeviceModelByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Getting device model with ID {DeviceModelId}", id);

                var deviceModel = await _deviceModelRepository.GetByIdAsync(id);
                if (deviceModel == null)
                {
                    _logger.LogWarning("Device model with ID {DeviceModelId} not found", id);
                    return ServiceResult<DeviceModelInfo>.CreateFailure($"Device model with ID {id} not found", "Device model not found");
                }

                var deviceModelInfo = new DeviceModelInfo
                {
                    Id = deviceModel.Id,
                    Name = deviceModel.Name,
                    DeviceRole = deviceModel.DeviceRole,
                    ManufacturerId = deviceModel.ManufacturerId,
                    ManufacturerName = deviceModel.Manufacturer?.Name ?? string.Empty
                };

                _logger.LogInformation("Successfully retrieved device model {DeviceModelId}", id);

                return ServiceResult<DeviceModelInfo>.CreateSuccess(deviceModelInfo);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device model with ID {DeviceModelId}", id);
                return ServiceResult<DeviceModelInfo>.CreateFailure("Error retrieving device model", ex.Message);
            }
        }

        public async Task<ServiceResult> UpdateDeviceModelAsync(Guid id, string modelName)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Updating device model {DeviceModelId} with new name {ModelName}", id, modelName);

                try
                {
                    // Get device model with its manufacturer
                    var deviceModel = await _deviceModelRepository.GetByIdAsync(id);
                    if (deviceModel == null)
                    {
                        _logger.LogWarning("Device model with ID {DeviceModelId} not found", id);
                        return ServiceResult.CreateFailure($"Device model with ID {id} not found", "Device model not found");
                    }

                    // Get manufacturer (the aggregate root) with all its device models
                    var manufacturer = await _manufacturerRepository.GetByIdAsync(deviceModel.ManufacturerId);
                    if (manufacturer == null)
                    {
                        _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", deviceModel.ManufacturerId);
                        return ServiceResult.CreateFailure($"Manufacturer with ID {deviceModel.ManufacturerId} not found", "Manufacturer not found");
                    }

                    // Log the manufacturer state
                    _logger.LogDebug("Manufacturer {ManufacturerId} found with {DeviceModelCount} device models",
                        manufacturer.Id, manufacturer.DeviceModels.Count);

                    // Find the device model in the manufacturer's collection
                    var deviceModelInManufacturer = manufacturer.DeviceModels.FirstOrDefault(dm => dm.Id == id);
                    if (deviceModelInManufacturer == null)
                    {
                        _logger.LogWarning("Device model with ID {DeviceModelId} not found in manufacturer {ManufacturerId}",
                            id, manufacturer.Id);
                        return ServiceResult.CreateFailure($"Device model with ID {id} not found in manufacturer {manufacturer.Id}",
                            "Device model not found in manufacturer");
                    }

                    // Log the device model to be updated
                    _logger.LogDebug("Found device model {DeviceModelId} with name {ModelName} and role {DeviceRole}",
                        deviceModelInManufacturer.Id, deviceModelInManufacturer.Name, deviceModelInManufacturer.DeviceRole);

                    // Update the device model name
                    deviceModelInManufacturer.Name = modelName;

                    _logger.LogDebug("Updated device model {DeviceModelId} with new name {ModelName}",
                        deviceModelInManufacturer.Id, deviceModelInManufacturer.Name);

                    // Update the device model in the repository
                    await _deviceModelRepository.UpdateAsync(deviceModelInManufacturer);

                    // Update manufacturer in the repository to ensure proper tracking
                    await _manufacturerRepository.UpdateAsync(manufacturer);

                    // Note: SaveChanges is handled by ExecuteInTransactionAsync

                    return ServiceResult.CreateSuccess();
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    // Log the concurrency exception
                    _logger.LogWarning(ex, "Concurrency exception when updating device model {DeviceModelId}", id);

                    // Return a more specific error message
                    return ServiceResult.CreateFailure(
                        "The manufacturer or device model was modified by another operation. Please try again.",
                        "DbUpdateConcurrencyException: " + ex.Message);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error updating device model {DeviceModelId}", id);
                    return ServiceResult.CreateFailure($"Error updating device model: {ex.Message}", ex.ToString());
                }
            }, "UpdateDeviceModel");
        }

        public async Task<ServiceResult> DeleteDeviceModelAsync(Guid id)
        {
            return await ExecuteInTransactionAsync(async () =>
            {
                _logger.LogInformation("Deleting device model {DeviceModelId}", id);

                try
                {
                    // Get device model with its manufacturer
                    var deviceModel = await _deviceModelRepository.GetByIdAsync(id);
                    if (deviceModel == null)
                    {
                        _logger.LogWarning("Device model with ID {DeviceModelId} not found", id);
                        return ServiceResult.CreateFailure($"Device model with ID {id} not found", "Device model not found");
                    }

                    // Get manufacturer (the aggregate root) with all its device models
                    var manufacturer = await _manufacturerRepository.GetByIdAsync(deviceModel.ManufacturerId);
                    if (manufacturer == null)
                    {
                        _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", deviceModel.ManufacturerId);
                        return ServiceResult.CreateFailure($"Manufacturer with ID {deviceModel.ManufacturerId} not found", "Manufacturer not found");
                    }

                    // Log the manufacturer state
                    _logger.LogDebug("Manufacturer {ManufacturerId} found with {DeviceModelCount} device models",
                        manufacturer.Id, manufacturer.DeviceModels.Count);

                    // Find the device model in the manufacturer's collection
                    var deviceModelInManufacturer = manufacturer.DeviceModels.FirstOrDefault(dm => dm.Id == id);
                    if (deviceModelInManufacturer == null)
                    {
                        _logger.LogWarning("Device model with ID {DeviceModelId} not found in manufacturer {ManufacturerId}",
                            id, manufacturer.Id);
                        return ServiceResult.CreateFailure($"Device model with ID {id} not found in manufacturer {manufacturer.Id}",
                            "Device model not found in manufacturer");
                    }

                    // Log the device model to be deleted
                    _logger.LogDebug("Found device model {DeviceModelId} with name {ModelName} and role {DeviceRole}",
                        deviceModelInManufacturer.Id, deviceModelInManufacturer.Name, deviceModelInManufacturer.DeviceRole);

                    // STEP 1: Find devices that reference this model and clear their model reference
                    var devicesWithModel = await _unitOfWork.Devices.GetDevicesByModelIdAsync(id);
                    _logger.LogInformation("Found {DeviceCount} devices referencing model {DeviceModelId}",
                        devicesWithModel.Count, id);

                    foreach (var device in devicesWithModel)
                    {
                        _logger.LogDebug("Clearing model reference from device {DeviceId}", device.Id);
                        device.ClearModel();
                        await _unitOfWork.Devices.UpdateAsync(device);
                    }

                    // STEP 2: Save changes to ensure device references are cleared in the database
                    // This is critical to avoid foreign key constraint violations
                    if (devicesWithModel.Count > 0)
                    {
                        _logger.LogInformation("Saving changes to clear model references from {DeviceCount} devices",
                            devicesWithModel.Count);
                        await _unitOfWork.SaveChangesAsync();
                    }

                    // STEP 3: Remove the device model from the manufacturer using domain method
                    manufacturer.RemoveDeviceModel(deviceModelInManufacturer);

                    // Log the updated manufacturer state
                    _logger.LogDebug("Manufacturer now has {DeviceModelCount} device models after removal",
                        manufacturer.DeviceModels.Count);

                    // Update manufacturer
                    await _manufacturerRepository.UpdateAsync(manufacturer);

                    // STEP 4: Mark the device model for deletion
                    await _deviceModelRepository.DeleteAsync(id);

                    // Note: Final SaveChanges is handled by ExecuteInTransactionAsync

                    return ServiceResult.CreateSuccess();
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    // Log the concurrency exception
                    _logger.LogWarning(ex, "Concurrency exception when deleting device model {DeviceModelId}", id);

                    // Return a more specific error message
                    return ServiceResult.CreateFailure(
                        "The manufacturer or device model was modified by another operation. Please try again.",
                        "DbUpdateConcurrencyException: " + ex.Message);
                }
                catch (DbUpdateException ex)
                {
                    // Check if this is a foreign key constraint violation
                    if (ex.InnerException?.Message.Contains("FOREIGN KEY constraint") == true)
                    {
                        _logger.LogWarning(ex, "Foreign key constraint violation when deleting device model {DeviceModelId}", id);
                        return ServiceResult.CreateFailure(
                            "Cannot delete this device model because it is still referenced by one or more devices. " +
                            "Please remove all references to this model first.",
                            "DbUpdateException: Foreign key constraint violation");
                    }

                    // For other database update exceptions
                    _logger.LogError(ex, "Database error when deleting device model {DeviceModelId}", id);
                    return ServiceResult.CreateFailure(
                        "A database error occurred while deleting the device model. Please try again.",
                        "DbUpdateException: " + ex.Message);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error deleting device model {DeviceModelId}", id);
                    return ServiceResult.CreateFailure($"Error deleting device model: {ex.Message}", ex.ToString());
                }
            }, "DeleteDeviceModel");
        }
    }
}