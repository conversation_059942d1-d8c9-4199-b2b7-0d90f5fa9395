using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Manufacturers.Models;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Manufacturers.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using System.Linq;

namespace Barret.Services.Areas.Manufacturers.Repositories
{
    public class ManufacturerRepository(IBarretDbContext dbContext, ILogger<ManufacturerRepository> logger) : IManufacturerRepository
    {
        private readonly IBarretDbContext _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        private readonly ILogger<ManufacturerRepository> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        public async Task<IEnumerable<Manufacturer>> GetAllAsync()
        {
            try
            {
                _logger.LogDebug("Fetching all manufacturers with their device models");
                // Include the DeviceModels navigation property when loading manufacturers
                return await _dbContext.Manufacturers
                    .Include(m => m.DeviceModels)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving all manufacturers", ex.Message);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task<IEnumerable<Manufacturer>> GetAllManufacturersAsync()
        {
            return await GetAllAsync();
        }

        public async Task<Manufacturer?> GetByIdAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Getting manufacturer with ID {ManufacturerId}", id);

                // Get the manufacturer with its device models
                var manufacturer = await _dbContext.Manufacturers
                    .Include(m => m.DeviceModels)
                    .FirstOrDefaultAsync(m => m.Id == id);

                if (manufacturer == null)
                {
                    _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", id);
                    return null;
                }

                // Log the manufacturer and its device models
                _logger.LogDebug("Successfully retrieved manufacturer {ManufacturerId} with {DeviceModelCount} device models",
                    id, manufacturer.DeviceModels.Count);

                foreach (var deviceModel in manufacturer.DeviceModels)
                {
                    _logger.LogDebug("Device model: {ModelId}, {ModelName}, {DeviceRole}",
                        deviceModel.Id, deviceModel.Name, deviceModel.DeviceRole);
                }

                return manufacturer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving manufacturer with ID {ManufacturerId}", id, ex.Message);
                throw;
            }
        }

        public async Task<Manufacturer?> GetWithDeviceModelsAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Getting manufacturer with ID {ManufacturerId} including device models", id);

                // First, get the manufacturer without including device models
                var manufacturer = await _dbContext.Manufacturers
                    .FirstOrDefaultAsync(m => m.Id == id);

                if (manufacturer == null)
                {
                    _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found", id);
                    return null;
                }

                // Then, load device models separately to avoid circular references
                var deviceModels = await _dbContext.DeviceModels
                    .Where(dm => dm.ManufacturerId == id)
                    .ToListAsync();

                // The device models are already associated with the manufacturer through the ManufacturerId
                // EF Core will automatically populate the navigation properties when the entities are tracked

                // Log the manufacturer state
                _logger.LogDebug("Manufacturer {ManufacturerId} loaded with {DeviceModelCount} device models",
                    manufacturer.Id, deviceModels.Count);

                foreach (var model in deviceModels)
                {
                    _logger.LogDebug("Device model: {ModelName} (ID: {ModelId}, Role: {DeviceRole})",
                        model.Name, model.Id, model.DeviceRole);
                }

                return manufacturer;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving manufacturer with ID {ManufacturerId} with device models", id, ex.Message);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task<Manufacturer?> GetManufacturerByIdAsync(Guid id)
        {
            return await GetByIdAsync(id);
        }

        public async Task AddAsync(Manufacturer manufacturer)
        {
            ArgumentNullException.ThrowIfNull(manufacturer);

            try
            {
                _logger.LogDebug("Adding manufacturer with ID {ManufacturerId}", manufacturer.Id);
                await _dbContext.Manufacturers.AddAsync(manufacturer);
                _logger.LogInformation("Manufacturer {ManufacturerId} added to context (not yet saved)", manufacturer.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error adding manufacturer with ID {ManufacturerId}", manufacturer.Id, ex.Message);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task AddManufacturerAsync(Manufacturer manufacturer)
        {
            await AddAsync(manufacturer);
            await _dbContext.SaveChangesAsync();
        }

        public async Task UpdateAsync(Manufacturer manufacturer)
        {
            ArgumentNullException.ThrowIfNull(manufacturer);

            try
            {
                _logger.LogDebug("Updating manufacturer with ID {ManufacturerId}", manufacturer.Id);

                // Check if the manufacturer is already being tracked
                var trackedManufacturer = _dbContext.ChangeTracker.Entries<Manufacturer>()
                    .FirstOrDefault(e => e.Entity.Id == manufacturer.Id)?.Entity;

                if (trackedManufacturer != null)
                {
                    _logger.LogDebug("Manufacturer {ManufacturerId} is already being tracked", manufacturer.Id);

                    // If the same instance is already being tracked, just return
                    if (ReferenceEquals(trackedManufacturer, manufacturer))
                    {
                        _logger.LogDebug("Same manufacturer instance is already being tracked, no need to update tracking");
                        return;
                    }

                    // If a different instance is being tracked, detach it
                    _logger.LogDebug("Detaching previously tracked manufacturer instance");
                    _dbContext.Entry(trackedManufacturer).State = EntityState.Detached;
                }

                // First, check if the manufacturer exists in the database
                var existingManufacturer = await _dbContext.Manufacturers
                    .AsNoTracking()
                    .FirstOrDefaultAsync(m => m.Id == manufacturer.Id);

                if (existingManufacturer == null)
                {
                    _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found in database", manufacturer.Id);
                    throw new InvalidOperationException($"Manufacturer with ID {manufacturer.Id} not found in database");
                }

                // Get all existing device models for this manufacturer
                var existingDeviceModels = await _dbContext.DeviceModels
                    .AsNoTracking()
                    .Where(dm => dm.ManufacturerId == manufacturer.Id)
                    .ToListAsync();

                _logger.LogDebug("Found {Count} existing device models for manufacturer {ManufacturerId}",
                    existingDeviceModels.Count, manufacturer.Id);

                // Attach the manufacturer and mark it as modified
                _dbContext.Manufacturers.Attach(manufacturer);
                _dbContext.Entry(manufacturer).State = EntityState.Modified;

                // Process each device model in the manufacturer
                foreach (var deviceModel in manufacturer.DeviceModels)
                {
                    // Check if this device model is already being tracked
                    var trackedDeviceModel = _dbContext.ChangeTracker.Entries<DeviceModel>()
                        .FirstOrDefault(e => e.Entity.Id == deviceModel.Id)?.Entity;

                    if (trackedDeviceModel != null)
                    {
                        // If a different instance is being tracked, detach it
                        if (!ReferenceEquals(trackedDeviceModel, deviceModel))
                        {
                            _logger.LogDebug("Detaching previously tracked device model instance {DeviceModelId}", deviceModel.Id);
                            _dbContext.Entry(trackedDeviceModel).State = EntityState.Detached;
                        }
                    }

                    // Check if this is a new device model
                    var existingDeviceModel = existingDeviceModels.FirstOrDefault(dm => dm.Id == deviceModel.Id);

                    if (existingDeviceModel == null)
                    {
                        // This is a new device model, mark it as added
                        _logger.LogDebug("Adding new device model {DeviceModelId} to manufacturer {ManufacturerId}",
                            deviceModel.Id, manufacturer.Id);
                        _dbContext.DeviceModels.Add(deviceModel);
                    }
                    else
                    {
                        // This is an existing device model, attach it and mark as modified
                        _logger.LogDebug("Updating existing device model {DeviceModelId}", deviceModel.Id);
                        _dbContext.DeviceModels.Attach(deviceModel);
                        _dbContext.Entry(deviceModel).State = EntityState.Modified;
                    }
                }

                // Find device models that have been removed
                var removedDeviceModels = existingDeviceModels
                    .Where(existingDm => !manufacturer.DeviceModels.Any(dm => dm.Id == existingDm.Id))
                    .ToList();

                // Mark removed device models for deletion
                foreach (var removedDeviceModel in removedDeviceModels)
                {
                    // Check if this device model is already being tracked
                    var trackedRemovedModel = _dbContext.ChangeTracker.Entries<DeviceModel>()
                        .FirstOrDefault(e => e.Entity.Id == removedDeviceModel.Id)?.Entity;

                    if (trackedRemovedModel != null)
                    {
                        // Use the tracked entity for deletion
                        _logger.LogDebug("Using tracked entity for deletion of device model {DeviceModelId}", removedDeviceModel.Id);
                        _dbContext.DeviceModels.Remove(trackedRemovedModel);
                    }
                    else
                    {
                        // Attach and mark for deletion
                        _logger.LogDebug("Marking device model {DeviceModelId} for deletion", removedDeviceModel.Id);
                        _dbContext.DeviceModels.Attach(removedDeviceModel);
                        _dbContext.DeviceModels.Remove(removedDeviceModel);
                    }
                }

                _logger.LogInformation("Manufacturer {ManufacturerId} marked for update with {AddedCount} added, {ModifiedCount} modified, and {RemovedCount} removed device models",
                    manufacturer.Id,
                    manufacturer.DeviceModels.Count(dm => !existingDeviceModels.Any(edm => edm.Id == dm.Id)),
                    manufacturer.DeviceModels.Count(dm => existingDeviceModels.Any(edm => edm.Id == dm.Id)),
                    removedDeviceModels.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating manufacturer with ID {ManufacturerId}", manufacturer.Id, ex.Message);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task UpdateManufacturerAsync(Manufacturer manufacturer)
        {
            await UpdateAsync(manufacturer);
            await _dbContext.SaveChangesAsync();
        }

        public async Task DeleteAsync(Guid id)
        {
            try
            {
                _logger.LogDebug("Deleting manufacturer with ID {ManufacturerId}", id);

                var manufacturer = await GetByIdAsync(id);
                if (manufacturer != null)
                {
                    _dbContext.Manufacturers.Remove(manufacturer);
                    _logger.LogInformation("Manufacturer {ManufacturerId} marked for deletion (not yet saved)", id);
                }
                else
                {
                    _logger.LogWarning("Manufacturer with ID {ManufacturerId} not found during delete operation", id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting manufacturer with ID {ManufacturerId}", id, ex.Message);
                throw;
            }
        }

        // Keep the old method for backward compatibility
        public async Task DeleteManufacturerAsync(Guid id)
        {
            await DeleteAsync(id);
            await _dbContext.SaveChangesAsync();
        }

        public async Task<IEnumerable<Manufacturer>> GetManufacturersWithFilteredDeviceModelsAsync(DeviceRole deviceRole)
        {
            try
            {
                _logger.LogInformation("Fetching manufacturers with device models filtered by role {DeviceRole}", deviceRole);

                // Get all manufacturers with their device models
                var allManufacturers = await _dbContext.Manufacturers
                    .Include(m => m.DeviceModels)
                    .ToListAsync();

                _logger.LogInformation("Total manufacturers in database: {Count}", allManufacturers.Count);

                foreach (var manufacturer in allManufacturers)
                {
                    _logger.LogInformation("DB - Manufacturer: {ManufacturerName} (ID: {ManufacturerId}) has {Count} device models",
                        manufacturer.Name, manufacturer.Id, manufacturer.DeviceModels.Count);

                    foreach (var model in manufacturer.DeviceModels)
                    {
                        _logger.LogInformation("DB - Model: {ModelName} (ID: {ModelId}, Role: {DeviceRole})",
                            model.Name, model.Id, model.DeviceRole);
                    }
                }

                // Filter the manufacturers in memory based on device role
                List<Manufacturer> filteredManufacturers;

                if (deviceRole == DeviceRole.Generic)
                {
                    // For Generic role, include manufacturers with Generic device models
                    filteredManufacturers = allManufacturers
                        .Where(m => m.DeviceModels.Any(dm => dm.DeviceRole == DeviceRole.Generic))
                        .ToList();
                }
                else
                {
                    // For specific roles, only include manufacturers with that specific role
                    // Exclude manufacturers that only have Generic device models
                    filteredManufacturers = allManufacturers
                        .Where(m => m.DeviceModels.Any(dm => dm.DeviceRole == deviceRole))
                        .ToList();
                }

                _logger.LogInformation("Found {Count} manufacturers with device models for role {DeviceRole}",
                    filteredManufacturers.Count, deviceRole);

                // Log the filtered manufacturers and their device models
                foreach (var manufacturer in filteredManufacturers)
                {
                    _logger.LogInformation("Filtered - Manufacturer: {ManufacturerName} (ID: {ManufacturerId}) has {Count} device models",
                        manufacturer.Name, manufacturer.Id, manufacturer.DeviceModels.Count);

                    // Count models that match the role
                    var matchingModels = manufacturer.DeviceModels
                        .Count(dm => dm.DeviceRole == deviceRole || dm.DeviceRole == DeviceRole.Generic);

                    _logger.LogInformation("Filtered - Manufacturer: {ManufacturerName} has {Count} models matching role {DeviceRole}",
                        manufacturer.Name, matchingModels, deviceRole);

                    foreach (var model in manufacturer.DeviceModels)
                    {
                        _logger.LogInformation("Filtered - Model: {ModelName} (ID: {ModelId}, Role: {DeviceRole}, Matches: {Matches})",
                            model.Name, model.Id, model.DeviceRole,
                            model.DeviceRole == deviceRole || model.DeviceRole == DeviceRole.Generic);
                    }
                }

                return filteredManufacturers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving manufacturers with filtered device models for role {DeviceRole}", deviceRole);
                throw;
            }
        }

        public async Task<IEnumerable<Manufacturer>> GetManufacturersByDeviceRoleAsync(DeviceRole deviceRole)
        {
            try
            {
                _logger.LogDebug("Getting manufacturers that have device models with role {DeviceRole}", deviceRole);

                // Get all manufacturers first (without device models for better performance)
                var allManufacturers = await _dbContext.Manufacturers.ToListAsync();

                // Then get all device models separately
                var allDeviceModels = await _dbContext.DeviceModels.ToListAsync();

                // Group device models by manufacturer ID for quick lookup
                var deviceModelsByManufacturer = allDeviceModels
                    .GroupBy(dm => dm.ManufacturerId)
                    .ToDictionary(g => g.Key, g => g.ToList());

                // Filter manufacturers based on device role
                List<Manufacturer> filteredManufacturers;

                if (deviceRole == DeviceRole.Generic)
                {
                    // For Generic role, include manufacturers with Generic device models
                    filteredManufacturers = allManufacturers
                        .Where(m => deviceModelsByManufacturer.ContainsKey(m.Id) &&
                               deviceModelsByManufacturer[m.Id].Any(dm => dm.DeviceRole == DeviceRole.Generic))
                        .ToList();
                }
                else
                {
                    // For specific roles, only include manufacturers with that specific role
                    // Exclude manufacturers that only have Generic device models
                    filteredManufacturers = allManufacturers
                        .Where(m => deviceModelsByManufacturer.ContainsKey(m.Id) &&
                               deviceModelsByManufacturer[m.Id].Any(dm => dm.DeviceRole == deviceRole))
                        .ToList();
                }

                _logger.LogInformation("Found {Count} manufacturers with device models for role {DeviceRole}",
                    filteredManufacturers.Count, deviceRole);

                return filteredManufacturers;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting manufacturers by device role {DeviceRole}", deviceRole);
                throw;
            }
        }
    }
}