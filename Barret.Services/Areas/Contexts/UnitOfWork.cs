using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Devices.Repositories;
using Barret.Services.Core.Areas.DeviceModels.Repositories;
using Barret.Services.Core.Areas.Manufacturers.Repositories;
using Barret.Services.Core.Areas.Vehicles.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Storage;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Areas.Contexts
{
    /// <summary>
    /// Implementation of the Unit of Work pattern for transaction management.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="UnitOfWork"/> class.
    /// </remarks>
    /// <param name="dbContext">The database context</param>
    /// <param name="logger">The logger</param>
    /// <param name="vesselRepository">The vessel repository</param>
    /// <param name="deviceRepository">The device repository</param>
    /// <param name="deviceModelRepository">The device model repository</param>
    /// <param name="manufacturerRepository">The manufacturer repository</param>
    /// <exception cref="ArgumentNullException">Thrown if any parameter is null</exception>
    public class UnitOfWork(
        IBarretDbContext dbContext,
        ILogger<UnitOfWork> logger,
        IVesselRepository vesselRepository,
        IDeviceRepository deviceRepository,
        IDeviceModelRepository deviceModelRepository,
        IManufacturerRepository manufacturerRepository) : IUnitOfWork
    {
        private readonly IBarretDbContext _dbContext = dbContext ?? throw new ArgumentNullException(nameof(dbContext));
        private readonly ILogger<UnitOfWork> _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        private readonly IVesselRepository _vesselRepository = vesselRepository ?? throw new ArgumentNullException(nameof(vesselRepository));
        private readonly IDeviceRepository _deviceRepository = deviceRepository ?? throw new ArgumentNullException(nameof(deviceRepository));
        private readonly IDeviceModelRepository _deviceModelRepository = deviceModelRepository ?? throw new ArgumentNullException(nameof(deviceModelRepository));
        private readonly IManufacturerRepository _manufacturerRepository = manufacturerRepository ?? throw new ArgumentNullException(nameof(manufacturerRepository));
        private bool _disposed;

        // Repository properties
        public IVesselRepository Vessels => _vesselRepository;
        public IDeviceRepository Devices => _deviceRepository;
        public IDeviceModelRepository DeviceModels => _deviceModelRepository;
        public IManufacturerRepository Manufacturers => _manufacturerRepository;

        public async Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Beginning database transaction");

            if (_dbContext is not DbContext context)
                throw new InvalidOperationException("DbContext is not available");

            return await context.Database.BeginTransactionAsync(cancellationToken);
        }

        public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Committing database transaction");

            if (_dbContext is not DbContext context)
                throw new InvalidOperationException("DbContext is not available");

            await context.Database.CommitTransactionAsync(cancellationToken);
        }

        public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Rolling back database transaction");

            if (_dbContext is not DbContext context)
                throw new InvalidOperationException("DbContext is not available");

            await context.Database.RollbackTransactionAsync(cancellationToken);
        }

        public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
        {
            _logger.LogDebug("Saving changes to database");

            if (_dbContext is not DbContext context)
                throw new InvalidOperationException("DbContext is not available");

            var result = await context.SaveChangesAsync(cancellationToken);
            _logger.LogInformation("Saved {Count} changes to database", result);
            return result;
        }

        public void ClearTracking()
        {
            if (_dbContext is not DbContext context) return;

            context.ChangeTracker.Clear();
            _logger.LogDebug("Cleared all entities from change tracker");
        }

        public void DetachAllEntities()
        {
            if (_dbContext is not DbContext context) return;

            var changedEntriesCopy = context.ChangeTracker.Entries()
                .Where(e => e.State == EntityState.Added ||
                            e.State == EntityState.Modified ||
                            e.State == EntityState.Deleted)
                .ToList();

            foreach (var entry in changedEntriesCopy)
            {
                entry.State = EntityState.Detached;
            }

            _logger.LogDebug("Detached {Count} entities from change tracker", changedEntriesCopy.Count);
        }

        public EntityEntry<TEntity> SafeAttach<TEntity>(TEntity entity, EntityState state) where TEntity : class
        {
            if (_dbContext is not DbContext context)
                throw new InvalidOperationException("DbContext is not available");

            var entry = context.Entry(entity);
            if (entry.State == EntityState.Detached)
            {
                // Entity is not being tracked, so we can safely attach it
                entry.State = state;
            }
            else if (entry.State != state)
            {
                // Entity is already being tracked but with a different state
                // We need to update its state
                _logger.LogDebug("Entity of type {EntityType} is already being tracked with state {CurrentState}, updating to {NewState}",
                    typeof(TEntity).Name, entry.State, state);
                entry.State = state;
            }

            return entry;
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // Dispose managed resources
                    (_dbContext as DbContext)?.Dispose();
                }

                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
