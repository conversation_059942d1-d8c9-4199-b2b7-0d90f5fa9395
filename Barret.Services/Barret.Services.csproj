﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Barret.Services.Core\Barret.Services.Core.csproj" />
    <ProjectReference Include="..\Barret.Core\Barret.Core.csproj" />
    <ProjectReference Include="..\Barret.Core.Abstractions\Barret.Core.Abstractions.csproj" />
    <ProjectReference Include="..\Barret.Shared\Barret.Shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="12.0.1" />
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="CsvHelper" Version="33.0.1" />
    <PackageReference Include="System.IO.Compression" Version="4.3.0" />
  </ItemGroup>
</Project>