# Device Editor Tab Configuration

This document explains how to configure which tabs are displayed for each device role in the device editors using the external JSON configuration system.

## Overview

The device editor tab configuration system has been migrated from hardcoded dictionaries to an external JSON configuration file. This allows easy modification of device editor behavior without requiring code changes.

## Configuration File Location

The configuration file is located at:
```
Barret.Web.Server/Configuration/DeviceEditorConfiguration.json
```

## Configuration Structure

### Root Structure
```json
{
  "deviceEditorConfiguration": {
    "defaultConfiguration": { ... },
    "deviceRoleConfigurations": { ... }
  }
}
```

### Default Configuration
The `defaultConfiguration` section defines the fallback behavior for device roles that don't have specific configurations:

```json
"defaultConfiguration": {
  "tabs": {
    "general": { "enabled": true, "order": 1 },
    "makeModel": { "enabled": true, "order": 2 },
    "position": { "enabled": true, "order": 3, "type": "standard" },
    "connection": { "enabled": true, "order": 4 },
    "settings": { "enabled": false, "order": 5 },
    "alarms": { "enabled": false, "order": 6 }
  }
}
```

### Device Role Configurations
The `deviceRoleConfigurations` section contains specific configurations for each device role:

```json
"deviceRoleConfigurations": {
  "Camera": {
    "tabs": {
      "general": { "enabled": true, "order": 1 },
      "makeModel": { "enabled": true, "order": 2 },
      "position": { "enabled": true, "order": 3, "type": "standard" },
      "connection": { "enabled": true, "order": 4 },
      "settings": { "enabled": true, "order": 5, "componentType": "CameraTab" },
      "alarms": { "enabled": true, "order": 6 }
    }
  }
}
```

## Tab Configuration Properties

### Common Properties
- **`enabled`** (boolean): Whether the tab should be displayed
- **`order`** (integer): Display order of the tab (lower numbers appear first)

### Tab-Specific Properties

#### Position Tab
- **`type`** (string): Position tab variant
  - `"standard"`: Standard 3D positioning (X, Y, Z coordinates)
  - `"maritime"`: Maritime positioning (Bow/Stern selection)

#### Settings Tab
- **`componentType`** (string): The specific settings component to render
  - Available components: `"CameraTab"`, `"EngineTab"`, `"ThrusterTab"`, `"RadarTab"`, `"LightTab"`, `"RudderTab"`, `"HornTab"`, `"AntennaTab"`, `"AutopilotTab"`

## Available Tabs

1. **`general`**: Basic device information (name, description, etc.)
2. **`makeModel`**: Manufacturer and model selection
3. **`position`**: Device positioning (standard or maritime)
4. **`connection`**: Device connections management
5. **`settings`**: Device-specific settings (requires `componentType`)
6. **`alarms`**: Device alarms configuration

## Examples

### Camera Device Configuration
```json
"Camera": {
  "tabs": {
    "general": { "enabled": true, "order": 1 },
    "makeModel": { "enabled": true, "order": 2 },
    "position": { "enabled": true, "order": 3, "type": "standard" },
    "connection": { "enabled": true, "order": 4 },
    "settings": { "enabled": true, "order": 5, "componentType": "CameraTab" },
    "alarms": { "enabled": true, "order": 6 }
  }
}
```

### Radar Device Configuration (with Maritime Positioning)
```json
"Radar": {
  "tabs": {
    "general": { "enabled": true, "order": 1 },
    "makeModel": { "enabled": true, "order": 2 },
    "position": { "enabled": true, "order": 3, "type": "maritime" },
    "connection": { "enabled": true, "order": 4 },
    "settings": { "enabled": true, "order": 5, "componentType": "RadarTab" },
    "alarms": { "enabled": true, "order": 6 }
  }
}
```

### Generic Device Configuration (No Settings Tab)
```json
"Generic": {
  "tabs": {
    "general": { "enabled": true, "order": 1 },
    "makeModel": { "enabled": true, "order": 2 },
    "position": { "enabled": true, "order": 3, "type": "standard" },
    "connection": { "enabled": true, "order": 4 },
    "settings": { "enabled": false, "order": 5 },
    "alarms": { "enabled": true, "order": 6 }
  }
}
```

## How to Modify Configuration

1. **Edit the JSON file**: Modify `DeviceEditorConfiguration.json`
2. **Restart the application**: Changes are loaded at startup
3. **Reload configuration**: Use the configuration service's `ReloadConfiguration()` method (if implemented in admin interface)

## Backward Compatibility

The system maintains backward compatibility with the existing `DeviceTabConfiguration` class used by the Razor views. The new configuration service automatically converts the JSON configuration to the legacy format when needed.

## Fallback Behavior

- If a device role is not found in the configuration, the default configuration is used
- If the configuration file is missing or invalid, a hardcoded fallback configuration is used that matches the previous behavior
- Unknown settings component types are logged as warnings and ignored

## Technical Implementation

- **Configuration Service**: `IDeviceEditorConfigurationService` / `DeviceEditorConfigurationService`
- **Tab Service**: `DeviceEditorTabService` (updated to use configuration service)
- **Models**: Located in `Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Configuration`
- **Registration**: Configured in `ServiceCollectionExtensions.AddFrontendServices()`
