{"deviceEditorConfiguration": {"defaultConfiguration": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3, "type": "standard"}, "connection": {"enabled": false, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "deviceRoleConfigurations": {"Camera": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": true, "order": 3, "type": "maritime"}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": true, "order": 5, "componentType": "CameraTab"}, "alarms": {"enabled": true, "order": 6}}}, "Engine": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": false, "order": 4}, "settings": {"enabled": true, "order": 5, "componentType": "EngineTab"}, "alarms": {"enabled": true, "order": 6}}}, "Thruster": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": false, "order": 4}, "settings": {"enabled": true, "order": 5, "componentType": "Thr<PERSON>Tab"}, "alarms": {"enabled": true, "order": 6}}}, "Radar": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": true, "order": 3, "type": "maritime"}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": true, "order": 5, "componentType": "RadarTab"}, "alarms": {"enabled": true, "order": 6}}}, "Light": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": false, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Rudder": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": false, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Horn": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": false, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Antenna": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Autopilot": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Generic": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": true, "order": 3, "type": "standard"}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Sensor": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "AMP": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "NVRScreen": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": true, "order": 3, "type": "maritime"}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "NVRRecording": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": true, "order": 3, "type": "maritime"}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "VHFMariphone": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "VHFNetworkInterface": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "AudioHub": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "PAAudio": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Trackpilot": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "CabinetReadoutIO": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "OperatorPanelIO": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "GPU": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "HMI": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "SafetySystemHead": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "SPAP": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Firewall": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Gateway": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Switch": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Generator": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": false, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Mast": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": false, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "EngineInterface": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "ThrusterInterface": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "AutopilotInterface": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Framegrabber": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "Mimer": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "NavData": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "WindowsPC": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "PA": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}, "SP": {"tabs": {"general": {"enabled": true, "order": 1}, "makeModel": {"enabled": true, "order": 2}, "position": {"enabled": false, "order": 3}, "connection": {"enabled": true, "order": 4}, "settings": {"enabled": false, "order": 5}, "alarms": {"enabled": true, "order": 6}}}}}}