// File system access functions

// Show a directory picker dialog and return the selected directory path
window.showDirectoryPicker = function () {
    // Since we're in a Blazor Server app, we can't use the File System Access API directly
    // Instead, we'll use a simple prompt to get the directory path from the user
    const dirPath = prompt("Enter the directory path where you want to save the configuration files:", "C:\\Configs");
    return dirPath;
};
