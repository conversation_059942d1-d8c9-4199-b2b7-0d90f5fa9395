// File utilities for Barret

// Download a file with the given content
function downloadFile(fileName, contentType, content) {
    const blob = new Blob([content], { type: contentType });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    
    setTimeout(() => {
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }, 0);
}

// Register beforeunload event to prevent accidental navigation
function registerBeforeUnload() {
    window.addEventListener('beforeunload', handleBeforeUnload);
}

// Unregister beforeunload event
function unregisterBeforeUnload() {
    window.removeEventListener('beforeunload', handleBeforeUnload);
}

// Handle beforeunload event
function handleBeforeUnload(e) {
    e.preventDefault();
    e.returnValue = '';
    return '';
}
