/*
 * Confirmation Dialog Radzen Styling
 * This file provides custom styling for the Radzen-based ConfirmationDialog component
 * to match the application's minimalist monochromatic design system
 */

/* Dialog Container - Following the styling guide */
.barret-confirmation-dialog {
  background: white !important;
  border-radius: var(--border-radius-lg) !important;
  border: none !important;
  box-shadow: var(--shadow-lg) !important;
  max-width: 90vw;
  max-height: 90vh;
}

/* Dialog Content Area */
.barret-confirmation-dialog .rz-dialog-content {
  padding: 0 !important;
  border-radius: inherit;
}

/* Hide default Radzen title bar since we use custom header */
.barret-confirmation-dialog .rz-dialog-titlebar {
  display: none !important;
}

/* Dialog Header Styling */
.barret-confirmation-dialog .rz-dialog-content > div:first-child {
  padding: var(--space-lg) var(--space-lg) var(--space-sm) var(--space-lg);
  border-bottom: var(--border-width-thin) solid var(--gray-50);
}

/* Dialog Body Content */
.barret-confirmation-dialog .rz-dialog-content > div:nth-child(2) {
  padding: var(--space-md) var(--space-lg);
}

/* Dialog Footer/Buttons Area */
.barret-confirmation-dialog .rz-dialog-content > div:last-child {
  padding: var(--space-sm) var(--space-lg) var(--space-lg) var(--space-lg);
  border-top: var(--border-width-thin) solid var(--gray-50);
  display: flex;
  justify-content: flex-end;
  gap: var(--space-sm);
}

/* Button Styling - Following application patterns */
.barret-confirmation-dialog .rz-button {
  border-radius: var(--button-border-radius) !important;
  font-weight: var(--font-weight-medium) !important;
  font-size: var(--font-size-sm) !important;
  letter-spacing: var(--letter-spacing-wide) !important;
  transition: all var(--transition-normal) !important;
  height: var(--button-md-height) !important;
  padding: var(--button-md-padding) !important;
  border: var(--border-width-thin) solid transparent !important;
  min-width: auto !important;
  line-height: var(--line-height-none) !important;
}

/* Primary/Confirm Button */
.barret-confirmation-dialog .rz-button-primary,
.barret-confirmation-dialog .rz-button:last-child {
  background: var(--gray-900) !important;
  color: #ffffff !important;
  border-color: var(--gray-900) !important;
}

.barret-confirmation-dialog .rz-button-primary:hover,
.barret-confirmation-dialog .rz-button:last-child:hover {
  background: var(--gray-800) !important;
  border-color: var(--gray-800) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.barret-confirmation-dialog .rz-button-primary:active,
.barret-confirmation-dialog .rz-button:last-child:active {
  background: var(--gray-900) !important;
  transform: translateY(1px);
}

/* Secondary/Cancel Button */
.barret-confirmation-dialog .rz-button-secondary,
.barret-confirmation-dialog .rz-button:first-child {
  background: transparent !important;
  color: var(--gray-700) !important;
  border-color: var(--gray-200) !important;
}

.barret-confirmation-dialog .rz-button-secondary:hover,
.barret-confirmation-dialog .rz-button:first-child:hover {
  background: var(--gray-50) !important;
  color: var(--gray-900) !important;
  border-color: var(--gray-300) !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.barret-confirmation-dialog .rz-button-secondary:active,
.barret-confirmation-dialog .rz-button:first-child:active {
  background: var(--gray-100) !important;
  transform: translateY(1px);
}

/* Danger Button Variant (for delete confirmations) */
.barret-confirmation-dialog .rz-button-danger {
  background: var(--error) !important;
  color: #ffffff !important;
  border-color: var(--error) !important;
}

.barret-confirmation-dialog .rz-button-danger:hover {
  background: #dc2626 !important;
  border-color: #dc2626 !important;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.barret-confirmation-dialog .rz-button-danger:active {
  background: var(--error) !important;
  transform: translateY(1px);
}

/* Dialog Overlay */
.rz-dialog-mask {
  background-color: rgba(0, 0, 0, 0.5) !important;
  backdrop-filter: blur(2px);
}

/* Animation - Subtle entrance */
.barret-confirmation-dialog {
  animation: dialogFadeIn 0.2s ease-out;
}

@keyframes dialogFadeIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* Focus States for Accessibility */
.barret-confirmation-dialog .rz-button:focus {
  outline: 2px solid var(--gray-300) !important;
  outline-offset: 2px;
}

.barret-confirmation-dialog .rz-button-primary:focus,
.barret-confirmation-dialog .rz-button:last-child:focus {
  outline-color: var(--gray-800) !important;
}

.barret-confirmation-dialog .rz-button-danger:focus {
  outline-color: var(--error) !important;
}

/* Responsive Design */
@media (max-width: 640px) {
  .barret-confirmation-dialog {
    width: 95vw !important;
    margin: 20px auto;
  }
  
  .barret-confirmation-dialog .rz-dialog-content > div:first-child {
    padding: var(--space-md);
  }
  
  .barret-confirmation-dialog .rz-dialog-content > div:nth-child(2) {
    padding: var(--space-sm) var(--space-md);
  }
  
  .barret-confirmation-dialog .rz-dialog-content > div:last-child {
    padding: var(--space-sm) var(--space-md) var(--space-md) var(--space-md);
    flex-direction: column;
    gap: var(--space-xs);
  }
  
  .barret-confirmation-dialog .rz-button {
    width: 100%;
    justify-content: center;
  }
}

/* Typography Adjustments */
.barret-confirmation-dialog h1,
.barret-confirmation-dialog h2,
.barret-confirmation-dialog h3 {
  font-size: var(--font-size-lg) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--gray-900) !important;
  margin: 0 0 var(--space-xs) 0 !important;
}

.barret-confirmation-dialog p {
  color: var(--gray-600) !important;
  font-size: var(--font-size-sm) !important;
  line-height: var(--line-height-relaxed) !important;
  margin: 0 !important;
}

/* Icon Styling */
.barret-confirmation-dialog i {
  font-size: 1.5rem;
}

/* Ensure proper z-index */
.rz-dialog-mask {
  z-index: 1050 !important;
}

.barret-confirmation-dialog {
  z-index: 1051 !important;
}
