/* Device Import Dialog Styles */

/* Selected row highlighting */
.selected-row {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}

/* Device details section */
.device-details {
    background-color: var(--gray-50);
    border-radius: var(--border-radius-sm);
}

/* Grid row hover effects */
.dxbl-grid-data-row {
    cursor: pointer;
}

.dxbl-grid-data-row:hover {
    background-color: rgba(var(--bs-primary-rgb), 0.05);
}

/* Grid header styling */
.dxbl-grid-header {
    background-color: var(--gray-100);
}
