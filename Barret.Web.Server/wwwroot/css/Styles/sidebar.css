/* Blazor-Style Side Navigation Component */

/* Layout Structure */
.sidebar {
  height: 100vh; /* h-screen */
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  width: 256px; /* w-64 */
  z-index: 10;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
}

.main-content-with-sidebar {
  margin-left: 256px; /* ml-64 to offset content */
  transition: margin-left 0.3s ease;
}

/* Core Styling */
.sidebar {
  background-color: var(--color-background);
  border-right: var(--border-width-thin) solid var(--color-border);
  box-shadow: none;
}

/* Navigation Items */
.sidebar-nav {
  padding: 0.5rem;
}

.nav-section {
  margin-bottom: 1rem;
}

.nav-section-header {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: var(--font-weight-semibold);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--color-muted-foreground);
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.625rem 0.75rem;
  border-radius: 0;
  margin: 0;
  border-left: 3px solid transparent;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-foreground);
  transition: all 0.2s ease-out;
}

.nav-link:hover {
  background-color: var(--color-secondary);
  color: var(--color-foreground);
}

.nav-link.active {
  border-left-color: var(--color-primary);
  background-color: var(--color-secondary);
  color: var(--color-primary);
  box-shadow: none;
}

.nav-link-icon {
  height: 1.25rem; /* h-5 */
  width: 1.25rem; /* w-5 */
  margin-right: 0.75rem; /* gap-3 */
  color: var(--color-muted-foreground); /* text-gray-500 */
}

.nav-link.active .nav-link-icon {
  color: var(--color-primary); /* text-gray-900 */
}

/* Nested Navigation */
.nav-children {
  padding-left: 1rem; /* ps-4 */
  margin-top: 0.25rem; /* mt-1 */
  margin-bottom: 0.25rem; /* mb-1 */
}

/* Sidebar Header and Footer */
.sidebar-header {
  padding: 1rem; /* p-4 */
  border-bottom: 1px solid var(--color-border); /* border-b border-gray-200 */
}

.sidebar-title {
  font-size: 1.125rem; /* text-lg */
  font-weight: 600; /* font-semibold */
  margin-bottom: 0.25rem;
}

.sidebar-subtitle {
  font-size: 0.875rem; /* text-sm */
  color: var(--color-muted-foreground); /* text-gray-500 */
}

.sidebar-footer {
  padding: 1rem; /* p-4 */
  border-top: 1px solid var(--color-border); /* border-t border-gray-200 */
  margin-top: auto;
}

/* Mobile Toggle Button */
.sidebar-toggle {
  position: fixed;
  top: 1rem;
  left: 1rem;
  z-index: 30;
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid var(--color-border);
  border-radius: 0.375rem;
  box-shadow: var(--shadow-sm);
  cursor: pointer;
}

.sidebar-toggle i {
  font-size: 1.5rem;
  color: var(--color-foreground);
}

.sidebar-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  border-radius: 9999px;
  cursor: pointer;
}

.sidebar-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.sidebar-close i {
  font-size: 1.25rem;
  color: var(--color-foreground);
}

/* Responsive Behavior */
@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .main-content-with-sidebar {
    margin-left: 0;
  }
}
