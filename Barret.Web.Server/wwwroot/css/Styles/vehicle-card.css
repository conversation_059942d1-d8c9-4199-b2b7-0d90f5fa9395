/*
 * Vehicle Card Styles
 * This file contains styles specific to vehicle cards
 */

/* Vehicle Card */
.vehicle-card {
    transition: all var(--transition-normal);
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: var(--border-width-thin) solid var(--gray-200);
    background-color: #ffffff;
    box-shadow: none;
    transform: translateY(0);
}

.vehicle-card:hover {
    transform: translateY(0);
    box-shadow: var(--shadow-hover);
    border-color: var(--gray-200);
}

.vehicle-card .card-title a {
    color: var(--color-foreground);
    text-decoration: none;
}

.vehicle-card .card-title a:hover {
    color: var(--color-primary);
}

.vehicle-card .card-body {
    padding: var(--space-4);
    display: flex;
    flex-direction: column;
}

.vehicle-card .vehicle-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #ffffff;
    color: var(--gray-800);
    font-size: 1.25rem;
    border: var(--border-width-thin) solid var(--gray-200);
}

.vehicle-card .badge {
    font-weight: var(--font-weight-medium);
    font-size: var(--font-size-xs);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--border-radius-pill);
}

.vehicle-card .badge.bg-light {
    border: var(--border-width-thin) solid var(--color-border);
}
