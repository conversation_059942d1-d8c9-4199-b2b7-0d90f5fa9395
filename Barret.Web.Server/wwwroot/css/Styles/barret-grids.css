/*
 * Barret Grid Components Styles
 * This file contains styles for all Barret grid components
 * Based on the Minimalist Monochromatic Design Guide
 */

/* Common grid styles */
.barret-data-grid {
    border: none;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
}

.barret-data-grid :deep(.dxbl-grid-header) {
    background-color: #ffffff;
    font-weight: var(--font-weight-semibold);
    color: var(--gray-800);
    border-bottom: var(--border-width-thin) solid var(--gray-300);
}

.barret-data-grid :deep(.dxbl-grid-header-content) {
    padding: var(--space-md) var(--space-lg);
}

.barret-data-grid :deep(.dxbl-grid-data-row) {
    border-bottom: var(--border-width-thin) solid var(--gray-200);
    background-color: #ffffff;
}

.barret-data-grid :deep(.dxbl-grid-data-row:hover) {
    background-color: var(--gray-50);
}

.barret-data-grid :deep(.dxbl-grid-cell-data) {
    padding: var(--space-md) var(--space-lg);
    color: var(--gray-800);
}

.barret-data-grid :deep(.dxbl-btn) {
    border-radius: var(--button-border-radius);
}

.barret-data-grid :deep(.dxbl-grid-empty-data) {
    padding: var(--space-xl);
    color: var(--gray-500);
    font-style: italic;
    background-color: #ffffff;
}

/* Vehicle list grid specific styles */
.vehicle-list-grid {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: var(--border-width-thin) solid var(--color-border);
}

.vehicle-list-grid :deep(.dxbl-grid-data-row) {
    cursor: pointer;
}

.vehicle-list-grid :deep(.dxbl-grid-data-row:hover) {
    background-color: var(--gray-50);
}

/* Device list grid specific styles */
.device-list-grid {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: var(--border-width-thin) solid var(--color-border);
}

.device-list-grid :deep(.dxbl-grid-data-row) {
    cursor: pointer;
}

/* Device import grid specific styles */
.device-import-grid {
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    border: var(--border-width-thin) solid var(--gray-300);
}

.device-import-grid :deep(.selected-row) {
    background-color: rgba(var(--bs-primary-rgb), 0.1) !important;
}

.device-import-grid :deep(.device-details) {
    background-color: var(--gray-50);
    border-radius: var(--border-radius-sm);
    padding: var(--space-md);
}

/* Nested grid styles */
.nested-grid {
    margin-top: var(--space-md);
    margin-bottom: var(--space-md);
}

/* Loading indicator */
.grid-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: var(--space-lg);
}

/* Empty state */
.grid-empty-state {
    padding: var(--space-lg);
    text-align: center;
    color: var(--gray-500);
}
