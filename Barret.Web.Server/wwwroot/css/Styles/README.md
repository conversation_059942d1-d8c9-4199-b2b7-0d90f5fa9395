# Barret Vehicle Configurator CSS Styling System

This directory contains the consolidated CSS styling system for the Barret Vehicle Configurator. The system is designed to provide a consistent, maintainable, and scalable approach to styling the application.

## File Structure

- `_variables.css` - Design tokens and variables
- `_base.css` - Base element styling
- `_components.css` - Reusable component styling
- `_layout.css` - Layout and structural styling
- `_utilities.css` - Utility classes
- `_devexpress.css` - DevExpress component styling
- `main.css` - Main CSS file that imports all other CSS files

## Usage

To use the styling system, include the main CSS file in your Razor component or HTML file:

```html
<link href="/Styles/main.css" rel="stylesheet" />
```

## Design Principles

### CSS Variables

The styling system uses CSS variables (custom properties) for all design tokens. This allows for consistent theming and easy updates.

```css
:root {
  --color-primary: #333333;
  --font-size-base: 1rem;
  --space-3: 1rem;
}
```

### BEM Naming Convention

The styling system follows the BEM (Block, Element, Modifier) naming convention for CSS classes:

- Block: `.card`
- Element: `.card__title`
- Modifier: `.card--featured`

### Responsive Design

The styling system includes responsive breakpoints and utilities for creating responsive layouts:

```css
@media (max-width: 768px) {
  /* Mobile styles */
}

@media (min-width: 769px) and (max-width: 1024px) {
  /* Tablet styles */
}

@media (min-width: 1025px) {
  /* Desktop styles */
}
```

## Component Styling

### Cards

```html
<div class="card">
  <div class="card-header">Header</div>
  <div class="card-body">Content</div>
  <div class="card-footer">Footer</div>
</div>
```

### Buttons

```html
<button class="btn btn-primary">Primary Button</button>
<button class="btn btn-secondary">Secondary Button</button>
```

### Forms

```html
<div class="form-group">
  <label class="form-label">Label</label>
  <input class="form-control" type="text" />
  <div class="form-text">Help text</div>
</div>
```

## DevExpress Integration

The styling system includes custom styling for DevExpress components to ensure they match the application's design language. These styles are defined in `_devexpress.css`.

### Example

```html
<DxButton RenderStyle="ButtonRenderStyle.Primary" Text="Button" />
```

## Utility Classes

The styling system includes utility classes for common styling needs:

```html
<div class="mt-3">Margin top 3</div>
<div class="p-4">Padding 4</div>
<div class="text-primary">Primary text color</div>
<div class="bg-secondary">Secondary background color</div>
```

## Extending the System

When adding new styles, follow these guidelines:

1. Use existing variables and utilities whenever possible
2. Add new variables to `_variables.css` if needed
3. Add new component styles to `_components.css`
4. Add new layout styles to `_layout.css`
5. Add new utility classes to `_utilities.css`
6. Add new DevExpress component styles to `_devexpress.css`

## Maintenance

The styling system is designed to be maintainable and scalable. When making changes:

1. Update variables instead of hard-coded values
2. Use the existing naming conventions
3. Document any new components or utilities
4. Test changes across different screen sizes
