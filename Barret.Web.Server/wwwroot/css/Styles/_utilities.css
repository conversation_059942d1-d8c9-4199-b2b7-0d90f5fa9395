/*
 * Utility Styles
 * This file contains utility classes
 */

/* Background colors */
.bg-primary { background-color: var(--color-primary) !important; }
.bg-secondary { background-color: var(--color-secondary) !important; }
.bg-muted { background-color: var(--color-muted) !important; }
.bg-background { background-color: var(--color-background) !important; }
.bg-success { background-color: var(--color-success) !important; }
.bg-danger { background-color: var(--color-danger) !important; }
.bg-warning { background-color: var(--color-warning) !important; }
.bg-info { background-color: var(--color-info) !important; }

/* Text colors */
.text-primary { color: var(--color-primary) !important; }
.text-secondary { color: var(--color-secondary-foreground) !important; }
.text-muted { color: var(--color-muted-foreground) !important; }
.text-foreground { color: var(--color-foreground) !important; }
.text-background { color: var(--color-background) !important; }
.text-success { color: var(--color-success) !important; }
.text-danger { color: var(--color-danger) !important; }
.text-warning { color: var(--color-warning) !important; }
.text-info { color: var(--color-info) !important; }

/* Border utilities */
.border { border: var(--border-width-thin) solid var(--color-border) !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: var(--border-width-thin) solid var(--color-border) !important; }
.border-end { border-right: var(--border-width-thin) solid var(--color-border) !important; }
.border-bottom { border-bottom: var(--border-width-thin) solid var(--color-border) !important; }
.border-start { border-left: var(--border-width-thin) solid var(--color-border) !important; }

.border-primary { border-color: var(--color-primary) !important; }
.border-secondary { border-color: var(--color-secondary) !important; }
.border-success { border-color: var(--color-success) !important; }
.border-danger { border-color: var(--color-danger) !important; }
.border-warning { border-color: var(--color-warning) !important; }
.border-info { border-color: var(--color-info) !important; }

/* Border radius */
.rounded { border-radius: var(--border-radius-md) !important; }
.rounded-sm { border-radius: var(--border-radius-sm) !important; }
.rounded-lg { border-radius: var(--border-radius-lg) !important; }
.rounded-pill { border-radius: var(--border-radius-pill) !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-0 { border-radius: 0 !important; }

/* Shadow utilities */
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: var(--shadow-sm) !important; }
.shadow { box-shadow: var(--shadow-md) !important; }
.shadow-lg { box-shadow: var(--shadow-lg) !important; }

/* Spacing utilities */
.m-0 { margin: 0 !important; }
.m-1 { margin: var(--space-1) !important; }
.m-2 { margin: var(--space-2) !important; }
.m-3 { margin: var(--space-3) !important; }
.m-4 { margin: var(--space-4) !important; }
.m-5 { margin: var(--space-5) !important; }
.m-6 { margin: var(--space-6) !important; }

.mt-0 { margin-top: 0 !important; }
.mt-1 { margin-top: var(--space-1) !important; }
.mt-2 { margin-top: var(--space-2) !important; }
.mt-3 { margin-top: var(--space-3) !important; }
.mt-4 { margin-top: var(--space-4) !important; }
.mt-5 { margin-top: var(--space-5) !important; }
.mt-6 { margin-top: var(--space-6) !important; }

.me-0 { margin-right: 0 !important; }
.me-1 { margin-right: var(--space-1) !important; }
.me-2 { margin-right: var(--space-2) !important; }
.me-3 { margin-right: var(--space-3) !important; }
.me-4 { margin-right: var(--space-4) !important; }
.me-5 { margin-right: var(--space-5) !important; }
.me-6 { margin-right: var(--space-6) !important; }

.mb-0 { margin-bottom: 0 !important; }
.mb-1 { margin-bottom: var(--space-1) !important; }
.mb-2 { margin-bottom: var(--space-2) !important; }
.mb-3 { margin-bottom: var(--space-3) !important; }
.mb-4 { margin-bottom: var(--space-4) !important; }
.mb-5 { margin-bottom: var(--space-5) !important; }
.mb-6 { margin-bottom: var(--space-6) !important; }

.ms-0 { margin-left: 0 !important; }
.ms-1 { margin-left: var(--space-1) !important; }
.ms-2 { margin-left: var(--space-2) !important; }
.ms-3 { margin-left: var(--space-3) !important; }
.ms-4 { margin-left: var(--space-4) !important; }
.ms-5 { margin-left: var(--space-5) !important; }
.ms-6 { margin-left: var(--space-6) !important; }

.mx-0 { margin-left: 0 !important; margin-right: 0 !important; }
.mx-1 { margin-left: var(--space-1) !important; margin-right: var(--space-1) !important; }
.mx-2 { margin-left: var(--space-2) !important; margin-right: var(--space-2) !important; }
.mx-3 { margin-left: var(--space-3) !important; margin-right: var(--space-3) !important; }
.mx-4 { margin-left: var(--space-4) !important; margin-right: var(--space-4) !important; }
.mx-5 { margin-left: var(--space-5) !important; margin-right: var(--space-5) !important; }
.mx-6 { margin-left: var(--space-6) !important; margin-right: var(--space-6) !important; }
.mx-auto { margin-left: auto !important; margin-right: auto !important; }

.my-0 { margin-top: 0 !important; margin-bottom: 0 !important; }
.my-1 { margin-top: var(--space-1) !important; margin-bottom: var(--space-1) !important; }
.my-2 { margin-top: var(--space-2) !important; margin-bottom: var(--space-2) !important; }
.my-3 { margin-top: var(--space-3) !important; margin-bottom: var(--space-3) !important; }
.my-4 { margin-top: var(--space-4) !important; margin-bottom: var(--space-4) !important; }
.my-5 { margin-top: var(--space-5) !important; margin-bottom: var(--space-5) !important; }
.my-6 { margin-top: var(--space-6) !important; margin-bottom: var(--space-6) !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: var(--space-1) !important; }
.p-2 { padding: var(--space-2) !important; }
.p-3 { padding: var(--space-3) !important; }
.p-4 { padding: var(--space-4) !important; }
.p-5 { padding: var(--space-5) !important; }
.p-6 { padding: var(--space-6) !important; }

.pt-0 { padding-top: 0 !important; }
.pt-1 { padding-top: var(--space-1) !important; }
.pt-2 { padding-top: var(--space-2) !important; }
.pt-3 { padding-top: var(--space-3) !important; }
.pt-4 { padding-top: var(--space-4) !important; }
.pt-5 { padding-top: var(--space-5) !important; }
.pt-6 { padding-top: var(--space-6) !important; }

.pe-0 { padding-right: 0 !important; }
.pe-1 { padding-right: var(--space-1) !important; }
.pe-2 { padding-right: var(--space-2) !important; }
.pe-3 { padding-right: var(--space-3) !important; }
.pe-4 { padding-right: var(--space-4) !important; }
.pe-5 { padding-right: var(--space-5) !important; }
.pe-6 { padding-right: var(--space-6) !important; }

.pb-0 { padding-bottom: 0 !important; }
.pb-1 { padding-bottom: var(--space-1) !important; }
.pb-2 { padding-bottom: var(--space-2) !important; }
.pb-3 { padding-bottom: var(--space-3) !important; }
.pb-4 { padding-bottom: var(--space-4) !important; }
.pb-5 { padding-bottom: var(--space-5) !important; }
.pb-6 { padding-bottom: var(--space-6) !important; }

.ps-0 { padding-left: 0 !important; }
.ps-1 { padding-left: var(--space-1) !important; }
.ps-2 { padding-left: var(--space-2) !important; }
.ps-3 { padding-left: var(--space-3) !important; }
.ps-4 { padding-left: var(--space-4) !important; }
.ps-5 { padding-left: var(--space-5) !important; }
.ps-6 { padding-left: var(--space-6) !important; }

.px-0 { padding-left: 0 !important; padding-right: 0 !important; }
.px-1 { padding-left: var(--space-1) !important; padding-right: var(--space-1) !important; }
.px-2 { padding-left: var(--space-2) !important; padding-right: var(--space-2) !important; }
.px-3 { padding-left: var(--space-3) !important; padding-right: var(--space-3) !important; }
.px-4 { padding-left: var(--space-4) !important; padding-right: var(--space-4) !important; }
.px-5 { padding-left: var(--space-5) !important; padding-right: var(--space-5) !important; }
.px-6 { padding-left: var(--space-6) !important; padding-right: var(--space-6) !important; }

.py-0 { padding-top: 0 !important; padding-bottom: 0 !important; }
.py-1 { padding-top: var(--space-1) !important; padding-bottom: var(--space-1) !important; }
.py-2 { padding-top: var(--space-2) !important; padding-bottom: var(--space-2) !important; }
.py-3 { padding-top: var(--space-3) !important; padding-bottom: var(--space-3) !important; }
.py-4 { padding-top: var(--space-4) !important; padding-bottom: var(--space-4) !important; }
.py-5 { padding-top: var(--space-5) !important; padding-bottom: var(--space-5) !important; }
.py-6 { padding-top: var(--space-6) !important; padding-bottom: var(--space-6) !important; }

/* Display utilities */
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }
.d-grid { display: grid !important; }

/* Flex utilities */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-grow-0 { flex-grow: 0 !important; }
.flex-grow-1 { flex-grow: 1 !important; }
.flex-shrink-0 { flex-shrink: 0 !important; }
.flex-shrink-1 { flex-shrink: 1 !important; }
.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }

/* Alignment utilities */
.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

/* Text utilities */
.text-start { text-align: left !important; }
.text-end { text-align: right !important; }
.text-center { text-align: center !important; }
.text-decoration-none { text-decoration: none !important; }
.text-uppercase { text-transform: uppercase !important; }
.text-lowercase { text-transform: lowercase !important; }
.text-capitalize { text-transform: capitalize !important; }

.fw-normal { font-weight: var(--font-weight-normal) !important; }
.fw-medium { font-weight: var(--font-weight-medium) !important; }
.fw-semibold { font-weight: var(--font-weight-semibold) !important; }
.fw-bold { font-weight: var(--font-weight-bold) !important; }

.fs-sm { font-size: var(--font-size-sm) !important; }
.fs-base { font-size: var(--font-size-base) !important; }
.fs-lg { font-size: var(--font-size-lg) !important; }
.fs-xl { font-size: var(--font-size-xl) !important; }
.fs-2xl { font-size: var(--font-size-2xl) !important; }
.fs-3xl { font-size: var(--font-size-3xl) !important; }
.fs-4xl { font-size: var(--font-size-4xl) !important; }

/* Width and height utilities */
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

/* Text size utilities */
.text-xs { font-size: 0.75rem !important; }
.text-sm { font-size: 0.875rem !important; }
.text-base { font-size: 1rem !important; }
.text-lg { font-size: 1.125rem !important; }
.text-xl { font-size: 1.25rem !important; }
.text-2xl { font-size: 1.5rem !important; }
.text-3xl { font-size: 1.75rem !important; }
.text-4xl { font-size: 2rem !important; }

/* Visibility utilities */
.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

/* Position utilities */
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

/* Progress bar utilities */
.progress-thin { height: 4px !important; }
.progress-75 { width: 75% !important; }
.progress-50 { width: 50% !important; }
.progress-25 { width: 25% !important; }
.progress-100 { width: 100% !important; }

/* Search utilities */
.search-width-300 { width: 300px !important; }
.search-width-400 { width: 400px !important; }
.search-width-500 { width: 500px !important; }

/* Demo utilities */
.color-sample { height: 100px !important; }

/* Custom focus styles */
*:focus {
  outline: none !important;
}

/* Custom focus visible styles for keyboard navigation - maintains accessibility */
*:focus-visible {
  outline: 2px solid rgba(209, 213, 219, 0.5) !important; /* gray-300 with opacity */
  outline-offset: 2px !important;
}

/* Hide scrollbar while maintaining scroll functionality */
.hide-scrollbar {
  -ms-overflow-style: none !important;  /* IE and Edge */
  scrollbar-width: none !important;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none !important;  /* Chrome, Safari and Opera */
}
