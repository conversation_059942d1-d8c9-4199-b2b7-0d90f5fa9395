using System;
using System.Threading.Tasks;
using Microsoft.JSInterop;
using Microsoft.Extensions.Logging;

namespace Barret.Web.Server.Services
{
    /// <summary>
    /// Service for handling file downloads in the browser
    /// </summary>
    public interface IFileDownloadService
    {
        /// <summary>
        /// Downloads a file to the browser
        /// </summary>
        /// <param name="fileName">The name of the file</param>
        /// <param name="fileContent">The content of the file as a byte array</param>
        /// <param name="contentType">The MIME type of the file</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task DownloadFileAsync(string fileName, byte[] fileContent, string contentType);
    }

    /// <summary>
    /// Implementation of IFileDownloadService
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the FileDownloadService class
    /// </remarks>
    /// <param name="jsRuntime">JavaScript runtime</param>
    /// <param name="logger">Logger for the service</param>
    public class FileDownloadService(IJSRuntime jsRuntime, ILogger<FileDownloadService> logger) : IFileDownloadService
    {
        private readonly IJSRuntime _jsRuntime = jsRuntime ?? throw new ArgumentNullException(nameof(jsRuntime));
        private readonly ILogger<FileDownloadService> _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        /// <summary>
        /// Downloads a file to the browser
        /// </summary>
        /// <param name="fileName">The name of the file</param>
        /// <param name="fileContent">The content of the file as a byte array</param>
        /// <param name="contentType">The MIME type of the file</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task DownloadFileAsync(string fileName, byte[] fileContent, string contentType)
        {
            try
            {
                _logger.LogInformation("Initiating download of file {FileName} ({ContentType})", fileName, contentType);
                
                // Convert the byte array to a base64 string
                var base64Content = Convert.ToBase64String(fileContent);
                
                // Call the JavaScript function to download the file
                await _jsRuntime.InvokeVoidAsync("downloadFileFromBase64", fileName, base64Content, contentType);
                
                _logger.LogInformation("Download initiated for file {FileName}", fileName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error downloading file {FileName}: {ErrorMessage}", fileName, ex.Message);
                throw;
            }
        }
    }
}
