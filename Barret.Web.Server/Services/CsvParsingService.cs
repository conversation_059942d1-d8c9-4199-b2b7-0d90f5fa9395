using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups;
using Barret.Services.Areas.Import.Mapping;
using Barret.Services.Core.Areas.Manufacturers;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Import;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Shared.Factories;
using CsvHelper;
using CsvHelper.Configuration;
using Microsoft.Extensions.Logging;
using System.Globalization;
using System.Text.RegularExpressions;

namespace Barret.Web.Server.Services
{
    /// <summary>
    /// Service for parsing and validating CSV files for device import
    /// </summary>
    public class CsvParsingService
    {
        private readonly IManufacturerService _manufacturerService;
        private readonly ILogger<CsvParsingService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="CsvParsingService"/> class.
        /// </summary>
        /// <param name="manufacturerService">The manufacturer service.</param>
        /// <param name="logger">The logger.</param>
        public CsvParsingService(
            IManufacturerService manufacturerService,
            ILogger<CsvParsingService> logger)
        {
            _manufacturerService = manufacturerService ?? throw new ArgumentNullException(nameof(manufacturerService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Validates a CSV file without importing it.
        /// </summary>
        /// <param name="csvStream">The CSV file stream.</param>
        /// <returns>An ImportResult containing validation results.</returns>
        public async Task<ImportResult> ValidateCsvAsync(Stream csvStream)
        {
            try
            {
                // Parse the CSV file
                var csvRecords = await ParseCsvAsync(csvStream);
                if (csvRecords.Count == 0)
                {
                    return ImportResult.CreateFailure("No records found in CSV file", "The CSV file is empty or could not be parsed.");
                }

                // Validate the records
                var errors = new List<string>();
                var validCount = 0;

                foreach (var record in csvRecords)
                {
                    var recordErrors = ValidateCsvRecord(record);
                    if (recordErrors.Count != 0)
                    {
                        errors.Add($"Record '{record.Name}': {string.Join(", ", recordErrors)}");
                    }
                    else
                    {
                        validCount++;
                    }
                }

                // Return the result
                if (errors.Count != 0)
                {
                    var result = ImportResult.CreateFailure("CSV validation failed", "Some records have validation errors");
                    result.Errors.AddRange(errors);
                    result.TotalRecords = csvRecords.Count;
                    result.SuccessfulRecords = validCount;
                    result.FailedRecords = csvRecords.Count - validCount;
                    return result;
                }

                return ImportResult.CreateSuccess(
                    "CSV validation successful",
                    Guid.Empty,
                    csvRecords.Count,
                    csvRecords.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating CSV");
                return ImportResult.CreateFailure("Error validating CSV", ex.Message);
            }
        }

        /// <summary>
        /// Creates a list of device DTOs from a CSV file.
        /// </summary>
        /// <param name="csvStream">The CSV file stream.</param>
        /// <param name="targetVesselId">The ID of the target vessel.</param>
        /// <returns>A tuple containing the list of device DTOs and any warnings.</returns>
        public async Task<(List<DeviceDto> Devices, List<string> Warnings)> CreateDevicesFromCsvAsync(Stream csvStream, Guid targetVesselId)
        {
            try
            {
                // Parse the CSV file
                var csvRecords = await ParseCsvAsync(csvStream);
                if (csvRecords.Count == 0)
                {
                    return (new List<DeviceDto>(), new List<string> { "No records found in CSV file" });
                }

                // Create manufacturers and device models
                var manufacturerMap = await CreateManufacturersAndDeviceModelsAsync(csvRecords);

                // Create devices
                var devices = new List<DeviceDto>();
                var warnings = new List<string>();

                foreach (var record in csvRecords)
                {
                    try
                    {
                        // Skip records with missing required fields
                        if (string.IsNullOrWhiteSpace(record.Name) || string.IsNullOrWhiteSpace(record.DeviceType))
                        {
                            warnings.Add($"Skipped record: Missing required fields (Name or Type) for record");
                            continue;
                        }

                        // Map device type to role
                        var deviceRole = MapDeviceTypeToRole(record.DeviceType);

                        // Create device DTO
                        var deviceDto = DeviceDTOFactory.CreateDto(deviceRole);
                        deviceDto.Name = record.Name;
                        deviceDto.VehicleId = targetVesselId;

                        // Set device model if available
                        if (!string.IsNullOrWhiteSpace(record.Manufacturer) && !string.IsNullOrWhiteSpace(record.Model))
                        {
                            var key = (record.Manufacturer.Trim(), record.Model.Trim(), record.DeviceType.Trim());
                            if (manufacturerMap.TryGetValue(key, out var deviceModelId))
                            {
                                deviceDto.DeviceModelId = deviceModelId;

                                // Get manufacturer and model names for display
                                deviceDto.ManufacturerName = record.Manufacturer.Trim();
                                deviceDto.ModelName = record.Model.Trim();
                            }
                        }

                        // Set IP address if available
                        if (!string.IsNullOrWhiteSpace(record.IPAddress))
                        {
                            deviceDto.Connection = new ConnectionHandlerDto
                            {
                                IPAddress = record.IPAddress,
                                Port = 80, // Default port
                                Protocol = Protocol.TcpClient // Default protocol
                            };
                        }

                        // Determine device group type based on role
                        var deviceGroupType = FindDeviceGroupTypeForRole(deviceRole);
                        deviceDto.DeviceGroupType = deviceGroupType;

                        // Add device to the list
                        devices.Add(deviceDto);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error creating device {DeviceName} from CSV", record.Name);
                        warnings.Add($"Error creating device {record.Name}: {ex.Message}");
                    }
                }

                return (devices, warnings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating devices from CSV");
                return (new List<DeviceDto>(), new List<string> { $"Error creating devices: {ex.Message}" });
            }
        }

        /// <summary>
        /// Creates a vessel DTO with devices from a CSV file.
        /// </summary>
        /// <param name="csvStream">The CSV file stream.</param>
        /// <param name="vesselName">The name to give the new vessel.</param>
        /// <param name="vesselId">The ID to assign to the new vessel.</param>
        /// <param name="mmsi">The MMSI number for the vessel (optional).</param>
        /// <returns>A tuple containing the vessel DTO and any warnings.</returns>
        public async Task<(VesselDto Vessel, List<string> Warnings)> CreateVesselFromCsvAsync(Stream csvStream, string vesselName, string vesselId, string mmsi = "")
        {
            try
            {
                // Create a new vessel DTO
                var vessel = new VesselDto
                {
                    Id = Guid.NewGuid(),
                    Name = vesselName,
                    VehicleId = vesselId,
                    // ENI must be 8 digits according to the domain model
                    ENI = "12345678", // Default valid ENI
                    MMSI = string.IsNullOrEmpty(mmsi) ? "000000000" : mmsi, // Default MMSI if not provided
                    DeviceGroups = []
                };

                // Initialize device groups
                InitializeDeviceGroups(vessel);

                // Create devices from CSV
                var (devices, warnings) = await CreateDevicesFromCsvAsync(csvStream, vessel.Id);

                // Add devices to the vessel
                foreach (var device in devices)
                {
                    var groupType = device.DeviceGroupType;

                    // Check if device has a group type and the vessel has that group
                    if (groupType.HasValue &&
                        vessel.DeviceGroups.TryGetValue(groupType.Value, out var group))
                    {
                        group.Devices.Add(device);
                    }
                    else
                    {
                        warnings.Add($"No suitable device group found for device {device.Name} with role {device.DeviceRole}");
                    }
                }

                return (vessel, warnings);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating vessel from CSV");
                return (null, new List<string> { $"Error creating vessel: {ex.Message}" });
            }
        }

        #region Helper Methods

        /// <summary>
        /// Parses a CSV file into a list of CsvDeviceRecord objects.
        /// </summary>
        /// <param name="csvStream">The CSV file stream.</param>
        /// <returns>A list of CsvDeviceRecord objects.</returns>
        private static async Task<List<CsvDeviceRecord>> ParseCsvAsync(Stream csvStream)
        {
            // Reset the stream position
            csvStream.Position = 0;

            using var reader = new StreamReader(csvStream);
            using var csv = new CsvReader(reader, new CsvConfiguration(CultureInfo.InvariantCulture)
            {
                HasHeaderRecord = true,
                HeaderValidated = null,
                MissingFieldFound = null,
                PrepareHeaderForMatch = args => args.Header.ToLower()
            });

            // Register our mapping
            csv.Context.RegisterClassMap<CsvMappingProfile>();

            // Read all records
            var records = new List<CsvDeviceRecord>();
            await foreach (var record in csv.GetRecordsAsync<CsvDeviceRecord>())
            {
                records.Add(record);
            }

            return records;
        }

        /// <summary>
        /// Creates manufacturers and device models from CSV records.
        /// </summary>
        /// <param name="csvRecords">The CSV records.</param>
        /// <returns>A dictionary mapping manufacturer and model names to device model IDs.</returns>
        private async Task<Dictionary<(string Manufacturer, string Model, string DeviceType), Guid>> CreateManufacturersAndDeviceModelsAsync(List<CsvDeviceRecord> csvRecords)
        {
            var manufacturerMap = new Dictionary<(string Manufacturer, string Model, string DeviceType), Guid>();
            var manufacturerNameToId = new Dictionary<string, Guid>(StringComparer.OrdinalIgnoreCase);

            // Get existing manufacturers
            var existingManufacturersResult = await _manufacturerService.GetAllManufacturersAsync();
            if (existingManufacturersResult.Success && existingManufacturersResult.Data != null)
            {
                foreach (var manufacturer in existingManufacturersResult.Data)
                {
                    manufacturerNameToId[manufacturer.Name] = manufacturer.Id;
                }
            }

            // Process unique manufacturer/model combinations
            var uniqueCombinations = csvRecords
                .Where(r => !string.IsNullOrWhiteSpace(r.Manufacturer) && !string.IsNullOrWhiteSpace(r.Model) && !string.IsNullOrWhiteSpace(r.DeviceType))
                .Select(r => (
                    Manufacturer: r.Manufacturer.Trim(),
                    Model: r.Model.Trim(),
                    DeviceType: r.DeviceType.Trim()
                ))
                .Distinct()
                .ToList();

            foreach (var (manufacturer, model, deviceType) in uniqueCombinations)
            {
                try
                {
                    // Create or get manufacturer
                    if (!manufacturerNameToId.TryGetValue(manufacturer, out var manufacturerId))
                    {
                        var createManufacturerResult = await _manufacturerService.CreateManufacturerAsync(manufacturer);
                        if (!createManufacturerResult.Success)
                        {
                            _logger.LogWarning("Failed to create manufacturer {Manufacturer}: {Error}",
                                manufacturer, createManufacturerResult.ErrorMessage);
                            continue;
                        }

                        manufacturerId = createManufacturerResult.Data;
                        manufacturerNameToId[manufacturer] = manufacturerId;
                        _logger.LogInformation("Created manufacturer {Manufacturer} with ID {ManufacturerId}",
                            manufacturer, manufacturerId);
                    }

                    // Map device type to device role
                    var deviceRole = MapDeviceTypeToRole(deviceType);

                    // Create device model using ManufacturerService (as Manufacturer is the aggregate root)
                    var createModelResult = await _manufacturerService.AddDeviceModelAsync(
                        manufacturerId, model, deviceRole);

                    if (createModelResult.Success)
                    {
                        var deviceModelId = createModelResult.Data;
                        manufacturerMap[(manufacturer, model, deviceType)] = deviceModelId;
                        _logger.LogInformation("Created device model {Model} with role {Role} for manufacturer {Manufacturer}",
                            model, deviceRole, manufacturer);
                    }
                    else
                    {
                        _logger.LogWarning("Failed to create device model {Model} for manufacturer {Manufacturer}: {Error}",
                            model, manufacturer, createModelResult.ErrorMessage);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error creating manufacturer/device model for {Manufacturer}/{Model}",
                        manufacturer, model);
                }
            }

            return manufacturerMap;
        }

        /// <summary>
        /// Initializes the device groups for a vessel with allowed roles.
        /// </summary>
        /// <param name="vessel">The vessel DTO.</param>
        private static void InitializeDeviceGroups(VesselDto vessel)
        {
            // Create standard device groups using the DeviceGroups enum
            foreach (var groupType in Enum.GetValues<DeviceGroups>())
            {
                // Get allowed roles directly from the enum
                var allowedRoles = groupType.GetAllowedRoles();

                // Create the device group DTO
                vessel.DeviceGroups[groupType] = new DeviceGroupDto
                {
                    Type = groupType,
                    AllowedRoles = allowedRoles.ToList(),
                    Devices = []
                };
            }

            // Note: All standard device groups are now created above using the enum values
            // No need for a separate "OtherDevices" group since we have all enum values covered
        }

        /// <summary>
        /// Finds the appropriate device group type for a device role.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>The device group type that can contain this role, or the first available group if not found.</returns>
        private static DeviceGroups FindDeviceGroupTypeForRole(DeviceRole role)
        {
            // Check each DeviceGroups enum value to find one that allows this role
            foreach (var groupType in Enum.GetValues<DeviceGroups>())
            {
                if (groupType.AllowsRole(role))
                {
                    return groupType;
                }
            }

            // If no matching group is found, return the first available group
            return Enum.GetValues<DeviceGroups>().First();
        }

        /// <summary>
        /// Maps a device type string from the CSV to a DeviceRole enum value.
        /// </summary>
        /// <param name="deviceType">The device type string.</param>
        /// <returns>The corresponding DeviceRole enum value.</returns>
        private static DeviceRole MapDeviceTypeToRole(string deviceType)
        {
            if (string.IsNullOrWhiteSpace(deviceType))
            {
                return DeviceRole.Generic;
            }

            // Normalize the device type
            var normalizedType = deviceType.Trim().ToLowerInvariant();

            // Map common device types to roles
            return normalizedType switch
            {
                "camera" => DeviceRole.Camera,
                "nvr" or "nvrscreen" => DeviceRole.NVRScreen,
                "nvrrecording" => DeviceRole.NVRRecording,
                "radar" => DeviceRole.Radar,
                "antenna" => DeviceRole.Antenna,
                "autopilot" => DeviceRole.Autopilot,
                "trackpilot" => DeviceRole.Trackpilot,
                "engine" => DeviceRole.Engine,
                "thruster" => DeviceRole.Thruster,
                "vhf" or "mariphone" or "vhfmariphone" => DeviceRole.VHFMariphone,
                "vhfnetwork" or "vhfnetworkinterface" => DeviceRole.VHFNetworkInterface,
                "audiohub" => DeviceRole.AudioHub,
                "paaudio" => DeviceRole.PAAudio,
                "amp" => DeviceRole.AMP,
                "spap" => DeviceRole.SPAP,
                "hmi" => DeviceRole.HMI,
                "cabinetreadoutio" => DeviceRole.CabinetReadoutIO,
                "operatorpanelio" => DeviceRole.OperatorPanelIO,
                "gpu" => DeviceRole.GPU,
                "safetysystemhead" => DeviceRole.SafetySystemHead,
                "firewall" => DeviceRole.Firewall,
                "switch" => DeviceRole.Switch,
                "gateway" => DeviceRole.Gateway,
                "plc" => DeviceRole.Plc,
                "navdata" => DeviceRole.NavData,
                "sensor" => DeviceRole.Sensor,
                _ => DeviceRole.Generic
            };
        }

        /// <summary>
        /// Validates a CSV record.
        /// </summary>
        /// <param name="record">The CSV record to validate.</param>
        /// <returns>A list of validation error messages.</returns>
        private static List<string> ValidateCsvRecord(CsvDeviceRecord record)
        {
            var errors = new List<string>();

            // Check required fields
            if (string.IsNullOrWhiteSpace(record.Name))
            {
                errors.Add("Name is required");
            }

            if (string.IsNullOrWhiteSpace(record.DeviceType))
            {
                errors.Add("Device type is required");
            }

            // Validate IP address if provided
            if (!string.IsNullOrWhiteSpace(record.IPAddress) && !IsValidIpAddress(record.IPAddress))
            {
                errors.Add($"Invalid IP address: {record.IPAddress}");
            }

            return errors;
        }

        /// <summary>
        /// Checks if a string is a valid IP address.
        /// </summary>
        /// <param name="ipAddress">The IP address string to validate.</param>
        /// <returns>True if the string is a valid IP address; otherwise, false.</returns>
        private static bool IsValidIpAddress(string ipAddress)
        {
            // Simple regex for IPv4 address validation
            var ipPattern = @"^((25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$";
            return Regex.IsMatch(ipAddress, ipPattern);
        }

        #endregion
    }
}
