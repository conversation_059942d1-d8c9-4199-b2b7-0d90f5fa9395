using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Devices.Alarms;
using Barret.Shared.DTOs.Devices.Cameras;
using Barret.Shared.Factories;
using Microsoft.Extensions.Logging;

namespace Barret.Web.Server.Services.DTO
{
    /// <summary>
    /// Frontend service for manipulating device DTOs
    /// </summary>
    public class DeviceDtoService(ILogger<DeviceDtoService> logger)
    {
        private readonly ILogger<DeviceDtoService> _logger = logger;

        /// <summary>
        /// Creates a new device DTO with the specified role and device group type
        /// </summary>
        /// <param name="role">The device role</param>
        /// <param name="deviceGroupType">The device group type (optional)</param>
        /// <returns>A new device DTO</returns>
        public DeviceDto CreateDevice(DeviceRole role, DeviceGroups? deviceGroupType = null)
        {
            _logger.LogInformation("Creating new device DTO with role {Role} for group {DeviceGroupType}",
                role, deviceGroupType);
            return DeviceDTOFactory.CreateDto(role, deviceGroupType);
        }

        /// <summary>
        /// Creates a deep copy of a device DTO
        /// </summary>
        /// <param name="source">The source device DTO</param>
        /// <returns>A deep copy of the device DTO, or null if source is null</returns>
        public DeviceDto? CopyDevice(DeviceDto? source)
        {
            if (source == null)
            {
                _logger.LogWarning("Attempted to copy a null device");
                return null;
            }

            _logger.LogInformation("Creating copy of device {DeviceName} with role {Role}",
                source.Name, source.DeviceRole);

            // Create a new device with the correct type based on role and preserve the device group type
            var copy = DeviceDTOFactory.CreateDto(source.DeviceRole, source.DeviceGroupType);

            // Copy basic properties
            copy.Id = source.Id;
            copy.Name = source.Name;
            copy.DeviceModelId = source.DeviceModelId;
            copy.ModelName = source.ModelName;
            copy.ManufacturerName = source.ManufacturerName;

            // Copy position if it exists
            if (source.Position != null)
            {
                copy.Position = new RelativePositionDto
                {
                    X = source.Position.X,
                    Y = source.Position.Y,
                    Z = source.Position.Z
                };
            }

            // Copy connection if it exists
            if (source.Connection != null)
            {
                copy.Connection = new ConnectionHandlerDto
                {
                    IPAddress = source.Connection.IPAddress,
                    Port = source.Connection.Port,
                    Protocol = source.Connection.Protocol
                };
            }

            // Copy alarms with all enhanced properties
            copy.Alarms = source.Alarms?.Select(a => new AlarmDto
            {
                Id = a.Id,
                Description = a.Description,
                NotificationType = a.NotificationType,
                Message = a.Message,
                NotificationGroupId = a.NotificationGroupId,
                EntityId = a.EntityId,
                WarningId = a.WarningId
            }).ToList() ?? [];

            // Copy connections
            copy.Connections = source.Connections?.Select(c => new DeviceConnectionDto
            {
                ConnectedDeviceId = c.ConnectedDeviceId,
                InterfaceDeviceId = c.InterfaceDeviceId,
                Type = c.Type,
                Direction = c.Direction
            }).ToList() ?? [];

            // Copy device-specific properties
            CopyDeviceSpecificProperties(copy, source);

            return copy;
        }

        /// <summary>
        /// Updates a device DTO with values from another device DTO
        /// </summary>
        /// <param name="target">The target device DTO to update</param>
        /// <param name="source">The source device DTO to copy values from</param>
        /// <returns>The updated target device DTO, or null if either target or source is null</returns>
        public DeviceDto? UpdateDevice(DeviceDto? target, DeviceDto? source)
        {
            if (target == null || source == null)
            {
                _logger.LogWarning("Attempted to update with null device");
                return target;
            }

            _logger.LogInformation("Updating device {DeviceName} with values from {SourceName}",
                target.Name, source.Name);

            // Update basic properties
            target.Name = source.Name;
            target.DeviceRole = source.DeviceRole;
            target.DeviceModelId = source.DeviceModelId;
            target.ModelName = source.ModelName;
            target.ManufacturerName = source.ManufacturerName;
            target.DeviceGroupType = source.DeviceGroupType;

            // Update position
            if (source.Position != null)
            {
                target.Position = new RelativePositionDto
                {
                    X = source.Position.X,
                    Y = source.Position.Y,
                    Z = source.Position.Z
                };
            }

            // Update connection
            if (source.Connection != null)
            {
                target.Connection = new ConnectionHandlerDto
                {
                    IPAddress = source.Connection.IPAddress,
                    Port = source.Connection.Port,
                    Protocol = source.Connection.Protocol
                };
            }

            // Update device-specific properties
            UpdateDeviceSpecificProperties(target, source);

            return target;
        }

        /// <summary>
        /// Adds an alarm to a device DTO
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="description">The alarm description</param>
        /// <param name="notificationType">The notification type</param>
        /// <returns>The added alarm DTO, or null if device is null</returns>
        public AlarmDto? AddAlarm(DeviceDto? device, string description, NotificationType notificationType)
        {
            if (device == null)
            {
                _logger.LogWarning("Attempted to add alarm to null device");
                return null;
            }

            _logger.LogInformation("Adding alarm to device {DeviceName}", device.Name);

            // Initialize alarms collection if needed
            device.Alarms ??= [];

            // Create new alarm
            var alarm = new AlarmDto
            {
                Id = Guid.NewGuid(),
                Description = description,
                NotificationType = notificationType
            };

            // Add to device
            device.Alarms.Add(alarm);

            return alarm;
        }

        /// <summary>
        /// Adds an alarm to a device DTO with all properties
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="description">The alarm description</param>
        /// <param name="notificationType">The notification type</param>
        /// <param name="message">The alarm message for Michelangelo integration</param>
        /// <param name="notificationGroupId">The notification group identifier</param>
        /// <param name="entityId">The entity identifier</param>
        /// <param name="warningId">The warning identifier</param>
        /// <returns>The created alarm DTO</returns>
        public AlarmDto AddAlarm(DeviceDto device, string description, NotificationType notificationType,
            string message, NotificationGroupId notificationGroupId, string entityId, WarningId warningId)
        {
            if (device == null)
                throw new ArgumentNullException(nameof(device));

            if (string.IsNullOrWhiteSpace(description))
                throw new ArgumentException("Description cannot be empty", nameof(description));

            _logger.LogInformation("Adding enhanced alarm to device {DeviceName}", device.Name);

            // Initialize alarms collection if needed
            device.Alarms ??= [];

            // Create new alarm with all properties
            var alarm = new AlarmDto
            {
                Id = Guid.NewGuid(),
                Description = description,
                NotificationType = notificationType,
                Message = message ?? string.Empty,
                NotificationGroupId = notificationGroupId,
                EntityId = entityId ?? string.Empty,
                WarningId = warningId
            };

            // Add to device
            device.Alarms.Add(alarm);

            return alarm;
        }

        /// <summary>
        /// Updates an alarm in a device DTO
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="alarmId">The alarm ID</param>
        /// <param name="description">The new description</param>
        /// <param name="notificationType">The new notification type</param>
        /// <returns>True if the alarm was updated, false otherwise</returns>
        public bool UpdateAlarm(DeviceDto device, Guid alarmId, string description, NotificationType notificationType)
        {
            if (device?.Alarms == null)
            {
                _logger.LogWarning("Attempted to update alarm in null device or with null alarms collection");
                return false;
            }

            // Find the alarm
            var alarm = device.Alarms.FirstOrDefault(a => a.Id == alarmId);
            if (alarm == null)
            {
                _logger.LogWarning("Alarm with ID {AlarmId} not found in device {DeviceName}",
                    alarmId, device.Name);
                return false;
            }

            _logger.LogInformation("Updating alarm in device {DeviceName}", device.Name);

            // Update alarm
            alarm.Description = description;
            alarm.NotificationType = notificationType;

            return true;
        }

        /// <summary>
        /// Updates an alarm in a device DTO with all properties
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="alarmId">The alarm ID</param>
        /// <param name="description">The new description</param>
        /// <param name="notificationType">The new notification type</param>
        /// <param name="message">The new alarm message</param>
        /// <param name="notificationGroupId">The new notification group identifier</param>
        /// <param name="entityId">The new entity identifier</param>
        /// <param name="warningId">The new warning identifier</param>
        /// <returns>True if the alarm was updated, false otherwise</returns>
        public bool UpdateAlarm(DeviceDto device, Guid alarmId, string description, NotificationType notificationType,
            string message, NotificationGroupId notificationGroupId, string entityId, WarningId warningId)
        {
            if (device?.Alarms == null)
            {
                _logger.LogWarning("Attempted to update alarm in null device or with null alarms collection");
                return false;
            }

            // Find the alarm
            var alarm = device.Alarms.FirstOrDefault(a => a.Id == alarmId);
            if (alarm == null)
            {
                _logger.LogWarning("Alarm with ID {AlarmId} not found in device {DeviceName}",
                    alarmId, device.Name);
                return false;
            }

            _logger.LogInformation("Updating enhanced alarm in device {DeviceName}", device.Name);

            // Update all alarm properties
            alarm.Description = description;
            alarm.NotificationType = notificationType;
            alarm.Message = message ?? string.Empty;
            alarm.NotificationGroupId = notificationGroupId;
            alarm.EntityId = entityId ?? string.Empty;
            alarm.WarningId = warningId;

            return true;
        }

        /// <summary>
        /// Removes an alarm from a device DTO
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="alarmId">The alarm ID</param>
        /// <returns>True if the alarm was removed, false otherwise</returns>
        public bool RemoveAlarm(DeviceDto device, Guid alarmId)
        {
            if (device?.Alarms == null)
            {
                _logger.LogWarning("Attempted to remove alarm from null device or with null alarms collection");
                return false;
            }

            // Find the alarm
            var alarm = device.Alarms.FirstOrDefault(a => a.Id == alarmId);
            if (alarm == null)
            {
                _logger.LogWarning("Alarm with ID {AlarmId} not found in device {DeviceName}",
                    alarmId, device.Name);
                return false;
            }

            _logger.LogInformation("Removing alarm from device {DeviceName}", device.Name);

            // Remove alarm
            return device.Alarms.Remove(alarm);
        }

        /// <summary>
        /// Adds a connection to a device DTO
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="interfaceDeviceId">The ID of the target device</param>
        /// <param name="connectionType">The type of connection</param>
        /// <param name="direction">The direction of the connection</param>
        /// <returns>A tuple containing the added connection DTO (or null) and an error message (or null if successful)</returns>
        public (DeviceConnectionDto? Connection, string? ErrorMessage) AddConnection(DeviceDto device, Guid interfaceDeviceId, ConnectionType connectionType, ConnectionDirection direction)
        {
            if (device == null)
            {
                _logger.LogWarning("Attempted to add connection to null device");
                return (null, "Device information is missing");
            }

            if (interfaceDeviceId == Guid.Empty)
            {
                _logger.LogWarning("Attempted to add connection with empty interface device ID");
                return (null, "Interface device ID cannot be empty");
            }

            if (device.Id == interfaceDeviceId)
            {
                _logger.LogWarning("Attempted to add connection from device to itself");
                return (null, "A device cannot connect to itself");
            }

            _logger.LogInformation("Adding connection from device {DeviceName} to device {InterfaceDeviceId}",
                device.Name, interfaceDeviceId);

            // Initialize connections collection if needed
            device.Connections ??= [];

            // Check if connection already exists - use exact match on both IDs
            bool connectionExists = device.Connections.Any(c =>
                c.ConnectedDeviceId == device.Id &&
                c.InterfaceDeviceId == interfaceDeviceId);

            if (connectionExists)
            {
                _logger.LogWarning("Connection from {ConnectedDeviceId} to {InterfaceDeviceId} already exists",
                    device.Id, interfaceDeviceId);
                return (null, "This connection already exists");
            }

            // Create new connection
            var connection = new DeviceConnectionDto
            {
                ConnectedDeviceId = device.Id,
                InterfaceDeviceId = interfaceDeviceId,
                Type = connectionType,
                Direction = direction
            };

            // Add to device
            device.Connections.Add(connection);

            return (connection, null);
        }

        /// <summary>
        /// Updates a connection in a device DTO
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="interfaceDeviceId">The ID of the target device</param>
        /// <param name="connectionType">The new type of connection</param>
        /// <param name="direction">The new direction of the connection</param>
        /// <returns>True if the connection was updated, false otherwise</returns>
        public bool UpdateConnection(DeviceDto device, Guid interfaceDeviceId, ConnectionType connectionType, ConnectionDirection direction)
        {
            if (device?.Connections == null)
            {
                _logger.LogWarning("Attempted to update connection in null device or with null connections collection");
                return false;
            }

            // Find the connection
            var connection = device.Connections.FirstOrDefault(c =>
                c.ConnectedDeviceId == device.Id && c.InterfaceDeviceId == interfaceDeviceId);

            if (connection == null)
            {
                _logger.LogWarning("Connection from {ConnectedDeviceId} to {InterfaceDeviceId} not found",
                    device.Id, interfaceDeviceId);
                return false;
            }

            _logger.LogInformation("Updating connection from {ConnectedDeviceId} to {InterfaceDeviceId}",
                device.Id, interfaceDeviceId);

            // Update connection
            connection.Type = connectionType;
            connection.Direction = direction;

            return true;
        }

        /// <summary>
        /// Removes a connection from a device DTO
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="interfaceDeviceId">The ID of the target device</param>
        /// <returns>A tuple containing a boolean indicating success and an error message (or null if successful)</returns>
        public (bool Success, string? ErrorMessage) RemoveConnection(DeviceDto device, Guid interfaceDeviceId)
        {
            if (device?.Connections == null)
            {
                _logger.LogWarning("Attempted to remove connection from null device or with null connections collection");
                return (false, "Device information is missing or device has no connections");
            }

            // Find the connection
            var connection = device.Connections.FirstOrDefault(c =>
                c.ConnectedDeviceId == device.Id && c.InterfaceDeviceId == interfaceDeviceId);

            if (connection == null)
            {
                _logger.LogWarning("Connection from {ConnectedDeviceId} to {InterfaceDeviceId} not found",
                    device.Id, interfaceDeviceId);
                return (false, "Connection not found");
            }

            _logger.LogInformation("Removing connection from {ConnectedDeviceId} to {InterfaceDeviceId}",
                device.Id, interfaceDeviceId);

            // Remove connection
            bool removed = device.Connections.Remove(connection);

            return removed
                ? (true, null)
                : (false, "Failed to remove connection from device");
        }

        /// <summary>
        /// Copies device-specific properties from source to target DTO
        /// </summary>
        /// <param name="target">The target device DTO</param>
        /// <param name="source">The source device DTO</param>
        private void CopyDeviceSpecificProperties(DeviceDto target, DeviceDto source)
        {
            // Handle Camera-specific properties
            if (target is CameraDto targetCamera && source is CameraDto sourceCamera)
            {
                _logger.LogInformation("[DeviceDtoService] Copying Camera ShowVideo from {SourceValue} to target",
                    sourceCamera.ShowVideo);
                targetCamera.ShowVideo = sourceCamera.ShowVideo;
                _logger.LogInformation("[DeviceDtoService] Target Camera ShowVideo is now {TargetValue}",
                    targetCamera.ShowVideo);
            }

            // Add handling for other device-specific properties here as needed
            // Example: if (target is RadarDto targetRadar && source is RadarDto sourceRadar) { ... }
        }

        /// <summary>
        /// Updates device-specific properties from source to target DTO
        /// </summary>
        /// <param name="target">The target device DTO to update</param>
        /// <param name="source">The source device DTO to copy values from</param>
        private void UpdateDeviceSpecificProperties(DeviceDto target, DeviceDto source)
        {
            // Handle Camera-specific properties
            if (target is CameraDto targetCamera && source is CameraDto sourceCamera)
            {
                _logger.LogInformation("[DeviceDtoService] Updating Camera {CameraName} ShowVideo from {OldValue} to {NewValue}",
                    targetCamera.Name, targetCamera.ShowVideo, sourceCamera.ShowVideo);
                targetCamera.ShowVideo = sourceCamera.ShowVideo;
                _logger.LogInformation("[DeviceDtoService] Camera {CameraName} ShowVideo updated to {NewValue}",
                    targetCamera.Name, targetCamera.ShowVideo);
            }

            // Add handling for other device-specific properties here as needed
            // Example: if (target is RadarDto targetRadar && source is RadarDto sourceRadar) { ... }
        }
    }
}
