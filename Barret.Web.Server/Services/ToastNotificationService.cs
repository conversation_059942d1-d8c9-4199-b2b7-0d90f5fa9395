using Radzen;

namespace Barret.Web.Server.Services
{
    public class ToastNotificationService(NotificationService notificationService) : IBarretToastService, IBarretToastNotificationService
    {
        private readonly NotificationService _notificationService = notificationService;

        public void ShowToast(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null)
        {
            var notificationSeverity = type switch
            {
                ToastType.Success => NotificationSeverity.Success,
                ToastType.Warning => NotificationSeverity.Warning,
                ToastType.Error => NotificationSeverity.Error,
                _ => NotificationSeverity.Info
            };

            var duration = displayTime?.TotalMilliseconds ?? 4000;

            var message = new NotificationMessage
            {
                Severity = notificationSeverity,
                Summary = title,
                Detail = text,
                Duration = duration
            };

            _notificationService.Notify(message);
        }

        public async Task ShowToastWithSoundAsync(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null, string soundType = null)
        {
            ShowToast(title, text, type, displayTime);

            // Get the sound type based on the toast type if not provided
            if (string.IsNullOrEmpty(soundType))
            {
                soundType = type switch
                {
                    ToastType.Error => "error",
                    ToastType.Warning => "warning",
                    ToastType.Success => "success",
                    _ => "info"
                };
            }

            // Play the sound
            await PlaySoundAsync(soundType);
        }

        private async Task PlaySoundAsync(string soundType)
        {
            // Logic to play a sound based on the sound type
            // This would typically involve JavaScript interop
            // For now, we'll just log the sound type
            Console.WriteLine($"Playing sound: {soundType}");
            await Task.CompletedTask;
        }
    }
}