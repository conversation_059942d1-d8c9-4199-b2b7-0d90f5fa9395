using Radzen;

namespace Barret.Web.Server.Services
{
    /// <summary>
    /// Toast notification types for the application.
    /// </summary>
    public enum ToastType
    {
        Info,
        Success,
        Warning,
        Error
    }

    public interface IBarretToastService
    {
        void ShowToast(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null);
        Task ShowToastWithSoundAsync(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null, string soundType = null);
    }

    public interface IBarretToastNotificationService
    {
        void ShowToast(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null);
        Task ShowToastWithSoundAsync(string title, string text, ToastType type = ToastType.Info, TimeSpan? displayTime = null, string soundType = null);
    }
}