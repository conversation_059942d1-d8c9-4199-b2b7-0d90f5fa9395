using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.Contexts;
using Barret.Services.Areas.Devices.Factories;
// Repositories
using Barret.Services.Areas.Devices.Repositories;
using Barret.Services.Areas.Devices.Services;
// DeviceModelService has been removed in favor of DeviceModelQueryService
using Barret.Services.Areas.DeviceModels.Repositories;
using Barret.Services.Areas.Manufacturers;
using Barret.Services.Areas.Manufacturers.Repositories;
using Barret.Services.Areas.Vehicles;
using Barret.Core.Areas.Common.Events;
using Barret.Services.Areas.Common.Events;
using Barret.Core.Areas.Devices.Events;
using Barret.Services.Areas.Devices.Events;
// Config Generation
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Mappers;
using Barret.Services.Areas.ConfigGeneration.Services;

// Import
using Barret.Services.Areas.Import;
using Microsoft.Extensions.Options;

// Configuration
using Barret.Core.Configuration;

// Add these imports for mappers
using Barret.Services.Core.Areas.Mapping;
using Barret.Services.Core.Areas.Devices.Mapping;
using Barret.Services.Core.Areas.Vehicles.Mapping;
using Barret.Services.Areas.Devices.Mapping;
using Barret.Services.Areas.Vehicles.Mapping;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Core.Areas.Devices.Models.GenericDevices;

// Factories
using Barret.Services.Areas.Vehicles.Factories;
using Barret.Services.Areas.Vehicles.Repositories;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Devices.Factories;
using Barret.Services.Core.Areas.Devices.Repositories;
using Barret.Services.Core.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.DeviceModels.Repositories;
using Barret.Services.Core.Areas.Manufacturers;
using Barret.Services.Core.Areas.Manufacturers.Repositories;
using Barret.Services.Core.Areas.Vehicles;
using Barret.Services.Core.Areas.Vehicles.Factories;
using Barret.Services.Core.Areas.Vehicles.Queries;
using Barret.Services.Core.Areas.Vehicles.Repositories;
// Removed DbSeeder import

// Database context
using Barret.Web.Server.Data.Contexts;
using Barret.Web.Server.Services;

// Third-party packages
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;

// Test UI
using Barret.Web.Server;
using Barret.Web.Server.Extensions;
using Barret.Web.Server.Features;
using Barret.Web.Server.Features.ConfigGeneration;
using Radzen;

// Initialize SQLite
SQLitePCL.Batteries_V2.Init();
var builder = WebApplication.CreateBuilder(args);

// Configuration files are loaded automatically

// Configure logging
builder.Logging.ClearProviders();
builder.Logging.AddConsole();

// builder.Logging.AddNLog();

// Set minimum log level
builder.Logging.SetMinimumLevel(LogLevel.Information);

// Configure category-specific log levels
builder.Logging.AddFilter("Microsoft.EntityFrameworkCore", LogLevel.Warning);
builder.Logging.AddFilter("Microsoft.AspNetCore", LogLevel.Warning);
builder.Logging.AddFilter("Barret.Services", LogLevel.Debug);

// Add core services
builder.Services.AddRazorPages();
builder.Services.AddServerSideBlazor();
builder.Services.AddControllers();

// Database configuration
builder.Services.AddDbContext<BarretDbContext>(options =>
{
    options.UseSqlite(builder.Configuration.GetConnectionString("DefaultConnection"));

    // For development: detailed query logging
    if (builder.Environment.IsDevelopment())
    {
        options.EnableSensitiveDataLogging();
        options.EnableDetailedErrors();
    }
});

// Register the DBContext as the interface implementation
builder.Services.AddScoped<IBarretDbContext>(provider =>
    provider.GetRequiredService<BarretDbContext>());

// Register repositories - using concrete implementations
builder.Services.AddScoped<IDeviceRepository, DeviceRepository>();
builder.Services.AddScoped<IVesselRepository, VesselRepository>();
builder.Services.AddScoped<IManufacturerRepository, ManufacturerRepository>();
builder.Services.AddScoped<IDeviceModelRepository, DeviceModelRepository>();

// Register domain event infrastructure
builder.Services.AddScoped<IDomainEventDispatcher, DomainEventDispatcher>();

// Register domain event handlers
builder.Services.AddScoped<IDomainEventHandler<DeviceCreatedEvent>, DeviceCreatedEventHandler>();
builder.Services.AddScoped<IDomainEventHandler<DeviceConnectionAddedEvent>, DeviceConnectionAddedEventHandler>();
builder.Services.AddScoped<IDomainEventHandler<DeviceConnectionRemovedEvent>, DeviceConnectionRemovedEventHandler>();

// Register query services
builder.Services.AddScoped<Barret.Services.Core.Areas.Vehicles.Queries.IVesselQueryService, Barret.Services.Areas.Vehicles.Queries.VesselQueryService>();
builder.Services.AddScoped<Barret.Services.Core.Areas.Devices.Queries.IDeviceQueryService, Barret.Services.Areas.Devices.Queries.DeviceQueryService>();
builder.Services.AddScoped<Barret.Services.Core.Areas.DeviceModels.Queries.IDeviceModelQueryService, Barret.Services.Areas.DeviceModels.Queries.DeviceModelQueryService>();

// Register services - using concrete implementations
builder.Services.AddScoped<IManufacturerService>(provider =>
    new ManufacturerService(
        provider.GetRequiredService<IUnitOfWork>(),
        provider.GetRequiredService<ILogger<ManufacturerService>>()
    ));
// DeviceModelService has been removed in favor of ManufacturerService for write operations
// and DeviceModelQueryService for read operations
// Register the IVehicleService<Vessel, VesselDto>
builder.Services.AddScoped<IVehicleService<Vessel, VesselDto>, VesselService>(provider =>
    new VesselService(
        provider.GetRequiredService<IUnitOfWork>(),
        provider.GetRequiredService<ILogger<VesselService>>(),
        provider.GetRequiredService<IVehicleMapper<Vessel, VesselDto>>(),
        provider.GetRequiredService<IDeviceMapper>(),
        provider.GetRequiredService<IVesselRepository>(),
        provider.GetRequiredService<IDeviceFactory>(),
        provider.GetRequiredService<IVesselQueryService>()
    ));

// Device role configuration is now handled by DeviceEditorTabService
// DeviceEditorTabService is now registered in ServiceCollectionExtensions.AddFrontendServices()

// Register DTO services for frontend (will be phased out in favor of ViewModels)
builder.Services.AddScoped<Barret.Web.Server.Services.DTO.DeviceDtoService>();

// Register vehicle service for device compatibility
builder.Services.AddScoped<Barret.Web.Server.Features.Vehicles.Services.VehicleService>(provider =>
    new Barret.Web.Server.Features.Vehicles.Services.VehicleService(
        provider.GetRequiredService<IVehicleService<Vessel, VesselDto>>(),
        provider.GetRequiredService<IVesselQueryService>(),
        provider.GetRequiredService<ILogger<Barret.Web.Server.Features.Vehicles.Services.VehicleService>>(),
        provider.GetRequiredService<IOptions<DeviceModelCompatibilityConfig>>()
    ));

// Register the interface as well
builder.Services.AddScoped<Barret.Web.Server.Features.Vehicles.Services.IVehicleService>(provider =>
    provider.GetRequiredService<Barret.Web.Server.Features.Vehicles.Services.VehicleService>());

// Register services
builder.Services.AddScoped<Barret.Web.Server.Services.CsvParsingService>();

// Register ViewModels
builder.Services.AddScoped<Barret.Web.Server.Features.Vehicles.Editor.ViewModels.VehicleEditorViewModel>();

// Register Vehicle List Feature services
builder.Services.AddScoped<Barret.Web.Server.Features.Vehicles.List.ViewModels.VehicleListViewModel>();

// Register factories - using concrete implementations
builder.Services.AddScoped<Barret.Services.Areas.Vehicles.Factories.IVehicleFactory, VehicleFactory>();
builder.Services.AddScoped<Barret.Services.Core.Areas.Vehicles.Factories.IVehicleFactory, VehicleFactory>();
builder.Services.AddScoped<IDeviceFactory, DeviceFactory>();

// Register UnitOfWork
builder.Services.AddScoped<IUnitOfWork, UnitOfWork>(provider =>
    new UnitOfWork(
        provider.GetRequiredService<IBarretDbContext>(),
        provider.GetRequiredService<ILogger<UnitOfWork>>(),
        provider.GetRequiredService<IVesselRepository>(),
        provider.GetRequiredService<IDeviceRepository>(),
        provider.GetRequiredService<IDeviceModelRepository>(),
        provider.GetRequiredService<IManufacturerRepository>()
    )
);

// DeviceDTOFactory is now a static class in Barret.Shared

// VehicleDeviceManager has been removed

// Radzen components are registered below

// Register any mapping profiles from the Services assembly
builder.Services.AddAutoMapper(typeof(VesselService).Assembly);

// Device group services are now registered directly above

// Register custom services
builder.Services.AddScoped<Barret.Web.Server.Services.ToastNotificationService>();
builder.Services.AddScoped<IBarretToastService>(sp => sp.GetRequiredService<Barret.Web.Server.Services.ToastNotificationService>());
builder.Services.AddScoped<Barret.Web.Server.Services.IBarretToastNotificationService>(sp => sp.GetRequiredService<Barret.Web.Server.Services.ToastNotificationService>());

// Register DeviceEditorService explicitly after toast service
builder.Services.AddScoped<Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Services.DeviceEditorService>();

// Config generation services are registered in ConfigurationServiceExtensions

// Register file download service
builder.Services.AddScoped<IFileDownloadService, FileDownloadService>();

builder.Services.AddRadzenComponents();

// Register entity mappers
builder.Services.AddScoped<DeviceMapper>();
builder.Services.AddScoped<VesselMapper>();
builder.Services.AddScoped<IEntityMapper<VesselDto, Vessel>>(sp =>
    sp.GetRequiredService<VesselMapper>());
builder.Services.AddScoped<IEntityMapper<DeviceDto, GenericDevice>>(sp =>
    (IEntityMapper<DeviceDto, GenericDevice>)sp.GetRequiredService<DeviceMapper>());
// Register IDeviceMapper implementation
builder.Services.AddScoped<IDeviceMapper>(sp =>
    sp.GetRequiredService<DeviceMapper>());
// Register IVehicleMapper implementation
builder.Services.AddScoped<IVehicleMapper<Vessel, VesselDto>>(sp =>
    sp.GetRequiredService<VesselMapper>());


// Identity configuration - ensure versions match between all projects
builder.Services.AddIdentity<IdentityUser, IdentityRole>(options =>
{
    options.Password.RequireDigit = true;
    options.Password.RequiredLength = 8;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireUppercase = true;
    options.Password.RequireLowercase = true;
    options.SignIn.RequireConfirmedAccount = false;
})
.AddEntityFrameworkStores<BarretDbContext>()
.AddDefaultTokenProviders()
.AddDefaultUI();

// Cookie configuration
builder.Services.ConfigureApplicationCookie(options =>
{
    options.LoginPath = "/Identity/Account/Login";
    options.LogoutPath = "/Identity/Account/Logout";
    options.AccessDeniedPath = "/Identity/Account/AccessDenied";
    options.Cookie.HttpOnly = true;
    options.SlidingExpiration = true;
    options.ExpireTimeSpan = TimeSpan.FromHours(1);
});

// Authorization configuration
builder.Services.AddAuthorizationBuilder()
    .SetFallbackPolicy(new AuthorizationPolicyBuilder()
        .RequireAuthenticatedUser()
        .Build())
    .AddPolicy("AdminOnly", policy => policy.RequireRole("Admin"));

// Device settings configuration has been removed

// Register device model compatibility configuration
builder.Services.Configure<DeviceModelCompatibilityConfig>(options =>
{
    // Load the configuration from the JSON file
    var configPath = Path.Combine(builder.Environment.ContentRootPath, "devicemodelcompatibility.json");
    var configuration = new ConfigurationBuilder()
        .AddJsonFile(configPath)
        .Build();

    configuration.GetSection("DeviceModelCompatibility").Bind(options);
});

// Register base components
builder.Services.AddScoped<LoadingService>();

// Register device group mapping service
builder.Services.AddScoped<Barret.Services.Areas.Devices.Services.IDeviceGroupMappingService, Barret.Services.Areas.Devices.Services.DeviceGroupMappingService>();

// Register frontend services
builder.AddFrontendServices();

// Register configuration generation services
builder.AddConfigGenerationServices();

var app = builder.Build();

// No startup tasks needed with the new configuration system

// No need to register device collections with the new simplified approach

// Configure request pipeline
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles();
app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

// Map endpoints
app.MapBlazorHub();
app.MapFallbackToPage("/_Host");
app.MapRazorPages();

// Apply database migrations and reset data
using (var scope = app.Services.CreateScope())
{
    var dbContext = scope.ServiceProvider.GetRequiredService<BarretDbContext>();
    var configuration = scope.ServiceProvider.GetRequiredService<IConfiguration>();
    var logger = scope.ServiceProvider.GetRequiredService<ILogger<Program>>();

    try
    {
        // Get the database file path from the connection string
        var connectionString = configuration.GetConnectionString("DefaultConnection");
        if (connectionString != null)
        {
            var dbFilePath = connectionString.Replace("Data Source=", "").Trim();

            // Only delete the database file if it doesn't exist or if explicitly requested
            var resetDatabase = configuration.GetValue<bool>("ResetDatabase", false);

            if (resetDatabase && File.Exists(dbFilePath))
            {
                logger.LogInformation("Deleting existing database file: {DbFilePath}", dbFilePath);

                // Ensure the database connection is closed before deleting the file
                dbContext.Database.CloseConnection();

                // Give the system a moment to release the file
                GC.Collect();
                GC.WaitForPendingFinalizers();

                // Try to delete the file
                try
                {
                    File.Delete(dbFilePath);
                }
                catch (IOException ex)
                {
                    logger.LogWarning(ex, "Could not delete database file. It may be in use.");
                }
            }

            logger.LogInformation("Creating new database...");

            // Use Migrate to apply migrations
            dbContext.Database.Migrate();
            logger.LogInformation("Database migrated successfully.");
        }
    }
    catch (Exception ex)
    {
        logger.LogError(ex, "Error initializing database");
    }
}

// Seed users (after database recreation)
using (var scope = app.Services.CreateScope())
{
    var configuration = app.Services.GetRequiredService<IConfiguration>();
    var userManager = scope.ServiceProvider.GetRequiredService<UserManager<IdentityUser>>();
    var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<IdentityRole>>();

    await SeedUsers(userManager, roleManager, configuration);
}

// We've removed the DbSeeder to avoid transaction conflicts

app.Run();

// Helper method to seed admin user
static async Task SeedUsers(
    UserManager<IdentityUser> userManager,
    RoleManager<IdentityRole> roleManager,
    IConfiguration configuration)
{
    try
    {
        if (!await roleManager.RoleExistsAsync("Admin"))
        {
            await roleManager.CreateAsync(new IdentityRole("Admin"));
        }

        var adminEmail = configuration["AdminSettings:DefaultEmail"];
        var adminPassword = configuration["AdminSettings:DefaultPassword"];

        if (string.IsNullOrWhiteSpace(adminEmail) || string.IsNullOrWhiteSpace(adminPassword))
        {
            throw new Exception("Admin email or password is not set in configuration.");
        }

        var adminUser = await userManager.FindByEmailAsync(adminEmail);

        if (adminUser == null)
        {
            adminUser = new IdentityUser
            {
                UserName = adminEmail,
                Email = adminEmail,
                EmailConfirmed = true
            };
            var createResult = await userManager.CreateAsync(adminUser, adminPassword);

            if (!createResult.Succeeded)
            {
                throw new Exception($"Failed to create default admin user: {string.Join(", ", createResult.Errors.Select(e => e.Description))}");
            }

            await userManager.AddToRoleAsync(adminUser, "Admin");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"An error occurred while seeding users: {ex.Message}");
    }
}
