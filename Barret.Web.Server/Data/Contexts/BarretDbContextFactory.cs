using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Microsoft.Extensions.Configuration;
using System.IO;

namespace Barret.Web.Server.Data.Contexts
{
    public class BarretDbContextFactory : IDesignTimeDbContextFactory<BarretDbContext>
    {
        public BarretDbContext CreateDbContext(string[] args)
        {
            // Build configuration from appsettings.json
            var configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json")
                .AddJsonFile("appsettings.Development.json", optional: true)
                .Build();
                
            // Create DbContext options with SQLite connection
            var optionsBuilder = new DbContextOptionsBuilder<BarretDbContext>();
            optionsBuilder.UseSqlite(configuration.GetConnectionString("DefaultConnection"));

            return new BarretDbContext(optionsBuilder.Options);
        }
    }
}