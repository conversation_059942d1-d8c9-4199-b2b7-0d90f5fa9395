using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Shared.DTOs.Devices;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System;
using System.Text.Json;

namespace Barret.Web.Server.Data.ValueConverters
{
    /// <summary>
    /// Value converter for the DeviceConnection immutable value object.
    /// Converts between DeviceConnection and a string representation for database storage.
    /// </summary>
    public class DeviceConnectionConverter : ValueConverter<DeviceConnection, string>
    {
        public DeviceConnectionConverter() : base(
            // Convert to database (DeviceConnection -> string)
            v => ConvertToString(v),

            // Convert from database (string -> DeviceConnection)
            s => ConvertFromString(s))
        { }

        private static string ConvertToString(DeviceConnection connection)
        {
            if (connection == null)
                return "{}";

            var dto = new DeviceConnectionDto
            {
                ConnectedDeviceId = connection.ConnectedDeviceId,
                InterfaceDeviceId = connection.InterfaceDeviceId,
                Type = connection.Type,
                Direction = connection.Direction
            };

            return JsonSerializer.Serialize(dto);
        }

        private static DeviceConnection ConvertFromString(string json)
        {
            if (string.IsNullOrEmpty(json))
                return null;

            return CreateDeviceConnectionFromJson(json);
        }

        private static DeviceConnection CreateDeviceConnectionFromJson(string json)
        {
            try
            {
                var dto = JsonSerializer.Deserialize<DeviceConnectionDto>(json);
                if (dto == null || dto.ConnectedDeviceId == Guid.Empty || dto.InterfaceDeviceId == Guid.Empty)
                    return null;

                return new DeviceConnection(
                    dto.ConnectedDeviceId,
                    dto.InterfaceDeviceId,
                    dto.Type,
                    dto.Direction
                );
            }
            catch (Exception)
            {
                // Fallback to null if there's any error
                return null;
            }
        }
    }
}
