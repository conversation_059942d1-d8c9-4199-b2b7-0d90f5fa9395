using Barret.Core.Areas.Common.ValueObjects;
using Barret.Shared.DTOs.Devices;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Text.Json;

namespace Barret.Web.Server.Data.ValueConverters
{
    /// <summary>
    /// Value converter for the RelativePosition immutable value object.
    /// Converts between RelativePosition and a string representation for database storage.
    /// </summary>
    public class RelativePositionConverter : ValueConverter<RelativePosition, string>
    {
        public RelativePositionConverter() : base(
            // Convert to database (RelativePosition -> string)
            v => ConvertToString(v),

            // Convert from database (string -> RelativePosition)
            s => ConvertFromString(s))
        { }

        private static string ConvertToString(RelativePosition position)
        {
            if (position.Equals(default(RelativePosition)))
                return "{\"X\":0,\"Y\":0,\"Z\":0}";

            var dto = new RelativePositionDto
            {
                X = position.X,
                Y = position.Y,
                Z = position.Z
            };

            return JsonSerializer.Serialize(dto);
        }

        private static RelativePosition ConvertFromString(string json)
        {
            if (string.IsNullOrEmpty(json))
                return RelativePosition.CreateDefault();

            return CreatePositionFromJson(json);
        }

        private static RelativePosition CreatePositionFromJson(string json)
        {
            try
            {
                var dto = JsonSerializer.Deserialize<RelativePositionDto>(json);
                return new RelativePosition(
                    dto?.X ?? 0,
                    dto?.Y ?? 0,
                    dto?.Z ?? 0
                );
            }
            catch (Exception)
            {
                // Fallback to default if there's any error
                return RelativePosition.CreateDefault();
            }
        }
    }
}
