using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Shared.DTOs.Devices;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Text.Json;

namespace Barret.Web.Server.Data.ValueConverters
{
    /// <summary>
    /// Value converter for the ConnectionHandler immutable value object.
    /// Converts between ConnectionHandler and a string representation for database storage.
    /// </summary>
    public class ConnectionHandlerConverter : ValueConverter<ConnectionHandler, string>
    {
        public ConnectionHandlerConverter() : base(
            // Convert to database (ConnectionHandler -> string)
            v => ConvertToString(v),

            // Convert from database (string -> ConnectionHandler)
            s => ConvertFromString(s))
        { }

        private static string ConvertToString(ConnectionHandler handler)
        {
            if (handler.Equals(default(ConnectionHandler)))
                return "{\"IPAddress\":\"*************\",\"Port\":8080,\"Protocol\":0}";

            var dto = new ConnectionHandlerDto
            {
                IPAddress = handler.IPAddress,
                Port = handler.Port,
                Protocol = handler.Protocol
            };

            return JsonSerializer.Serialize(dto);
        }

        private static ConnectionHandler ConvertFromString(string json)
        {
            if (string.IsNullOrEmpty(json))
                return ConnectionHandler.CreateDefault();

            return CreateConnectionHandlerFromJson(json);
        }

        private static ConnectionHandler CreateConnectionHandlerFromJson(string json)
        {
            try
            {
                var dto = JsonSerializer.Deserialize<ConnectionHandlerDto>(json);
                return new ConnectionHandler(
                    dto?.IPAddress ?? "*************",
                    dto?.Port ?? 8080,
                    dto?.Protocol ?? Protocol.TcpClient
                );
            }
            catch (Exception)
            {
                // Fallback to default if there's any error
                return ConnectionHandler.CreateDefault();
            }
        }
    }
}
