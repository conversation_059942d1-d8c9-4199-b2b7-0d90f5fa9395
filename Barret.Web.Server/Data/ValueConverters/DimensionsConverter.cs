using Barret.Core.Areas.Vehicles.ValueObjects;
using Barret.Shared.DTOs.Vehicles;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.Text.Json;

namespace Barret.Web.Server.Data.ValueConverters
{
    /// <summary>
    /// Value converter for the DimensionsFromGpsLocation immutable value object.
    /// Converts between DimensionsFromGpsLocation and a string representation for database storage.
    /// </summary>
    public class DimensionsConverter : ValueConverter<DimensionsFromGpsLocation, string>
    {
        public DimensionsConverter() : base(
            // Convert to database (DimensionsFromGpsLocation -> string)
            v => ConvertToString(v),

            // Convert from database (string -> DimensionsFromGpsLocation)
            s => ConvertFromString(s))
        { }

        private static string ConvertToString(DimensionsFromGpsLocation dimensions)
        {
            if (dimensions.Equals(default(DimensionsFromGpsLocation)))
                return "{\"DistanceGpsToFront\":0,\"DistanceGpsToBack\":0,\"DistanceGpsToLeft\":0,\"DistanceGpsToRight\":0}";

            var dto = new DimensionsDto
            {
                DistanceGpsToFront = dimensions.DistanceGpsToFront,
                DistanceGpsToBack = dimensions.DistanceGpsToBack,
                DistanceGpsToLeft = dimensions.DistanceGpsToLeft,
                DistanceGpsToRight = dimensions.DistanceGpsToRight
            };

            return JsonSerializer.Serialize(dto);
        }

        private static DimensionsFromGpsLocation ConvertFromString(string json)
        {
            if (string.IsNullOrEmpty(json))
                return DimensionsFromGpsLocation.CreateDefault();

            return CreateDimensionsFromJson(json);
        }

        private static DimensionsFromGpsLocation CreateDimensionsFromJson(string json)
        {
            try
            {
                var dto = JsonSerializer.Deserialize<DimensionsDto>(json);
                return new DimensionsFromGpsLocation(
                    dto?.DistanceGpsToFront ?? 0,
                    dto?.DistanceGpsToBack ?? 0,
                    dto?.DistanceGpsToLeft ?? 0,
                    dto?.DistanceGpsToRight ?? 0
                );
            }
            catch (Exception)
            {
                // Fallback to default if there's any error
                return DimensionsFromGpsLocation.CreateDefault();
            }
        }
    }
}
