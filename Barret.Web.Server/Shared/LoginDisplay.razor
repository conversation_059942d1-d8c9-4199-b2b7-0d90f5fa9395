@using Microsoft.AspNetCore.Components.Authorization
@using Microsoft.AspNetCore.Components.Web
@using Microsoft.AspNetCore.Identity
@using System
@implements IDisposable
@inject NavigationManager Navigation

<AuthorizeView>
    <Authorized>
        <div class="relative">
            <button @onclick="ToggleDropdown"
                    class="flex items-center gap-2 px-4 py-2 rounded-full border border-gray-200 text-gray-700 hover:bg-gray-50 transition-colors">
                <span class="hidden sm:inline">Hello, @context.User.Identity?.Name!</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M6 9l6 6 6-6"/>
                </svg>
            </button>

            @if (isDropdownOpen)
            {
                <div class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 border border-gray-100">
                    <a href="/admin" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Admin Dashboard
                    </a>
                    <a href="/Identity/Account/Manage" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        Manage Account
                    </a>
                    <div class="border-t border-gray-100 my-1"></div>
                    <form method="post" action="/Identity/Account/Logout?returnUrl=/">
                        <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                            Log out
                        </button>
                    </form>
                </div>
            }
        </div>
    </Authorized>
    <NotAuthorized>
        <div class="flex gap-2">
            <a href="/Identity/Account/Register" class="px-4 py-2 rounded-full border border-gray-200 text-gray-700 hover:bg-gray-50 transition-colors">
                Register
            </a>
            <a href="/Identity/Account/Login" class="px-4 py-2 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors">
                Log in
            </a>
        </div>
    </NotAuthorized>
</AuthorizeView>

@code {
    private bool isDropdownOpen = false;
    private bool isJsInitialized = false;
    private DotNetObjectReference<LoginDisplay>? objRef;

    private void ToggleDropdown()
    {
        isDropdownOpen = !isDropdownOpen;
    }

    // We'll set up the event listener after the component is rendered
    protected override async Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender && !isJsInitialized)
        {
            // Only create the reference and set up the listener once
            objRef = DotNetObjectReference.Create(this);
            await JSRuntime.InvokeVoidAsync("document.addEventListener", "click", objRef);
            isJsInitialized = true;
        }
    }

    [JSInvokable]
    public void HandleDocumentClick()
    {
        if (isDropdownOpen)
        {
            isDropdownOpen = false;
            StateHasChanged();
        }
    }

    // Clean up the event listener and dispose the reference when the component is disposed
    public async void Dispose()
    {
        if (isJsInitialized && objRef != null)
        {
            try
            {
                // Remove the event listener
                await JSRuntime.InvokeVoidAsync("document.removeEventListener", "click", objRef);
                // Dispose the reference
                objRef.Dispose();
            }
            catch
            {
                // Ignore exceptions during disposal
            }
        }
    }
}

@inject IJSRuntime JSRuntime