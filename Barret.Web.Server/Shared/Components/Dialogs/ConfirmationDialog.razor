@using Microsoft.AspNetCore.Components.Web
@using <PERSON><PERSON><PERSON>
@inject Radzen.DialogService DialogService

@* Radzen Dialog Implementation using DialogService *@

@code {
    [Parameter]
    public bool IsVisible { get; set; }

    [Parameter]
    public string Title { get; set; } = "Confirmation";

    [Parameter]
    public string Message { get; set; } = "Are you sure you want to proceed?";

    [Parameter]
    public string ConfirmText { get; set; } = "Confirm";

    [Parameter]
    public string CancelText { get; set; } = "Cancel";

    [Parameter]
    public string Icon { get; set; } = "bi bi-question-circle";

    [Parameter]
    public string IconClass { get; set; } = "text-gray-700";

    [Parameter]
    public string DialogClass { get; set; } = "bg-white rounded-xl shadow-lg";

    [Parameter]
    public string ConfirmButtonClass { get; set; } = "bg-gray-900 hover:bg-gray-800 text-white rounded-full px-4 py-2";

    [Parameter]
    public string CancelButtonClass { get; set; } = "border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-full px-4 py-2";

    [Parameter]
    public EventCallback OnConfirm { get; set; }

    [Parameter]
    public EventCallback OnCancel { get; set; }

    private bool _previousIsVisible = false;

    protected override async Task OnParametersSetAsync()
    {
        // Show dialog when IsVisible changes from false to true
        if (IsVisible && !_previousIsVisible)
        {
            await ShowDialog();
        }
        _previousIsVisible = IsVisible;
    }

    private async Task ShowDialog()
    {
        var options = new ConfirmOptions()
        {
            OkButtonText = ConfirmText,
            CancelButtonText = CancelText,
            Width = "400px",
            CssClass = "barret-confirmation-dialog"
        };

        // Create custom message with icon if provided
        var messageContent = CreateMessageContent();

        var result = await DialogService.Confirm(messageContent, Title, options);

        if (result == true)
        {
            await OnConfirm.InvokeAsync();
        }
        else
        {
            await OnCancel.InvokeAsync();
        }
    }

    private RenderFragment CreateMessageContent()
    {
        return builder =>
        {
            var sequence = 0;

            // Main container following the styling guide
            builder.OpenElement(sequence++, "div");
            builder.AddAttribute(sequence++, "class", "flex items-start gap-4");

            // Icon container with proper styling
            if (!string.IsNullOrEmpty(Icon))
            {
                builder.OpenElement(sequence++, "div");
                builder.AddAttribute(sequence++, "class", GetIconContainerClass());

                builder.OpenElement(sequence++, "i");
                builder.AddAttribute(sequence++, "class", $"{Icon} {GetIconClass()}");
                builder.CloseElement();

                builder.CloseElement(); // Close icon container
            }

            // Message content container
            builder.OpenElement(sequence++, "div");
            builder.AddAttribute(sequence++, "class", "flex-1");

            // Message text
            builder.OpenElement(sequence++, "p");
            builder.AddAttribute(sequence++, "class", "text-gray-600 text-sm leading-relaxed");
            builder.AddContent(sequence++, Message);
            builder.CloseElement();

            builder.CloseElement(); // Close message container
            builder.CloseElement(); // Close main container
        };
    }

    /// <summary>
    /// Gets the icon container CSS class based on the icon type.
    /// </summary>
    private string GetIconContainerClass()
    {
        // Determine icon type based on icon class or default to info
        if (IconClass.Contains("red") || Icon.Contains("exclamation") || Icon.Contains("warning"))
        {
            return "h-12 w-12 rounded-full bg-red-50 flex items-center justify-center flex-shrink-0 mt-1";
        }
        else if (IconClass.Contains("green") || Icon.Contains("check"))
        {
            return "h-12 w-12 rounded-full bg-green-50 flex items-center justify-center flex-shrink-0 mt-1";
        }
        else
        {
            return "h-12 w-12 rounded-full bg-gray-100 flex items-center justify-center flex-shrink-0 mt-1";
        }
    }

    /// <summary>
    /// Gets the icon CSS class based on the icon type.
    /// </summary>
    private string GetIconClass()
    {
        // Determine icon color based on icon class or default to gray
        if (IconClass.Contains("red") || Icon.Contains("exclamation") || Icon.Contains("warning"))
        {
            return "h-6 w-6 text-red-500";
        }
        else if (IconClass.Contains("green") || Icon.Contains("check"))
        {
            return "h-6 w-6 text-green-500";
        }
        else
        {
            return "h-6 w-6 text-gray-700";
        }
    }
}
