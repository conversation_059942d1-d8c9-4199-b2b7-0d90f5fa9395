@using Microsoft.AspNetCore.Components.Web
@using Ra<PERSON>zen
@inject Radzen.DialogService DialogService

@* Reusable Form Dialog Component for Radzen Dialog System *@

<div class="barret-form-dialog">
    <div class="barret-dialog-header">
        <div class="flex items-center">
            @if (!string.IsNullOrEmpty(Icon))
            {
                <i class="@Icon @IconColorClass mr-3"></i>
            }
            <h3 class="text-lg font-semibold text-gray-900 m-0">@Title</h3>
        </div>
    </div>
    
    <div class="barret-dialog-content">
        @if (!string.IsNullOrEmpty(Description))
        {
            <p class="text-gray-600 mb-4">@Description</p>
        }
        
        @if (HasError)
        {
            <div class="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
                <div class="flex items-center">
                    <i class="bi bi-exclamation-triangle text-red-500 mr-2"></i>
                    <span class="text-red-700 text-sm">@ErrorMessage</span>
                </div>
            </div>
        }
        
        @ChildContent
    </div>
    
    <div class="barret-dialog-footer">
        <div class="barret-dialog-btn-group">
            <RadzenButton Text="@CancelText"
                          ButtonStyle="ButtonStyle.Secondary"
                          Click="@CancelAsync"
                          Disabled="@IsProcessing"
                          class="barret-btn barret-form-btn" />
            <RadzenButton Text="@ConfirmText"
                          ButtonStyle="ButtonStyle.Primary"
                          Click="@ConfirmAsync"
                          Disabled="@(!IsValid || IsProcessing)"
                          class="barret-btn barret-form-btn">
                @if (IsProcessing)
                {
                    <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>
                }
                @ConfirmText
            </RadzenButton>
        </div>
    </div>
</div>

@code {
    [Parameter] public string Title { get; set; } = "Form Dialog";
    [Parameter] public string Description { get; set; } = "";
    [Parameter] public string Icon { get; set; } = "";
    [Parameter] public string IconColorClass { get; set; } = "text-blue-600";
    [Parameter] public string ConfirmText { get; set; } = "Save";
    [Parameter] public string CancelText { get; set; } = "Cancel";
    [Parameter] public bool IsValid { get; set; } = true;
    [Parameter] public bool IsProcessing { get; set; } = false;
    [Parameter] public bool HasError { get; set; } = false;
    [Parameter] public string ErrorMessage { get; set; } = "";
    [Parameter] public RenderFragment? ChildContent { get; set; }
    [Parameter] public EventCallback OnConfirm { get; set; }
    [Parameter] public EventCallback OnCancel { get; set; }

    private async Task ConfirmAsync()
    {
        if (OnConfirm.HasDelegate)
        {
            await OnConfirm.InvokeAsync();
        }
        else
        {
            // Default behavior: close dialog with success result
            DialogService.Close(true);
        }
    }

    private async Task CancelAsync()
    {
        if (OnCancel.HasDelegate)
        {
            await OnCancel.InvokeAsync();
        }
        else
        {
            // Default behavior: close dialog with cancel result
            DialogService.Close(false);
        }
    }
}
