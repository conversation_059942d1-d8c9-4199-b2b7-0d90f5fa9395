@using Barret.Shared.DTOs.Devices

<div class="position-tab">
    <h5>Device Position</h5>
    <div class="card">
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="positionX" class="form-label">X Position</label>
                        <input type="number" class="form-control" id="positionX" value="@Device.Position.X" @onchange="@(e => OnPositionXChanged(e))" />
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="positionY" class="form-label">Y Position</label>
                        <input type="number" class="form-control" id="positionY" value="@Device.Position.Y" @onchange="@(e => OnPositionYChanged(e))" />
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label for="positionZ" class="form-label">Z Position</label>
                        <input type="number" class="form-control" id="positionZ" value="@Device.Position.Z" @onchange="@(e => OnPositionZChanged(e))" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    private async Task OnPositionXChanged(ChangeEventArgs e)
    {
        if (double.TryParse(e.Value?.ToString(), out var newValue) && Device.Position.X != newValue)
        {
            Device.Position.X = newValue;
            if (OnPropertyChanged.HasDelegate)
            {
                await OnPropertyChanged.InvokeAsync();
            }
        }
    }

    private async Task OnPositionYChanged(ChangeEventArgs e)
    {
        if (double.TryParse(e.Value?.ToString(), out var newValue) && Device.Position.Y != newValue)
        {
            Device.Position.Y = newValue;
            if (OnPropertyChanged.HasDelegate)
            {
                await OnPropertyChanged.InvokeAsync();
            }
        }
    }

    private async Task OnPositionZChanged(ChangeEventArgs e)
    {
        if (double.TryParse(e.Value?.ToString(), out var newValue) && Device.Position.Z != newValue)
        {
            Device.Position.Z = newValue;
            if (OnPropertyChanged.HasDelegate)
            {
                await OnPropertyChanged.InvokeAsync();
            }
        }
    }
}
