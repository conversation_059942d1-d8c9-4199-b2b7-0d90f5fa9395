@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Autopilots

<div class="autopilot-tab">
    <h5>Autopilot Settings</h5>
    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Autopilot Type</label>
                <select class="form-select" @bind="AutopilotType">
                    <option value="Standard">Standard</option>
                    <option value="Advanced">Advanced</option>
                    <option value="Professional">Professional</option>
                </select>
            </div>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="hasGpsIntegration" @bind="HasGpsIntegration" />
                <label class="form-check-label" for="hasGpsIntegration">Has GPS Integration</label>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // Autopilot-specific properties with property change notifications
    private string _autopilotType = "Standard";
    private string AutopilotType
    {
        get => _autopilotType;
        set
        {
            if (_autopilotType != value)
            {
                _autopilotType = value;
                NotifyPropertyChanged();
            }
        }
    }

    private bool _hasGpsIntegration = true;
    private bool HasGpsIntegration
    {
        get => _hasGpsIntegration;
        set
        {
            if (_hasGpsIntegration != value)
            {
                _hasGpsIntegration = value;
                NotifyPropertyChanged();
            }
        }
    }

    private void NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            _ = OnPropertyChanged.InvokeAsync();
        }
    }
}
