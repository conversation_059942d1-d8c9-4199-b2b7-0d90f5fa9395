@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@namespace Barret.Web.Server.Shared.Components.DeviceEditors.Tabs

<div class="row mb-3">
    <div class="col-md-4">
        <div class="form-group">
            <label for="ipAddress" class="form-label">IP Address</label>
            <input type="text" class="form-control @(RequireConnection && string.IsNullOrWhiteSpace(Device.Connection.IPAddress) ? "border-danger" : "")"
                   id="ipAddress"
                   @bind="Device.Connection.IPAddress"
                   @bind:after="NotifyPropertyChanged" />
            @if (RequireConnection && string.IsNullOrWhiteSpace(Device.Connection.IPAddress))
            {
                <div class="text-danger">IP Address is required</div>
            }
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label for="port" class="form-label">Port</label>
            <input type="number" class="form-control @(RequireConnection && Device.Connection.Port <= 0 ? "border-danger" : "")"
                   id="port"
                   @bind="Device.Connection.Port"
                   @bind:after="NotifyPropertyChanged" />
            @if (RequireConnection && Device.Connection.Port <= 0)
            {
                <div class="text-danger">Port is required</div>
            }
        </div>
    </div>
    <div class="col-md-4">
        <div class="form-group">
            <label for="protocol" class="form-label">Protocol</label>
            <select class="form-select @(RequireConnection && Device.Connection.Protocol == Protocol.Undefined ? "border-danger" : "")"
                    id="protocol"
                    @bind="Device.Connection.Protocol"
                    @bind:after="NotifyPropertyChanged">
                @foreach (var protocol in Enum.GetValues(typeof(Protocol)))
                {
                    <option value="@protocol">@protocol</option>
                }
            </select>
            @if (RequireConnection && Device.Connection.Protocol == Protocol.Undefined)
            {
                <div class="text-danger">Protocol is required</div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; } = null!;

    [Parameter]
    public bool RequireConnection { get; set; } = false;

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    private void NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            _ = OnPropertyChanged.InvokeAsync();
        }
    }
}
