@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Cameras

<div class="camera-tab">
    <h5>Camera Settings</h5>
    <div class="card">
        <div class="card-body">
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="showVideo" @bind="ShowVideo" />
                <label class="form-check-label" for="showVideo">Show Video</label>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // ShowVideo property bound to the Device property
    private bool ShowVideo
    {
        get
        {
            bool result = Device is CameraDto cameraDto ? cameraDto.ShowVideo : false;
            Console.WriteLine($"[CameraTab] Getting ShowVideo for {Device?.Name} = {result}");
            return result;
        }
        set
        {
            Console.WriteLine($"[CameraTab] Setting ShowVideo for {Device?.Name} to {value}");
            if (Device is CameraDto cameraDto)
            {
                cameraDto.ShowVideo = value;
                Console.WriteLine($"[CameraTab] Updated CameraDto.ShowVideo for {cameraDto.Name} to {cameraDto.ShowVideo}");

                // Notify that device properties have changed
                if (OnPropertyChanged.HasDelegate)
                {
                    _ = OnPropertyChanged.InvokeAsync();
                }
            }
        }
    }
}
