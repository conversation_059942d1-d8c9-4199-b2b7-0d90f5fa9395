@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Antennas

<div class="antenna-tab">
    <h5>Antenna Settings</h5>
    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Antenna Type</label>
                <select class="form-select" @bind="AntennaType">
                    <option value="GPS">GPS</option>
                    <option value="VHF">VHF</option>
                    <option value="AIS">AIS</option>
                    <option value="Satellite">Satellite</option>
                    <option value="TV">TV</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Gain (dB)</label>
                <input type="number" class="form-control" @bind="Gain" />
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // Antenna-specific properties with property change notifications
    private string _antennaType = "GPS";
    private string AntennaType
    {
        get => _antennaType;
        set
        {
            if (_antennaType != value)
            {
                _antennaType = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _gain = 10;
    private double Gain
    {
        get => _gain;
        set
        {
            if (_gain != value)
            {
                _gain = value;
                NotifyPropertyChanged();
            }
        }
    }

    private void NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            _ = OnPropertyChanged.InvokeAsync();
        }
    }
}
