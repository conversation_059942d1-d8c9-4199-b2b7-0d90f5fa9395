@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Rudders

<div class="rudder-tab">
    <h5>Rudder Settings</h5>
    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Rudder Type</label>
                <select class="form-select" @bind="RudderType">
                    <option value="Standard">Standard</option>
                    <option value="Balanced">Balanced</option>
                    <option value="Semi-balanced">Semi-balanced</option>
                    <option value="Spade">Spade</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Maximum Angle (degrees)</label>
                <input type="number" class="form-control" @bind="MaxAngle" />
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // Rudder-specific properties with property change notifications
    private string _rudderType = "Standard";
    private string RudderType
    {
        get => _rudderType;
        set
        {
            if (_rudderType != value)
            {
                _rudderType = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _maxAngle = 35;
    private double MaxAngle
    {
        get => _maxAngle;
        set
        {
            if (_maxAngle != value)
            {
                _maxAngle = value;
                NotifyPropertyChanged();
            }
        }
    }

    private void NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            _ = OnPropertyChanged.InvokeAsync();
        }
    }
}
