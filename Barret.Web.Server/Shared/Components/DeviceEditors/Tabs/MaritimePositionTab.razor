@using Barret.Shared.DTOs.Devices
@using Barret.Core.Areas.Devices.Enums

<div class="maritime-position-tab">
    <h5>Maritime Position</h5>
    <div class="card">
        <div class="card-body">
            <div class="row mb-3">
                <div class="col-md-6">
                    <div class="form-group">
                        <label for="foreAftPosition" class="form-label">Fore-Aft Position</label>
                        <select class="form-select" id="foreAftPosition" value="@SelectedForeAft" @onchange="@OnForeAftChanged">
                            <option value="@ForeAftPosition.None">Not specified</option>
                            <option value="@ForeAftPosition.Bow">Bow (Forward)</option>
                            <option value="@ForeAftPosition.Stern">Stern (Aft)</option>
                        </select>
                        <div class="form-text">
                            Select the fore-aft position of this device on the vessel.
                        </div>
                    </div>
                </div>
            </div>

            @if (Device.MaritimePosition?.IsDefined == true)
            {
                <div class="row">
                    <div class="col-12">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            <strong>Current Position:</strong> @Device.MaritimePosition.ToDisplayString()
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; } = null!;

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    private ForeAftPosition SelectedForeAft
    {
        get => Device.MaritimePosition?.ForeAft ?? ForeAftPosition.None;
    }

    private async Task OnForeAftChanged(ChangeEventArgs e)
    {
        if (Enum.TryParse<ForeAftPosition>(e.Value?.ToString(), out var newValue))
        {
            var oldValue = SelectedForeAft;

            // Initialize maritime position if it doesn't exist
            Device.MaritimePosition ??= new MaritimePositionDto();

            // Update the fore-aft position
            Device.MaritimePosition.ForeAft = newValue;

            // Clear maritime position if all values are None/null
            if (Device.MaritimePosition.ForeAft == ForeAftPosition.None &&
                Device.MaritimePosition.Lateral == LateralPosition.None &&
                !Device.MaritimePosition.Facing.HasValue)
            {
                Device.MaritimePosition = null;
            }

            // Notify of change if value actually changed
            if (oldValue != newValue && OnPropertyChanged.HasDelegate)
            {
                await OnPropertyChanged.InvokeAsync();
            }
        }
    }
}
