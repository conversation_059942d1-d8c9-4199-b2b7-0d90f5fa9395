@namespace Barret.Web.Server.Shared.Components.DeviceEditors.Tabs

<li class="nav-item" role="presentation">
    <button class="nav-link @(IsActive ? "active" : "")" id="@Id-tab" data-bs-toggle="tab" data-bs-target="#@Id" type="button" role="tab" aria-controls="@Id" aria-selected="@IsActive.ToString().ToLower()">
        @Title
    </button>
</li>

@code {
    /// <summary>
    /// The unique identifier for this tab header.
    /// </summary>
    [Parameter]
    public string Id { get; set; } = "";

    /// <summary>
    /// The title to display in the tab header.
    /// </summary>
    [Parameter]
    public string Title { get; set; } = "";

    /// <summary>
    /// Whether this tab is currently active.
    /// </summary>
    [Parameter]
    public bool IsActive { get; set; }
}
