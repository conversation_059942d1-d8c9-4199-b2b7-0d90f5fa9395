@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Radars

<div class="radar-tab">
    <h5>Radar Settings</h5>
    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Radar Type</label>
                <select class="form-select" @bind="RadarType">
                    <option value="Navigation">Navigation</option>
                    <option value="Weather">Weather</option>
                    <option value="Surveillance">Surveillance</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Range (km)</label>
                <input type="number" class="form-control" @bind="Range" />
            </div>
            <div class="mb-3">
                <label class="form-label">Frequency (GHz)</label>
                <input type="number" class="form-control" @bind="Frequency" />
            </div>
            <div class="mb-3">
                <label class="form-label">Rotation Speed (rpm)</label>
                <input type="number" class="form-control" @bind="RotationSpeed" />
            </div>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="hasDopplerMode" @bind="HasDopplerMode" />
                <label class="form-check-label" for="hasDopplerMode">Has Doppler Mode</label>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // Radar-specific properties with property change notifications
    private string _radarType = "Navigation";
    private string RadarType
    {
        get => _radarType;
        set
        {
            if (_radarType != value)
            {
                _radarType = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _range = 50;
    private double Range
    {
        get => _range;
        set
        {
            if (_range != value)
            {
                _range = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _frequency = 9.4;
    private double Frequency
    {
        get => _frequency;
        set
        {
            if (_frequency != value)
            {
                _frequency = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _rotationSpeed = 24;
    private double RotationSpeed
    {
        get => _rotationSpeed;
        set
        {
            if (_rotationSpeed != value)
            {
                _rotationSpeed = value;
                NotifyPropertyChanged();
            }
        }
    }

    private bool _hasDopplerMode = true;
    private bool HasDopplerMode
    {
        get => _hasDopplerMode;
        set
        {
            if (_hasDopplerMode != value)
            {
                _hasDopplerMode = value;
                NotifyPropertyChanged();
            }
        }
    }

    private void NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            _ = OnPropertyChanged.InvokeAsync();
        }
    }
}
