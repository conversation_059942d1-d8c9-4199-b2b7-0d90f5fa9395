@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Horns

<div class="horn-tab">
    <h5>Horn Settings</h5>
    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Horn Type</label>
                <select class="form-select" @bind="HornType">
                    <option value="Standard">Standard</option>
                    <option value="Fog">Fog</option>
                    <option value="Emergency">Emergency</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Sound Level (dB)</label>
                <input type="number" class="form-control" @bind="SoundLevel" />
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // Horn-specific properties with property change notifications
    private string _hornType = "Standard";
    private string HornType
    {
        get => _hornType;
        set
        {
            if (_hornType != value)
            {
                _hornType = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _soundLevel = 120;
    private double SoundLevel
    {
        get => _soundLevel;
        set
        {
            if (_soundLevel != value)
            {
                _soundLevel = value;
                NotifyPropertyChanged();
            }
        }
    }

    private void NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            _ = OnPropertyChanged.InvokeAsync();
        }
    }
}
