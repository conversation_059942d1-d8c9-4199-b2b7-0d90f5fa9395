@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Thrusters

<div class="thruster-tab">
    <h5>Thruster Settings</h5>
    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Thruster Type</label>
                <select class="form-select" @bind="ThrusterType">
                    <option value="Bow">Bow</option>
                    <option value="Stern">Stern</option>
                    <option value="Tunnel">Tunnel</option>
                    <option value="Azimuth">Azimuth</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Power (kW)</label>
                <input type="number" class="form-control" @bind="Power" />
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // Thruster-specific properties with property change notifications
    private string _thrusterType = "Bow";
    private string ThrusterType
    {
        get => _thrusterType;
        set
        {
            if (_thrusterType != value)
            {
                _thrusterType = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _power = 100;
    private double Power
    {
        get => _power;
        set
        {
            if (_power != value)
            {
                _power = value;
                NotifyPropertyChanged();
            }
        }
    }

    private void NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            _ = OnPropertyChanged.InvokeAsync();
        }
    }
}
