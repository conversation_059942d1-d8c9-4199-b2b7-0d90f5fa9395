@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Engines

<div class="engine-tab">
    <h5>Engine Settings</h5>
    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Engine Type</label>
                <select class="form-select" @bind="EngineType">
                    <option value="Diesel">Diesel</option>
                    <option value="Gasoline">Gasoline</option>
                    <option value="Electric">Electric</option>
                    <option value="Hybrid">Hybrid</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Horsepower</label>
                <input type="number" class="form-control" @bind="Horsepower" />
            </div>
            <div class="mb-3">
                <label class="form-label">Fuel Capacity (L)</label>
                <input type="number" class="form-control" @bind="FuelCapacity" />
            </div>
            <div class="mb-3">
                <label class="form-label">Maximum RPM</label>
                <input type="number" class="form-control" @bind="MaxRpm" />
            </div>
            <div class="mb-3">
                <label class="form-label">Fuel Consumption (L/h)</label>
                <input type="number" class="form-control" @bind="FuelConsumption" />
            </div>
            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="hasStarterMotor" @bind="HasStarterMotor" />
                <label class="form-check-label" for="hasStarterMotor">Has Starter Motor</label>
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // Engine-specific properties with property change notifications
    private string _engineType = "Diesel";
    private string EngineType
    {
        get => _engineType;
        set
        {
            if (_engineType != value)
            {
                _engineType = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _horsepower = 500;
    private double Horsepower
    {
        get => _horsepower;
        set
        {
            if (_horsepower != value)
            {
                _horsepower = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _fuelCapacity = 1000;
    private double FuelCapacity
    {
        get => _fuelCapacity;
        set
        {
            if (_fuelCapacity != value)
            {
                _fuelCapacity = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _maxRpm = 3000;
    private double MaxRpm
    {
        get => _maxRpm;
        set
        {
            if (_maxRpm != value)
            {
                _maxRpm = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _fuelConsumption = 50;
    private double FuelConsumption
    {
        get => _fuelConsumption;
        set
        {
            if (_fuelConsumption != value)
            {
                _fuelConsumption = value;
                NotifyPropertyChanged();
            }
        }
    }

    private bool _hasStarterMotor = true;
    private bool HasStarterMotor
    {
        get => _hasStarterMotor;
        set
        {
            if (_hasStarterMotor != value)
            {
                _hasStarterMotor = value;
                NotifyPropertyChanged();
            }
        }
    }

    private void NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            _ = OnPropertyChanged.InvokeAsync();
        }
    }
}
