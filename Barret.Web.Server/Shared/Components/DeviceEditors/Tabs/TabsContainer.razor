@namespace Barret.Web.Server.Shared.Components.DeviceEditors.Tabs

<div class="card">
    <div class="card-header">
        <ul class="nav nav-tabs card-header-tabs" id="@Id" role="tablist">
            @TabHeaders
        </ul>
    </div>
    <div class="card-body">
        <div class="tab-content">
            @TabContents
        </div>
    </div>
</div>

@code {
    /// <summary>
    /// The unique identifier for this tabs container.
    /// </summary>
    [Parameter]
    public string Id { get; set; } = "device-tabs";

    /// <summary>
    /// The tab headers to display.
    /// </summary>
    [Parameter]
    public RenderFragment TabHeaders { get; set; } = null!;

    /// <summary>
    /// The tab contents to display.
    /// </summary>
    [Parameter]
    public RenderFragment TabContents { get; set; } = null!;
}
