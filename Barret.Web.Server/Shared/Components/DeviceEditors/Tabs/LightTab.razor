@using Barret.Shared.DTOs.Devices
@using Barret.Shared.DTOs.Devices.Lights

<div class="light-tab">
    <h5>Light Settings</h5>
    <div class="card">
        <div class="card-body">
            <div class="mb-3">
                <label class="form-label">Light Type</label>
                <select class="form-select" @bind="LightType">
                    <option value="Navigation">Navigation</option>
                    <option value="Deck">Deck</option>
                    <option value="Interior">Interior</option>
                    <option value="Searchlight">Searchlight</option>
                </select>
            </div>
            <div class="mb-3">
                <label class="form-label">Brightness (lumens)</label>
                <input type="number" class="form-control" @bind="Brightness" />
            </div>
            <div class="mb-3">
                <label class="form-label">Color Temperature (K)</label>
                <input type="number" class="form-control" @bind="ColorTemperature" />
            </div>
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    // Light-specific properties with property change notifications
    private string _lightType = "Navigation";
    private string LightType
    {
        get => _lightType;
        set
        {
            if (_lightType != value)
            {
                _lightType = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _brightness = 1000;
    private double Brightness
    {
        get => _brightness;
        set
        {
            if (_brightness != value)
            {
                _brightness = value;
                NotifyPropertyChanged();
            }
        }
    }

    private double _colorTemperature = 5000;
    private double ColorTemperature
    {
        get => _colorTemperature;
        set
        {
            if (_colorTemperature != value)
            {
                _colorTemperature = value;
                NotifyPropertyChanged();
            }
        }
    }

    private void NotifyPropertyChanged()
    {
        if (OnPropertyChanged.HasDelegate)
        {
            _ = OnPropertyChanged.InvokeAsync();
        }
    }
}
