@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Barret.Services.Core.Areas.DeviceModels.Queries
@using Microsoft.Extensions.Logging
@namespace Barret.Web.Server.Shared.Components.DeviceEditors.Tabs
@inject IDeviceModelQueryService DeviceModelQueryService

<div class="row mb-3">
    <div class="col-md-6">
        <div class="form-group">
            <label for="deviceManufacturer" class="form-label">Make</label>
            <RadzenDropDown @bind-Value="@SelectedManufacturerId"
                           Data="@Manufacturers"
                           TextProperty="Name"
                           ValueProperty="Id"
                           Placeholder="Select manufacturer (optional)"
                           AllowClear="true"
                           class="barret-input w-full" />
        </div>
    </div>
    <div class="col-md-6">
        <div class="form-group">
            <label for="deviceModel" class="form-label">Model</label>
            <RadzenDropDown @bind-Value="@DeviceModelId"
                           Data="@FilteredModels"
                           TextProperty="Name"
                           ValueProperty="Id"
                           Placeholder="Select model (optional)"
                           AllowClear="true"
                           Disabled="@(_selectedManufacturerId == Guid.Empty || !FilteredModels.Any())"
                           class="barret-input w-full" />
            @if (_selectedManufacturerId != Guid.Empty && !FilteredModels.Any())
            {
                <div class="text-muted mt-1">
                    <small>No models available for selected manufacturer</small>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter]
    public DeviceDto Device { get; set; } = null!;

    [Parameter]
    public List<ManufacturerInfo> Manufacturers { get; set; } = new List<ManufacturerInfo>();

    [Parameter]
    public bool IsModelRequired { get; set; }

    [Parameter]
    public bool ShowValidationErrors { get; set; }

    [Parameter]
    public EventCallback<Guid> OnManufacturerSelected { get; set; }

    [Parameter]
    public EventCallback OnPropertyChanged { get; set; }

    [Inject]
    private ILogger<ModelTab> Logger { get; set; } = null!;

    private Guid _selectedManufacturerId;
    private Guid SelectedManufacturerId
    {
        get => _selectedManufacturerId;
        set
        {
            if (_selectedManufacturerId != value)
            {
                _selectedManufacturerId = value;
                _ = OnManufacturerSelectionChanged(value);
            }
        }
    }

    private Guid? DeviceModelId
    {
        get => Device.DeviceModelId;
        set
        {
            if (Device.DeviceModelId != value)
            {
                _ = OnModelSelectionChanged(value);
            }
        }
    }

    private List<DeviceModelInfo> FilteredModels { get; set; } = new List<DeviceModelInfo>();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        // Set the selected manufacturer based on the device's model if it exists
        if (Device.DeviceModelId.HasValue && Device.DeviceModelId.Value != Guid.Empty)
        {
            SetSelectedManufacturerFromModel();
        }

        // Initialize the filtered models
        await UpdateFilteredModels();
    }

    protected override async Task OnParametersSetAsync()
    {
        await base.OnParametersSetAsync();

        // If the device model changed, update the selected manufacturer
        if (Device.DeviceModelId.HasValue && Device.DeviceModelId.Value != Guid.Empty)
        {
            SetSelectedManufacturerFromModel();
        }

        // Update the filtered models based on the selected manufacturer
        await UpdateFilteredModels();
    }

    private async void SetSelectedManufacturerFromModel()
    {
        if (Device.DeviceModelId.HasValue && Device.DeviceModelId.Value != Guid.Empty)
        {
            try
            {
                // Get the device model to find its manufacturer
                var model = await DeviceModelQueryService.GetDeviceModelByIdAsync(Device.DeviceModelId.Value);
                if (model != null)
                {
                    _selectedManufacturerId = model.ManufacturerId;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error getting device model {ModelId}", Device.DeviceModelId);
            }
        }
    }

    private async Task OnManufacturerSelectionChanged(Guid manufacturerId)
    {
        // Update the selected manufacturer ID
        _selectedManufacturerId = manufacturerId;

        // Reset the model when manufacturer changes
        Device.DeviceModelId = null;
        Device.ModelName = null;
        Device.ManufacturerName = null;

        // Update the filtered models
        await UpdateFilteredModels();

        // Notify parent component
        await OnManufacturerSelected.InvokeAsync(manufacturerId);

        // Notify that device properties have changed
        if (OnPropertyChanged.HasDelegate)
        {
            await OnPropertyChanged.InvokeAsync();
        }
    }

    private async Task OnModelSelectionChanged(Guid? modelId)
    {
        if (modelId.HasValue && modelId.Value != Guid.Empty)
        {
            // Find the selected model
            var selectedModel = FilteredModels.FirstOrDefault(m => m.Id == modelId.Value);
            if (selectedModel != null)
            {
                // Update the device model ID and display properties
                Device.DeviceModelId = selectedModel.Id;
                Device.ModelName = selectedModel.Name;
                Device.ManufacturerName = selectedModel.ManufacturerName;
            }
        }
        else
        {
            // Clear the model
            Device.DeviceModelId = null;
            Device.ModelName = null;
            Device.ManufacturerName = null;
        }

        // Notify that device properties have changed
        if (OnPropertyChanged.HasDelegate)
        {
            await OnPropertyChanged.InvokeAsync();
        }
    }

    private async Task UpdateFilteredModels()
    {
        // Clear the filtered models list
        FilteredModels.Clear();

        Logger.LogInformation("Updating filtered models for device role {DeviceRole}", Device.DeviceRole);
        Logger.LogInformation("Total manufacturers available: {Count}", Manufacturers.Count);

        // Get device models from the selected manufacturer
        if (_selectedManufacturerId != Guid.Empty)
        {
            Logger.LogInformation("Selected manufacturer ID: {ManufacturerId}", _selectedManufacturerId);

            try
            {
                // Get device models for the selected manufacturer and device role
                var models = await DeviceModelQueryService.GetDeviceModelsForManufacturerWithRoleAsync(
                    _selectedManufacturerId, Device.DeviceRole);

                // Add the filtered models to our list
                FilteredModels.AddRange(models);

                // Log the filtered models
                Logger.LogInformation("Added {Count} device models to filtered list", FilteredModels.Count);
                foreach (var model in FilteredModels)
                {
                    Logger.LogInformation("Filtered model: {ModelName} (ID: {ModelId}, Role: {DeviceRole})",
                        model.Name, model.Id, model.DeviceRole);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading device models for manufacturer {ManufacturerId} and role {DeviceRole}",
                    _selectedManufacturerId, Device.DeviceRole);
            }
        }
        else
        {
            Logger.LogInformation("No manufacturer selected");
        }

        // Force a state update to ensure the UI reflects the changes
        StateHasChanged();
    }
}
