@namespace Barret.Web.Server.Shared.Components.DeviceEditors.Tabs

<div class="tab-pane fade @(IsActive ? "show active" : "")" id="@Id" role="tabpanel" aria-labelledby="@Id-tab">
    @ChildContent
</div>

@code {
    /// <summary>
    /// The unique identifier for this tab.
    /// </summary>
    [Parameter]
    public string Id { get; set; } = "";

    /// <summary>
    /// Whether this tab is currently active.
    /// </summary>
    [Parameter]
    public bool IsActive { get; set; }

    /// <summary>
    /// The content to display in this tab.
    /// </summary>
    [Parameter]
    public RenderFragment ChildContent { get; set; } = null!;
}
