@page
@model Barret.Web.Server.Areas.Identity.Pages.Account.LoginModel
@{
    ViewData["Title"] = "Log in";
}

<div class="container">
    <div class="row justify-content-center mt-5">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-primary-foreground">
                    <h4 class="m-0">@ViewData["Title"]</h4>
                </div>
                <div class="card-body">
                    <form id="account" method="post">
                        <div asp-validation-summary="ModelOnly" class="text-danger" role="alert"></div>
                        <div class="mb-3">
                            <label asp-for="Input.Email" class="form-label">Email</label>
                            <input asp-for="Input.Email" class="form-control" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
                            <span asp-validation-for="Input.Email" class="text-danger"></span>
                        </div>
                        <div class="mb-3">
                            <label asp-for="Input.Password" class="form-label">Password</label>
                            <input asp-for="Input.Password" class="form-control" autocomplete="current-password" aria-required="true" placeholder="password" />
                            <span asp-validation-for="Input.Password" class="text-danger"></span>
                        </div>
                        <div class="mb-3 form-check">
                            <input class="form-check-input" asp-for="Input.RememberMe" />
                            <label class="form-check-label" asp-for="Input.RememberMe">Remember me</label>
                        </div>
                        <div>
                            <button id="login-submit" type="submit" class="btn btn-primary w-100">Log in</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}