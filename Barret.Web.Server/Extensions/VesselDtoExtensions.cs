using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles.Vessels;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Barret.Web.Server.Extensions
{
    /// <summary>
    /// Extension methods for VesselDto
    /// </summary>
    public static class VesselDtoExtensions
    {
        /// <summary>
        /// Gets the total number of devices in a vessel
        /// </summary>
        /// <param name="vessel">The vessel DTO</param>
        /// <returns>The total number of devices</returns>
        public static int GetDeviceCount(this VesselDto vessel)
        {
            if (vessel == null || vessel.DeviceGroups == null)
            {
                return 0;
            }

            return vessel.GetAllDevices().Count();
        }

        /// <summary>
        /// Gets a device by ID from a vessel
        /// </summary>
        /// <param name="vessel">The vessel DTO</param>
        /// <param name="deviceId">The device ID</param>
        /// <returns>The device DTO, or null if not found</returns>
        public static DeviceDto GetDeviceById(this VesselDto vessel, Guid deviceId)
        {
            if (vessel == null || vessel.DeviceGroups == null)
            {
                return null;
            }

            return vessel.GetAllDevices().FirstOrDefault(d => d.Id == deviceId);
        }

        /// <summary>
        /// Gets all devices in a vessel
        /// </summary>
        /// <param name="vessel">The vessel DTO</param>
        /// <returns>A collection of all devices</returns>
        public static IEnumerable<DeviceDto> GetAllDevices(this VesselDto vessel)
        {
            if (vessel == null || vessel.DeviceGroups == null)
            {
                return Enumerable.Empty<DeviceDto>();
            }

            var devices = new List<DeviceDto>();
            foreach (var group in vessel.DeviceGroups)
            {
                // Access the Value property of the KeyValuePair to get the DeviceGroupDto
                var deviceGroup = group.Value;
                if (deviceGroup.Devices != null)
                {
                    devices.AddRange(deviceGroup.Devices);
                }
            }
            return devices;
        }
    }
}
