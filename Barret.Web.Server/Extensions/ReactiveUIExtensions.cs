using System;
using System.Reactive;
using System.Reactive.Linq;
using System.Reactive.Threading.Tasks;
using System.Threading.Tasks;

namespace Barret.Web.Server.Extensions
{
    /// <summary>
    /// Extension methods for ReactiveUI.
    /// </summary>
    public static class ReactiveUIExtensions
    {
        /// <summary>
        /// Converts an IObservable to a Task.
        /// </summary>
        /// <typeparam name="T">The type of the observable.</typeparam>
        /// <param name="observable">The observable to convert.</param>
        /// <returns>A task that completes when the observable completes.</returns>
        public static Task<T> ToTask<T>(this IObservable<T> observable)
        {
            if (observable == null)
                throw new ArgumentNullException(nameof(observable));
                
            var tcs = new TaskCompletionSource<T>();
            
            var subscription = observable.Subscribe(
                value => tcs.TrySetResult(value),
                ex => tcs.TrySetException(ex),
                () => {
                    // If no value was produced, set a default value
                    if (typeof(T) == typeof(Unit))
                        tcs.TrySetResult((T)(object)Unit.Default);
                    else
                        tcs.TrySetResult(default);
                });
            
            return tcs.Task.ContinueWith(t => {
                subscription.Dispose();
                return t;
            }).Unwrap();
        }

        /// <summary>
        /// Converts an IObservable of Unit to a Task.
        /// </summary>
        /// <param name="observable">The observable to convert.</param>
        /// <returns>A task that completes when the observable completes.</returns>
        public static Task ToTask(this IObservable<Unit> observable)
        {
            if (observable == null)
                throw new ArgumentNullException(nameof(observable));
                
            var tcs = new TaskCompletionSource<Unit>();
            
            var subscription = observable.Subscribe(
                unit => tcs.TrySetResult(unit),
                ex => tcs.TrySetException(ex),
                () => tcs.TrySetResult(Unit.Default));
            
            return tcs.Task.ContinueWith(t => {
                subscription.Dispose();
                return t;
            }).Unwrap();
        }
    }
}
