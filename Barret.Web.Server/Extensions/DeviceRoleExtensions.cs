using Barret.Core.Areas.Devices.Enums;
using System.Collections.Immutable;

namespace Barret.Web.Server.Extensions
{
    /// <summary>
    /// Extension methods for device roles in the UI.
    /// </summary>
    public static class DeviceRoleExtensions
    {
        #region Display Names

        private static readonly ImmutableDictionary<DeviceRole, string> DisplayNames = new Dictionary<DeviceRole, string>
        {
            { DeviceRole.Camera, "Camera" },
            { DeviceRole.Engine, "Engine" },
            { DeviceRole.Thruster, "Thruster" },
            { DeviceRole.Radar, "Radar" },
            { DeviceRole.Antenna, "Antenna" },
            { DeviceRole.Autopilot, "Autopilot" },
            { DeviceRole.Trackpilot, "Trackpilot" },
            { DeviceRole.NVRScreen, "NVR - Screens" },
            { DeviceRole.NVRRecording, "NVR - Recording" },
            { DeviceRole.AudioHub, "Audio Hub" },
            { DeviceRole.Framegrabber, "Radar Framegrabber" },
            { DeviceRole.PAAudio, "PA Audio" },
            { DeviceRole.HMI, "HMI" },
            { DeviceRole.Firewall, "Firewall" },
            { DeviceRole.VHFMariphone, "VHF Mariphone" },
            { DeviceRole.VHFNetworkInterface, "VDE Adapter" },
            { DeviceRole.Light, "Light" },
            { DeviceRole.Horn, "Horn" },
            { DeviceRole.Rudder, "Rudder" },
            { DeviceRole.Sensor, "Sensor" },
            { DeviceRole.AMP, "Amplifier" },
            { DeviceRole.SPAP, "Speaker/PA" },
            { DeviceRole.Switch, "Network Switch" },
            { DeviceRole.Generic, "Generic Device" },
            { DeviceRole.CabinetReadoutIO, "Cabinet Readout I/O" },
            { DeviceRole.OperatorPanelIO, "Operator Panel I/O" },
            { DeviceRole.GPU, "GPU" },
            { DeviceRole.SafetySystemHead, "Safety System Head" },
            { DeviceRole.Firewall, "Firewall" },
            { DeviceRole.Gateway, "Gateway" },
            { DeviceRole.Switch, "Switch" },
            { DeviceRole.Plc, "PLC" },
            { DeviceRole.NavData, "Navigation Data" }
        }.ToImmutableDictionary();

        /// <summary>
        /// Gets a user-friendly display name for a device role.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>A user-friendly display name.</returns>
        public static string GetDisplayName(this DeviceRole role)
        {
            return DisplayNames.TryGetValue(role, out var displayName)
                ? displayName
                : role.ToString();
        }

        #endregion

        #region Plural Names

        private static readonly ImmutableDictionary<DeviceRole, string> PluralNames = new Dictionary<DeviceRole, string>
        {
            { DeviceRole.Camera, "Cameras" },
            { DeviceRole.Engine, "Engines" },
            { DeviceRole.Thruster, "Thrusters" },
            { DeviceRole.Radar, "Radars" },
            { DeviceRole.Antenna, "Antennas" },
            { DeviceRole.Autopilot, "Autopilots" },
            { DeviceRole.Trackpilot, "Trackpilots" },
            { DeviceRole.NVRScreen, "NVR - Screens" },
            { DeviceRole.NVRRecording, "NVR - Recording" },
            { DeviceRole.AudioHub, "Audio Hubs" },
            { DeviceRole.Framegrabber, "Radar Framegrabbers" },
            { DeviceRole.PAAudio, "PA Audio Devices" },
            { DeviceRole.HMI, "HMIs" },
            { DeviceRole.Firewall, "Firewalls" },
            { DeviceRole.VHFMariphone, "VHF Mariphones" },
            { DeviceRole.VHFNetworkInterface, "VDE Adapters" },
            { DeviceRole.Light, "Lights" },
            { DeviceRole.Horn, "Horns" },
            { DeviceRole.Rudder, "Rudders" },
            { DeviceRole.Sensor, "Sensors" },
            { DeviceRole.AMP, "Amplifiers" },
            { DeviceRole.SPAP, "Speaker/PAs" },
            { DeviceRole.Switch, "Network Switches" },
            { DeviceRole.Generic, "Generic Devices" },
            { DeviceRole.CabinetReadoutIO, "Cabinet Readout I/Os" },
            { DeviceRole.OperatorPanelIO, "Operator Panel I/Os" },
            { DeviceRole.GPU, "GPUs" },
            { DeviceRole.SafetySystemHead, "Safety System Heads" },
            { DeviceRole.Firewall, "Firewalls" },
            { DeviceRole.Gateway, "Gateways" },
            { DeviceRole.Switch, "Switches" },
            { DeviceRole.Plc, "PLCs" },
            { DeviceRole.NavData, "Navigation Data Systems" }
        }.ToImmutableDictionary();

        /// <summary>
        /// Gets a user-friendly plural name for a device role.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>A user-friendly plural name.</returns>
        public static string GetPluralName(this DeviceRole role)
        {
            return PluralNames.TryGetValue(role, out var pluralName)
                ? pluralName
                : role.ToString() + "s";
        }

        #endregion

        #region Icon Classes

        private static readonly ImmutableDictionary<DeviceRole, string> IconClasses = new Dictionary<DeviceRole, string>
        {
            { DeviceRole.Camera, "bi bi-camera-video" },
            { DeviceRole.Engine, "bi bi-gear" },
            { DeviceRole.Thruster, "bi bi-arrow-left-right" },
            { DeviceRole.Radar, "bi bi-broadcast" },
            { DeviceRole.Antenna, "bi bi-broadcast-pin" },
            { DeviceRole.Autopilot, "bi bi-compass" },
            { DeviceRole.NVRScreen, "bi bi-display" },
            { DeviceRole.NVRRecording, "bi bi-record-circle" },
            { DeviceRole.Trackpilot, "bi bi-compass" },
            { DeviceRole.AudioHub, "bi bi-speaker" },
            { DeviceRole.Framegrabber, "bi bi-camera" },
            { DeviceRole.PAAudio, "bi bi-speaker" },
            { DeviceRole.HMI, "bi bi-display" },
            { DeviceRole.Firewall, "bi bi-shield" },
            { DeviceRole.VHFMariphone, "bi bi-broadcast-pin" },
            { DeviceRole.VHFNetworkInterface, "bi bi-ethernet" },
            { DeviceRole.Light, "bi bi-lightbulb" },
            { DeviceRole.Horn, "bi bi-megaphone" },
            { DeviceRole.Rudder, "bi bi-arrow-down-up" },
            { DeviceRole.Sensor, "bi bi-thermometer-half" },
            { DeviceRole.AMP, "bi bi-volume-up" },
            { DeviceRole.SPAP, "bi bi-speaker" },
            { DeviceRole.Switch, "bi bi-hdd-network" },
            { DeviceRole.Generic, "bi bi-device-ssd" },
            { DeviceRole.CabinetReadoutIO, "bi bi-cpu" },
            { DeviceRole.OperatorPanelIO, "bi bi-cpu" },
            { DeviceRole.GPU, "bi bi-gpu-card" },
            { DeviceRole.SafetySystemHead, "bi bi-shield-check" },
            { DeviceRole.Firewall, "bi bi-shield" },
            { DeviceRole.Gateway, "bi bi-router" },
            { DeviceRole.Switch, "bi bi-ethernet" },
            { DeviceRole.Plc, "bi bi-cpu" },
            { DeviceRole.NavData, "bi bi-compass" }
        }.ToImmutableDictionary();

        /// <summary>
        /// Gets an icon CSS class for a device role.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>A CSS class for the icon.</returns>
        public static string GetIconClass(this DeviceRole role)
        {
            return IconClasses.TryGetValue(role, out var iconClass)
                ? iconClass
                : "bi bi-question-circle";
        }

        #endregion

        #region CSS Classes

        private static readonly ImmutableDictionary<DeviceRole, string> CssClasses = new Dictionary<DeviceRole, string>
        {
            { DeviceRole.Camera, "device-camera" },
            { DeviceRole.NVRScreen, "device-nvr" },
            { DeviceRole.NVRRecording, "device-nvr" },
            { DeviceRole.Engine, "device-engine" },
            { DeviceRole.Thruster, "device-thruster" },
            { DeviceRole.Radar, "device-radar" },
            { DeviceRole.Light, "device-light" },
            { DeviceRole.Rudder, "device-rudder" },
            { DeviceRole.Horn, "device-horn" },
            { DeviceRole.Antenna, "device-antenna" },
            { DeviceRole.Autopilot, "device-autopilot" },
            { DeviceRole.Trackpilot, "device-trackpilot" },
            { DeviceRole.VHFMariphone, "device-radio" },
            { DeviceRole.Sensor, "device-sensor" },
            { DeviceRole.AMP, "device-amp" },
            { DeviceRole.SPAP, "device-spap" },
            { DeviceRole.AudioHub, "device-audio-hub" },
            { DeviceRole.Switch, "device-network-switch" },
            { DeviceRole.Generic, "device-generic" }
        }.ToImmutableDictionary();

        /// <summary>
        /// Gets a CSS class for a device role to use in styling.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>A CSS class for styling.</returns>
        public static string GetCssClass(this DeviceRole role)
        {
            return CssClasses.TryGetValue(role, out var cssClass)
                ? cssClass
                : "device-unknown";
        }

        #endregion

        #region Badge Colors

        private static readonly ImmutableDictionary<DeviceRole, string> BadgeColors = new Dictionary<DeviceRole, string>
        {
            { DeviceRole.Camera, "primary" },
            { DeviceRole.NVRScreen, "info" },
            { DeviceRole.NVRRecording, "info" },
            { DeviceRole.Engine, "danger" },
            { DeviceRole.Thruster, "warning" },
            { DeviceRole.Radar, "success" },
            { DeviceRole.Light, "warning" },
            { DeviceRole.Rudder, "secondary" },
            { DeviceRole.Horn, "dark" },
            { DeviceRole.Antenna, "info" },
            { DeviceRole.Autopilot, "primary" },
            { DeviceRole.Trackpilot, "primary" },
            { DeviceRole.VHFMariphone, "info" },
            { DeviceRole.Sensor, "secondary" },
            { DeviceRole.AMP, "dark" },
            { DeviceRole.SPAP, "dark" },
            { DeviceRole.AudioHub, "dark" },
            { DeviceRole.Switch, "info" },
            { DeviceRole.Generic, "secondary" }
        }.ToImmutableDictionary();

        /// <summary>
        /// Gets a badge color for a device role.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>A Bootstrap badge color class.</returns>
        public static string GetBadgeColor(this DeviceRole role)
        {
            return BadgeColors.TryGetValue(role, out var badgeColor)
                ? badgeColor
                : "secondary";
        }

        #endregion
    }
}
