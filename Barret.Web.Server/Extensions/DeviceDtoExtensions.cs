using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices;

namespace Barret.Web.Server.Extensions
{
    /// <summary>
    /// Extension methods for DeviceDto
    /// </summary>
    public static class DeviceDtoExtensions
    {
        /// <summary>
        /// Gets all connected devices for a device
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="allDevices">All devices in the vehicle</param>
        /// <returns>A list of all connected devices</returns>
        public static List<DeviceDto> GetConnectedDevices(this DeviceDto device, List<DeviceDto> allDevices)
        {
            if (device?.Connections == null || allDevices == null)
            {
                return [];
            }

            return device.Connections
                .Select(c => allDevices.FirstOrDefault(d => d.Id == c.InterfaceDeviceId))
                .Where(d => d != null)
                .ToList();
        }

        /// <summary>
        /// Gets all connected devices with a specific role
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="allDevices">All devices in the vehicle</param>
        /// <param name="role">The device role</param>
        /// <returns>A list of all connected devices with the specified role</returns>
        public static List<DeviceDto> GetConnectedDevicesByRole(this DeviceDto device, List<DeviceDto> allDevices, DeviceRole role)
        {
            return device.GetConnectedDevices(allDevices).Where(d => d.DeviceRole == role).ToList();
        }

        /// <summary>
        /// Gets a connected device by ID
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="allDevices">All devices in the vehicle</param>
        /// <param name="connectedDeviceId">The connected device ID</param>
        /// <returns>The connected device DTO, or null if not found</returns>
        public static DeviceDto GetConnectedDeviceById(this DeviceDto device, List<DeviceDto> allDevices, Guid connectedDeviceId)
        {
            return device.GetConnectedDevices(allDevices).FirstOrDefault(d => d.Id == connectedDeviceId);
        }

        /// <summary>
        /// Checks if a device is connected to another device
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <param name="connectedDeviceId">The connected device ID</param>
        /// <returns>True if the device is connected to the specified device, false otherwise</returns>
        public static bool IsConnectedTo(this DeviceDto device, Guid connectedDeviceId)
        {
            return device?.Connections?.Any(c => c.InterfaceDeviceId == connectedDeviceId) == true;
        }

        /// <summary>
        /// Gets a formatted display name for a device
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <returns>A formatted display name</returns>
        public static string GetDisplayName(this DeviceDto device)
        {
            if (device == null)
            {
                return string.Empty;
            }

            string roleName = device.DeviceRole.GetDisplayName();
            return string.IsNullOrEmpty(device.Name) ? roleName : $"{device.Name} ({roleName})";
        }

        /// <summary>
        /// Gets a short display name for a device
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <returns>A short display name</returns>
        public static string GetShortDisplayName(this DeviceDto device)
        {
            if (device == null)
            {
                return string.Empty;
            }

            return string.IsNullOrEmpty(device.Name) ? device.DeviceRole.GetDisplayName() : device.Name;
        }

        /// <summary>
        /// Gets whether the device is active (UI property)
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <returns>True if the device is considered active</returns>
        public static bool IsActive(this DeviceDto device)
        {
            // For now, consider a device active if it has a model or connections
            return device?.HasModel == true || (device?.Connections?.Count > 0);
        }

        /// <summary>
        /// Gets the device model for UI display
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <returns>A device model info object for UI binding</returns>
        public static DeviceModelInfo? DeviceModel(this DeviceDto device)
        {
            if (device?.DeviceModelId == null || string.IsNullOrEmpty(device.ModelName))
            {
                return null;
            }

            return new DeviceModelInfo
            {
                Id = device.DeviceModelId.Value,
                Name = device.ModelName,
                DeviceRole = device.DeviceRole,
                ManufacturerId = device.ManufacturerId ?? Guid.Empty
            };
        }

        /// <summary>
        /// Gets the description for the device (UI property)
        /// </summary>
        /// <param name="device">The device DTO</param>
        /// <returns>A description string</returns>
        public static string Description(this DeviceDto device)
        {
            if (device == null)
            {
                return string.Empty;
            }

            // Generate a description based on device properties
            var parts = new List<string>();

            if (!string.IsNullOrEmpty(device.ModelName))
            {
                parts.Add($"Model: {device.ModelName}");
            }

            if (!string.IsNullOrEmpty(device.ManufacturerName))
            {
                parts.Add($"Manufacturer: {device.ManufacturerName}");
            }

            if (device.Connections?.Count > 0)
            {
                parts.Add($"Connections: {device.Connections.Count}");
            }

            return parts.Count > 0 ? string.Join(", ", parts) : $"{device.DeviceRole} device";
        }
    }
}
