using Microsoft.Extensions.DependencyInjection;

namespace Barret.Web.Server
{
    /// <summary>
    /// Extension methods for WebApplicationBuilder
    /// </summary>
    public static class WebApplicationBuilderExtensions
    {
        /// <summary>
        /// Adds test services to the service collection
        /// </summary>
        /// <param name="builder">The WebApplicationBuilder</param>
        /// <returns>The WebApplicationBuilder</returns>
        public static WebApplicationBuilder AddTestServices(this WebApplicationBuilder builder)
        {
            // Commented out until we're ready to use it
            // builder.Services.AddTestServices();
            return builder;
        }
    }
}
