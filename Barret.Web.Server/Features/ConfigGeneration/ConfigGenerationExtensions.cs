using System;
using System.IO;
using Barret.Services.Areas.ConfigGeneration.Extensions;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace Barret.Web.Server.Features.ConfigGeneration
{
    /// <summary>
    /// Extension methods for registering configuration generation services
    /// </summary>
    public static class ConfigGenerationExtensions
    {
        /// <summary>
        /// Adds configuration generation services to the service collection
        /// </summary>
        /// <param name="builder">The web application builder</param>
        /// <returns>The web application builder for chaining</returns>
        public static WebApplicationBuilder AddConfigGenerationServices(this WebApplicationBuilder builder)
        {
            var loggerFactory = builder.Services.BuildServiceProvider().GetRequiredService<ILoggerFactory>();
            var logger = loggerFactory.CreateLogger("ConfigGeneration");

            try
            {
                // Get the template base path from the shared project
                var templateBasePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "ConfigTemplates");

                // If the directory doesn't exist, try to find it in the Barret.Shared project
                if (!Directory.Exists(templateBasePath))
                {
                    // Try to find the Barret.Shared project directory
                    var currentDir = new DirectoryInfo(AppDomain.CurrentDomain.BaseDirectory);
                    while (currentDir != null && !Directory.Exists(Path.Combine(currentDir.FullName, "Barret.Shared", "ConfigTemplates")))
                    {
                        currentDir = currentDir.Parent;
                    }

                    if (currentDir != null)
                    {
                        templateBasePath = Path.Combine(currentDir.FullName, "Barret.Shared", "ConfigTemplates");
                    }
                }

                // Ensure the template base path exists
                if (!Directory.Exists(templateBasePath))
                {
                    logger.LogWarning("Template base path not found: {TemplatePath}. Configuration generation will not work correctly.", templateBasePath);
                    return builder;
                }

                logger.LogInformation("Using template base path: {TemplatePath}", templateBasePath);

                // Register configuration services
                builder.Services.AddConfigurationServices(templateBasePath);

                return builder;
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error adding configuration generation services");
                return builder;
            }
        }
    }
}
