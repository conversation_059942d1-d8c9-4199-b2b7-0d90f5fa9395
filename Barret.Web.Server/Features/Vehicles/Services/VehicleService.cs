using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Extensions;
using Barret.Core.Areas.DeviceGroups;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Core.Configuration;
using Barret.Services.Areas.Vehicles;
using Barret.Services.Core.Areas.Vehicles;
using Barret.Services.Core.Areas.Vehicles.Queries;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Shared.Results;
using Barret.Web.Server.Features.Vehicles.Data;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Services
{
    /// <summary>
    /// Service for interacting with vehicle data.
    /// This service adapts the backend VesselService to the frontend data model.
    /// </summary>
    public class VehicleService : IVehicleService
    {
        private readonly IVehicleService<Vessel, VesselDto> _vesselService;
        private readonly IVesselQueryService _vesselQueryService;
        private readonly ILogger<VehicleService> _logger;
        private readonly IOptions<DeviceModelCompatibilityConfig> _modelCompatibilityConfig;

        /// <summary>
        /// Initializes a new instance of the <see cref="VehicleService"/> class.
        /// </summary>
        /// <param name="vesselService">The vessel service.</param>
        /// <param name="vesselQueryService">The vessel query service.</param>
        /// <param name="logger">The logger.</param>
        /// <param name="modelCompatibilityConfig">The model compatibility configuration.</param>
        public VehicleService(
            IVehicleService<Vessel, VesselDto> vesselService,
            IVesselQueryService vesselQueryService,
            ILogger<VehicleService> logger,
            IOptions<DeviceModelCompatibilityConfig> modelCompatibilityConfig)
        {
            _vesselService = vesselService ?? throw new ArgumentNullException(nameof(vesselService));
            _vesselQueryService = vesselQueryService ?? throw new ArgumentNullException(nameof(vesselQueryService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _modelCompatibilityConfig = modelCompatibilityConfig ?? throw new ArgumentNullException(nameof(modelCompatibilityConfig));
        }

        /// <summary>
        /// Gets all vehicles.
        /// </summary>
        /// <returns>A list of vehicle data objects.</returns>
        public async Task<ServiceResult<List<VehicleData>>> GetAllVehiclesAsync()
        {
            try
            {
                _logger.LogInformation("Getting all vehicles");

                // Get all vessels from the service
                var vesselsResult = await _vesselService.GetAllAsync();
                if (!vesselsResult.Success)
                {
                    _logger.LogWarning("Failed to get vessels: {ErrorMessage}", vesselsResult.ErrorMessage);
                    return ServiceResult<List<VehicleData>>.CreateFailure(vesselsResult.ErrorMessage);
                }

                // Map vessels to vehicle data
                var vehicles = vesselsResult.Data
                    .Select(MapVesselToVehicleData)
                    .ToList();

                _logger.LogInformation("Retrieved {Count} vehicles", vehicles.Count);
                return ServiceResult<List<VehicleData>>.CreateSuccess(vehicles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all vehicles");
                return ServiceResult<List<VehicleData>>.CreateFailure($"Error getting vehicles: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets all vehicles for list display (lightweight, optimized for performance).
        /// This method doesn't load devices or device groups, making it much faster for list views.
        /// </summary>
        /// <returns>A list of vehicle data objects with basic information only.</returns>
        public async Task<ServiceResult<List<VehicleData>>> GetAllVehiclesForListAsync()
        {
            try
            {
                _logger.LogInformation("Getting all vehicles for list display");

                // Call backend service (proper layer separation)
                var vesselsResult = await _vesselService.GetAllBasicAsync();
                if (!vesselsResult.Success)
                {
                    _logger.LogWarning("Failed to get basic vessels: {ErrorMessage}", vesselsResult.ErrorMessage);
                    return ServiceResult<List<VehicleData>>.CreateFailure(vesselsResult.ErrorMessage);
                }

                // Map vessels to vehicle data
                var vehicles = vesselsResult.Data
                    .Select(MapVesselToVehicleData)
                    .ToList();

                _logger.LogInformation("Retrieved {Count} vehicles for list display", vehicles.Count);
                return ServiceResult<List<VehicleData>>.CreateSuccess(vehicles);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vehicles for list display");
                return ServiceResult<List<VehicleData>>.CreateFailure($"Error getting vehicles for list display: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets a vehicle by ID.
        /// </summary>
        /// <param name="id">The ID of the vehicle to get.</param>
        /// <returns>The vehicle data object.</returns>
        public async Task<ServiceResult<VehicleData>> GetVehicleByIdAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Getting vehicle with ID {VehicleId}", id);

                // Get the vessel from the service
                var vesselResult = await _vesselService.GetByIdAsync(id);
                if (!vesselResult.Success)
                {
                    _logger.LogWarning("Failed to get vessel: {ErrorMessage}", vesselResult.ErrorMessage);
                    return ServiceResult<VehicleData>.CreateFailure(vesselResult.ErrorMessage);
                }

                // Map vessel to vehicle data
                var vehicle = MapVesselToVehicleData(vesselResult.Data);

                _logger.LogInformation("Retrieved vehicle {VehicleName}", vehicle.Name);
                return ServiceResult<VehicleData>.CreateSuccess(vehicle);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting vehicle with ID {VehicleId}", id);
                return ServiceResult<VehicleData>.CreateFailure($"Error getting vehicle: {ex.Message}");
            }
        }

        /// <summary>
        /// Deletes a vehicle.
        /// </summary>
        /// <param name="id">The ID of the vehicle to delete.</param>
        /// <returns>A service result indicating success or failure.</returns>
        public async Task<ServiceResult> DeleteVehicleAsync(Guid id)
        {
            try
            {
                _logger.LogInformation("Deleting vehicle with ID {VehicleId}", id);

                // Delete the vessel using the service
                var result = await _vesselService.DeleteAsync(id);
                if (!result.Success)
                {
                    _logger.LogWarning("Failed to delete vessel: {ErrorMessage}", result.ErrorMessage);
                    return ServiceResult.CreateFailure(result.ErrorMessage);
                }

                _logger.LogInformation("Vehicle with ID {VehicleId} deleted successfully", id);
                return ServiceResult.CreateSuccess();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting vehicle with ID {VehicleId}", id);
                return ServiceResult.CreateFailure($"Error deleting vehicle: {ex.Message}");
            }
        }

        /// <summary>
        /// Copies a vehicle.
        /// </summary>
        /// <param name="id">The ID of the vehicle to copy.</param>
        /// <param name="newName">The name for the new vehicle.</param>
        /// <returns>A service result containing the ID of the new vehicle.</returns>
        public async Task<ServiceResult<Guid>> CopyVehicleAsync(Guid id, string newName)
        {
            try
            {
                _logger.LogInformation("Copying vehicle with ID {VehicleId} to new name {NewName}", id, newName);

                // Copy the vessel using the service
                var result = await _vesselService.CopyVehicleAsync(id, newName);
                if (!result.Success)
                {
                    _logger.LogWarning("Failed to copy vessel: {ErrorMessage}", result.ErrorMessage);
                    return ServiceResult<Guid>.CreateFailure(result.ErrorMessage);
                }

                _logger.LogInformation("Vehicle with ID {VehicleId} copied successfully to new ID {NewVehicleId}", id, result.Data);
                return ServiceResult<Guid>.CreateSuccess(result.Data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error copying vehicle with ID {VehicleId}", id);
                return ServiceResult<Guid>.CreateFailure($"Error copying vehicle: {ex.Message}");
            }
        }

        /// <summary>
        /// Determines if a device can connect to an interface device based on their roles and models.
        /// </summary>
        /// <param name="connectedDevice">The device being connected.</param>
        /// <param name="interfaceDevice">The device serving as an interface.</param>
        /// <returns>True if the connection is valid; otherwise, false.</returns>
        public bool CanDevicesConnect(DeviceDto connectedDevice, DeviceDto interfaceDevice)
        {
            if (connectedDevice == null || interfaceDevice == null)
                return false;

            if (connectedDevice.Id == interfaceDevice.Id)
                return false; // A device cannot connect to itself

            // First check: Role-based compatibility
            bool roleCompatible = connectedDevice.DeviceRole.IsCompatibleWith(interfaceDevice.DeviceRole);

            // If roles are not compatible, no need to check models
            if (!roleCompatible)
                return false;

            // Second check: Model-based compatibility (if both devices have models)
            if (!string.IsNullOrEmpty(connectedDevice.ModelName) && !string.IsNullOrEmpty(interfaceDevice.ModelName))
            {
                return AreDeviceModelsCompatible(
                    connectedDevice.ModelName,
                    interfaceDevice.ModelName,
                    connectedDevice.DeviceRole,
                    interfaceDevice.DeviceRole);
            }

            // If no model information, rely on role compatibility
            return true;
        }

        /// <summary>
        /// Determines if two device models are compatible.
        /// </summary>
        /// <param name="connectedModelName">The name of the model being connected.</param>
        /// <param name="interfaceModelName">The name of the model serving as an interface.</param>
        /// <param name="connectedDeviceRole">The role of the device being connected.</param>
        /// <param name="interfaceDeviceRole">The role of the device serving as an interface.</param>
        /// <returns>True if the models are compatible; otherwise, false.</returns>
        private bool AreDeviceModelsCompatible(
            string connectedModelName,
            string interfaceModelName,
            DeviceRole connectedDeviceRole,
            DeviceRole interfaceDeviceRole)
        {
            var models = _modelCompatibilityConfig.Value.Models;

            // Check if the connected model has configuration
            if (models.TryGetValue(connectedModelName, out var connectedModelConfig))
            {
                // Check if the interface model is in the list of compatible models
                return connectedModelConfig.CanConnectTo.Contains(interfaceModelName);
            }

            // If no compatibility information is available, assume compatibility based on roles
            return true;
        }

        /// <summary>
        /// Gets devices that can serve as interfaces for a given device.
        /// </summary>
        /// <param name="device">The device to find compatible interfaces for.</param>
        /// <param name="candidates">The candidate devices to check.</param>
        /// <returns>A collection of devices that can serve as interfaces for the given device.</returns>
        public IEnumerable<DeviceDto> GetCompatibleInterfaceDevices(DeviceDto device, IEnumerable<DeviceDto> candidates)
        {
            return candidates.Where(candidate =>
                candidate.Id != device.Id &&
                CanDevicesConnect(device, candidate));
        }

        /// <summary>
        /// Gets devices that can connect to a given device as an interface.
        /// </summary>
        /// <param name="interfaceDevice">The device serving as an interface.</param>
        /// <param name="candidates">The candidate devices to check.</param>
        /// <returns>A collection of devices that can connect to the given interface device.</returns>
        public IEnumerable<DeviceDto> GetDevicesThatCanConnectToInterface(DeviceDto interfaceDevice, IEnumerable<DeviceDto> candidates)
        {
            return candidates.Where(candidate =>
                candidate.Id != interfaceDevice.Id &&
                CanDevicesConnect(candidate, interfaceDevice));
        }



        /// <summary>
        /// Gets the allowed device roles for a specific device group from the domain model.
        /// </summary>
        /// <param name="deviceGroupType">The device group type.</param>
        /// <returns>A list of allowed device roles for the specified group.</returns>
        public List<DeviceRole> GetAllowedDeviceRolesForGroup(DeviceGroups deviceGroupType)
        {
            try
            {
                _logger.LogDebug("Getting allowed roles for device group: {GroupType}", deviceGroupType);

                // Get allowed roles from the DeviceGroups enum
                var allowedRoles = deviceGroupType.GetAllowedRoles().ToList();
                _logger.LogInformation("Found {Count} allowed roles for device group {GroupType}", allowedRoles.Count, deviceGroupType);
                return allowedRoles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting allowed roles for device group {GroupType}", deviceGroupType);
                return [];
            }
        }

        /// <summary>
        /// Maps a vessel DTO to a vehicle data object.
        /// </summary>
        /// <param name="vesselDto">The vessel DTO to map.</param>
        /// <returns>The mapped vehicle data object.</returns>
        private static VehicleData MapVesselToVehicleData(VesselDto vesselDto)
        {
            // Create a dictionary of properties specific to vessels
            var properties = new Dictionary<string, string>();

            if (!string.IsNullOrEmpty(vesselDto.MMSI))
            {
                properties.Add("MMSI", vesselDto.MMSI);
            }

            if (!string.IsNullOrEmpty(vesselDto.ENI))
            {
                properties.Add("ENI", vesselDto.ENI);
            }

            if (vesselDto.Dimensions != null)
            {
                var length = vesselDto.Dimensions.DistanceGpsToFront + vesselDto.Dimensions.DistanceGpsToBack;
                var width = vesselDto.Dimensions.DistanceGpsToLeft + vesselDto.Dimensions.DistanceGpsToRight;

                properties.Add("Length", $"{length}m");
                properties.Add("Width", $"{width}m");
            }

            // Use cached device count if available, otherwise count devices
            var deviceCount = vesselDto.CachedDeviceCount ?? vesselDto.GetAllDevices().Count;

            return new VehicleData
            {
                Id = vesselDto.Id,
                VehicleId = vesselDto.VehicleId,
                Name = vesselDto.Name,
                VehicleType = VehicleTypeEnum.Vessel, // Hardcoded for now since we're only dealing with vessels
                IconCssClass = "bi bi-water", // Hardcoded icon for vessels
                DeviceCount = deviceCount,
                Properties = properties,
                LastModified = DateTime.Now // Set current date as last modified date
            };
        }
    }
}
