using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups;
using Barret.Shared.DTOs.Devices;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Services
{
    /// <summary>
    /// Interface for the frontend vehicle service.
    /// </summary>
    public interface IVehicleService
    {
        /// <summary>
        /// Determines if a device can connect to an interface device based on their roles and models.
        /// </summary>
        /// <param name="connectedDevice">The device being connected.</param>
        /// <param name="interfaceDevice">The device serving as an interface.</param>
        /// <returns>True if the connection is valid; otherwise, false.</returns>
        bool CanDevicesConnect(DeviceDto connectedDevice, DeviceDto interfaceDevice);

        /// <summary>
        /// Gets devices that can serve as interfaces for a given device.
        /// </summary>
        /// <param name="device">The device to find compatible interfaces for.</param>
        /// <param name="candidates">The candidate devices to check.</param>
        /// <returns>A collection of devices that can serve as interfaces for the given device.</returns>
        IEnumerable<DeviceDto> GetCompatibleInterfaceDevices(DeviceDto device, IEnumerable<DeviceDto> candidates);

        /// <summary>
        /// Gets devices that can connect to a given device as an interface.
        /// </summary>
        /// <param name="interfaceDevice">The device serving as an interface.</param>
        /// <param name="candidates">The candidate devices to check.</param>
        /// <returns>A collection of devices that can connect to the given interface device.</returns>
        IEnumerable<DeviceDto> GetDevicesThatCanConnectToInterface(DeviceDto interfaceDevice, IEnumerable<DeviceDto> candidates);

        /// <summary>
        /// Gets the allowed device roles for a specific device group from the domain model.
        /// </summary>
        /// <param name="deviceGroupType">The device group type.</param>
        /// <returns>A list of allowed device roles for the specified group.</returns>
        List<DeviceRole> GetAllowedDeviceRolesForGroup(DeviceGroups deviceGroupType);
    }
}
