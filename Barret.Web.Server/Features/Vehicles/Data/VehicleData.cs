using System;
using System.Collections.Generic;

namespace Barret.Web.Server.Features.Vehicles.Data
{
    /// <summary>
    /// Data model for vehicles displayed in the UI.
    /// </summary>
    public class VehicleData
    {
        /// <summary>
        /// The unique identifier for the vehicle.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// The vehicle's identifier (business key).
        /// </summary>
        public string VehicleId { get; set; } = string.Empty;

        /// <summary>
        /// The name of the vehicle.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The type of the vehicle.
        /// </summary>
        public VehicleTypeEnum VehicleType { get; set; }

        /// <summary>
        /// The icon CSS class for the vehicle type.
        /// </summary>
        public string IconCssClass { get; set; } = string.Empty;

        /// <summary>
        /// The number of devices installed on this vehicle.
        /// </summary>
        public int DeviceCount { get; set; }

        /// <summary>
        /// Additional properties specific to the vehicle type.
        /// </summary>
        public Dictionary<string, string> Properties { get; set; } = new();

        /// <summary>
        /// The date and time when the vehicle was last modified.
        /// </summary>
        public DateTime? LastModified { get; set; }

        /// <summary>
        /// Gets a formatted string representation of the last modified date.
        /// </summary>
        public string FormattedLastModified => LastModified.HasValue
            ? LastModified.Value.ToString("MMM d, yyyy")
            : "Not available";
    }
}
