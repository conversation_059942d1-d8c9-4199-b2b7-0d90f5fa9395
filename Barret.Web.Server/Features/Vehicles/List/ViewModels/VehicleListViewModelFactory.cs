using Barret.Web.Server.Features.Vehicles.Services;
using Microsoft.Extensions.Logging;

namespace Barret.Web.Server.Features.Vehicles.List.ViewModels
{
    /// <summary>
    /// Factory for creating VehicleListViewModel instances.
    /// </summary>
    public class VehicleListViewModelFactory : IVehicleListViewModelFactory
    {
        private readonly VehicleService _vehicleService;
        private readonly ILogger<VehicleListViewModel> _logger;

        /// <summary>
        /// Initializes a new instance of the VehicleListViewModelFactory class.
        /// </summary>
        /// <param name="vehicleService">The vehicle service.</param>
        /// <param name="logger">The logger to use.</param>
        public VehicleListViewModelFactory(VehicleService vehicleService, ILogger<VehicleListViewModel> logger)
        {
            _vehicleService = vehicleService;
            _logger = logger;
        }

        /// <inheritdoc/>
        public VehicleListViewModel Create()
        {
            return new VehicleListViewModel(_vehicleService, _logger);
        }
    }
}
