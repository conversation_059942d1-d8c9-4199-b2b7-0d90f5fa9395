using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Barret.Web.Server.Features.Shared;
using Barret.Web.Server.Features.Vehicles.Data;
using Barret.Web.Server.Features.Vehicles.Services;
using Microsoft.Extensions.Logging;

namespace Barret.Web.Server.Features.Vehicles.List.ViewModels
{
    /// <summary>
    /// ViewModel for the vehicle list page.
    /// </summary>
    public class VehicleListViewModel : ViewModelBase
    {
        private readonly VehicleService _vehicleService;
        private bool _isLoading;
        private bool _hasError;
        private string _errorMessage = string.Empty;
        private List<VehicleData> _vehicles = new();
        private VehicleFilterData _filter = new();
        private ViewModeEnum _viewMode = ViewModeEnum.Grid;

        /// <summary>
        /// Gets or sets the list of vehicles.
        /// </summary>
        public List<VehicleData> Vehicles
        {
            get => _vehicles;
            private set => SetProperty(ref _vehicles, value);
        }

        /// <summary>
        /// Gets or sets the filter for vehicles.
        /// </summary>
        public VehicleFilterData Filter
        {
            get => _filter;
            set => SetProperty(ref _filter, value);
        }

        /// <summary>
        /// Gets or sets whether the data is loading.
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            private set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets whether an error occurred during data loading.
        /// </summary>
        public bool HasError
        {
            get => _hasError;
            private set => SetProperty(ref _hasError, value);
        }

        /// <summary>
        /// Gets or sets the error message if an error occurred.
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            private set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// Gets or sets the view mode (grid or list).
        /// </summary>
        public ViewModeEnum ViewMode
        {
            get => _viewMode;
            set => SetProperty(ref _viewMode, value);
        }

        /// <summary>
        /// Initializes a new instance of the VehicleListViewModel class.
        /// </summary>
        /// <param name="vehicleService">The vehicle service.</param>
        /// <param name="logger">The logger to use.</param>
        public VehicleListViewModel(VehicleService vehicleService, ILogger<VehicleListViewModel> logger) : base(logger)
        {
            _vehicleService = vehicleService ?? throw new ArgumentNullException(nameof(vehicleService));
        }

        /// <summary>
        /// Loads the vehicles based on the current filter.
        /// </summary>
        public async Task LoadVehiclesAsync()
        {
            try
            {
                // Get a stack trace to help diagnose where this method is being called from
                var stackTrace = Environment.StackTrace;
                var caller = stackTrace.Split(new[] { Environment.NewLine }, StringSplitOptions.None)
                    .Skip(1) // Skip the current method
                    .FirstOrDefault() ?? "Unknown caller";

                // Reset state
                IsLoading = true;
                HasError = false;
                ErrorMessage = string.Empty;

                Logger.LogInformation("Loading vehicles with filter: {VehicleType}, {SearchTerm} at {Timestamp}, called from: {Caller}",
                    Filter.VehicleType, Filter.SearchTerm, DateTime.Now.ToString("HH:mm:ss.fff"), caller);

                // Get all vehicles from the service (lightweight version for list display)
                var result = await _vehicleService.GetAllVehiclesForListAsync();
                if (!result.Success)
                {
                    throw new Exception(result.ErrorMessage);
                }

                // Apply filters
                var filteredVehicles = ApplyFilters(result.Data);

                Vehicles = filteredVehicles;
                Logger.LogInformation("Loaded {Count} vehicles at {Timestamp}",
                    Vehicles.Count, DateTime.Now.ToString("HH:mm:ss.fff"));
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading vehicles");
                HasError = true;
                ErrorMessage = "Failed to load vehicles. Please try again later.";
                Vehicles = new List<VehicleData>(); // Clear vehicles on error
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Applies filters to the vehicle list.
        /// </summary>
        private List<VehicleData> ApplyFilters(List<VehicleData> vehicles)
        {
            Logger.LogDebug("ApplyFilters called with {VehicleCount} vehicles. Filter: VehicleType={VehicleType}, SearchTerm='{SearchTerm}'",
                vehicles.Count, Filter.VehicleType, Filter.SearchTerm ?? "null");

            var filteredVehicles = vehicles;

            // Filter by vehicle type
            if (Filter.VehicleType.HasValue && Filter.VehicleType.Value != VehicleTypeEnum.All)
            {
                Logger.LogDebug("Filtering by vehicle type: {VehicleType}", Filter.VehicleType.Value);
                var beforeCount = filteredVehicles.Count;
                filteredVehicles = filteredVehicles
                    .Where(v => v.VehicleType == Filter.VehicleType.Value)
                    .ToList();
                Logger.LogDebug("Vehicle type filter applied: {BeforeCount} -> {AfterCount} vehicles", beforeCount, filteredVehicles.Count);
            }
            else
            {
                Logger.LogDebug("No vehicle type filter applied (showing all vehicle types)");
            }

            // Filter by search term
            if (!string.IsNullOrEmpty(Filter.SearchTerm))
            {
                Logger.LogDebug("Filtering by search term: '{SearchTerm}'", Filter.SearchTerm);
                var beforeCount = filteredVehicles.Count;
                filteredVehicles = filteredVehicles
                    .Where(v =>
                        v.Name.Contains(Filter.SearchTerm, StringComparison.OrdinalIgnoreCase) ||
                        v.VehicleId.Contains(Filter.SearchTerm, StringComparison.OrdinalIgnoreCase))
                    .ToList();
                Logger.LogDebug("Search term filter applied: {BeforeCount} -> {AfterCount} vehicles", beforeCount, filteredVehicles.Count);
            }

            Logger.LogDebug("ApplyFilters returning {FilteredCount} vehicles", filteredVehicles.Count);
            return filteredVehicles;
        }

        /// <summary>
        /// Handles deleting a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle to delete.</param>
        /// <returns>True if the operation was successful, false otherwise.</returns>
        public async Task<bool> DeleteVehicleAsync(Guid vehicleId)
        {
            try
            {
                Logger.LogInformation("Deleting vehicle with ID: {VehicleId}", vehicleId);

                // Delete the vehicle using the service
                var result = await _vehicleService.DeleteVehicleAsync(vehicleId);
                if (!result.Success)
                {
                    Logger.LogWarning("Failed to delete vehicle: {ErrorMessage}", result.ErrorMessage);
                    HasError = true;
                    ErrorMessage = result.ErrorMessage;
                    return false;
                }

                // Remove the vehicle from the local list
                var removed = Vehicles.RemoveAll(v => v.Id == vehicleId);
                if (removed == 0)
                {
                    Logger.LogWarning("Vehicle with ID {VehicleId} not found in local list", vehicleId);
                }

                // Notify UI of changes
                OnPropertyChanged(nameof(Vehicles));

                Logger.LogInformation("Vehicle deleted successfully");
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error deleting vehicle");
                HasError = true;
                ErrorMessage = "Failed to delete vehicle. Please try again later.";
                return false;
            }
        }

        /// <summary>
        /// Handles copying a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle to copy.</param>
        /// <param name="newName">The name for the copied vehicle.</param>
        /// <returns>True if the operation was successful, false otherwise.</returns>
        public async Task<bool> CopyVehicleAsync(Guid vehicleId, string newName)
        {
            try
            {
                Logger.LogInformation("Copying vehicle with ID: {VehicleId} with new name: {NewName}", vehicleId, newName);

                // Find the vehicle to copy in the local list to verify it exists
                var vehicleToCopy = Vehicles.FirstOrDefault(v => v.Id == vehicleId);
                if (vehicleToCopy == null)
                {
                    Logger.LogWarning("Vehicle with ID {VehicleId} not found in local list", vehicleId);
                    HasError = true;
                    ErrorMessage = "Vehicle not found.";
                    return false;
                }

                // Copy the vehicle using the service
                var result = await _vehicleService.CopyVehicleAsync(vehicleId, newName);
                if (!result.Success)
                {
                    Logger.LogWarning("Failed to copy vehicle: {ErrorMessage}", result.ErrorMessage);
                    HasError = true;
                    ErrorMessage = result.ErrorMessage;
                    return false;
                }

                // Reload the vehicles to include the new copy
                await LoadVehiclesAsync();

                Logger.LogInformation("Vehicle copied successfully with new ID: {NewVehicleId}", result.Data);
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error copying vehicle");
                HasError = true;
                ErrorMessage = "Failed to copy vehicle. Please try again later.";
                return false;
            }
        }

        /// <summary>
        /// Clears any error state.
        /// </summary>
        public void ClearError()
        {
            HasError = false;
            ErrorMessage = string.Empty;
        }
    }
}
