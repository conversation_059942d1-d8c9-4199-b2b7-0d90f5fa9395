using Microsoft.AspNetCore.Components;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.List.Components
{
    /// <summary>
    /// Code-behind for the CustomTabControl component.
    /// </summary>
    public partial class CustomTabControl : ComponentBase
    {
        /// <summary>
        /// Gets or sets the active tab index.
        /// </summary>
        [Parameter]
        public int ActiveTabIndex { get; set; }

        /// <summary>
        /// Gets or sets the callback for when the active tab index changes.
        /// </summary>
        [Parameter]
        public EventCallback<int> ActiveTabIndexChanged { get; set; }

        /// <summary>
        /// Gets or sets the callback for when a tab is clicked.
        /// </summary>
        [Parameter]
        public EventCallback<int> OnTabClicked { get; set; }

        /// <summary>
        /// Gets the CSS classes for a tab based on whether it's active.
        /// </summary>
        /// <param name="index">The tab index.</param>
        /// <returns>The CSS classes for the tab.</returns>
        protected string GetTabClasses(int index) => ActiveTabIndex == index
            ? "inline-flex items-center gap-2 px-6 py-3 border-b-2 border-gray-900 text-gray-900 font-medium relative focus:outline-none"
            : "inline-flex items-center gap-2 px-6 py-3 border-b-2 border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 font-medium transition-colors duration-200 focus:outline-none";

        /// <summary>
        /// Handles the tab click event.
        /// </summary>
        /// <param name="index">The tab index that was clicked.</param>
        protected async Task OnTabClick(int index)
        {
            if (ActiveTabIndex != index)
            {
                ActiveTabIndex = index;
                await ActiveTabIndexChanged.InvokeAsync(index);
                await OnTabClicked.InvokeAsync(index);
            }
        }
    }
}
