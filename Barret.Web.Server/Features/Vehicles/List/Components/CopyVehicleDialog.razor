@using <PERSON><PERSON><PERSON>
@using Ra<PERSON>zen.Blazor
@inject Radzen.DialogService DialogService

@* Copy Vehicle Form Dialog Component *@

<div class="barret-form-dialog copy-vehicle-dialog">
    <div class="barret-dialog-content">
        <div class="barret-dialog-header">
            <div class="flex items-center">
                <i class="bi bi-copy text-blue-600 mr-2 text-xl"></i>
                <span class="text-lg font-semibold text-gray-900">Copy Vehicle</span>
            </div>
        </div>

        <div class="barret-dialog-body">
            <div class="p-4">
                <div class="text-center mb-4">
                    <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-2">
                        <i class="bi bi-copy text-2xl text-gray-700"></i>
                    </div>
                </div>
                <p class="text-gray-700 mb-4">Please enter a name for the copied vehicle:</p>
                <div class="mb-4">
                    <label for="vehicleName" class="block text-sm font-medium text-gray-700 mb-1">Vehicle Name</label>
                    <RadzenTextBox Value="@VehicleName"
                                  ValueChanged="@((value) => VehicleName = value)"
                                  Placeholder="Enter vehicle name"
                                  class="barret-input" />
                </div>
            </div>
        </div>

        <div class="barret-dialog-footer">
            <div class="barret-dialog-btn-group-right">
                <RadzenButton Text="Cancel"
                             Icon="cancel"
                             ButtonStyle="ButtonStyle.Secondary"
                             Click="@CancelAsync"
                             class="barret-btn barret-form-btn" />
                <RadzenButton Text="Copy"
                             Icon="content_copy"
                             ButtonStyle="ButtonStyle.Primary"
                             Click="@CopyAsync"
                             Disabled="@(string.IsNullOrWhiteSpace(VehicleName) || IsProcessing)"
                             class="barret-btn barret-form-btn">
                    @if (IsProcessing)
                    {
                        <i class="bi bi-arrow-clockwise animate-spin mr-2"></i>
                    }
                    Copy
                </RadzenButton>
            </div>
        </div>
    </div>
</div>
