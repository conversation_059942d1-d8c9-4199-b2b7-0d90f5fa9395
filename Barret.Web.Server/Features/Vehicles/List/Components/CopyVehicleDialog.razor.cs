using Microsoft.AspNetCore.Components;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.List.Components
{
    /// <summary>
    /// Code-behind for the CopyVehicleDialog component.
    /// Service-based dialog component for copying vehicles.
    /// </summary>
    public partial class CopyVehicleDialog : ComponentBase
    {
        /// <summary>
        /// Gets or sets the name of the original vehicle being copied.
        /// </summary>
        [Parameter]
        public string OriginalName { get; set; } = string.Empty;

        private string vehicleName = "";
        private bool isProcessing = false;

        /// <summary>
        /// Gets or sets the name for the new vehicle.
        /// </summary>
        protected string VehicleName
        {
            get => vehicleName;
            set => vehicleName = value;
        }

        /// <summary>
        /// Gets whether the dialog is currently processing.
        /// </summary>
        protected bool IsProcessing => isProcessing;

        /// <summary>
        /// Method called when the component parameters are set.
        /// </summary>
        protected override void OnParametersSet()
        {
            if (!string.IsNullOrEmpty(OriginalName) && string.IsNullOrEmpty(vehicleName))
            {
                // Set default name when dialog is opened
                vehicleName = $"{OriginalName} (Copy)";
            }
        }

        /// <summary>
        /// Handles the copy button click.
        /// </summary>
        protected async Task CopyAsync()
        {
            try
            {
                isProcessing = true;

                if (!string.IsNullOrWhiteSpace(vehicleName))
                {
                    var result = new CopyVehicleResult
                    {
                        NewVehicleName = vehicleName.Trim(),
                        OriginalName = OriginalName
                    };

                    DialogService.Close(result);
                }
            }
            finally
            {
                isProcessing = false;
            }
        }

        /// <summary>
        /// Handles the cancel button click.
        /// </summary>
        protected async Task CancelAsync()
        {
            DialogService.Close(null);
        }

        /// <summary>
        /// Result class for copy vehicle dialog.
        /// </summary>
        public class CopyVehicleResult
        {
            public string NewVehicleName { get; set; } = "";
            public string OriginalName { get; set; } = "";
        }
    }
}
