@using Microsoft.AspNetCore.Components.Web

<div class="w-full border-b border-gray-100 mb-8">
    <div class="flex overflow-x-auto hide-scrollbar">
        <button
            @onclick="() => OnTabClick(0)"
            class="@GetTabClasses(0)"
            aria-current="@(ActiveTabIndex == 0 ? "page" : null)">
            <div class="inline-flex items-center gap-2">
                All Vehicles
            </div>
        </button>

        <button
            @onclick="() => OnTabClick(1)"
            class="@GetTabClasses(1)"
            aria-current="@(ActiveTabIndex == 1 ? "page" : null)">
            <div class="inline-flex items-center gap-2">
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21c-3.5 0-7-1.5-7-1.5s-3.5 1.5-7 1.5c-2 0-3.5-.5-3.5-.5L2 12l.5-1C3 11 4.5 10 7 10c3.5 0 7 1.5 7 1.5s3.5-1.5 7-1.5c2.5 0 4 1 4.5 1l.5 1-1 9c-.5 0-2 .5-4 .5Z"></path>
                    <path d="M12 10V3l-3 2"></path>
                </svg>
                Vessels
            </div>
        </button>

        <button
            @onclick="() => OnTabClick(2)"
            class="@GetTabClasses(2)"
            aria-current="@(ActiveTabIndex == 2 ? "page" : null)">
            <div class="inline-flex items-center gap-2">
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M10 17h4V5H2v12h3"></path>
                    <path d="M20 17h2v-3.34a4 4 0 0 0-1.17-2.83L19 9h-5v8h1"></path>
                    <circle cx="7.5" cy="17.5" r="2.5"></circle>
                    <circle cx="17.5" cy="17.5" r="2.5"></circle>
                </svg>
                Trucks
            </div>
        </button>

        <button
            @onclick="() => OnTabClick(3)"
            class="@GetTabClasses(3)"
            aria-current="@(ActiveTabIndex == 3 ? "page" : null)">
            <div class="inline-flex items-center gap-2">
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 16V9h-8V4H8v12h12z"></path>
                    <path d="M18 16v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6"></path>
                </svg>
                Cranes
            </div>
        </button>
    </div>
</div>
