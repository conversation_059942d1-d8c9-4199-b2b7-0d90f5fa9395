using Barret.Web.Server.Features.Vehicles.Data;
using Microsoft.AspNetCore.Components;
using System;

namespace Barret.Web.Server.Features.Vehicles.List.Components
{
    /// <summary>
    /// Code-behind for the VehicleCard component.
    /// </summary>
    public partial class VehicleCard : ComponentBase
    {
        /// <summary>
        /// Gets or sets the vehicle data to display.
        /// </summary>
        [Parameter, EditorRequired]
        public VehicleData Vehicle { get; set; } = default!;

        /// <summary>
        /// Gets or sets the callback for when the configure button is clicked.
        /// </summary>
        [Parameter]
        public EventCallback<Guid> OnConfigureClick { get; set; }

        /// <summary>
        /// Gets or sets the callback for when the copy button is clicked.
        /// </summary>
        [Parameter]
        public EventCallback<Guid> OnCopyClick { get; set; }

        /// <summary>
        /// Gets or sets the callback for when the delete button is clicked.
        /// </summary>
        [Parameter]
        public EventCallback<Guid> OnDeleteClick { get; set; }
    }
}
