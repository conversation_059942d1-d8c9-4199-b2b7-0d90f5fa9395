using System.Collections.ObjectModel;
using System.Collections.Specialized;
using System.Reactive;
using System.Reactive.Linq;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Core.Areas.Vehicles;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Shared.Factories;
using Barret.Shared.Results;
using Barret.Web.Server.Features.Vehicles.Editor.Data;
using Microsoft.Extensions.Logging;
using ReactiveUI;

namespace Barret.Web.Server.Features.Vehicles.Editor.ViewModels
{
    /// <summary>
    /// ViewModel for the vehicle editor.
    /// </summary>
    public class VehicleEditorViewModel : ReactiveObject, IDisposable
    {
        private readonly IVehicleService<Vessel, VesselDto> _vesselService;
        private readonly ILogger<VehicleEditorViewModel> _logger;
        private readonly List<IDisposable> _subscriptions = new();

        private VesselDto? _vessel;
        private bool _isLoading;
        private bool _isSaving;
        private bool _isDirty;
        private bool _isNewVessel;
        private TabIdentifier _activeTab = TabIdentifier.ForBasicInfo();
        private DeviceGroups? _activeDeviceGroupType;

        /// <summary>
        /// Gets or sets the vessel being edited.
        /// </summary>
        public VesselDto? Vessel
        {
            get => _vessel;
            private set => this.RaiseAndSetIfChanged(ref _vessel, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the editor is in loading state.
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            private set => this.RaiseAndSetIfChanged(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the editor is in saving state.
        /// </summary>
        public bool IsSaving
        {
            get => _isSaving;
            private set => this.RaiseAndSetIfChanged(ref _isSaving, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the editor has unsaved changes.
        /// </summary>
        public bool IsDirty
        {
            get => _isDirty;
            private set => this.RaiseAndSetIfChanged(ref _isDirty, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether this is a new vessel.
        /// </summary>
        public bool IsNewVessel
        {
            get => _isNewVessel;
            private set => this.RaiseAndSetIfChanged(ref _isNewVessel, value);
        }

        /// <summary>
        /// Gets or sets the active tab.
        /// </summary>
        public TabIdentifier ActiveTab
        {
            get => _activeTab;
            set
            {
                this.RaiseAndSetIfChanged(ref _activeTab, value);

                // Update the active device group type
                ActiveDeviceGroupType = value.DeviceGroupType;
            }
        }

        /// <summary>
        /// Gets or sets the active device group type.
        /// </summary>
        public DeviceGroups? ActiveDeviceGroupType
        {
            get => _activeDeviceGroupType;
            private set => this.RaiseAndSetIfChanged(ref _activeDeviceGroupType, value);
        }

        /// <summary>
        /// Gets the observable collections for devices in each group.
        /// </summary>
        public Dictionary<DeviceGroups, ObservableCollection<DeviceDto>> DeviceCollections { get; } = new();

        /// <summary>
        /// Gets the command to save the vessel.
        /// </summary>
        public ReactiveCommand<Unit, Unit> SaveCommand { get; }

        /// <summary>
        /// Gets the command to add a device to the vessel.
        /// </summary>
        public ReactiveCommand<(DeviceDto Device, DeviceGroups GroupType), Unit> AddDeviceCommand { get; }

        /// <summary>
        /// Gets the command to remove a device from the vessel.
        /// </summary>
        public ReactiveCommand<(Guid DeviceId, DeviceGroups GroupType), Unit> RemoveDeviceCommand { get; }

        /// <summary>
        /// Gets the command to update a device in the vessel.
        /// </summary>
        public ReactiveCommand<(DeviceDto Device, DeviceGroups GroupType), Unit> UpdateDeviceCommand { get; }

        /// <summary>
        /// Gets the command to import devices to the vessel.
        /// </summary>
        public ReactiveCommand<List<DeviceDto>, Unit> ImportDevicesCommand { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="VehicleEditorViewModel"/> class.
        /// </summary>
        /// <param name="vesselService">The vessel service.</param>
        /// <param name="logger">The logger.</param>
        public VehicleEditorViewModel(
            IVehicleService<Vessel, VesselDto> vesselService,
            ILogger<VehicleEditorViewModel> logger)
        {
            _vesselService = vesselService;
            _logger = logger;

            // Initialize commands
            SaveCommand = ReactiveCommand.CreateFromTask(
                SaveVesselAsync,
                this.WhenAnyValue(
                    x => x.IsNewVessel,
                    x => x.IsDirty,
                    x => x.IsSaving,
                    (isNewVessel, isDirty, isSaving) => (isNewVessel || isDirty) && !isSaving));

            AddDeviceCommand = ReactiveCommand.Create<(DeviceDto Device, DeviceGroups GroupType)>(
                AddDevice,
                this.WhenAnyValue(x => x.Vessel).Select(vessel => vessel != null));

            RemoveDeviceCommand = ReactiveCommand.Create<(Guid DeviceId, DeviceGroups GroupType)>(
                RemoveDevice,
                this.WhenAnyValue(x => x.Vessel).Select(vessel => vessel != null));

            UpdateDeviceCommand = ReactiveCommand.Create<(DeviceDto Device, DeviceGroups GroupType)>(
                UpdateDevice,
                this.WhenAnyValue(x => x.Vessel).Select(vessel => vessel != null));

            ImportDevicesCommand = ReactiveCommand.Create<List<DeviceDto>>(
                ImportDevices,
                this.WhenAnyValue(x => x.Vessel).Select(vessel => vessel != null));
        }

        /// <summary>
        /// Initializes the view model with the specified vessel ID.
        /// </summary>
        /// <param name="vesselId">The vessel ID, or null to create a new vessel.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        public async Task InitializeAsync(Guid? vesselId)
        {
            try
            {
                IsLoading = true;

                // Clear any previous state to prevent accumulation during navigation
                ClearPreviousState();

                // Load or create the vessel
                if (vesselId.HasValue)
                {
                    _logger.LogInformation("Initializing VehicleEditorViewModel for existing vessel {VesselId}", vesselId.Value);

                    var result = await _vesselService.GetByIdAsync(vesselId.Value);
                    if (result.Success)
                    {
                        Vessel = result.Data;
                        IsNewVessel = false; // This is an existing vessel

                        // Log device loading details for debugging navigation issues
                        var totalDevices = Vessel.GetAllDevices().Count;
                        var devicesByGroup = Vessel.DeviceGroups.ToDictionary(
                            kvp => kvp.Key.ToString(),
                            kvp => kvp.Value.Devices.Count);

                        _logger.LogInformation("Loaded vessel {VesselName} with ID {VesselId} containing {DeviceCount} devices. Groups: {DevicesByGroup}",
                            Vessel?.Name, vesselId, totalDevices, string.Join(", ", devicesByGroup.Select(kvp => $"{kvp.Key}:{kvp.Value}")));
                    }
                    else
                    {
                        _logger.LogError("Failed to load vessel with ID {VesselId}: {ErrorMessage}",
                            vesselId, result.ErrorMessage);
                        throw new InvalidOperationException($"Failed to load vessel: {result.ErrorMessage}");
                    }
                }
                else
                {
                    _logger.LogInformation("Initializing VehicleEditorViewModel for new vessel");

                    Vessel = new VesselDto
                    {
                        Id = Guid.NewGuid(),
                        VehicleId = $"V000000",
                        Name = "New Vessel",
                        MMSI = "000000000",
                        DeviceGroups = new Dictionary<DeviceGroups, DeviceGroupDto>(),
                        Dimensions = new Barret.Shared.DTOs.Vehicles.DimensionsDto
                        {
                            DistanceGpsToFront = 10,
                            DistanceGpsToBack = 10,
                            DistanceGpsToLeft = 5,
                            DistanceGpsToRight = 5
                        }
                    };

                    // Mark as a new vessel
                    IsNewVessel = true;

                    // Initialize default device groups
                    InitializeDefaultDeviceGroups();

                    _logger.LogInformation("Created new vessel with ID {VesselId}", Vessel.Id);
                }

                // Initialize observable collections
                InitializeObservableCollections();

                // Validate vessel state to detect any issues early
                ValidateVesselState();

                // Set the active tab and group
                ActiveTab = TabIdentifier.ForBasicInfo();

                // Mark as clean since we just loaded
                MarkAsClean();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing vessel editor");
                throw;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Marks the editor as dirty.
        /// </summary>
        public void MarkAsDirty()
        {
            IsDirty = true;
        }

        /// <summary>
        /// Marks the editor as clean.
        /// </summary>
        public void MarkAsClean()
        {
            IsDirty = false;
        }

        /// <summary>
        /// Clears previous state to prevent accumulation during navigation.
        /// </summary>
        private void ClearPreviousState()
        {
            var previousVesselId = _vessel?.Id;
            var previousDeviceCount = DeviceCollections.Values.Sum(c => c.Count);

            // Dispose of any existing subscriptions
            foreach (var subscription in _subscriptions)
            {
                subscription.Dispose();
            }
            _subscriptions.Clear();

            // Clear device collections
            foreach (var collection in DeviceCollections.Values)
            {
                collection.Clear();
            }
            DeviceCollections.Clear();

            // Reset vessel reference
            _vessel = null;

            // Reset state flags
            _isDirty = false;
            _isNewVessel = false;
            _activeTab = TabIdentifier.ForBasicInfo();
            _activeDeviceGroupType = null;

            _logger.LogDebug("Cleared previous ViewModel state for navigation. Previous vessel: {PreviousVesselId}, Previous device count: {PreviousDeviceCount}",
                previousVesselId, previousDeviceCount);
        }

        /// <summary>
        /// Initializes default device groups for a new vessel.
        /// </summary>
        private void InitializeDefaultDeviceGroups()
        {
            if (Vessel == null) return;

            // Initialize standard device groups
            VehicleDTOFactory.InitializeDeviceGroups(Vessel);
        }

        /// <summary>
        /// Initializes observable collections for each device group.
        /// </summary>
        private void InitializeObservableCollections()
        {
            if (Vessel == null) return;

            _logger.LogDebug("Initializing observable collections for vessel {VesselId}", Vessel.Id);

            // Clear existing collections first
            DeviceCollections.Clear();

            // Track total devices and duplicates for logging
            var totalDevicesInDto = 0;
            var totalDevicesInCollections = 0;
            var totalDuplicatesSkipped = 0;

            // Create a new collection for each device group
            foreach (var group in Vessel.DeviceGroups)
            {
                // Create a new collection with a copy of the devices to prevent reference issues
                var collection = new ObservableCollection<DeviceDto>();

                // Check for duplicate devices before adding them to the collection
                var uniqueDeviceIds = new HashSet<Guid>();
                var duplicatesInGroup = 0;

                totalDevicesInDto += group.Value.Devices.Count;

                foreach (var device in group.Value.Devices)
                {
                    // Only add devices with valid IDs and no duplicates
                    if (device.Id != Guid.Empty && uniqueDeviceIds.Add(device.Id))
                    {
                        collection.Add(device);
                        totalDevicesInCollections++;
                    }
                    else if (device.Id == Guid.Empty)
                    {
                        // For new devices without IDs, always add them
                        collection.Add(device);
                        totalDevicesInCollections++;
                    }
                    else
                    {
                        duplicatesInGroup++;
                        totalDuplicatesSkipped++;
                        _logger.LogWarning("Skipping duplicate device with ID {DeviceId} in group {GroupName}",
                            device.Id, group.Key);
                    }
                }

                // Store the collection
                DeviceCollections[group.Key] = collection;

                // Create a named event handler for this collection that we can properly unsubscribe later
                NotifyCollectionChangedEventHandler handler = (sender, e) => SyncCollectionToDto(group.Key);

                // Set up synchronization with the named handler
                collection.CollectionChanged += handler;

                // Add a subscription to clean up when the ViewModel is disposed
                _subscriptions.Add(new DisposableAction(() => collection.CollectionChanged -= handler));

                _logger.LogDebug("Initialized collection for group {GroupName} with {DeviceCount} devices (skipped {DuplicateCount} duplicates)",
                    group.Key, collection.Count, duplicatesInGroup);
            }

            // Log summary for debugging navigation issues
            _logger.LogInformation("Observable collections initialized for vessel {VesselId}. DTO devices: {DtoDevices}, Collection devices: {CollectionDevices}, Duplicates skipped: {DuplicatesSkipped}",
                Vessel.Id, totalDevicesInDto, totalDevicesInCollections, totalDuplicatesSkipped);

            if (totalDuplicatesSkipped > 0)
            {
                _logger.LogWarning("Device duplication detected during observable collection initialization for vessel {VesselId}. This may indicate a navigation or mapping issue.",
                    Vessel.Id);
            }
        }

        /// <summary>
        /// Synchronizes the observable collection to the DTO.
        /// </summary>
        /// <param name="groupType">The group type.</param>
        private void SyncCollectionToDto(DeviceGroups groupType)
        {
            if (Vessel == null || !DeviceCollections.TryGetValue(groupType, out var collection)) return;

            if (Vessel.DeviceGroups.TryGetValue(groupType, out var group))
            {
                // Check for duplicate devices before syncing to the DTO
                var uniqueDevices = new List<DeviceDto>();
                var uniqueDeviceIds = new HashSet<Guid>();

                foreach (var device in collection)
                {
                    // Only add devices with valid IDs and no duplicates
                    if (device.Id != Guid.Empty)
                    {
                        if (uniqueDeviceIds.Add(device.Id))
                        {
                            uniqueDevices.Add(device);
                        }
                        else
                        {
                            _logger.LogWarning("Skipping duplicate device with ID {DeviceId} during sync for group {GroupType}",
                                device.Id, groupType);
                        }
                    }
                    else
                    {
                        // For new devices without IDs, always add them
                        uniqueDevices.Add(device);
                    }
                }

                // Replace the devices list with our deduplicated list
                group.Devices = uniqueDevices;

                _logger.LogDebug("Synced {DeviceCount} devices to group {GroupType}",
                    uniqueDevices.Count, groupType);

                MarkAsDirty();
            }
        }

        /// <summary>
        /// Adds a device to the vessel.
        /// </summary>
        /// <param name="parameters">The device and group type.</param>
        private void AddDevice((DeviceDto Device, DeviceGroups GroupType) parameters)
        {
            if (Vessel == null) return;

            var (device, groupType) = parameters;

            if (Vessel.DeviceGroups.TryGetValue(groupType, out var group))
            {
                // Set the vehicle ID
                device.VehicleId = Vessel.Id;
                device.DeviceGroupType = groupType;

                // Add to the group
                group.Devices.Add(device);

                // Update the observable collection
                if (DeviceCollections.TryGetValue(groupType, out var collection))
                {
                    collection.Add(device);
                }

                // If the device has connections, ensure they are also added to the vessel's DeviceConnections list
                if (device.Connections != null && device.Connections.Any())
                {
                    _logger.LogInformation("Syncing {ConnectionCount} connections from device to vessel", device.Connections.Count);

                    foreach (var connection in device.Connections)
                    {
                        // Check if the connection already exists in the vessel
                        if (!Vessel.DeviceConnections.Any(c =>
                            c.ConnectedDeviceId == connection.ConnectedDeviceId &&
                            c.InterfaceDeviceId == connection.InterfaceDeviceId))
                        {
                            // Add the connection to the vessel
                            Vessel.DeviceConnections.Add(connection);
                            _logger.LogDebug("Added connection from {ConnectedId} to {InterfaceId} to vessel",
                                connection.ConnectedDeviceId, connection.InterfaceDeviceId);
                        }
                    }
                }

                MarkAsDirty();
                _logger.LogInformation("Added device {DeviceName} to group {GroupType}", device.Name, groupType);
            }
        }

        /// <summary>
        /// Removes a device from the vessel.
        /// </summary>
        /// <param name="parameters">The device ID and group type.</param>
        private void RemoveDevice((Guid DeviceId, DeviceGroups GroupType) parameters)
        {
            if (Vessel == null) return;

            var (deviceId, groupType) = parameters;

            if (Vessel.DeviceGroups.TryGetValue(groupType, out var group))
            {
                // Find the device
                var device = group.Devices.FirstOrDefault(d => d.Id == deviceId);
                if (device != null)
                {
                    // Remove from the group
                    group.Devices.Remove(device);

                    // Update the observable collection
                    if (DeviceCollections.TryGetValue(groupType, out var collection))
                    {
                        var collectionDevice = collection.FirstOrDefault(d => d.Id == deviceId);
                        if (collectionDevice != null)
                        {
                            collection.Remove(collectionDevice);
                        }
                    }

                    MarkAsDirty();
                    _logger.LogInformation("Removed device {DeviceName} from group {GroupType}", device.Name, groupType);
                }
            }
        }

        /// <summary>
        /// Updates a device in the vessel.
        /// </summary>
        /// <param name="parameters">The device and group type.</param>
        private void UpdateDevice((DeviceDto Device, DeviceGroups GroupType) parameters)
        {
            if (Vessel == null) return;

            var (device, groupType) = parameters;

            if (Vessel.DeviceGroups.TryGetValue(groupType, out var group))
            {
                // Find the device
                var existingDevice = group.Devices.FirstOrDefault(d => d.Id == device.Id);
                if (existingDevice != null)
                {
                    // Update the device
                    var index = group.Devices.IndexOf(existingDevice);
                    group.Devices[index] = device;

                    // Update the observable collection
                    if (DeviceCollections.TryGetValue(groupType, out var collection))
                    {
                        var collectionDevice = collection.FirstOrDefault(d => d.Id == device.Id);
                        if (collectionDevice != null)
                        {
                            var collectionIndex = collection.IndexOf(collectionDevice);
                            collection[collectionIndex] = device;
                        }
                    }

                    // Sync device connections with vessel
                    if (device.Connections != null)
                    {
                        // First, remove any existing connections for this device from the vessel
                        var connectionsToRemove = Vessel.DeviceConnections
                            .Where(c => c.ConnectedDeviceId == device.Id)
                            .ToList();

                        foreach (var connection in connectionsToRemove)
                        {
                            Vessel.DeviceConnections.Remove(connection);
                            _logger.LogDebug("Removed connection from {ConnectedId} to {InterfaceId} from vessel",
                                connection.ConnectedDeviceId, connection.InterfaceDeviceId);
                        }

                        // Then add the current connections
                        foreach (var connection in device.Connections)
                        {
                            // Check if the connection already exists in the vessel
                            if (!Vessel.DeviceConnections.Any(c =>
                                c.ConnectedDeviceId == connection.ConnectedDeviceId &&
                                c.InterfaceDeviceId == connection.InterfaceDeviceId))
                            {
                                // Add the connection to the vessel
                                Vessel.DeviceConnections.Add(connection);
                                _logger.LogDebug("Added connection from {ConnectedId} to {InterfaceId} to vessel",
                                    connection.ConnectedDeviceId, connection.InterfaceDeviceId);
                            }
                        }
                    }

                    MarkAsDirty();
                    _logger.LogInformation("Updated device {DeviceName} in group {GroupType}", device.Name, groupType);
                }
            }
        }

        /// <summary>
        /// Saves the vessel.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task SaveVesselAsync()
        {
            if (Vessel == null) return;

            try
            {
                IsSaving = true;

                // Ensure all collections are synced to the DTO
                foreach (var groupType in Vessel.DeviceGroups.Keys)
                {
                    SyncCollectionToDto(groupType);
                }

                // Save the vessel
                ServiceResult<VesselDto> result;

                // Check if this is a new vessel (not yet saved to the database)
                if (IsNewVessel)
                {
                    // Create a new vessel
                    var createResult = await _vesselService.CreateAsync(Vessel);
                    if (createResult.Success)
                    {
                        // Get the newly created vessel
                        result = await _vesselService.GetByIdAsync(createResult.Data);

                        // Mark as no longer a new vessel
                        IsNewVessel = false;
                    }
                    else
                    {
                        // Return a failure result with the error message
                        result = ServiceResult<VesselDto>.CreateFailure(createResult.ErrorMessage);
                    }
                }
                else
                {
                    // Update existing vessel
                    result = await _vesselService.UpdateAsync(Vessel.Id, Vessel);
                }

                if (result.Success)
                {
                    // Update the vessel with any changes from the server
                    Vessel = result.Data;

                    // Mark as clean
                    MarkAsClean();

                    _logger.LogInformation("Saved vessel {VesselName} with ID {VesselId}", Vessel.Name, Vessel.Id);
                }
                else
                {
                    _logger.LogError("Failed to save vessel {VesselName} with ID {VesselId}: {ErrorMessage}",
                        Vessel.Name, Vessel.Id, result.ErrorMessage);
                    throw new InvalidOperationException($"Failed to save vessel: {result.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving vessel {VesselName} with ID {VesselId}", Vessel?.Name, Vessel?.Id);
                throw;
            }
            finally
            {
                IsSaving = false;
            }
        }

        /// <summary>
        /// Imports devices to the vessel.
        /// </summary>
        /// <param name="devices">The devices to import.</param>
        private void ImportDevices(List<DeviceDto> devices)
        {
            if (Vessel == null || devices == null || !devices.Any()) return;

            _logger.LogInformation("Importing {Count} devices to vessel {VesselName}", devices.Count, Vessel.Name);

            foreach (var device in devices)
            {
                // The devices are already copied by the backend, so we just need to add them to the appropriate group

                // Determine the appropriate group for this device
                DeviceGroups? targetGroupType = null;

                // Use the device group type directly
                if (device.DeviceGroupType.HasValue)
                {
                    targetGroupType = device.DeviceGroupType.Value;
                }

                // If the group doesn't exist in the current vessel, find an appropriate one
                if (!targetGroupType.HasValue || !Vessel.DeviceGroups.ContainsKey(targetGroupType.Value))
                {
                    // Find a group that allows this device role
                    targetGroupType = Vessel.DeviceGroups
                        .FirstOrDefault(g => g.Value.AllowedRoles.Contains(device.DeviceRole))
                        .Key;

                    // If no suitable group found, use the first group
                    if (!targetGroupType.HasValue && Vessel.DeviceGroups.Any())
                    {
                        targetGroupType = Vessel.DeviceGroups.First().Key;
                    }
                }

                // Add the device to the appropriate group
                if (targetGroupType.HasValue)
                {
                    device.DeviceGroupType = targetGroupType.Value;
                    AddDevice((device, targetGroupType.Value));
                    _logger.LogInformation("Imported device {DeviceName} to group {GroupType}", device.Name, targetGroupType.Value);
                }
                else
                {
                    _logger.LogWarning("Could not find a suitable group for device {DeviceName}", device.Name);
                }
            }

            MarkAsDirty();
        }

        /// <summary>
        /// Validates vessel state to detect potential device duplication issues.
        /// </summary>
        /// <returns>True if vessel state is valid; otherwise, false.</returns>
        private bool ValidateVesselState()
        {
            if (Vessel == null) return true;

            var allDeviceIds = Vessel.GetAllDevices()
                .Select(d => d.Id)
                .Where(id => id != Guid.Empty)
                .ToList();

            var uniqueDeviceIds = allDeviceIds.Distinct().ToList();
            var hasDuplicates = allDeviceIds.Count != uniqueDeviceIds.Count;

            if (hasDuplicates)
            {
                var duplicateCount = allDeviceIds.Count - uniqueDeviceIds.Count;
                _logger.LogError("Vessel state validation failed for {VesselId}. Found {DuplicateCount} duplicate devices. Total: {Total}, Unique: {Unique}",
                    Vessel.Id, duplicateCount, allDeviceIds.Count, uniqueDeviceIds.Count);

                // Log details about duplicates
                var duplicateIds = allDeviceIds.GroupBy(id => id)
                    .Where(g => g.Count() > 1)
                    .Select(g => new { Id = g.Key, Count = g.Count() })
                    .ToList();

                foreach (var duplicate in duplicateIds)
                {
                    _logger.LogError("Device {DeviceId} appears {Count} times in vessel {VesselId}",
                        duplicate.Id, duplicate.Count, Vessel.Id);
                }

                return false;
            }

            _logger.LogDebug("Vessel state validation passed for {VesselId}. {DeviceCount} unique devices found.",
                Vessel.Id, uniqueDeviceIds.Count);

            return true;
        }

        /// <summary>
        /// Disposes the view model and cleans up all resources.
        /// </summary>
        public void Dispose()
        {
            _logger.LogDebug("Disposing VehicleEditorViewModel");

            // Dispose all subscriptions
            foreach (var subscription in _subscriptions)
            {
                subscription.Dispose();
            }
            _subscriptions.Clear();

            // Clear all collections to prevent memory leaks
            foreach (var collection in DeviceCollections.Values)
            {
                collection.Clear();
            }
            DeviceCollections.Clear();

            // Clear vessel reference
            _vessel = null;

            _logger.LogDebug("VehicleEditorViewModel disposed");
        }
    }

    /// <summary>
    /// Simple IDisposable implementation for cleanup
    /// </summary>
    internal class DisposableAction : IDisposable
    {
        private readonly Action _action;

        public DisposableAction(Action action)
        {
            _action = action;
        }

        public void Dispose()
        {
            _action();
        }
    }
}
