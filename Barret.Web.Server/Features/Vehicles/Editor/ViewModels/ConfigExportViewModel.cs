using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Web.Server.Services;

namespace Barret.Web.Server.Features.Vehicles.Editor.ViewModels
{
    /// <summary>
    /// View model for the config export dialog
    /// </summary>
    public class ConfigExportViewModel : INotifyPropertyChanged
    {
        private readonly ILogger<ConfigExportViewModel>? _logger;
        private readonly IConfigExportService? _configExportService;
        private readonly IFileDownloadService? _fileDownloadService;

        private bool _isLoading;
        private bool _isExporting;
        private int _exportProgress;
        private string _errorMessage = string.Empty;
        private string _warningMessage = string.Empty;
        private string _successMessage = string.Empty;
        private CancellationTokenSource? _cancellationTokenSource;

        /// <summary>
        /// Initializes a new instance of the ConfigExportViewModel class
        /// </summary>
        public ConfigExportViewModel()
        {
            // Default constructor for design-time support
        }

        /// <summary>
        /// Initializes a new instance of the ConfigExportViewModel class
        /// </summary>
        /// <param name="logger">The logger</param>
        /// <param name="configExportService">The configuration export service</param>
        /// <param name="fileDownloadService">The file download service</param>
        public ConfigExportViewModel(
            ILogger<ConfigExportViewModel> logger,
            IConfigExportService configExportService,
            IFileDownloadService fileDownloadService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configExportService = configExportService ?? throw new ArgumentNullException(nameof(configExportService));
            _fileDownloadService = fileDownloadService ?? throw new ArgumentNullException(nameof(fileDownloadService));
        }

        /// <summary>
        /// Gets or sets a value indicating whether the export is in progress
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets a value indicating whether the export is in progress
        /// </summary>
        public bool IsExporting
        {
            get => _isExporting;
            set => SetProperty(ref _isExporting, value);
        }

        /// <summary>
        /// Gets or sets the export progress (0-100)
        /// </summary>
        public int ExportProgress
        {
            get => _exportProgress;
            set => SetProperty(ref _exportProgress, value);
        }

        /// <summary>
        /// Gets or sets the error message
        /// </summary>
        public string ErrorMessage
        {
            get => _errorMessage;
            set => SetProperty(ref _errorMessage, value);
        }

        /// <summary>
        /// Gets or sets the warning message
        /// </summary>
        public string WarningMessage
        {
            get => _warningMessage;
            set => SetProperty(ref _warningMessage, value);
        }

        /// <summary>
        /// Gets or sets the success message
        /// </summary>
        public string SuccessMessage
        {
            get => _successMessage;
            set => SetProperty(ref _successMessage, value);
        }

        /// <summary>
        /// Gets a value indicating whether the export can be cancelled
        /// </summary>
        public bool CanCancel => IsExporting && _cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested;

        /// <summary>
        /// Exports configurations for a vessel
        /// </summary>
        /// <param name="vessel">The vessel to export configurations for</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task ExportConfigurationsAsync(Vessel vessel)
        {
            if (_configExportService == null || _fileDownloadService == null)
            {
                ErrorMessage = "Services not initialized";
                return;
            }

            if (vessel == null)
            {
                ErrorMessage = "Vessel is required";
                return;
            }

            try
            {
                // Reset state
                IsLoading = true;
                IsExporting = true;
                ExportProgress = 0;
                ErrorMessage = string.Empty;
                WarningMessage = string.Empty;
                SuccessMessage = string.Empty;

                // Create a cancellation token source
                _cancellationTokenSource = new CancellationTokenSource();
                OnPropertyChanged(nameof(CanCancel));

                // Create a progress reporter
                var progress = new Progress<int>(percent =>
                {
                    ExportProgress = percent;
                });

                // Export configurations to a ZIP file
                var exportResult = await _configExportService.ExportConfigurationsToZipAsync(vessel);

                if (!exportResult.Success)
                {
                    ErrorMessage = $"Failed to export configurations: {exportResult.ErrorMessage}";
                    return;
                }

                // Download the ZIP file to the user's machine
                var zipPath = exportResult.Data;
                var zipBytes = await File.ReadAllBytesAsync(zipPath);

                try
                {
                    // Use the vessel ID in the filename for better identification
                    await _fileDownloadService.DownloadFileAsync($"VesselConfigs_{vessel.VehicleId}.zip", zipBytes, "application/zip");

                    // Show success message
                    SuccessMessage = "Configuration files generated and downloaded successfully";

                    // Log the output directory for debugging
                    _logger?.LogInformation("Generated config files and downloaded as ZIP");
                }
                finally
                {
                    // Clean up the ZIP file
                    try
                    {
                        if (File.Exists(zipPath))
                        {
                            File.Delete(zipPath);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogWarning(ex, "Error cleaning up temporary files");
                    }
                }
            }
            catch (OperationCanceledException)
            {
                WarningMessage = "Export operation was cancelled";
                _logger?.LogInformation("Export operation was cancelled");
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error exporting configs");
                ErrorMessage = $"Error exporting configs: {ex.Message}";
            }
            finally
            {
                IsLoading = false;
                IsExporting = false;
                ExportProgress = 0;

                // Dispose the cancellation token source
                if (_cancellationTokenSource != null)
                {
                    _cancellationTokenSource.Dispose();
                    _cancellationTokenSource = null;
                }

                OnPropertyChanged(nameof(CanCancel));
            }
        }

        /// <summary>
        /// Cancels the export operation
        /// </summary>
        public void CancelExport()
        {
            _cancellationTokenSource?.Cancel();
            OnPropertyChanged(nameof(CanCancel));
        }

        /// <summary>
        /// Occurs when a property value changes
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event
        /// </summary>
        /// <param name="propertyName">The name of the property that changed</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Sets a property value and raises the PropertyChanged event if the value changed
        /// </summary>
        /// <typeparam name="T">The type of the property</typeparam>
        /// <param name="field">The field to set</param>
        /// <param name="value">The new value</param>
        /// <param name="propertyName">The name of the property</param>
        /// <returns>True if the value changed, false otherwise</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (EqualityComparer<T>.Default.Equals(field, value))
                return false;

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }
    }
}
