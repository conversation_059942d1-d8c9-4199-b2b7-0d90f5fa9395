using Barret.Shared.DTOs.Vehicles.Vessels;
using Microsoft.AspNetCore.Components;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Tabs
{
    /// <summary>
    /// Base class for the basic info tab view.
    /// </summary>
    public class BasicInfoTabViewBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the vessel.
        /// </summary>
        [Parameter]
        public VesselDto Vessel { get; set; } = null!;

        /// <summary>
        /// Gets or sets the callback for when the vessel changes.
        /// </summary>
        [Parameter]
        public EventCallback<VesselDto> VesselChanged { get; set; }

        /// <summary>
        /// Gets or sets the callback for when the vessel is changed.
        /// </summary>
        [Parameter]
        public EventCallback OnChange { get; set; }

        /// <summary>
        /// Updates a field in the vessel.
        /// </summary>
        /// <param name="value">The new value.</param>
        /// <param name="propertyName">The property name.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task UpdateField(string value, string propertyName)
        {
            switch (propertyName)
            {
                case nameof(Vessel.Name):
                    Vessel.Name = value;
                    break;
                case nameof(Vessel.VehicleId):
                    Vessel.VehicleId = value;
                    break;
                case nameof(Vessel.MMSI):
                    Vessel.MMSI = value;
                    break;
                case nameof(Vessel.ENI):
                    Vessel.ENI = value;
                    break;
            }

            await NotifyChanges();
        }

        /// <summary>
        /// Updates a dimension in the vessel.
        /// </summary>
        /// <param name="value">The new value.</param>
        /// <param name="propertyName">The property name.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task UpdateDimension(double value, string propertyName)
        {
            if (Vessel.Dimensions == null)
            {
                Vessel.Dimensions = new Barret.Shared.DTOs.Vehicles.DimensionsDto();
            }

            switch (propertyName)
            {
                case nameof(Vessel.Dimensions.DistanceGpsToFront):
                    Vessel.Dimensions.DistanceGpsToFront = value;
                    break;
                case nameof(Vessel.Dimensions.DistanceGpsToBack):
                    Vessel.Dimensions.DistanceGpsToBack = value;
                    break;
                case nameof(Vessel.Dimensions.DistanceGpsToLeft):
                    Vessel.Dimensions.DistanceGpsToLeft = value;
                    break;
                case nameof(Vessel.Dimensions.DistanceGpsToRight):
                    Vessel.Dimensions.DistanceGpsToRight = value;
                    break;
            }

            await NotifyChanges();
        }

        /// <summary>
        /// Notifies that the vessel has changed.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task NotifyChanges()
        {
            if (VesselChanged.HasDelegate)
            {
                await VesselChanged.InvokeAsync(Vessel);
            }

            if (OnChange.HasDelegate)
            {
                await OnChange.InvokeAsync();
            }
        }
    }
}
