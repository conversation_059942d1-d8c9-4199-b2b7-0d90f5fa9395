@using Radzen.Blazor
@inherits BasicInfoTabViewBase

<div class="space-y-8">
    <!-- Vehicle Information Section -->
    <div>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Vehicle Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Vehicle Name</label>
                <RadzenTextBox Value="@Vessel.Name"
                              ValueChanged="@(value => UpdateField(value, nameof(Vessel.Name)))"
                              Placeholder="Enter vehicle name"
                              class="barret-input w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Vehicle ID</label>
                <RadzenTextBox Value="@Vessel.VehicleId"
                              ValueChanged="@(value => UpdateField(value, nameof(Vessel.VehicleId)))"
                              Placeholder="Enter vehicle ID"
                              class="barret-input w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
        </div>
    </div>

    <!-- Vessel Identifiers Section -->
    <div>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Vessel Identifiers</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">MMSI</label>
                <RadzenTextBox Value="@Vessel.MMSI"
                              ValueChanged="@(value => UpdateField(value, nameof(Vessel.MMSI)))"
                              Placeholder="Enter MMSI"
                              class="barret-input w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
                <p class="text-xs text-gray-500">Maritime Mobile Service Identity (9 digits)</p>
            </div>
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">ENI</label>
                <RadzenTextBox Value="@Vessel.ENI"
                              ValueChanged="@(value => UpdateField(value, nameof(Vessel.ENI)))"
                              Placeholder="Enter ENI (optional)"
                              class="barret-input w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
                <p class="text-xs text-gray-500">European Number of Identification (8 digits)</p>
            </div>
        </div>
    </div>

    <!-- Vessel Dimensions Section -->
    <div>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Vessel Dimensions</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Distance GPS to Front (m)</label>
                <RadzenNumeric Value="@((decimal)Vessel.Dimensions.DistanceGpsToFront)"
                              ValueChanged="@((decimal value) => UpdateDimension((double)value, nameof(Vessel.Dimensions.DistanceGpsToFront)))"
                              ShowUpDown="true"
                              class="barret-input w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Distance GPS to Back (m)</label>
                <RadzenNumeric Value="@((decimal)Vessel.Dimensions.DistanceGpsToBack)"
                              ValueChanged="@((decimal value) => UpdateDimension((double)value, nameof(Vessel.Dimensions.DistanceGpsToBack)))"
                              ShowUpDown="true"
                              class="barret-input w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Distance GPS to Left (m)</label>
                <RadzenNumeric Value="@((decimal)Vessel.Dimensions.DistanceGpsToLeft)"
                              ValueChanged="@((decimal value) => UpdateDimension((double)value, nameof(Vessel.Dimensions.DistanceGpsToLeft)))"
                              ShowUpDown="true"
                              class="barret-input w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
            <div class="space-y-2">
                <label class="text-sm font-medium text-gray-700">Distance GPS to Right (m)</label>
                <RadzenNumeric Value="@((decimal)Vessel.Dimensions.DistanceGpsToRight)"
                              ValueChanged="@((decimal value) => UpdateDimension((double)value, nameof(Vessel.Dimensions.DistanceGpsToRight)))"
                              ShowUpDown="true"
                              class="barret-input w-full h-11 rounded-lg border-gray-200 focus:border-gray-300 focus:ring focus:ring-gray-200 focus:ring-opacity-50" />
            </div>
        </div>
    </div>

    @* <!-- Wizard Navigation -->
    <div class="flex justify-end pt-8 border-t border-gray-100">
        <button class="flex items-center gap-2 px-4 py-2 rounded-full bg-gray-900 text-white hover:bg-gray-800"
                @onclick="NavigateToNextTab">
            Next
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polyline points="9 18 15 12 9 6"></polyline>
            </svg>
        </button>
    </div> *@
</div>

@code {
    private void NavigateToNextTab()
    {
        // This would need to be implemented based on your navigation logic
        // For now, we'll just call the OnChange callback
        OnChange.InvokeAsync();
    }
}
