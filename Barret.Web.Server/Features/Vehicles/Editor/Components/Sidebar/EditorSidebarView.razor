@using Barret.Web.Server.Features.Vehicles.Editor.Data
@inherits EditorSidebarViewBase

<!-- Sidebar Header -->
<div class="p-4 border-b border-gray-100">
    <h2 class="text-lg font-medium text-gray-900">Device Manager</h2>
    <p class="text-sm text-gray-500 mt-1">Manage all connected devices</p>
</div>

<!-- Sidebar Navigation -->
<nav class="flex-1 overflow-y-auto py-2">
    <ul class="space-y-1 px-2">
        @foreach (var tab in GetAvailableTabs())
        {
            @if (tab.IsParentTab)
            {
                <!-- Parent Tab (Devices) -->
                <li>
                    <button class="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium text-gray-600 hover:bg-white/50 hover:text-gray-900"
                            @onclick="async () => await ToggleParentTab(tab)">
                        <span class="text-gray-500">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                                <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                                <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                                <rect width="7" height="7" x="3" y="14" rx="1"></rect>
                            </svg>
                        </span>
                        <span>@tab.Name</span>
                        <div class="ml-auto flex items-center gap-2">
                            <span class="text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded-full">
                                @tab.DeviceCount
                            </span>
                            <svg class="h-4 w-4 text-gray-400 transition-transform duration-200 @(tab.IsExpanded ? "rotate-180" : "")"
                                 xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <polyline points="6 9 12 15 18 9"></polyline>
                            </svg>
                        </div>
                    </button>

                    @if (tab.IsExpanded)
                    {
                        <!-- Child Tabs (Device Groups) -->
                        <ul class="mt-1 ml-4 space-y-1 border-l border-gray-100 pl-4">
                            @foreach (var childTab in tab.ChildTabs)
                            {
                                <li>
                                    <button class="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-sm font-medium @(ActiveTab.Id == childTab.TabIdentifier.Id ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:bg-white/50 hover:text-gray-900")"
                                            @onclick="() => OnTabSelected.InvokeAsync(childTab.TabIdentifier)">
                                        <span class="text-gray-500">
                                            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                                @switch (childTab.Name)
                                                {
                                                    case "Camera System":
                                                        <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                                                        <circle cx="12" cy="13" r="4"></circle>
                                                        break;
                                                    case "Sensors":
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <path d="M16.2 7.8l-2 6.3-6.4 2.1 2-6.3z"></path>
                                                        break;
                                                    case "Navigation":
                                                    case "Autopilots":
                                                    case "Trackpilots":
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <polygon points="16.24 7.76 14.12 14.12 7.76 16.24 9.88 9.88 16.24 7.76"></polygon>
                                                        break;
                                                    case "Radios":
                                                    case "Antennas":
                                                    case "Communication":
                                                        <path d="M12 20v-6M6 20V10M18 20V4"></path>
                                                        break;
                                                    case "Engines":
                                                    case "Thrusters":
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <path d="M8 12h8M12 8v8"></path>
                                                        break;
                                                    case "Radars":
                                                        <circle cx="12" cy="12" r="10"></circle>
                                                        <circle cx="12" cy="12" r="6"></circle>
                                                        <circle cx="12" cy="12" r="2"></circle>
                                                        break;
                                                    case "Audio System":
                                                        <path d="M3 18v-6a9 9 0 0 1 18 0v6"></path>
                                                        <path d="M21 19a2 2 0 0 1-2 2h-1a2 2 0 0 1-2-2v-3a2 2 0 0 1 2-2h3zM3 19a2 2 0 0 0 2 2h1a2 2 0 0 0 2-2v-3a2 2 0 0 0-2-2H3z"></path>
                                                        break;
                                                    default:
                                                        <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                                                        <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                                                        <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                                                        <rect width="7" height="7" x="3" y="14" rx="1"></rect>
                                                        break;
                                                }
                                            </svg>
                                        </span>
                                        <span>@childTab.Name</span>
                                        <div class="ml-auto flex items-center gap-2">
                                            <span class="text-xs text-gray-500 bg-gray-100 px-1.5 py-0.5 rounded-full">
                                                @childTab.DeviceCount
                                            </span>
                                        </div>
                                    </button>
                                </li>
                            }
                        </ul>
                    }
                </li>
            }
            else
            {
                <!-- Regular Tab (Basic Info) -->
                <li>
                    <button class="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium @(ActiveTab.Id == tab.TabIdentifier.Id ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:bg-white/50 hover:text-gray-900")"
                            @onclick="() => OnTabSelected.InvokeAsync(tab.TabIdentifier)">
                        <span class="text-gray-500">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                <path d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                <path d="M18.375 2.625a2.121 2.121 0 1 1 3 3L12 15l-4 1 1-4Z"></path>
                            </svg>
                        </span>
                        <span>@tab.Name</span>
                        <div class="ml-auto">
                            @if (IsTabComplete(tab.TabIdentifier))
                            {
                                <svg class="h-4 w-4 text-green-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                                    <polyline points="22 4 12 14.01 9 11.01"></polyline>
                                </svg>
                            }
                        </div>
                    </button>
                </li>
            }
        }
    </ul>
</nav>

<!-- Progress Indicator -->
<div class="p-4 border-t border-gray-100">
    <div class="flex items-center justify-between mb-2">
        <div class="text-xs text-gray-500">
            Step @GetCurrentStep() of @GetTotalSteps()
        </div>
        <div class="text-xs text-gray-500">
            @GetCompletionPercentage()% Complete
        </div>
    </div>
    <div class="h-1.5 bg-gray-100 rounded-full overflow-hidden">
        <div class="h-full bg-gray-900 transition-all duration-300" style="width: @(GetCompletionPercentage())%"></div>
    </div>
</div>

@code {
    // All methods are now inherited from the base class
}
