using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Core.Areas.DeviceGroups;
using Barret.Web.Server.Features.Vehicles.Editor.Data;
using Microsoft.AspNetCore.Components;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Sidebar
{
    /// <summary>
    /// Base class for the editor sidebar view.
    /// </summary>
    public class EditorSidebarViewBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the vessel.
        /// </summary>
        [Parameter]
        public VesselDto Vessel { get; set; } = null!;

        /// <summary>
        /// Gets or sets the active tab.
        /// </summary>
        [Parameter]
        public TabIdentifier ActiveTab { get; set; } = TabIdentifier.ForBasicInfo();

        /// <summary>
        /// Gets or sets the callback for when a tab is selected.
        /// </summary>
        [Parameter]
        public EventCallback<TabIdentifier> OnTabSelected { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the devices tab is expanded.
        /// </summary>
        protected bool _isDevicesTabExpanded = false;

        /// <summary>
        /// Initializes the component.
        /// </summary>
        protected override void OnInitialized()
        {
            base.OnInitialized();

            // Set the initial state of the devices tab
            _isDevicesTabExpanded = ActiveTab.Id != TabIdentifier.BasicInfo;
        }

        /// <summary>
        /// Called when parameters are set.
        /// </summary>
        protected override void OnParametersSet()
        {
            base.OnParametersSet();

            // If the active tab is not BasicInfo, expand the devices tab
            if (ActiveTab.Id != TabIdentifier.BasicInfo)
            {
                _isDevicesTabExpanded = true;
            }
        }

        /// <summary>
        /// Handles tab click.
        /// </summary>
        /// <param name="tabIdentifier">The clicked tab identifier.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task OnTabClicked(TabIdentifier tabIdentifier)
        {
            if (OnTabSelected.HasDelegate)
            {
                await OnTabSelected.InvokeAsync(tabIdentifier);
            }
        }

        /// <summary>
        /// Toggles the expansion of a parent tab.
        /// </summary>
        /// <param name="tab">The parent tab to toggle.</param>
        protected async Task ToggleParentTab(TabViewModel tab)
        {
            if (tab.IsParentTab)
            {
                if (tab.Name == "Devices")
                {
                    _isDevicesTabExpanded = !_isDevicesTabExpanded;

                    // If we're collapsing the devices tab and a device group is selected,
                    // navigate to the basic info tab
                    if (!_isDevicesTabExpanded && ActiveTab.Id != TabIdentifier.BasicInfo)
                    {
                        await OnTabSelected.InvokeAsync(TabIdentifier.ForBasicInfo());
                    }
                }

                tab.IsExpanded = !tab.IsExpanded;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Gets the available tabs.
        /// </summary>
        /// <returns>A list of tab view models.</returns>
        protected List<TabViewModel> GetAvailableTabs()
        {
            var tabs = new List<TabViewModel>();

            // Add basic info tab
            var basicInfoTab = TabIdentifier.ForBasicInfo();
            tabs.Add(new TabViewModel
            {
                TabIdentifier = basicInfoTab,
                Name = "Basic Info",
                IconCssClass = "bi bi-info-circle",
                IsDeviceGroupTab = false,
                DeviceCount = 0
            });

            // Create a parent "Devices" tab
            var devicesTab = new TabViewModel
            {
                TabIdentifier = basicInfoTab, // This is just a placeholder, not actually used
                Name = "Devices",
                IconCssClass = "bi bi-hdd-stack",
                IsDeviceGroupTab = false,
                IsParentTab = true,
                IsExpanded = _isDevicesTabExpanded || ActiveTab.Id != TabIdentifier.BasicInfo, // Expand if any device tab is active or if explicitly expanded
                DeviceCount = Vessel.DeviceGroups.Sum(g => g.Value.Devices.Count)
            };

            // Add device group tabs as children
            foreach (var group in Vessel.DeviceGroups)
            {
                // Add all device groups, even if they have no devices
                var tabIdentifier = TabIdentifier.ForDeviceGroup(group.Key);
                var metadata = VehicleEditorData.GetTabMetadata(tabIdentifier);

                var childTab = new TabViewModel
                {
                    TabIdentifier = tabIdentifier,
                    Name = metadata.Name,
                    IconCssClass = metadata.IconCssClass,
                    IsDeviceGroupTab = true,
                    DeviceCount = group.Value.Devices.Count
                };

                // Add to the devices tab
                devicesTab.ChildTabs.Add(childTab);

                // If this tab is active, make sure the parent is expanded
                if (ActiveTab.IsDeviceGroupTab && ActiveTab.DeviceGroupType == group.Key)
                {
                    devicesTab.IsExpanded = true;
                }
            }

            // Sort child tabs by display order
            devicesTab.ChildTabs = devicesTab.ChildTabs
                .OrderBy(t => t.TabIdentifier.IsDeviceGroupTab && t.TabIdentifier.DeviceGroupType != null ?
                    t.TabIdentifier.DeviceGroupType.Value.GetDisplayOrder() : 999)
                .ToList();

            // Add the devices tab to the main tabs list
            tabs.Add(devicesTab);

            return tabs;
        }

        /// <summary>
        /// Gets the completion percentage.
        /// </summary>
        /// <returns>The completion percentage.</returns>
        protected int GetCompletionPercentage()
        {
            // Basic validation checks
            int totalChecks = 0;
            int passedChecks = 0;

            // Check if basic info is filled
            totalChecks++;
            if (!string.IsNullOrWhiteSpace(Vessel.Name) && !string.IsNullOrWhiteSpace(Vessel.VehicleId))
            {
                passedChecks++;
            }

            // Check if dimensions are set
            totalChecks++;
            if (Vessel.Dimensions != null)
            {
                passedChecks++;
            }

            // Check if at least one engine is added
            totalChecks++;
            if (Vessel.DeviceGroups.TryGetValue(DeviceGroups.EngineGroup, out var engines) && engines.Devices.Count > 0)
            {
                passedChecks++;
            }

            // Calculate percentage
            return totalChecks > 0 ? (passedChecks * 100) / totalChecks : 0;
        }

        /// <summary>
        /// Gets the current step number.
        /// </summary>
        /// <returns>The current step number.</returns>
        protected int GetCurrentStep()
        {
            // If we're on the basic info tab, we're on step 1
            if (ActiveTab.Id == TabIdentifier.BasicInfo)
            {
                return 1;
            }

            // Otherwise, find the index of the current tab in the device groups
            var tabs = GetAvailableTabs();
            var devicesTabs = tabs.FirstOrDefault(t => t.IsParentTab)?.ChildTabs ?? new List<TabViewModel>();

            var currentTabIndex = devicesTabs.FindIndex(t => t.TabIdentifier.Id == ActiveTab.Id);
            return currentTabIndex >= 0 ? currentTabIndex + 2 : 1; // +2 because we start at 1 and basic info is step 1
        }

        /// <summary>
        /// Gets the total number of steps.
        /// </summary>
        /// <returns>The total number of steps.</returns>
        protected int GetTotalSteps()
        {
            // Basic info tab + number of device groups
            var tabs = GetAvailableTabs();
            var devicesTabs = tabs.FirstOrDefault(t => t.IsParentTab)?.ChildTabs ?? new List<TabViewModel>();
            return devicesTabs.Count + 1; // +1 for basic info tab
        }

        /// <summary>
        /// Determines if a tab is complete.
        /// </summary>
        /// <param name="tabIdentifier">The tab identifier to check.</param>
        /// <returns>True if the tab is complete, false otherwise.</returns>
        protected bool IsTabComplete(TabIdentifier tabIdentifier)
        {
            if (tabIdentifier.Id == TabIdentifier.BasicInfo)
            {
                // Basic info tab is complete if name and vehicle ID are filled
                return !string.IsNullOrWhiteSpace(Vessel.Name) && !string.IsNullOrWhiteSpace(Vessel.VehicleId);
            }

            // For device group tabs, check if the group has at least one device
            if (tabIdentifier.IsDeviceGroupTab && tabIdentifier.DeviceGroupType != null &&
                Vessel.DeviceGroups.TryGetValue(tabIdentifier.DeviceGroupType.Value, out var group))
            {
                return group.Devices.Count > 0;
            }

            return false;
        }
    }

    /// <summary>
    /// View model for a tab in the sidebar.
    /// </summary>
    public class TabViewModel
    {
        /// <summary>
        /// Gets or sets the tab identifier.
        /// </summary>
        public TabIdentifier TabIdentifier { get; set; } = TabIdentifier.ForBasicInfo();

        /// <summary>
        /// Gets or sets the name of the tab.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the icon CSS class for the tab.
        /// </summary>
        public string IconCssClass { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets a value indicating whether the tab is a device group tab.
        /// </summary>
        public bool IsDeviceGroupTab { get; set; }

        /// <summary>
        /// Gets or sets the device count for the tab.
        /// </summary>
        public int DeviceCount { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the tab is a parent tab.
        /// </summary>
        public bool IsParentTab { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the tab is expanded.
        /// </summary>
        public bool IsExpanded { get; set; }

        /// <summary>
        /// Gets or sets the child tabs.
        /// </summary>
        public List<TabViewModel> ChildTabs { get; set; } = new List<TabViewModel>();
    }
}
