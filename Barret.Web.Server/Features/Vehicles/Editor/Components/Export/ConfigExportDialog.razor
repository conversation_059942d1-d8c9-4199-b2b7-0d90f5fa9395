@using Barret.Web.Server.Features.Vehicles.Editor.ViewModels
@using Radzen.Blazor
@inherits ConfigExportDialogBase

<div class="barret-complex-dialog config-export-dialog">
    <div class="barret-dialog-content">
        <div class="barret-dialog-header">
            <div class="flex items-center">
                <i class="bi bi-file-earmark-code text-blue-600 mr-2 text-xl"></i>
                <span class="text-lg font-semibold text-gray-900">Export Configuration</span>
            </div>
        </div>

        <div class="barret-dialog-body">
            <div class="mb-4">
                <p class="text-gray-600">This will generate configuration files for the current vehicle and download them as a ZIP file.</p>
            </div>

            @if (ViewModel.IsExporting)
            {
                <div class="my-4">
                    <div class="flex items-center mb-2">
                        <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
                        <span class="text-sm text-gray-700">Generating configurations... @ViewModel.ExportProgress%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                             style="width: @(ViewModel.ExportProgress)%"></div>
                    </div>
                    <div class="text-xs text-gray-500 mt-1 text-center">@ViewModel.ExportProgress%</div>
                </div>
            }
            else if (ViewModel.IsLoading)
            {
                <div class="flex justify-center my-4">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
            }

            @if (!string.IsNullOrEmpty(ViewModel.ErrorMessage))
            {
                <div class="barret-alert barret-alert-error mb-4">
                    <div class="flex items-start">
                        <i class="bi bi-exclamation-triangle text-red-600 mr-2 mt-0.5"></i>
                        <div>
                            <strong class="text-red-800">Error</strong>
                            <p class="text-red-700 mt-1">@ViewModel.ErrorMessage</p>
                        </div>
                    </div>
                </div>
            }

            @if (!string.IsNullOrEmpty(ViewModel.WarningMessage))
            {
                <div class="barret-alert barret-alert-warning mb-4">
                    <div class="flex items-start">
                        <i class="bi bi-exclamation-triangle text-yellow-600 mr-2 mt-0.5"></i>
                        <div>
                            <strong class="text-yellow-800">Warning</strong>
                            <p class="text-yellow-700 mt-1">@ViewModel.WarningMessage</p>
                        </div>
                    </div>
                </div>
            }

            @if (!string.IsNullOrEmpty(ViewModel.SuccessMessage))
            {
                <div class="barret-alert barret-alert-success mb-4">
                    <div class="flex items-start">
                        <i class="bi bi-check-circle text-green-600 mr-2 mt-0.5"></i>
                        <div>
                            <strong class="text-green-800">Success</strong>
                            <p class="text-green-700 mt-1">@ViewModel.SuccessMessage</p>
                        </div>
                    </div>
                </div>
            }
        </div>

        <div class="barret-dialog-footer">
            <div class="barret-dialog-btn-group-right">
                @if (ViewModel.CanCancel)
                {
                    <RadzenButton Text="Cancel"
                                 Icon="cancel"
                                 ButtonStyle="ButtonStyle.Danger"
                                 Click="@HandleCancelExport"
                                 class="barret-btn barret-form-btn" />
                }
                <RadzenButton Text="Close"
                             Icon="close"
                             ButtonStyle="ButtonStyle.Secondary"
                             Click="@HandleCloseAsync"
                             class="barret-btn barret-form-btn" />
                <RadzenButton Text="Export"
                             Icon="download"
                             ButtonStyle="ButtonStyle.Primary"
                             Click="@HandleExportAsync"
                             Disabled="@(ViewModel.IsLoading || ViewModel.IsExporting)"
                             IsBusy="@(ViewModel.IsLoading || ViewModel.IsExporting)"
                             class="barret-btn barret-form-btn" />
            </div>
        </div>
    </div>
</div>
