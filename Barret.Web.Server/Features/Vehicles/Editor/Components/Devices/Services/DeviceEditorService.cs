using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups;
using Barret.Services.Core.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.Manufacturers;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.Factories;
using Barret.Web.Server.Services;

using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Services
{
    /// <summary>
    /// Service for the device editor.
    /// </summary>
    public class DeviceEditorService
    {
        private readonly IDeviceModelQueryService _deviceModelQueryService;
        private readonly IManufacturerService _manufacturerService;
        private readonly IBarretToastNotificationService _toastService;
        private readonly ILogger<DeviceEditorService> _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceEditorService"/> class.
        /// </summary>
        /// <param name="deviceModelQueryService">The device model query service.</param>
        /// <param name="manufacturerService">The manufacturer service.</param>
        /// <param name="toastService">The toast notification service.</param>
        /// <param name="logger">The logger.</param>
        public DeviceEditorService(
            IDeviceModelQueryService deviceModelQueryService,
            IManufacturerService manufacturerService,
            IBarretToastNotificationService toastService,
            ILogger<DeviceEditorService> logger)
        {
            _deviceModelQueryService = deviceModelQueryService;
            _manufacturerService = manufacturerService;
            _toastService = toastService;
            _logger = logger;
        }

        /// <summary>
        /// Creates a new device DTO for the specified role and group.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <param name="groupType">The device group type (optional).</param>
        /// <returns>A new device DTO.</returns>
        public DeviceDto CreateNewDevice(DeviceRole role, DeviceGroups? groupType = null)
        {
            _logger.LogInformation("Creating new device with role {DeviceRole} for group {GroupType}", role, groupType);
            return DeviceDTOFactory.CreateDto(role, groupType);
        }

        /// <summary>
        /// Gets manufacturers for a device role.
        /// </summary>
        /// <param name="role">The device role.</param>
        /// <returns>A list of manufacturers.</returns>
        public async Task<List<ManufacturerInfo>> GetManufacturersForDeviceRoleAsync(DeviceRole role)
        {
            try
            {
                _logger.LogInformation("Getting manufacturers for device role {DeviceRole}", role);
                var manufacturers = await _manufacturerService.GetManufacturersWithDeviceRoleAsync(role);
                return manufacturers.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting manufacturers for device role {DeviceRole}", role);
                _toastService.ShowToast("Error", $"Error loading manufacturers: {ex.Message}", ToastType.Error);
                return new List<ManufacturerInfo>();
            }
        }

        /// <summary>
        /// Gets device models for a manufacturer and role.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer ID.</param>
        /// <param name="role">The device role.</param>
        /// <returns>A list of device models.</returns>
        public async Task<List<DeviceModelInfo>> GetDeviceModelsForManufacturerAndRoleAsync(Guid manufacturerId, DeviceRole role)
        {
            try
            {
                _logger.LogInformation("Getting device models for manufacturer {ManufacturerId} and role {DeviceRole}", manufacturerId, role);
                var models = await _deviceModelQueryService.GetDeviceModelsForManufacturerWithRoleAsync(manufacturerId, role);
                return models;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting device models for manufacturer {ManufacturerId} and role {DeviceRole}", manufacturerId, role);
                _toastService.ShowToast("Error", $"Error loading device models: {ex.Message}", ToastType.Error);
                return new List<DeviceModelInfo>();
            }
        }

        /// <summary>
        /// Validates a device.
        /// </summary>
        /// <param name="device">The device to validate.</param>
        /// <param name="showToasts">Whether to show toast notifications for validation errors.</param>
        /// <returns>True if the device is valid; otherwise, false.</returns>
        public bool ValidateDevice(DeviceDto device, bool showToasts)
        {
            if (device == null)
            {
                if (showToasts)
                {
                    _toastService.ShowToast("Error", "Device is null", ToastType.Error);
                }
                return false;
            }

            // Check for required fields
            if (string.IsNullOrWhiteSpace(device.Name))
            {
                if (showToasts)
                {
                    _toastService.ShowToast("Error", "Device name is required", ToastType.Error);
                }
                return false;
            }

            // Add more validation as needed for specific device types

            return true;
        }
    }
}
