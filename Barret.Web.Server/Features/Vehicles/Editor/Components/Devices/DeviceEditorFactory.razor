@using Barret.Core.Areas.Devices.Enums
@using Barret.Services.Core.Areas.DeviceModels.Queries
@using Barret.Services.Core.Areas.Manufacturers
@using Barret.Shared.DTOs.Devices
@using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Views
@using Barret.Web.Server.Extensions
@using Microsoft.Extensions.Logging
@using Ra<PERSON>zen

@inject ILogger<DeviceEditorFactory> Logger
@inject IDeviceModelQueryService DeviceModelQueryService
@inject IManufacturerService ManufacturerService
@inject Radzen.DialogService DialogService

@* Device Editor is now handled via service-based pattern *@

@code {
    [Parameter]
    public EventCallback<DeviceDto> OnDeviceEdited { get; set; }

    [Parameter]
    public EventCallback<DeviceDto> OnDevicePropertyChanged { get; set; }

    [Parameter]
    public bool IsAddingInterface { get; set; }

    private bool _operationInProgress = false;

    /// <summary>
    /// Reloads the device models from the server.
    /// Call this method after adding a new model to ensure the UI is updated.
    /// Note: With service-based dialogs, this is handled automatically when dialogs are opened.
    /// </summary>
    public Task ReloadDeviceModels()
    {
        // With service-based dialogs, models are loaded fresh each time
        return Task.CompletedTask;
    }

    /// <summary>
    /// Opens the device editor for editing an existing device using service-based pattern.
    /// Loads manufacturers and models on demand.
    /// </summary>
    /// <param name="device">The device to edit.</param>
    public async void OpenForEdit(DeviceDto device)
    {
        if (_operationInProgress)
        {
            Logger.LogWarning("Attempted to open editor while another operation is in progress");
            return;
        }

        try
        {
            _operationInProgress = true;

            // Load manufacturers for this device role
            var manufacturers = await LoadManufacturersForDeviceRole(device.DeviceRole);

            var result = await DialogService.OpenAsync<DeviceEditorView>(
                $"Edit {device.DeviceRole}",
                new Dictionary<string, object>
                {
                    { "OnSaveDevice", EventCallback.Factory.Create<DeviceDto>(this, OnDeviceSaved) },
                    { "OnDevicePropertyChanged", EventCallback.Factory.Create<DeviceDto>(this, HandleDevicePropertyChanged) },
                    { "IsAddingInterface", IsAddingInterface },
                    { "Device", device },
                    { "Manufacturers", manufacturers },
                    { "IsAdding", false }
                },
                new DialogOptions()
                {
                    Width = "900px",
                    Height = "auto",
                    Resizable = false,
                    Draggable = true,
                    CloseDialogOnEsc = true,
                    CloseDialogOnOverlayClick = false,
                    CssClass = "barret-complex-dialog device-editor-dialog"
                });

            // Handle the result if needed
            if (result is DeviceDto savedDevice)
            {
                // Device was saved successfully
                Logger.LogInformation($"Device {savedDevice.DeviceRole} edited successfully");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error opening device editor for edit");
        }
        finally
        {
            _operationInProgress = false;
        }
    }

    /// <summary>
    /// Opens the device editor for adding a new device using service-based pattern.
    /// Loads manufacturers and models on demand.
    /// </summary>
    /// <param name="device">The device to add.</param>
    public async void OpenForAdd(DeviceDto device)
    {
        if (_operationInProgress)
        {
            Logger.LogWarning("Attempted to open editor while another operation is in progress");
            return;
        }

        try
        {
            _operationInProgress = true;

            // Load manufacturers for this device role
            var manufacturers = await LoadManufacturersForDeviceRole(device.DeviceRole);

            var result = await DialogService.OpenAsync<DeviceEditorView>(
                $"Add {device.DeviceRole}",
                new Dictionary<string, object>
                {
                    { "OnSaveDevice", EventCallback.Factory.Create<DeviceDto>(this, OnDeviceSaved) },
                    { "OnDevicePropertyChanged", EventCallback.Factory.Create<DeviceDto>(this, HandleDevicePropertyChanged) },
                    { "IsAddingInterface", IsAddingInterface },
                    { "Device", device },
                    { "Manufacturers", manufacturers },
                    { "IsAdding", true }
                },
                new DialogOptions()
                {
                    Width = "900px",
                    Height = "auto",
                    Resizable = false,
                    Draggable = true,
                    CloseDialogOnEsc = true,
                    CloseDialogOnOverlayClick = false,
                    CssClass = "barret-complex-dialog device-editor-dialog"
                });

            // Handle the result if needed
            if (result is DeviceDto savedDevice)
            {
                // Device was saved successfully
                Logger.LogInformation($"Device {savedDevice.DeviceRole} added successfully");
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error opening device editor for add");
        }
        finally
        {
            _operationInProgress = false;
        }
    }

    /// <summary>
    /// Loads manufacturers for a device role.
    /// </summary>
    /// <param name="role">The device role.</param>
    /// <returns>A list of manufacturers.</returns>
    private async Task<List<ManufacturerInfo>> LoadManufacturersForDeviceRole(DeviceRole role)
    {
        try
        {
            // Get manufacturers that have models for this device role
            var manufacturers = await ManufacturerService.GetManufacturersWithDeviceRoleAsync(role);
            return manufacturers.ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading manufacturers for device role {DeviceRole}", role);
            return new List<ManufacturerInfo>();
        }
    }

    /// <summary>
    /// Handles device save.
    /// </summary>
    /// <param name="device">The saved device.</param>
    private async Task OnDeviceSaved(DeviceDto device)
    {
        try
        {
            // Invoke the callback to notify the parent component
            if (OnDeviceEdited.HasDelegate)
            {
                await OnDeviceEdited.InvokeAsync(device);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling device save");
        }
    }

    /// <summary>
    /// Handles device property changes.
    /// </summary>
    /// <param name="device">The device with changed properties.</param>
    private async Task HandleDevicePropertyChanged(DeviceDto device)
    {
        try
        {
            // Invoke the callback to notify the parent component
            if (OnDevicePropertyChanged.HasDelegate)
            {
                await OnDevicePropertyChanged.InvokeAsync(device);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error handling device property change");
        }
    }
}
