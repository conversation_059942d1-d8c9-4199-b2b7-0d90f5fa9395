@using Barret.Core.Areas.Devices.Enums
@using Barret.Shared.DTOs.Devices
@using Barret.Services.Core.Areas.Devices.Queries
@using Barret.Services.Core.Areas.Vehicles
@using Barret.Core.Areas.Vehicles.Models.Vessel
@using Barret.Shared.DTOs.Vehicles.Vessels
@using Barret.Web.Server.Services
@using Barret.Web.Server.Services.DTO
@using Barret.Web.Server.Features.Vehicles.Services
@using Microsoft.Extensions.Logging
@using Radzen.Blazor
@using <PERSON><PERSON><PERSON>
@using System.Linq

@inject IDeviceQueryService DeviceQueryService
@inject IVehicleService VehicleService
@inject DeviceDtoService DeviceDtoService
@inject IBarretToastNotificationService ToastService
@inject ILogger<DeviceConnectionsPanel> Logger
@inject DialogService DialogService

<div class="relative device-connections-panel">
    <div class="flex justify-between items-center mb-4 px-4 pt-4">
        <h4 class="text-lg font-medium text-gray-900">Device Connections</h4>
        <button class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors connection-button"
                @onclick="OpenAddConnectionDialog">
            <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
            </svg>
            <span>Add Connection</span>
        </button>
    </div>

    @if (isLoading)
    {
        <div class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
    }
    else if (connections.Count == 0)
    {
        <div class="text-center py-8 bg-gray-50 rounded-lg">
            <div class="mx-auto w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                <svg class="h-8 w-8 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                    <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                </svg>
            </div>
            <h3 class="text-xl font-medium text-gray-900 mb-2">No Connections</h3>
            <p class="text-gray-500 mb-6 max-w-md mx-auto">
                This device doesn't have any connections yet. Add a connection to link this device with other devices in the system.
            </p>
            <button class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors mx-auto connection-button"
                    @onclick="OpenAddConnectionDialog">
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span>Add Connection</span>
            </button>
        </div>
    }
    else
    {
        <!-- Custom Tab Navigation -->
        <div class="w-full border-b border-gray-100 mb-4">
            <div class="flex overflow-x-auto hide-scrollbar">
                <RadzenButton Text="Incoming Connections"
                            Icon="arrow_downward"
                            ButtonStyle="@(activeTabIndex == 0 ? ButtonStyle.Primary : ButtonStyle.Light)"
                            Size="ButtonSize.Medium"
                            Click="@(() => activeTabIndex = 0)"
                            class="@($"{GetTabButtonClasses(0)} mr-2")" />

                <RadzenButton Text="Outgoing Connections"
                            Icon="arrow_upward"
                            ButtonStyle="@(activeTabIndex == 1 ? ButtonStyle.Primary : ButtonStyle.Light)"
                            Size="ButtonSize.Medium"
                            Click="@(() => activeTabIndex = 1)"
                            class="@GetTabButtonClasses(1)" />
            </div>
        </div>

        <!-- Tab Content -->
        @if (activeTabIndex == 0)
        {
            <!-- Incoming Connections Tab -->
            @if (!devicesUsingThisAsInterface.Any())
                    {
                        <div class="text-center py-8 bg-gray-50 rounded-lg">
                            <div class="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M4 6h16M4 12h16m-7 6h7"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Incoming Connections</h3>
                            <p class="text-gray-500 mb-4">No devices are currently connected to this device.</p>
                            <RadzenButton Text="Add Connection"
                                        Icon="add"
                                        ButtonStyle="ButtonStyle.Primary"
                                        Size="ButtonSize.Medium"
                                        Click="@OpenAddConnectionDialog"
                                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors" />
                        </div>
                    }
                    else
                    {
                        <RadzenDataGrid Data="@devicesUsingThisAsInterface"
                                       TItem="DeviceConnectionDto"
                                       AllowFiltering="false"
                                       AllowSorting="true"
                                       AllowPaging="false"
                                       class="barret-data-grid">
                            <Columns>
                                <RadzenDataGridColumn TItem="DeviceConnectionDto" Title="Connected Device" Width="35%">
                                    <Template Context="connection">
                                        <div class="flex items-center space-x-3">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">@GetDeviceName(connection.ConnectedDeviceId)</div>
                                                <div class="text-sm text-gray-500">@GetDeviceRole(connection.ConnectedDeviceId)</div>
                                            </div>
                                        </div>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn TItem="DeviceConnectionDto" Property="Type" Title="Connection Type" Width="20%">
                                    <Template Context="connection">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            @connection.Type
                                        </span>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn TItem="DeviceConnectionDto" Property="Direction" Title="Data Flow" Width="20%">
                                    <Template Context="connection">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @(connection.Direction == ConnectionDirection.Duplex ? "bg-green-100 text-green-800" :
                                              connection.Direction == ConnectionDirection.Inbound ? "bg-yellow-100 text-yellow-800" :
                                              "bg-purple-100 text-purple-800")">
                                            @connection.Direction
                                        </span>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn TItem="DeviceConnectionDto" Title="Actions" Width="25%" Sortable="false">
                                    <Template Context="connection">
                                        <div class="flex items-center space-x-2">
                                            <RadzenButton Icon="edit"
                                                        ButtonStyle="ButtonStyle.Light"
                                                        Size="ButtonSize.ExtraSmall"
                                                        Click="@(() => EditConnection(connection))"
                                                        Tooltip="Edit Connection"
                                                        class="px-2 py-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors" />
                                            <RadzenButton Icon="delete"
                                                        ButtonStyle="ButtonStyle.Danger"
                                                        Size="ButtonSize.ExtraSmall"
                                                        Click="@(() => DeleteConnection(connection))"
                                                        Tooltip="Delete Connection"
                                                        class="px-2 py-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors" />
                                        </div>
                                    </Template>
                                </RadzenDataGridColumn>
                            </Columns>
                        </RadzenDataGrid>

                        <div class="mt-4 flex justify-between items-center">
                            <div class="text-sm text-gray-500">
                                Total: @devicesUsingThisAsInterface.Count incoming connection(s)
                            </div>
                            <RadzenButton Text="Add Connection"
                                        Icon="add"
                                        ButtonStyle="ButtonStyle.Primary"
                                        Size="ButtonSize.Small"
                                        Click="@OpenAddConnectionDialog"
                                        class="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors" />
                        </div>
                    }
        }
        else if (activeTabIndex == 1)
        {
            <!-- Outgoing Connections Tab -->
            @if (!interfacesThisDeviceConnectsTo.Any())
                    {
                        <div class="text-center py-8 bg-gray-50 rounded-lg">
                            <div class="mx-auto w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <svg class="h-6 w-6 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                                </svg>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Outgoing Connections</h3>
                            <p class="text-gray-500 mb-4">This device is not connected to any interface devices.</p>
                            <RadzenButton Text="Add Connection"
                                        Icon="add"
                                        ButtonStyle="ButtonStyle.Primary"
                                        Size="ButtonSize.Medium"
                                        Click="@OpenAddConnectionDialog"
                                        class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors" />
                        </div>
                    }
                    else
                    {
                        <RadzenDataGrid Data="@interfacesThisDeviceConnectsTo"
                                       TItem="DeviceConnectionDto"
                                       AllowFiltering="false"
                                       AllowSorting="true"
                                       AllowPaging="false"
                                       class="barret-data-grid">
                            <Columns>
                                <RadzenDataGridColumn TItem="DeviceConnectionDto" Title="Interface Device" Width="35%">
                                    <Template Context="connection">
                                        <div class="flex items-center space-x-3">
                                            <div class="flex-shrink-0">
                                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                                    <svg class="w-4 h-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div>
                                                <div class="text-sm font-medium text-gray-900">@GetDeviceName(connection.InterfaceDeviceId)</div>
                                                <div class="text-sm text-gray-500">@GetDeviceRole(connection.InterfaceDeviceId)</div>
                                            </div>
                                        </div>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn TItem="DeviceConnectionDto" Property="Type" Title="Connection Type" Width="20%">
                                    <Template Context="connection">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            @connection.Type
                                        </span>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn TItem="DeviceConnectionDto" Property="Direction" Title="Data Flow" Width="20%">
                                    <Template Context="connection">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            @(connection.Direction == ConnectionDirection.Duplex ? "bg-green-100 text-green-800" :
                                              connection.Direction == ConnectionDirection.Inbound ? "bg-yellow-100 text-yellow-800" :
                                              "bg-purple-100 text-purple-800")">
                                            @connection.Direction
                                        </span>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn TItem="DeviceConnectionDto" Title="Actions" Width="25%" Sortable="false">
                                    <Template Context="connection">
                                        <div class="flex items-center space-x-2">
                                            <RadzenButton Icon="edit"
                                                        ButtonStyle="ButtonStyle.Light"
                                                        Size="ButtonSize.ExtraSmall"
                                                        Click="@(() => EditConnection(connection))"
                                                        Tooltip="Edit Connection"
                                                        class="px-2 py-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors" />
                                            <RadzenButton Icon="delete"
                                                        ButtonStyle="ButtonStyle.Danger"
                                                        Size="ButtonSize.ExtraSmall"
                                                        Click="@(() => DeleteConnection(connection))"
                                                        Tooltip="Delete Connection"
                                                        class="px-2 py-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded transition-colors" />
                                        </div>
                                    </Template>
                                </RadzenDataGridColumn>
                            </Columns>
                        </RadzenDataGrid>

                        <div class="mt-4 flex justify-between items-center">
                            <div class="text-sm text-gray-500">
                                Total: @interfacesThisDeviceConnectsTo.Count outgoing connection(s)
                            </div>
                            <RadzenButton Text="Add Connection"
                                        Icon="add"
                                        ButtonStyle="ButtonStyle.Primary"
                                        Size="ButtonSize.Small"
                                        Click="@OpenAddConnectionDialog"
                                        class="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors" />
                        </div>
                    }
        }
    }
</div>

<!-- Connection Editor and Delete Confirmation dialogs are now handled via DialogService.OpenAsync pattern in code-behind -->



<style>
    .device-connections-panel {
        position: relative;
        overflow: visible;
        background-color: white;
    }

    .connection-button {
        position: relative;
        z-index: 999 !important;
    }

    /* Custom tab styling for RadzenButton-based tabs */
    .hide-scrollbar {
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* Internet Explorer 10+ */
    }

    .hide-scrollbar::-webkit-scrollbar {
        display: none; /* WebKit */
    }

    /* RadzenDataGrid styling for device connections */
    .barret-data-grid :deep(.rz-datatable) {
        border: none;
        border-radius: 0.375rem;
        overflow: hidden;
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    }

    .barret-data-grid :deep(.rz-datatable-header) {
        background-color: #f9fafb;
        border-bottom: 1px solid #e5e7eb;
    }

    .barret-data-grid :deep(.rz-datatable-header th) {
        padding: 0.75rem 1rem;
        font-weight: 500;
        color: #374151;
        font-size: 0.875rem;
        text-align: left;
    }

    .barret-data-grid :deep(.rz-datatable-data tr) {
        border-bottom: 1px solid #f3f4f6;
        transition: background-color 0.15s ease;
    }

    .barret-data-grid :deep(.rz-datatable-data tr:hover) {
        background-color: #f9fafb;
    }

    .barret-data-grid :deep(.rz-datatable-data td) {
        padding: 0.75rem 1rem;
        color: #374151;
        font-size: 0.875rem;
        vertical-align: middle;
    }

    .barret-data-grid :deep(.rz-datatable-data tr:last-child) {
        border-bottom: none;
    }
</style>

@code {
    [Parameter]
    public DeviceDto Device { get; set; } = null!;

    [Parameter]
    public EventCallback<DeviceDto> OnDeviceChanged { get; set; }

    [Parameter]
    public List<DeviceDto> AllDevices { get; set; } = new();

    [Parameter]
    public VesselDto Vessel { get; set; } = null!;

    private bool isLoading = true;
    private List<DeviceConnectionDto> connections = new();
    private List<DeviceDto> availableInterfaceDevices = new();
    private List<DeviceDto> availableDevicesToConnect = new();
    private int activeTabIndex = 0;

    /// <summary>
    /// Gets the CSS classes for tab buttons based on whether they're active.
    /// </summary>
    /// <param name="index">The tab index.</param>
    /// <returns>The CSS classes for the tab button.</returns>
    protected string GetTabButtonClasses(int index) => activeTabIndex == index
        ? "bg-blue-600 text-white border-blue-600 hover:bg-blue-700"
        : "bg-white text-gray-700 border-gray-300 hover:bg-gray-50";

    /// <summary>
    /// Gets the device name by ID.
    /// </summary>
    /// <param name="deviceId">The device ID.</param>
    /// <returns>The device name or "Unknown Device" if not found.</returns>
    protected string GetDeviceName(Guid deviceId)
    {
        var device = AllDevices.FirstOrDefault(d => d.Id == deviceId);
        return device?.Name ?? "Unknown Device";
    }

    /// <summary>
    /// Gets the device role by ID.
    /// </summary>
    /// <param name="deviceId">The device ID.</param>
    /// <returns>The device role or "Unknown Role" if not found.</returns>
    protected string GetDeviceRole(Guid deviceId)
    {
        var device = AllDevices.FirstOrDefault(d => d.Id == deviceId);
        return device?.DeviceRole.ToString() ?? "Unknown Role";
    }

    /// <summary>
    /// Gets the list of devices using this device as an interface (incoming connections).
    /// </summary>
    protected List<DeviceConnectionDto> devicesUsingThisAsInterface =>
        connections.Where(c => c.InterfaceDeviceId == Device?.Id).ToList();

    /// <summary>
    /// Gets the list of interfaces this device connects to (outgoing connections).
    /// </summary>
    protected List<DeviceConnectionDto> interfacesThisDeviceConnectsTo =>
        connections.Where(c => c.ConnectedDeviceId == Device?.Id).ToList();

    // Connection mode enum
    private enum ConnectionMode
    {
        ConnectTo,
        ServeAsInterface
    }

    // Pre-populated lists for enums (kept for future dialog implementation)
    private ConnectionType[] ConnectionTypes => Enum.GetValues<ConnectionType>();
    private ConnectionDirection[] ConnectionDirections => Enum.GetValues<ConnectionDirection>();

    protected override async Task OnInitializedAsync()
    {
        await LoadDataAsync();
    }

    protected override async Task OnParametersSetAsync()
    {
        await LoadDataAsync();
    }

    private async Task LoadDataAsync()
    {
        try
        {
            isLoading = true;
            StateHasChanged();

            if (Device == null || Vessel == null)
            {
                connections = new List<DeviceConnectionDto>();
                return;
            }

            // Initialize connections collection if needed
            Device.Connections ??= [];
            Vessel.DeviceConnections ??= [];

            // Create a new list to hold all connections relevant to this device
            var allConnections = new List<DeviceConnectionDto>();

            // Add outgoing connections from the device's Connections collection
            // These are connections where this device is the connectedDevice
            allConnections.AddRange(Device.Connections);

            // Add incoming connections from the vessel's DeviceConnections collection
            // These are connections where this device is the interfaceDevice
            var incomingConnections = Vessel.DeviceConnections
                .Where(c => c.InterfaceDeviceId == Device.Id)
                .ToList();

            // Log the number of connections found
            Logger.LogDebug("Found {OutgoingCount} outgoing and {IncomingCount} incoming connections for device {DeviceId}",
                Device.Connections.Count, incomingConnections.Count, Device.Id);

            // Add incoming connections that aren't already in the list
            foreach (var connection in incomingConnections)
            {
                // Check if this connection is already in the list
                if (!allConnections.Any(c =>
                    c.ConnectedDeviceId == connection.ConnectedDeviceId &&
                    c.InterfaceDeviceId == connection.InterfaceDeviceId))
                {
                    allConnections.Add(connection);
                }
            }

            // Set the connections list
            connections = allConnections;

            // Filter available devices using the vehicle service
            availableInterfaceDevices = VehicleService
                .GetCompatibleInterfaceDevices(Device, AllDevices)
                .ToList();

            // Filter devices that can connect to this device as an interface
            availableDevicesToConnect = VehicleService
                .GetDevicesThatCanConnectToInterface(Device, AllDevices)
                .ToList();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading device connections for device {DeviceId}", Device?.Id);
            ToastService.ShowToast("Error", $"Failed to load connections: {ex.Message}", ToastType.Error);
        }
        finally
        {
            isLoading = false;
            StateHasChanged();
        }
    }

    private async Task OpenAddConnectionDialog()
    {
        try
        {
            // For now, show a simple notification that this feature will be implemented with DialogService
            ToastService.ShowToast("Info", "Connection editor will be implemented with RadzenDialog in the next phase", ToastType.Info);

            // TODO: Implement with DialogService.OpenAsync<ConnectionEditorDialog>()
            // var result = await DialogService.OpenAsync<ConnectionEditorDialog>("Add Connection",
            //     new Dictionary<string, object>
            //     {
            //         { "Device", Device },
            //         { "AllDevices", AllDevices },
            //         { "AvailableInterfaceDevices", availableInterfaceDevices },
            //         { "AvailableDevicesToConnect", availableDevicesToConnect }
            //     },
            //     new DialogOptions { Width = "600px", Height = "500px", Resizable = true, Draggable = true });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error opening add connection dialog");
            ToastService.ShowToast("Error", "Failed to open connection editor", ToastType.Error);
        }
    }

    private async Task EditConnection(DeviceConnectionDto connection)
    {
        try
        {
            // For now, show a simple notification that this feature will be implemented with DialogService
            ToastService.ShowToast("Info", "Connection editor will be implemented with RadzenDialog in the next phase", ToastType.Info);

            // TODO: Implement with DialogService.OpenAsync<ConnectionEditorDialog>()
            // var result = await DialogService.OpenAsync<ConnectionEditorDialog>("Edit Connection",
            //     new Dictionary<string, object>
            //     {
            //         { "Device", Device },
            //         { "AllDevices", AllDevices },
            //         { "Connection", connection },
            //         { "IsEditing", true }
            //     },
            //     new DialogOptions { Width = "600px", Height = "500px", Resizable = true, Draggable = true });
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error opening edit connection dialog");
            ToastService.ShowToast("Error", "Failed to open connection editor", ToastType.Error);
        }
    }

    private async Task DeleteConnection(DeviceConnectionDto connection)
    {
        try
        {
            // For now, show a simple confirmation dialog using browser confirm
            var confirmed = await DialogService.Confirm(
                $"Are you sure you want to delete this connection? This action cannot be undone.",
                "Confirm Deletion",
                new ConfirmOptions { OkButtonText = "Delete", CancelButtonText = "Cancel" });

            if (confirmed == true)
            {
                await ConfirmDeleteConnection(connection);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting connection");
            ToastService.ShowToast("Error", "Failed to delete connection", ToastType.Error);
        }
    }

    // SaveConnection method removed - will be implemented in separate dialog component using DialogService

    private async Task ConfirmDeleteConnection(DeviceConnectionDto connectionToDelete)
    {
        try
        {
            if (Device == null || connectionToDelete == null)
            {
                ToastService.ShowToast("Error", "Device or connection information is missing", ToastType.Error);
                return;
            }

            bool success = false;

            // Determine which device is the connected device and which is the interface
            if (connectionToDelete.InterfaceDeviceId == Device.Id)
            {
                // This device is the interface, so we need to remove the connection from the connected device
                var connectedDevice = AllDevices.FirstOrDefault(d => d.Id == connectionToDelete.ConnectedDeviceId);
                if (connectedDevice != null)
                {
                    var (removed, removeErrorMessage) = DeviceDtoService.RemoveConnection(
                        connectedDevice,
                        Device.Id);
                    success = removed;
                    if (!removed && removeErrorMessage != null)
                    {
                        ToastService.ShowToast("Error", removeErrorMessage, ToastType.Error);
                        return;
                    }
                }
                else
                {
                    ToastService.ShowToast("Error", "Connected device not found", ToastType.Error);
                    return;
                }
            }
            else
            {
                // This device is the connected device, so we remove the connection directly
                var (removed, removeErrorMessage) = DeviceDtoService.RemoveConnection(
                    Device,
                    connectionToDelete.InterfaceDeviceId);
                success = removed;
                if (!removed && removeErrorMessage != null)
                {
                    ToastService.ShowToast("Error", removeErrorMessage, ToastType.Error);
                    return;
                }
            }

            if (success)
            {
                // Also remove the connection from the vessel's DeviceConnections list
                if (Vessel != null)
                {
                    var vesselConnection = Vessel.DeviceConnections.FirstOrDefault(c =>
                        c.ConnectedDeviceId == connectionToDelete.ConnectedDeviceId &&
                        c.InterfaceDeviceId == connectionToDelete.InterfaceDeviceId);

                    if (vesselConnection != null)
                    {
                        Vessel.DeviceConnections.Remove(vesselConnection);
                        Logger.LogInformation("Removed connection from vessel's DeviceConnections list");
                    }
                    else
                    {
                        // If the connection wasn't found directly, it might be because the IDs are reversed
                        // This can happen when we're deleting a connection where this device is the interface
                        vesselConnection = Vessel.DeviceConnections.FirstOrDefault(c =>
                            c.ConnectedDeviceId == connectionToDelete.InterfaceDeviceId &&
                            c.InterfaceDeviceId == connectionToDelete.ConnectedDeviceId);

                        if (vesselConnection != null)
                        {
                            Vessel.DeviceConnections.Remove(vesselConnection);
                            Logger.LogInformation("Removed reversed connection from vessel's DeviceConnections list");
                        }
                    }
                }

                ToastService.ShowToast("Success", "Connection deleted successfully", ToastType.Success);

                // Refresh the connections list
                await LoadDataAsync();

                // Notify that the device has changed
                await OnDeviceChanged.InvokeAsync(Device);
            }
            else
            {
                ToastService.ShowToast("Error", "Failed to delete connection", ToastType.Error);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error deleting connection for device {DeviceId}", Device?.Id);
            ToastService.ShowToast("Error", $"Failed to delete connection: {ex.Message}", ToastType.Error);
        }
    }


}
