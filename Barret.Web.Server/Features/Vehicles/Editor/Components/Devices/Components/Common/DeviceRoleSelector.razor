@using Barret.Core.Areas.Devices.Enums
@using Radzen.Blazor
@inherits DeviceRoleSelectorBase

<div class="barret-device-role-selector-dialog">
    <div class="barret-dialog-content">
        <div class="barret-dialog-header">
            <div class="flex items-center">
                <i class="bi bi-device-hdd text-blue-600 mr-2 text-xl"></i>
                <span class="text-lg font-semibold text-gray-900">Select Device Type</span>
            </div>
        </div>

        <div class="barret-dialog-body">
            <div class="device-role-selector-container p-4">
                <div class="mb-4">
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="bi bi-search text-gray-400"></i>
                        </div>
                        <RadzenTextBox Placeholder="Search device types..."
                                      Value="@SearchText"
                                      ValueChanged="@((value) => OnSearchTextChanged(value))"
                                      class="barret-input barret-form-input pl-10 w-full" />
                    </div>
                </div>

                <div class="device-role-grid">
                    @if (FilteredRoles.Count == 0)
                    {
                        <div class="barret-alert barret-alert-info">
                            <div class="flex items-center">
                                <i class="bi bi-info-circle text-blue-600 mr-2"></i>
                                <span class="text-blue-800">No device types found matching your search.</span>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="grid grid-cols-2 gap-3">
                            @foreach (var roleViewModel in FilteredRoles)
                            {
                                <div class="device-role-card bg-white border border-gray-200 rounded-lg p-4 hover:bg-gray-50 hover:border-blue-300 cursor-pointer transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                     @onclick="() => SelectRoleAsync(roleViewModel.Role)"
                                     tabindex="0"
                                     @onkeypress="@(async (e) => { if (e.Key == "Enter" || e.Key == " ") await SelectRoleAsync(roleViewModel.Role); })">
                                    <div class="device-role-icon text-center mb-2">
                                        <i class="@roleViewModel.IconCssClass text-2xl text-blue-600"></i>
                                    </div>
                                    <div class="device-role-name text-center text-sm font-medium text-gray-900">
                                        @roleViewModel.DisplayName
                                    </div>
                                </div>
                            }
                        </div>
                    }
                </div>
            </div>
        </div>

        <div class="barret-dialog-footer">
            <div class="barret-dialog-btn-group-right">
                <RadzenButton Text="Cancel"
                             Icon="cancel"
                             ButtonStyle="ButtonStyle.Secondary"
                             Click="@CancelAsync"
                             class="barret-btn barret-form-btn" />
            </div>
        </div>
    </div>
</div>
