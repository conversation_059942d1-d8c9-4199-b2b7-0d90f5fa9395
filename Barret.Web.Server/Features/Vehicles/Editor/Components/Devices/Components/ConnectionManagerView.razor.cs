using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Radzen.Blazor;
using System;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Components
{
    /// <summary>
    /// Code-behind for the ConnectionManagerView component.
    /// </summary>
    public partial class ConnectionManagerView
    {
        private RadzenDataGrid<DeviceConnectionDto>? connectionsGrid;

        /// <summary>
        /// Gets or sets the parent device.
        /// </summary>
        [Parameter]
        public DeviceDto ParentDevice { get; set; } = null!;

        /// <summary>
        /// Gets or sets the callback for when the device changes.
        /// </summary>
        [Parameter]
        public EventCallback<DeviceDto> OnDeviceChanged { get; set; }

        /// <summary>
        /// Gets or sets the logger factory for creating ViewModels.
        /// </summary>
        [Inject]
        protected ILoggerFactory LoggerFactory { get; set; } = null!;

        /// <summary>
        /// Initializes the component.
        /// </summary>
        protected override void OnInitialized()
        {
            // Create the ViewModel with logger
            ViewModel = new ConnectionManagerViewModel(LoggerFactory.CreateLogger<ConnectionManagerViewModel>());

            // Subscribe to device changes
            ViewModel.DeviceChanged += OnViewModelDeviceChanged;

            base.OnInitialized();
        }

        /// <summary>
        /// Called when parameters are set.
        /// </summary>
        protected override void OnParametersSet()
        {
            if (ParentDevice != null && ViewModel != null)
            {
                ViewModel.ParentDevice = ParentDevice;
            }
            
            base.OnParametersSet();
        }

        /// <summary>
        /// Handles editing a connection.
        /// </summary>
        /// <param name="connection">The connection to edit.</param>
        private void EditConnection(DeviceConnectionDto connection)
        {
            ViewModel.SelectedConnection = connection;
            ViewModel.IsAddConnectionDialogVisible = true;
        }

        /// <summary>
        /// Handles device changes from the ViewModel.
        /// </summary>
        /// <param name="device">The updated device.</param>
        private async void OnViewModelDeviceChanged(DeviceDto device)
        {
            try
            {
                if (OnDeviceChanged.HasDelegate)
                {
                    await OnDeviceChanged.InvokeAsync(device);
                }
                
                // Refresh the grid
                if (connectionsGrid != null)
                {
                    await connectionsGrid.Reload();
                }
                
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling device change from ViewModel");
            }
        }

        /// <summary>
        /// Disposes resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing && ViewModel != null)
            {
                ViewModel.DeviceChanged -= OnViewModelDeviceChanged;
            }
            
            base.Dispose(disposing);
        }
    }
}
