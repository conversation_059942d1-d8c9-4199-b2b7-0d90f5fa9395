using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.ViewModels;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Components
{
    /// <summary>
    /// Code-behind for the AlarmManagerView component.
    /// </summary>
    public partial class AlarmManagerView
    {
        /// <summary>
        /// Gets or sets the device being managed.
        /// </summary>
        [Parameter]
        public DeviceDto Device { get; set; } = null!;

        /// <summary>
        /// Gets or sets the callback for when the device changes.
        /// </summary>
        [Parameter]
        public EventCallback<DeviceDto> OnDeviceChanged { get; set; }

        /// <summary>
        /// Gets or sets the logger factory for creating ViewModels.
        /// </summary>
        [Inject]
        protected ILoggerFactory LoggerFactory { get; set; } = null!;

        /// <summary>
        /// Initializes the component.
        /// </summary>
        protected override void OnInitialized()
        {
            // Create the ViewModel with logger
            ViewModel = new AlarmManagerViewModel(LoggerFactory.CreateLogger<AlarmManagerViewModel>());

            // Subscribe to device changes
            ViewModel.DeviceChanged += OnViewModelDeviceChanged;

            base.OnInitialized();
        }

        /// <summary>
        /// Called when parameters are set.
        /// </summary>
        protected override void OnParametersSet()
        {
            if (Device != null && ViewModel != null)
            {
                ViewModel.Device = Device;
            }
            
            base.OnParametersSet();
        }

        /// <summary>
        /// Handles device changes from the ViewModel.
        /// </summary>
        /// <param name="device">The updated device.</param>
        private async void OnViewModelDeviceChanged(DeviceDto device)
        {
            try
            {
                if (OnDeviceChanged.HasDelegate)
                {
                    await OnDeviceChanged.InvokeAsync(device);
                }
                
                await InvokeAsync(StateHasChanged);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error handling device change from ViewModel");
            }
        }

        /// <summary>
        /// Gets the notification types for dropdown.
        /// </summary>
        /// <returns>Array of notification types.</returns>
        private NotificationType[] GetNotificationTypes()
        {
            return Enum.GetValues<NotificationType>();
        }

        /// <summary>
        /// Gets the notification group IDs for dropdown.
        /// </summary>
        /// <returns>Array of notification group IDs.</returns>
        private NotificationGroupId[] GetNotificationGroupIds()
        {
            return Enum.GetValues<NotificationGroupId>();
        }

        /// <summary>
        /// Gets the warning IDs for dropdown.
        /// </summary>
        /// <returns>Array of warning IDs.</returns>
        private WarningId[] GetWarningIds()
        {
            return Enum.GetValues<WarningId>();
        }

        /// <summary>
        /// Gets the domain driver IDs for dropdown.
        /// Note: DomainDriverId corresponds to WarningId in the current implementation.
        /// </summary>
        /// <returns>Array of warning IDs (domain driver IDs).</returns>
        private WarningId[] GetDomainDriverIds()
        {
            return Enum.GetValues<WarningId>();
        }

        /// <summary>
        /// Disposes resources.
        /// </summary>
        protected override void Dispose(bool disposing)
        {
            if (disposing && ViewModel != null)
            {
                ViewModel.DeviceChanged -= OnViewModelDeviceChanged;
            }

            base.Dispose(disposing);
        }
    }
}
