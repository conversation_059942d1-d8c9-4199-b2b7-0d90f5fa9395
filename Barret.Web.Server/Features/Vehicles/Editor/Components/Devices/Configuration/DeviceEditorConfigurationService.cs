using Barret.Core.Areas.Devices.Enums;
using Barret.Web.Server.Shared.Components.DeviceEditors.Tabs;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Configuration
{
    /// <summary>
    /// Service for loading and managing device editor tab configuration from external JSON file.
    /// The JSON file is the single source of truth for all device editor tab configurations.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the DeviceEditorConfigurationService
    /// </remarks>
    /// <param name="logger">The logger</param>
    /// <param name="environment">The host environment</param>
    public class DeviceEditorConfigurationService(
        ILogger<DeviceEditorConfigurationService> logger,
        IHostEnvironment environment) : IDeviceEditorConfigurationService
    {
        private readonly ILogger<DeviceEditorConfigurationService> _logger = logger;
        private readonly IHostEnvironment _environment = environment;
        private DeviceEditorConfiguration? _configuration;
        private readonly object _lock = new();

        /// <summary>
        /// Gets the complete device editor configuration
        /// </summary>
        /// <returns>The device editor configuration</returns>
        public DeviceEditorConfiguration GetConfiguration()
        {
            if (_configuration == null)
            {
                lock (_lock)
                {
                    if (_configuration == null)
                    {
                        LoadConfiguration();
                    }
                }
            }
            return _configuration!;
        }

        /// <summary>
        /// Gets the tab configuration for a specific device role
        /// </summary>
        /// <param name="role">The device role</param>
        /// <returns>The tab configuration for the role, or default configuration if not found</returns>
        public DeviceRoleTabConfiguration GetConfigurationForRole(DeviceRole role)
        {
            var config = GetConfiguration();
            var roleKey = role.ToString();

            if (config.DeviceRoleConfigurations.TryGetValue(roleKey, out var roleConfig))
            {
                return roleConfig;
            }

            _logger.LogDebug("No configuration found for device role {DeviceRole}, using default configuration", role);
            return config.DefaultConfiguration;
        }

        /// <summary>
        /// Reloads the configuration from the JSON file
        /// </summary>
        public void ReloadConfiguration()
        {
            lock (_lock)
            {
                _configuration = null;
                LoadConfiguration();
            }
        }

        /// <summary>
        /// Cached JSON serializer options for performance
        /// </summary>
        private static readonly JsonSerializerOptions JsonOptions = new()
        {
            PropertyNameCaseInsensitive = true,
            ReadCommentHandling = JsonCommentHandling.Skip
        };

        /// <summary>
        /// Loads the configuration from the JSON file
        /// </summary>
        private void LoadConfiguration()
        {
            try
            {
                var configPath = Path.Combine(_environment.ContentRootPath, "Configuration", "DeviceEditorConfiguration.json");

                if (!File.Exists(configPath))
                {
                    _logger.LogError("Configuration file not found at {ConfigPath}. The application requires this file to function properly.", configPath);
                    _configuration = CreateEmergencyFallbackConfiguration();
                    return;
                }

                var jsonContent = File.ReadAllText(configPath);
                var configWrapper = JsonSerializer.Deserialize<DeviceEditorConfigurationWrapper>(jsonContent, JsonOptions);

                if (configWrapper?.DeviceEditorConfiguration == null)
                {
                    _logger.LogError("Failed to deserialize configuration from {ConfigPath}. Invalid JSON structure.", configPath);
                    _configuration = CreateEmergencyFallbackConfiguration();
                    return;
                }

                _configuration = configWrapper.DeviceEditorConfiguration;
                _logger.LogInformation("Device editor configuration loaded successfully from {ConfigPath} with {ConfigCount} device role configurations",
                    configPath, _configuration.DeviceRoleConfigurations.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load device editor configuration from JSON file");
                _configuration = CreateEmergencyFallbackConfiguration();
            }
        }

        /// <summary>
        /// Creates an emergency fallback configuration when the JSON file cannot be loaded.
        /// This provides a minimal working configuration to prevent application crashes.
        /// </summary>
        /// <returns>A minimal emergency fallback configuration</returns>
        private DeviceEditorConfiguration CreateEmergencyFallbackConfiguration()
        {
            return new DeviceEditorConfiguration
            {
                DefaultConfiguration = new DeviceRoleTabConfiguration
                {
                    Tabs = new Dictionary<string, TabConfiguration>
                    {
                        ["general"] = new() { Enabled = true, Order = 1 },
                        ["makeModel"] = new() { Enabled = true, Order = 2 },
                        ["position"] = new() { Enabled = false, Order = 3, Type = "standard" },
                        ["connection"] = new() { Enabled = false, Order = 4 },
                        ["settings"] = new() { Enabled = false, Order = 5 },
                        ["alarms"] = new() { Enabled = false, Order = 6 }
                    }
                },
                DeviceRoleConfigurations = new Dictionary<string, DeviceRoleTabConfiguration>()
            };
        }
    }
}
