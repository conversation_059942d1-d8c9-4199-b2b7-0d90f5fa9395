using Barret.Core.Areas.Devices.Enums;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Configuration
{
    /// <summary>
    /// Service for loading and managing device editor tab configuration
    /// </summary>
    public interface IDeviceEditorConfigurationService
    {
        /// <summary>
        /// Gets the complete device editor configuration
        /// </summary>
        /// <returns>The device editor configuration</returns>
        DeviceEditorConfiguration GetConfiguration();

        /// <summary>
        /// Gets the tab configuration for a specific device role
        /// </summary>
        /// <param name="role">The device role</param>
        /// <returns>The tab configuration for the role, or default configuration if not found</returns>
        DeviceRoleTabConfiguration GetConfigurationForRole(DeviceRole role);

        /// <summary>
        /// Reloads the configuration from the JSON file
        /// </summary>
        void ReloadConfiguration();
    }
}
