using System.Collections.Generic;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Configuration
{
    /// <summary>
    /// Root configuration for device editor tabs loaded from JSON
    /// </summary>
    public class DeviceEditorConfiguration
    {
        /// <summary>
        /// Default configuration used when no specific device role configuration is found
        /// </summary>
        public DeviceRoleTabConfiguration DefaultConfiguration { get; set; } = new();

        /// <summary>
        /// Device role specific configurations keyed by DeviceRole name
        /// </summary>
        public Dictionary<string, DeviceRoleTabConfiguration> DeviceRoleConfigurations { get; set; } = new();
    }

    /// <summary>
    /// Configuration for tabs displayed for a specific device role
    /// </summary>
    public class DeviceRoleTabConfiguration
    {
        /// <summary>
        /// Tab configurations keyed by tab name (general, makeModel, position, connection, settings, alarms)
        /// </summary>
        public Dictionary<string, TabConfiguration> Tabs { get; set; } = new();
    }

    /// <summary>
    /// Configuration for an individual tab
    /// </summary>
    public class TabConfiguration
    {
        /// <summary>
        /// Whether this tab should be displayed
        /// </summary>
        public bool Enabled { get; set; }

        /// <summary>
        /// Display order of the tab (lower numbers appear first)
        /// </summary>
        public int Order { get; set; }

        /// <summary>
        /// Type specification for tabs that have variants (e.g., position tab: "standard" or "maritime")
        /// </summary>
        public string? Type { get; set; }

        /// <summary>
        /// Component type name for settings tabs (e.g., "CameraTab", "RadarTab")
        /// </summary>
        public string? ComponentType { get; set; }
    }

    /// <summary>
    /// Wrapper class for JSON deserialization
    /// </summary>
    public class DeviceEditorConfigurationWrapper
    {
        /// <summary>
        /// The device editor configuration
        /// </summary>
        public DeviceEditorConfiguration DeviceEditorConfiguration { get; set; } = new();
    }

    /// <summary>
    /// Enumeration of supported tab types
    /// </summary>
    public enum TabType
    {
        General,
        MakeModel,
        Position,
        Connection,
        Settings,
        Alarms
    }

    /// <summary>
    /// Enumeration of position tab types
    /// </summary>
    public enum PositionTabType
    {
        Standard,
        Maritime
    }
}
