using Barret.Services.Core.Areas.Vehicles;
using Barret.Services.Core.Areas.Vehicles.Queries;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Web.Server.Features.Shared;
using Microsoft.Extensions.Logging;
using ReactiveUI;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Reactive;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Components.Import.ViewModels
{
    /// <summary>
    /// ViewModel for a vessel with its devices.
    /// </summary>
    public class VesselWithDevicesViewModel : ReactiveObject
    {
        private VesselDto _vessel;
        private bool _isExpanded;
        private bool _isLoaded;
        private List<DeviceDto> _devices = new();

        /// <summary>
        /// Gets or sets the vessel.
        /// </summary>
        public VesselDto Vessel
        {
            get => _vessel;
            set => this.RaiseAndSetIfChanged(ref _vessel, value);
        }

        /// <summary>
        /// Gets or sets whether the vessel is expanded.
        /// </summary>
        public bool IsExpanded
        {
            get => _isExpanded;
            set => this.RaiseAndSetIfChanged(ref _isExpanded, value);
        }

        /// <summary>
        /// Gets or sets whether the vessel's devices are loaded.
        /// </summary>
        public bool IsLoaded
        {
            get => _isLoaded;
            set => this.RaiseAndSetIfChanged(ref _isLoaded, value);
        }

        /// <summary>
        /// Gets or sets the devices.
        /// </summary>
        public List<DeviceDto> Devices
        {
            get => _devices;
            set => this.RaiseAndSetIfChanged(ref _devices, value);
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="VesselWithDevicesViewModel"/> class.
        /// </summary>
        /// <param name="vessel">The vessel.</param>
        public VesselWithDevicesViewModel(VesselDto vessel)
        {
            _vessel = vessel;
        }
    }

    /// <summary>
    /// ViewModel for the device import dialog.
    /// </summary>
    public class DeviceImportDialogViewModel : ViewModelBase
    {
        private readonly IVesselQueryService _vesselQueryService;
        private readonly IVehicleService<Core.Areas.Vehicles.Models.Vessel.Vessel, VesselDto> _vesselService;

        private bool _isVisible;
        private bool _isLoading;
        private ObservableCollection<VesselWithDevicesViewModel> _vessels = new();
        private Dictionary<Guid, bool> _selectedDevices = new();
        private Guid _currentVehicleId;

        /// <summary>
        /// Gets or sets whether the dialog is visible.
        /// </summary>
        public bool IsVisible
        {
            get => _isVisible;
            set => this.RaiseAndSetIfChanged(ref _isVisible, value);
        }

        /// <summary>
        /// Gets or sets whether the dialog is loading data.
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            set => this.RaiseAndSetIfChanged(ref _isLoading, value);
        }

        /// <summary>
        /// Gets or sets the vessels with their devices.
        /// </summary>
        public ObservableCollection<VesselWithDevicesViewModel> Vessels
        {
            get => _vessels;
            set => this.RaiseAndSetIfChanged(ref _vessels, value);
        }

        /// <summary>
        /// Gets or sets the dictionary of selected devices.
        /// </summary>
        public Dictionary<Guid, bool> SelectedDevices
        {
            get => _selectedDevices;
            set => this.RaiseAndSetIfChanged(ref _selectedDevices, value);
        }

        /// <summary>
        /// Gets the command to load vessels.
        /// </summary>
        public ReactiveCommand<Unit, Unit> LoadVesselsCommand { get; }

        /// <summary>
        /// Gets the command to load devices for a vessel.
        /// </summary>
        public ReactiveCommand<VesselWithDevicesViewModel, Unit> LoadDevicesCommand { get; }

        /// <summary>
        /// Gets the command to import selected devices.
        /// </summary>
        public ReactiveCommand<Unit, List<DeviceDto>> ImportDevicesCommand { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceImportDialogViewModel"/> class.
        /// </summary>
        /// <param name="vesselQueryService">The vessel query service.</param>
        /// <param name="vesselService">The vessel service.</param>
        /// <param name="logger">The logger.</param>
        public DeviceImportDialogViewModel(
            IVesselQueryService vesselQueryService,
            IVehicleService<Core.Areas.Vehicles.Models.Vessel.Vessel, VesselDto> vesselService,
            ILogger<DeviceImportDialogViewModel> logger)
            : base(logger)
        {
            _vesselQueryService = vesselQueryService;
            _vesselService = vesselService;

            // Initialize commands
            LoadVesselsCommand = ReactiveCommand.CreateFromTask(LoadVesselsAsync);
            LoadDevicesCommand = ReactiveCommand.CreateFromTask<VesselWithDevicesViewModel, Unit>(LoadDevicesAsync);
            ImportDevicesCommand = ReactiveCommand.CreateFromTask(ImportSelectedDevicesAsync);
        }

        /// <summary>
        /// Initializes the dialog for the specified vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the current vehicle.</param>
        public async Task InitializeAsync(Guid vehicleId)
        {
            _currentVehicleId = vehicleId;
            SelectedDevices.Clear();
            Vessels.Clear();
            await LoadVesselsAsync();
        }

        /// <summary>
        /// Loads the available vessels.
        /// </summary>
        private async Task LoadVesselsAsync()
        {
            try
            {
                IsLoading = true;
                Logger.LogInformation("Loading available vessels for import");

                // Load vessels with device summaries
                var vessels = await _vesselQueryService.SearchVesselsAsync(new VesselFilterDto());

                // Filter out the current vehicle
                var filteredVessels = vessels.Where(v => v.Id != _currentVehicleId).ToList();

                // Create view models for each vessel
                Vessels.Clear();
                foreach (var vessel in filteredVessels)
                {
                    Vessels.Add(new VesselWithDevicesViewModel(vessel));
                }

                // Clear selected devices
                SelectedDevices.Clear();

                Logger.LogInformation("Loaded {Count} vessels for import", Vessels.Count);
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading vessels for import");
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Loads the devices for the specified vessel.
        /// </summary>
        private async Task<Unit> LoadDevicesAsync(VesselWithDevicesViewModel vesselViewModel)
        {
            if (vesselViewModel == null || vesselViewModel.IsLoaded)
            {
                return Unit.Default;
            }

            try
            {
                vesselViewModel.IsLoaded = false;
                Logger.LogInformation("Loading devices for vessel {VesselId}", vesselViewModel.Vessel.Id);

                // Load the vessel with full device tree
                var vessel = await _vesselQueryService.GetVesselWithDeviceTreeAsync(vesselViewModel.Vessel.Id);
                if (vessel != null)
                {
                    // Update the vessel in the view model
                    vesselViewModel.Vessel = vessel;

                    // Get all devices
                    var devices = vessel.GetAllDevices();
                    vesselViewModel.Devices = devices;

                    // Initialize selection state for all devices
                    foreach (var device in devices)
                    {
                        if (!SelectedDevices.ContainsKey(device.Id))
                        {
                            SelectedDevices[device.Id] = false;
                        }
                    }

                    vesselViewModel.IsLoaded = true;
                    Logger.LogInformation("Loaded {Count} devices for vessel {VesselId}", devices.Count, vessel.Id);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error loading devices for vessel {VesselId}", vesselViewModel.Vessel.Id);
            }

            return Unit.Default;
        }

        /// <summary>
        /// Imports the selected devices.
        /// </summary>
        private async Task<List<DeviceDto>> ImportSelectedDevicesAsync()
        {
            try
            {
                IsLoading = true;
                Logger.LogInformation("Importing selected devices");

                // Get the selected device IDs
                var selectedDeviceIds = SelectedDevices
                    .Where(kvp => kvp.Value)
                    .Select(kvp => kvp.Key)
                    .ToList();

                if (selectedDeviceIds.Count == 0)
                {
                    Logger.LogWarning("No devices selected for import");
                    return new List<DeviceDto>();
                }

                // Use the VehicleService to create copies of the selected devices
                var result = await _vesselService.CopyDevicesAsync(selectedDeviceIds);

                if (!result.Success)
                {
                    Logger.LogError("Failed to copy devices: {ErrorMessage}", result.ErrorMessage);
                    return new List<DeviceDto>();
                }

                // Get the copied devices
                var copiedDevices = result.Data;

                // Set the current vehicle ID for each copied device
                foreach (var device in copiedDevices)
                {
                    device.VehicleId = _currentVehicleId;
                }

                Logger.LogInformation("Copied {Count} devices for import", copiedDevices.Count);
                return copiedDevices;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, "Error importing devices");
                return new List<DeviceDto>();
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Toggles the selection state of a device.
        /// </summary>
        /// <param name="deviceId">The ID of the device.</param>
        public void ToggleDeviceSelection(Guid deviceId)
        {
            if (SelectedDevices.ContainsKey(deviceId))
            {
                SelectedDevices[deviceId] = !SelectedDevices[deviceId];
                this.RaisePropertyChanged(nameof(SelectedDevices));
            }
        }

        /// <summary>
        /// Gets the number of selected devices.
        /// </summary>
        public int GetSelectedDeviceCount()
        {
            return SelectedDevices.Count(kvp => kvp.Value);
        }

        /// <summary>
        /// Toggles the expanded state of a vessel and loads its devices if needed.
        /// </summary>
        /// <param name="vesselViewModel">The vessel view model.</param>
        public async Task ToggleVesselExpandedAsync(VesselWithDevicesViewModel vesselViewModel)
        {
            if (vesselViewModel == null)
            {
                return;
            }

            // Toggle the expanded state
            vesselViewModel.IsExpanded = !vesselViewModel.IsExpanded;

            // If expanding and devices are not loaded, load them
            if (vesselViewModel.IsExpanded && !vesselViewModel.IsLoaded)
            {
                await LoadDevicesAsync(vesselViewModel);
            }
        }
    }
}
