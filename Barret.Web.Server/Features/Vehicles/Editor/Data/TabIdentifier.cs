using Barret.Core.Areas.DeviceGroups;
using System;

namespace Barret.Web.Server.Features.Vehicles.Editor.Data
{
    /// <summary>
    /// Represents a tab identifier in the vehicle editor.
    /// This replaces the VehicleEditorTab enum to allow for dynamic tab generation.
    /// </summary>
    public class TabIdentifier
    {
        /// <summary>
        /// Special tab identifier for the Basic Info tab.
        /// </summary>
        public static readonly string BasicInfo = "BasicInfo";

        /// <summary>
        /// Special tab identifier for the Dimensions tab.
        /// </summary>
        public static readonly string Dimensions = "Dimensions";

        /// <summary>
        /// Prefix for device group tabs.
        /// </summary>
        private const string DeviceGroupPrefix = "DeviceGroup:";

        /// <summary>
        /// Gets the unique identifier for this tab.
        /// </summary>
        public string Id { get; }

        /// <summary>
        /// Gets a value indicating whether this tab represents a device group.
        /// </summary>
        public bool IsDeviceGroupTab { get; }

        /// <summary>
        /// Gets the device group type if this is a device group tab, otherwise null.
        /// </summary>
        public DeviceGroups? DeviceGroupType { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="TabIdentifier"/> class for a special tab.
        /// </summary>
        /// <param name="id">The tab identifier.</param>
        private TabIdentifier(string id)
        {
            Id = id;
            IsDeviceGroupTab = false;
            DeviceGroupType = null;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="TabIdentifier"/> class for a device group tab.
        /// </summary>
        /// <param name="deviceGroupType">The device group type.</param>
        private TabIdentifier(DeviceGroups deviceGroupType)
        {
            DeviceGroupType = deviceGroupType;
            IsDeviceGroupTab = true;
            Id = $"{DeviceGroupPrefix}{deviceGroupType}";
        }

        /// <summary>
        /// Creates a TabIdentifier for the Basic Info tab.
        /// </summary>
        /// <returns>A TabIdentifier for the Basic Info tab.</returns>
        public static TabIdentifier ForBasicInfo() => new TabIdentifier(BasicInfo);

        /// <summary>
        /// Creates a TabIdentifier for the Dimensions tab.
        /// </summary>
        /// <returns>A TabIdentifier for the Dimensions tab.</returns>
        public static TabIdentifier ForDimensions() => new TabIdentifier(Dimensions);

        /// <summary>
        /// Creates a TabIdentifier for a device group tab.
        /// </summary>
        /// <param name="deviceGroupType">The device group type.</param>
        /// <returns>A TabIdentifier for the specified device group.</returns>
        public static TabIdentifier ForDeviceGroup(DeviceGroups deviceGroupType) => new TabIdentifier(deviceGroupType);

        /// <summary>
        /// Parses a string into a TabIdentifier.
        /// </summary>
        /// <param name="id">The string to parse.</param>
        /// <returns>A TabIdentifier.</returns>
        /// <exception cref="ArgumentException">Thrown if the string is not a valid tab identifier.</exception>
        public static TabIdentifier Parse(string id)
        {
            if (id == BasicInfo) return ForBasicInfo();
            if (id == Dimensions) return ForDimensions();
            if (id.StartsWith(DeviceGroupPrefix))
            {
                var deviceGroupTypeName = id.Substring(DeviceGroupPrefix.Length);
                if (Enum.TryParse<DeviceGroups>(deviceGroupTypeName, out var deviceGroupType))
                {
                    return ForDeviceGroup(deviceGroupType);
                }
            }
            throw new ArgumentException($"Invalid tab identifier: {id}");
        }

        /// <summary>
        /// Determines whether the specified object is equal to the current object.
        /// </summary>
        /// <param name="obj">The object to compare with the current object.</param>
        /// <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
        public override bool Equals(object? obj) => obj is TabIdentifier other && Id == other.Id;

        /// <summary>
        /// Returns the hash code for this instance.
        /// </summary>
        /// <returns>A hash code for the current object.</returns>
        public override int GetHashCode() => Id.GetHashCode();

        /// <summary>
        /// Returns a string that represents the current object.
        /// </summary>
        /// <returns>A string that represents the current object.</returns>
        public override string ToString() => Id;

        /// <summary>
        /// Determines whether two TabIdentifier instances are equal.
        /// </summary>
        /// <param name="left">The first TabIdentifier to compare.</param>
        /// <param name="right">The second TabIdentifier to compare.</param>
        /// <returns>true if the TabIdentifiers are equal; otherwise, false.</returns>
        public static bool operator ==(TabIdentifier? left, TabIdentifier? right) =>
            left is null ? right is null : left.Equals(right);

        /// <summary>
        /// Determines whether two TabIdentifier instances are not equal.
        /// </summary>
        /// <param name="left">The first TabIdentifier to compare.</param>
        /// <param name="right">The second TabIdentifier to compare.</param>
        /// <returns>true if the TabIdentifiers are not equal; otherwise, false.</returns>
        public static bool operator !=(TabIdentifier? left, TabIdentifier? right) => !(left == right);
    }
}
