namespace Barret.Web.Server.Features.Vehicles.Editor.Data
{
    /// <summary>
    /// Metadata for a tab in the vehicle editor.
    /// </summary>
    public class TabMetadata
    {
        /// <summary>
        /// Gets or sets the tab identifier.
        /// </summary>
        public TabIdentifier TabIdentifier { get; set; } = TabIdentifier.ForBasicInfo();

        /// <summary>
        /// Gets or sets the name of the tab.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the icon CSS class for the tab.
        /// </summary>
        public string IconCssClass { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the display order of the tab.
        /// </summary>
        public int DisplayOrder { get; set; }

        /// <summary>
        /// Gets a value indicating whether the tab is a device group tab.
        /// </summary>
        public bool IsDeviceGroupTab => TabIdentifier.IsDeviceGroupTab;
    }
}
