using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles.Vessels;
using Barret.Core.Areas.DeviceGroups;


namespace Barret.Web.Server.Features.Vehicles.Editor.Data
{
    /// <summary>
    /// Data class for the vehicle editor.
    /// </summary>
    public class VehicleEditorData
    {
        /// <summary>
        /// Gets or sets the vessel being edited.
        /// </summary>
        public VesselDto? Vessel { get; set; }

        /// <summary>
        /// Gets or sets the active tab.
        /// </summary>
        public TabIdentifier ActiveTab { get; set; } = TabIdentifier.ForBasicInfo();

        /// <summary>
        /// Gets or sets the active device group type.
        /// </summary>
        public DeviceGroups? ActiveDeviceGroupType { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the editor is in loading state.
        /// </summary>
        public bool IsLoading { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the editor is in saving state.
        /// </summary>
        public bool IsSaving { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the editor has unsaved changes.
        /// </summary>
        public bool IsDirty { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether this is a new vessel.
        /// </summary>
        public bool IsNewVessel { get; set; }

        /// <summary>
        /// Gets the tab metadata for the specified tab identifier.
        /// </summary>
        /// <param name="tabIdentifier">The tab identifier.</param>
        /// <returns>The tab metadata.</returns>
        public static TabMetadata GetTabMetadata(TabIdentifier tabIdentifier)
        {
            if (tabIdentifier.Id == TabIdentifier.BasicInfo)
            {
                return new TabMetadata
                {
                    TabIdentifier = tabIdentifier,
                    Name = "Basic Info",
                    IconCssClass = "bi bi-info-circle",
                    DisplayOrder = 0
                };
            }
            else if (tabIdentifier.Id == TabIdentifier.Dimensions)
            {
                return new TabMetadata
                {
                    TabIdentifier = tabIdentifier,
                    Name = "Dimensions",
                    IconCssClass = "bi bi-rulers",
                    DisplayOrder = 1
                };
            }
            else if (tabIdentifier.IsDeviceGroupTab && tabIdentifier.DeviceGroupType != null)
            {
                // For device group tabs, use the device group type to get the display name and icon
                DeviceGroups groupType = tabIdentifier.DeviceGroupType.Value;
                return new TabMetadata
                {
                    TabIdentifier = tabIdentifier,
                    Name = groupType.GetDisplayName(),
                    IconCssClass = groupType.GetIconCssClass(),
                    DisplayOrder = groupType.GetDisplayOrder()
                };
            }
            else
            {
                // Fallback for unknown tabs
                return new TabMetadata
                {
                    TabIdentifier = tabIdentifier,
                    Name = "Unknown",
                    IconCssClass = "bi bi-question-circle",
                    DisplayOrder = 99
                };
            }
        }

        /// <summary>
        /// Gets the tab metadata for a device group.
        /// </summary>
        /// <param name="deviceGroupType">The device group type.</param>
        /// <returns>The tab metadata.</returns>
        public static TabMetadata GetTabMetadataForDeviceGroup(DeviceGroups deviceGroupType)
        {
            var tabIdentifier = TabIdentifier.ForDeviceGroup(deviceGroupType);
            return GetTabMetadata(tabIdentifier);
        }

        /// <summary>
        /// Gets the tab identifier for the specified device group type.
        /// </summary>
        /// <param name="deviceGroupType">The device group type.</param>
        /// <returns>The tab identifier.</returns>
        public static TabIdentifier GetTabForDeviceGroup(DeviceGroups deviceGroupType)
        {
            return TabIdentifier.ForDeviceGroup(deviceGroupType);
        }
    }
}
