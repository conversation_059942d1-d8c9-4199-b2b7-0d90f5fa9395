using Barret.Core.Areas.DeviceGroups;
using Barret.Shared.DTOs.Devices;
using Barret.Web.Server.Extensions;
using Barret.Web.Server.Features.Vehicles.Editor.Data;
using Barret.Web.Server.Features.Vehicles.Editor.ViewModels;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Import.Views;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Export;
using Barret.Web.Server.Features.Shared;
using Microsoft.AspNetCore.Components;
using Microsoft.JSInterop;
using Radzen;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Barret.Web.Server.Features.Vehicles.Editor.Views
{
    /// <summary>
    /// Base class for the vehicle editor view.
    /// </summary>
    public class VehicleEditorViewBase : ComponentBase, IDisposable, IAsyncDisposable
    {
        private bool _hasInitialized = false;
        /// <summary>
        /// Gets or sets the vessel ID.
        /// </summary>
        [Parameter]
        public Guid? Id { get; set; }

        /// <summary>
        /// Gets or sets the vessel name from query parameter.
        /// </summary>
        [Parameter]
        [SupplyParameterFromQuery(Name = "name")]
        public string? VesselName { get; set; }

        /// <summary>
        /// Gets or sets the vessel identifier from query parameter.
        /// </summary>
        [Parameter]
        [SupplyParameterFromQuery(Name = "vesselId")]
        public string? VesselIdentifier { get; set; }

        /// <summary>
        /// Gets or sets the MMSI from query parameter.
        /// </summary>
        [Parameter]
        [SupplyParameterFromQuery(Name = "mmsi")]
        public string? MMSI { get; set; }

        /// <summary>
        /// Gets or sets the view model.
        /// </summary>
        [Inject]
        public VehicleEditorViewModel ViewModel { get; set; } = null!;

        /// <summary>
        /// Gets or sets the navigation manager.
        /// </summary>
        [Inject]
        public NavigationManager NavigationManager { get; set; } = null!;

        /// <summary>
        /// Gets or sets the JS runtime.
        /// </summary>
        [Inject]
        public IJSRuntime JSRuntime { get; set; } = null!;

        /// <summary>
        /// Gets or sets the dialog service.
        /// </summary>
        [Inject]
        public DialogService DialogService { get; set; } = null!;

        /// <summary>
        /// Gets or sets a value indicating whether the sidebar is open.
        /// </summary>
        protected bool isSidebarOpen = false;

        /// <summary>
        /// Gets or sets a value indicating whether to show the leave confirmation dialog.
        /// </summary>
        protected bool showLeaveConfirmation = false;

        /// <summary>
        /// Gets or sets a value indicating whether to show the export dialog.
        /// </summary>
        protected bool exportDialogVisible = false;

        // Export and import dialogs are now handled via service-based pattern

        /// <summary>
        /// Gets or sets the pending navigation URL.
        /// </summary>
        protected string? pendingNavigation = null;

        private DotNetObjectReference<VehicleEditorViewBase>? _dotNetObjectReference;

        /// <summary>
        /// Toggles the sidebar.
        /// </summary>
        protected async Task ToggleSidebar()
        {
            isSidebarOpen = !isSidebarOpen;

            // Call the JavaScript function to toggle the sidebar and handle the overlay
            await JSRuntime.InvokeVoidAsync("toggleSidebar");
        }

        /// <summary>
        /// Initializes the component.
        /// </summary>
        protected override void OnInitialized()
        {
            // Component initialization logic can go here if needed
        }

        /// <summary>
        /// Initializes the component asynchronously.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected override async Task OnInitializedAsync()
        {
            // Initialize the ViewModel with the current vessel ID
            await ViewModel.InitializeAsync(Id);

            // Apply query parameters if creating a new vessel
            if (Id == null && ViewModel.Vessel != null)
            {
                if (!string.IsNullOrWhiteSpace(VesselName))
                {
                    ViewModel.Vessel.Name = VesselName;
                }

                if (!string.IsNullOrWhiteSpace(VesselIdentifier))
                {
                    ViewModel.Vessel.VehicleId = VesselIdentifier;
                }

                if (!string.IsNullOrWhiteSpace(MMSI))
                {
                    ViewModel.Vessel.MMSI = MMSI;
                }
            }

            // Mark that we have completed initial initialization
            _hasInitialized = true;
        }

        /// <summary>
        /// Called when parameters are set or changed.
        /// This handles navigation to different vessel IDs.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected override async Task OnParametersSetAsync()
        {
            // Skip if this is the initial load (OnInitializedAsync handles that)
            if (!_hasInitialized)
            {
                return;
            }

            // Skip if we don't have a vessel loaded yet
            if (ViewModel.Vessel == null)
            {
                return;
            }

            // Only reinitialize if this is a different vessel ID than what's currently loaded
            if (Id != null && ViewModel.Vessel.Id != Id)
            {
                // Different vessel ID - need to load new vessel
                await ViewModel.InitializeAsync(Id);
            }
            else if (Id == null && ViewModel.Vessel.Id != Guid.Empty)
            {
                // Navigating from existing vessel to create new vessel
                await ViewModel.InitializeAsync(Id);

                // Apply query parameters for new vessel
                if (ViewModel.Vessel != null)
                {
                    if (!string.IsNullOrWhiteSpace(VesselName))
                    {
                        ViewModel.Vessel.Name = VesselName;
                    }

                    if (!string.IsNullOrWhiteSpace(VesselIdentifier))
                    {
                        ViewModel.Vessel.VehicleId = VesselIdentifier;
                    }

                    if (!string.IsNullOrWhiteSpace(MMSI))
                    {
                        ViewModel.Vessel.MMSI = MMSI;
                    }
                }
            }
            // If Id == ViewModel.Vessel.Id, we're returning to the same vessel - no need to reload
        }

        /// <summary>
        /// Called after the component has been rendered.
        /// </summary>
        /// <param name="firstRender">True if this is the first time the component has been rendered.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            if (firstRender)
            {
                // Register beforeunload event to prevent accidental navigation
                await JSRuntime.InvokeVoidAsync("registerBeforeUnload");

                // Register event listener for showing the import dialog
                _dotNetObjectReference = DotNetObjectReference.Create(this);
                await JSRuntime.InvokeVoidAsync("registerImportDialogEvent", _dotNetObjectReference);
            }
        }

        /// <summary>
        /// Shows the import dialog. Called from JavaScript.
        /// </summary>
        [JSInvokable]
        public async Task ShowImportDialogFromJS()
        {
            await ShowImportDialog();
            StateHasChanged();
        }

        /// <summary>
        /// Handles tab selection.
        /// </summary>
        /// <param name="tabIdentifier">The selected tab identifier.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected virtual async Task HandleTabSelected(TabIdentifier tabIdentifier)
        {
            // Update the active tab in the ViewModel
            ViewModel.ActiveTab = tabIdentifier;

            // Close the sidebar on mobile after selection
            if (isSidebarOpen)
            {
                await ToggleSidebar();
            }
        }

        /// <summary>
        /// Gets the CSS classes for the save button based on its state.
        /// </summary>
        /// <returns>The CSS classes for the save button.</returns>
        protected string GetSaveButtonClasses()
        {
            bool isDisabled = !ViewModel.IsNewVessel && !ViewModel.IsDirty || ViewModel.IsSaving;

            if (isDisabled)
            {
                return "bg-gray-300 text-gray-500 cursor-not-allowed";
            }

            return "bg-gray-900 hover:bg-gray-800 text-white";
        }

        /// <summary>
        /// Gets the tooltip text for the save button based on its state.
        /// </summary>
        /// <returns>The tooltip text for the save button.</returns>
        protected string GetSaveButtonTooltip()
        {
            if (ViewModel.IsSaving)
            {
                return "Saving changes...";
            }

            if (!ViewModel.IsNewVessel && !ViewModel.IsDirty)
            {
                return "No changes to save";
            }

            return "Save changes";
        }

        /// <summary>
        /// Handles device addition.
        /// </summary>
        /// <param name="device">The device to add.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task HandleDeviceAdded(DeviceDto device)
        {
            if (device != null)
            {
                // Add the device to the appropriate collection
                var groupType = device.DeviceGroupType;
                if (groupType.HasValue)
                {
                    await ViewModel.AddDeviceCommand.Execute((device, groupType.Value)).ToTask();
                }
            }
        }

        /// <summary>
        /// Handles device removal.
        /// </summary>
        /// <param name="deviceId">The device ID.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task HandleDeviceRemoved(Guid deviceId)
        {
            // Find the device and its group
            foreach (var kvp in ViewModel.DeviceCollections)
            {
                var groupType = kvp.Key;
                var collection = kvp.Value;

                var device = collection.FirstOrDefault(d => d.Id == deviceId);
                if (device != null)
                {
                    await ViewModel.RemoveDeviceCommand.Execute((deviceId, groupType)).ToTask();
                    break;
                }
            }
        }

        /// <summary>
        /// Handles device update.
        /// </summary>
        /// <param name="device">The device to update.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task HandleDeviceUpdated(DeviceDto device)
        {
            if (device != null)
            {
                // Update the device in the appropriate collection
                var groupType = device.DeviceGroupType;
                if (groupType.HasValue)
                {
                    await ViewModel.UpdateDeviceCommand.Execute((device, groupType.Value)).ToTask();
                }
            }
        }

        /// <summary>
        /// Saves the vessel.
        /// </summary>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task SaveVessel()
        {
            if (ViewModel.IsSaving || (!ViewModel.IsNewVessel && !ViewModel.IsDirty))
            {
                return;
            }

            await ViewModel.SaveCommand.Execute().ToTask();
        }

        /// <summary>
        /// Handles devices imported from service-based dialog.
        /// </summary>
        /// <param name="devices">The imported devices.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task HandleDevicesImported(IEnumerable<DeviceDto> devices)
        {
            if (devices != null)
            {
                await ViewModel.ImportDevicesCommand.Execute(devices.ToList()).ToTask();
            }
        }

        /// <summary>
        /// Navigates back to the vehicles list.
        /// </summary>
        protected virtual void NavigateBack()
        {
            if (ViewModel.IsDirty)
            {
                pendingNavigation = "/vehicles";
                showLeaveConfirmation = true;
            }
            else
            {
                NavigationManager.NavigateTo("/vehicles");
            }
        }

        /// <summary>
        /// Confirms leaving the page.
        /// </summary>
        protected void ConfirmLeave()
        {
            showLeaveConfirmation = false;
            if (!string.IsNullOrEmpty(pendingNavigation))
            {
                NavigationManager.NavigateTo(pendingNavigation);
                pendingNavigation = null;
            }
        }

        /// <summary>
        /// Cancels leaving the page.
        /// </summary>
        protected void CancelLeave()
        {
            showLeaveConfirmation = false;
            pendingNavigation = null;
        }

        /// <summary>
        /// Shows the export dialog using service-based pattern.
        /// </summary>
        protected async Task ShowExportDialog()
        {
            if (ViewModel.Vessel == null) return;

            try
            {
                await DialogService.OpenAsync<ConfigExportDialog>(
                    "Export Configuration",
                    new Dictionary<string, object>
                    {
                        { "VesselId", ViewModel.Vessel.Id }
                    },
                    new DialogOptions()
                    {
                        Width = "600px",
                        Height = "auto",
                        Resizable = false,
                        Draggable = true,
                        CloseDialogOnEsc = true,
                        CloseDialogOnOverlayClick = false,
                        CssClass = "barret-complex-dialog"
                    });
            }
            catch (Exception ex)
            {
                // Log error but don't throw
                Console.WriteLine($"Error opening export dialog: {ex.Message}");
            }
        }

        /// <summary>
        /// Shows the import dialog using service-based pattern.
        /// </summary>
        protected async Task ShowImportDialog()
        {
            if (ViewModel.Vessel == null) return;

            try
            {
                var result = await DialogService.OpenAsync<DeviceImportDialogView>(
                    "Import Devices",
                    new Dictionary<string, object>
                    {
                        { "VehicleId", ViewModel.Vessel.Id }
                    },
                    new DialogOptions()
                    {
                        Width = "900px",
                        Height = "auto",
                        Resizable = false,
                        Draggable = true,
                        CloseDialogOnEsc = true,
                        CloseDialogOnOverlayClick = false,
                        CssClass = "barret-complex-dialog"
                    });

                if (result is DeviceImportDialogViewBase.DeviceImportResult importResult)
                {
                    await HandleDevicesImported(importResult.ImportedDevices);
                }
            }
            catch (Exception ex)
            {
                // Log error but don't throw
                Console.WriteLine($"Error opening import dialog: {ex.Message}");
            }
        }

        /// <summary>
        /// Disposes of the component synchronously.
        /// </summary>
        public void Dispose()
        {
            // Dispose of the DotNetObjectReference
            _dotNetObjectReference?.Dispose();
            _dotNetObjectReference = null;

            // Dispose of the ViewModel to ensure proper cleanup
            ViewModel?.Dispose();

            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes of the component asynchronously.
        /// </summary>
        public async ValueTask DisposeAsync()
        {
            // Unregister JavaScript event handlers
            try
            {
                if (_dotNetObjectReference != null)
                {
                    await JSRuntime.InvokeVoidAsync("unregisterImportDialogEvent");
                    await JSRuntime.InvokeVoidAsync("unregisterBeforeUnload");
                }
            }
            catch (Exception ex)
            {
                // Log but don't throw during disposal
                Console.WriteLine($"Error during async disposal: {ex.Message}");
            }

            // Call synchronous dispose for the rest
            Dispose();
        }
    }
}
