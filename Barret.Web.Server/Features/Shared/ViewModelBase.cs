using System;
using System.ComponentModel;
using System.Reactive;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Runtime.CompilerServices;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using ReactiveUI;
using ReactiveUI.Fody.Helpers;

namespace Barret.Web.Server.Features.Shared
{
    /// <summary>
    /// Base class for all ViewModels in the application.
    /// Implements ReactiveUI patterns with Fody support for reactive properties and commands.
    /// </summary>
    public abstract class ViewModelBase : ReactiveObject, IDisposable
    {
        private readonly CompositeDisposable _disposables = new();

        /// <summary>
        /// Gets the logger for this ViewModel.
        /// </summary>
        protected ILogger Logger { get; }

        /// <summary>
        /// Gets the composite disposable for managing subscriptions.
        /// </summary>
        protected CompositeDisposable Disposables => _disposables;

        /// <summary>
        /// Gets or sets whether the ViewModel is loading data.
        /// </summary>
        [Reactive]
        public bool IsLoading { get; protected set; }

        /// <summary>
        /// Gets or sets whether the ViewModel has unsaved changes.
        /// </summary>
        [Reactive]
        public bool IsDirty { get; protected set; }

        /// <summary>
        /// Gets or sets the error message if an operation fails.
        /// </summary>
        [Reactive]
        public string ErrorMessage { get; protected set; } = string.Empty;

        /// <summary>
        /// Gets or sets whether the ViewModel has an error.
        /// </summary>
        [Reactive]
        public bool HasError { get; protected set; }

        /// <summary>
        /// Initializes a new instance of the ViewModelBase class.
        /// </summary>
        /// <param name="logger">The logger to use.</param>
        protected ViewModelBase(ILogger logger)
        {
            Logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Event that is raised when a property value changes.
        /// </summary>
        public event PropertyChangedEventHandler? PropertyChanged;

        /// <summary>
        /// Raises the PropertyChanged event for the specified property.
        /// </summary>
        /// <param name="propertyName">The name of the property that changed.</param>
        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// Sets the value of a property and raises the PropertyChanged event if the value changed.
        /// </summary>
        /// <typeparam name="T">The type of the property.</typeparam>
        /// <param name="field">The field to set.</param>
        /// <param name="value">The new value.</param>
        /// <param name="propertyName">The name of the property.</param>
        /// <returns>True if the value changed, false otherwise.</returns>
        protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
        {
            if (Equals(field, value))
            {
                return false;
            }

            field = value;
            OnPropertyChanged(propertyName);
            return true;
        }

        /// <summary>
        /// Executes an asynchronous operation and logs any exceptions.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to log if an exception occurs.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task ExecuteWithErrorHandlingAsync(Func<Task> operation, string errorMessage)
        {
            try
            {
                await operation();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, errorMessage);
                throw;
            }
        }

        /// <summary>
        /// Executes an asynchronous operation that returns a value and logs any exceptions.
        /// </summary>
        /// <typeparam name="T">The type of the return value.</typeparam>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to log if an exception occurs.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        protected async Task<T> ExecuteWithErrorHandlingAsync<T>(Func<Task<T>> operation, string errorMessage)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, errorMessage);
                throw;
            }
        }

        /// <summary>
        /// Marks the ViewModel as dirty.
        /// </summary>
        public virtual void MarkAsDirty()
        {
            IsDirty = true;
        }

        /// <summary>
        /// Marks the ViewModel as clean.
        /// </summary>
        public virtual void MarkAsClean()
        {
            IsDirty = false;
        }

        /// <summary>
        /// Sets an error message and marks the ViewModel as having an error.
        /// </summary>
        /// <param name="message">The error message.</param>
        protected virtual void SetError(string message)
        {
            ErrorMessage = message;
            HasError = !string.IsNullOrEmpty(message);
        }

        /// <summary>
        /// Clears the error message and marks the ViewModel as not having an error.
        /// </summary>
        protected virtual void ClearError()
        {
            ErrorMessage = string.Empty;
            HasError = false;
        }

        /// <summary>
        /// Executes an operation with loading state and error handling.
        /// </summary>
        /// <typeparam name="T">The type of the result.</typeparam>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to set if the operation fails.</param>
        /// <returns>The result of the operation, or default if the operation fails.</returns>
        protected async Task<T?> ExecuteWithLoadingAsync<T>(Func<Task<T>> operation, string errorMessage)
        {
            try
            {
                IsLoading = true;
                ClearError();

                return await operation();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, errorMessage);
                SetError($"{errorMessage}: {ex.Message}");
                return default;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Executes an operation with loading state and error handling.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to set if the operation fails.</param>
        /// <returns>True if the operation succeeds, false otherwise.</returns>
        protected async Task<bool> ExecuteWithLoadingAsync(Func<Task> operation, string errorMessage)
        {
            try
            {
                IsLoading = true;
                ClearError();

                await operation();
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, errorMessage);
                SetError($"{errorMessage}: {ex.Message}");
                return false;
            }
            finally
            {
                IsLoading = false;
            }
        }

        /// <summary>
        /// Creates a command that executes an operation with loading state and error handling.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="canExecute">An observable that determines when the command can execute.</param>
        /// <param name="errorMessage">The error message to set if the operation fails.</param>
        /// <returns>A ReactiveCommand that executes the operation.</returns>
        protected ReactiveCommand<Unit, Unit> CreateCommand(
            Func<Task> operation,
            IObservable<bool>? canExecute = null,
            string errorMessage = "Operation failed")
        {
            // Default can-execute observable is !IsLoading
            canExecute ??= this.WhenAnyValue(x => x.IsLoading).Select(isLoading => !isLoading);

            // Create the command
            var command = ReactiveCommand.CreateFromTask(
                async () =>
                {
                    try
                    {
                        IsLoading = true;
                        ClearError();
                        await operation();
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, errorMessage);
                        SetError($"{errorMessage}: {ex.Message}");
                        throw;
                    }
                    finally
                    {
                        IsLoading = false;
                    }
                },
                canExecute);

            // Handle errors
            command.ThrownExceptions
                .Subscribe(ex =>
                {
                    Logger.LogError(ex, errorMessage);
                    SetError($"{errorMessage}: {ex.Message}");
                })
                .DisposeWith(_disposables);

            return command;
        }

        /// <summary>
        /// Creates a command that executes an operation with loading state and error handling.
        /// </summary>
        /// <typeparam name="TParam">The type of the parameter.</typeparam>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="canExecute">An observable that determines when the command can execute.</param>
        /// <param name="errorMessage">The error message to set if the operation fails.</param>
        /// <returns>A ReactiveCommand that executes the operation.</returns>
        protected ReactiveCommand<TParam, Unit> CreateCommand<TParam>(
            Func<TParam, Task> operation,
            IObservable<bool>? canExecute = null,
            string errorMessage = "Operation failed")
        {
            // Default can-execute observable is !IsLoading
            canExecute ??= this.WhenAnyValue(x => x.IsLoading).Select(isLoading => !isLoading);

            // Create the command
            var command = ReactiveCommand.CreateFromTask<TParam>(
                async param =>
                {
                    try
                    {
                        IsLoading = true;
                        ClearError();

                        await operation(param);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(ex, errorMessage);
                        SetError($"{errorMessage}: {ex.Message}");
                        throw;
                    }
                    finally
                    {
                        IsLoading = false;
                    }
                },
                canExecute);

            // Handle errors
            command.ThrownExceptions
                .Subscribe(ex =>
                {
                    Logger.LogError(ex, errorMessage);
                    SetError($"{errorMessage}: {ex.Message}");
                })
                .DisposeWith(_disposables);

            return command;
        }

        /// <summary>
        /// Disposes the ViewModel.
        /// </summary>
        public virtual void Dispose()
        {
            _disposables.Dispose();
            GC.SuppressFinalize(this);
        }
    }
}
