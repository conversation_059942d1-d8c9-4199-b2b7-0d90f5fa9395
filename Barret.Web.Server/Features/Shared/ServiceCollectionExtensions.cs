using Barret.Web.Server.Features.Home.ViewModels;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Services;
using Barret.Web.Server.Features.Vehicles.Editor.Components.Devices.Configuration;
using Barret.Web.Server.Features.Vehicles.Editor.ViewModels;
using Barret.Web.Server.Features.Vehicles.List.ViewModels;
using Microsoft.Extensions.DependencyInjection;

namespace Barret.Web.Server.Features.Shared
{
    /// <summary>
    /// Extension methods for IServiceCollection to register services.
    /// </summary>
    public static class ServiceCollectionExtensions
    {
        /// <summary>
        /// Adds the services required for the frontend.
        /// </summary>
        /// <param name="services">The service collection.</param>
        /// <returns>The service collection.</returns>
        public static IServiceCollection AddFrontendServices(this IServiceCollection services)
        {
            // Register Home ViewModels
            services.AddScoped<IHomeViewModelFactory, HomeViewModelFactory>();

            // Register Vehicle ViewModels
            services.AddScoped<IVehicleListViewModelFactory, VehicleListViewModelFactory>();

            // Register Vehicle Editor Services
            services.AddScoped<VehicleEditorViewModel>();

            // Register Device Editor Configuration Services
            services.AddSingleton<IDeviceEditorConfigurationService, DeviceEditorConfigurationService>();

            // Register Device Editor Services
            // DeviceEditorService is now registered in Program.cs
            services.AddScoped<DeviceEditorTabService>(); // Uses the Features namespace version with configuration support

            return services;
        }
    }
}
