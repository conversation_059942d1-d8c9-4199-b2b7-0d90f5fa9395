using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Reactive.Disposables;
using System.Reactive.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using ReactiveUI;

namespace Barret.Web.Server.Features.Shared
{
    /// <summary>
    /// Base class for all View code-behind files in the application.
    /// Provides common functionality for component lifecycle management,
    /// ReactiveUI integration, and resource management.
    /// </summary>
    /// <typeparam name="TViewModel">The type of the ViewModel.</typeparam>
    public abstract class ViewBase<TViewModel> : ComponentBase, IDisposable, IAsyncDisposable
        where TViewModel : ViewModelBase
    {
        private bool _isDisposed;
        private bool _isInitialized;
        private bool _isFirstRender = true;

        /// <summary>
        /// Composite disposable for managing subscriptions.
        /// </summary>
        protected readonly CompositeDisposable Disposables = new();

        /// <summary>
        /// Gets or sets the logger for this view.
        /// </summary>
        [Inject]
        protected ILogger<ViewBase<TViewModel>> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the ViewModel for this view.
        /// </summary>
        [Parameter]
        public TViewModel ViewModel { get; set; } = null!;

        /// <summary>
        /// Method called when the component is initialized.
        /// </summary>
        protected override void OnInitialized()
        {
            Logger.LogDebug("Initializing {ComponentType}", GetType().Name);

            base.OnInitialized();

            if (ViewModel == null)
            {
                throw new InvalidOperationException($"ViewModel of type {typeof(TViewModel).Name} is required.");
            }

            // Set up any initial subscriptions here
            SetupSubscriptions();

            _isInitialized = true;
        }

        /// <summary>
        /// Method called when the component parameters are set.
        /// </summary>
        protected override void OnParametersSet()
        {
            base.OnParametersSet();

            if (ViewModel == null)
            {
                throw new InvalidOperationException($"ViewModel of type {typeof(TViewModel).Name} is required.");
            }

            // If parameters have changed and the component is already initialized,
            // you might need to update subscriptions or perform other actions
            if (_isInitialized)
            {
                // Handle parameter changes here
                HandleParameterChanges();
            }
        }

        /// <summary>
        /// Method called after the component is rendered.
        /// </summary>
        /// <param name="firstRender">True if this is the first time the component has been rendered.</param>
        protected override async Task OnAfterRenderAsync(bool firstRender)
        {
            await base.OnAfterRenderAsync(firstRender);

            if (firstRender)
            {
                _isFirstRender = false;
                await OnFirstRenderAsync();
            }
        }

        /// <summary>
        /// Method called after the component is rendered for the first time.
        /// Override this method to perform one-time initialization that requires the component to be rendered.
        /// </summary>
        protected virtual Task OnFirstRenderAsync()
        {
            return Task.CompletedTask;
        }

        /// <summary>
        /// Sets up subscriptions to ViewModel properties.
        /// Override this method to set up subscriptions to ViewModel properties.
        /// </summary>
        protected virtual void SetupSubscriptions()
        {
            // Example: Subscribe to ViewModel property changes
            // this.WhenAnyValue(x => x.ViewModel.SomeProperty)
            //     .Subscribe(_ => StateHasChanged())
            //     .DisposeWith(Disposables);
        }

        /// <summary>
        /// Handles parameter changes.
        /// Override this method to handle parameter changes.
        /// </summary>
        protected virtual void HandleParameterChanges()
        {
            // Handle parameter changes here
        }

        /// <summary>
        /// Executes an asynchronous operation and logs any exceptions.
        /// </summary>
        /// <param name="operation">The operation to execute.</param>
        /// <param name="errorMessage">The error message to log if an exception occurs.</param>
        protected async Task ExecuteWithErrorHandlingAsync(Func<Task> operation, string errorMessage)
        {
            try
            {
                await operation();
            }
            catch (Exception ex)
            {
                Logger.LogError(ex, errorMessage);
                throw;
            }
        }

        /// <summary>
        /// Subscribes to a ViewModel property and automatically triggers StateHasChanged when it changes.
        /// </summary>
        /// <typeparam name="T">The type of the property.</typeparam>
        /// <param name="propertyExpression">Expression that selects the property to watch.</param>
        /// <param name="onChanged">Optional action to execute when the property changes.</param>
        protected void SubscribeToProperty<T>(Expression<Func<TViewModel, T>> propertyExpression, Action<T>? onChanged = null)
        {
            this.WhenAnyValue(x => x.ViewModel)
                .Where(vm => vm != null)
                .SelectMany(vm => vm.WhenAnyValue(propertyExpression))
                .Subscribe(value =>
                {
                    onChanged?.Invoke(value);
                    InvokeAsync(StateHasChanged);
                })
                .DisposeWith(Disposables);
        }

        /// <summary>
        /// Subscribes to multiple ViewModel properties and automatically triggers StateHasChanged when any of them change.
        /// </summary>
        /// <param name="propertyExpressions">Expressions that select the properties to watch.</param>
        protected void SubscribeToProperties(params Expression<Func<TViewModel, object>>[] propertyExpressions)
        {
            foreach (var expression in propertyExpressions)
            {
                this.WhenAnyValue(x => x.ViewModel)
                    .Where(vm => vm != null)
                    .SelectMany(vm => vm.WhenAnyValue(expression))
                    .Subscribe(_ => InvokeAsync(StateHasChanged))
                    .DisposeWith(Disposables);
            }
        }

        /// <summary>
        /// Safely invokes StateHasChanged on the UI thread.
        /// </summary>
        protected void SafeStateHasChanged()
        {
            if (!_isDisposed)
            {
                InvokeAsync(StateHasChanged);
            }
        }

        /// <summary>
        /// Disposes the component synchronously.
        /// </summary>
        public void Dispose()
        {
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the component asynchronously.
        /// </summary>
        public async ValueTask DisposeAsync()
        {
            await DisposeAsyncCore();
            Dispose(disposing: false);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the component.
        /// </summary>
        /// <param name="disposing">True if disposing, false if finalizing.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed && disposing)
            {
                Logger.LogDebug("Disposing {ComponentType}", GetType().Name);

                try
                {
                    Disposables.Dispose();
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error disposing {ComponentType}", GetType().Name);
                }

                _isDisposed = true;
            }
        }

        /// <summary>
        /// Disposes the component asynchronously.
        /// </summary>
        protected virtual ValueTask DisposeAsyncCore()
        {
            if (!_isDisposed)
            {
                Logger.LogDebug("Disposing {ComponentType} asynchronously", GetType().Name);

                try
                {
                    Disposables.Dispose();
                }
                catch (Exception ex)
                {
                    Logger.LogError(ex, "Error disposing {ComponentType} asynchronously", GetType().Name);
                }

                _isDisposed = true;
            }

            return ValueTask.CompletedTask;
        }
    }
}
