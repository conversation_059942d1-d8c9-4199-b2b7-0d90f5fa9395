@using Microsoft.AspNetCore.Components.Web
@using Radzen.Blazor
@inherits LayoutComponentBase

<PageTitle>Barret - Vehicle Configurator</PageTitle>

<RadzenTheme Theme="material" @rendermode="RenderMode.InteractiveServer" />

<div class="min-h-screen flex flex-col bg-white">
    <!-- Header with user dropdown -->
    <header class="bg-white border-b border-gray-100">
        <div class="flex justify-end px-4 py-3">
            <Barret.Web.Server.Shared.LoginDisplay />
        </div>
    </header>

    <!-- Content -->
    <main class="flex-1 bg-white">
        @Body
    </main>
</div>

<!-- Radzen Components - includes dialog support -->
<RadzenComponents @rendermode="RenderMode.InteractiveServer" />

<!-- Ra<PERSON>zen Dialog Component - Required for DialogService -->
<RadzenDialog @rendermode="RenderMode.InteractiveServer" />

<!-- <PERSON><PERSON>zen Notification Component - For notifications -->
<RadzenNotification @rendermode="RenderMode.InteractiveServer" />
