@inject NavigationManager Navigation
@using Microsoft.AspNetCore.Components.Authorization

@code {
    protected override void OnInitialized()
    {
        // Get the current URL to redirect back after login
        var returnUrl = Navigation.ToBaseRelativePath(Navigation.Uri);
        
        if (string.IsNullOrWhiteSpace(returnUrl))
            Navigation.NavigateTo("/Identity/Account/Login", forceLoad: true);
        else
            Navigation.NavigateTo($"/Identity/Account/Login?returnUrl={Uri.EscapeDataString(returnUrl)}", forceLoad: true);
    }
}
