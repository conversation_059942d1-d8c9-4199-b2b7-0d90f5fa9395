using Barret.Web.Server.Features.Shared;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;

namespace Barret.Web.Server.Features
{
    /// <summary>
    /// Extension methods for WebApplicationBuilder to register services for the frontend.
    /// </summary>
    public static class ProgramExtensions
    {
        /// <summary>
        /// Adds the services required for the frontend.
        /// </summary>
        /// <param name="builder">The WebApplicationBuilder.</param>
        /// <returns>The WebApplicationBuilder.</returns>
        public static WebApplicationBuilder AddFrontendServices(this WebApplicationBuilder builder)
        {
            // Register services
            builder.Services.AddFrontendServices();

            return builder;
        }
    }
}
