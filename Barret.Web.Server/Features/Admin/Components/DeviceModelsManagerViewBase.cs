using Barret.Core.Areas.Devices.Enums;
using Barret.Services.Core.Areas.DeviceModels.Queries;
using Barret.Services.Core.Areas.Manufacturers;
using Barret.Shared.DTOs.Devices;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;
using Barret.Web.Server.Services;
using Radzen.Blazor;
using Barret.Web.Server.Features.Admin.Components.Dialogs;
using <PERSON><PERSON>zen;

namespace Barret.Web.Server.Features.Admin.Components
{
    /// <summary>
    /// Base class for the device models manager view.
    /// </summary>
    public class DeviceModelsManagerViewBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the manufacturer ID.
        /// </summary>
        [Parameter]
        public Guid ManufacturerId { get; set; }

        /// <summary>
        /// Gets or sets the manufacturer name.
        /// </summary>
        [Parameter]
        public string ManufacturerName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the manufacturer service.
        /// </summary>
        [Inject]
        protected IManufacturerService ManufacturerService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the device model query service.
        /// </summary>
        [Inject]
        protected IDeviceModelQueryService DeviceModelQueryService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the logger.
        /// </summary>
        [Inject]
        protected ILogger<DeviceModelsManagerViewBase> Logger { get; set; } = null!;

        /// <summary>
        /// Gets or sets the toast notification service.
        /// </summary>
        [Inject]
        protected IBarretToastService ToastService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the Radzen dialog service.
        /// </summary>
        [Inject]
        protected DialogService DialogService { get; set; } = null!;

        /// <summary>
        /// Gets or sets the device models grid reference.
        /// </summary>
        protected RadzenDataGrid<DeviceModelInfo> DeviceModelsGrid { get; set; } = null!;

        /// <summary>
        /// Gets or sets a value indicating whether the component is loading.
        /// </summary>
        protected bool IsLoading { get; set; } = true;

        /// <summary>
        /// Gets or sets the list of device models.
        /// </summary>
        protected List<DeviceModelInfo> DeviceModels { get; set; } = new();

        /// <summary>
        /// Gets the list of device roles.
        /// </summary>
        protected DeviceRole[] DeviceRoles => Enum.GetValues<DeviceRole>();

        // No longer needed - using service-based dialogs

        /// <summary>
        /// Method invoked when the component is initialized.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected override async Task OnInitializedAsync()
        {
            await LoadDeviceModelsAsync();
        }

        /// <summary>
        /// Method invoked when the component parameters are set.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        protected override async Task OnParametersSetAsync()
        {
            await LoadDeviceModelsAsync();
        }

        /// <summary>
        /// Loads the device models from the service.
        /// </summary>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task LoadDeviceModelsAsync()
        {
            try
            {
                if (ManufacturerId == Guid.Empty)
                {
                    DeviceModels = new List<DeviceModelInfo>();
                    IsLoading = false;
                    return;
                }

                IsLoading = true;
                StateHasChanged();

                try
                {
                    DeviceModels = await DeviceModelQueryService.GetDeviceModelsByManufacturerIdAsync(ManufacturerId);
                    Logger.LogInformation("Successfully loaded {Count} device models for manufacturer {ManufacturerId}", DeviceModels.Count, ManufacturerId);
                }
                catch (Exception ex)
                {
                    ToastService.ShowToast("Error", $"Failed to load device models: {ex.Message}", ToastType.Error);
                    Logger.LogError(ex, "Failed to load device models for manufacturer {ManufacturerId}", ManufacturerId);
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error loading device models");
            }
            finally
            {
                IsLoading = false;
                StateHasChanged();
            }
        }

        /// <summary>
        /// Opens the add device model dialog.
        /// </summary>
        protected async Task OpenAddDeviceModelDialog()
        {
            // Create manufacturer info for the dialog
            var manufacturerInfo = new ManufacturerInfo { Id = ManufacturerId, Name = ManufacturerName };

            var result = await DialogService.OpenAsync<DeviceModelFormDialog>(
                "Add Device Model",
                new Dictionary<string, object>()
                {
                    { "DeviceModel", null },
                    { "Manufacturer", manufacturerInfo },
                    { "IsEditing", false },
                    { "DeviceRoles", DeviceRoles }
                },
                new DialogOptions()
                {
                    Width = "500px",
                    CssClass = "barret-form-dialog"
                });

            if (result is DeviceModelFormDialog.DeviceModelFormResult formResult)
            {
                await SaveDeviceModel(formResult);
            }
        }

        /// <summary>
        /// Opens the edit device model dialog.
        /// </summary>
        /// <param name="deviceModel">The device model to edit.</param>
        protected async Task EditDeviceModel(DeviceModelInfo deviceModel)
        {
            // Create manufacturer info for the dialog
            var manufacturerInfo = new ManufacturerInfo { Id = ManufacturerId, Name = ManufacturerName };

            var result = await DialogService.OpenAsync<DeviceModelFormDialog>(
                "Edit Device Model",
                new Dictionary<string, object>()
                {
                    { "DeviceModel", deviceModel },
                    { "Manufacturer", manufacturerInfo },
                    { "IsEditing", true },
                    { "DeviceRoles", DeviceRoles }
                },
                new DialogOptions()
                {
                    Width = "500px",
                    CssClass = "barret-form-dialog"
                });

            if (result is DeviceModelFormDialog.DeviceModelFormResult formResult)
            {
                await SaveDeviceModel(formResult);
            }
        }

        /// <summary>
        /// Opens the delete device model confirmation dialog.
        /// </summary>
        /// <param name="deviceModel">The device model to delete.</param>
        protected async Task DeleteDeviceModel(DeviceModelInfo deviceModel)
        {
            var result = await DialogService.Confirm(
                $"Are you sure you want to delete the device model \"{deviceModel.Name}\"? This action cannot be undone.",
                "Confirm Deletion",
                new ConfirmOptions()
                {
                    OkButtonText = "Delete",
                    CancelButtonText = "Cancel",
                    Width = "450px",
                    CssClass = "barret-confirmation-dialog"
                });

            if (result == true)
            {
                await ConfirmDeleteDeviceModel(deviceModel);
            }
        }

        /// <summary>
        /// Saves the device model.
        /// </summary>
        /// <param name="formResult">The form result from the dialog.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task SaveDeviceModel(DeviceModelFormDialog.DeviceModelFormResult formResult)
        {
            try
            {
                if (!formResult.IsEditing)
                {
                    // Create new device model
                    var result = await ManufacturerService.AddDeviceModelAsync(ManufacturerId, formResult.Name, formResult.DeviceRole);
                    if (result.Success)
                    {
                        ToastService.ShowToast("Success", "Device model created successfully", ToastType.Success);
                        await LoadDeviceModelsAsync();
                    }
                    else
                    {
                        ToastService.ShowToast("Error", $"Failed to create device model: {result.ErrorMessage}", ToastType.Error);
                        Logger.LogError("Failed to create device model: {ErrorMessage}", result.ErrorMessage);
                    }
                }
                else if (formResult.OriginalDeviceModel != null)
                {
                    // Update existing device model
                    var updateResult = await ManufacturerService.UpdateDeviceModelAsync(formResult.OriginalDeviceModel.Id, formResult.Name);
                    if (updateResult.Success)
                    {
                        ToastService.ShowToast("Success", "Device model updated successfully", ToastType.Success);
                        await LoadDeviceModelsAsync();
                    }
                    else
                    {
                        ToastService.ShowToast("Error", $"Failed to update device model: {updateResult.ErrorMessage}", ToastType.Error);
                        Logger.LogError("Failed to update device model: {ErrorMessage}", updateResult.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error saving device model");
            }
        }

        /// <summary>
        /// Confirms the deletion of a device model.
        /// </summary>
        /// <param name="deviceModel">The device model to delete.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        protected async Task ConfirmDeleteDeviceModel(DeviceModelInfo deviceModel)
        {
            try
            {
                var result = await ManufacturerService.RemoveDeviceModelFromManufacturerAsync(ManufacturerId, deviceModel.Id);
                if (result.Success)
                {
                    ToastService.ShowToast("Success", "Device model deleted successfully", ToastType.Success);
                    await LoadDeviceModelsAsync();
                }
                else
                {
                    ToastService.ShowToast("Error", $"Failed to delete device model: {result.ErrorMessage}", ToastType.Error);
                    Logger.LogError("Failed to delete device model: {ErrorMessage}", result.ErrorMessage);
                }
            }
            catch (Exception ex)
            {
                ToastService.ShowToast("Error", $"An error occurred: {ex.Message}", ToastType.Error);
                Logger.LogError(ex, "Error deleting device model");
            }
        }
    }
}
