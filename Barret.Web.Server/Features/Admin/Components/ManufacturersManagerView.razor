@using Barret.Shared.DTOs.Devices
@using Barret.Services.Core.Areas.Manufacturers
@using Barret.Core.Areas.Devices.Enums
@using Radzen.Blazor
@inherits ManufacturersManagerViewBase

<div class="space-y-6">
    <!-- Action Bar -->
    <div class="bg-white rounded-xl p-4 border border-gray-100 shadow-sm">
        <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div>
                <h3 class="text-xl font-medium text-gray-900">@Title</h3>
                <p class="text-sm text-gray-500">Manage manufacturers and their device models</p>
            </div>
            <div class="flex items-center gap-3">
                <button class="flex items-center gap-2 h-10 px-4 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors"
                        @onclick="OpenAddManufacturerDialog">
                    <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="12" y1="5" x2="12" y2="19"></line>
                        <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    <span>Add Manufacturer</span>
                </button>
            </div>
        </div>
    </div>

    @if (IsLoading)
    {
        <div class="flex justify-center items-center py-12">
            <div class="h-12 w-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin"></div>
        </div>
    }
    else if (!Manufacturers.Any())
    {
        <div class="flex flex-col items-center justify-center py-16 bg-white rounded-xl border border-gray-100 shadow-sm text-center">
            <div class="bg-gray-50 p-5 rounded-full mb-4">
                <svg class="h-14 w-14 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                    <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                    <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                    <rect width="7" height="7" x="3" y="14" rx="1"></rect>
                </svg>
            </div>
            <h3 class="text-2xl font-medium text-gray-900 mb-2">No Manufacturers</h3>
            <p class="text-gray-500 mb-8 max-w-md mx-auto">
                There are no manufacturers in the system yet. Add a manufacturer to get started.
            </p>
            <button class="flex items-center gap-2 h-10 px-5 rounded-full bg-gray-900 text-white hover:bg-gray-800 transition-colors mx-auto"
                    @onclick="OpenAddManufacturerDialog">
                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
                <span>Add Manufacturer</span>
            </button>
        </div>
    }
    else
    {
        <RadzenDataGrid @ref="Grid"
                        Data="@Manufacturers"
                        TItem="ManufacturerInfo"
                        Class="barret-grid bg-white rounded-xl overflow-hidden border border-gray-100"
                        AllowFiltering="true"
                        AllowPaging="true"
                        PageSize="10"
                        ExpandMode="DataGridExpandMode.Single">
            <Columns>
                <RadzenDataGridColumn TItem="ManufacturerInfo" Property="Name" Title="Manufacturer Name" Width="60%">
                    <Template Context="manufacturer">
                        <div class="flex items-center">
                            <span class="text-gray-900 font-medium">@manufacturer.Name</span>
                        </div>
                    </Template>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn TItem="ManufacturerInfo" Title="Actions" Width="40%" Sortable="false" Filterable="false">
                    <Template Context="manufacturer">
                        <div class="barret-grid-actions">
                            <button class="flex items-center gap-1 px-3 py-1.5 rounded text-blue-600 hover:bg-blue-50 transition-colors"
                                    @onclick="() => EditManufacturer(manufacturer)">
                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                    <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                                </svg>
                                <span>Edit</span>
                            </button>
                            <button class="flex items-center gap-1 px-3 py-1.5 rounded text-red-600 hover:bg-red-50 transition-colors"
                                    @onclick="() => DeleteManufacturer(manufacturer)">
                                <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M3 6h18"></path>
                                    <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6"></path>
                                    <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path>
                                    <line x1="10" y1="11" x2="10" y2="17"></line>
                                    <line x1="14" y1="11" x2="14" y2="17"></line>
                                </svg>
                                <span>Delete</span>
                            </button>
                        </div>
                    </Template>
                </RadzenDataGridColumn>
            </Columns>
            <Template Context="manufacturer">
                <DeviceModelsManagerView ManufacturerId="@manufacturer.Id"
                                        ManufacturerName="@manufacturer.Name" />
            </Template>
        </RadzenDataGrid>
    }
</div>

@* Dialogs are now handled via DialogService - no component markup needed *@
