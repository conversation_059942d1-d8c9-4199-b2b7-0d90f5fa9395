@page "/admin"
@using Barret.Web.Server.Features.Admin.Components
@inherits AdminDashboardViewBase

<PageTitle>Barret - Admin Dashboard</PageTitle>

<div class="min-h-screen flex flex-col bg-white">
    <!-- Header -->
    <header class="border-b border-gray-100 bg-white z-10">
        <div class="max-w-[1400px] mx-auto px-4 sm:px-6 py-4 flex justify-between items-center">
            <div class="flex items-center gap-4">
                <!-- Mobile menu toggle - only visible on small screens -->
                <button class="h-10 w-10 rounded-full block md:hidden items-center justify-center text-gray-500 hover:bg-gray-100" @onclick="ToggleSidebar">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                </button>

                <h1 class="text-xl sm:text-2xl font-medium text-gray-900">
                    Admin Dashboard
                </h1>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <div class="flex-1 flex">
        <!-- Sidebar / Vertical Navigation -->
        <aside class="bg-gray-50 border-r border-gray-100 w-64 flex-shrink-0 flex flex-col transition-all duration-300 ease-in-out fixed md:static inset-y-0 left-0 z-20 md:translate-x-0 @(isSidebarOpen ? "" : "-translate-x-full") h-screen pt-[65px]">
            <div class="p-4 border-b border-gray-100">
                <h2 class="text-lg font-medium text-gray-900">Admin Panel</h2>
                <p class="text-sm text-gray-500">Manage system settings</p>
            </div>

            <nav class="flex-1 overflow-y-auto py-4">
                <ul class="space-y-1 px-3">
                    <li>
                        <button class="w-full flex items-center gap-3 px-3 py-2.5 rounded-lg text-sm font-medium @(ActiveTab == AdminTab.Manufacturers ? "bg-white text-gray-900 shadow-sm" : "text-gray-600 hover:bg-white/50 hover:text-gray-900")"
                                @onclick="() => SetActiveTab(AdminTab.Manufacturers)">
                            <span class="text-gray-500">
                                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                                    <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                                    <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                                    <rect width="7" height="7" x="3" y="14" rx="1"></rect>
                                </svg>
                            </span>
                            <span>Manufacturers & Models</span>
                        </button>
                    </li>
                    <!-- Additional tabs can be added here in the future -->
                </ul>
            </nav>
        </aside>

        <!-- Main Content Area -->
        <main class="flex-1 overflow-y-auto p-4 sm:p-6 md:ml-0 w-full">
            <div class="space-y-8 max-w-5xl mx-auto">
                @if (ActiveTab == AdminTab.Manufacturers)
                {
                    <ManufacturersManagerView Title="@GetActiveTabTitle()" />
                }
                <!-- Additional tab content can be added here in the future -->
            </div>
        </main>
    </div>
</div>
