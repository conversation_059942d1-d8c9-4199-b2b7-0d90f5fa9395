using Barret.Web.Server.Features.Admin.Data;
using Microsoft.AspNetCore.Components;

namespace Barret.Web.Server.Features.Admin.Views
{
    /// <summary>
    /// Base class for the admin dashboard view.
    /// </summary>
    public class AdminDashboardViewBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the active tab.
        /// </summary>
        protected AdminTab ActiveTab { get; set; } = AdminTab.Manufacturers;

        /// <summary>
        /// Gets or sets a value indicating whether the sidebar is open.
        /// </summary>
        protected bool isSidebarOpen = false;

        /// <summary>
        /// Sets the active tab.
        /// </summary>
        /// <param name="tab">The tab to set as active.</param>
        protected void SetActiveTab(AdminTab tab)
        {
            ActiveTab = tab;
        }

        /// <summary>
        /// Toggles the sidebar visibility.
        /// </summary>
        protected void ToggleSidebar()
        {
            isSidebarOpen = !isSidebarOpen;
        }

        /// <summary>
        /// Gets the title of the active tab.
        /// </summary>
        /// <returns>The title of the active tab.</returns>
        protected string GetActiveTabTitle()
        {
            return ActiveTab switch
            {
                AdminTab.Manufacturers => "Manufacturers & Models",
                _ => "Admin Dashboard"
            };
        }
    }
}
