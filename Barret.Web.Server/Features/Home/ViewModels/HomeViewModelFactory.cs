using Microsoft.Extensions.Logging;

namespace Barret.Web.Server.Features.Home.ViewModels
{
    /// <summary>
    /// Factory for creating HomeViewModel instances.
    /// </summary>
    public class HomeViewModelFactory : IHomeViewModelFactory
    {
        private readonly ILogger<HomeViewModel> _logger;

        /// <summary>
        /// Initializes a new instance of the HomeViewModelFactory class.
        /// </summary>
        /// <param name="logger">The logger to use.</param>
        public HomeViewModelFactory(ILogger<HomeViewModel> logger)
        {
            _logger = logger;
        }

        /// <inheritdoc/>
        public HomeViewModel Create()
        {
            return new HomeViewModel(_logger);
        }
    }
}
