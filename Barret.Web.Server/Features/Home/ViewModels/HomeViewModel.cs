using System.Collections.Generic;
using System.Threading.Tasks;
using Barret.Web.Server.Features.Home.Data;
using Barret.Web.Server.Features.Shared;
using Microsoft.Extensions.Logging;

namespace Barret.Web.Server.Features.Home.ViewModels
{
    /// <summary>
    /// ViewModel for the home page.
    /// </summary>
    public class HomeViewModel : ViewModelBase
    {
        private bool _isLoading;
        private List<VehicleTypeData> _vehicleTypes = new();

        /// <summary>
        /// Gets or sets the list of vehicle types
        /// </summary>
        public List<VehicleTypeData> VehicleTypes
        {
            get => _vehicleTypes;
            private set => SetProperty(ref _vehicleTypes, value);
        }

        /// <summary>
        /// Gets or sets whether the data is loading
        /// </summary>
        public bool IsLoading
        {
            get => _isLoading;
            private set => SetProperty(ref _isLoading, value);
        }

        /// <summary>
        /// Initializes a new instance of the HomeViewModel class.
        /// </summary>
        /// <param name="logger">The logger to use.</param>
        public HomeViewModel(ILogger<HomeViewModel> logger) : base(logger)
        {
        }

        /// <summary>
        /// Loads the vehicle types
        /// </summary>
        public async Task LoadVehicleTypesAsync()
        {
            try
            {
                IsLoading = true;
                Logger.LogInformation("Loading vehicle types");

                // In a real implementation, this would load from a service
                // For now, we'll just create some sample data
                await Task.Delay(500); // Simulate network delay

                VehicleTypes = new List<VehicleTypeData>
                {
                    new VehicleTypeData
                    {
                        Model = "All",
                        Name = "All Vehicles",
                        Description = "View and manage all vehicle configurations in one place",
                        NavigationUrl = "/vehicles",
                        IconCssClass = "bi bi-grid-3x3-gap fs-1"
                    },
                    new VehicleTypeData
                    {
                        Model = "Vessel",
                        Name = "Vessels",
                        Description = "Maritime vessels with specialized navigation and control systems",
                        NavigationUrl = "/vehicles?type=Vessel",
                        IconCssClass = "bi bi-water fs-1"
                    },
                    new VehicleTypeData
                    {
                        Model = "Truck",
                        Name = "Trucks",
                        Description = "Trucks with logistics and tracking capabilities",
                        NavigationUrl = "/vehicles?type=Truck",
                        IconCssClass = "bi bi-truck fs-1"
                    },
                    new VehicleTypeData
                    {
                        Model = "Crane",
                        Name = "Cranes",
                        Description = "Cranes with safety and operational control systems",
                        NavigationUrl = "/vehicles?type=Crane",
                        IconCssClass = "bi bi-box-seam fs-1"
                    }
                };

                Logger.LogInformation("Loaded {Count} vehicle types", VehicleTypes.Count);
            }
            catch (System.Exception ex)
            {
                Logger.LogError(ex, "Error loading vehicle types");
            }
            finally
            {
                IsLoading = false;
            }
        }
    }
}
