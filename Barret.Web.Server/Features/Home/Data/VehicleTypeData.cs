namespace Barret.Web.Server.Features.Home.Data
{
    /// <summary>
    /// Data model for vehicle types displayed on the home page
    /// </summary>
    public class VehicleTypeData
    {
        /// <summary>
        /// The model identifier for the vehicle type
        /// </summary>
        public string Model { get; set; } = string.Empty;

        /// <summary>
        /// The display name for the vehicle type
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// A brief description of the vehicle type
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// The URL to navigate to when this vehicle type is selected
        /// </summary>
        public string NavigationUrl { get; set; } = string.Empty;

        /// <summary>
        /// The icon CSS class for the vehicle type
        /// </summary>
        public string IconCssClass { get; set; } = string.Empty;
    }
}
