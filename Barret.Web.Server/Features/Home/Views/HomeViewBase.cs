using System.Threading.Tasks;
using Barret.Web.Server.Features.Home.ViewModels;
using Barret.Web.Server.Features.Shared;
using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Logging;

namespace Barret.Web.Server.Features.Home.Views
{
    /// <summary>
    /// Base class for the HomeView component.
    /// </summary>
    public class HomeViewBase : ViewBase<HomeViewModel>
    {
        /// <summary>
        /// Gets or sets the HomeViewModel factory.
        /// </summary>
        [Inject]
        protected IHomeViewModelFactory ViewModelFactory { get; set; } = null!;

        /// <summary>
        /// Gets or sets the navigation manager.
        /// </summary>
        [Inject]
        protected NavigationManager NavigationManager { get; set; } = null!;

        /// <summary>
        /// Method called when the component is initialized.
        /// </summary>
        protected override void OnInitialized()
        {
            // Create the ViewModel if it doesn't exist
            ViewModel ??= ViewModelFactory.Create();

            base.OnInitialized();
        }

        /// <summary>
        /// Method called when the component is initialized.
        /// </summary>
        protected override async Task OnInitializedAsync()
        {
            await ViewModel.LoadVehicleTypesAsync();
            await base.OnInitializedAsync();
        }

        /// <summary>
        /// Navigates to the specified URL.
        /// </summary>
        /// <param name="url">The URL to navigate to.</param>
        protected void NavigateTo(string url)
        {
            NavigationManager.NavigateTo(url);
        }
    }
}
