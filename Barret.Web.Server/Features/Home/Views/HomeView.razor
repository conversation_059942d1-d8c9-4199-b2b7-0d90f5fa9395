@page "/home"
@inherits HomeViewBase
@layout Barret.Web.Server.Features.Shared.Components.Layout.MainLayout

<PageTitle>Barret - Vehicle Configuration</PageTitle>

<div class="max-w-[1200px] mx-auto px-6 py-12 flex-1 flex flex-col">
    <!-- Header -->
    <header class="mb-16 text-left">
        <h1 class="text-3xl font-medium text-gray-900">Vehicle Configuration System</h1>
        <p class="text-gray-500 mt-2 text-lg">Select a vehicle type to begin configuration</p>
    </header>

    @if (ViewModel.IsLoading)
    {
        <div class="flex-1 flex flex-col items-center justify-center">
            <div class="h-12 w-12 border-4 border-gray-200 border-t-gray-800 rounded-full animate-spin"></div>
            <p class="mt-4 text-gray-500">Loading vehicle types...</p>
        </div>
    }
    else
    {
        <!-- Vehicle Type Selection -->
        <div class="flex-1 flex flex-col justify-center max-w-5xl mx-auto w-full">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 auto-rows-fr">
                @foreach (var vehicleType in ViewModel.VehicleTypes)
                {
                    <div class="bg-gray-50 rounded-lg p-8 flex flex-col items-center text-center vehicle-type-card-with-shadow h-full cursor-pointer"
                         @onclick="@(() => NavigateTo(vehicleType.NavigationUrl))">
                        <div class="mb-8">
                            @if (vehicleType.Model == "All")
                            {
                                <svg class="h-12 w-12 text-gray-800" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                                    <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                                    <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                                    <rect width="7" height="7" x="3" y="14" rx="1"></rect>
                                </svg>
                            }
                            else if (vehicleType.Model == "Vessel")
                            {
                                <svg class="h-12 w-12 text-gray-800" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M20 21c-3.5 0-7-1.5-7-1.5s-3.5 1.5-7 1.5c-2 0-3.5-.5-3.5-.5L2 12l.5-1C3 11 4.5 10 7 10c3.5 0 7 1.5 7 1.5s3.5-1.5 7-1.5c2.5 0 4 1 4.5 1l.5 1-1 9c-.5 0-2 .5-4 .5Z"></path>
                                    <path d="M12 10V3l-3 2"></path>
                                </svg>
                            }
                            else if (vehicleType.Model == "Truck")
                            {
                                <svg class="h-12 w-12 text-gray-800" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M10 17h4V5H2v12h3"></path>
                                    <path d="M20 17h2v-3.34a4 4 0 0 0-1.17-2.83L19 9h-5v8h1"></path>
                                    <circle cx="7.5" cy="17.5" r="2.5"></circle>
                                    <circle cx="17.5" cy="17.5" r="2.5"></circle>
                                </svg>
                            }
                            else if (vehicleType.Model == "Crane")
                            {
                                <svg class="h-12 w-12 text-gray-800" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                                    <path d="M20 16V9h-8V4H8v12h12z"></path>
                                    <path d="M18 16v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6"></path>
                                </svg>
                            }
                        </div>
                        <h2 class="text-xl font-medium text-gray-900 mb-3">@vehicleType.Name</h2>
                        <p class="text-gray-500 mb-8 text-sm">
                            Configure and manage your fleet of @(vehicleType.Model.ToLower())
                        </p>
                        <div class="mt-auto rounded-full bg-gray-900 text-white px-4 py-2 text-sm font-medium">
                            Select @vehicleType.Name
                        </div>
                    </div>
                }
            </div>
        </div>
    }
</div>
