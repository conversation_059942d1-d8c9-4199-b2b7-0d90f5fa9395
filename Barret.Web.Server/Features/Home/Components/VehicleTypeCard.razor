@using Barret.Web.Server.Features.Home.Data
@inherits VehicleTypeCardBase

<div class="vehicle-type-card-new card-with-shadow"
     @onclick="HandleClickAsync">
    <div class="vehicle-type-card-inner-new">
        <div class="vehicle-type-icon-new">
            @if (VehicleType.Model == "Vessel")
            {
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 21c-3.5 0-7-1.5-7-1.5s-3.5 1.5-7 1.5c-2 0-3.5-.5-3.5-.5L2 12l.5-1C3 11 4.5 10 7 10c3.5 0 7 1.5 7 1.5s3.5-1.5 7-1.5c2.5 0 4 1 4.5 1l.5 1-1 9c-.5 0-2 .5-4 .5Z"></path>
                    <path d="M12 10V3l-3 2"></path>
                </svg>
            }
            else if (VehicleType.Model == "Truck")
            {
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M10 17h4V5H2v12h3"></path>
                    <path d="M20 17h2v-3.34a4 4 0 0 0-1.17-2.83L19 9h-5v8h1"></path>
                    <circle cx="7.5" cy="17.5" r="2.5"></circle>
                    <circle cx="17.5" cy="17.5" r="2.5"></circle>
                </svg>
            }
            else if (VehicleType.Model == "Crane")
            {
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M20 16V9h-8V4H8v12h12z"></path>
                    <path d="M18 16v2a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V6"></path>
                </svg>
            }
            else
            {
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <rect width="7" height="7" x="3" y="3" rx="1"></rect>
                    <rect width="7" height="7" x="14" y="3" rx="1"></rect>
                    <rect width="7" height="7" x="14" y="14" rx="1"></rect>
                    <rect width="7" height="7" x="3" y="14" rx="1"></rect>
                </svg>
            }
        </div>
        <h2 class="vehicle-type-title-new">@VehicleType.Name</h2>
        <p class="vehicle-type-description-new">@VehicleType.Description</p>
        <button class="vehicle-type-button-new">
            Select @VehicleType.Name
        </button>
    </div>
</div>
