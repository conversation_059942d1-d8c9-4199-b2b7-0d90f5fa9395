using Barret.Web.Server.Features.Home.Data;
using Microsoft.AspNetCore.Components;

namespace Barret.Web.Server.Features.Home.Components
{
    /// <summary>
    /// Base class for the VehicleTypeCard component.
    /// </summary>
    public class VehicleTypeCardBase : ComponentBase
    {
        /// <summary>
        /// Gets or sets the vehicle type data to display.
        /// </summary>
        [Parameter, EditorRequired]
        public VehicleTypeData VehicleType { get; set; } = default!;

        /// <summary>
        /// Gets or sets the callback when the card is clicked.
        /// </summary>
        [Parameter]
        public EventCallback<string> OnClick { get; set; }

        /// <summary>
        /// Handles the click event on the card.
        /// </summary>
        protected async Task HandleClickAsync()
        {
            if (OnClick.HasDelegate)
            {
                await OnClick.InvokeAsync(VehicleType.NavigationUrl);
            }
        }
    }
}
