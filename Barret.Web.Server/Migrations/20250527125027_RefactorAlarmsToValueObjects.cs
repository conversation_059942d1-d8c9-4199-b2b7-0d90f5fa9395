﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Barret.Web.Server.Migrations
{
    /// <inheritdoc />
    public partial class RefactorAlarmsToValueObjects : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // First, add the new Alarms JSON column to Devices table
            migrationBuilder.AddColumn<string>(
                name: "Alarms",
                table: "Devices",
                type: "TEXT",
                nullable: false,
                defaultValue: "[]");

            // Migrate existing alarm data to JSON format
            migrationBuilder.Sql(@"
                UPDATE Devices
                SET Alarms = (
                    SELECT COALESCE(
                        '[' || GROUP_CONCAT(
                            '{""Id"":""' || a.Id || '"",""Description"":""' || REPLACE(a.Description, '""', '\""') || '"",""NotificationType"":' || a.NotificationType || ',""Message"":""' || REPLACE(COALESCE(a.Message, ''), '""', '\""') || '"",""NotificationGroupId"":' || a.NotificationGroupId || ',""EntityId"":""' || REPLACE(COALESCE(a.EntityId, ''), '""', '\""') || '"",""WarningId"":' || a.WarningId || '}'
                        ) || ']',
                        '[]'
                    )
                    FROM Alarms a
                    WHERE a.DeviceId = Devices.Id
                )
            ");

            // Now drop the old Alarms table
            migrationBuilder.DropTable(
                name: "Alarms");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Alarms",
                table: "Devices");

            migrationBuilder.CreateTable(
                name: "Alarms",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "TEXT", nullable: false),
                    Description = table.Column<string>(type: "TEXT", maxLength: 200, nullable: false),
                    DeviceId = table.Column<Guid>(type: "TEXT", nullable: false),
                    EntityId = table.Column<string>(type: "TEXT", maxLength: 100, nullable: false),
                    Message = table.Column<string>(type: "TEXT", maxLength: 500, nullable: false),
                    NotificationGroupId = table.Column<int>(type: "INTEGER", nullable: false),
                    NotificationType = table.Column<int>(type: "INTEGER", nullable: false),
                    WarningId = table.Column<int>(type: "INTEGER", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Alarms", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Alarms_Devices_DeviceId",
                        column: x => x.DeviceId,
                        principalTable: "Devices",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Alarms_DeviceId",
                table: "Alarms",
                column: "DeviceId");
        }
    }
}
