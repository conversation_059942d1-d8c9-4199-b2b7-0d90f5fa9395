﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Barret.Web.Server.Migrations
{
    /// <inheritdoc />
    public partial class AddRadarMaritimePosition : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "MaritimePosition_Facing",
                table: "Devices",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MaritimePosition_ForeAft",
                table: "Devices",
                type: "TEXT",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "MaritimePosition_Lateral",
                table: "Devices",
                type: "TEXT",
                nullable: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "MaritimePosition_Facing",
                table: "Devices");

            migrationBuilder.DropColumn(
                name: "MaritimePosition_ForeAft",
                table: "Devices");

            migrationBuilder.DropColumn(
                name: "MaritimePosition_Lateral",
                table: "Devices");
        }
    }
}
