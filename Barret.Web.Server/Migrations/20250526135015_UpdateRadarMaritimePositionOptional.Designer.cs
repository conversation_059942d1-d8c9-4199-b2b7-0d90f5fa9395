﻿// <auto-generated />
using System;
using Barret.Web.Server.Data.Contexts;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace Barret.Web.Server.Migrations
{
    [DbContext(typeof(BarretDbContext))]
    [Migration("20250526135015_UpdateRadarMaritimePositionOptional")]
    partial class UpdateRadarMaritimePositionOptional
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder.HasAnnotation("ProductVersion", "9.0.5");

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Alarms.Alarm", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("TEXT")
                        .HasColumnName("Description");

                    b.Property<Guid>("DeviceId")
                        .HasColumnType("TEXT");

                    b.Property<int>("NotificationType")
                        .HasColumnType("INTEGER")
                        .HasColumnName("NotificationType");

                    b.HasKey("Id");

                    b.HasIndex("DeviceId");

                    b.ToTable("Alarms");
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.DeviceModels.DeviceModel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<int>("DeviceRole")
                        .HasColumnType("INTEGER")
                        .HasColumnName("DeviceRole");

                    b.Property<Guid>("ManufacturerId")
                        .HasColumnType("TEXT")
                        .HasColumnName("ManufacturerId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnName("Name");

                    b.HasKey("Id");

                    b.HasIndex("ManufacturerId");

                    b.ToTable("DeviceModels");
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Connection")
                        .HasColumnType("TEXT");

                    b.Property<Guid?>("DeviceModelId")
                        .HasColumnType("TEXT")
                        .HasColumnName("DeviceModelId");

                    b.Property<int>("DeviceRole")
                        .HasColumnType("INTEGER");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnName("Name");

                    b.Property<string>("Position")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.Property<Guid?>("VehicleId")
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("DeviceModelId");

                    b.HasIndex("VehicleId");

                    b.ToTable("Devices");

                    b.HasDiscriminator<int>("DeviceRole").HasValue(-1);

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Barret.Core.Areas.Manufacturers.Models.Manufacturer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnName("Name");

                    b.HasKey("Id");

                    b.ToTable("Manufacturers");
                });

            modelBuilder.Entity("Barret.Core.Areas.Vehicles.Models.Vehicle", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("TEXT");

                    b.Property<string>("DeviceConnections")
                        .IsRequired()
                        .HasColumnType("TEXT")
                        .HasColumnName("DeviceConnections");

                    b.Property<string>("Dimensions")
                        .HasColumnType("TEXT");

                    b.Property<string>("VehicleId")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("TEXT")
                        .HasColumnName("VehicleId");

                    b.Property<string>("VehicleName")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnName("VehicleName");

                    b.Property<string>("VehicleType")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.ToTable("Vehicles", (string)null);

                    b.HasDiscriminator<string>("VehicleType").HasValue("Vehicle");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex");

                    b.ToTable("AspNetRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("TEXT");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("INTEGER");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("TEXT");

                    b.Property<string>("Email")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("TEXT");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("TEXT");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("INTEGER");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("TEXT");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("INTEGER");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex");

                    b.ToTable("AspNetUsers", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("INTEGER");

                    b.Property<string>("ClaimType")
                        .HasColumnType("TEXT");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("TEXT");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("TEXT");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("TEXT");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("RoleId")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("TEXT");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("TEXT");

                    b.Property<string>("Name")
                        .HasColumnType("TEXT");

                    b.Property<string>("Value")
                        .HasColumnType("TEXT");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens", (string)null);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Antennas.Antenna", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(5);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Audio.AMP", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(21);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Audio.AudioHub", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(7);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Audio.PAAudio", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(9);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Audio.SPAP", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(22);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Autopilots.Autopilot", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(6);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Cameras.Camera", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.Property<bool>("ShowVideo")
                        .HasColumnType("INTEGER");

                    b.HasDiscriminator().HasValue(1);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Engines.Engine", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(2);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.NVR.NVRRecording", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(25);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.NVR.NVRScreen", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(24);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Network.Firewall", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(11);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Network.Gateway", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(18);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Network.Plc", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(27);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Network.Switch", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(19);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Radars.Radar", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(4);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Radios.VHFMariphone", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(12);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Radios.VHFNetworkInterface", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(13);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Sensors.NavData", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(16);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Sensors.Sensor", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(31);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Thrusters.Thruster", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(3);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Trackpilots.Trackpilot", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(23);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.VCS.CabinetReadoutIO", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(14);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.VCS.GPU", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(17);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.VCS.HMI", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(10);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.VCS.OperatorPanelIO", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(15);
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.VCS.SafetySystemHead", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice");

                    b.HasDiscriminator().HasValue(20);
                });

            modelBuilder.Entity("Barret.Core.Areas.Vehicles.Models.Vessel.Vessel", b =>
                {
                    b.HasBaseType("Barret.Core.Areas.Vehicles.Models.Vehicle");

                    b.Property<string>("ENI")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("TEXT")
                        .HasColumnName("ENI");

                    b.Property<string>("MMSI")
                        .IsRequired()
                        .HasMaxLength(9)
                        .HasColumnType("TEXT")
                        .HasColumnName("MMSI");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("TEXT")
                        .HasColumnName("Name");

                    b.HasDiscriminator().HasValue("Vessel");
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Alarms.Alarm", b =>
                {
                    b.HasOne("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice", null)
                        .WithMany("Alarms")
                        .HasForeignKey("DeviceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.DeviceModels.DeviceModel", b =>
                {
                    b.HasOne("Barret.Core.Areas.Manufacturers.Models.Manufacturer", "Manufacturer")
                        .WithMany("DeviceModels")
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Manufacturer");
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice", b =>
                {
                    b.HasOne("Barret.Core.Areas.Devices.Models.DeviceModels.DeviceModel", "Model")
                        .WithMany()
                        .HasForeignKey("DeviceModelId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.HasOne("Barret.Core.Areas.Vehicles.Models.Vehicle", null)
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade);

                    b.Navigation("Model");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.Radars.Radar", b =>
                {
                    b.OwnsOne("Barret.Core.Areas.Devices.ValueObjects.Position", "MaritimePosition", b1 =>
                        {
                            b1.Property<Guid>("RadarId")
                                .HasColumnType("TEXT");

                            b1.Property<string>("Facing")
                                .HasColumnType("TEXT")
                                .HasColumnName("MaritimePosition_Facing");

                            b1.Property<string>("ForeAft")
                                .IsRequired()
                                .HasColumnType("TEXT")
                                .HasColumnName("MaritimePosition_ForeAft");

                            b1.Property<string>("Lateral")
                                .IsRequired()
                                .HasColumnType("TEXT")
                                .HasColumnName("MaritimePosition_Lateral");

                            b1.HasKey("RadarId");

                            b1.ToTable("Devices");

                            b1.WithOwner()
                                .HasForeignKey("RadarId");
                        });

                    b.Navigation("MaritimePosition");
                });

            modelBuilder.Entity("Barret.Core.Areas.Devices.Models.GenericDevices.GenericDevice", b =>
                {
                    b.Navigation("Alarms");
                });

            modelBuilder.Entity("Barret.Core.Areas.Manufacturers.Models.Manufacturer", b =>
                {
                    b.Navigation("DeviceModels");
                });
#pragma warning restore 612, 618
        }
    }
}
