/*
 * Layout Styles
 * This file contains styles for layout components
 */

/* Header */
.top-header {
  background-color: var(--color-foreground);
  color: var(--color-primary-foreground);
  padding: var(--space-3) var(--space-4);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky);
}

.brand h1 {
  font-size: 1.5rem;
  letter-spacing: 0.05em;
  margin-bottom: 0;
}

.brand span {
  font-size: var(--font-size-sm);
}

.user-menu a {
  color: var(--color-primary-foreground);
  display: flex;
  align-items: center;
}

.user-menu a:hover {
  color: rgba(255, 255, 255, 0.8);
}

/* Footer */
.footer {
  background-color: var(--color-muted);
  border-top: var(--border-width-thin) solid var(--color-border);
  padding: var(--space-3) 0;
  color: var(--color-muted-foreground);
}

/* Sidebar */
.sidebar {
  background-color: var(--color-muted);
  border-right: var(--border-width-thin) solid var(--color-border);
  height: 100%;
  transition: width var(--transition-normal);
  width: 256px;
  display: flex;
  flex-direction: column;
}

.sidebar-collapsed {
  width: 64px;
}

.sidebar-header {
  padding: var(--space-3);
  border-bottom: var(--border-width-thin) solid var(--color-border);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-2);
}

.sidebar-footer {
  padding: var(--space-3);
  border-top: var(--border-width-thin) solid var(--color-border);
  margin-top: auto;
}

/* Navigation */
.sidebar-nav {
  padding: 0.5rem;
}

.nav-section {
  margin-bottom: 1rem;
}

.nav-section-header {
  padding: 0.5rem 0.75rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--color-muted-foreground);
}

.nav-item {
  margin-bottom: 0.25rem;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 0.625rem 0.75rem;
  border-radius: 0.375rem;
  text-decoration: none;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--color-foreground);
  transition: all 0.2s ease;
}

.nav-link:hover {
  background-color: rgba(255, 255, 255, 0.5);
  color: var(--color-foreground);
}

.nav-link.active {
  background-color: white;
  color: var(--color-primary);
  box-shadow: var(--shadow-sm);
}

.nav-link-icon {
  height: 1.25rem;
  width: 1.25rem;
  margin-right: 0.75rem;
  color: var(--color-muted-foreground);
}

.nav-link.active .nav-link-icon {
  color: var(--color-primary);
}

/* Nested Navigation */
.nav-children {
  padding-left: 1rem;
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}

/* Grid Layout */
.grid {
  display: grid;
  gap: var(--space-4);
  width: 100%;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

/* Content area with sidebar */
.content-with-sidebar {
  display: flex;
  min-height: calc(100vh - 64px); /* Adjust based on header height */
}

.content-with-sidebar .main-content {
  flex: 1;
  padding: var(--space-5) 5rem;
  overflow-y: auto;
}

/* Page container */
.page-container {
  max-width: var(--container-max-width);
  margin: 0 auto;
  padding: var(--space-5) 5rem;
}

/* View-specific styles */
.home-view,
.vehicle-list-view,
.vehicle-editor-view {
  margin-left: auto;
  margin-right: auto;
}

/* Vehicle Editor Layout */
.vehicle-editor-container {
  display: flex;
  position: relative;
  min-height: calc(100vh - 140px);
}

.vehicle-editor-sidebar {
  width: 256px;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  z-index: 10;
  display: flex;
  flex-direction: column;
  background-color: var(--color-background);
  border-right: var(--border-width-thin) solid var(--color-border);
  box-shadow: var(--shadow-sm);
  padding-top: 64px; /* Space for the header */
  overflow-y: auto;
}

.vehicle-editor-content {
  flex: 1;
  margin-left: 256px; /* Same as sidebar width */
  padding: var(--space-4);
  transition: margin-left 0.3s ease;
}

/* Mobile Toggle Button */
.sidebar-toggle {
  position: fixed;
  top: 5rem;
  left: 1rem;
  z-index: 30;
  width: 2.5rem;
  height: 2.5rem;
  display: none;
  align-items: center;
  justify-content: center;
  background-color: white;
  border: 1px solid var(--color-border);
  border-radius: 0.375rem;
  box-shadow: var(--shadow-sm);
  cursor: pointer;
}

.sidebar-toggle i {
  font-size: 1.5rem;
  color: var(--color-foreground);
}

.sidebar-close {
  position: absolute;
  top: 0.75rem;
  right: 0.75rem;
  width: 2rem;
  height: 2rem;
  display: none;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: none;
  border-radius: 9999px;
  cursor: pointer;
}

.sidebar-close:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.sidebar-close i {
  font-size: 1.25rem;
  color: var(--color-foreground);
}

/* Connections container styling */
.connections-container {
  background-color: var(--color-secondary);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
}

/* Device list grid styling */
.device-list-grid {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  border: var(--border-width-thin) solid var(--color-border);
}

/* Connection list grid styling */
.connection-list-grid {
  border-radius: var(--border-radius-md);
  overflow: hidden;
  border: var(--border-width-thin) solid var(--color-border);
}

/* Responsive Behavior */
@media (max-width: 768px) {
  .page-container {
    padding: var(--space-4) 1.5rem;
  }

  .content-with-sidebar .main-content {
    padding: var(--space-4) 1.5rem;
  }

  .sidebar {
    position: fixed;
    z-index: var(--z-index-fixed);
    left: 0;
    top: 0;
    bottom: 0;
    box-shadow: var(--shadow-md);
    transform: translateX(-100%);
  }

  .sidebar.show {
    transform: translateX(0);
  }

  .sidebar-collapsed {
    transform: translateX(0);
  }

  .grid-cols-2 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-cols-3 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .grid-cols-4 { grid-template-columns: repeat(2, minmax(0, 1fr)); }

  .vehicle-editor-sidebar {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }

  .vehicle-editor-sidebar.show {
    transform: translateX(0);
  }

  .vehicle-editor-content {
    margin-left: 0;
    padding-top: 3.5rem; /* Add space for the toggle button */
  }

  .sidebar-toggle {
    display: flex;
  }

  .sidebar-close {
    display: flex;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .grid-cols-3 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .grid-cols-4 { grid-template-columns: repeat(3, minmax(0, 1fr)); }

  .page-container {
    padding: var(--space-4) 3rem;
  }

  .content-with-sidebar .main-content {
    padding: var(--space-4) 3rem;
  }
}
