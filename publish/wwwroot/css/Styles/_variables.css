/*
 * Design System Variables
 * This file defines the core design tokens for the application
 * Based on the Minimalist Monochromatic Design Guide
 */

:root {
  /* Grayscale Color Palette */
  --gray-50: #fafafa;
  --gray-100: #f5f5f5;
  --gray-200: #e5e5e5;
  --gray-300: #d4d4d4;
  --gray-400: #a3a3a3;
  --gray-500: #737373;
  --gray-600: #525252;
  --gray-700: #404040;
  --gray-800: #262626;
  --gray-900: #171717;

  /* We're removing accent colors and using grayscale instead */
  --accent-100: var(--gray-100);
  --accent-300: var(--gray-300);
  --accent-500: var(--gray-800);
  --accent-700: var(--gray-900);
  --accent-900: var(--gray-900);

  /* Functional Colors */
  --success: #4ade80;
  --warning: #fbbf24;
  --error: #f87171;
  --info: #60a5fa;

  /* Semantic Color Mapping */
  --color-background: var(--gray-50);
  --color-background-light: var(--gray-100);
  --color-foreground: var(--gray-900);
  --color-primary: var(--accent-500);
  --color-primary-foreground: #FFFFFF;
  --color-secondary: var(--gray-100);
  --color-secondary-foreground: var(--gray-900);
  --color-muted: var(--gray-100);
  --color-muted-foreground: var(--gray-500);
  --color-text-secondary: var(--gray-600);
  --color-border: var(--gray-300);

  /* Functional color mapping */
  --color-danger: var(--error);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-info: var(--info);

  /* Hover state variations */
  --color-primary-hover: var(--accent-700);
  --color-secondary-hover: var(--gray-200);

  /* Border properties for consistent UI */
  --border-width-thin: 1px;
  --border-width-medium: 2px;
  --border-width-thick: 3px;

  /* Border radius for consistent UI */
  --border-radius-sm: 0.375rem; /* 6px */
  --border-radius-md: 0.5rem; /* 8px */
  --border-radius-lg: 0.75rem;  /* 12px */
  --border-radius-pill: 9999px;

  /* Shadow variations - more subtle with directional emphasis */
  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.01), 0 1px 2px rgba(0, 0, 0, 0.01);
  --shadow-md: 0 1px 1px rgba(0, 0, 0, 0.01), 0 2px 3px rgba(0, 0, 0, 0.02), 0 2px 4px rgba(0, 0, 0, 0.02);
  --shadow-lg: 0 1px 1px rgba(0, 0, 0, 0.01), 0 2px 4px rgba(0, 0, 0, 0.02), 0 3px 6px rgba(0, 0, 0, 0.03);

  /* Hover shadow - minimal at top, slightly more on sides, most at bottom */
  --shadow-hover: 0 1px 2px rgba(0, 0, 0, 0.01), 0 2px 4px rgba(0, 0, 0, 0.02), 0 4px 8px rgba(0, 0, 0, 0.03);

  /* Spacing Scale (based on 4px base unit) */
  --space-2xs: 0.25rem;  /* 4px */
  --space-xs: 0.5rem;    /* 8px */
  --space-sm: 0.75rem;   /* 12px */
  --space-md: 1rem;      /* 16px */
  --space-lg: 1.5rem;    /* 24px */
  --space-xl: 2rem;      /* 32px */
  --space-2xl: 3rem;     /* 48px */
  --space-3xl: 4rem;     /* 64px */

  /* Legacy spacing variables (for backward compatibility) */
  --space-1: var(--space-2xs);
  --space-2: var(--space-xs);
  --space-3: var(--space-md);
  --space-4: var(--space-lg);
  --space-5: var(--space-xl);
  --space-6: var(--space-2xl);

  /* Typography */
  --font-family-base: 'Inter', system-ui, -apple-system, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;

  /* Font Sizes */
  --font-size-xs: 0.75rem;      /* 12px - Caption */
  --font-size-sm: 0.875rem;     /* 14px - Small body, button */
  --font-size-base: 1rem;       /* 16px - Body, input */
  --font-size-lg: 1.125rem;     /* 18px - H4 */
  --font-size-xl: 1.25rem;      /* 20px - H3 */
  --font-size-2xl: 1.5rem;      /* 24px - H2 */
  --font-size-3xl: 2rem;        /* 32px - H1 */

  /* Font Weights */
  --font-weight-normal: 400;    /* Regular */
  --font-weight-medium: 500;    /* Medium */
  --font-weight-semibold: 600;  /* Semi-bold */
  --font-weight-bold: 700;      /* Bold */

  /* Line Heights */
  --line-height-none: 1;        /* For buttons */
  --line-height-tight: 1.2;     /* For headings */
  --line-height-snug: 1.3;      /* For subheadings */
  --line-height-normal: 1.5;    /* For body text */
  --line-height-relaxed: 1.75;  /* For readable text */

  /* Letter Spacing */
  --letter-spacing-tight: -0.02em;  /* For large headings */
  --letter-spacing-normal: 0;       /* For most text */
  --letter-spacing-wide: 0.01em;    /* For buttons, labels */
  --letter-spacing-wider: 0.02em;   /* For small text */

  /* Transitions */
  --transition-fast: 0.2s ease-out;
  --transition-normal: 0.3s ease-out;
  --transition-slow: 0.5s ease-out;

  /* Z-index layers */
  --z-index-dropdown: 1000;
  --z-index-sticky: 1020;
  --z-index-fixed: 1030;
  --z-index-modal-backdrop: 1040;
  --z-index-modal: 1050;
  --z-index-popover: 1060;
  --z-index-tooltip: 1070;

  /* Container sizes */
  --container-max-width: 1440px; /* 90rem */
  --container-lg-width: 1200px;
  --container-md-width: 992px;
  --container-sm-width: 768px;
  --container-xs-width: 576px;

  /* Component-specific variables */
  /* Buttons */
  --button-sm-height: 32px;
  --button-md-height: 40px;
  --button-lg-height: 48px;
  --button-sm-padding: var(--space-xs) var(--space-sm); /* 8px 12px */
  --button-md-padding: var(--space-sm) var(--space-md); /* 12px 16px */
  --button-lg-padding: var(--space-sm) var(--space-lg); /* 12px 24px */
  --button-border-radius: var(--border-radius-sm); /* 8px */
  --button-focus-shadow: 0 0 0 3px rgba(95, 90, 137, 0.4);

  /* Inputs */
  --input-height: 40px;
  --input-padding: 0 var(--space-md); /* 0 16px */
  --input-border-radius: var(--border-radius-sm); /* 8px */
  --input-focus-shadow: 0 0 0 3px rgba(95, 90, 137, 0.2);

  /* Cards */
  --card-padding: var(--space-lg); /* 24px */
  --card-header-padding: var(--space-lg) var(--space-lg) var(--space-md); /* 24px 24px 16px */
  --card-footer-padding: var(--space-md) var(--space-lg) var(--space-lg); /* 16px 24px 24px */
  --card-border-radius: var(--border-radius-md); /* 8px */
  --card-compact-padding: var(--space-md); /* 16px */
  --card-compact-border-radius: var(--border-radius-sm); /* 6px */

  /* Navigation */
  --nav-item-gap: var(--space-xl); /* 32px */
  --nav-vertical-width: 240px;
  --nav-vertical-item-gap: var(--space-xs); /* 8px */
  --nav-vertical-item-padding: var(--space-sm) var(--space-md); /* 12px 16px */

  /* Bootstrap theme overrides */
  --bs-primary: var(--accent-500);
  --bs-primary-rgb: 95, 90, 137; /* #5f5a89 */
  --bs-secondary: var(--gray-100);
  --bs-secondary-rgb: 245, 245, 245; /* #f5f5f5 */
  --bs-success: var(--success);
  --bs-success-rgb: 74, 222, 128; /* #4ade80 */
  --bs-info: var(--info);
  --bs-info-rgb: 96, 165, 250; /* #60a5fa */
  --bs-warning: var(--warning);
  --bs-warning-rgb: 251, 191, 36; /* #fbbf24 */
  --bs-danger: var(--error);
  --bs-danger-rgb: 248, 113, 113; /* #f87171 */
  --bs-light: var(--gray-100);
  --bs-light-rgb: 245, 245, 245; /* #f5f5f5 */
  --bs-dark: var(--gray-900);
  --bs-dark-rgb: 23, 23, 23; /* #171717 */
  --bs-font-sans-serif: var(--font-family-base);
  --bs-body-font-family: var(--font-family-base);
  --bs-body-font-size: var(--font-size-base);
  --bs-body-font-weight: var(--font-weight-normal);
  --bs-body-line-height: var(--line-height-normal);
  --bs-body-color: var(--gray-900);
  --bs-body-bg: var(--gray-50);
  --bs-border-color: var(--gray-300);
  --bs-border-radius: var(--border-radius-sm);
  --bs-border-radius-lg: var(--border-radius-md);
  --bs-border-radius-sm: 0.25rem;

  /* DevExpress theme variables overrides */
  --dx-accent-color: var(--accent-500);
  --dx-accent-color-hover: var(--accent-700);
  --dx-accent-color-text: #FFFFFF;

  /* DevExpress button overrides */
  --dxbl-btn-primary-background: var(--accent-500);
  --dxbl-btn-primary-border: var(--accent-500);
  --dxbl-btn-primary-hover-background: var(--accent-700);
  --dxbl-btn-primary-hover-border: var(--accent-700);
  --dxbl-btn-primary-active-background: var(--accent-900);
  --dxbl-btn-primary-active-border: var(--accent-900);
  --dxbl-btn-primary-color: #FFFFFF;

  /* DevExpress secondary button overrides */
  --dxbl-btn-secondary-background: transparent;
  --dxbl-btn-secondary-border: var(--gray-300);
  --dxbl-btn-secondary-color: var(--gray-900);
  --dxbl-btn-secondary-hover-background: var(--gray-100);
  --dxbl-btn-secondary-hover-border: var(--gray-400);
  --dxbl-btn-secondary-active-background: var(--gray-200);
  --dxbl-btn-secondary-active-border: var(--gray-500);
}
