/*
 * Vehicle List Page Styles
 * This file contains styles specific to the vehicle list page
 */

/* View toggle controls */
.view-toggle-container {
    display: flex;
    border: var(--border-width-thin) solid var(--color-border);
    border-radius: var(--border-radius-sm);
}

.view-toggle-button {
    background: none;
    border: none;
    padding: var(--space-2) var(--space-3);
    cursor: pointer;
    color: var(--color-muted-foreground);
}

.view-toggle-button.active {
    background-color: var(--color-secondary);
    color: var(--color-foreground);
}

/* Vehicle cards */
.vehicle-card {
    border: var(--border-width-thin) solid var(--gray-200);
    transition: all var(--transition-normal);
    padding: var(--space-3);
    background-color: #ffffff;
    box-shadow: none;
    transform: translateY(0);
}

.vehicle-card:hover {
    border-color: var(--gray-200);
    transform: translateY(0);
    box-shadow: var(--shadow-hover);
    cursor: pointer;
}

.vehicle-card .card-body {
    padding: 0;
}

.vehicle-card .card-actions {
    margin-top: var(--space-3);
    padding-top: var(--space-3);
    border-top: var(--border-width-thin) solid var(--color-border);
}

/* Vehicle icons */
.vehicle-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--gray-50);
    color: var(--gray-800);
    font-size: 1.25rem;
    border: var(--border-width-thin) solid var(--gray-200);
}

.vehicle-icon-sm {
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: var(--color-secondary);
    color: var(--color-primary);
    font-size: 1rem;
    border: var(--border-width-thin) solid var(--color-border);
}

/* Vehicle properties */
.vehicle-properties {
    margin-top: var(--space-2);
    font-size: var(--font-size-sm);
}

.property-item {
    margin-bottom: var(--space-1);
    font-size: var(--font-size-sm);
}

.property-label {
    font-weight: var(--font-weight-medium);
    margin-right: var(--space-1);
}

.property-value {
    color: var(--color-muted-foreground);
}

.vehicle-properties-compact {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-2);
}

.property-item-compact {
    font-size: var(--font-size-sm);
    background-color: var(--color-secondary);
    padding: var(--space-1) var(--space-2);
    border-radius: var(--border-radius-sm);
}

/* Tab navigation */
.vehicle-type-tabs {
    border-bottom: 1px solid var(--gray-200);
    background-color: transparent !important;
}

.vehicle-type-tabs .nav-link {
    padding: var(--space-2) var(--space-4);
    font-weight: var(--font-weight-medium);
    color: var(--gray-600);
    border: none !important;
    border-bottom: 1px solid transparent !important;
    background-color: transparent !important;
    box-shadow: none !important;
    border-radius: 0 !important;
}

.vehicle-type-tabs .nav-link:hover {
    color: var(--gray-900);
    border-bottom-color: var(--gray-300) !important;
    background-color: transparent !important;
}

.vehicle-type-tabs .nav-link.active {
    color: var(--gray-900);
    border-bottom: 2px solid var(--gray-900) !important;
    background-color: transparent !important;
    font-weight: var(--font-weight-semibold);
    box-shadow: none !important;
}

/* Search box */
.search-box {
    min-width: 250px;
}

/* Empty state */
.empty-state {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 300px;
    background-color: var(--color-background);
    border: var(--border-width-thin) dashed var(--color-border);
    border-radius: var(--border-radius-md);
}

.empty-state-content {
    text-align: center;
    max-width: 400px;
    padding: var(--space-5);
}

.empty-state-icon {
    color: var(--color-border);
    font-size: 3rem;
    margin-bottom: var(--space-3);
}

/* Table styles */
.vehicle-row {
    cursor: pointer;
}

.vehicle-row:hover {
    background-color: var(--color-secondary);
}

/* Loading container */
.loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;
}
