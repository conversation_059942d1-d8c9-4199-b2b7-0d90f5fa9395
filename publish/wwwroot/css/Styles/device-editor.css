/* Device Editor Styles */

/* Device Editor Container */
.device-editor-container {
    display: flex;
    height: 100%;
    min-height: 400px;
}

/* Device Editor Tabs */
.device-editor-tabs {
    height: 100%;
    min-height: 400px;
}

/* Tab Content Panel */
.tab-content-panel {
    padding: 1rem;
    height: 100%;
    overflow-y: auto;
}

/* Device Role Selector */
.device-role-selector-container {
    padding: 1rem;
}

.device-role-grid {
    max-height: 400px;
    overflow-y: auto;
}

.device-role-card {
    display: flex;
    align-items: center;
    padding: 1rem;
    border: 1px solid var(--bs-border-color);
    border-radius: 0.25rem;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    margin-bottom: 0.5rem;
}

.device-role-card:hover {
    background-color: var(--bs-light);
    border-color: var(--bs-gray-400);
}

.device-role-icon {
    font-size: 1.5rem;
    margin-right: 1rem;
    color: var(--bs-primary);
}

.device-role-name {
    font-weight: 500;
}

/* Device Group Tab */
.device-group-tab {
    width: 100%;
}

.device-list-grid {
    width: 100%;
}

/* Form Controls */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    font-size: 0.875rem;
    color: var(--bs-gray-600);
}

/* Validation */
.validation-error {
    color: var(--bs-danger);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* Popup */
.device-editor-popup {
    max-width: 90vw;
}

/* Accessible Popup */
.accessible-popup {
    outline: none;
}

/* Device Editor Footer */
.device-editor-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-top: 1px solid var(--bs-border-color);
}

/* Device Editor Header */
.device-editor-header {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--bs-border-color);
}

/* Device Editor Title */
.device-editor-title {
    font-size: 1.25rem;
    font-weight: 500;
    margin: 0;
}

/* Device Editor Icon */
.device-editor-icon {
    font-size: 1.5rem;
    margin-right: 0.5rem;
    color: var(--bs-primary);
}

/* Device Editor Loading */
.device-editor-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    min-height: 200px;
}

/* Device Editor Error */
.device-editor-error {
    padding: 1rem;
    color: var(--bs-danger);
    border: 1px solid var(--bs-danger);
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

/* Device Editor Success */
.device-editor-success {
    padding: 1rem;
    color: var(--bs-success);
    border: 1px solid var(--bs-success);
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

/* Device Editor Warning */
.device-editor-warning {
    padding: 1rem;
    color: var(--bs-warning);
    border: 1px solid var(--bs-warning);
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

/* Device Editor Info */
.device-editor-info {
    padding: 1rem;
    color: var(--bs-info);
    border: 1px solid var(--bs-info);
    border-radius: 0.25rem;
    margin-bottom: 1rem;
}

/* Device Editor Section */
.device-editor-section {
    margin-bottom: 1.5rem;
}

/* Device Editor Section Title */
.device-editor-section-title {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--bs-border-color);
}

/* Device Editor Section Content */
.device-editor-section-content {
    padding: 0.5rem 0;
}

/* Device Editor Section Footer */
.device-editor-section-footer {
    padding-top: 0.5rem;
    margin-top: 0.5rem;
    border-top: 1px solid var(--bs-border-color);
}

/* Device Editor Section Header */
.device-editor-section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

/* Device Editor Section Description */
.device-editor-section-description {
    font-size: 0.875rem;
    color: var(--bs-gray-600);
    margin-bottom: 1rem;
}

/* Device Editor Section Icon */
.device-editor-section-icon {
    font-size: 1.25rem;
    margin-right: 0.5rem;
    color: var(--bs-primary);
}

/* Device Editor Section Badge */
.device-editor-section-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    background-color: var(--bs-primary);
    color: var(--bs-white);
}

/* Device Editor Section Badge Success */
.device-editor-section-badge-success {
    background-color: var(--bs-success);
}

/* Device Editor Section Badge Danger */
.device-editor-section-badge-danger {
    background-color: var(--bs-danger);
}

/* Device Editor Section Badge Warning */
.device-editor-section-badge-warning {
    background-color: var(--bs-warning);
}

/* Device Editor Section Badge Info */
.device-editor-section-badge-info {
    background-color: var(--bs-info);
}

/* Device Editor Section Badge Secondary */
.device-editor-section-badge-secondary {
    background-color: var(--bs-secondary);
}

/* Device Editor Section Badge Light */
.device-editor-section-badge-light {
    background-color: var(--bs-light);
    color: var(--bs-dark);
}

/* Device Editor Section Badge Dark */
.device-editor-section-badge-dark {
    background-color: var(--bs-dark);
}
