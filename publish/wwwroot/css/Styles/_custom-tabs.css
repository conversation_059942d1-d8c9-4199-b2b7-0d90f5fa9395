/*
 * Custom Tailwind CSS Tab Styling
 * This file contains styles to override DevExpress tab styling with Tailwind CSS
 */

/* Hide scrollbar while maintaining scroll functionality */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* Override DevExpress tab styling with !important to ensure our styles take precedence */
.tailwind-tabs {
  width: 100% !important;
  border-bottom: 1px solid #f3f4f6 !important; /* border-gray-100 */
  margin-bottom: 2rem !important; /* mb-8 */
  background-color: transparent !important;
  padding: 0 !important;
}

.tailwind-tabs-container {
  display: flex !important;
  overflow-x: auto !important;
}

/* Target DevExpress tab elements with high specificity */
.tailwind-tabs :deep(.dxbl-tabs),
.tailwind-tabs :deep(.dxbl-tabs-header),
.tailwind-tabs :deep(.dxbl-tabs-header-content),
.tailwind-tabs :deep(.dxbl-tabs-header-content-container) {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Style for all tabs */
.tailwind-tabs :deep(.dxbl-tab-item) {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important; /* gap-2 */
  padding: 0.75rem 1.5rem !important; /* py-3 px-6 */
  border: none !important;
  border-bottom: 2px solid transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  font-weight: 500 !important; /* font-medium */
  color: #6b7280 !important; /* text-gray-500 */
  transition: color 0.2s, border-color 0.2s !important;
  box-shadow: none !important;
  border-radius: 0 !important;
}

/* Style for active tab */
.tailwind-tabs :deep(.dxbl-tab-item.dxbl-active) {
  border-bottom: 2px solid #111827 !important; /* border-gray-900 */
  color: #111827 !important; /* text-gray-900 */
  font-weight: 500 !important; /* font-medium */
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
}

/* Style for hover state on inactive tabs */
.tailwind-tabs :deep(.dxbl-tab-item:not(.dxbl-active):hover) {
  border-bottom-color: #d1d5db !important; /* border-gray-300 */
  color: #374151 !important; /* text-gray-700 */
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
}

/* Style for tab text */
.tailwind-tabs :deep(.dxbl-tab-text) {
  color: inherit !important;
  font-weight: inherit !important;
  padding: 0 !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Focus styles for accessibility */
.tailwind-tabs :deep(.dxbl-tab-item:focus) {
  outline: none !important;
  box-shadow: 0 0 0 2px #f3f4f6, 0 0 0 4px #9ca3af !important; /* focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 */
}

/* Style for tab content */
.tailwind-tabs :deep(.dxbl-tab-content) {
  background-color: transparent !important;
  border: none !important;
  padding: 0 !important;
}

/* Custom tab icon styling */
.tab-icon {
  height: 1.25rem !important; /* h-5 */
  width: 1.25rem !important; /* w-5 */
}
