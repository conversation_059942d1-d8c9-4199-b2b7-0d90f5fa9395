/*
 * Vehicle Editor Styles
 * This file contains styles specific to the vehicle editor
 */

/* Note: The main layout styles for vehicle-editor-container, vehicle-editor-sidebar,
   and vehicle-editor-content are defined in _layout.css */

/* Sidebar */
.editor-sidebar {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: var(--color-background);
    border-right: var(--border-width-thin) solid var(--color-border);
}

.sidebar-header {
    padding: var(--space-4);
    border-bottom: var(--border-width-thin) solid var(--color-border);
}

.sidebar-content {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-3) 0;
}

.sidebar-tabs {
    display: flex;
    flex-direction: column;
}

.sidebar-tab {
    display: flex;
    align-items: center;
    padding: var(--space-3) var(--space-4);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    border-left: 3px solid transparent;
    color: var(--color-foreground);
}

.sidebar-tab:hover {
    background-color: var(--color-secondary);
}

.sidebar-tab.active {
    background-color: var(--color-background);
    border-left: 3px solid var(--color-primary);
    font-weight: var(--font-weight-medium);
}

.tab-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: var(--space-3);
    color: var(--color-primary);
}

.tab-content {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.tab-title {
    color: var(--color-foreground);
}

.parent-tab {
    position: relative;
    font-weight: var(--font-weight-medium);
    background-color: var(--color-secondary);
    border-bottom: 1px solid var(--color-border);
    color: var(--color-foreground);
}

.parent-tab:hover {
    background-color: var(--color-secondary-hover);
    color: var(--color-foreground);
}

.parent-tab.expanded {
    background-color: var(--color-secondary-hover);
    color: var(--color-foreground);
}

.expand-icon {
    position: absolute;
    right: var(--space-3);
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.875rem;
    color: var(--color-text-secondary);
    transition: transform var(--transition-fast);
}

.parent-tab.expanded .expand-icon {
    transform: translateY(-50%) rotate(0deg);
}

.parent-tab:not(.expanded) .expand-icon {
    transform: translateY(-50%) rotate(-90deg);
}

.child-tabs {
    background-color: var(--color-background-light);
    border-left: 3px solid var(--color-secondary);
    border-bottom: 1px solid var(--color-border);
}

.child-tab {
    padding-left: var(--space-6);
    font-size: 0.9rem;
    background-color: var(--color-background-light);
    color: var(--color-foreground);
}

.child-tab:hover {
    background-color: var(--color-secondary);
    color: var(--color-foreground);
}

.child-tab .tab-icon {
    width: 20px;
    height: 20px;
}

.tab-badge {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    border-radius: 10px;
    background-color: var(--color-secondary);
    color: var(--color-primary);
    font-size: 0.75rem;
    padding: 0 var(--space-1);
}

.sidebar-footer {
    padding: var(--space-4);
    border-top: var(--border-width-thin) solid var(--color-border);
}

.progress-container {
    margin-bottom: var(--space-2);
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: var(--space-2);
    font-size: var(--font-size-sm);
}

.progress {
    height: 6px;
    border-radius: 3px;
    background-color: var(--color-secondary);
}

.progress-bar {
    background-color: var(--color-primary);
    border-radius: 3px;
}

/* Tabs */
.basic-info-tab {
    padding: var(--space-4);
}

.device-group-tab {
    padding: var(--space-4);
}

.device-list-grid {
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: var(--border-width-thin) solid var(--color-border);
}

/* Button Styles */
.btn-lg.shadow {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08);
    transition: all 0.2s ease;
}

.btn-lg.shadow:hover {
    transform: translateY(-2px);
    box-shadow: 0 7px 14px rgba(0, 0, 0, 0.1), 0 3px 6px rgba(0, 0, 0, 0.08);
}

.btn-lg.shadow:active {
    transform: translateY(1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* Note: Responsive styles for vehicle-editor-sidebar and vehicle-editor-content
   are defined in _layout.css */
