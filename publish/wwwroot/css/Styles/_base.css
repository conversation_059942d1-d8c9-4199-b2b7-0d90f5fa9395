/*
 * Base Styles
 * This file contains base styles for HTML elements
 * Based on the Minimalist Monochromatic Design Guide
 */

/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

/* Reset and base styles */
html, body {
  font-family: var(--font-family-base);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--gray-900);
  background-color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  padding: 0;
  margin: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-top: 0;
  margin-bottom: var(--space-md);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
}

h1 {
  font-size: var(--font-size-3xl); /* 32px */
  line-height: var(--line-height-tight); /* 1.2 */
  letter-spacing: var(--letter-spacing-tight); /* -0.02em */
}

h1:focus {
  outline: none !important;
}

h2 {
  font-size: var(--font-size-2xl); /* 24px */
  line-height: var(--line-height-tight); /* 1.25 */
  letter-spacing: var(--letter-spacing-tight); /* -0.01em */
}

h3 {
  font-size: var(--font-size-xl); /* 20px */
  line-height: var(--line-height-snug); /* 1.3 */
  letter-spacing: var(--letter-spacing-tight); /* -0.01em */
}

h4 {
  font-size: var(--font-size-lg); /* 18px */
  line-height: var(--line-height-snug); /* 1.4 */
}

h5 {
  font-size: var(--font-size-base); /* 16px */
  line-height: var(--line-height-normal); /* 1.5 */
  font-weight: var(--font-weight-semibold);
}

h6 {
  font-size: var(--font-size-sm); /* 14px */
  line-height: var(--line-height-normal); /* 1.5 */
  font-weight: var(--font-weight-semibold);
}

p {
  margin-top: 0;
  margin-bottom: var(--space-md);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
}

small, .text-small {
  font-size: var(--font-size-sm);
  line-height: var(--line-height-normal);
}

.caption, .text-caption {
  font-size: var(--font-size-xs);
  line-height: var(--line-height-normal);
  letter-spacing: var(--letter-spacing-wider);
}

a {
  color: var(--gray-800);
  text-decoration: none;
  transition: color var(--transition-normal);
}

a:hover {
  color: var(--gray-900);
}

/* Focus styles for accessibility */
:focus {
  outline: var(--border-width-medium) solid var(--gray-800);
  outline-offset: 2px;
}

:focus:not(:focus-visible) {
  outline: none;
}

:focus-visible {
  outline: var(--border-width-medium) solid var(--gray-800);
  outline-offset: 2px;
}

/* Global DevExpress overrides */
.dxbl-tabs,
.dxbl-tab-item,
.dxbl-tab-item-content,
.dxbl-tab-text {
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
}

/* Layout */
.container-max {
  max-width: var(--container-max-width);
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-2xl);
  padding-right: var(--space-2xl);
}

.page {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
}

main {
  flex: 1;
}

/* Global content container styles for consistent whitespace */
.main-article,
article {
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--space-2xl) !important;
  padding-right: var(--space-2xl) !important;
  max-width: var(--container-max-width);
  width: 100%;
}

/* Other container styles without the excessive padding */
.main-content-container,
.container,
.container-fluid,
.main,
.content,
.content-px-4,
.content-wrapper,
.page-container,
.main-content,
.container-max {
  margin-left: auto;
  margin-right: auto;
}

/* Grid System */
.grid {
  display: grid;
  gap: var(--space-lg);
  width: 100%;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

/* Responsive utilities */
@media (max-width: 576px) {
  /* xs breakpoint */
  h1 {
    font-size: 1.75rem; /* 28px */
  }

  h2 {
    font-size: 1.375rem; /* 22px */
  }

  h3 {
    font-size: 1.25rem; /* 20px */
  }

  .container-max {
    padding-left: var(--space-md);
    padding-right: var(--space-md);
  }

  .main-content-container {
    padding-left: var(--space-md);
    padding-right: var(--space-md);
  }

  .container, .container-fluid {
    padding-left: var(--space-md);
    padding-right: var(--space-md);
  }

  .main-article,
  article {
    padding-left: var(--space-md) !important;
    padding-right: var(--space-md) !important;
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (min-width: 577px) and (max-width: 768px) {
  /* sm breakpoint */
  .container-max {
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
  }

  .main-content-container {
    padding-left: var(--space-lg);
    padding-right: var(--space-lg);
  }

  .main-article,
  article {
    padding-left: var(--space-lg) !important;
    padding-right: var(--space-lg) !important;
  }

  .grid-cols-3,
  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 769px) and (max-width: 992px) {
  /* md breakpoint */
  .container-max {
    padding-left: var(--space-xl);
    padding-right: var(--space-xl);
  }

  .main-content-container {
    padding-left: var(--space-xl);
    padding-right: var(--space-xl);
  }

  .main-article,
  article {
    padding-left: var(--space-xl) !important;
    padding-right: var(--space-xl) !important;
  }

  .grid-cols-4,
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 993px) and (max-width: 1200px) {
  /* lg breakpoint */
  .grid-cols-5,
  .grid-cols-6 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
