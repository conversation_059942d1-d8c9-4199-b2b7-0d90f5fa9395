/*
 * Vehicle List Tabs Styles
 * This file contains specific styles for the tabs in the vehicle list page
 */

/* Target the specific tabs in the vehicle list page */
.vehicle-list-tabs {
  border-bottom: 1px solid var(--gray-200) !important;
  background-color: transparent !important;
}

/* Override DevExpress tab styles with extremely specific selectors */
.vehicle-list-tabs .dxbl-tabs,
.vehicle-list-tabs .dxbl-tabs-scrollable,
.vehicle-list-tabs .dxbl-tabs-top {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

.vehicle-list-tabs .dxbl-tabs-header {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  padding: 0 !important;
  box-shadow: none !important;
}

.vehicle-list-tabs .dxbl-tab-item {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid transparent !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: 0.75rem 1.25rem !important;
  margin-right: 1rem !important;
  transition: all 0.3s ease-out !important;
  color: var(--gray-600) !important;
  font-weight: 500 !important;
}

.vehicle-list-tabs .dxbl-tab-item.dxbl-active {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 2px solid var(--gray-900) !important;
  box-shadow: none !important;
  color: var(--gray-900) !important;
  font-weight: 600 !important;
}

.vehicle-list-tabs .dxbl-tab-item:hover:not(.dxbl-active) {
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  border-bottom: 1px solid var(--gray-300) !important;
  box-shadow: none !important;
  color: var(--gray-900) !important;
}

.vehicle-list-tabs .dxbl-tab-item-content {
  background-color: transparent !important;
  background-image: none !important;
  padding: 0 !important;
  box-shadow: none !important;
}

.vehicle-list-tabs .dxbl-tab-text {
  color: inherit !important;
  font-weight: inherit !important;
  padding: 0 !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* Ensure no background colors or borders on any elements */
.vehicle-list-tabs *,
.vehicle-list-tabs *::before,
.vehicle-list-tabs *::after {
  background-color: transparent !important;
  background-image: none !important;
  box-shadow: none !important;
}
