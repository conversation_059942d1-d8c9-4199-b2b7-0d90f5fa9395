/*
 * Focus Styles
 * This file contains styles for focus states across the application
 */

/* Remove focus outline from h1 elements */
h1:focus {
  outline: none !important;
}

/* Remove focus outline from h2 elements */
h2:focus {
  outline: none !important;
}

/* Remove focus outline from h3 elements */
h3:focus {
  outline: none !important;
}

/* Remove focus outline from h4 elements */
h4:focus {
  outline: none !important;
}

/* Remove focus outline from h5 elements */
h5:focus {
  outline: none !important;
}

/* Remove focus outline from h6 elements */
h6:focus {
  outline: none !important;
}

/* Custom focus styles for interactive elements */
a:focus,
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid rgba(0, 0, 0, 0.1) !important;
  outline-offset: 2px !important;
}

/* Remove focus outline for mouse users, keep it for keyboard navigation */
:focus:not(:focus-visible) {
  outline: none !important;
}

:focus-visible {
  outline: 2px solid rgba(0, 0, 0, 0.2) !important;
  outline-offset: 2px !important;
}
