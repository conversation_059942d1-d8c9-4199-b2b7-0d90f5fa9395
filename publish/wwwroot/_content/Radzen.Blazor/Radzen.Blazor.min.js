Element.prototype.matches||(Element.prototype.matches=Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector),Element.prototype.closest||(Element.prototype.closest=function(e){var t=this;do{if(t.matches(e))return t;t=t.parentElement||t.parentNode}while(null!==t&&1===t.nodeType);return null}),document.fonts&&document.body&&(document.body.classList.add("rz-icons-loading"),document.fonts.load("16px Material Symbols").then((()=>{document.body.classList.remove("rz-icons-loading")})));var radzenRecognition,resolveCallbacks=[],rejectCallbacks=[];window.Radzen={isRTL:function(e){return e&&"rtl"==getComputedStyle(e).direction},throttle:function(e,t){var n=null;return function(){var o=arguments,a=this;n||(n=setTimeout((function(){e.apply(a,o),n=null}),t))}},mask:function(e,t,n,o){var a=document.getElementById(e);if(a){void 0!==window.safari&&(a.onblur=function(e){a.dispatchEvent(new Event("change"))});var i=a.selectionStart!=a.value.length?a.selectionStart:-1,r=a.selectionEnd!=a.value.length?a.selectionEnd:-1;a.value=function(e,t,n,o){for(var a=o?e.match(new RegExp(o,"g")):e.replace(new RegExp(n,"g"),"").split(""),i=0,r="",l=0;l<t.length;l++){const e=t[l];a&&a[i]&&("*"===e||e==a[i]?(r+=a[i],i++):r+=e)}return r}(a.value,t,n,o),a.selectionStart=-1!=i?i:a.selectionStart,a.selectionEnd=-1!=r?r:a.selectionEnd}},addContextMenu:function(e,t){var n=document.getElementById(e);if(n){var o=function(e){return e.stopPropagation(),e.preventDefault(),t.invokeMethodAsync("RadzenComponent.RaiseContextMenu",{ClientX:e.clientX,ClientY:e.clientY,ScreenX:e.screenX,ScreenY:e.screenY,AltKey:e.altKey,ShiftKey:e.shiftKey,CtrlKey:e.ctrlKey,MetaKey:e.metaKey,Button:e.button,Buttons:e.buttons}),!1};Radzen[e+"contextmenu"]=o,n.addEventListener("contextmenu",o,!1)}},addMouseEnter:function(e,t){var n=document.getElementById(e);if(n){var o=function(e){t.invokeMethodAsync("RadzenComponent.RaiseMouseEnter")};Radzen[e+"mouseenter"]=o,n.addEventListener("mouseenter",o,!1)}},addMouseLeave:function(e,t){var n=document.getElementById(e);if(n){var o=function(e){t.invokeMethodAsync("RadzenComponent.RaiseMouseLeave")};Radzen[e+"mouseleave"]=o,n.addEventListener("mouseleave",o,!1)}},removeContextMenu:function(e){var t=document.getElementById(e);t&&Radzen[e+"contextmenu"]&&t.removeEventListener("contextmenu",Radzen[e+"contextmenu"])},removeMouseEnter:function(e){var t=document.getElementById(e);t&&Radzen[e+"mouseenter"]&&t.removeEventListener("mouseenter",Radzen[e+"mouseenter"])},removeMouseLeave:function(e){var t=document.getElementById(e);t&&Radzen[e+"mouseleave"]&&t.removeEventListener("mouseleave",Radzen[e+"mouseleave"])},adjustDataGridHeader:function(e,t){e&&t&&(e.style.cssText=t.clientHeight<t.scrollHeight?"margin-left:0px;padding-right: "+(t.offsetWidth-t.clientWidth)+"px":"margin-left:0px;")},preventDefaultAndStopPropagation:function(e){e.preventDefault(),e.stopPropagation()},preventArrows:function(e){e&&e.addEventListener("keydown",(function(e){if(38===e.keyCode||40===e.keyCode)return e.preventDefault(),!1}),!1)},selectTab:function(e,t){var n=document.getElementById(e);if(n&&n.parentNode&&n.parentNode.previousElementSibling)for(var o=n.parentNode.children.length,a=0;a<o;a++){var i=n.parentNode.children[a];i&&(i.style.display=a==t?"":"none");var r=n.parentNode.previousElementSibling.children[a];r&&(a==t?(r.classList.add("rz-tabview-selected"),r.classList.add("rz-state-focused")):(r.classList.remove("rz-tabview-selected"),r.classList.remove("rz-state-focused")))}},loadGoogleMaps:function(e,t,n,o,a){if(resolveCallbacks.push(n),rejectCallbacks.push(o),!e.rz_map_init){e.rz_map_init=function(){for(var t=0;t<resolveCallbacks.length;t++)resolveCallbacks[t](e.google)};var i=e.document,r=i.createElement("script");r.src="https://maps.googleapis.com/maps/api/js?"+(a?"language="+a+"&":"")+(t?"key="+t+"&":"")+"callback=rz_map_init&libraries=marker",r.async=!0,r.defer=!0,r.onerror=function(e){for(var t=0;t<rejectCallbacks.length;t++)rejectCallbacks[t](e)},i.body.appendChild(r)}},createMap:function(e,t,n,o,a,i,r,l,d,s,c){var u;(u=document.defaultView,new Promise((function(e,t){if(u.google&&u.google.maps)return e(u.google);Radzen.loadGoogleMaps(u,o,e,t,c)}))).then((function(u){Radzen[n]=t,Radzen[n].google=u,Radzen[n].instance=new u.maps.Map(e,{center:r,zoom:i,mapId:a}),Radzen[n].instance.addListener("click",(function(e){Radzen[n].invokeMethodAsync("RadzenGoogleMap.OnMapClick",{Position:{Lat:e.latLng.lat(),Lng:e.latLng.lng()}})})),Radzen.updateMap(n,o,i,r,l,d,s,c)}))},updateMap:function(e,t,n,o,a,i,r,l){var d;(d=document.defaultView,new Promise((function(e,n){if(d.google&&d.google.maps)return e(d.google);Radzen.loadGoogleMaps(d,t,e,n,l)}))).then((function(t){let l=new t.maps.LatLngBounds;if(Radzen[e]&&Radzen[e].instance){if(Radzen[e].instance.markers&&Radzen[e].instance.markers.length)for(var d=0;d<Radzen[e].instance.markers.length;d++)Radzen[e].instance.markers[d].setMap(null);a&&(Radzen[e].instance.markers=[],a.forEach((function(t){var n;t.label&&((n=document.createElement("span")).innerHTML=t.label);var o=new this.google.maps.marker.AdvancedMarkerElement({position:t.position,title:t.title,content:n});o.addListener("click",(function(t){Radzen[e].invokeMethodAsync("RadzenGoogleMap.OnMarkerClick",{Title:o.title,Label:o.content.innerText,Position:o.position})})),o.setMap(Radzen[e].instance),Radzen[e].instance.markers.push(o),l.extend(o.position)}))),n&&Radzen[e].instance.setZoom(n),o&&Radzen[e].instance.setCenter(o),i&&Radzen[e].instance.setOptions(i),a&&r&&Radzen[e].instance.fitBounds(l)}}))},destroyMap:function(e){Radzen[e].instance&&delete Radzen[e].instance},focusSecurityCode:function(e){if(e){var t=e.querySelector(".rz-security-code-input");t&&setTimeout((function(){t.focus()}),500)}},destroySecurityCode:function(e,t){if(Radzen[e]){var n=t.getElementsByTagName("input");if(Radzen[e].keyPress&&Radzen[e].paste){for(var o=0;o<n.length;o++)n[o].removeEventListener("keypress",Radzen[e].keyPress),n[o].removeEventListener("keydown",Radzen[e].keyDown),n[o].removeEventListener("paste",Radzen[e].paste);delete Radzen[e].keyPress,delete Radzen[e].paste}Radzen[e]=null}},createSecurityCode:function(e,t,n,o){if(n&&t){var a=n.querySelector('input[type="hidden"]');Radzen[e]={},Radzen[e].inputs=[...n.querySelectorAll(".rz-security-code-input")],Radzen[e].paste=function(n){if(n.clipboardData){var i=n.clipboardData.getData("text");if(i){for(var r=0;r<i.length;r++)o&&isNaN(+i[r])||(Radzen[e].inputs[r].value=i[r]);var l=Radzen[e].inputs.map((e=>e.value)).join("").trim();a.value=l,t.invokeMethodAsync("RadzenSecurityCode.OnValueChange",l),Radzen[e].inputs[Radzen[e].inputs.length-1].focus()}n.preventDefault()}},Radzen[e].keyPress=function(n){var i=n.data?n.data.charCodeAt(0):n.which,r=n.data||String.fromCharCode(n.which);if(!n.metaKey&&!n.ctrlKey&&9!=i&&8!=i&&13!=i)if(o&&(i<48||i>57))n.preventDefault();else if(n.currentTarget.value!=r){n.currentTarget.value=r;var l=Radzen[e].inputs.map((e=>e.value)).join("").trim();a.value=l,t.invokeMethodAsync("RadzenSecurityCode.OnValueChange",l);var d=Radzen[e].inputs.indexOf(n.currentTarget);d<Radzen[e].inputs.length-1&&Radzen[e].inputs[d+1].focus()}},Radzen[e].keyDown=function(n){if(8==(n.data?n.data.charCodeAt(0):n.which)){n.currentTarget.value="";var o=Radzen[e].inputs.map((e=>e.value)).join("").trim();a.value=o,t.invokeMethodAsync("RadzenSecurityCode.OnValueChange",o);var i=Radzen[e].inputs.indexOf(n.currentTarget);i>0&&Radzen[e].inputs[i-1].focus()}};for(var i=0;i<Radzen[e].inputs.length;i++)Radzen[e].inputs[i].addEventListener(navigator.userAgent.match(/Android/i)?"textInput":"keypress",Radzen[e].keyPress),Radzen[e].inputs[i].addEventListener(navigator.userAgent.match(/Android/i)?"textInput":"keydown",Radzen[e].keyDown),Radzen[e].inputs[i].addEventListener("paste",Radzen[e].paste)}},createSlider:function(e,t,n,o,a,i,r,l,d,s,c){Radzen[e]={},Radzen[e].mouseMoveHandler=function(e){e.preventDefault();var o=t.isMin?a:i;if(t.canChange){var d=e.targetTouches&&e.targetTouches[0]?e.targetTouches[0].pageX-e.target.getBoundingClientRect().left:e.pageX-o.getBoundingClientRect().left,s=e.targetTouches&&e.targetTouches[0]?e.targetTouches[0].pageY-e.target.getBoundingClientRect().top:e.pageY-o.getBoundingClientRect().top,u=c?(n.offsetHeight-o.offsetTop-s)/n.offsetHeight:(Radzen.isRTL(o)?n.offsetWidth-o.offsetLeft-d:o.offsetLeft+d)/n.offsetWidth;u>1?u=1:u<0&&(u=0);var m=u*(l-r)+r;t.canChange&&m>=r&&m<=l&&t.invokeMethodAsync("RadzenSlider.OnValueChange",m,!!t.isMin)}},Radzen[e].mouseDownHandler=function(s){if(!n.classList.contains("rz-state-disabled"))if(document.addEventListener("mousemove",Radzen[e].mouseMoveHandler),document.addEventListener("touchmove",Radzen[e].mouseMoveHandler,{passive:!1,capture:!0}),document.addEventListener("mouseup",Radzen[e].mouseUpHandler),document.addEventListener("touchend",Radzen[e].mouseUpHandler,{passive:!0}),a==s.target||i==s.target)t.canChange=!0,t.isMin=a==s.target;else{var c=(s.targetTouches&&s.targetTouches[0]?s.targetTouches[0].pageX-s.target.getBoundingClientRect().left:s.offsetX)/n.offsetWidth*(l-r)+r,u=o?d[t.isMin?0:1]:d;c>=r&&c<=l&&c!=u&&t.invokeMethodAsync("RadzenSlider.OnValueChange",c,!!t.isMin)}},Radzen[e].mouseUpHandler=function(n){t.canChange=!1,document.removeEventListener("mousemove",Radzen[e].mouseMoveHandler),document.removeEventListener("touchmove",Radzen[e].mouseMoveHandler,{passive:!1,capture:!0}),document.removeEventListener("mouseup",Radzen[e].mouseUpHandler),document.removeEventListener("touchend",Radzen[e].mouseUpHandler,{passive:!0})},n.addEventListener("mousedown",Radzen[e].mouseDownHandler),n.addEventListener("touchstart",Radzen[e].mouseDownHandler,{passive:!0})},destroySlider:function(e,t){Radzen[e]&&(Radzen[e].mouseMoveHandler&&(document.removeEventListener("mousemove",Radzen[e].mouseMoveHandler),document.removeEventListener("touchmove",Radzen[e].mouseMoveHandler),delete Radzen[e].mouseMoveHandler),Radzen[e].mouseUpHandler&&(document.removeEventListener("mouseup",Radzen[e].mouseUpHandler),document.removeEventListener("touchend",Radzen[e].mouseUpHandler),delete Radzen[e].mouseUpHandler),Radzen[e].mouseDownHandler&&(t.removeEventListener("mousedown",Radzen[e].mouseDownHandler),t.removeEventListener("touchstart",Radzen[e].mouseDownHandler),delete Radzen[e].mouseDownHandler),Radzen[e]=null)},prepareDrag:function(e){e&&(e.ondragover=function(e){e.preventDefault()},e.ondragstart=function(e){e.dataTransfer.setData("",e.target.id)})},focusElement:function(e){var t=document.getElementById(e);t&&t.focus()},scrollCarouselItem:function(e){e.parentElement.scroll(e.offsetLeft,0)},scrollIntoViewIfNeeded:function(e,t){var n=t?e.getElementsByClassName(t)[0]:e;n&&n.scrollIntoViewIfNeeded?n.scrollIntoViewIfNeeded():n&&n.scrollIntoView&&n.scrollIntoView()},selectListItem:function(e,t,n){if(e&&t){var o=t.getElementsByTagName("LI"),a=t.querySelectorAll(".rz-state-highlight");if(a.length)for(var i=0;i<a.length;i++)a[i].classList.remove("rz-state-highlight");t.nextSelectedIndex=n,t.nextSelectedIndex>=0&&t.nextSelectedIndex<=o.length-1&&(o[t.nextSelectedIndex].classList.add("rz-state-highlight"),o[t.nextSelectedIndex].scrollIntoView({block:"nearest"}))}},focusListItem:function(e,t,n,o){if(e&&t){var a=t.getElementsByTagName("LI");if(a&&0!=a.length){if(null!=o&&null!=o||(o=-1),t.nextSelectedIndex=o,n)for(;t.nextSelectedIndex<a.length-1&&(t.nextSelectedIndex++,a[t.nextSelectedIndex].classList.contains("rz-state-disabled")););else for(;t.nextSelectedIndex>=0&&(t.nextSelectedIndex--,a[t.nextSelectedIndex]&&a[t.nextSelectedIndex].classList.contains("rz-state-disabled")););var i=t.querySelectorAll(".rz-state-highlight");if(i.length)for(var r=0;r<i.length;r++)i[r].classList.remove("rz-state-highlight");return t.nextSelectedIndex>=0&&t.nextSelectedIndex<=a.length-1&&(a[t.nextSelectedIndex].classList.add("rz-state-highlight"),Radzen.scrollIntoViewIfNeeded(a[t.nextSelectedIndex])),t.nextSelectedIndex}}},clearFocusedHeaderCell:function(e){var t=document.getElementById(e);if(t){var n=t.querySelector(".rz-grid-table").getElementsByTagName("thead")[0].querySelectorAll(".rz-state-focused");if(n.length)for(var o=0;o<n.length;o++)n[o].classList.remove("rz-state-focused")}},focusTableRow:function(e,t,n,o,a){var i=document.getElementById(e);if(i){var r=i.querySelector(".rz-grid-table"),l=r.tBodies[0],d=r.tHead,s=(null!=o&&d&&d.rows&&d.rows.length?[...d.rows]:[]).concat(l&&l.rows&&l.rows.length?[...l.rows]:[]);if(a&&("ArrowUp"==t||"ArrowDown"==t||"PageUp"==t||"PageDown"==t||"Home"==t||"End"==t)){if(0==n&&("End"==t||"PageDown"==t))if((v=d.querySelectorAll(".rz-state-focused")).length)for(var c=0;c<v.length;c++)v[c].classList.remove("rz-state-focused");if("ArrowUp"==t||"ArrowDown"==t||"PageUp"==t||"PageDown"==t){var u=s[s.length-1]?s[s.length-1].offsetHeight:40,m="PageUp"==t||"PageDown"==t?10:1;r.parentNode.scrollTop=r.parentNode.scrollTop+m*("ArrowDown"==t||"PageDown"==t?u:-u)}else r.parentNode.scrollTop="Home"==t?0:r.parentNode.scrollHeight}if(r.nextSelectedIndex=n||0,r.nextSelectedCellIndex=o||0,"ArrowDown"==t)for(;r.nextSelectedIndex<s.length-1&&(r.nextSelectedIndex++,s[r.nextSelectedIndex]&&s[r.nextSelectedIndex].classList.contains("rz-state-disabled")););else if("ArrowUp"==t)for(;r.nextSelectedIndex>0&&(r.nextSelectedIndex--,s[r.nextSelectedIndex]&&s[r.nextSelectedIndex].classList.contains("rz-state-disabled")););else if("ArrowRight"==t)for(;r.nextSelectedCellIndex<s[r.nextSelectedIndex].cells.length-1&&(r.nextSelectedCellIndex++,s[r.nextSelectedIndex]&&s[r.nextSelectedIndex].cells[r.nextSelectedCellIndex]&&s[r.nextSelectedIndex].cells[r.nextSelectedCellIndex].classList.contains("rz-state-disabled")););else if("ArrowLeft"==t)for(;r.nextSelectedCellIndex>0&&(r.nextSelectedCellIndex--,s[r.nextSelectedIndex]&&s[r.nextSelectedIndex].cells[r.nextSelectedCellIndex]&&s[r.nextSelectedIndex].cells[r.nextSelectedCellIndex].classList.contains("rz-state-disabled")););else!a||"PageDown"!=t&&"End"!=t?!a||"PageUp"!=t&&"Home"!=t||(r.nextSelectedIndex=1):r.nextSelectedIndex=s.length-1;if("ArrowLeft"==t||"ArrowRight"==t||"ArrowUp"==t&&null!=o&&0==r.nextSelectedIndex&&0==r.parentNode.scrollTop){var v;if((v=s[r.nextSelectedIndex].querySelectorAll(".rz-state-focused")).length)for(c=0;c<v.length;c++)v[c].classList.remove("rz-state-focused");if(r.nextSelectedCellIndex>=0&&r.nextSelectedCellIndex<=s[r.nextSelectedIndex].cells.length-1){var p=s[r.nextSelectedIndex].cells[r.nextSelectedCellIndex];p.classList.contains("rz-state-focused")||(p.classList.add("rz-state-focused"),!a&&r.parentElement.scrollWidth>r.parentElement.clientWidth&&Radzen.scrollIntoViewIfNeeded(p))}}else if("ArrowDown"==t||"ArrowUp"==t){var g=r.querySelectorAll(".rz-state-focused");if(g.length)for(c=0;c<g.length;c++)g[c].classList.remove("rz-state-focused");if(r.nextSelectedIndex>=0&&r.nextSelectedIndex<=s.length-1){var f=s[r.nextSelectedIndex];f.classList.contains("rz-state-focused")||(f.classList.add("rz-state-focused"),!a&&r.parentElement.scrollHeight>r.parentElement.clientHeight&&Radzen.scrollIntoViewIfNeeded(f))}}return[r.nextSelectedIndex,r.nextSelectedCellIndex]}},uploadInputChange:function(e,t,n,o,a,i){n?(Radzen.upload(e.target,t,o,a,i),e.target.value=""):Radzen.uploadChange(e.target)},uploads:function(e,t){Radzen.uploadComponents||(Radzen.uploadComponents={}),Radzen.uploadComponents[t]=e},uploadChange:function(e){for(var t=[],n=0;n<e.files.length;n++){var o=e.files[n];t.push({Name:o.name,Size:o.size,Url:URL.createObjectURL(o)})}var a=Radzen.uploadComponents&&Radzen.uploadComponents[e.id];if(a){if(a.localFiles)for(n=0;n<a.localFiles.length;n++){(o=a.localFiles[n]).Url&&URL.revokeObjectURL(o.Url)}a.files=Array.from(e.files),a.localFiles=t,a.invokeMethodAsync("RadzenUpload.OnChange",t)}for(n=0;n<e.files.length;n++){(o=e.files[n]).Url&&URL.revokeObjectURL(o.Url)}},removeFileFromUpload:function(e,t){var n=Radzen.uploadComponents&&Radzen.uploadComponents[e.id];if(n){var o=n.files.find((function(e){return e.name==t}));if(o){var a=n.localFiles.find((function(e){return e.Name==t}));a&&URL.revokeObjectURL(a.Url);var i=n.files.indexOf(o);-1!=i&&n.files.splice(i,1),e.value=""}}},removeFileFromFileInput:function(e){e.value=""},upload:function(e,t,n,o,a){var i=Radzen.uploadComponents&&Radzen.uploadComponents[e.id];if(i){i.files&&!o||(i.files=Array.from(e.files));for(var r=new FormData,l=[],d=!1,s=0;s<i.files.length;s++){var c=i.files[s];r.append(a||(n?"files":"file"),c,c.name),l.push({Name:c.name,Size:c.size})}var u=new XMLHttpRequest;u.withCredentials=!0,u.upload.onprogress=function(t){if(t.lengthComputable){var n=Radzen.uploadComponents&&Radzen.uploadComponents[e.id];if(n){var o=parseInt(t.loaded/t.total*100);n.invokeMethodAsync("RadzenUpload.OnProgress",o,t.loaded,t.total,l,d).then((function(e){e&&(d=!0,u.abort())}))}}},u.onreadystatechange=function(t){if(u.readyState===XMLHttpRequest.DONE){var n=u.status,o=Radzen.uploadComponents&&Radzen.uploadComponents[e.id];o&&(0===n||n>=200&&n<400?o.invokeMethodAsync("RadzenUpload.OnComplete",u.responseText,d):o.invokeMethodAsync("RadzenUpload.OnError",u.responseText))}},i.invokeMethodAsync("GetHeaders").then((function(e){for(var n in u.open("POST",t,!0),e)u.setRequestHeader(n,e[n]);u.send(r)}))}},getCookie:function(e){var t=("; "+decodeURIComponent(document.cookie)).split("; "+e+"=");if(2==t.length)return t.pop().split(";").shift()},getCulture:function(){var e=Radzen.getCookie(".AspNetCore.Culture");return(e?e.split("|").pop().split("=").pop():null)||"en-US"},numericOnPaste:function(e,t,n){if(e.clipboardData){var o=e.clipboardData.getData("text");if(o&&!isNaN(+o)){var a=+o;if(null!=t&&a>=t)return;if(null!=n&&a<=n)return}e.preventDefault()}},numericOnInput:function(e,t,n,o){var a=e.target.value;if(o||""!=a||null==t||(e.target.value=t),a&&!isNaN(+a)){var i=+a;null!=t&&!isNaN(+t)&&i<t&&(e.target.value=t),null!=n&&!isNaN(+n)&&i>n&&(e.target.value=n)}},numericKeyPress:function(e,t,n){if(!e.metaKey&&!e.ctrlKey&&9!=e.keyCode&&8!=e.keyCode&&13!=e.keyCode){if("NumpadDecimal"===e.code&&!t){var o=e.target.selectionEnd;return e.target.value=[e.target.value.slice(0,e.target.selectionStart),n,e.target.value.slice(e.target.selectionEnd)].join(""),e.target.selectionStart=++o,e.target.selectionEnd=o,void e.preventDefault()}var a=String.fromCharCode(e.charCode);(t?/^[-\d]$/:/^[-\d,.]$/).test(a)||e.preventDefault()}},openContextMenu:function(e,t,n,o,a){Radzen.closePopup(n),Radzen.openPopup(null,n,!1,null,e,t,o,a),setTimeout((function(){var e=document.getElementById(n);if(e){var t=e.querySelector(".rz-menu");t&&t.focus()}}),500)},openTooltip:function(e,t,n,o,a,i,r,l){Radzen.closeTooltip(t),n?Radzen[t+"delay"]=setTimeout(Radzen.openPopup,n,e,t,!1,a,null,null,r,l,i):Radzen.openPopup(e,t,!1,a,null,null,r,l,i),o&&(Radzen[t+"duration"]=setTimeout(Radzen.closePopup,o,t,r,l))},closeTooltip(e){Radzen.activeElement=null,Radzen.closePopup(e),Radzen[e+"delay"]&&clearTimeout(Radzen[e+"delay"]),Radzen[e+"duration"]&&clearTimeout(Radzen[e+"duration"])},destroyDatePicker(e){var t=document.getElementById(e);if(t){var n=t.querySelector(".rz-datepicker-trigger");n&&(n.onclick=null);var o=t.querySelector(".rz-inputtext");o&&(o.onclick=null)}},createDatePicker(e,t){if(e){var n=function(e,n){n&&Radzen.togglePopup(e.currentTarget.parentNode,t,!1,null,null,!0,!1)},o=e.querySelector(".rz-inputtext"),a=e.querySelector(".rz-datepicker-trigger");a&&(a.onclick=function(e){n(e,!(e.currentTarget.classList.contains("rz-state-disabled")||o&&o.classList.contains("rz-readonly")))}),o&&(o.onclick=function(e){n(e,e.currentTarget.classList.contains("rz-input-trigger")&&!e.currentTarget.classList.contains("rz-readonly"))})}},findPopup:function(e){for(var t=[],n=0;n<document.body.children.length;n++)document.body.children[n].id==e&&t.push(document.body.children[n]);return t},repositionPopup:function(e,t){var n=document.getElementById(t);if(n){var o=n.getBoundingClientRect(),a=e?e.getBoundingClientRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};if(/Edge/.test(navigator.userAgent))var i=document.body.scrollTop;else i=document.documentElement.scrollTop;var r=a.bottom+i;r+o.height>window.innerHeight+i&&a.top>o.height&&(r=a.top-o.height+i),n.style.top=r+"px"}},openPopup:function(e,t,n,o,a,i,r,l,d=!0,s=!1,c=!1){var u=document.getElementById(t);if(u){Radzen.activeElement=document.activeElement;var m=e?e.getBoundingClientRect():{top:i||0,bottom:0,left:a||0,right:0,width:0,height:0};if(/Edge/.test(navigator.userAgent))var v=document.body.scrollLeft,p=document.body.scrollTop;else v=document.documentElement.scrollLeft,p=document.documentElement.scrollTop;var g=i||m.bottom,f=a||m.left;if(n&&(u.style.width=m.width+"px",u.style.minWidth||(u.minWidth=!0,u.style.minWidth=m.width+"px")),window.chrome){var h=u.closest(".rz-frozen-cell");h&&(Radzen[t+"FZL"]={cell:h,left:h.style.left},h.style.left="")}u.style.display="block",u.onanimationend=null,u.classList.add("rz-open"),u.classList.remove("rz-close");var z=u.getBoundingClientRect();z.width=a?z.width+20:z.width,z.height=i?z.height+20:z.height;var R=!o||"bottom"==o;if(R&&g+z.height>window.innerHeight&&m.top>z.height&&(!0!==c&&(g=m.top-z.height),o)){g-=40;var y="rz-"+o+"-tooltip-content";if((E=u.children[0]).classList.contains(y)&&(E.classList.remove(y),E.classList.add("rz-top-tooltip-content"),o="top",r&&l))try{r.invokeMethodAsync(l,o)}catch{}}if(R&&f+z.width>window.innerWidth&&window.innerWidth>z.width&&(f=o?z.left:window.innerWidth-z.width,o)){g=i||m.top;var E;y="rz-"+o+"-tooltip-content";if((E=u.children[0]).classList.contains(y)&&(E.classList.remove(y),E.classList.add("rz-left-tooltip-content"),o="left",r&&l))try{r.invokeMethodAsync(l,o)}catch{}}R&&o&&(g+=20),"left"==o&&(f=m.left-z.width-5,g=m.top),"right"==o&&(f=m.right+10,g=m.top),"top"==o&&(g=m.top-z.height+5,f=m.left),u.style.zIndex=2e3,u.style.left=f+v+"px",u.style.top=g+p+"px",u.classList.contains("rz-overlaypanel")||u.classList.add("rz-popup"),Radzen[t]=function(e){var t=Radzen.popups&&Radzen.popups[Radzen.popups.length-1],n=null!=t&&document.getElementById(t.id)||u;if(t&&(n.instance=t.instance,n.callback=t.callback,n.parent=t.parent),"contextmenu"!=e.type&&e.target&&d)if(/Android/i.test(navigator.userAgent)||["input","textarea"].includes(document.activeElement?document.activeElement.tagName.toLowerCase():"")||"resize"!=e.type){var o=e.target.closest&&(e.target.closest(".rz-link")||e.target.closest(".rz-navigation-item-link"));"resize"!=e.type||/Android/i.test(navigator.userAgent)||(o&&o.closest&&o.closest("a")&&0==e.button?(o.closest("a").click(),Radzen.closeAllPopups()):Radzen.closeAllPopups()),n.parent?"mousedown"!=e.type||n.parent.contains(e.target)||n.contains(e.target)||Radzen.closePopup(n.id,n.instance,n.callback,e):e.target.nodeType&&!n.contains(e.target)&&Radzen.closePopup(n.id,n.instance,n.callback,e)}else Radzen.closePopup(n.id,n.instance,n.callback,e)},Radzen.popups||(Radzen.popups=[]),Radzen.popups.push({id:t,instance:r,callback:l,parent:e}),document.body.appendChild(u),document.removeEventListener("mousedown",Radzen[t]),document.addEventListener("mousedown",Radzen[t]),window.removeEventListener("resize",Radzen[t]),window.addEventListener("resize",Radzen[t]);for(var L=e;L&&L!=document.body;)(L.scrollWidth>L.clientWidth||L.scrollHeight>L.clientHeight)&&(L.removeEventListener("scroll",Radzen.closeAllPopups),L.addEventListener("scroll",Radzen.closeAllPopups)),L=L.parentElement;e||(document.removeEventListener("contextmenu",Radzen[t]),document.addEventListener("contextmenu",Radzen[t])),s&&setTimeout((function(){u.removeEventListener("keydown",Radzen.focusTrap),u.addEventListener("keydown",Radzen.focusTrap);var e=Radzen.getFocusableElements(u)[0];e&&e.focus()}),200)}},closeAllPopups:function(e,t){if(Radzen.popups){for(var n=e&&e.target||t&&documentElement.getElementById(t),o=Radzen.popups,a=0;a<o.length;a++){var i=o[a],r=n&&n.closest&&(n.closest(".rz-popup")||n.closest(".rz-overlaypanel"));if(r&&r!=i)return;Radzen.closePopup(i.id,i.instance,i.callback,e)}Radzen.popups=[]}},closePopup:function(e,t,n,o){var a=document.getElementById(e);if(a){if("none"==a.style.display){var i=Radzen.findPopup(e);if(!(i.length>1))return;for(var r=0;r<i.length;r++)"none"==i[r].style.display?i[r].parentNode.removeChild(i[r]):a=i[r]}if(a&&(a.minWidth&&(a.style.minWidth=""),window.chrome&&Radzen[e+"FZL"]&&(Radzen[e+"FZL"].cell.style.left=Radzen[e+"FZL"].left,Radzen[e+"FZL"]=null),a.onanimationend=function(){a.style.display="none",a.onanimationend=null},a.classList.add("rz-close"),a.classList.remove("rz-open")),document.removeEventListener("mousedown",Radzen[e]),window.removeEventListener("resize",Radzen[e]),Radzen[e]=null,t&&n)if(n.includes("RadzenTooltip"))try{t.invokeMethodAsync(n,null)}catch{}else try{t.invokeMethodAsync(n)}catch{}Radzen.popups=(Radzen.popups||[]).filter((function(t){return t.id!==e})),(Radzen.activeElement&&Radzen.activeElement==document.activeElement||Radzen.activeElement&&document.activeElement==document.body||Radzen.activeElement&&document.activeElement&&(document.activeElement.classList.contains("rz-dropdown-filter")||document.activeElement.classList.contains("rz-lookup-search-input")))&&setTimeout((function(){o&&o.target&&-1!=o.target.tabIndex&&(Radzen.activeElement=o.target),Radzen.activeElement&&Radzen.activeElement.focus(),Radzen.activeElement=null}),100)}},popupOpened:function(e){var t=document.getElementById(e);return!!t&&"none"!=t.style.display},togglePopup:function(e,t,n,o,a,i=!0,r=!1){var l=document.getElementById(t);l&&("block"==l.style.display?Radzen.closePopup(t,o,a):Radzen.openPopup(e,t,n,null,null,null,o,a,i,r))},destroyPopup:function(e){var t=document.getElementById(e);t&&t.parentNode.removeChild(t),document.removeEventListener("mousedown",Radzen[e])},scrollDataGrid:function(e){var t=(e.target.scrollLeft?"-"+e.target.scrollLeft:0)+"px";e.target.previousElementSibling.style.marginLeft=t,e.target.previousElementSibling.firstElementChild.style.paddingRight=e.target.clientHeight<e.target.scrollHeight?e.target.offsetWidth-e.target.clientWidth+"px":"0px",e.target.nextElementSibling&&(e.target.nextElementSibling.style.marginLeft=t,e.target.nextElementSibling.firstElementChild.style.paddingRight=e.target.clientHeight<e.target.scrollHeight?e.target.offsetWidth-e.target.clientWidth+"px":"0px");for(var n=0;n<document.body.children.length;n++)document.body.children[n].classList.contains("rz-overlaypanel")&&(document.body.children[n].style.display="none")},focusFirstFocusableElement:function(e){var t=Radzen.getFocusableElements(e),n=e.querySelector(".rz-html-editor");if(n&&!t.includes(n.previousElementSibling)){var o=e.querySelector(".rz-html-editor-content");if(o){var a=window.getSelection(),i=document.createRange();i.setStart(o,0),i.setEnd(o,0),a.removeAllRanges(),a.addRange(i)}}else{var r=t[0];r&&r.focus()}},openSideDialog:function(e){setTimeout((function(){if(e.autoFocusFirstElement){var t=document.querySelectorAll(".rz-dialog-side-content");if(0==t.length)return;var n=t[t.length-1];Radzen.focusFirstFocusableElement(n)}}),500)},openDialog:function(e,t,n){Radzen.closeAllPopups&&Radzen.closeAllPopups(),Radzen.dialogService=t,document.documentElement.scrollHeight>document.documentElement.clientHeight&&document.body.classList.add("no-scroll"),setTimeout((function(){var t=document.querySelectorAll(".rz-dialog-content");if(0!=t.length){var o=t[t.length-1];if(o){if(o.options=e,o.removeEventListener("keydown",Radzen.focusTrap),o.addEventListener("keydown",Radzen.focusTrap),e.resizable){n.offsetWidth=o.parentElement.offsetWidth,n.offsetHeight=o.parentElement.offsetHeight;Radzen.dialogResizer=new ResizeObserver((function(e){n&&(n.offsetWidth==e[0].target.offsetWidth&&n.offsetHeight==e[0].target.offsetHeight||(n.offsetWidth=e[0].target.offsetWidth,n.offsetHeight=e[0].target.offsetHeight,n.invokeMethodAsync("RadzenDialog.OnResize",e[0].target.offsetWidth,e[0].target.offsetHeight)))})).observe(o.parentElement)}if(e.draggable){var a=o.parentElement.querySelector(".rz-dialog-titlebar");a&&(Radzen[a]=function(e){var t=o.parentElement.getBoundingClientRect(),a=e.clientX-t.left,i=e.clientY-t.top,r=function(e){var t=e.clientX-a,r=e.clientY-i;o.parentElement.style.left=t+"px",o.parentElement.style.top=r+"px",n.invokeMethodAsync("RadzenDialog.OnDrag",r,t)},l=function(){document.removeEventListener("mousemove",r),document.removeEventListener("mouseup",l)};document.addEventListener("mousemove",r),document.addEventListener("mouseup",l)},a.addEventListener("mousedown",Radzen[a]))}e.autoFocusFirstElement&&Radzen.focusFirstFocusableElement(o)}}}),500),document.removeEventListener("keydown",Radzen.closePopupOrDialog),e.closeDialogOnEsc&&document.addEventListener("keydown",Radzen.closePopupOrDialog)},closeDialog:function(){Radzen.dialogResizer=null,document.body.classList.remove("no-scroll");var e=document.querySelectorAll(".rz-dialog-content"),t=e.length&&e[e.length-1];if(t){var n=t.parentElement.querySelector(".rz-dialog-titlebar");n&&(n.removeEventListener("mousedown",Radzen[n]),Radzen[n]=null,delete Radzen[n])}e.length<=1&&(document.removeEventListener("keydown",Radzen.closePopupOrDialog),delete Radzen.dialogService)},disableKeydown:function(e){(e=e||window.event).preventDefault()},getFocusableElements:function(e){return[...e.querySelectorAll("a, button, input, textarea, select, details, iframe, embed, object, summary dialog, audio[controls], video[controls], [contenteditable], [tabindex]")].filter((e=>e&&e.tabIndex>-1&&!e.hasAttribute("disabled")&&null!==e.offsetParent))},focusTrap:function(e){if("key"in(e=e||window.event)?"Tab"===e.key:9===e.keyCode){var t=Radzen.getFocusableElements(e.currentTarget),n=t[0],o=t[t.length-1];n&&o&&e.shiftKey&&document.activeElement===n?(e.preventDefault(),o.focus()):n&&o&&!e.shiftKey&&document.activeElement===o&&(e.preventDefault(),n.focus())}},closePopupOrDialog:function(e){if(("key"in(e=e||window.event)?"Escape"===e.key||"Esc"===e.key:27===e.keyCode)&&Radzen.dialogService){for(var t=document.querySelectorAll(".rz-popup,.rz-overlaypanel"),n=0;n<t.length;n++)if("none"!=t[n].style.display)return;var o=document.querySelectorAll(".rz-dialog-content");if(0==o.length)return;var a=o[o.length-1];if(a&&a.options&&a.options.closeDialogOnEsc&&(Radzen.dialogService.invokeMethodAsync("DialogService.Close",null),o.length<=1)){document.removeEventListener("keydown",Radzen.closePopupOrDialog),delete Radzen.dialogService;var i=document.querySelector(".rz-layout");i&&i.removeEventListener("keydown",Radzen.disableKeydown)}}},getNumericValue:function(e){var t=e instanceof Element||e instanceof HTMLDocument?e:document.getElementById(e);return t?Radzen.getInputValue(t.children[0]):null},getInputValue:function(e){var t=e instanceof Element||e instanceof HTMLDocument?e:document.getElementById(e);return t&&""!=t.value?t.value:null},setInputValue:function(e,t){var n=e instanceof Element||e instanceof HTMLDocument?e:document.getElementById(e);n&&(n.value=t)},blur:function(e,t){e&&(t.preventDefault(),e.dispatchEvent(new KeyboardEvent("keydown",{bubbles:!0,cancelable:!0,keyCode:9})))},readFileAsBase64:function(e,t,n,o){return function(e){return new Promise((function(a,i){var r=new FileReader;r.onerror=function(){r.abort(),i("Error reading file.")},r.addEventListener("load",(function(){if(e.files[0]&&e.files[0].type.includes("image")&&n>0&&o>0){var t=document.createElement("img");t.onload=function(i){var r=document.createElement("canvas"),l=function(e){var t=e.width,a=e.height;return t>a?t>n&&(a*=n/t,t=n):a>o&&(t*=o/a,a=o),{width:t,height:a}}(t);r.width=l.width,r.height=l.height,r.getContext("2d").drawImage(t,0,0,l.width,l.height),a(r.toDataURL(e.type))},t.src=r.result}else a(r.result)}),!1);var l=e.files[0];l&&(l.size<=t?r.readAsDataURL(l):i("File too large."))}))}(e)},toggleMenuItem:function(e,t,n,o){var a=e.closest(".rz-navigation-item"),i=null!=n?n:!a.classList.contains("rz-navigation-item-active");function r(t){a.classList.toggle("rz-navigation-item-active",t),e.classList.toggle("rz-navigation-item-wrapper-active",t);var n=a.querySelector(".rz-navigation-menu");n&&(t?(n.onanimationend=null,n.style.display="",n.classList.add("rz-open"),n.classList.remove("rz-close")):(n.onanimationend=function(){n.style.display="none",n.onanimationend=null},n.classList.remove("rz-open"),n.classList.add("rz-close")));var o=a.querySelector(".rz-navigation-item-icon-children");o&&(o.classList.toggle("rz-state-expanded",t),o.classList.toggle("rz-state-collapsed",!t))}!1===o&&a.parentElement&&a.parentElement.closest(".rz-navigation-item")&&!n||(r(i),document.removeEventListener("click",e.clickHandler),e.clickHandler=function(e){if(a.contains(e.target)){var t=e.target.closest(".rz-navigation-item");if(t&&t.querySelector(".rz-navigation-menu"))return}r(!1)},document.addEventListener("click",e.clickHandler))},destroyChart:function(e){e&&(e.removeEventListener("mouseleave",e.mouseLeaveHandler),delete e.mouseLeaveHandler,e.removeEventListener("mouseenter",e.mouseEnterHandler),delete e.mouseEnterHandler,e.removeEventListener("mousemove",e.mouseMoveHandler),delete e.mouseMoveHandler,e.removeEventListener("click",e.clickHandler),delete e.clickHandler,this.destroyResizable(e))},destroyGauge:function(e){this.destroyResizable(e)},destroyResizable:function(e){e.resizeObserver&&(e.resizeObserver.disconnect(),delete e.resizeObserver),e.resizeHandler&&(window.removeEventListener("resize",e.resizeHandler),delete e.resizeHandler)},createResizable:function(e,t){e.resizeHandler=function(){var n=e.getBoundingClientRect();t.invokeMethodAsync("Resize",n.width,n.height)},window.ResizeObserver?(e.resizeObserver=new ResizeObserver(e.resizeHandler),e.resizeObserver.observe(e)):window.addEventListener("resize",e.resizeHandler);var n=e.getBoundingClientRect();return{width:n.width,height:n.height}},createChart:function(e,t){var n=!1;return e.mouseMoveHandler=this.throttle((function(o){if(n){var a=e.getBoundingClientRect(),i=o.clientX-a.left,r=o.clientY-a.top;t.invokeMethodAsync("MouseMove",i,r)}}),100),e.mouseEnterHandler=function(){n=!0},e.mouseLeaveHandler=function(e){e.relatedTarget&&(e.relatedTarget.matches(".rz-chart-tooltip")||e.relatedTarget.closest(".rz-chart-tooltip"))||(n=!1,t.invokeMethodAsync("MouseMove",-1,-1))},e.clickHandler=function(n){var o=e.getBoundingClientRect(),a=n.clientX-o.left,i=n.clientY-o.top;n.target.closest(".rz-marker")||t.invokeMethodAsync("Click",a,i)},e.addEventListener("mouseenter",e.mouseEnterHandler),e.addEventListener("mouseleave",e.mouseLeaveHandler),e.addEventListener("mousemove",e.mouseMoveHandler),e.addEventListener("click",e.clickHandler),this.createResizable(e,t)},createGauge:function(e,t){return this.createResizable(e,t)},destroyScheduler:function(e){e&&e.resizeHandler&&(window.removeEventListener("resize",e.resizeHandler),delete e.resizeHandler)},createScheduler:function(e,t){e.resizeHandler=function(){var n=e.getBoundingClientRect();t.invokeMethodAsync("Resize",n.width,n.height)},window.addEventListener("resize",e.resizeHandler);var n=e.getBoundingClientRect();return{width:n.width,height:n.height}},innerHTML:function(e,t){if(null==t)return e.innerHTML;null!=e&&(e.innerHTML=t)},execCommand:function(e,t,n){return document.activeElement!=e&&e&&e.focus(),document.execCommand(t,!1,n),this.queryCommands(e)},queryCommands:function(e){return{html:null!=e?e.innerHTML:null,fontName:document.queryCommandValue("fontName"),fontSize:document.queryCommandValue("fontSize"),formatBlock:document.queryCommandValue("formatBlock"),bold:document.queryCommandState("bold"),underline:document.queryCommandState("underline"),justifyRight:document.queryCommandState("justifyRight"),justifyLeft:document.queryCommandState("justifyLeft"),justifyCenter:document.queryCommandState("justifyCenter"),justifyFull:document.queryCommandState("justifyFull"),italic:document.queryCommandState("italic"),strikeThrough:document.queryCommandState("strikeThrough"),superscript:document.queryCommandState("superscript"),subscript:document.queryCommandState("subscript"),unlink:document.queryCommandEnabled("unlink"),undo:document.queryCommandEnabled("undo"),redo:document.queryCommandEnabled("redo")}},mediaQueries:{},mediaQuery:function(e,t){if(t){function n(e){t.invokeMethodAsync("OnChange",e.matches)}e=matchMedia(e);return this.mediaQueries[t._id]=function(){e.removeListener(n)},e.addListener(n),e.matches}t=e,this.mediaQueries[t._id]&&(this.mediaQueries[t._id](),delete this.mediaQueries[t._id])},createEditor:function(e,t,n,o,a){e.inputListener=function(){o.invokeMethodAsync("OnChange",e.innerHTML)},e.keydownListener=function(e){var t="";(e.ctrlKey||e.metaKey)&&(t+="Ctrl+"),e.altKey&&(t+="Alt+"),e.shiftKey&&(t+="Shift+"),t+=e.code.replace("Key","").replace("Digit","").replace("Numpad",""),a.includes(t)&&(e.preventDefault(),o.invokeMethodAsync("ExecuteShortcutAsync",t))},e.clickListener=function(t){if(t.target){for(var n of(t.target.matches("a,button")&&t.preventDefault(),e.querySelectorAll("img.rz-state-selected")))n.classList.remove("rz-state-selected");if(t.target.matches("img")){t.target.classList.add("rz-state-selected");var o=document.createRange();o.selectNode(t.target),getSelection().removeAllRanges(),getSelection().addRange(o)}}},e.selectionChangeListener=function(){document.activeElement==e&&o.invokeMethodAsync("OnSelectionChange")},e.pasteListener=function(e){var a=e.clipboardData.items[0];if("file"==a.kind){e.preventDefault();var i=a.getAsFile();if(t){var r=new XMLHttpRequest;(d=new FormData).append("file",i),r.onreadystatechange=function(e){if(r.readyState===XMLHttpRequest.DONE){var t=r.status;if(0===t||t>=200&&t<400){var a=JSON.parse(r.responseText),i='<img src="'+a.url+'">';n?o.invokeMethodAsync("OnPaste",i).then((function(e){document.execCommand("insertHTML",!1,e)})):document.execCommand("insertHTML",!1,'<img src="'+a.url+'">'),o.invokeMethodAsync("OnUploadComplete",r.responseText)}else o.invokeMethodAsync("OnError",r.responseText)}},o.invokeMethodAsync("GetHeaders").then((function(e){for(var n in r.open("POST",t,!0),e)r.setRequestHeader(n,e[n]);r.send(d)}))}else{var l=new FileReader;l.onload=function(e){var t='<img src="'+e.target.result+'">';n?o.invokeMethodAsync("OnPaste",t).then((function(e){document.execCommand("insertHTML",!1,e)})):document.execCommand("insertHTML",!1,t)},l.readAsDataURL(i)}}else if(n){e.preventDefault();var d=e.clipboardData.getData("text/html")||e.clipboardData.getData("text/plain");o.invokeMethodAsync("OnPaste",d).then((function(e){document.execCommand("insertHTML",!1,e)}))}},e.addEventListener("input",e.inputListener),e.addEventListener("paste",e.pasteListener),e.addEventListener("keydown",e.keydownListener),e.addEventListener("click",e.clickListener),document.addEventListener("selectionchange",e.selectionChangeListener),document.execCommand("styleWithCSS",!1,!0)},saveSelection:function(e){if(document.activeElement==e){var t=getSelection();t.rangeCount>0&&(e.range=t.getRangeAt(0))}},restoreSelection:function(e){var t=e.range;if(t){delete e.range,e&&e.focus();var n=getSelection();n.removeAllRanges(),n.addRange(t)}},selectionAttributes:function(e,t,n){for(var o=getSelection(),a=o.rangeCount>0&&o.getRangeAt(0),i=a&&a.commonAncestorContainer,r=n.querySelector("img.rz-state-selected"),l=r&&"img"==e;i;){if(i==n){l=!0;break}i=i.parentNode}if(!l)return{};var d,s=o.focusNode;return r&&"img"==e?s=r:s&&(3==s.nodeType?s=s.parentElement:(s=s.childNodes[o.focusOffset])&&(d=s.outerHTML),s&&s.matches&&!s.matches(e)&&(s=s.closest(e))),t.reduce((function(e,t){return s&&(e[t]="innerText"==t?s[t]:s.getAttribute(t)),e}),{innerText:o.toString(),innerHTML:d})},destroyEditor:function(e){e&&(e.removeEventListener("input",e.inputListener),e.removeEventListener("paste",e.pasteListener),e.removeEventListener("keydown",e.keydownListener),e.removeEventListener("click",e.clickListener),document.removeEventListener("selectionchange",e.selectionChangeListener))},startDrag:function(e,t,n){return e?(e.mouseMoveHandler=function(e){t.invokeMethodAsync(n,{clientX:e.clientX,clientY:e.clientY})},e.touchMoveHandler=function(o){o.targetTouches[0]&&e.contains(o.targetTouches[0].target)&&t.invokeMethodAsync(n,{clientX:o.targetTouches[0].clientX,clientY:o.targetTouches[0].clientY})},e.mouseUpHandler=function(t){Radzen.endDrag(e)},document.addEventListener("mousemove",e.mouseMoveHandler),document.addEventListener("mouseup",e.mouseUpHandler),document.addEventListener("touchmove",e.touchMoveHandler,{passive:!0,capture:!0}),document.addEventListener("touchend",e.mouseUpHandler,{passive:!0}),Radzen.clientRect(e)):{left:0,top:0,width:0,height:0}},submit:function(e){e.submit()},clientRect:function(e){var t=(e instanceof Element||e instanceof HTMLDocument?e:document.getElementById(e)).getBoundingClientRect();return{left:t.left,top:t.top,width:t.width,height:t.height}},endDrag:function(e){document.removeEventListener("mousemove",e.mouseMoveHandler),document.removeEventListener("mouseup",e.mouseUpHandler),document.removeEventListener("touchmove",e.touchMoveHandler),document.removeEventListener("touchend",e.mouseUpHandler)},startColumnReorder:function(e){var t=document.getElementById(e+"-drag").parentNode.parentNode,n=document.createElement("th");n.className=t.className+" rz-column-draggable",n.style=t.style,n.style.display="none",n.style.position="absolute",n.style.height=t.offsetHeight+"px",n.style.width=t.offsetWidth+"px",n.style.zIndex=2e3,n.innerHTML=t.firstChild.outerHTML,n.id=e+"visual",document.body.appendChild(n);var o=t.parentNode.querySelectorAll(".rz-column-resizer");for(let e=0;e<o.length;e++)o[e].style.display="none";Radzen[e+"end"]=function(n){var o=document.getElementById(e+"visual");if(o){document.body.removeChild(o),Radzen[e+"end"]=null,Radzen[e+"move"]=null;var a=t.parentNode.querySelectorAll(".rz-column-resizer");for(let e=0;e<a.length;e++)a[e].style.display="block"}},document.removeEventListener("click",Radzen[e+"end"]),document.addEventListener("click",Radzen[e+"end"]),Radzen[e+"move"]=function(t){var n=document.getElementById(e+"visual");if(n){if(n.style.display="block",/Edge/.test(navigator.userAgent))var o=document.body.scrollLeft,a=document.body.scrollTop;else o=document.documentElement.scrollLeft,a=document.documentElement.scrollTop;n.style.top=t.clientY+a+10+"px",n.style.left=t.clientX+o+10+"px"}},document.removeEventListener("mousemove",Radzen[e+"move"]),document.addEventListener("mousemove",Radzen[e+"move"])},stopColumnResize:function(e,t,n){var o=document.getElementById(e+"-resizer");if(o){var a=o.parentNode.parentNode;a&&Radzen[o]&&(t.invokeMethodAsync("RadzenGrid.OnColumnResized",n,a.getBoundingClientRect().width),o.style.width=null,document.removeEventListener("mousemove",Radzen[o].mouseMoveHandler),document.removeEventListener("mouseup",Radzen[o].mouseUpHandler),document.removeEventListener("touchmove",Radzen[o].touchMoveHandler),document.removeEventListener("touchend",Radzen[o].mouseUpHandler),Radzen[o]=null)}},startColumnResize:function(e,t,n,o){var a=document.getElementById(e+"-resizer"),i=a.parentNode.parentNode,r=document.getElementById(e+"-col"),l=document.getElementById(e+"-data-col"),d=document.getElementById(e+"-footer-col");Radzen[a]={clientX:o,width:i.getBoundingClientRect().width,mouseUpHandler:function(e){Radzen[a]&&(t.invokeMethodAsync("RadzenGrid.OnColumnResized",n,i.getBoundingClientRect().width),a.style.width=null,document.removeEventListener("mousemove",Radzen[a].mouseMoveHandler),document.removeEventListener("mouseup",Radzen[a].mouseUpHandler),document.removeEventListener("touchmove",Radzen[a].touchMoveHandler),document.removeEventListener("touchend",Radzen[a].mouseUpHandler),Radzen[a]=null)},mouseMoveHandler:function(e){if(Radzen[a]){var t=Radzen[a].width-(Radzen.isRTL(i)?-1:1)*(Radzen[a].clientX-e.clientX),n=parseFloat(i.style.minWidth||0),o=parseFloat(i.style.maxWidth||0);t<n&&(t=n),i.style.maxWidth&&t>o&&(t=o);var s=t+"px";i&&(i.style.width=s),r&&(r.style.width=s),l&&(l.style.width=s),d&&(d.style.width=s)}},touchMoveHandler:function(e){e.targetTouches[0]&&Radzen[a].mouseMoveHandler(e.targetTouches[0])}},a.style.width="100%",document.addEventListener("mousemove",Radzen[a].mouseMoveHandler),document.addEventListener("mouseup",Radzen[a].mouseUpHandler),document.addEventListener("touchmove",Radzen[a].touchMoveHandler,{passive:!0}),document.addEventListener("touchend",Radzen[a].mouseUpHandler,{passive:!0})},startSplitterResize:function(e,t,n,o,a,i,r,l,d,s){var c,u,m,v,p=document.getElementById(e),g=document.getElementById(n),f=document.getElementById(o),h="Horizontal"==a,z=0;function R(e){if(!e)return null;if((e=e.trim().toLowerCase()).endsWith("%"))return z*parseFloat(e)/100;if(e.endsWith("px"))return parseFloat(e);throw"Invalid value"}Array.from(p.children).forEach((e=>{z+=h?e.getBoundingClientRect().width:e.getBoundingClientRect().height})),g&&(m=(c=h?g.getBoundingClientRect().width:g.getBoundingClientRect().height)/z*100+"%"),f&&(v=(u=h?f.getBoundingClientRect().width:f.getBoundingClientRect().height)/z*100+"%"),r=R(r),l=R(l),d=R(d),s=R(s),Radzen[p]={clientPos:i,panePerc:parseFloat(m),paneNextPerc:isFinite(parseFloat(v))?parseFloat(v):0,paneLength:c,paneNextLength:isFinite(u)?u:0,mouseUpHandler:function(e){Radzen[p]&&(t.invokeMethodAsync("RadzenSplitter.OnPaneResized",parseInt(g.getAttribute("data-index")),parseFloat(g.style.flexBasis),f?parseInt(f.getAttribute("data-index")):null,f?parseFloat(f.style.flexBasis):null),document.removeEventListener("pointerup",Radzen[p].mouseUpHandler),document.removeEventListener("pointermove",Radzen[p].mouseMoveHandler),p.removeEventListener("touchmove",y),Radzen[p]=null)},mouseMoveHandler:function(e){if(Radzen[p]){t.invokeMethodAsync("RadzenSplitter.OnPaneResizing");var n=Radzen[p].panePerc+Radzen[p].paneNextPerc,o=Radzen[p].paneLength+Radzen[p].paneNextLength,a=Radzen[p].paneLength-(h&&Radzen.isRTL(e.target)?-1:1)*(Radzen[p].clientPos-(h?e.clientX:e.clientY));if(a>o&&(a=o),r&&a<r&&(a=r),l&&a>l&&(a=l),f){var i=o-a;d&&i<d&&(a=o-d),s&&i>s&&(a=o+s)}var c=a/Radzen[p].paneLength;isFinite(c)||(c=1,Radzen[p].panePerc=.1,Radzen[p].paneLength=h?g.getBoundingClientRect().width:g.getBoundingClientRect().height);var u=Radzen[p].panePerc*c;u<0&&(u=0),u>100&&(u=100),g.style.flexBasis=u+"%",f&&(f.style.flexBasis=n-u+"%")}},touchMoveHandler:function(e){e.targetTouches[0]&&Radzen[p].mouseMoveHandler(e.targetTouches[0])}};const y=e=>{e.preventDefault(),e.stopPropagation()};document.addEventListener("pointerup",Radzen[p].mouseUpHandler),document.addEventListener("pointermove",Radzen[p].mouseMoveHandler),p.addEventListener("touchmove",y,{passive:!1})},resizeSplitter(e,t){var n=document.getElementById(e);n&&Radzen[n]&&(Radzen[n].mouseMoveHandler(t),Radzen[n].mouseUpHandler(t))},openWaiting:function(){document.documentElement.scrollHeight>document.documentElement.clientHeight&&document.body.classList.add("no-scroll"),null!=Radzen.WaitingIntervalId&&clearInterval(Radzen.WaitingIntervalId),setTimeout((function(){var e=document.getElementsByClassName("rz-waiting-timer");if(0!=e.length){var t=(new Date).getTime();Radzen.WaitingIntervalId=setInterval((function(){if(null==e||null==e[0])clearInterval(Radzen.WaitingIntervalId);else{var n=new Date((new Date).getTime()-t);e[0].innerHTML=Math.floor(n/1e3)+"."+Math.floor(n%1e3/100)}}),100)}}),100)},closeWaiting:function(){document.body.classList.remove("no-scroll"),null!=Radzen.WaitingIntervalId&&(clearInterval(Radzen.WaitingIntervalId),Radzen.WaitingIntervalId=null)},toggleDictation:function(e,t){function n(){const n=window.SpeechRecognition||window.webkitSpeechRecognition;n&&((radzenRecognition=new n).componentRef=e,radzenRecognition.continuous=!0,t&&(radzenRecognition.lang=t),radzenRecognition.onresult=function(t){if(t.results.length<1)return;let n=t.results[t.results.length-1][0].transcript;e.invokeMethodAsync("OnResult",n)},radzenRecognition.onend=function(t){e.invokeMethodAsync("StopRecording"),radzenRecognition=null},radzenRecognition.start())}radzenRecognition?(radzenRecognition.componentRef._id!=e._id&&radzenRecognition.addEventListener("end",n),radzenRecognition.stop()):n()},openChartTooltip:function(e,t,n,o,a,i){Radzen.closeTooltip(o);var r=e.getBoundingClientRect();t=Math.max(2,r.left+t),n=Math.max(2,r.top+n),Radzen.openPopup(e,o,!1,null,t,n,a,i,!0,!1,!1);var l=document.getElementById(o);if(l){var d=l.children[0],s="rz-top-chart-tooltip";n-d.getBoundingClientRect().height<0&&(s="rz-bottom-chart-tooltip"),d.classList.remove("rz-top-chart-tooltip"),d.classList.remove("rz-bottom-chart-tooltip"),d.classList.add(s)}},navigateTo:function(e,t){if(e.startsWith("#")&&history.replaceState(null,"",location.pathname+location.search+e),t){const t=document.querySelector(e);t&&t.scrollIntoView({behavior:"smooth",block:"center",inline:"start"})}},registerScrollListener:function(e,t,n,o){let a;const i=o?document.querySelector(o):document.documentElement,r=n.map(document.querySelector,document);this.unregisterScrollListener(e),e.scrollHandler=()=>{const e=("HTML"===i.tagName?0:i.getBoundingClientRect().top)+i.clientHeight/2;let o,l=Number.MAX_SAFE_INTEGER;for(let t=0;t<r.length;t++){const a=r[t];if(!a)continue;const i=a.getBoundingClientRect(),d=Math.abs(i.top-e);!o&&i.top<e?(o=n[t],l=d):o&&i.top>=e||d<l&&(o=n[t],l=d)}o!==a&&(a=o,this.navigateTo(a,!1),t.invokeMethodAsync("ScrollIntoView",a))},document.addEventListener("scroll",e.scrollHandler,!0),window.addEventListener("resize",e.scrollHandler,!0)},unregisterScrollListener:function(e){document.removeEventListener("scroll",e.scrollHandler,!0),window.removeEventListener("resize",e.scrollHandler,!0)}};