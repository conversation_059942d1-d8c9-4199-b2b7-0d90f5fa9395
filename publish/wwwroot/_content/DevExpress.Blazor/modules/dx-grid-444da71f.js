import{D as e}from"./dx-html-element-base-3262304e.js";import{C as t}from"./css-classes-c63af734.js";import{H as n,T as i,a as s,P as o,c as r,d as l,e as a}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{DxUIHandlersBridgeTagName as d}from"./dx-ui-handlers-bridge-c2148178.js";import{b as h}from"./browser-3fc721b7.js";import{k as u}from"./key-ffa272aa.js";import{S as c,H as m,V as g,D as C,a as p,b as I}from"./dx-scroll-viewer-da0fb41c.js";import{S as f}from"./scroll-viewer-css-classes-e724f203.js";import{D as w,K as b,F as y,L as v}from"./keyboard-navigation-strategy-ea41c807.js";import{T as S,B as E,S as R,V as x}from"./dx-virtual-scroll-viewer-f4a3bc9e.js";import{DxCheckBoxTagName as D}from"./dx-check-internal-a6f9ec37.js";import{D as N,I as T,d as H}from"./dragging-helper-863a69d5.js";import{H as P}from"./dom-utils-d057dcaa.js";import{D as F}from"./layouthelper-67dd777a.js";import{A as W,t as B,D as V,v as K,w as L,u as A,E as O}from"./popup-355ecaa4.js";import{G as M}from"./grid-scroll-utils-a8c65cf1.js";import{containsFocusHiddenAttribute as _,removeFocusHiddenAttribute as k,addFocusHiddenAttribute as U}from"./focus-utils-ae044224.js";import{S as z}from"./svg-utils-3c1b4ead.js";import{T as $}from"./transformhelper-ebad0156.js";import{d as G}from"./dom-554d0cc7.js";class q{}q.ClassName=t.Prefix+"-grid",q.DataTableClassName=q.ClassName+"-table",q.DataTableNoScrollClassName=q.DataTableClassName+"-no-scroll",q.TopPanelClassName=q.ClassName+"-top-panel",q.BottomPanelClassName=q.ClassName+"-bottom-panel",q.ToolbarContainerClassName=q.ClassName+"-toolbar-container",q.PagerPanelClassName="dxbl-pager-container",q.GroupPanelContentContainerClassName=q.ClassName+"-group-panel-container",q.SearchBoxContainerClassName=q.ClassName+"-search-box-container",q.SearchBoxClassName=q.ClassName+"-search-box",q.EmptyHeaderCellContentContainerClassName=q.ClassName+"-empty-header",q.GroupContentFreeSpaceClassName=q.ClassName+"-group-panel-free-space",q.HeaderRowClassName=q.ClassName+"-header-row",q.EditRowClassName=q.ClassName+"-edit-row",q.FilterRowClassName=q.ClassName+"-filter-row",q.GroupRowClassName=q.ClassName+"-group-row",q.FooterRowClassName=q.ClassName+"-footer-row",q.GroupFooterRowClassName=q.ClassName+"-group-footer-row",q.PagerRowClassName=q.ClassName+"-pager-row",q.EmptyRowClassName=q.ClassName+"-empty-row",q.ArmRowClassName=q.ClassName+"-arm-row",q.HeaderElementClassName=q.ClassName+"-header",q.HeaderContentClassName=q.ClassName+"-header-content",q.AllowDragClassName=q.ClassName+"-action",q.DraggableHeaderClassName=q.ClassName+"-draggable-header",q.SelectionCellClassName=q.ClassName+"-selection-cell",q.CommandCellClassName=q.ClassName+"-command-cell",q.FixedCellClassName=q.ClassName+"-fixed-cell",q.LastLeftFixedCellClassName=q.ClassName+"-last-fixed-left-cell",q.IndentCellClassName=q.ClassName+"-indent-cell",q.HeaderIndentCellClassName=q.ClassName+"-header-indent-cell",q.GroupFooterIndentCellClassName=q.ClassName+"-group-footer-indent-cell",q.ExpandButtonCellClassName=q.ClassName+"-expand-button-cell",q.DetailCellClassName=q.ClassName+"-detail-cell",q.EmptyDataRowCellClassName=q.ClassName+"-empty-data-area",q.EmptyCellClassName=q.ClassName+"-empty-cell",q.EditFormClassName=q.ClassName+"-edit-form",q.EditNewItemRowClassName=q.ClassName+"-edit-new-item-row",q.EditNewItemRowInplaceClassName=q.ClassName+"-edit-new-item-row-inplace",q.TopFixedBodyClassName=q.ClassName+"-top-fixed-body",q.SelectedRowClassName=q.ClassName+"-selected-row",q.TouchSelectionClassName=q.ClassName+"-touch-selection",q.TouchSelectionEdgeClassName=q.ClassName+"-touch-selection-edge",q.MoveCursorCssClassName=q.ClassName+"-move-cursor",q.DraggingDownTargetClassName=q.ClassName+"-dragging-down-target",q.DraggingUpTargetClassName=q.ClassName+"-dragging-up-target",q.ColumnsResizeAnchorClassName=q.ClassName+"-column-resize-anchor",q.ColumnsSeparatorClassName=q.ClassName+"-columns-separator",q.HiddenEmptyCellClassName=q.ClassName+"-hidden-empty-cell",q.RowDragHintClassName=q.ClassName+"-row-drag-hint",q.RowDragAnchorClassName=q.ClassName+"-row-drag-anchor-cell",q.DropTargetIndicatorClassName=q.ClassName+"-drop-target-indicator",q.ColumnChooserItemClassName=q.ClassName+"-column-chooser-item",q.ColumnChooserDraggingItemClassName=q.ClassName+"-column-chooser-dragging-item",q.ColumnChooserDraggingItemNextSiblingClassName=q.ClassName+"-column-chooser-dragging-item-next-sibling",q.ColumnChooserItemDragAnchorClassName=q.ClassName+"-column-chooser-item-drag-anchor",q.ColumnChooserItemDragLockClassName=q.ClassName+"-column-chooser-item-drag-lock",q.HighlightedTextClassName=q.ClassName+"-highlighted-text",q.CalculateContentWidthsClassName=q.DataTableClassName+"-content-fit-calc";const j="dxbl-grid-header-content";class Y{get previousColumnUid(){return this._previousColumnUid}get nextColumnUid(){return this._nextColumnUid}get nextVisibleColumnUid(){return this._nextVisibleColumnUid}constructor(e,t,n){this._previousColumnUid=e,this._nextColumnUid=t,this._nextVisibleColumnUid=n}}class X{get uID(){return this._uID}get parentUID(){return this._parentUID}get allowGroup(){return this._allowGroup}get allowReorder(){return this._allowReorder}get dragRestrictions(){return this._dragRestrictions}get width(){return this._width}get minWidth(){return this._minWidth}get hasWidth(){return!!this._width}get isFreeWidth(){return!this.width&&!this.isIndent&&!this.isEmpty}get isPercentWidth(){return this.hasWidth&&-1!==this.width.indexOf("%")}get isAbsoluteWidth(){return this.hasWidth&&!this.isPercentWidth||this.isIndent}get colElementWidth(){return this._colElementWidth}set colElementWidth(e){this._colElementWidth=e}get isIndent(){return this._isIndent}get isEmpty(){return this._isEmpty}get isDetail(){return this._isDetail}get isEditable(){return this._isEditable}get fixedPosition(){return this._fixedPosition}get headerRowIndex(){return this._headerRowIndex}get headerCellIndex(){return this._headerCellIndex}get leafIndex(){return this._leafIndex}get parent(){return this._parent}get parentPath(){return this._parentPath}get isRoot(){return this.parentUID<0}get hasLeafs(){return this._leafs.length>0}constructor(e,t,n,i,s,o,r=!1,l=!1,a=!1,d=!1,h=!0,u=null){this._parent=null,this._parentPath=[],this._leafs=[],this._leafIndex=-1,this._headerRowIndex=-1,this._headerCellIndex=-1,this._uID=e,this._parentUID=t,this._allowGroup=n,this._allowReorder=h,this._dragRestrictions=u,this._width=i,this._colElementWidth=i,this._minWidth=s,this._isIndent=r,this._isEmpty=l,this._isDetail=a,this._isEditable=d,this._fixedPosition=o}getPercents(){return this.isPercentWidth?parseFloat(this.width.replace("%","")):0}updateWidth(e){this._width=e,this._colElementWidth=e}updateHeaderLayoutInfo(e,t){this._headerRowIndex=e,this._headerCellIndex=t}attachParent(e){var t,n;this._parent=e,this._parentPath=[...null!==(n=null===(t=this.parent)||void 0===t?void 0:t.parentPath)&&void 0!==n?n:[],e]}attachLeaf(e){this._leafs.push(e)}updateLeafIndex(e){this._leafIndex=e}containsLeaf(e){return this.hasLeafs?this._leafs.findIndex((t=>t.uID===e))>=0:this.uID===e}getLeftLeaf(){return this.hasLeafs?this._leafs[0]:null}getRightLeaf(){return this.hasLeafs?this._leafs[this._leafs.length-1]:null}}const J="dxbl-grid-drop-restrictions-info",Q="data";var Z,ee,te;!function(e){e[e.ColumnsChanged=0]="ColumnsChanged",e[e.GridResize=1]="GridResize"}(Z||(Z={}));class ne{constructor(e){this.grid=e}correctWidths(e){if(0===this.grid.columnsInfo.length)return!1;const t=this.grid.getDataTable();return!!t&&("fixed"===window.getComputedStyle(t).tableLayout&&(requestAnimationFrame((()=>{const t=this.grid.columnsInfo,n=this.grid.getColElements();if(e===Z.ColumnsChanged&&ne.resetColumnWidths(t,n),!ne.canAcquireAbsoluteColumnWidths(this.grid))return;this.updateEmptyColumnVisibility();const i=new Map;for(let e=0;e<this.grid.columnsInfo.length;++e){const t=this.grid.columnsInfo[e];t.isAbsoluteWidth&&i.set(t,this.grid.getColElementOffsetWidth(e))}const s={columnsInfo:this.grid.columnsInfo,columnCurrentWidths:new Map,tableWidth:this.grid.getTableContainerWidth(),absoluteColumnWidths:i,percentColumns:this.grid.columnsInfo.filter((e=>e.isPercentWidth)),freeColumns:this.grid.columnsInfo.filter((e=>e.isFreeWidth)),emptyColumn:this.grid.columnsInfo.filter((e=>e.isEmpty))[0]};ne.doDistributionWaves(s),ne.applyColumnWidths(s,n),this.updateEmptyColumnVisibility(),this.grid.styleGenerator.updateFixedCellsStyle()})),!0))}autoFitColumnWidths(){const e=ne.calculateContentWidths(this.grid);let t=0,n=0;for(const[i,s]of e)i.isIndent||(t+=s,i.isAbsoluteWidth&&(n+=s));const i=t-n,s=[],o=[];for(const[t,n]of e){const e=t.isPercentWidth||t.isFreeWidth?n/i*100+"%":`${n}px`;t.updateWidth(e),s.push(t.uID),o.push(e)}this.grid.notifyColumnsChanged(this.grid.columnsInfo,this.grid.headerLayout),this.grid.onColumnWidthsChanged(s,o)}autoFitColumnWidthsLegacy(e=!1){const t=ne.calculateContentWidths(this.grid);let n=0;for(const[e,i]of t)e.isIndent||(n+=i);const i=[],s=[];for(const[o,r]of t){const t=e?r/n*100+"%":`${r}px`;o.updateWidth(t),i.push(o.uID),s.push(t)}this.grid.notifyColumnsChanged(this.grid.columnsInfo,this.grid.headerLayout),this.grid.onColumnWidthsChanged(i,s)}static calculateContentWidths(e){const t=new Map,n=e.getDataTable();if(n){n.classList.add(q.CalculateContentWidthsClassName);const i=e.getColElements();for(let n=0;n<i.length;n++){const i=e.columnsInfo[n];if(i&&-1!==i.uID){const s=e.getColElementOffsetWidth(n),o=s?Math.ceil(s)+1:0;t.set(i,o)}}n.classList.remove(q.CalculateContentWidthsClassName)}return t}updateEmptyColumnVisibility(){const e=this.grid.getColElements(),t=e[e.length-1];ne.hasAnyDataColumnWithEmptyWidth(this.grid.columnsInfo,e)?t.classList.add(q.HiddenEmptyCellClassName):t.classList.remove(q.HiddenEmptyCellClassName)}static resetColumnWidths(e,t){for(let n=0;n<t.length;n++){const i=e[n];!i||i.isIndent||i.isEmpty||(t[n].style.width=i.width)}}static doDistributionWaves(e){let t=!1,n=0;do{ne.performAllColumnsRealWidthCalculation(e),t=ne.evaluateColumnMinWidthCorrection(e)}while(t&&++n<=e.columnsInfo.length)}static performAllColumnsRealWidthCalculation(e){e.columnCurrentWidths=new Map;const t=ne.calculateAbsoluteColumnWidths(e);let n=Math.max(0,e.tableWidth-t);n=ne.calculatePercentColumnWidths(e,n),n=ne.calculateFreeColumnWidths(e,n),ne.calculateEmptyColumnWidth(e,n)}static calculateAbsoluteColumnWidths(e){let t=0;for(const n of e.absoluteColumnWidths.keys()){const i=e.absoluteColumnWidths.get(n);t+=i,e.columnCurrentWidths.set(n,i)}return t}static calculatePercentColumnWidths(e,t){let n=0;for(const t of e.percentColumns)n+=t.getPercents();let i=e.tableWidth;const s=n>100?100/n:1,o=i*(n*s)/100;i*=t<o?t/o:1;let r=0;for(const t of e.percentColumns){const n=i*(t.getPercents()*s/100);e.columnCurrentWidths.set(t,n),r+=n}return t-r}static calculateFreeColumnWidths(e,t){if(e.freeColumns.length>0){const n=t/e.freeColumns.length;for(const t of e.freeColumns)e.columnCurrentWidths.set(t,n);return 0}return t}static calculateEmptyColumnWidth(e,t){e.emptyColumn&&e.columnCurrentWidths.set(e.emptyColumn,t)}static evaluateColumnMinWidthCorrection(e){const t=e.absoluteColumnWidths.size;for(const t of e.columnCurrentWidths.keys())e.columnCurrentWidths.get(t)>t.minWidth||0===t.minWidth||e.absoluteColumnWidths.set(t,t.minWidth);return e.percentColumns=e.percentColumns.filter((t=>!e.absoluteColumnWidths.has(t))),e.freeColumns=e.freeColumns.filter((t=>!e.absoluteColumnWidths.has(t))),t!==e.absoluteColumnWidths.size}static applyColumnWidths(e,t){for(let n=0;n<e.columnsInfo.length;++n){const i=e.columnsInfo[n];if(i.isIndent)continue;const s=e.absoluteColumnWidths.has(i)?`${e.absoluteColumnWidths.get(i)}px`:i.width;e.columnsInfo[n].colElementWidth=s,t[n].style.width=s}}static hasAnyDataColumnWithEmptyWidth(e,t){let n=0;for(let i=0;i<e.length;i++){const s=e[i];if(!s.isIndent&&!s.isEmpty&&(++n,!t[i].style.width))return!0}return 0===n}static canAcquireAbsoluteColumnWidths(e){for(let t=0;t<e.columnsInfo.length;++t){if(e.columnsInfo[t].isAbsoluteWidth&&null===e.getColElementOffsetWidth(t))return!1}return!0}}!function(e){e[e.Mouse=1]="Mouse",e[e.Keyboard=2]="Keyboard",e[e.DblClick=3]="DblClick"}(ee||(ee={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(te||(te={}));class ie{get isResizing(){return!!this.resizeReason}get columnIndex(){return this._columnIndex}get columnsInfo(){return this.grid.columnsInfo}get tableContainerWidth(){return this._tableContainerWidth}get currentColumnInfo(){return this.columnsInfo[this.columnIndex]}constructor(e){this.startMousePosition=-1,this.startHeaderCellPosition=-1,this.timeoutId=null,this.initialColumnWidths=new Map,this.boundOnDocumentPointerMoveHandler=this.onPointerMove.bind(this),this.getColumnOffsetWidth=e=>this.grid.getColElementOffsetWidth(e),this.setColumnWidth=(e,t)=>{this.grid.getColElements()[e].style.width=t},this.grid=e,this.columnResizeSeparator=e.getColumnResizeSeparator()}startDrag(e,n){var i;this.initializeResizing(e),this.resizeReason=ee.Mouse,this.startMousePosition=n,this.startHeaderCellPosition=this.getHeaderCellPosition(this.columnIndex),document.addEventListener("pointermove",this.boundOnDocumentPointerMoveHandler),document.body.classList.add(t.ResizeCursor),null===(i=this.grid.getScrollViewer())||void 0===i||i.startAutoScrolling(c.Horizontal,this.boundOnDocumentPointerMoveHandler)}autoFitColumnWidth(e){if(this.isResizing)return;const t=ne.calculateContentWidths(this.grid).get(this.columnsInfo[e]);if(void 0!==t){this.resizeReason=ee.DblClick,this.initializeResizing(e);const n=t-this.getColumnOffsetWidth(e),i=n>1?Math.ceil(n):Math.floor(n);this.performResize(i),requestAnimationFrame((()=>setTimeout((()=>this.finalizeResizing(!1)))))}}anchorFocused(e){this.resizeReason=ee.Keyboard,this.initializeResizing(e)}anchorUnfocused(){this.finalizeResizing(!1)}stopDrag(){this.finalizeResizing(!1)}cancelDrag(){this.timeoutId&&clearTimeout(this.timeoutId),this.finalizeResizing(!0)}performMoveStep(e){if(this.resizeReason===ee.Mouse)return;let t=e===te.Right?1:-1;this.isReverseResizing&&(t*=-1);const n=this.calculateDiffByMoveStep(t);this.performResize(n),this.timeoutId&&clearTimeout(this.timeoutId),this.timeoutId=setTimeout((()=>this.createNotificationAction()()),500)}initializeResizing(e){var t;this._tableContainerWidth=this.grid.getTableContainerWidth(),this.columnResizeSeparator=this.grid.getColumnResizeSeparator(),e=this.calculateResizingColumnIndex(e),this._columnIndex=e,this.initialColumnWidths.clear();const n=this.grid.getColElements();for(let e=0;e<n.length;++e)this.initialColumnWidths.set(e,n[e].style.width);this.initializeResizingCore();const i=this.columnsInfo[e],s=null!==(t=i.parentPath.find((e=>e.getRightLeaf()===i)))&&void 0!==t?t:i,o=this.grid.getHeaderCell(s.headerRowIndex,s.headerCellIndex);o&&this.columnResizeSeparator.show(o,this.isReverseResizing),this.grid.updateEmptyColumnVisibility()}performResize(e){var t;e=Math.round(e),this.performResizeCore(e),requestAnimationFrame((()=>this.grid.styleGenerator.updateFixedCellsStyle())),null===(t=this.columnResizeSeparator)||void 0===t||t.render()}finalizeResizing(e){var n,i;if(!this.resizeReason)return;this.resizeReason===ee.Mouse&&(document.removeEventListener("pointermove",this.boundOnDocumentPointerMoveHandler),document.body.classList.remove(t.ResizeCursor),null===(n=this.grid.getScrollViewer())||void 0===n||n.stopAutoScrolling(),this.startMousePosition=-1,this.startHeaderCellPosition=-1),null===(i=this.columnResizeSeparator)||void 0===i||i.hide();let s=null;if(e){this.cancelResizeCore();const e=this.grid.getColElements();this.initialColumnWidths.forEach(((t,n)=>{t!==e[n].style.width&&(e[n].style.width=t)})),this.initialColumnWidths.clear(),requestAnimationFrame((()=>this.grid.styleGenerator.updateFixedCellsStyle()))}else s=this.createNotificationAction(),this.finalizeResizeCore();this.grid.updateEmptyColumnVisibility(),this.resizeReason=void 0,this._columnIndex=void 0,this._tableContainerWidth=void 0,this.timeoutId&&clearTimeout(this.timeoutId),s&&s()}createNotificationAction(){const e=this.applyResizingAndGetChangedColumns(),t=e.map((e=>e.uID)),n=e.map((e=>e.width));return()=>{this.grid.onColumnWidthsChanged(t,n)}}getHeaderCellPosition(e){var t;const n=this.grid.findHeaderCellByColumnIndex(e);return null!==(t=null==n?void 0:n.getBoundingClientRect().x)&&void 0!==t?t:-1}onPointerMove(e){const t=this.getHeaderCellPosition(this.columnIndex);let n=e.pageX-this.startMousePosition;if(this.isReverseResizing)n*=-1;else{n+=this.startHeaderCellPosition-t}this.performResize(n)}calculateResizingColumnIndex(e){return e}}class se extends ie{constructor(){super(...arguments),this.columnInitialWidth=-1,this.columnNewWidth=-1,this.nextColumnInitialWidth=-1,this.nextColumnNewWidth=-1,this.nextColumnIndex=-1,this.totalPercents=0,this.totalFreeWidth=0}get nextColumnInfo(){return this.columnsInfo[this.nextColumnIndex]}get isReverseResizing(){return!1}calculateDiffByMoveStep(e){return this.columnNewWidth-this.columnInitialWidth+Math.min(5,Math.ceil((this.columnInitialWidth+this.nextColumnInitialWidth)/100))*e}initializeResizingCore(){this.columnInitialWidth=this.getColumnOffsetWidth(this.columnIndex),this.columnNewWidth=this.columnInitialWidth,this.nextColumnIndex=this.columnIndex+1,this.nextColumnInitialWidth=this.getColumnOffsetWidth(this.nextColumnIndex),this.nextColumnNewWidth=this.nextColumnInitialWidth;for(let e=0;e<this.columnsInfo.length;++e){const t=this.columnsInfo[e];t.isPercentWidth&&(this.totalPercents+=t.getPercents()),t.isFreeWidth&&(this.totalFreeWidth+=this.getColumnOffsetWidth(e))}}performResizeCore(e){this.columnNewWidth=this.columnInitialWidth+e,this.columnNewWidth<this.currentColumnInfo.minWidth&&(this.columnNewWidth=this.currentColumnInfo.minWidth),this.nextColumnNewWidth=this.columnInitialWidth+this.nextColumnInitialWidth-this.columnNewWidth;const t=this.columnInitialWidth+this.nextColumnInitialWidth-this.nextColumnInfo.minWidth;this.columnNewWidth>t&&(this.columnNewWidth=t,this.nextColumnNewWidth=this.nextColumnInfo.minWidth),requestAnimationFrame((()=>{this.isResizing&&(this.setColumnWidth(this.columnIndex,`${this.columnNewWidth}px`),this.setColumnWidth(this.nextColumnIndex,`${this.nextColumnNewWidth}px`))}))}cancelResizeCore(){this.reset()}applyResizingAndGetChangedColumns(){const e=[];if(this.currentColumnInfo.isPercentWidth||this.nextColumnInfo.isPercentWidth)return this.currentColumnInfo.isPercentWidth?this.calculateNewWidthsForPercentColumn(this.currentColumnInfo,this.columnNewWidth,this.columnInitialWidth,this.nextColumnInfo,this.nextColumnNewWidth,this.nextColumnInitialWidth):this.calculateNewWidthsForPercentColumn(this.nextColumnInfo,this.nextColumnNewWidth,this.nextColumnInitialWidth,this.currentColumnInfo,this.columnNewWidth,this.columnInitialWidth);{const t=0!==this.totalPercents,n=(this.currentColumnInfo.isFreeWidth||this.nextColumnInfo.isFreeWidth)&&!t;if((this.currentColumnInfo.isAbsoluteWidth||this.currentColumnInfo.isFreeWidth&&t)&&(this.currentColumnInfo.updateWidth(`${this.columnNewWidth}px`),e.push(this.currentColumnInfo)),n){const t=this.totalFreeWidth+(this.currentColumnInfo.isFreeWidth?this.columnNewWidth-this.columnInitialWidth:0)+(this.nextColumnInfo.isFreeWidth?this.nextColumnNewWidth-this.nextColumnInitialWidth:0);for(let n=0;n<this.columnsInfo.length;++n){const i=this.columnsInfo[n];i.isFreeWidth&&(i.updateWidth(100*this.getColumnOffsetWidth(n)/t+"%"),e.push(i))}}}return e.includes(this.nextColumnInfo)||(this.nextColumnInfo.updateWidth(`${this.nextColumnNewWidth}px`),e.push(this.nextColumnInfo)),e}calculateNewWidthsForPercentColumn(e,t,n,i,s,o){const r=[],l=(e,t,n)=>e*t/n,a=e.getPercents(),d=l(a,t,n);if(e.updateWidth(e.colElementWidth===e.width?`${d}%`:`${t}px`),r.push(e),i.isPercentWidth)i.updateWidth(`${l(i.getPercents(),s,o)}%`),r.push(i);else{i.updateWidth(`${s}px`),r.push(i);const t=this.totalPercents-a+d;if(t>100){const n=t/this.totalPercents;for(let t=0;t<this.columnsInfo.length;++t){const i=this.columnsInfo[t];i.isPercentWidth&&(i.updateWidth(i.getPercents()/n+"%"),i!==e&&r.push(i))}}}return r}finalizeResizeCore(){this.reset()}reset(){this.columnInitialWidth=-1,this.columnNewWidth=-1,this.nextColumnInitialWidth=-1,this.nextColumnNewWidth=-1,this.nextColumnIndex=-1,this.totalFreeWidth=0,this.totalPercents=0}}class oe extends ie{constructor(){super(...arguments),this.enforcedColumnWidths=new Map,this.columnInitialWidth=-1,this.columnsInitialTotalWidth=-1,this.columnNewWidth=-1}get isReverseResizing(){return"right"===this.currentColumnInfo.fixedPosition}calculateDiffByMoveStep(e){return this.columnNewWidth-this.columnInitialWidth+Math.min(5,Math.ceil(2*this.columnInitialWidth/100))*e}initializeResizingCore(){if(this.columnInitialWidth=this.getColumnOffsetWidth(this.columnIndex),this.columnNewWidth=this.columnInitialWidth,this.currentColumnInfo.fixedPosition){let e=this.tableContainerWidth;for(let t=0;t<this.columnsInfo.length;++t){const n=this.columnsInfo[t];n!==this.currentColumnInfo&&n.fixedPosition&&(e-=this.getColumnOffsetWidth(t))}this.columnMaxWidth=Math.max(this.columnInitialWidth,e)}this.enforcedColumnWidths.clear();for(let e=0;e<this.columnsInfo.length;++e){const t=this.columnsInfo[e];if(!t.isIndent&&!t.isEmpty&&!t.isAbsoluteWidth){const t=`${this.getColumnOffsetWidth(e)}px`;this.setColumnWidth(e,t),this.enforcedColumnWidths.set(e,t)}}}performResizeCore(e){let t=this.columnInitialWidth+e;if(t<this.currentColumnInfo.minWidth&&(t=this.currentColumnInfo.minWidth),this.columnMaxWidth&&t>this.columnMaxWidth&&(t=this.columnMaxWidth),this.isReverseResizing){const e=this.columnsInitialTotalWidth-this.columnInitialWidth;e+t<this.tableContainerWidth&&(t=this.tableContainerWidth-e)}this.columnNewWidth=t;const n=this.columnIndex;requestAnimationFrame((()=>{this.isResizing&&this.setColumnWidth(n,`${t}px`)}))}cancelResizeCore(){this.reset()}applyResizingAndGetChangedColumns(){const e=[],t=this.columnsInfo,n=t[this.columnIndex];return n.updateWidth(`${this.columnNewWidth}px`),e.push(n),this.enforcedColumnWidths.forEach(((i,s)=>{const o=t[s];o!==n&&(o.updateWidth(i),e.push(o))})),e}calculateResizingColumnIndex(e){if("right"===this.columnsInfo[e].fixedPosition){let t=0;for(let e=0;e<this.columnsInfo.length;++e)this.columnsInfo[e].isEmpty||(t+=this.getColumnOffsetWidth(e));if(this.columnsInitialTotalWidth=t,t<this.tableContainerWidth)return e-1}return e}finalizeResizeCore(){this.reset()}reset(){this.columnInitialWidth=-1,this.columnsInitialTotalWidth=-1,this.columnNewWidth=-1,this.enforcedColumnWidths.clear(),this.columnMaxWidth=void 0}}class re extends e{constructor(){super(),this.cell=null,this.leftSide=!1,this.parentWidth=0,this.tableContainerResizeObserver=new ResizeObserver(this.onTableContainerSizeChanged.bind(this)),this.boundOnTableContainerScroll=this.onTableContainerScroll.bind(this)}get tableContainer(){return this._tableContainer}initializeComponent(){super.initializeComponent(),this.initializeTableContainer(),this.tableContainer.addEventListener("scroll",this.boundOnTableContainerScroll),this.tableContainerResizeObserver.observe(this.tableContainer)}initializeTableContainer(){this._tableContainer=this.closest(`.${f.ContentContainerClassName}`)}disposeComponent(){super.disposeComponent(),this.tableContainer.removeEventListener("scroll",this.boundOnTableContainerScroll),this.tableContainerResizeObserver.unobserve(this.tableContainer)}show(e,t){this.cell=e,this.leftSide=t,this.classList.add(re.ActiveColumnResizeSeparator),this.render()}hide(){this.cell=null,this.leftSide=!1,this.classList.remove(re.ActiveColumnResizeSeparator),this.style.transform="none"}render(){requestAnimationFrame((()=>{if(!this.cell)return;const e=this.getColumnSeparatorPosition(this.cell),t=this.getBorderWidth(this.cell),n=re.getCenteredByBorderPosition(e,this.offsetWidth,t),i=this.getActualHeight(),s=this.cell.offsetTop;this.style.transform=s>0?`translate(${n},${s}px)`:`translateX(${n})`,i>0&&(this.style.height=i-s+"px")}))}onTableContainerScroll(){this.render()}onTableContainerSizeChanged(e){const t=e[0].contentRect.width;this.parentWidth!==t&&(this.parentWidth=t,this.render())}getColumnSeparatorPosition(e){const t=e.offsetLeft-this.tableContainer.scrollLeft;return this.leftSide?t:t+e.offsetWidth}getActualHeight(){const e=this.tableContainer.querySelector(`.${q.DataTableClassName} > tfoot`);if(!e)return 0;const t=e.getBoundingClientRect().height;return this.tableContainer.getBoundingClientRect().height-t}getBorderWidth(e){var t;const n=re.getBorderWidthByElement(e,"border-right-width");if(n>0)return n;const i=null===(t=this.cell)||void 0===t?void 0:t.nextElementSibling;return i&&re.getBorderWidthByElement(i,"border-left-width")}static getBorderWidthByElement(e,t){const n=getComputedStyle(e),i=n.getPropertyValue(t);return parseFloat(i)||parseFloat(n.borderWidth)}static getCenteredByBorderPosition(e,t,n){return e-(t-n)/2+"px"}}re.ActiveColumnResizeSeparator=t.Active;const le="data-column-uid",ae="data-parent-column-uid";var de,he,ue,ce,me,ge,Ce;!function(e){e[e.ItemMoving=0]="ItemMoving",e[e.Stop=1]="Stop",e[e.Cancel=2]="Cancel"}(de||(de={}));class pe{constructor(e){this.boundOnDocumentMouseMoveHandler=this.onDocumentMouseMove.bind(this),this.reorderContext=null,this.reorderingCallback=e}start(e,t,n){const i=e.map((e=>e.getBoundingClientRect())),s=e.map((e=>e.getAttribute(le))),o=e.map((e=>e.getAttribute(ae))),r=e.map((e=>e.getAttribute("data-column-fixed-position"))),l=e.indexOf(t),a=pe.populateItemGroup(l,s,o),d=t.parentElement,h=pe.calcOffsetParentY(d),u=pe.calculateReorderIntervals(i,a,s,o,r),c=pe.findInterval(u,0);this.reorderContext={state:de.ItemMoving,startY:n.y,currentY:n.y,offsetParentStartY:h,offsetParent:d,draggingItemIndices:a,draggingItem:t,items:e,intervals:u,initialInterval:c,currentInterval:c},this.refreshUI(),document.addEventListener("pointermove",this.boundOnDocumentMouseMoveHandler)}stop(){this.end(de.Stop)}cancel(){this.end(de.Cancel)}end(e){null!=this.reorderContext&&(this.reorderContext.state=e,this.refreshUI(),document.removeEventListener("pointermove",this.boundOnDocumentMouseMoveHandler))}onDocumentMouseMove(e){null!=this.reorderContext&&(this.reorderContext.currentY=e.y,this.refreshUI())}refreshUI(){if(null==this.reorderContext)return;const e=this.reorderContext.offsetParentStartY-pe.calcOffsetParentY(this.reorderContext.offsetParent),t=this.reorderContext.currentY-this.reorderContext.startY+e;let n=pe.findInterval(this.reorderContext.intervals,t);this.reorderContext.state===de.Cancel&&(n=this.reorderContext.initialInterval),this.reorderContext.currentInterval=n,this.applyTransformations(t)}applyTransformations(e){requestAnimationFrame((()=>{if(null==this.reorderContext)return;const t=this.reorderContext.currentInterval;if(null==t)return;const n=this.reorderContext.state===de.ItemMoving&&e>=t.top&&e<=t.bottom;for(let i=0;i<t.transforms.length;i++){let s=t.transforms[i];this.reorderContext.draggingItemIndices.includes(i)&&n&&(s=e),this.reorderContext.items[i].style.transform="translate(0, "+s+"px)"}this.reorderingCallback(this.reorderContext.items,t.itemIndices,this.reorderContext.draggingItemIndices,this.reorderContext.state)}))}static calcOffsetParentY(e){return e.getBoundingClientRect().y-e.scrollTop}static calculateReorderIntervals(e,t,n,i,s){const o=t[0],r=t.length,l=pe.calcIntervalBounds(e,o,n,i,s),a=Array.from(Array(e.length).keys());a.splice(o,r);const d=Math.ceil(pe.calculateTotalGroupHeight(t,e)/r);return l.map((e=>{const n=Array.from(a),i=e.itemIndex>o?1:0,s=e.itemIndex!==o?n.indexOf(e.itemIndex)+i:e.itemIndex;return n.splice(s,0,...t),{top:e.top,bottom:e.bottom,itemIndices:n,transforms:this.calcItemsTransformations(n,d),itemIndex:e.itemIndex}}))}static calcIntervalBounds(e,t,n,i,s){const o=Array.from(Array(e.length).keys()),r=s[t],l=i[t];let a=0!==t?o.slice(0,t).reverse():[],d=t!==o.length-1?o.slice(t+1):[];s.some((e=>null!==e))&&(a=a.filter((e=>s[e]===r)),d=d.filter((e=>s[e]===r))),i.some((e=>null!==e))&&(a=a.filter((e=>i[e]===l)),d=d.filter((e=>i[e]===l)));const h=a.map((e=>pe.populateItemGroup(e,n,i))),u=d.map((e=>pe.populateItemGroup(e,n,i))),c=this.calcIntervalBoundsCore(e,h,!0),m=this.calcIntervalBoundsCore(e,u,!1),g=[];c.reverse();for(let e=0;e<c.length;e++){const t=c[e];g.push({top:-1*t.bottom,bottom:-1*t.top,itemIndex:t.itemIndex})}return g.push({top:g.length>0?g[g.length-1].bottom+1:0,bottom:m.length>0?m[0].top-1:0,itemIndex:t}),g.concat(m)}static calcIntervalBoundsCore(e,t,n=!0){if(0===t.length)return[];let i=0;const s=[];for(let n=0;n<t.length;n++){const o=pe.calculateTotalGroupHeight(t[n],e);s.push(i+Math.ceil(o/2)),i+=o}s.push(i);const o=[];for(let e=0;e<s.length-1;e++){const i=t[e];o.push({top:1+s[e],bottom:s[e+1],itemIndex:i[n?0:i.length-1]})}return o}static calcItemsTransformations(e,t){const n=[];for(let i=0;i<e.length;i++){let s=0;const o=e.indexOf(i)-i;0!==o&&(s=t*o),n.push(s)}return n}static findInterval(e,t){for(let n=0;n<e.length;n++)if(t>=e[n].top&&t<=e[n].bottom)return e[n];return t<e[0].top?e[0]:t>e[e.length-1].bottom?e[e.length-1]:null}static populateItemGroup(e,t,n){const i=[e],s=new Set;s.add(t[e]);for(let o=e+1;o<t.length;o++){const e=n[o];e&&s.has(e)&&(i.push(o),s.add(t[o]))}return i}static calculateTotalGroupHeight(e,t){return e.reduce(((e,n)=>e+t[n].height),0)}}class Ie{}Ie.VisibleIndexName="data-visible-index",Ie.SummaryItem=q.ClassName+"-summary-item",Ie.EditCell="data-edit-cell",Ie.FocusedRowHidden="dxbl-grid-focused-row-hidden";class fe{static getBodyCellsSelector(e,t){return this.getDataTableBodySelector(e,t)+" > tr > td"}static getBodyEditRowSelector(e,t){return this.getDataTableBodySelector(e,t)+` > .${q.EditRowClassName}`}static getBodyEditCellSelector(e,t){return this.getBodyEditRowSelector(e,t)+`> td[${Ie.EditCell}]`}static getDataTableBodySelector(e,t){return`${null!=t?t:ct}[${mt}="${e}"] > .${f.ClassName} > .${f.ContentContainerClassName} > .${q.DataTableClassName} > tbody`}}fe.DataTableSelector=`.${q.DataTableClassName}`,fe.TableBodyRows=`.${q.DataTableClassName} > tbody > tr`,fe.TableCell=`${fe.TableBodyRows} > td`,fe.TableHeader=`.${q.DataTableClassName} > thead > tr > th`,fe.TableHeaderCell=`${fe.TableHeader} > ${q.HeaderContentClassName}`,fe.TableHeaderCellContent=`${fe.TableHeaderCell} span`,fe.TableGroupFooterCell=`${fe.TableBodyRows}.${q.GroupFooterRowClassName} > td`,fe.TableGroupFooterCellContent=`${fe.TableGroupFooterCell} > div[${Ie.SummaryItem}]`,fe.TableFooterCell=`.${q.DataTableClassName} > tfoot > tr > td`,fe.TableFooterCellContent=`${fe.TableFooterCell} > div[${Ie.SummaryItem}]`,fe.TableCellSelector=`.${q.DataTableClassName} > * > tr > td`,fe.TitleElementsSelector=[fe.TableCellSelector,fe.TableHeaderCellContent,fe.TableHeader,fe.TableFooterCellContent,fe.TableGroupFooterCellContent,fe.TableFooterCell].join(", "),fe.DataTablePartsSelector=":scope > *:not(colgroup)",fe.ColElementSelector=`:scope > .${f.ClassName} > .${f.ContentContainerClassName} > .${q.DataTableClassName} > colgroup > col`,fe.RowSelector=`:scope > tr:not(.${q.EmptyRowClassName}):not(.${q.ArmRowClassName}):not([${S}]):not([${E}])`,fe.HeaderRowSelector=`.${q.HeaderRowClassName}`,fe.FilterRowSelector=`.${q.FilterRowClassName}`,fe.EditRowSelector=`.${q.EditRowClassName}`,fe.EditFormRowCellSelector=`:scope > .${q.EditFormClassName}`,fe.EditNewItemRowSelector=`.${q.EditNewItemRowClassName}`,fe.GroupRowSelector=`.${q.GroupRowClassName}`,fe.DataRowSelector=`tr[${Ie.VisibleIndexName}]:not(.${q.EditRowClassName}):not(.${q.GroupRowClassName})`,fe.EmptyDataRowCellSelector=`:scope > .${q.EmptyDataRowCellClassName}`,fe.DetailRowCellSelector=`:scope > .${q.DetailCellClassName}`,fe.FooterRowSelector=`.${q.FooterRowClassName}`,fe.GroupFooterRowSelector=`.${q.GroupFooterRowClassName}`,fe.KeyboardNavigatorSelector=`:scope > ${w}`,fe.ToolbarContainerSelector=`.${q.ToolbarContainerClassName}`,fe.TopPanelSelector=`.${q.TopPanelClassName}`,fe.PagerPanelSelector=`.${q.PagerPanelClassName}`,fe.CellCheckBoxDisplayViewSelector="td .dxbl-checkbox-display-view-unchecked, td .dxbl-checkbox-display-view-checked, td .dxbl-checkbox-display-view-indeterminate",fe.CellCheckBoxEditViewInputSelector=`:scope > ${D} input, :scope > .dxbl-checkbox-container > ${D} input`;class we extends b{constructor(e,t){super(e.getKeyboardNavigator(),t),this.grid=e}canSwitchToNestedContentMode(){return!0}get editorManager(){return this.grid.editorManager}get isEditing(){return this.grid.isEditing}get isInplaceEditingEnabled(){return this.grid.enableInplaceEditing}get isFocusedRowEnabled(){return this.grid.enableFocusedRow}get isSelectionRowEnabled(){return this.grid.allowSelectRowByClick}get hasRowClickEvent(){return this.grid.hasRowClickEvent}getToolbarContainer(){return this.grid.getToolbarContainer()}getDataTable(){return this.grid.getDataTable()}getPagerPanel(e){return this.grid.getPagerPanel(e)}getGroupPanelContentContainer(){return this.grid.getGroupPanelContentContainer()}getSearchBoxContainer(){return this.grid.getSearchBoxContainer()}getScrollViewer(){return this.grid.getScrollViewer()}getScrollViewerContent(){return this.grid.getScrollViewerContent()}getColumnUID(e){return this.grid.getColumnUID(e)}getHeaderColumnInfo(e){return this.grid.getColumnInfo(e)}getColumnInfoByCell(e){return this.grid.getColumnInfoByCell(e)}findHeaderCellByColumnIndex(e){return this.grid.findHeaderCellByColumnIndex(e)}handleTabKeyDown(e,t=!1,n=!1){if(!t&&e.altKey)return!1;if(e.shiftKey){if(this.firstItemSelected&&!n)return!1;this.moveToPrevItem(n)}else{if(this.lastItemSelected&&!n)return!1;this.moveToNextItem(n)}return!0}}!function(e){e[e.None=0]="None",e[e.GroupPanel=1]="GroupPanel",e[e.DataTable=2]="DataTable",e[e.Pager=3]="Pager",e[e.ColumnChooser=4]="ColumnChooser",e[e.ToolbarContainer=5]="ToolbarContainer",e[e.SearchBox=6]="SearchBox"}(he||(he={})),function(e){e[e.None=0]="None",e[e.Header=1]="Header",e[e.Filter=2]="Filter",e[e.Edit=3]="Edit",e[e.EditForm=4]="EditForm",e[e.Group=5]="Group",e[e.Data=6]="Data",e[e.EmptyData=7]="EmptyData",e[e.Detail=8]="Detail",e[e.Footer=9]="Footer",e[e.GroupFooter=10]="GroupFooter",e[e.EmptyRow=11]="EmptyRow",e[e.EditNewItemRow=12]="EditNewItemRow"}(ue||(ue={})),function(e){e[e.Data=0]="Data",e[e.Edit=1]="Edit",e[e.Selection=2]="Selection",e[e.Command=3]="Command",e[e.Indent=4]="Indent",e[e.ExpandButton=5]="ExpandButton",e[e.EditNewItemRowCell=6]="EditNewItemRowCell"}(ce||(ce={}));class be{static getNavigationAreaType(e){return e.matches(fe.ToolbarContainerSelector)?he.ToolbarContainer:e.matches(fe.DataTableSelector)?he.DataTable:e.matches(fe.PagerPanelSelector)?he.Pager:e.matches(fe.TopPanelSelector)?he.GroupPanel:he.None}static getRowType(e){return e.matches(fe.HeaderRowSelector)?ue.Header:e.matches(fe.FilterRowSelector)?ue.Filter:e.matches(fe.EditRowSelector)?ue.Edit:e.querySelector(fe.EditFormRowCellSelector)?ue.EditForm:e.matches(fe.GroupRowSelector)?ue.Group:e.matches(fe.EditNewItemRowSelector)?ue.EditNewItemRow:e.matches(fe.DataRowSelector)?ue.Data:e.querySelector(fe.EmptyDataRowCellSelector)?ue.EmptyData:e.querySelector(fe.DetailRowCellSelector)?ue.Detail:e.matches(fe.FooterRowSelector)?ue.Footer:e.matches(fe.GroupFooterRowSelector)?ue.GroupFooter:e.matches(`.${q.EmptyRowClassName}`)?ue.EmptyRow:ue.None}static getCellType(e){return void 0!==e.dataset.editCell?ce.Edit:be.isSelectionCell(e)?ce.Selection:be.isCommandCell(e)?ce.Command:be.isExpandButtonCell(e)?ce.ExpandButton:be.isIndentCell(e)?ce.Indent:be.isEditNewItemRowCell(e)?ce.EditNewItemRowCell:ce.Data}static isIndentCell(e){return null==e?void 0:e.matches(`.${q.IndentCellClassName}, .${q.GroupFooterIndentCellClassName}`)}static isCommandCell(e){var t;return null!==(t=null==e?void 0:e.matches(`.${q.CommandCellClassName}`))&&void 0!==t&&t}static isExpandButtonCell(e){var t;return null!==(t=null==e?void 0:e.matches(`.${q.ExpandButtonCellClassName}`))&&void 0!==t&&t}static isSelectionCell(e){var t;return null!==(t=null==e?void 0:e.matches(`.${q.SelectionCellClassName}`))&&void 0!==t&&t}static isFixedCell(e){var t;return null!==(t=null==e?void 0:e.matches(`.${q.FixedCellClassName}`))&&void 0!==t&&t}static isEditNewItemRowCell(e){var t,n;return null!==(n=null===(t=null==e?void 0:e.parentElement)||void 0===t?void 0:t.matches(`.${q.EditNewItemRowClassName}`))&&void 0!==n&&n}static isSearchBoxContainer(e){return null==e?void 0:e.matches(`.${q.SearchBoxContainerClassName}`)}}class ye{static isEditRowCell(e){return null!==e&&e.rowType===ue.Edit}static areIdentical(e,t){return null!==e&&null!==t&&(e.rowIndex===t.rowIndex&&e.columnInfo.uID===t.columnInfo.uID)}static fromElement(e,t){const n=ye.getCellElement(e,t);if(null!==n){const i=ye.getHitInnerElement(t),s=ye.getRowType(n),o=be.getCellType(n),r=ye.getCellRowIndex(n),l=e.getColumnInfoByCell(n);if(null!==s&&null!==o&&null!==r&&null!==l)return new ye(o,s,r,l,i,n)}return null}static findCellInnerElement(e,t){return null!==e&&t===me.CheckBox?e.querySelector(fe.CellCheckBoxEditViewInputSelector):null}static getCellElement(e,t){if(null===e.uId||null==t)return null;const n=t.closest(fe.getBodyCellsSelector(e.uId,e.getTagName()));if(null!==n)return n;const i=W.getPortalFrom(t);return null!==i?i.closest(fe.getBodyCellsSelector(e.uId,e.getTagName())):null}static getRowType(e){if(null===e)return null;const t=e.parentElement;return null===t?null:be.getRowType(t)}static getCellRowIndex(e){const t=null==e?void 0:e.parentElement;if(null!=t){const e=t.dataset.visibleIndex;if(void 0!==e)return parseInt(e)}return null}static getHitInnerElement(e){return null!==e&&e.closest(fe.CellCheckBoxDisplayViewSelector)?me.CheckBox:null}constructor(e,t,n,i,s,o){this._type=e,this._rowType=t,this._rowIndex=n,this._columnInfo=i,this._hitInnerElement=s,this._element=o}get type(){return this._type}get rowIndex(){return this._rowIndex}get rowType(){return this._rowType}get columnInfo(){return this._columnInfo}get hitInnerElement(){return this._hitInnerElement}get element(){return this._element}}!function(e){e[e.CheckBox=0]="CheckBox"}(me||(me={}));class ve{constructor(e){this.activePopup=null,this.pendingCommandId=null,this.closingTimerId=null,this.pressedCellInfo=null,this.boundOnDocumentPointerDownHandler=this.onDocumentPointerDown.bind(this),this.boundOnDocumentPointerUpHandler=this.onDocumentPointerUp.bind(this),this.boundOnDocumentFocusOutHandler=this.onDocumentFocusOut.bind(this),this.boundOnPopupShownHandler=this.onPopupShown.bind(this),this.boundOnPopupClosingHandler=this.onPopupClosing.bind(this),this.boundOnPopupClosedHandler=this.onPopupClosed.bind(this),this.grid=e}get isPendingCommandResult(){return null!==this.pendingCommandId}get isEditorPopupShown(){return null!==this.activePopup}initialize(){document.addEventListener("pointerdown",this.boundOnDocumentPointerDownHandler),document.addEventListener("pointerup",this.boundOnDocumentPointerUpHandler),document.addEventListener("focusout",this.boundOnDocumentFocusOutHandler),document.addEventListener(B.eventName,this.boundOnPopupShownHandler)}dispose(){document.removeEventListener("pointerdown",this.boundOnDocumentPointerDownHandler),document.removeEventListener("pointerup",this.boundOnDocumentPointerUpHandler),document.removeEventListener("focusout",this.boundOnDocumentFocusOutHandler),document.removeEventListener(B.eventName,this.boundOnPopupShownHandler)}showNewItemRowEditor(e){var t;let n=e;return(n&&n.isEditable||(n=null!==(t=this.grid.columnsInfo.find((e=>e.isEditable)))&&void 0!==t?t:null,n))&&this.sendCommand("showNewItemRowEditor",{columnUid:n.uID})?n.leafIndex:-1}showEditor(e,t,n=null){if(!this.allowInteraction)return!1;const i=this.grid.getCellInfo(e);return null!==i&&(i.type===ce.Data&&(!!i.columnInfo.isEditable&&this.sendCommand("showEditor",{visibleIndex:i.rowIndex,columnUid:i.columnInfo.uID},n,(e=>{e.result&&null!==t&&this.clickToEditCellInnerElement(t,i)}))))}closeEditor(e=ge.Save,t=null){return!!this.allowInteraction&&this.sendCommand("closeEditor",{action:e},t)}onEndCommand(e,t=null){if(null===t)return;let n=Ce.Aborted;e.processedBy(this.pendingCommandId)&&(n=e.result?Ce.Completed:Ce.Failed),t(n)}notifyPendingEditorShow(e){this.allowInteraction&&(e?this.pendingCommandId=ve.ServerSideShowCommandId:this.pendingCommandId===ve.ServerSideShowCommandId&&(this.pendingCommandId=null))}sendCommand(e,t,n=null,i=null){return!!this.uiHandlersBridge&&(this.cancelClosingTimer(),this.pendingCommandId=ve.InitializationCommandId,document.activeElement instanceof HTMLElement&&document.activeElement.blur(),this.pendingCommandId=this.uiHandlersBridge.send(e,t,(e=>{this.onEndCommand(e,n),e.processedBy(this.pendingCommandId)&&(i&&i(e),this.pendingCommandId=null)})),!0)}get allowInteraction(){return this.grid.enableInplaceEditing}get uiHandlersBridge(){return this.grid.uiHandlersBridge}onDocumentPointerDown(e){this.allowInteraction&&(this.pressedCellInfo=this.grid.getCellInfo(e.target))}onDocumentPointerUp(){this.allowInteraction&&(this.pressedCellInfo=null)}onDocumentFocusOut(e){var t;if(!this.allowInteraction)return;if(!this.grid.isEditing)return;if(this.isPendingCommandResult)return;const n=this.grid.getCellInfo(e.target);if(!ye.isEditRowCell(n))return;const i=this.grid.getCellInfo(null!==(t=e.relatedTarget)&&void 0!==t?t:this.activePopup),s=ye.areIdentical(n,i);ye.isEditRowCell(i)?!s&&this.isPressedCell(i)&&this.startClosingTimer(ge.Hide):this.isPressedDataCell(i)&&!this.isDataCellInteractiveElement(e.relatedTarget)?this.startClosingTimer():this.closeEditor()}isDataCellInteractiveElement(e){return!1}onPopupShown(e){if(!this.allowInteraction)return;if(!this.grid.isEditing)return;if(!(e.target instanceof V))return;const t=this.grid.getCellInfo(e.target);ye.isEditRowCell(t)&&(this.activePopup=e.target,this.activePopup.addEventListener(K.eventName,this.boundOnPopupClosingHandler),this.activePopup.addEventListener(L.eventName,this.boundOnPopupClosingHandler),this.activePopup.addEventListener(A.eventName,this.boundOnPopupClosedHandler))}onPopupClosing(e){if(null===this.activePopup)return;if(this.activePopup.removeEventListener(K.eventName,this.boundOnPopupClosingHandler),this.activePopup.removeEventListener(L.eventName,this.boundOnPopupClosingHandler),e.detail.closeReason!==O.OutsideClick)return;const t=W.getPortalFrom(document.activeElement);t&&t.branchId!==e.detail.branchId||(this.activePopup.removeEventListener(A.eventName,this.boundOnPopupClosedHandler),this.activePopup=null,this.closeEditor())}onPopupClosed(e){null!==this.activePopup&&(this.activePopup.removeEventListener(K.eventName,this.boundOnPopupClosingHandler),this.activePopup.removeEventListener(L.eventName,this.boundOnPopupClosingHandler),this.activePopup.removeEventListener(A.eventName,this.boundOnPopupClosedHandler),setTimeout((()=>this.activePopup=null),0))}clickToEditCellInnerElement(e,t){var n;const i=this.getEditCellElement(),s=this.grid.getCellInfo(i);ye.areIdentical(t,s)&&(null===(n=ye.findCellInnerElement(i,e))||void 0===n||n.click())}getEditCellElement(){return null===this.grid.uId?null:this.grid.querySelector(fe.getBodyEditCellSelector(this.grid.uId,this.grid.getTagName()))}isPressedDataCell(e){return(null==e?void 0:e.type)===ce.Data&&this.isPressedCell(e)}isPressedCell(e){return ye.areIdentical(e,this.pressedCellInfo)}startClosingTimer(e=ge.Save){this.cancelClosingTimer(),this.closingTimerId=setTimeout((()=>this.closeEditor(e)),ve.ClosingDelay)}cancelClosingTimer(){null!==this.closingTimerId&&(clearTimeout(this.closingTimerId),this.closingTimerId=null)}}ve.InitializationCommandId=-1,ve.ServerSideShowCommandId=-2,ve.ClosingDelay=150,function(e){e[e.Save=0]="Save",e[e.Cancel=1]="Cancel",e[e.Hide=2]="Hide",e[e.SaveAndStartNewRow=3]="SaveAndStartNewRow",e[e.SaveAndStartNewRowLastEditor=4]="SaveAndStartNewRowLastEditor"}(ge||(ge={})),function(e){e[e.Completed=0]="Completed",e[e.Failed=1]="Failed",e[e.Aborted=2]="Aborted"}(Ce||(Ce={}));class Se{static selectInputValue(e){null!==e&&e instanceof HTMLInputElement&&F.isTextInput(e)&&e.select()}}const Ee=`:scope > *:not(.${[q.IndentCellClassName,q.HeaderIndentCellClassName,q.GroupFooterIndentCellClassName,q.EmptyCellClassName,q.HiddenEmptyCellClassName,q.RowDragAnchorClassName].join(",.")})`;class Re extends we{constructor(e,t,n){super(t,n),this.obsolete=!1,this.tableStrategy=e}get isObsolete(){return this.obsolete}makeObsolete(){this.obsolete=!0}getRowVisibleIndex(e=!1){const t=e?null:this.tableStrategy.hiddenRowVisibleIndex;return null!=t?t:F.getAttributeIntValue(this.targetElement,Ie.VisibleIndexName,-1)}selectFirstCell(){this.selectItem(0)}selectLastCell(){this.selectItem(this.itemCount-1)}getSyncSelectedColumnInfo(){return this.getSelectedColumnInfo()}syncSelectedColumn(e){const t=null!==e?this.findItemElementIndexByColumnUid(e.uID):-1;this.selectedItemIndex=Math.max(t,0)}leaveRow(){y.removeTabIndex(this.selectedItemElement)}getSelectedColumnInfo(){var e;return null!==(e=this.getColumnInfo(this.selectedItemElement))&&void 0!==e?e:null}getColumnInfo(e){return this.getColumnInfoByCell(e)}findItemElementIndexByColumnUid(e){return this.items.findIndex((t=>this.getColumnUidByCell(t)===e))}get firstItemSelected(){return this.tableStrategy.isFirstCellSelected(this)||super.firstItemSelected}get lastItemSelected(){return this.tableStrategy.isLastCellSelected(this)||super.lastItemSelected}getShortcutContext(){var e,t,n,i;return{RowType:null!==(t=null===(e=this.tableStrategy.selectedHiddenRowStrategy)||void 0===e?void 0:e.getRowType())&&void 0!==t?t:this.getRowType(),ColumnUID:null!==(n=this.getSelectedColumnUid())&&void 0!==n?n:-1,VisibleIndex:null!==(i=this.getRowVisibleIndex())&&void 0!==i?i:-1}}tryRestoreSelection(){return!!super.tryRestoreSelection()||(this.tableStrategy.syncSelectedColumn(this),!1)}queryItems(){return this.queryItemsBySelector(Ee)}handleKeyDown(e){const t=this.handleKeyDownForHiddenItem(e);if(null!==t)return t;if(!this.isObsolete&&super.handleKeyDown(e))return!0;if(this.nestedContentSelected)return!1;switch(u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.Left:return this.handleArrowLeftKeyDown(e);case u.KeyCode.Right:return this.handleArrowRightKeyDown(e);case u.KeyCode.Tab:return this.handleTabKeyDown(e);case u.KeyCode.Home:if(!e.ctrlKey)return this.selectFirstCell(),!0;break;case u.KeyCode.End:if(!e.ctrlKey)return this.selectLastCell(),!0}return!1}selectItem(e){const t=e!==this.selectedItemIndex;t&&this.tableStrategy.trySelectHiddenCell(e)||(super.selectItem(e),t&&this.saveSelectedColumn(),!this.navigator.isActive||!t&&this.preventScrollOnFocus||this.alignRequiredVisibleElement(this.selectedItemElement))}get selectedColumnUidSearchElement(){var e,t;return null!==(t=null===(e=this.tableStrategy.selectedHiddenRowStrategy)||void 0===e?void 0:e.selectedItemElement)&&void 0!==t?t:this.selectedItemElement}get preventScrollOnFocus(){return this.tableStrategy.preventScrollOnFocus}isImmediatelyClickEnabled(){return this.isActiveCommandCell()||this.isActiveExpandButtonCell()||this.isSelectionCell()}handleKeyDownForHiddenItem(e){return this.tableStrategy.handleKeyDownForHiddenRow(this,e)}handleArrowLeftKeyDown(e){return this.moveToPrevItem(),!0}handleArrowRightKeyDown(e){return this.moveToNextItem(),!0}getColumnUidByCell(e){var t;return null===(t=this.getColumnInfo(e))||void 0===t?void 0:t.uID}syncFocusedRowVisibleIndex(){if(!this.isFocusedRowEnabled)return;const e=this.getRowVisibleIndex();this.grid.notifyFocusedRowChanged(e)}isActiveCommandCell(){return be.isCommandCell(this.selectedItemElement)}isActiveExpandButtonCell(){return be.isExpandButtonCell(this.selectedItemElement)}isSelectionCell(){return be.isSelectionCell(this.selectedItemElement)}saveSelectedColumn(){const e=this.getRowType();$e.isColumnSyncRequired(e)&&this.tableStrategy.saveSelectedColumn(this)}getSelectedColumnUid(){const e=this.selectedColumnUidSearchElement;let t;return e&&(t=this.getColumnUidByCell(e)),null!=t?t:null}isFixedCell(e){return be.isFixedCell(e)}alignRequiredVisibleElement(e){const t=this.getScrollViewerContent();if(t&&!this.isFixedCell(e)){e.scrollIntoView({block:"nearest"});const n=Array.from(Array(this.itemCount-this.selectedItemIndex-1),((e,t)=>++t)).map((e=>this.getItem(e+this.selectedItemIndex))).filter((e=>e&&this.isFixedCell(e)));if(n.length>0){const i=M.calculateHorizontalScrollCorrection(e,n.reverse(),!1);t.scrollBy(i,0)}const i=Array.from(Array(this.selectedItemIndex),((e,t)=>t++)).map((e=>this.getItem(e))).filter((e=>e&&this.isFixedCell(e)));if(i.length>0){const n=M.calculateHorizontalScrollCorrection(e,i,!0);t.scrollBy(n,0)}}}}class xe extends Re{constructor(e,t,n){super(e,t,n)}getSelectedItemRowSpan(){return this.selectedItemElement.rowSpan}getSelectedItemLeafIndex(e){return this.getLeafIndex(this.selectedItemElement,e)}getRowType(){return ue.Header}handleKeyDown(e){if(super.handleKeyDown(e))return!0;if(this.nestedContentSelected)return!1;switch(u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.Space:return this.performShortcutEvent(e),!0;case u.KeyCode.Down:return e.altKey&&this.performShortcutEvent(e),e.altKey}return!1}isImmediatelyClickEnabled(){return!0}getColumnInfo(e){return this.getHeaderColumnInfo(e)}findItemElementIndexByColumnUid(e){let t=super.findItemElementIndexByColumnUid(e);return t<0&&(t=this.items.findIndex((t=>{var n;return null===(n=this.getColumnInfo(t))||void 0===n?void 0:n.containsLeaf(e)}))),t}getSyncSelectedColumnInfo(){const e=super.getSyncSelectedColumnInfo();if(e&&e.hasLeafs){const t=this.tableStrategy.getSavedColumnInfo();return t&&e.containsLeaf(t.uID)?t:e.getLeftLeaf()}return e}handleArrowLeftKeyDown(e){return!!(this.selectedItemIndex-1>=0&&this.isAdjacentColumn(-1))&&super.handleArrowLeftKeyDown(e)}handleArrowRightKeyDown(e){return!!(this.selectedItemIndex+1<this.itemCount&&this.isAdjacentColumn(1))&&super.handleArrowRightKeyDown(e)}isAdjacentColumn(e){const t=e<0,n=this.getSelectedItemLeafIndex(t);return this.getLeafIndex(this.getItem(this.selectedItemIndex+e),!t)===n+e}getLeafIndex(e,t){var n,i,s;const o=this.getColumnInfo(e);if(!o||!o.hasLeafs)return null!==(i=null===(n=this.getColumnInfo(e))||void 0===n?void 0:n.leafIndex)&&void 0!==i?i:-1;const r=t?o.getLeftLeaf():o.getRightLeaf();return null!==(s=null==r?void 0:r.leafIndex)&&void 0!==s?s:-1}}class De extends Re{constructor(e,t,n){super(e,t,n)}focusNestedEditor(){return!!this.switchToNestedContent()&&(this.selectFocusedInputValue(),!0)}updateSelectedItemByChildElement(e,t=null){t instanceof FocusEvent&&this.syncFocusedRowVisibleIndex(),super.updateSelectedItemByChildElement(e,t)}selectFocusedInputValue(){Se.selectInputValue(document.activeElement)}}class Ne extends De{constructor(e,t,n){super(e,t,n)}getNestedContentContainer(){return this.nestedContentSelected?this.targetElement:super.getNestedContentContainer()}activate(){this.nestedContentSelected&&!this.isNestedElementFocused()&&this.leaveFromNestedContent(),super.activate()}switchToNestedContent(){return this.getNestedContentElement()&&y.scheduleResetTabIndex(this.selectedItemElement),super.switchToNestedContent()}leaveFromNestedContent(e=v.None){y.makeElementFocusable(this.selectedItemElement),super.leaveFromNestedContent(e)}handleKeyDown(e){const t=this.handleKeyDownForHiddenItem(e);if(null!==t)return t;this.nestedContentSelected&&y.removeTabIndex(this.selectedItemElement);const n=this.nestedContentSelected,i=super.handleKeyDown(e);return!(u.KeyUtils.getEventKeyCode(e)===u.KeyCode.Tab&&i&&n&&!this.nestedContentSelected)&&i}}class Te extends Ne{constructor(e,t,n){super(e,t,n)}getRowType(){return ue.Filter}}class He extends Ne{constructor(e,t,n){super(e,t,n)}getRowType(){return ue.Edit}selectItem(e){e!==this.selectedItemIndex&&this.nestedContentSelected&&this.leaveFromNestedContent(),super.selectItem(e),this.switchToNestedContent()}handleKeyDown(e){const t=this.handleKeyDownForHiddenItem(e);if(null!==t)return t;if(this.nestedContentSelected){const t=u.KeyUtils.getEventKeyCode(e);if(e.ctrlKey&&[u.KeyCode.Up,u.KeyCode.Down,u.KeyCode.Home,u.KeyCode.End].includes(t)||e.altKey&&t===u.KeyCode.Tab)return this.leaveFromNestedContent(),!1;if(t===u.KeyCode.Enter&&this.canSaveOnEnter()||t===u.KeyCode.Esc)return this.leaveFromNestedContent(),this.saveRowSelectedColumn(),this.performShortcutEvent(e),!0;t!==u.KeyCode.Space&&t!==u.KeyCode.Enter||this.saveRowSelectedColumn()}return super.handleKeyDown(e)}saveRowSelectedColumn(){this.tableStrategy.saveRowSelectedColumn(this)}canSaveOnEnter(){if(!document.activeElement)return!1;const e=document.activeElement;return F.isTextInput(e)&&!F.isMultipleRowInput(e)}}class Pe extends De{constructor(e,t,n){super(e,t,n)}get tableBodyStrategy(){return this.tableStrategy}getRowType(){return ue.Edit}handleKeyDown(e){const t=this.handleKeyDownForHiddenItem(e);if(null!==t)return t;if(this.editorManager.isPendingCommandResult)return!0;if(this.isEditorCell()){if(this.isActiveEditorCell()){if(u.KeyUtils.getEventKeyCode(e)===u.KeyCode.Tab){const t=this.isNewItemRow;if(this.tableBodyStrategy.startEditNearestRowItem(!e.shiftKey,t))return!0;const n=t?this.getNewItemRowClosingAction(!e.shiftKey):ge.Hide;if(this.tableBodyStrategy.endEditRowItem(n))return!0}if(u.KeyUtils.getEventKeyCode(e)===u.KeyCode.Enter&&!e.shiftKey&&!this.editorManager.isEditorPopupShown&&this.tableBodyStrategy.endEditRowItem(ge.Hide))return!0}else if(u.KeyUtils.getEventKeyCode(e)===u.KeyCode.Enter&&this.isEditorCell()&&this.tableBodyStrategy.startEditRowItem(this,this.selectedItemIndex))return!0;if(u.KeyUtils.getEventKeyCode(e)===u.KeyCode.Esc&&this.tableBodyStrategy.endEditRowItem(ge.Cancel))return!0}return super.handleKeyDown(e)}handleTabKeyDown(e,t=!1,n=!1){return this.isNewItemRow?!(!(!e.shiftKey&&this.lastItemSelected||e.shiftKey&&this.firstItemSelected)||!this.tableBodyStrategy.endEditRowItem(this.getNewItemRowClosingAction(!e.shiftKey)))||super.handleTabKeyDown(e,t,!0):super.handleTabKeyDown(e,t)}getNewItemRowClosingAction(e){return e?ge.SaveAndStartNewRow:ge.SaveAndStartNewRowLastEditor}get isNewItemRow(){return this.targetElement.classList.contains(q.EditNewItemRowInplaceClassName)}isEditorCell(){return!be.isCommandCell(this.selectedItemElement)&&!be.isExpandButtonCell(this.selectedItemElement)&&!be.isSelectionCell(this.selectedItemElement)}isActiveEditorCell(){return be.getCellType(this.selectedItemElement)===ce.Edit}}class Fe extends De{constructor(e,t,n){super(e,t,n)}getRowType(){return ue.EditForm}}class We extends Re{constructor(e,t,n){super(e,t,n)}getRowType(){return ue.EditNewItemRow}handleKeyDown(e){if(this.editorManager.isPendingCommandResult)return!0;const t=u.KeyUtils.getEventKeyCode(e);if(!this.isObsolete&&t===u.KeyCode.Enter){const e=this.tableStrategy.getSavedColumnInfo();if(this.tableStrategy.startEditNewItemRow(this,e))return!0}return super.handleKeyDown(e)}}class Be extends Re{constructor(e,t,n){super(e,t,n)}get selectedColumnUidSearchElement(){var e;const t=super.selectedColumnUidSearchElement;return null!==(e=null==t?void 0:t.querySelector(`.${q.ExpandButtonCellClassName}`))&&void 0!==e?e:t}getRowType(){return ue.Group}queryItems(){return new Array}handleKeyDown(e){const t=this.handleKeyDownForHiddenItem(e);return null!==t?t:this.nestedContentSelected||u.KeyUtils.getEventKeyCode(e)!==u.KeyCode.Left&&u.KeyUtils.getEventKeyCode(e)!==u.KeyCode.Right?super.handleKeyDown(e):(this.tableStrategy.trySelectHiddenCell(this.selectedItemIndex,(()=>this.dispatchKeyboardEventToSelectedItemStrategy(e)))||this.performShortcutEvent(e),!0)}isImmediatelyClickEnabled(){return!0}dispatchKeyboardEventToSelectedItemStrategy(e){const t=this.tableStrategy.getSelectedItemStrategy();null==t||t.targetElement.dispatchEvent(e)}}class Ve extends Re{constructor(e,t,n){super(e,t,n)}get tableBodyStrategy(){return this.tableStrategy}getRowType(){return ue.Data}handleKeyDown(e){const t=this.handleKeyDownForHiddenItem(e);if(null!==t)return t;if(this.editorManager.isPendingCommandResult)return!0;const n=u.KeyUtils.getEventKeyCode(e);if(!this.nestedContentSelected){if(n===u.KeyCode.Space&&this.isSelectionRowEnabled)return this.performShortcutEvent(e),!0;if(!this.isObsolete&&n===u.KeyCode.Enter&&this.tableBodyStrategy.startEditRowItem(this,this.selectedItemIndex))return!0}return!!super.handleKeyDown(e)||!(this.nestedContentSelected||n!==u.KeyCode.Enter||!this.hasRowClickEvent)&&(this.performShortcutEvent(e),!0)}}class Ke extends Re{constructor(e,t,n){super(e,t,n)}getRowType(){return ue.EmptyData}}class Le extends Re{constructor(e,t,n){super(e,t,n)}getRowType(){return ue.Detail}}class Ae extends Re{constructor(e,t,n){super(e,t,n)}getRowType(){return ue.Footer}}class Oe extends Re{constructor(e,t,n){super(e,t,n)}getRowType(){return ue.GroupFooter}}class Me{constructor(e){this.cellInfo=e}}class _e{constructor(e){this.result=e}}class ke extends CustomEvent{constructor(e){super(ke.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!1})}}ke.eventName="dxbl-grid-request-start-editing";class Ue extends CustomEvent{constructor(e){const t=new _e(e);super(Ue.eventName,{detail:t,bubbles:!0,composed:!1,cancelable:!1})}}Ue.eventName="dxbl-grid-cell-editor-shown";class ze extends we{constructor(e,t){super(e,t),this.savedColumnInfo=null}getSavedColumnInfo(){return this.savedColumnInfo}saveSelectedColumn(e){this.savedColumnInfo=e.getSyncSelectedColumnInfo()}syncSelectedColumn(e){const t=this.getSavedColumnInfo();e.syncSelectedColumn(t)}queryItems(){return this.queryItemsBySelector(fe.DataTablePartsSelector)}createTableBodyItemStrategy(e){return this.isVirtualScrollingEnabled(e)?new je(this,this.grid,e):new qe(this,this.grid,e)}createItemStrategy(e){return e.matches("thead")?new Ge(this,this.grid,e):e.matches("tbody")?this.createTableBodyItemStrategy(e):e.matches("tfoot")?new Ye(this,this.grid,e):null}selectItem(e,t=-1){var n;const i=this.getStrategy(e),s=this.syncSelectedColumns(i);t>=0&&i.enterTablePartWithNewIndex(t),(t<0||s&&(null===(n=i.getStrategy(t))||void 0===n?void 0:n.getRowType())===ue.Header)&&(t=i.selectedItemIndex);const o=this.getSelectedItemStrategy();o&&o!==i&&o.leaveTablePart(),i.selectItem(t)}handleKeyDown(e){switch(u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.Up:return this.handleArrowUpKeyDown(e.ctrlKey);case u.KeyCode.Down:return this.handleArrowDownKeyDown(e.ctrlKey);case u.KeyCode.Tab:return this.handleTabKeyDown(e);case u.KeyCode.PageUp:case u.KeyCode.PageDown:return this.handleBodyShortcut(e,!0);case u.KeyCode.Home:case u.KeyCode.End:return this.handleBodyShortcut(e,!1)}return!1}handleTabKeyDown(e){var t,n;if(e.altKey)return super.handleTabKeyDown(e,!0);if(e.shiftKey){if(this.firstItemSelected)return!1;null===(t=this.getStrategy(this.selectedItemIndex-1))||void 0===t||t.selectLastCell()}else{if(this.lastItemSelected)return!1;null===(n=this.getStrategy(this.selectedItemIndex+1))||void 0===n||n.selectFirstCell()}return!0}isVirtualScrollingEnabled(e){if(e.classList.contains(q.TopFixedBodyClassName))return!1;const t=this.getScrollViewer();return!!t&&M.isVirtualScrollingEnabled(t)}handleArrowUpKeyDown(e){if(0===this.selectedItemIndex)return!1;if(e)this.moveToPrevItem();else{const e=this.getStrategy(this.selectedItemIndex-1);this.selectItem(this.selectedItemIndex-1,e.itemCount-1)}return!0}handleArrowDownKeyDown(e){return!(this.selectedItemIndex>=this.itemCount-1)&&(e?this.moveToNextItem():this.selectItem(this.selectedItemIndex+1,0),!0)}handleBodyShortcut(e,t){const n=this.items.map(((e,t)=>e.matches("tbody")?t:-1)).filter((e=>e>=0));if(!n.length)return!1;const i=this.getStrategy(n[n.length-1]);return!(!i||i===this.getSelectedItemStrategy())&&(t&&this.syncSelectedColumns(i),i.handleKeyDown(e))}getStrategy(e){return this.getItemStrategy(e)}syncSelectedColumns(e){const t=this.getSelectedItemStrategy();if(t){const n=t.getSelectedItemStrategy(),i=e.getSelectedItemStrategy();if(n&&i)return e.syncSelectedColumns(n,i)}return!1}}class $e extends we{constructor(e,t,n){super(t,n),this.parentTableStrategy=e}get selectedHiddenRowStrategy(){return null}get hiddenRowVisibleIndex(){return null}get preventScrollOnFocus(){return!this.navigator.initialized}enterTablePartWithNewIndex(e){}isFirstCellSelected(e){return!1}isLastCellSelected(e){return!1}trySelectHiddenCell(e,t=null){return!1}handleKeyDownForHiddenRow(e,t){return null}leaveTablePart(){}getStrategy(e){const t=this.getItemStrategy(e);return t instanceof Re?t:null}selectFirstCell(){var e;this.selectItem(0),null===(e=this.getSelectedItemStrategy())||void 0===e||e.selectFirstCell()}selectLastCell(){var e;this.selectItem(this.itemCount-1),null===(e=this.getSelectedItemStrategy())||void 0===e||e.selectLastCell()}getSavedColumnInfo(){return this.parentTableStrategy.getSavedColumnInfo()}saveRowSelectedColumn(e){this.saveSelectedColumn(e)}saveSelectedColumn(e){this.parentTableStrategy.saveSelectedColumn(e)}syncSelectedColumn(e){this.isColumnSyncRequired(e)&&this.parentTableStrategy.syncSelectedColumn(e)}syncSelectedColumns(e,t){return t&&e!==t&&(e&&this.isColumnSyncRequired(e)&&this.saveSelectedColumn(e),this.syncSelectedColumn(t)),!1}getShortcutContext(){return{AreaType:he.DataTable}}queryItems(){return this.queryItemsBySelector(fe.RowSelector)}getSelectedItemStrategy(){const e=super.getSelectedItemStrategy();return e instanceof Re?e:null}selectItem(e){const t=this.getSelectedItemStrategy(),n=this.getStrategy(e);this.syncSelectedColumns(t,n),super.selectItem(e),t&&t!==n&&t.leaveRow()}updateSelectedItemByIndex(e){const t=this.getSelectedItemStrategy();t&&e!==this.selectedItemIndex&&t.leaveRow(),super.updateSelectedItemByIndex(e)}handleKeyDown(e){switch(u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.Up:return this.handleArrowUpKeyDown(e.ctrlKey);case u.KeyCode.Down:return this.handleArrowDownKeyDown(e.ctrlKey);case u.KeyCode.Tab:return this.handleTabKeyDown(e)}return!1}handleTabKeyDown(e){const t=super.handleTabKeyDown(e);return t&&this.onAfterTabKeyDownHandled(e),t}onAfterTabKeyDownHandled(e){var t,n;e.shiftKey?null===(t=this.getSelectedItemStrategy())||void 0===t||t.selectLastCell():null===(n=this.getSelectedItemStrategy())||void 0===n||n.selectFirstCell()}getStrategyByRowVisibleIndex(e){const t=this.items.findIndex(((t,n)=>{var i;return(null===(i=this.getStrategy(n))||void 0===i?void 0:i.getRowVisibleIndex())===e}));return this.getItemStrategy(t)}handleArrowUpKeyDown(e){if(!e){if(this.firstItemSelected)return!1;this.moveToPrevItem()}return!e}handleArrowDownKeyDown(e){if(!e){if(this.lastItemSelected)return!1;this.moveToNextItem()}return!e}isColumnSyncRequired(e){const t=e.getRowType();return $e.isColumnSyncRequired(t)}static isColumnSyncRequired(e){switch(e){case ue.Header:case ue.Filter:case ue.Edit:case ue.Data:case ue.Footer:case ue.GroupFooter:return!0}return!1}}class Ge extends $e{constructor(e,t,n){super(e,t,n),this.isTabKeyDownHandling=!1}selectItem(e){const t=this.getSelectedItemStrategy(),n=this.getStrategy(e),i=this.syncSelectedRows(t,n);super.selectItem(i>=0?i:e)}handleKeyDown(e){switch(u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.Left:return this.moveHorizontally(!0);case u.KeyCode.Right:return this.moveHorizontally(!1)}return super.handleKeyDown(e)}createItemStrategy(e){switch(be.getRowType(e)){case ue.Header:return new xe(this,this.grid,e);case ue.Filter:return new Te(this,this.grid,e)}throw new Error("Not implemented")}syncSelectedColumns(e,t){if(e&&e!==t&&this.isColumnSyncRequired(t)){const n=this.syncSelectedRows(e,t);if(n>=0)return this.selectedItemIndex=n,t=this.getSelectedItemStrategy(),super.syncSelectedColumns(e,t),!0}return super.syncSelectedColumns(e,t)}syncSelectedRows(e,t){if(!this.isTabKeyDownHandling&&e&&!this.isHeaderRowStrategy(e)&&t&&this.isHeaderRowStrategy(t)){const t=this.isColumnSyncRequired(e)?e.getSyncSelectedColumnInfo():this.getSavedColumnInfo();if(t)return t.headerRowIndex}return-1}handleArrowDownKeyDown(e){const t=this.getSelectedItemStrategy();if(!e&&t&&this.isHeaderRowStrategy(t)){const e=t,n=e.getSelectedColumnInfo();if(n&&!n.hasLeafs){const t=n.headerRowIndex+e.getSelectedItemRowSpan();return!(t>=this.itemCount)&&(this.selectItem(t),!0)}}return super.handleArrowDownKeyDown(e)}handleTabKeyDown(e){this.isTabKeyDownHandling=!0;const t=super.handleTabKeyDown(e);return this.isTabKeyDownHandling=!1,t}isHeaderRowStrategy(e){return e.getRowType()===ue.Header}moveHorizontally(e){const t=this.getSelectedItemStrategy();if(t&&this.isHeaderRowStrategy(t)){const n=t.getSelectedItemLeafIndex(e);return this.selectColumn(n+(e?-1:1))}return!1}selectColumn(e){if(e>=0){const t=this.findHeaderCellByColumnIndex(e);if(t){const e=this.findItemElementIndexByChild(t),n=this.getStrategy(e);if(n){const i=n.getColumnInfo(t);if(i)return n.syncSelectedColumn(i),this.selectedItemIndex=e,this.selectItem(e),!0}}}return!1}}class qe extends $e{constructor(e,t,n){super(e,t,n),this.savedVisibleIndex=-1,this.restoreSelectedColumn=!1,this.expectedFocusedRowFromServer=!1,this.boundOnVisibleElementChangedHandler=this.onVisibleElementChanged.bind(this),this.boundOnGridRequestStartCellEditingHandle=this.onRequestStartCellEditing.bind(this),this.addEventSubscriptions()}onDispose(){super.onDispose(),this.removeEventSubscriptions()}createDataRowStrategy(e){return new Ve(this,this.grid,e)}createEditRowStrategy(e){return new He(this,this.grid,e)}createInplaceEditRowStrategy(e){return new Pe(this,this.grid,e)}createItemStrategy(e){switch(be.getRowType(e)){case ue.Edit:return this.isInplaceEditingEnabled?this.createInplaceEditRowStrategy(e):this.createEditRowStrategy(e);case ue.EditForm:return new Fe(this,this.grid,e);case ue.EditNewItemRow:return new We(this,this.grid,e);case ue.Group:return new Be(this,this.grid,e);case ue.Data:return this.createDataRowStrategy(e);case ue.EmptyData:return new Ke(this,this.grid,e);case ue.Detail:return new Le(this,this.grid,e);case ue.GroupFooter:return new Oe(this,this.grid,e)}throw new Error("Not implemented")}selectItem(e){const t=this.getStrategy(e);this.saveFocusedRowVisibleIndex(t),super.selectItem(e),this.restoreSelectedColumn=!1}saveRowSelectedColumn(e){super.saveRowSelectedColumn(e),this.restoreSelectedColumn=!0}syncSelectedColumns(e,t){const n=super.syncSelectedColumns(e,t);return!n&&this.restoreSelectedColumn&&t&&this.parentTableStrategy.syncSelectedColumn(t),n||this.restoreSelectedColumn}handleKeyDown(e){switch(this.expectedFocusedRowFromServer=!1,u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.PageDown:return this.handlePageUpDown(e,!1)||this.performShortcutEvent(e),!0;case u.KeyCode.PageUp:return this.handlePageUpDown(e,!0)||this.performShortcutEvent(e),!0;case u.KeyCode.Home:return this.handleHomeKeyDown(e.ctrlKey);case u.KeyCode.End:return this.handleEndKeyDown(e.ctrlKey)}return super.handleKeyDown(e)}handleKeyUp(e){switch(u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.Down:case u.KeyCode.Up:case u.KeyCode.PageUp:case u.KeyCode.PageDown:case u.KeyCode.Home:case u.KeyCode.End:case u.KeyCode.Tab:this.notifyFocusedRowChanged()}return!1}moveToPrevItem(){super.moveToPrevItem(),this.scrollToElement(null,m.None)}moveToNextItem(){const e=this.getScrollViewer(),t=e&&M.bottomAlignmentRequired(this.selectedItemElement,e)?g.Bottom:null;super.moveToNextItem(),this.scrollToElement(t,m.None)}leaveTablePart(){super.leaveTablePart(),this.notifyFocusedRowChanged()}onHandlePageUpDownKey(e,t,n){const i=this.validateItemIndex(e);this.selectItem(i),this.scrollToElement(t,n)}scrollToElement(e,t){const n=this.getScrollViewer();n&&C.scrollToElementRelyOnStickyDescendants(this.selectedItemElement,e,t,n)}onMakeRowVisible(e){this.selectItem(e)}handlePageUpDown(e,t){this.savedVisibleIndex=-1;const n=t?this.firstItemSelected:this.lastItemSelected,i=this.getSelectedItemStrategy(),s=i&&i.getRowType()===ue.EmptyData,o=this.targetElement.contains(e.target);if(n&&(s||o))return this.expectedFocusedRowFromServer=this.isFocusedRowEnabled,!1;const r=this.getScrollViewer();if(r){const e=t?g.Top:g.Bottom,n=M.calculateBoundaryItemIndex(this,r,!t);this.onHandlePageUpDownKey(n,e,m.None)}return!0}handleHomeKeyDown(e){return e&&this.selectFirstCell(),e}handleEndKeyDown(e){return e&&this.selectLastCell(),e}notifyFocusedRowChanged(){this.isFocusedRowEnabled&&this.savedVisibleIndex>-1&&(this.grid.notifyFocusedRowChanged(this.savedVisibleIndex),this.savedVisibleIndex=-1)}shouldSaveFocusedRowVisibleIndex(e){return!this.expectedFocusedRowFromServer&&this.isFocusableRow(e.getRowType())}saveFocusedRowVisibleIndex(e){e&&this.shouldSaveFocusedRowVisibleIndex(e)&&(this.savedVisibleIndex=e.getRowVisibleIndex())}isFocusableRow(e){switch(e){case ue.Group:case ue.Edit:case ue.Data:return!0}return!1}addEventSubscriptions(){const e=this.getScrollViewer();e&&e.addEventListener(p.eventName,this.boundOnVisibleElementChangedHandler),this.grid.addEventListener(ke.eventName,this.boundOnGridRequestStartCellEditingHandle)}removeEventSubscriptions(){const e=this.getScrollViewer();e&&e.removeEventListener(p.eventName,this.boundOnVisibleElementChangedHandler),this.grid.removeEventListener(ke.eventName,this.boundOnGridRequestStartCellEditingHandle)}onVisibleElementChanged(e){if(e.detail.isFocusRequired){const t=this.items.indexOf(e.detail.element);this.onMakeRowVisible(t)}}onRequestStartCellEditing(e){let t=!1;const n=e.detail.cellInfo,i=this.findItemElementIndexByChild(n.element);if(i<0)return;const s=this.getStrategy(i);if(!(s instanceof We)){if(s instanceof Re){this.saveRowSelectedColumn(s);const e=s.findItemElementIndexByColumnUid(n.columnInfo.uID);t=this.startEditRowItem(s,e,n.hitInnerElement,this.raiseStartCellEditingRequestComplete.bind(this))}t||(n.rowType===ue.Edit&&this.endEditRowItem(ge.Hide),this.raiseStartCellEditingRequestComplete(!1))}}raiseStartCellEditingRequestComplete(e){this.grid.dispatchEvent(new Ue(e))}startEditNewItemRow(e,t){const n=this.editorManager.showNewItemRowEditor(t);return n>=0&&(e.selectItem(n),this.saveRowSelectedColumn(e),!0)}startEditRowItem(e,t,n=null,i=null){if(null===e||!this.isInplaceEditableRow(e))return!1;if(t<0||t>=e.itemCount)return!1;return!!this.editorManager.showEditor(e.getItem(t),n,(e=>{const n=e===Ce.Completed;if(n){const e=this.findEditRowStrategy();null!==e&&(e.updateSelectedItemByIndex(t),this.saveRowSelectedColumn(e),e.focusNestedEditor()||e.leaveFromNestedContent())}null!==i&&i(n)}))&&(e.selectItem(t),this.saveRowSelectedColumn(e),!0)}endEditRowItem(e=ge.Save){const t=this.getSelectedItemStrategy();return null!==t&&t instanceof Pe&&(t.leaveFromNestedContent(),e!==ge.Hide&&this.saveRowSelectedColumn(t),this.editorManager.closeEditor(e,(n=>{e===ge.Cancel&&n===Ce.Failed&&(t.focusNestedEditor()||t.leaveFromNestedContent())})))}startEditNearestRowItem(e,t=!1){const n=this.findNearestEditablePosition(e);if(null===n)return!1;const[i,s]=n;if(t&&i!==this.selectedItemIndex)return!1;const o=this.getSelectedItemStrategy(),r=this.getStrategy(i);return o&&o!==r&&o.updateSelectedItemByIndex(s),this.startEditRowItem(r,s,null,this.raiseStartCellEditingRequestComplete.bind(this))}findNearestEditablePosition(e){const t=e?1:-1,n=this.itemCount,i=this.selectedItemIndex;for(let s=i;s>=0&&s<n;s+=t){const n=this.getStrategy(s);if(null===n||!this.isInplaceEditableRow(n))continue;let o;o=s===i?n.selectedItemIndex+t:e?0:n.itemCount-1;for(let e=o;e>=0&&e<n.itemCount;e+=t){const t=n.getItem(e),i=this.getColumnInfoByCell(t);if(null!==i&&i.isEditable)return[s,e]}}return null}isInplaceEditableRow(e){if(!this.isInplaceEditingEnabled)return!1;const t=e.getRowType();return t===ue.Data||t===ue.Edit}findEditRowStrategy(){for(let e=0;e<this.itemCount;e++){const t=this.getStrategy(e),n=qe.castToEditRowStategy(t);if(n)return n}return null}static castToEditRowStategy(e){return e instanceof De&&e.getRowType()===ue.Edit?e:null}}class je extends qe{constructor(e,t,n){super(e,t,n),this.selectedRowVirtualIndex=-1,this.selectedNonVirtualRowIndexOffset=0,this.selectedHiddenStrategy=null,this.isLastRowSelected=!1,this.rowVirtualIndexToShow=null,this.scrollRequired=!1,this.onRowMadeVisibleCallback=null,this.cellEditingStarted=!1,this.skipIndexCorrectionForIncomingEditRow=!1,this.boundOnScrollHandler=this.onScroll.bind(this),this.boundOnStartCellEditingRequestHandler=this.onStartCellEditingRequest.bind(this),this.addEventListeners()}onDispose(){this.removeEventListeners(),super.onDispose()}get preventScrollOnFocus(){return super.preventScrollOnFocus||this.isFocusHidden||!this.scrollRequired&&!this.isRowInView(this.selectedItemElement)}get firstItemSelected(){return super.firstItemSelected&&0===this.selectedRowVirtualIndex}get lastItemSelected(){return this.isLastRowSelected||!this.totalItemCount}get isFocusHidden(){return _(this.targetElement)}get lastVirtualIndexSelected(){return this.selectedRowVirtualIndex>=this.lastVirtualIndex}get lastVirtualIndex(){const e=this.totalItemCount;return e?e-1:0}get totalItemCount(){return this.getScrollViewer().totalItemCount}get makingRowVisibleStarted(){return null!==this.rowVirtualIndexToShow}initialize(){super.initialize(),this.cellEditingStarted=!1}isFirstCellSelected(e){var t,n;return null!==(n=null===(t=this.getSelectedHiddenStrategyNotEqualTo(e))||void 0===t?void 0:t.firstItemSelected)&&void 0!==n?n:super.isFirstCellSelected(e)}isLastCellSelected(e){var t,n;return null!==(n=null===(t=this.getSelectedHiddenStrategyNotEqualTo(e))||void 0===t?void 0:t.lastItemSelected)&&void 0!==n?n:super.isLastCellSelected(e)}isIndexWithinBoundaries(e){return!0}enterTablePartWithNewIndex(e){this.scrollRequired=!0;const t=0===e;this.updateSelectedVirtualIndex(t?0:this.lastVirtualIndex),this.selectedNonVirtualRowIndexOffset=t?-1:je.maxOffset}selectItem(e){const t=this.correctSelectionIndex(e);t<0||(super.selectItem(t),this.scrollRequired=!1,this.isFocusHidden||this.updateSelectedVirtualState())}leaveTablePart(){super.leaveTablePart(),this.scrollRequired=!0}updateSelectedItemByChildElement(e,t=null){const n=this.selectedItemIndex;if(super.updateSelectedItemByChildElement(e,t),this.skipIndexCorrectionForIncomingEditRow=!1,t instanceof MouseEvent)return k(this.targetElement),void this.updateSelectedVirtualState();(n!==this.selectedItemIndex||e.parentElement!==this.selectedItemElement)&&this.resetVirtualStateBySelectedStrategy(e)}moveToPrevItem(){this.isFocusHidden?this.moveToSiblingRowByVirtualIndex(-1):super.moveToPrevItem()}moveToNextItem(){this.isFocusHidden?this.moveToSiblingRowByVirtualIndex(1):super.moveToNextItem()}selectFirstCell(){this.selectedNonVirtualRowIndexOffset=-1,this.setOnRowMadeVisibleCallback((()=>{var e;null===(e=this.getSelectedItemStrategy())||void 0===e||e.selectFirstCell()})),this.makeRowVisible(0)}selectLastCell(){this.selectedNonVirtualRowIndexOffset=je.maxOffset,this.setOnRowMadeVisibleCallback((()=>{var e;null===(e=this.getSelectedItemStrategy())||void 0===e||e.selectLastCell()})),this.makeRowVisible(this.lastVirtualIndex)}get hiddenRowVisibleIndex(){return this.isFocusHidden?this.selectedNonVirtualRowIndexOffset?-1:this.selectedRowVirtualIndex:super.hiddenRowVisibleIndex}get selectedHiddenRowStrategy(){return this.isFocusHidden&&this.selectedHiddenStrategy?this.selectedHiddenStrategy:super.selectedHiddenRowStrategy}handleKeyDownForHiddenRow(e,t){const n=this.getSelectedHiddenStrategyNotEqualTo(e);return n?n.handleKeyDown(t):super.handleKeyDownForHiddenRow(e,t)}trySelectHiddenCell(e,t=null){return!!this.isFocusHidden&&(this.setOnRowMadeVisibleCallback((()=>{var n;null===(n=this.getSelectedItemStrategy())||void 0===n||n.selectItem(e),this.scrollToElement(null,m.None),null==t||t()})),this.makeRowVisible(this.selectedRowVirtualIndex),!0)}getSelectedItemStrategy(){var e;const t=null!==(e=super.getSelectedItemStrategy())&&void 0!==e?e:this.selectedHiddenStrategy;return t&&this.selectedHiddenStrategy&&t!==this.selectedHiddenStrategy&&this.syncSelectedColumns(this.selectedHiddenStrategy,t),t}onAfterTabKeyDownHandled(e){this.makingRowVisibleStarted?this.setOnRowMadeVisibleCallback((()=>{this.scrollToElement(null,m.None),super.onAfterTabKeyDownHandled(e)}),!0):super.onAfterTabKeyDownHandled(e)}onMakeRowVisible(e){var t;if(this.makingRowVisibleStarted)this.rowVirtualIndexToShow=null,this.scrollRequired=!0,super.onMakeRowVisible(null!==(t=this.findVisibleIndexByVirtualIndex(this.selectedRowVirtualIndex))&&void 0!==t?t:e),this.callOnRowMadeVisibleCallback(),this.notifyFocusedRowChanged();else{const t=this.getStrategy(e);t&&this.resetVirtualStateForIncomingEditRow(t)||this.updateSelectedVirtualIndex(-1),super.onMakeRowVisible(e)}}onHandlePageUpDownKey(e,t,n){if(this.isFocusHidden)return void this.makeRowVisibleWithAlignment(this.selectedRowVirtualIndex,t,n);const i=this.validateItemIndex(e),s=this.findClosestVisibleIndexWithVirtualIndex(i,null!=t?t:void 0),o=s.virtualIndex,r=e-s.visibleIndex;if(i===e)this.selectedNonVirtualRowIndexOffset=r,this.makeRowVisibleWithAlignment(o,t,n);else{this.setOnRowMadeVisibleCallback((()=>{this.selectedNonVirtualRowIndexOffset=0;const e=this.findVisibleIndexByVirtualIndex(o);e&&this.selectItem(e+r),this.scrollToElement(t,n)}));this.makeRowVisible(o+(t===g.Bottom?2:-1))}}shouldSaveFocusedRowVisibleIndex(e){return super.shouldSaveFocusedRowVisibleIndex(e)&&(this.selectedRowVirtualIndex!==e.getRowVisibleIndex()||0!==this.selectedNonVirtualRowIndexOffset||this.scrollRequired)}resetVirtualStateBySelectedStrategy(e){const t=this.getSelectedItemStrategy();t&&this.resetVirtualStateForIncomingEditRow(t)&&(t.updateSelectedItemByChildElement(e),t.switchToNestedContent(),this.saveRowSelectedColumn(t))}addEventListeners(){const e=this.getScrollViewer();e&&(e.addEventListener(R.eventName,this.boundOnScrollHandler),this.grid.addEventListener(Ue.eventName,this.boundOnStartCellEditingRequestHandler))}removeEventListeners(){const e=this.getScrollViewer();e&&(e.removeEventListener(R.eventName,this.boundOnScrollHandler),this.grid.removeEventListener(Ue.eventName,this.boundOnStartCellEditingRequestHandler))}onScroll(e){this.cellEditingStarted&&!this.isRowInView(this.selectedItemElement)&&(this.endEditRowItem(),this.cellEditingStarted=!1)}onStartCellEditingRequest(e){this.cellEditingStarted=e.detail.result,this.updateSelectedVirtualState()}correctSelectionIndex(e){var t,n,i;if(this.makingRowVisibleStarted||this.skipIndexCorrectionForIncomingEditRow)return this.skipIndexCorrectionForIncomingEditRow=!1,-1;if(this.selectedRowVirtualIndex<0)return e;if(e===this.selectedItemIndex&&!this.scrollRequired)this.isVirtualIndexRendered(this.selectedRowVirtualIndex)?(e=null!==(t=this.findVisibleIndexByVirtualIndex(this.selectedRowVirtualIndex))&&void 0!==t?t:e,k(this.targetElement)):(U(this.targetElement),null===(n=this.selectedHiddenStrategy)||void 0===n||n.makeObsolete());else if(this.isFocusHidden||this.scrollRequired){if(!this.isVirtualIndexRendered(this.selectedRowVirtualIndex))return this.makeRowVisible(this.selectedRowVirtualIndex),-1;e=null!==(i=this.findVisibleIndexByVirtualIndex(this.selectedRowVirtualIndex))&&void 0!==i?i:e,k(this.targetElement)}return e}resetVirtualStateForIncomingEditRow(e){if(this.skipIndexCorrectionForIncomingEditRow)return!1;const t=qe.castToEditRowStategy(e);return this.skipIndexCorrectionForIncomingEditRow=!!t&&e!==this.selectedHiddenStrategy&&this.isRowInView(t.targetElement),!!this.skipIndexCorrectionForIncomingEditRow&&(this.selectedRowVirtualIndex>=0&&(k(this.targetElement),this.updateSelectedVirtualIndex(-1),this.selectedHiddenStrategy=null),!0)}getSelectedHiddenStrategyNotEqualTo(e){return this.isFocusHidden&&this.selectedHiddenStrategy&&e!==this.selectedHiddenStrategy?this.selectedHiddenStrategy:null}callOnRowMadeVisibleCallback(){this.onRowMadeVisibleCallback&&(this.onRowMadeVisibleCallback(),this.onRowMadeVisibleCallback=null)}updateSelectedVirtualState(){this.selectedHiddenStrategy=super.getSelectedItemStrategy(),this.updateSelectedVirtualIndex()}setOnRowMadeVisibleCallback(e,t=!1){this.makingRowVisibleStarted&&!t||(this.onRowMadeVisibleCallback=e)}async makeRowVisible(e){var t;if(this.makingRowVisibleStarted)return;const n=this.getValidVirtualIndex(e);if(this.updateSelectedVirtualIndex(n),this.isVirtualIndexRendered(n))this.scrollRequired=!0,this.selectItem(null!==(t=this.findVisibleIndexByVirtualIndex(n))&&void 0!==t?t:0),this.callOnRowMadeVisibleCallback();else{this.rowVirtualIndexToShow=n,this.grid.makeRowVisible(n);const e=await this.waitForVirtualIndexToShow(n);this.rowVirtualIndexToShow===n&&(e?this.onMakeRowVisible(this.selectedItemIndex):this.rowVirtualIndexToShow=null)}}moveToSiblingRowByVirtualIndex(e){this.selectedNonVirtualRowIndexOffset+=e,this.makeRowVisibleWithAlignment(this.selectedRowVirtualIndex,null,m.None)}makeRowVisibleWithAlignment(e,t,n){this.setOnRowMadeVisibleCallback((()=>{this.scrollToElement(t,n)})),this.makeRowVisible(e)}updateSelectedVirtualIndex(e=null){if(null===e){const t=this.findClosestVisibleIndexWithVirtualIndex(this.selectedItemIndex);this.selectedNonVirtualRowIndexOffset=this.selectedItemIndex-t.visibleIndex,e=t.virtualIndex}null!==e&&(this.selectedRowVirtualIndex=e,this.isLastRowSelected=this.lastVirtualIndexSelected&&super.lastItemSelected)}waitForVirtualIndexToShow(e,t=0){return new Promise((n=>{setTimeout((async()=>{try{if(this.rowVirtualIndexToShow!==e)return void n(!1);const i=this.isVirtualIndexRendered(e);if(i||t>1)n(i);else{const i=await this.waitForVirtualIndexToShow(e,++t);n(i)}}catch{n(!1)}}),1e3)}))}getValidVirtualIndex(e){if(e<0)return 0;const t=this.lastVirtualIndex;return e>t?t:e}isVirtualIndexRendered(e){const{virtualIndex:t}=this.findClosestVisibleIndexWithVirtualIndex(0),{virtualIndex:n}=this.findClosestVisibleIndexWithVirtualIndex(super.itemCount-1);return e>=t&&e<=n}findClosestVisibleIndexWithVirtualIndex(e,t=g.Top){let n=-1,i=t===g.Top?-1:1,s=e,o=!1;for(;super.isIndexWithinBoundaries(s)||!o;){const t=super.getItem(s);if(t){if(n=F.getAttributeIntValue(t,x,-1),n>=0)break;s+=i}else o=!0,i*=-1,s=e+i}return n=n>=0?n:this.selectedRowVirtualIndex,{virtualIndex:n,visibleIndex:s}}findVisibleIndexByVirtualIndex(e){const t=e.toString(),n=super.itemCount;for(let e=0;e<n;++e){const n=super.getItem(e);if(n&&t===n.getAttribute(x))return super.validateItemIndex(e+this.selectedNonVirtualRowIndexOffset)}return null}isRowInView(e){const t=this.getScrollViewer();return!!t&&M.isElementInView(e,t)}}je.maxOffset=1e3;class Ye extends $e{constructor(e,t,n){super(e,t,n)}createItemStrategy(e){if(be.getRowType(e)===ue.Footer)return new Ae(this,this.grid,e);throw new Error("Not implemented")}}class Xe extends we{constructor(e){super(e,e),this.searchBoxInputSelector=`.${q.SearchBoxClassName} > input`}getShortcutContext(){return{IsMacOSPlatform:this.isMacOSPlatform}}queryItems(){return new Array(this.getToolbarContainer(),this.getPagerPanel(!0),this.getTopPanel(),this.getDataTable(),this.getPagerPanel(!1))}createDataTableStrategy(e){return new ze(this.grid,e)}createItemStrategy(e){switch(be.getNavigationAreaType(e)){case he.ToolbarContainer:return new Je(this.grid,e);case he.GroupPanel:case he.SearchBox:return new Qe(this.grid,e);case he.DataTable:return this.createDataTableStrategy(e);case he.Pager:return new Ze(this.grid,e)}return null}handleKeyDown(e){const t=u.KeyUtils.getEventKeyCode(e);return t===u.KeyCode.Tab?this.handleTabKeyDown(e):t===u.KeyCode.Up&&e.ctrlKey?(this.moveToPrevItem(),!0):t===u.KeyCode.Down&&e.ctrlKey?(this.moveToNextItem(),!0):!(t!==u.KeyCode.Key_f||!e.ctrlKey)&&this.tryFocusSearchBox()}handleTabKeyDown(e){return super.handleTabKeyDown(e,e.altKey)||(e.shiftKey?this.leaveBackward():this.leaveForward()),!0}moveToPrevItem(){this.selectedItemIndex>0?super.moveToPrevItem():this.leaveBackward()}moveToNextItem(){this.selectedItemIndex<this.itemCount-1?super.moveToNextItem():this.leaveForward()}getTopPanel(){var e,t;const n=null!==(e=this.getGroupPanelContentContainer())&&void 0!==e?e:this.getSearchBoxContainer();return null!==(t=null==n?void 0:n.closest(fe.TopPanelSelector))&&void 0!==t?t:null}tryFocusSearchBox(){const e=this.getSearchBoxContainer();if(e){const t=e.querySelector(this.searchBoxInputSelector);return y.focusElement(null!=t?t:e),!0}return!1}}class Je extends we{constructor(e,t){super(e,t)}getShortcutContext(){return{AreaType:he.ToolbarContainer}}}class Qe extends we{constructor(e,t){super(e,t),this.itemSelector=`.${q.HeaderElementClassName}`,this.searchBoxContainer=null,this.searchBoxContainer=this.getSearchBoxContainer()}getShortcutContext(){return{AreaType:this.getAreaType(),ColumnUID:this.getSelectedColumnUID()}}queryItems(){const e=this.queryItemsBySelector(this.itemSelector);return e.push(this.searchBoxContainer),e}handleKeyDown(e){if(e.ctrlKey&&(u.KeyUtils.getEventKeyCode(e)===u.KeyCode.Enter||u.KeyUtils.getEventKeyCode(e)===u.KeyCode.Space))return!0;if(super.handleKeyDown(e))return!0;if(this.nestedContentSelected)return!1;switch(u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.Left:return this.moveToPrevItem(),!0;case u.KeyCode.Right:return this.moveToNextItem(),!0;case u.KeyCode.Space:return this.performShortcutEvent(e),!0;case u.KeyCode.Up:if(!this.isSearchBoxItemSelected()&&this.isSearchBoxInMobileView()){const e=this.items.findIndex((e=>be.isSearchBoxContainer(e)));return this.selectItem(e),!0}break;case u.KeyCode.Down:if(e.altKey&&!this.isSearchBoxItemSelected())return this.performShortcutEvent(e),!0;if(!e.altKey&&this.isSearchBoxItemSelected()&&this.isSearchBoxInMobileView())return this.selectItem(0),!0}return!1}isImmediatelyClickEnabled(){return this.selectedItemElement!==this.searchBoxContainer}getAreaType(){return be.isSearchBoxContainer(this.selectedItemElement)?he.SearchBox:he.GroupPanel}getSelectedColumnUID(){return this.getColumnUID(this.selectedItemElement)}isSearchBoxItemSelected(){return this.getAreaType()===he.SearchBox}isSearchBoxInMobileView(){return!!this.searchBoxContainer&&this.searchBoxContainer.offsetWidth===this.targetElement.offsetWidth}}class Ze extends we{constructor(e,t){super(e,t)}getShortcutContext(){return{AreaType:he.Pager}}handleKeyDown(e){if(super.handleKeyDown(e))return!0;if(this.nestedContentSelected)return!1;switch(u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.Left:case u.KeyCode.Right:case u.KeyCode.Home:case u.KeyCode.End:return this.performShortcutEvent(e),!0}return!1}}class et extends b{constructor(e,t){super(t,e),this.itemSelector=`.${q.ColumnChooserItemClassName}`,this.savedColumnUID=null,this.hasXafActionsContainer=!1,this.boundOnDragStopHandler=this.onDragStop.bind(this),this.columnChooser=e,this.addEventSubscriptions(),this.hasXafActionsContainer=this.targetElement.nextSibling instanceof HTMLElement&&this.targetElement.nextSibling.classList.contains("column-chooser-actions-container")}initialize(){super.initialize(),null!==this.savedColumnUID&&this.restoreSelectedColumn()}queryItems(){return this.queryItemsBySelector(this.itemSelector)}getShortcutContext(){return{AreaType:he.ColumnChooser,ColumnUID:this.getSelectedColumnUID()}}isImmediatelyClickEnabled(){return!0}handleKeyDown(e){if(super.handleKeyDown(e))return!0;if(this.nestedContentSelected)return!1;switch(u.KeyUtils.getEventKeyCode(e)){case u.KeyCode.Up:return this.moveToPrevItem(),!0;case u.KeyCode.Down:return this.moveToNextItem(),!0;case u.KeyCode.Tab:return e.shiftKey?this.moveToPrevItem(!0):this.lastItemSelected&&this.hasXafActionsContainer?this.leaveForward():this.moveToNextItem(!0),!0;case u.KeyCode.Space:return this.performShortcutEvent(e),!0}return!1}onDispose(){super.onDispose(),this.removeEventSubscriptions()}onDragStop(e){this.savedColumnUID=this.getSelectedColumnUID()}restoreSelectedColumn(){const e=this.findItemByColumnUID(this.savedColumnUID);e>=0&&this.selectItem(e),this.savedColumnUID=null}addEventSubscriptions(){this.columnChooser.addEventListener(s.eventName,this.boundOnDragStopHandler)}removeEventSubscriptions(){this.columnChooser.removeEventListener(s.eventName,this.boundOnDragStopHandler)}findItemByColumnUID(e){return this.items.findIndex((t=>this.getColumnUID(t)===e))}getSelectedColumnUID(){return this.getColumnUID(this.selectedItemElement)}getColumnUID(e){var t;return null!==(t=this.columnChooser.getColumnUid(e))&&void 0!==t?t:-1}}var tt,nt,it,st,ot;!function(e){e[e.Append=0]="Append",e[e.Before=1]="Before",e[e.After=2]="After",e[e.Inside=3]="Inside"}(tt||(tt={}));class rt extends e{constructor(){super(),this.tableContainer=null,this.dropTargetElement=null,this.dropPosition=tt.Before,this.lastObservedWidth=0,this.resizeObserver=new ResizeObserver(this.onTableContainerSizeChanged.bind(this)),this.boundOnTableContainerScrollHandler=this.onTableContainerScroll.bind(this)}get isHidden(){return!this.dropTargetElement}show(){this.classList.add(t.Active),this.render()}hide(){this.dropTargetElement=null,this.classList.remove(t.Active),this.removeAttribute("style")}updatePosition(e,t){this.dropTargetElement=e,this.dropPosition=t}initializeComponent(){super.initializeComponent(),this.tableContainer=this.closest(`.${f.ContentContainerClassName}`),this.tableContainer&&(this.tableContainer.addEventListener("scroll",this.boundOnTableContainerScrollHandler),this.resizeObserver.observe(this.tableContainer))}disposeComponent(){super.disposeComponent(),this.tableContainer&&(this.tableContainer.removeEventListener("scroll",this.boundOnTableContainerScrollHandler),this.resizeObserver.unobserve(this.tableContainer))}onTableContainerScroll(){this.render()}onTableContainerSizeChanged(e){const t=e[0].contentRect.width;this.lastObservedWidth!==t&&(this.lastObservedWidth=t,this.render())}render(){this.isHidden||requestAnimationFrame(this.updateIndicatorStyle.bind(this))}updateIndicatorStyle(){const e=this.getTargetElement();this.style.height=e&&this.shouldFitIntoElement()?`${e.offsetHeight}px`:"";const t=this.calculatePosition();this.style.transform=`translateY(${t})`,this.style.marginLeft=this.calculateLeftOffset()}calculatePosition(){const e=this.getTargetElement(),t=this.calculateIndicatorPosition(e),n=this.shouldFitIntoElement()?t:rt.getCenteredPosition(t,this.offsetHeight);return`${Math.ceil(n)}px`}shouldFitIntoElement(){return this.dropPosition===tt.Append||this.dropPosition===tt.Inside}calculateIndicatorPosition(e){if(!e||!this.tableContainer)return 0;const t=e.getBoundingClientRect(),n=this.tableContainer.getBoundingClientRect(),i=t.top-n.top;return this.dropPosition===tt.After?i+e.offsetHeight:i}calculateLeftOffset(){const e=this.getTargetElement();if(!e||be.getRowType(e)!==ue.Data)return"";const t=e.querySelectorAll(`.${q.IndentCellClassName}`);if(0===t.length)return"";const n=t[t.length-1];return`${n.offsetLeft+n.offsetWidth}px`}getTargetElement(){return this.dropPosition===tt.Append?this.tableContainer:this.dropTargetElement}static getCenteredPosition(e,t){return e-t/2}}!function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(nt||(nt={}));class lt{constructor(){this.downTargetElement=lt.addTargetToDom(q.DraggingDownTargetClassName,z.ArrowDownIconName),this.upTargetElement=lt.addTargetToDom(q.DraggingUpTargetClassName,z.ArrowUpIconName);const e=this.upTargetElement.getBoundingClientRect();this.targetElementSize={width:e.width,height:e.height},this.upTargetElementPosition=null,this.downTargetElementPosition=null,this.hide()}show(){this.upTargetElementPosition&&this.downTargetElementPosition&&(this.downTargetElement.style.transform=$.translateByPoint(this.downTargetElementPosition),this.upTargetElement.style.transform=$.translateByPoint(this.upTargetElementPosition),this.downTargetElement.classList.remove(t.DisplayNone),this.upTargetElement.classList.remove(t.DisplayNone))}hide(){this.downTargetElement.classList.add(t.DisplayNone),this.upTargetElement.classList.add(t.DisplayNone)}setPositionRelativeToElement(e,t){this.setUpTargetPosition(e,t),this.setDownTargetPosition(e,t)}setUpTargetPosition(e,t){this.upTargetElementPosition={x:this.getTargetXCoordinate(e,t),y:e.y+e.height}}setDownTargetPosition(e,t){this.downTargetElementPosition={x:this.getTargetXCoordinate(e,t),y:e.y-this.targetElementSize.height}}getTargetXCoordinate(e,t){let n=e.x-this.targetElementSize.width/2;return t===nt.Right&&(n+=e.width),n}dispose(){lt.removeElementsByClassName(q.DraggingDownTargetClassName),lt.removeElementsByClassName(q.DraggingUpTargetClassName)}static removeElementsByClassName(e){const t=document.querySelectorAll(`.${e}`);for(let e=0;e<t.length;e++)t[e].remove()}static addTargetToDom(e,t){const n=document.createElement("div");return n.className=e,n.innerHTML=z.getSvgHtml(t),document.body.appendChild(n),n}}!function(e){e[e.HeaderRow=0]="HeaderRow",e[e.GroupPanel=1]="GroupPanel",e[e.ColumnChooser=2]="ColumnChooser"}(it||(it={})),function(e){e[e.Left=0]="Left",e[e.Right=1]="Right"}(st||(st={}));class at extends N{constructor(e){super(),this.draggableContext=null,this.boundOnDocumentPointerUpHandler=this.onDocumentPointerUp.bind(this),this.boundOnHeaderMouseMoveHandler=this.onHeaderMouseMove.bind(this),this.boundOnElementMouseOutHandler=this.onElementMouseOut.bind(this),this.boundOnGroupPanelMouseMoveHandler=this.onGroupPanelMouseMove.bind(this),this.boundOnEmptyHeaderCellMouseMoveHandler=this.onEmptyHeaderCellMouseMove.bind(this),this.boundOnGroupFreeSpaceMouseMoveHandler=this.onGroupFreeSpaceMouseMove.bind(this),this.grid=e}start(e,t){this.isColumnDraggingAllowed(e)&&(super.start(e,t),this.grid.styleGenerator.notifyColumnDragStarted())}stop(){var e;null===(e=this.draggableContext)||void 0===e||e.targets.dispose(),super.stop(),document.body.classList.remove(q.MoveCursorCssClassName),this.grid.styleGenerator.notifyColumnDragEnded()}getDraggableElement(e){return at.addDraggableHeaderToDOM(e)}hideDraggableElement(){at.removeDraggableHeaderFromDom()}updateDraggableContextByEvent(e){const t=document.elementFromPoint(e.clientX,e.clientY);this.updateDraggableContextByElement(t,e)}onDocumentPointerUp(e){var t;const n=null===(t=this.draggableContext)||void 0===t?void 0:t.srcElement;n&&!n.isConnected&&this.stop()}onHeaderMouseMove(e){this.updateDraggableContextByElement(e.target,e)}onGroupPanelMouseMove(){const e=this.grid.getGroupPanelContentContainer();this.onPanelElementMouseMove(e,it.GroupPanel)}onEmptyHeaderCellMouseMove(){const e=this.grid.getEmptyHeaderCellContentContainer();this.onPanelElementMouseMove(e,it.HeaderRow)}onGroupFreeSpaceMouseMove(){const e=this.grid.getGroupContentFreeSpaceElement();this.onPanelElementMouseMove(e,it.GroupPanel)}isColumnDraggingAllowed(e){const t=this.grid.getColumnInfo(e);return!!t&&(t.allowReorder||t.allowGroup&&!!this.grid.getGroupPanelContentContainer())}onPanelElementMouseMove(e,t){e&&this.draggableContext&&this.draggableContext.mouseOverElement!==e&&this.draggableContext.srcElement!==e.previousElementSibling&&(this.clearMoveStateFromDraggableContext(),this.setMouseOverElementInfo(e),this.draggableContext.dropArea=t,this.isPossibleDropToCurrentArea()&&(this.draggableContext.isPossibleDropItem=!0,this.draggableContext.targets.setPositionRelativeToElement(this.draggableContext.mouseOverElementRect,nt.Left)),this.refreshUI())}isPossibleDropToCurrentArea(){var e,t,n;return(null===(e=this.draggableContext)||void 0===e?void 0:e.allowGroup)||(null===(t=this.draggableContext)||void 0===t?void 0:t.dropArea)===(null===(n=this.draggableContext)||void 0===n?void 0:n.srcArea)}createColumnDraggedHandlerArgs(){var e;if(!this.draggableContext||!this.draggableContext.isPossibleDropItem)return null;const t=this.draggableContext;let n=t.beforeColumnUid;return t.dropArea===it.HeaderRow&&(n=null!==(e=(e=>{var t;return e.dragRestrictions?null!==(t=e.dragRestrictions.nextColumnUid)&&void 0!==t?t:-1:null})(t.draggedColumn))&&void 0!==e?e:n),{columnUid:t.draggedColumn.uID,beforeColumnUid:n,srcArea:t.srcArea,dropArea:t.dropArea}}getLatestVisibleColumnIndex(){for(let t=this.grid.columnsInfo.length-1;t>=0;t--)if(!(e=this.grid.columnsInfo[t]).isIndent&&!e.isEmpty)return t;var e;return-1}getRestrictedDropHeaderInfo(e){const t=this.getDropColumnArea(e);if(!!(!this.draggableContext||!this.draggableContext.draggedColumn.dragRestrictions||this.draggableContext.srcArea!==it.GroupPanel||this.draggableContext.dropArea===it.GroupPanel||t!==it.HeaderRow))return null;let n,i=st.Left;const s=this.getLatestVisibleColumnIndex(),o=this.draggableContext.draggedColumn.dragRestrictions;let r=-1;return o&&null!==o.nextVisibleColumnUid?r=this.grid.columnsInfo.findIndex((e=>e.uID===o.nextVisibleColumnUid&&!e.isIndent)):(r=s,i=st.Right),-1!==r&&(n=this.grid.getHeaderCells()[r]),n?{dropHeaderElement:n,position:i}:null}updateDraggableContextByElement(e,t){let n=this.grid.findHeaderElement(e);const i=this.grid.getColumnInfo(n);n&&i&&this.draggableContext&&this.draggableContext.draggedColumn.parentUID!==i.parentUID&&(n=this.findMatchingParentHeader(n)),n&&(this.updateDraggableContext(n,t),this.refreshUI())}findMatchingParentHeader(e){let t=this.grid.getColumnInfo(e);for(;t;){if(this.draggableContext&&this.draggableContext.draggedColumn.parentUID===t.parentUID)return this.grid.getHeaderCell(t.headerRowIndex,t.headerCellIndex);t=t.parent}return e}updateDraggableContext(e,t){var n;if(this.clearMoveStateFromDraggableContext(),null==this.draggableContext)return;const i=e?this.getRestrictedDropHeaderInfo(e):null;if(i&&(e=i.dropHeaderElement),this.draggableContext.srcElement!==e&&(this.setMouseOverElementInfo(e),this.isPossibleDropToCurrentArea())){const s=void 0!==(null==i?void 0:i.position)?i.position===st.Left:this.isThereCursorInLeftPart(t),o=this.grid.getColumnInfo(e);if(o&&!o.hasLeafs&&o.uID===this.draggableContext.draggedColumn.parentUID)this.draggableContext.beforeColumnUid=-1,this.draggableContext.isPossibleDropItem=!0;else if(s&&e.previousElementSibling!==this.draggableContext.srcElement||!s&&e.nextElementSibling!==this.draggableContext.srcElement){let t=s?o:this.grid.getColumnInfo(e.nextElementSibling),i=s?this.grid.getColumnInfo(e.previousElementSibling):o;s&&i&&i.parentUID!==(null==t?void 0:t.parentUID)&&(i=null),!s&&t&&t.parentUID!==(null==i?void 0:i.parentUID)&&(t=null);if(!this.canDropBetweenColumns(i,t))return void(this.draggableContext.isPossibleDropItem=!1);this.draggableContext.beforeColumnUid=null!==(n=null==t?void 0:t.uID)&&void 0!==n?n:-1,this.draggableContext.isPossibleDropItem=!0}this.draggableContext.isPossibleDropItem&&this.draggableContext.targets.setPositionRelativeToElement(this.draggableContext.mouseOverElementRect,s?nt.Left:nt.Right)}}static canDropColumnWithDisabledReorderingBetweenColumns(e,t,n,i){var s,o;if(t!==it.GroupPanel)return!1;const r=e.dragRestrictions;if(r){const e=null!==(s=null==n?void 0:n.uID)&&void 0!==s?s:null,t=null!==(o=null==i?void 0:i.uID)&&void 0!==o?o:null;if(r.previousColumnUid!==e&&r.nextColumnUid!==t&&r.nextVisibleColumnUid!==t)return!1}return!0}canDropBetweenColumns(e,t){if(!this.draggableContext)return!1;if(this.draggableContext.dropArea===it.GroupPanel)return!0;if(!this.draggableContext.draggedColumn.allowReorder&&!at.canDropColumnWithDisabledReorderingBetweenColumns(this.draggableContext.draggedColumn,this.draggableContext.srcArea,e,t))return!1;const n=this.draggableContext.draggedColumn.parentUID,i=t&&n===t.parentUID;if(!(!i&&e&&n===e.parentUID)&&!i)return!1;const s=e=>"left"===e?0:"right"===e?2:1,o=[];e&&o.push(s(e.fixedPosition)),o.push(s(this.draggableContext.draggedColumn.fixedPosition)),t&&o.push(s(t.fixedPosition));for(let e=0;e<o.length-1;++e)if(o[e]>o[e+1])return!1;return!0}getDropColumnArea(e){const t=this.grid.getGroupPanelContentContainer();return t&&at.isThereHeaderInsideGroupPanel(t,e)?it.GroupPanel:it.HeaderRow}static isThereHeaderInsideGroupPanel(e,t){const n=e.childNodes.length;for(let i=0;i<n;i++)if(e.childNodes[i]===t)return!0;return!1}refreshUICore(){super.refreshUICore();const e=this.draggableContext;e&&(e.isPossibleDropItem?e.targets.show():e.targets.hide(),document.body.classList.add(q.MoveCursorCssClassName))}setMouseOverElementInfo(e){if(super.setMouseOverElementInfo(e),!this.draggableContext)return;const t=this.grid.getColumnInfo(e),n=null==t?void 0:t.getRightLeaf();if(n){const t=this.grid.getHeaderCell(n.headerRowIndex,n.headerCellIndex),i=null==t?void 0:t.getBoundingClientRect();if(i&&this.draggableContext.mouseOverElementRect){const t=e.getBoundingClientRect();this.draggableContext.mouseOverElementRect.height=i.height+i.top-t.top}}if(this.draggableContext.scrollViewerRect&&this.draggableContext.mouseOverElementRect){const e=this.draggableContext.scrollViewerRect;this.draggableContext.mouseOverElementRect.x<e.x&&(this.draggableContext.mouseOverElementRect.x=e.x),this.draggableContext.mouseOverElementRect.x+this.draggableContext.mouseOverElementRect.width>e.x+e.width&&(this.draggableContext.mouseOverElementRect.width=e.x+e.width-this.draggableContext.mouseOverElementRect.x)}this.draggableContext.dropArea=this.getDropColumnArea(e)}isThereCursorInLeftPart(e){if(!this.draggableContext||!this.draggableContext.mouseOverElementRect)return!1;const t=this.draggableContext.mouseOverElementRect;return e.pageX<t.x+t.width/2}updateDraggableElementPosition(){if(!this.draggableContext)return;const e=this.draggableContext;e.draggableElementPosition={x:e.srcElementPosition.x+(e.currentCursorPosition.x-e.initialCursorPosition.x)+(e.currentScrollPosition.x-e.initialScrollPosition.x)+e.currentScrollPosition.x,y:e.srcElementPosition.y+(e.currentCursorPosition.y-e.initialCursorPosition.y)+(e.currentScrollPosition.y-e.initialScrollPosition.y)+e.currentScrollPosition.y}}addEventSubscriptions(){super.addEventSubscriptions(),document.addEventListener("pointerup",this.boundOnDocumentPointerUpHandler),this.addHeaderEventSubscriptions(),this.addGroupPanelEventSubscriptions(),this.addEmptyHeaderCellEventSubscriptions(),this.addGroupContentFreeSpaceElementEventSubscriptions()}addHeaderEventSubscriptions(){const e=this.grid.getHeaderCells();for(let t=0;t<e.length;t++){const n=e[t];n.addEventListener("pointermove",this.boundOnHeaderMouseMoveHandler),n.addEventListener("pointerout",this.boundOnElementMouseOutHandler)}}addEmptyHeaderCellEventSubscriptions(){const e=this.grid.getEmptyHeaderCellContentContainer();e&&this.addPanelElementEventSubscriptions(e,this.boundOnEmptyHeaderCellMouseMoveHandler)}addGroupPanelEventSubscriptions(){if(!this.shouldAddEventListenersToGroupPanel())return;const e=this.grid.getGroupPanelContentContainer();this.addPanelElementEventSubscriptions(e,this.boundOnGroupPanelMouseMoveHandler)}addGroupContentFreeSpaceElementEventSubscriptions(){const e=this.grid.getGroupContentFreeSpaceElement();e&&this.addPanelElementEventSubscriptions(e,this.boundOnGroupFreeSpaceMouseMoveHandler)}addPanelElementEventSubscriptions(e,t){e.addEventListener("pointermove",t),e.addEventListener("pointerout",this.boundOnElementMouseOutHandler)}removeEventSubscriptions(){super.removeEventSubscriptions(),document.removeEventListener("pointerup",this.boundOnDocumentPointerUpHandler),this.removeHeaderEventSubscriptions(),this.removeGroupPanelEventSubscriptions(),this.removeEmptyHeaderCellEventSubscriptions(),this.removeGroupContentFreeSpaceElementEventSubscriptions()}removeHeaderEventSubscriptions(){const e=this.grid.getHeaderCells();for(let t=0;t<e.length;t++){const n=e[t];n.removeEventListener("pointermove",this.boundOnHeaderMouseMoveHandler),n.removeEventListener("pointerout",this.boundOnElementMouseOutHandler)}}removeGroupPanelEventSubscriptions(){if(!this.shouldAddEventListenersToGroupPanel())return;const e=this.grid.getGroupPanelContentContainer();this.removePanelElementEventSubscriptions(e,this.boundOnGroupPanelMouseMoveHandler)}removeEmptyHeaderCellEventSubscriptions(){const e=this.grid.getEmptyHeaderCellContentContainer();e&&this.removePanelElementEventSubscriptions(e,this.boundOnEmptyHeaderCellMouseMoveHandler)}removeGroupContentFreeSpaceElementEventSubscriptions(){const e=this.grid.getGroupContentFreeSpaceElement();e&&this.removePanelElementEventSubscriptions(e,this.boundOnGroupFreeSpaceMouseMoveHandler)}removePanelElementEventSubscriptions(e,t){e.removeEventListener("pointermove",t),e.removeEventListener("pointerout",this.boundOnElementMouseOutHandler)}shouldAddEventListenersToGroupPanel(){const e=this.grid.getGroupPanelContentContainer();return!!e&&!(null==e?void 0:e.querySelector(`.${q.HeaderElementClassName}`))}static addDraggableHeaderToDOM(e){const t=document.createElement("div");t.className=`${q.DraggableHeaderClassName}`,e.classList.contains(q.SelectionCellClassName)&&t.classList.add(q.SelectionCellClassName),e.classList.contains(q.CommandCellClassName)&&t.classList.add(q.CommandCellClassName);const n=e.getBoundingClientRect();t.style.width=`${n.width}px`,t.style.height=`${n.height}px`;const i=getComputedStyle(e);t.style.padding=i.padding,t.style.font=i.font,t.style.whiteSpace=i.whiteSpace;const s=this.getContentElement(e).cloneNode(!0);return t.appendChild(s),document.body.appendChild(t),t}static getContentElement(e){return e.querySelector(`.${q.HeaderContentClassName}`)}static removeDraggableHeaderFromDom(){const e=document.querySelectorAll(`.${q.DraggableHeaderClassName}`);for(let t=0;t<e.length;t++)e[t].remove()}createDraggableContext(e,t,n){const i=super.createDraggableContext(e,t,n),s=this.grid.getScrollViewer(),o=this.grid.getColumnInfo(e);return{...i,allowGroup:o.allowGroup,scrollViewerRect:s&&s.getRectangle(),draggedColumn:o,beforeColumnUid:null,srcArea:e.closest(`.${q.GroupPanelContentContainerClassName}`)?it.GroupPanel:it.HeaderRow,dropArea:it.HeaderRow,targets:new lt}}clearMoveStateFromDraggableContext(){super.clearMoveStateFromDraggableContext(),this.draggableContext&&(this.draggableContext.beforeColumnUid=null)}}class dt{constructor(e){this._startSelectionRowIndex=-1,this._endSelectionRowIndex=-1,this.savedSelectedRows=[],this.boundOnRowPointerEnterHandler=this.onRowPointerEnter.bind(this),this.grid=e}get startSelectionRowIndex(){return this._startSelectionRowIndex}get endSelectionRowIndex(){return this._endSelectionRowIndex}start(e,t){this._startSelectionRowIndex=this.grid.getRowVisibleIndexByTarget(e),this._startSelectionRowIndex>-1&&(this._endSelectionRowIndex=this._startSelectionRowIndex,t&&this.storeSelectedRows(),this.addRowEventSubscriptions(),this.updateRowsSelection())}stop(){this._startSelectionRowIndex>-1&&(this.deselectRows(),this.removeRowEventSubscriptions(),this.restoreSelectedRows(),this._startSelectionRowIndex=-1,this._endSelectionRowIndex=-1)}isStarted(){return this._startSelectionRowIndex>-1&&this._endSelectionRowIndex>-1}updateSeletionByEvent(e){const t=document.elementFromPoint(e.clientX,e.clientY);this.updateSeletionByElement(t)}findRowElement(e){return this.grid.getMainElement().querySelector(`tr[${Ie.VisibleIndexName}="${e}"]`)}findRowElements(){return this.grid.getMainElement().querySelectorAll(`tr[${Ie.VisibleIndexName}]`)}findSelectedRowElements(){return this.grid.getMainElement().querySelectorAll(`tr.${q.SelectedRowClassName}[${Ie.VisibleIndexName}]`)}addRowEventSubscriptions(){const e=this.findRowElements();if(e)for(let t=0;t<e.length;t++)e[t].addEventListener("pointerenter",this.boundOnRowPointerEnterHandler)}removeRowEventSubscriptions(){const e=this.findRowElements();if(e)for(let t=0;t<e.length;t++)e[t].removeEventListener("pointerenter",this.boundOnRowPointerEnterHandler)}updateSeletionByElement(e){const t=this.grid.getRowVisibleIndexByTarget(e,!0);t>-1&&(this._endSelectionRowIndex=t,this.updateRowsSelection())}onRowPointerEnter(e){this.updateSeletionByElement(e.target)}selectRows(e){for(let t=0;t<e.length;t++){const n=this.findRowElement(e[t]);n&&n.classList.add(0===t||t===e.length-1?q.TouchSelectionEdgeClassName:q.TouchSelectionClassName)}}deselectRows(){const e=this.findRowElements();if(e)for(let t=0;t<e.length;t++)e[t].classList.remove(q.TouchSelectionClassName,q.TouchSelectionEdgeClassName)}updateRowsSelection(){if(-1===this.startSelectionRowIndex||-1===this.endSelectionRowIndex)return;this.deselectRows();const e=Math.min(this.startSelectionRowIndex,this.endSelectionRowIndex),t=Math.max(this.startSelectionRowIndex,this.endSelectionRowIndex),n=Array.from({length:t-e+1},((t,n)=>n+e));this.selectRows(n)}storeSelectedRows(){const e=this.findSelectedRowElements();if(e)for(let t=0;t<e.length;t++)e[t].classList.remove(q.SelectedRowClassName),this.savedSelectedRows.push(e[t])}restoreSelectedRows(){for(let e=0;e<this.savedSelectedRows.length;e++)this.savedSelectedRows[e].classList.add(q.SelectedRowClassName);this.savedSelectedRows=[]}}!function(e){e[e.Disabled=0]="Disabled",e[e.LeftBorder=1]="LeftBorder",e[e.BetweenBorders=2]="BetweenBorders",e[e.RightBorder=3]="RightBorder"}(ot||(ot={}));class ht{get highlightLeftBorder(){return this.scrollStatus===ot.BetweenBorders||this.scrollStatus===ot.RightBorder||this.isDragging}get highlightRightBorder(){return this.scrollStatus===ot.BetweenBorders||this.scrollStatus===ot.LeftBorder||this.isDragging}constructor(e){this.grid=e,this.gridUid="",this.lastGeneratedStyle="",this.isDragging=!1,this.scrollStatus=ot.Disabled}notifyColumnDragStarted(){this.hasAnyFixedColumn()&&(this.isDragging=!0,requestAnimationFrame((()=>this.updateFixedCellsStyle())))}notifyColumnDragEnded(){this.hasAnyFixedColumn()&&(this.isDragging=!1,requestAnimationFrame((()=>this.updateFixedCellsStyle())))}scrollPositionUpdated(e){this.hasAnyFixedColumn()&&(this.scrollStatus=this.calcScrollStatus(e),requestAnimationFrame((()=>this.updateFixedCellsStyle())))}updateFixedCellsStyle(){this.gridUid=this.grid.getAttribute(mt);const e=this.grid.querySelector("style");if(!e)return;let t="";this.hasAnyFixedColumn()&&(t+=this.createPositionStyles(),t+=this.createBorderStyles()),this.lastGeneratedStyle!==t&&(e.innerText=t,this.lastGeneratedStyle=t)}createPositionStyles(){const e=this.grid.getColElements(),t=this.getOffsetWidths(e),n=new Array(t.length),i=new Array(t.length);n[0]=0,i[t.length-1]=0;for(let e=1;e<t.length;++e)n[e]=n[e-1]+t[e-1];for(let e=t.length-2;e>=0;--e)i[e]=i[e+1]+t[e+1];const s=this.grid.columnsInfo.some((e=>"left"===e.fixedPosition));let o="";for(const e of this.grid.headerLayout)for(const t of e){let e=t.fixedPosition;if(t.isIndent&&s&&(e="left"),e){let s=t;const r="left"===e;s.hasLeafs&&(s=r?t.getLeftLeaf():t.getRightLeaf());o+=this.createPositionStyle(t,s.leafIndex,e,r?n[s.leafIndex]:i[s.leafIndex])}}return o}hasAnyBandColumn(){return this.grid.headerLayout.length>1}hasAnyFixedColumn(){return this.grid.columnsInfo.some((e=>!!e.fixedPosition))}calcScrollStatus(e){const t=this.grid.getDataTable().clientWidth;return t===e.clientWidth?ot.Disabled:0===e.scrollLeft?ot.LeftBorder:Math.abs(t-e.clientWidth-e.scrollLeft)<1?ot.RightBorder:ot.BetweenBorders}createPositionStyle(e,t,n,i){const s=[],o=this.hasAnyBandColumn();if(o&&(s.push(this.getHeaderRowsSelector(e.headerRowIndex)),s.push(this.createPositionStyleForColumn("th",e.headerCellIndex,n,i))),!e.hasLeafs){const e=o?"td":"";s.push(this.getTableRowsSelector()),s.push(this.createPositionStyleForColumn(e,t,n,i))}return s.join("")}createPositionStyleForColumn(e,t,n,i){return` > ${e}.${q.FixedCellClassName}:nth-child(${t+1}) { ${n}: ${i}px; }`}createBorderStyles(){const e=[],t=this.hasAnyBandColumn();return this.highlightLeftBorder&&(e.push(this.createLeftBorderStyle("th")),e.push(this.createLeftBorderStyle("td"))),this.highlightRightBorder&&(e.push(t?this.createHeaderRightBorderStyle():this.createRightBorderStyle("th")),e.push(this.createRightBorderStyle("td"))),e.join("")}createLeftBorderStyle(e){return this.getTableRowsSelector()+` > ${e}.${q.LastLeftFixedCellClassName} { border-right-color: var(--dxbl-grid-fixed-column-border-color); }`}createRightBorderStyle(e){return this.getTableRowsSelector()+` > ${e}:not(.${q.FixedCellClassName}) + ${e}.${q.FixedCellClassName} { border-left-color: var(--dxbl-grid-fixed-column-border-color); }`}createHeaderRightBorderStyle(){const e=[];for(const t of this.grid.headerLayout){const n=t.find((e=>"right"===e.fixedPosition));n&&(e.push(this.getHeaderRowsSelector(n.headerRowIndex)),e.push(` > th.${q.FixedCellClassName}:nth-child(${n.headerCellIndex+1})`),e.push(" { border-left-color: var(--dxbl-grid-fixed-column-border-color); }"))}return e.join("")}getHeaderRowsSelector(e){return this.getTableRowsSelector()+`.${q.HeaderRowClassName}:nth-child(${e+1})`}getTableRowsSelector(){return[`${this.grid.getTagName()}[${mt}="${this.gridUid}"]`,`.${f.ClassName}`,`.${f.ContentContainerClassName}`,`.${q.DataTableClassName}`,"*","tr"].join(" > ")}getOffsetWidths(e){const t=[];for(let n=0;n<e.length;++n)t.push(this.grid.getColElementOffsetWidth(n));return t}}const ut="dxbl-tree-list",ct="dxbl-grid",mt="data-dx-grid-uid",gt=document.createElement("template");gt.innerHTML="\n<style></style>\n<slot />";const Ct="data-dx-grid-uid";class pt extends e{constructor(){super(),this._columnsInfo=[],this._headerLayout=[],this._allowSelectRowByClick=!1,this._enableFocusedRow=!1,this._enableInplaceEditing=!1,this._enableMultiSelect=!1,this._enableRowDrag=!1,this._enableItemDrop=!1,this._enablePositionalItemDrop=!1,this._hasRowClickEvent=!1,this._hasRowDblClickEvent=!1,this._isEditing=!1,this._focusedRowIndex=-1,this._latestAutoFitColumnWidthsRequestGuid=null,this.boundOnDragStartHandler=this.onDragStart.bind(this),this.boundOnDragStopHandler=this.onDragStop.bind(this),this.boundOnDragCancelHandler=this.onDragCancel.bind(this),this.boundOnClickHandler=this.onClick.bind(this),this.boundOnDblClickHandler=this.onDblClick.bind(this),this.boundOnScrollViewerUpdateHandler=this.onScrollViewerUpdate.bind(this),this.boundOnStartCellEditingRequestCompleteHandler=this.onStartCellEditingRequestComplete.bind(this),this.boundOnScrollViewerScrollHandler=this.onScrollViewerScroll.bind(this),this.boundOnDocumentKeyDownHandler=this.onDocumentKeyDown.bind(this),this.boundOnMouseDownHandler=this.onMouseDown.bind(this),this.currentWidth=-1,this.focusedRowRestoreVisibilityTimeout=500,this.rowDraggingHelper=this.createRowDraggingHelper(),this.columnDraggingHelper=new at(this),this.columnWidthsController=new ne(this),this.selectionHelper=new dt(this),this._editorManager=this.createEditorManager(),this._styleGenerator=null,this.resizeObserver=new ResizeObserver(this.onSizeChanged.bind(this))}initializeComponent(){super.initializeComponent(),this._editorManager.initialize(),this._styleGenerator=new ht(this),this._uiHandlersBridge=this.querySelector(d),this.initializeKeyboardNavigator(),this.ensureColumnResizeStrategy(),this.addEventSubscriptions(),this.resizeObserver.observe(this)}disposeComponent(){this.removeEventSubscriptions(),this.resizeObserver.unobserve(this),this._editorManager.dispose(),delete this._uiHandlersBridge,delete this.keyboardNavigator,super.disposeComponent()}createEditorManager(){return new ve(this)}get hoverTitleElementsSelector(){return fe.TitleElementsSelector}get bypassNonInlineHoverTitleElementChildSelector(){return null}getMainElement(){return this}getDataTable(){return this.querySelector(fe.DataTableSelector)}getHeaderRows(){return this.querySelectorAll(`thead > tr.${q.HeaderRowClassName}`)}getFilterRow(){return this.querySelector(`thead > tr.${q.FilterRowClassName}`)}getHeaderCells(){return this.querySelectorAll(`.${q.HeaderElementClassName}`)}getToolbarContainer(){return this.querySelector(`.${q.ToolbarContainerClassName}`)}getGroupPanelContentContainer(){return this.querySelector(`.${q.GroupPanelContentContainerClassName}`)}getSearchBoxContainer(){return this.querySelector(`.${q.SearchBoxContainerClassName}`)}getEmptyHeaderCellContentContainer(){return this.querySelector(`.${q.EmptyHeaderCellContentContainerClassName}`)}getGroupContentFreeSpaceElement(){return this.querySelector(`.${q.GroupContentFreeSpaceClassName}`)}getColElements(){return this.querySelectorAll(fe.ColElementSelector)}getFooterRow(){return this.querySelector(`.${q.FooterRowClassName}`)}getPagerPanel(e){return this.querySelector(`:scope > ${fe.PagerPanelSelector}.${e?q.TopPanelClassName:q.BottomPanelClassName}`)}getRowDragHint(){return this.querySelector(`.${q.RowDragHintClassName}`)}getDropRestrictions(){return this.querySelector(J)}getScrollViewer(){return this.querySelector(`.${f.ClassName}`)}getScrollViewerContent(){return this.querySelector(`.${f.ContentContainerClassName}`)}getTableContainerWidth(){const e=this.getScrollViewerContent();return e?null==e?void 0:e.getBoundingClientRect().width:0}getColumnResizeSeparator(){return this.querySelector(`.${q.ColumnsSeparatorClassName}`)}getDropTargetIndicator(){return this.querySelector(`.${q.DropTargetIndicatorClassName}`)}createRootKeyboardNavigationStrategy(){return new Xe(this)}initializeKeyboardNavigator(){if(this.keyboardNavigator=this.querySelector(fe.KeyboardNavigatorSelector),this.keyboardNavigator&&!this.keyboardNavigator.initialized){let e=null;const t=this.getScrollViewer();t&&M.isVirtualScrollingEnabled(t)&&(e=x),this.keyboardNavigator.initialize(this,this.createRootKeyboardNavigationStrategy(),e)}}getKeyboardNavigator(){return this.keyboardNavigator}getColElementOffsetWidth(e){let t=this.getColElements()[e].getBoundingClientRect().width;if(0===t&&(h.Browser.Safari||h.Browser.MacOSMobilePlatform||h.Browser.WebKitFamily||h.Browser.AndroidDefaultBrowser)){const n=this.findHeaderCellByColumnIndex(e);t=n?n.getBoundingClientRect().width:null}return t}addEventSubscriptions(){var e;this.addEventListener(o.eventName,this.boundOnDragStartHandler),this.addEventListener(s.eventName,this.boundOnDragStopHandler),this.addEventListener(r.eventName,this.boundOnDragCancelHandler),this.addEventListener(l.eventName,this.boundOnClickHandler),this.addEventListener(a.eventName,this.boundOnDblClickHandler),this.addEventListener(I.eventName,this.boundOnScrollViewerUpdateHandler),this.addEventListener(Ue.eventName,this.boundOnStartCellEditingRequestCompleteHandler),null===(e=this.getScrollViewer())||void 0===e||e.subscribeToScroll(this.boundOnScrollViewerScrollHandler),document.addEventListener("keydown",this.boundOnDocumentKeyDownHandler),h.Browser.Firefox&&this.addEventListener("mousedown",this.boundOnMouseDownHandler)}removeEventSubscriptions(){var e;this.removeEventListener(o.eventName,this.boundOnDragStartHandler),this.removeEventListener(s.eventName,this.boundOnDragStopHandler),this.removeEventListener(r.eventName,this.boundOnDragCancelHandler),this.removeEventListener(l.eventName,this.boundOnClickHandler),this.removeEventListener(a.eventName,this.boundOnDblClickHandler),this.removeEventListener(I.eventName,this.boundOnScrollViewerUpdateHandler),this.removeEventListener(Ue.eventName,this.boundOnStartCellEditingRequestCompleteHandler),null===(e=this.getScrollViewer())||void 0===e||e.unsubscribeFromScroll(),document.removeEventListener("keydown",this.boundOnDocumentKeyDownHandler),h.Browser.Firefox&&this.removeEventListener("mousedown",this.boundOnMouseDownHandler)}onDragStart(e){var t,n,i;const s=e.target,o=e.detail.source;if(this.isResizeAnchor(s)){const e=this.findHeaderElement(s),n=this.findLeafIndex(e);null===(t=this.columnResizeStrategy)||void 0===t||t.startDrag(n,o.pageX)}else if(this.isHeaderContentElement(s)){const e=this.findHeaderElement(s);e&&(this.columnDraggingHelper.start(e,o),null===(n=this.getScrollViewer())||void 0===n||n.startAutoScrolling(c.Horizontal,(e=>this.columnDraggingHelper.updateDraggableContextByEvent(e))))}else if(this.isRowDragAnchor(o.target)){const e=this.getRowVisibleIndexByTarget(o.target,this.enableInplaceEditing),t=this.getMainElement().querySelector(`tr[${Ie.VisibleIndexName}="${e}"]`);t&&this.rowDraggingHelper.start(t,o)}else this.selectionHelper.start(o.target,!o.ctrlKey&&!o.metaKey&&!e.detail.isLongTap),null===(i=this.getScrollViewer())||void 0===i||i.startAutoScrolling(c.Vertical,(e=>this.selectionHelper.updateSeletionByEvent(e)),(e=>{const t=this.getDataTable();if(t){const n=t.querySelector("thead");n&&(e.y+=n.offsetHeight,e.height-=n.offsetHeight);const i=t.querySelector("tfoot");i&&(e.height-=i.offsetHeight)}}));e.stopPropagation()}onDragStop(e){var t,n;if(null===(t=this.columnResizeStrategy)||void 0===t?void 0:t.isResizing)this.columnResizeStrategy.stopDrag();else if(this.columnDraggingHelper.isStarted()){const e=this.columnDraggingHelper.createColumnDraggedHandlerArgs();this.columnDraggingHelper.stop(),this.onColumnDragged(e)}else if(this.rowDraggingHelper.isStarted()){const e=this.rowDraggingHelper.createItemDraggedHandlerArgs();this.rowDraggingHelper.stop(),this.onRowDragged(e)}else if(this.selectionHelper.isStarted()){if(this.uiHandlersBridge&&this.selectionHelper.startSelectionRowIndex>-1&&this.selectionHelper.endSelectionRowIndex>-1){this.uiHandlersBridge.send("rowDragged",{startVisibleIndex:this.selectionHelper.startSelectionRowIndex,endVisibleIndex:this.selectionHelper.endSelectionRowIndex,cleanSelection:!e.detail.source.ctrlKey&&!e.detail.source.metaKey&&!e.detail.isLongTap})}this.selectionHelper.stop()}null===(n=this.getScrollViewer())||void 0===n||n.stopAutoScrolling(),e.stopPropagation()}onDragCancel(e){var t,n;(null===(t=this.columnResizeStrategy)||void 0===t?void 0:t.isResizing)?this.columnResizeStrategy.cancelDrag():this.columnDraggingHelper.isStarted()?this.columnDraggingHelper.stop():this.rowDraggingHelper.isStarted()?this.rowDraggingHelper.stop():this.selectionHelper.isStarted()&&this.selectionHelper.stop(),null===(n=this.getScrollViewer())||void 0===n||n.stopAutoScrolling(),e.stopPropagation()}allowInplaceEditingOnCellElementClick(e){return!0}onClick(e){const t=e.detail.source.target,n=0===e.detail.source.button;if(this.isResizeAnchor(e.target))e.detail.source.stopPropagation();else if(n&&this.enableInplaceEditing){const e=this.getCellInfo(t);null!==e&&this.allowInplaceEditingOnCellElementClick(t)&&this.requestStartCellEditing(e)}}requestStartCellEditing(e){if(!this.keyboardNavigator)return;if(null===e||e.type===ce.Edit)return;e.type===ce.Data&&this.setFocusedRowVisible(!1);const t=new Me(e);setTimeout((()=>this.dispatchEvent(new ke(t))),0)}onStartCellEditingRequestComplete(){this.setFocusedRowVisible(!0)}onDblClick(e){var t;const n=e.target;if(this.isResizeAnchor(n)){e.stopPropagation();const i=this.findHeaderElement(n),s=this.findLeafIndex(i);null===(t=this.columnResizeStrategy)||void 0===t||t.autoFitColumnWidth(s)}}onScrollViewerUpdate(e){const t=this.getScrollViewerContent(),n=this.getDataTable();n&&(t&&(n.offsetHeight<t.clientHeight?G.DomUtils.addClassName(n,q.DataTableNoScrollClassName):n.offsetHeight>t.clientHeight&&G.DomUtils.removeClassName(n,q.DataTableNoScrollClassName)),this.updateTopFixedBodyPosition(n)),e.stopPropagation()}onScrollViewerScroll(){this.styleGenerator.scrollPositionUpdated(this.getScrollViewerContent())}getRowVisibleIndexByTarget(e,t){let n=e;for(;n&&n!==this&&(t||!n.classList.contains(q.EditRowClassName));){if("tr"===n.tagName.toLowerCase()&&n.dataset.visibleIndex)return parseInt(n.dataset.visibleIndex);n=n.parentElement}return-1}get allowSelectRowByClick(){return this._allowSelectRowByClick}set allowSelectRowByClick(e){this._allowSelectRowByClick=e,this.updateHandlePointerEventsMode()}get enableMultiSelect(){return this._enableMultiSelect}set enableMultiSelect(e){this._enableMultiSelect=e,this.updateHandlePointerEventsMode()}get enableFocusedRow(){return this._enableFocusedRow}set enableFocusedRow(e){this._enableFocusedRow=e,this.updateHandlePointerEventsMode()}get enableRowDrag(){return this._enableRowDrag}set enableRowDrag(e){this._enableRowDrag=e,this.updateHandlePointerEventsMode()}get enableItemDrop(){return this._enableItemDrop}set enableItemDrop(e){this._enableItemDrop=e,this.onItemDropStateChanged()}get enablePositionalItemDrop(){return this._enablePositionalItemDrop}get enableInplaceEditing(){return this._enableInplaceEditing}set enableInplaceEditing(e){this._enableInplaceEditing=e,this.updateHandlePointerEventsMode()}get isEditing(){return this._isEditing}set isEditing(e){this._isEditing=e}get focusedRowIndex(){return this._focusedRowIndex}set focusedRowIndex(e){this._focusedRowIndex=e}get keyboardNavigationEnabled(){return!!this.keyboardNavigator}get hasRowClickEvent(){return this._hasRowClickEvent}set hasRowClickEvent(e){this._hasRowClickEvent=e,this.updateHandlePointerEventsMode()}get hasRowDblClickEvent(){return this._hasRowDblClickEvent}set hasRowDblClickEvent(e){this._hasRowDblClickEvent=e,this.updateHandlePointerEventsMode()}get columnsInfo(){return this._columnsInfo}get headerLayout(){return this._headerLayout}get styleGenerator(){return this._styleGenerator}get editorManager(){return this._editorManager}get uiHandlersBridge(){return this._uiHandlersBridge}get uId(){return this.getAttribute(Ct)}updateHandlePointerEventsMode(){this.handlePointerEventsMode=n.None,(this.hasRowClickEvent||this.enableFocusedRow||this.enableInplaceEditing)&&(this.handlePointerEventsMode|=n.Click),this.hasRowDblClickEvent&&(this.handlePointerEventsMode|=n.DblClick),this.enableRowDrag&&(this.handlePointerEventsMode|=n.Dragging),this.allowSelectRowByClick&&(this.handlePointerEventsMode|=n.Click,this.enableMultiSelect&&h.Browser.TouchUI&&(this.handlePointerEventsMode|=n.Dragging))}makeRowVisible(e){if(this.uiHandlersBridge&&e>=0){this.uiHandlersBridge.send("makeRowVisible",{visibleIndex:e})}}notifyFocusedRowChanged(e){if(e!==this.focusedRowIndex&&this.enableFocusedRow&&this.uiHandlersBridge){this.focusedRowIndex=e;this.uiHandlersBridge.send("focusedRowChanged",{visibleIndex:e})}}notifyColumnsInitialized(e,t){this._columnsInfo=e,this._headerLayout=t}notifyColumnsChanged(e,t){this._columnsInfo=e,this._headerLayout=t,this.correctWidths(Z.ColumnsChanged)}notifyColumnResizeAnchorFocus(e){var t;const n=this.findHeaderElement(e),i=this.findLeafIndex(n);null===(t=this.columnResizeStrategy)||void 0===t||t.anchorFocused(i)}notifyColumnResizeAnchorBlur(){var e;null===(e=this.columnResizeStrategy)||void 0===e||e.anchorUnfocused()}notifyColumnResizeAnchorMove(e){var t;null===(t=this.columnResizeStrategy)||void 0===t||t.performMoveStep(e)}updateEmptyColumnVisibility(){this.columnWidthsController.updateEmptyColumnVisibility()}autoFitColumnWidthsLegacy(e=!1){this.columnWidthsController.autoFitColumnWidthsLegacy(e)}autoFitColumnWidths(){this.columnWidthsController.autoFitColumnWidths()}updateTopFixedBodyPosition(e){const t=e.querySelector(":scope > thead"),n=e.querySelector(`:scope > tbody.${q.TopFixedBodyClassName}`);t&&n&&(n.style.top=window.getComputedStyle(t).height)}correctWidths(e){var t;if(null===(t=this.columnResizeStrategy)||void 0===t?void 0:t.isResizing)return;this.columnWidthsController.correctWidths(e);const n=this.getScrollViewer();n&&n.refreshUI&&n.refreshUI()}getElementIndex(e){if(!e)return-1;if(e instanceof HTMLTableCellElement)return e.cellIndex;if(e&&e.parentElement)for(let t=0;t<e.parentElement.children.length;t++)if(e.parentElement.children[t]===e)return t;return-1}getColumnInfo(e){const t=this.findHeaderElement(e),n=this.getElementIndex(t);if(n>=0){const e=Array.from(this.getHeaderRows()).findIndex((e=>e===t.parentElement));let i=this.columnsInfo;if(e>=0&&(i=this.headerLayout[e]),n<i.length)return i[n]}return null}getCellInfo(e){return ye.fromElement(this,e)}getCellElement(e){return ye.getCellElement(this,e)}getColumnInfoByCell(e){if(e instanceof HTMLElement){const t=this.getElementIndex(e);if(t>=0&&t<this.columnsInfo.length)return this.columnsInfo[t]}return null}getColumnUID(e){const t=this.getColumnInfo(e);return t?t.uID:-1}findHeaderElement(e){const t=e;return t?this.isHeaderElement(t)?t:t.closest(`.${q.HeaderElementClassName}`):null}findHeaderCellByColumnIndex(e){const t=this.columnsInfo[e];return this.getHeaderCell(t.headerRowIndex,t.headerCellIndex)}getHeaderCell(e,t){var n,i;return null!==(i=null===(n=this.getHeaderRow(e))||void 0===n?void 0:n.cells[t])&&void 0!==i?i:null}findLeafIndex(e){let t=this.getColumnInfo(e);return(null==t?void 0:t.hasLeafs)&&(t=t.getRightLeaf()),t?t.leafIndex:-1}getHeaderRow(e){return this.getHeaderRows().item(e)}isHeaderElement(e){return e&&e.classList.contains(q.HeaderElementClassName)}isHeaderContentElement(e){return e.tagName.toLowerCase()===j}isResizeAnchor(e){return e.classList.contains(q.ColumnsResizeAnchorClassName)}isRowDragAnchor(e){return e.classList.contains(q.RowDragAnchorClassName)}onItemDropStateChanged(){this.rowDraggingHelper.initItemDropTarget(this.enableItemDrop)}onDocumentKeyDown(e){if(!this.keyboardNavigationEnabled&&("Enter"===e.key||" "===e.key)){const t=this.findHeaderElement(e.target);if(t){const n=new MouseEvent("click",{bubbles:!0,cancelable:!0,ctrlKey:e.ctrlKey,metaKey:e.metaKey,shiftKey:e.shiftKey});t.dispatchEvent(n)}}}onMouseDown(e){e.ctrlKey&&e.preventDefault()}onColumnChooserItemDragged(e,t){this.onColumnDragged({columnUid:e,beforeColumnUid:t,srcArea:it.ColumnChooser,dropArea:it.ColumnChooser})}onRowDragged(e){e&&this.uiHandlersBridge&&this.uiHandlersBridge.send("rowDropped",e)}onColumnDragged(e){e&&this.uiHandlersBridge&&this.uiHandlersBridge.send("columnHeaderDragged",e)}onColumnWidthsChanged(e,t){this.uiHandlersBridge&&this.uiHandlersBridge.send("columnWidthsChanged",{columnUids:e,widths:t})}onSizeChanged(e){const t=e[0].contentRect.width;this.currentWidth!==t&&t>0&&(this.correctWidths(-1===this.currentWidth?Z.ColumnsChanged:Z.GridResize),this.currentWidth=t)}getContentTemplate(){return gt}static get observedAttributes(){return["allow-select-row-by-click","enable-multi-select","has-row-click-event","has-row-dbl-click-event","resize-mode","enable-focused-row","enable-inplace-editing","is-editing","request-auto-fit-column-widths","focus-pending-editor-column-uid","is-pending-editor-show","focused-row-index","enable-row-drag","enable-item-drop","enable-positional-item-drop","has-fixed-on-top-body"]}attributeChangedCallback(e,t,n){var i;switch(e){case"allow-select-row-by-click":this.allowSelectRowByClick=null!==n;break;case"enable-focused-row":this.enableFocusedRow=null!==n;break;case"enable-multi-select":this.enableMultiSelect=null!==n;break;case"enable-inplace-editing":this.enableInplaceEditing=null!=n;break;case"enable-row-drag":this.enableRowDrag=null!==n;break;case"enable-item-drop":this.enableItemDrop=null!==n;break;case"enable-positional-item-drop":this._enablePositionalItemDrop=null!==n;break;case"has-row-click-event":this.hasRowClickEvent=null!==n;break;case"has-row-dbl-click-event":this.hasRowDblClickEvent=null!==n;break;case"is-editing":this.isEditing=null!==n;break;case"focused-row-index":this.focusedRowIndex=parseInt(n);break;case"resize-mode":null===(i=this.columnResizeStrategy)||void 0===i||i.cancelDrag(),this.ensureColumnResizeStrategy();break;case"is-pending-editor-show":this.editorManager.notifyPendingEditorShow(null!==n);break;case"has-fixed-on-top-body":if(null!==n){const e=this.getDataTable();e&&setTimeout((()=>this.updateTopFixedBodyPosition(e)))}break;case"focus-pending-editor-column-uid":null!==n&&this.requestFocusEditor(parseInt(n));break;case"request-auto-fit-column-widths":if(n){const e=JSON.parse(n);if(e[0]&&e[0]!==this._latestAutoFitColumnWidthsRequestGuid)if(this._latestAutoFitColumnWidthsRequestGuid=e[0],2===e.length){const t=e[1];setTimeout((()=>{this.autoFitColumnWidthsLegacy(t)}))}else setTimeout((()=>{this.autoFitColumnWidths()}))}}}ensureColumnResizeStrategy(){const e=this.getAttribute("resize-mode");this.columnResizeStrategy=e?"next-column"===e?new se(this):new oe(this):void 0}requestFocusEditor(e){setTimeout((()=>this.focusEditor(e)),0)}focusEditor(e){const t=this.uId;if(null===t)return;const n=this.querySelector(fe.getBodyEditRowSelector(t,this.getTagName()));if(null===n)return;let i=null;for(const t of n.cells){const n=this.getCellInfo(t);if(null!==n&&n.columnInfo.uID===e&&n.type===ce.Edit){i=t;break}}if(null===i)return;const s=y.findFocusableElements(i),o=s.length>0?s[0]:null;o&&(o.focus(),Se.selectInputValue(o))}setFocusedRowVisible(e){e?(clearTimeout(this.focusedRowRestoreVisibilityTimerId),this.focusedRowRestoreVisibilityTimerId=void 0,this.hasAttribute(Ie.FocusedRowHidden)&&this.removeAttribute(Ie.FocusedRowHidden)):(this.enableFocusedRow&&this.setAttribute(Ie.FocusedRowHidden,""),clearTimeout(this.focusedRowRestoreVisibilityTimerId),this.focusedRowRestoreVisibilityTimerId=setTimeout((()=>this.setFocusedRowVisible(!0)),this.focusedRowRestoreVisibilityTimeout))}static findGrid(e){let t=document.querySelector(`${ct}[${Ct}="${e}"]`);return t||(t=document.querySelector(`${ut}[${Ct}="${e}"]`)),t}}customElements.define(j,class extends e{constructor(){super(),this.handlePointerEventsMode=n.Click|n.Dragging}get handlePointerEventsTarget(){return this.closest(`.${q.HeaderElementClassName}`)}get handlePointerEventsDelay(){return i}}),customElements.define("dxbl-grid-columns-info",class extends e{get data(){if(!this._dataCache){const e=this.getAttribute("data");this._dataCache=e?JSON.parse(e):{}}return this._dataCache}get header(){if(!this._headerCache){const e=this.data["header-uids"];this._headerCache=e?this.parseHeaderLayout(e):[]}return this._headerCache}get leafs(){if(!this._leafsCache){const e=this.data["leaf-uids"];this._leafsCache=e?this.parseColumns(e,!0):[],this.attachLeafs(this._leafsCache)}return this._leafsCache}get columns(){if(!this._columnsInfoCache){const e=this.data.columns;this._columnsInfoCache=e?this.parseColumnsInfo(e):{},this.attachParents(this._columnsInfoCache)}return this._columnsInfoCache}get indents(){if(!this._indentsCache){const e=this.data.indents;this._indentsCache=e?this.parseInfoArray(e):[]}return this._indentsCache}get emptyColumn(){if(!this._emptyColumnCache){const e=this.data["empty-column"];this._emptyColumnCache=e?this.parseColumnInfo(e):null}return this._emptyColumnCache}constructor(){super(),this._dataCache=null,this._columnsInfoCache=null,this._headerCache=null,this._leafsCache=null,this._indentsCache=null,this._emptyColumnCache=null,this._gridElementPromise=null}initializeComponent(){super.initializeComponent(),this.grid=this.closest(`.${q.ClassName}`),this._gridElementPromise=customElements.whenDefined(this.grid.tagName.toLowerCase()),this._gridElementPromise.then(this.notifyColumnsInitialized.bind(this))}invalidateCaches(){this._dataCache=null,this._columnsInfoCache=null,this._headerCache=null,this._leafsCache=null,this._indentsCache=null,this._emptyColumnCache=null}parseHeaderLayout(e){const t=[];for(let n=0;n<e.length;n++){const i=this.parseColumns(e[n],0===n);this.updateHeaderLayoutInfo(i,n),t.push(i)}return t}updateHeaderLayoutInfo(e,t){for(let n=0;n<e.length;n++)e[n].updateHeaderLayoutInfo(t,n)}parseColumns(e,t){const n=t?this.indents.slice():[];for(let t=0;t<e.length;t++){n.push(this.columns[e[t]])}return t&&this.emptyColumn&&n.push(this.emptyColumn),n}parseColumnsInfo(e){const t={};for(let n=0;n<e.length;n++){const i=e[n];t[i.UID]=this.parseColumnInfo(i)}return t}parseInfoArray(e){const t=[];for(let n=0;n<e.length;n++){t.push(this.parseColumnInfo(e[n]))}return t}parseColumnInfo(e){const t=e.DragRestrictions?new Y(e.DragRestrictions.PreviousColumnUid,e.DragRestrictions.NextColumnUid,e.DragRestrictions.NextVisibleColumnUid):null;return new X(e.UID,e.ParentUID,e.AllowGroup,e.Width,e.MinWidth,e.FixedPosition,e.IsIndent,e.IsEmpty,e.IsDetail,e.IsEditable,e.AllowReorder,t)}attachParents(e){for(const t of Object.values(e)){const n=e[t.parentUID];n&&t.attachParent(n)}}attachLeafs(e){for(let t=0;t<e.length;t++){const n=e[t];n.isIndent||n.isRoot||this.attachLeafRecursive(n.parentUID,n),n.updateLeafIndex(t)}}attachLeafRecursive(e,t){const n=e>=0?this.columns[e]:null;n&&(n.attachLeaf(t),this.attachLeafRecursive(n.parentUID,t))}notifyColumnsInitialized(){var e;null===(e=this.grid)||void 0===e||e.notifyColumnsInitialized(this.leafs,this.header)}notifyColumnsChanged(){var e;null===(e=this.grid)||void 0===e||e.notifyColumnsChanged(this.leafs,this.header)}static get observedAttributes(){return["data"]}attributeChangedCallback(e,t,n){var i;this.invalidateCaches(),null===(i=this._gridElementPromise)||void 0===i||i.then(this.notifyColumnsChanged.bind(this))}}),customElements.define(J,class extends e{constructor(){super(...arguments),this._dataCache=null,this._allowedComponentsCache=null,this._reorderDeniedRangesCache=null}get allowedComponents(){var e;return this._allowedComponentsCache||(this._allowedComponentsCache=null!==(e=this.data["allowed-components"])&&void 0!==e?e:[]),this._allowedComponentsCache}get reorderDeniedRanges(){var e;return this._reorderDeniedRangesCache||(this._reorderDeniedRangesCache=null!==(e=this.data["reorder-denied-ranges"])&&void 0!==e?e:[]),this._reorderDeniedRangesCache}get data(){if(!this._dataCache){const e=this.getAttribute(Q);this._dataCache=e?JSON.parse(e):{}}return this._dataCache}invalidateCaches(){this._dataCache=null,this._allowedComponentsCache=null,this._reorderDeniedRangesCache=null}static get observedAttributes(){return[Q]}attributeChangedCallback(e,t,n){this.invalidateCaches()}}),customElements.define("dxbl-column-resize-anchor",class extends e{constructor(){super(),this.boundOnAnchorFocusHandler=this.onAnchorFocus.bind(this),this.boundOnAnchorBlurHandler=this.onAnchorBlur.bind(this),this.boundOnKeyDownHandler=this.onKeyDown.bind(this),this.handlePointerEventsMode=n.Click|n.DblClick|n.Dragging}get handlePointerEventsDelay(){return i}initializeComponent(){var e,n,i;super.initializeComponent(),h.Browser.TouchUI&&this.classList.add(t.Touch),this.grid=this.closest(`.${q.ClassName}`),this.focusableElement=this.querySelector("div"),null===(e=this.focusableElement)||void 0===e||e.addEventListener("focus",this.boundOnAnchorFocusHandler),null===(n=this.focusableElement)||void 0===n||n.addEventListener("blur",this.boundOnAnchorBlurHandler),null===(i=this.focusableElement)||void 0===i||i.addEventListener("keydown",this.boundOnKeyDownHandler)}disposeComponent(){var e,t,n;super.disposeComponent(),null===(e=this.focusableElement)||void 0===e||e.removeEventListener("focus",this.boundOnAnchorFocusHandler),null===(t=this.focusableElement)||void 0===t||t.removeEventListener("blur",this.boundOnAnchorBlurHandler),null===(n=this.focusableElement)||void 0===n||n.removeEventListener("keydown",this.boundOnKeyDownHandler)}onAnchorFocus(e){var t;null===(t=this.grid)||void 0===t||t.notifyColumnResizeAnchorFocus(e.target)}onAnchorBlur(){var e;null===(e=this.grid)||void 0===e||e.notifyColumnResizeAnchorBlur()}onKeyDown(e){var t,n;e.keyCode===u.KeyCode.Left&&(e.preventDefault(),null===(t=this.grid)||void 0===t||t.notifyColumnResizeAnchorMove(te.Left)),e.keyCode===u.KeyCode.Right&&(e.preventDefault(),null===(n=this.grid)||void 0===n||n.notifyColumnResizeAnchorMove(te.Right))}}),customElements.define("dxbl-column-resize-separator",re),customElements.define("dxbl-grid-column-chooser",class extends e{constructor(){super(),this.boundOnDragStartHandler=this.onDragStart.bind(this),this.boundOnDragStopHandler=this.onDragStop.bind(this),this.boundOnDragCancelHandler=this.onDragCancel.bind(this),this.grid=null,this.scrollViewer=null,this.itemsReorderHelper=new pe(this.onItemsReording.bind(this)),this.handlePointerEventsMode=n.Dragging}get handlePointerEventsDelay(){return i}get useShadowDom(){return!1}initializeComponent(){super.initializeComponent(),this.addEventSubscriptions(),this.grid=wt.findGrid(this.getAttribute(mt)),this.scrollViewer=this.querySelector(`.${f.ClassName}`),this.initializeKeyboardNavigator()}disposeComponent(){super.disposeComponent(),this.removeEventSubscriptions(),delete this.keyboardNavigator}getColumnUid(e){return parseInt(e.getAttribute(le))}getColumnParentUid(e){return e.getAttribute(ae)}addEventSubscriptions(){this.addEventListener(o.eventName,this.boundOnDragStartHandler),this.addEventListener(s.eventName,this.boundOnDragStopHandler),this.addEventListener(r.eventName,this.boundOnDragCancelHandler)}removeEventSubscriptions(){this.removeEventListener(o.eventName,this.boundOnDragStartHandler),this.removeEventListener(s.eventName,this.boundOnDragStopHandler),this.removeEventListener(r.eventName,this.boundOnDragCancelHandler)}initializeKeyboardNavigator(){this.keyboardNavigator=this.querySelector(w),this.keyboardNavigator&&!this.keyboardNavigator.initialized&&this.keyboardNavigator.initialize(this,new et(this,this.keyboardNavigator))}onDragStart(e){var t;const n=e.detail.source,i=n.target,s=i.closest(`.${q.ColumnChooserItemDragAnchorClassName}`),o=i.closest(`.${q.ColumnChooserItemClassName}`);if(null!=s&&null!=o){const e=Array.from(this.getElementsByClassName(q.ColumnChooserItemClassName));this.itemsReorderHelper.start(e,o,n),null===(t=this.scrollViewer)||void 0===t||t.startAutoScrolling(c.Vertical,(()=>{this.itemsReorderHelper.refreshUI()}))}e.stopPropagation()}onDragStop(e){var t;this.itemsReorderHelper.stop(),null===(t=this.scrollViewer)||void 0===t||t.stopAutoScrolling(),e.stopPropagation()}onDragCancel(e){var t;this.itemsReorderHelper.cancel(),null===(t=this.scrollViewer)||void 0===t||t.stopAutoScrolling(),e.stopPropagation()}onItemsReording(e,t,n,i){const s=n.map((t=>e[t])),o=s[0],r=t.indexOf(n[0])+n.length;let l=null;if(!(r===t.length)){l=e[t[r]];this.getColumnParentUid(l)!==this.getColumnParentUid(o)&&(l=null)}this.applyStyles(e,s,l,i),i===de.Stop&&this.onItemsReorded(o,l)}applyStyles(e,t,n,i){for(let t=0;t<e.length;t++){const s=e[t],o=s.classList.contains(q.ColumnChooserDraggingItemNextSiblingClassName);(i!==de.ItemMoving||o&&s!==n)&&s.classList.remove(q.ColumnChooserDraggingItemNextSiblingClassName)}if(i===de.ItemMoving){for(const e of t)e.classList.contains(q.ColumnChooserDraggingItemClassName)||e.classList.add(q.ColumnChooserDraggingItemClassName);null==n||n.classList.contains(q.ColumnChooserDraggingItemNextSiblingClassName)||n.classList.add(q.ColumnChooserDraggingItemNextSiblingClassName)}else{for(const e of t)e.classList.remove(q.ColumnChooserDraggingItemClassName);null==n||n.classList.remove(q.ColumnChooserDraggingItemNextSiblingClassName)}}onItemsReorded(e,t){if(null==this.grid)return;const n=this.getColumnUid(e),i=null!=t?this.getColumnUid(t):null;setTimeout((()=>{var e;return null===(e=this.grid)||void 0===e?void 0:e.onColumnChooserItemDragged(n,i)}),200)}}),customElements.define("dxbl-drop-target-indicator",rt);class It extends T{constructor(e){super(),this.grid=e,this.boundOnDroppableMouseMoveHandler=this.onDroppableAreaMove.bind(this)}get draggableRowContext(){return this.draggableItemContext}getKey(){return this.grid.uId}getItemDropArea(){return this.grid.getDataTable()}initItemDropTarget(e){e?H.register(this):H.remove(this.getKey())}start(e,t){this.isRowDraggingAllowed(e)&&(super.start(e,t),this.toggleCursorClass(!0))}stop(){var e;this.toggleCursorClass(!1),null===(e=this.getTargetIndicator())||void 0===e||e.hide(),super.stop()}activateDropTarget(e){var t;super.activateDropTarget(e),null===(t=this.grid.getScrollViewer())||void 0===t||t.startAutoScrolling(c.Vertical)}deactivateDropTarget(){var e,t;super.deactivateDropTarget(),null===(e=this.getTargetIndicator())||void 0===e||e.hide(),null===(t=this.grid.getScrollViewer())||void 0===t||t.stopAutoScrolling()}addElementListeners(e){super.addElementListeners(e),e.addEventListener("pointermove",this.boundOnDroppableMouseMoveHandler)}removeElementListeners(e){super.removeElementListeners(e),e.removeEventListener("pointermove",this.boundOnDroppableMouseMoveHandler)}createSourceInfo(){return{...super.createSourceInfo(),visibleIndex:this.draggableRowContext.visibleIndex}}createTargetInfo(){return{...super.createTargetInfo(),visibleIndex:this.getRowVisibleIndex(this.draggableRowContext.mouseOverElement),dropPosition:this.getDropPosition()}}createDraggableContext(e,t,n){return{...super.createDraggableContext(e,t,n),visibleIndex:this.getRowVisibleIndex(e),restrictions:this.grid.getDropRestrictions()}}updateDraggableContext(e){super.updateDraggableContext(e),this.updateIndicator()}updateDraggableElementPosition(){if(super.updateDraggableElementPosition(),!this.draggableContext)return;const e=this.draggableContext;e.draggableElementPosition={x:e.draggableElementPosition.x-e.draggableElementOffset.x,y:e.draggableElementPosition.y-e.draggableElementOffset.y}}refreshUICore(){var e;super.refreshUICore();const t=this.getTargetIndicator();this.isStarted()&&(null===(e=this.draggableRowContext)||void 0===e?void 0:e.isPossibleDropItem)&&this.draggableRowContext.target===this?t.show():null==t||t.hide()}getDraggableElement(e){return this.grid.getRowDragHint()}getDroppableTargets(){const{restrictions:e}=this.draggableRowContext||{};if(!e)return[];const t=this.getItemDropArea();return e.allowedComponents.map((e=>H.find(e))).filter((e=>{if(!e)return!1;const n=e.getItemDropArea();return!T.isDescendantElement(n,t)&&!T.isDescendantElement(t,n)}))}isItemDropPossible(e){return!(!this.draggableRowContext||e===this.draggableRowContext.srcElement)&&(this.isWithinDragging()?this.isReorderPossible(e):this.isDropPossible(e))}isDropPossible(e){const t=be.getRowType(e);return t===ue.Data||t===ue.Group||t===ue.EmptyData||t===ue.EmptyRow}isReorderPossible(e){const t=be.getRowType(e);if(!this.draggableRowContext||t!==ue.Data)return!1;const n=this.getItemDropArea();if(!n||e.closest(n.tagName)!==n)return!1;const i=this.getRowVisibleIndex(e);return!this.isWithinDeniedRange(i)}isRestrictedTopArea(){if(!this.draggableRowContext)return!1;const{currentCursorPosition:e,mouseOverElementRect:t,mouseOverElement:n}=this.draggableRowContext;if(!n||!t)return!1;const i=this.getRowVisibleIndex(n),s=It.calculateVerticalCenter(t);return this.isWithinDragging()&&e.y>s&&this.isTopAdjacentDeniedRange(i)}isRestrictedBottomArea(){if(!this.draggableRowContext)return!1;const{currentCursorPosition:e,mouseOverElementRect:t,mouseOverElement:n}=this.draggableRowContext;if(!n||!t)return!1;const i=this.getRowVisibleIndex(n),s=It.calculateVerticalCenter(t);return this.isWithinDragging()&&e.y<s&&this.isBottomAdjacentDeniedRange(i)}isWithinDeniedRange(e){var t,n,i;return(null!==(i=null===(n=null===(t=this.draggableRowContext)||void 0===t?void 0:t.restrictions)||void 0===n?void 0:n.reorderDeniedRanges)&&void 0!==i?i:[]).some((t=>t.Start<=e&&t.End>=e))}isTopAdjacentDeniedRange(e){var t,n,i;return(null!==(i=null===(n=null===(t=this.draggableRowContext)||void 0===t?void 0:t.restrictions)||void 0===n?void 0:n.reorderDeniedRanges)&&void 0!==i?i:[]).some((t=>e===t.Start-1))}isBottomAdjacentDeniedRange(e){var t,n,i;return(null!==(i=null===(n=null===(t=this.draggableRowContext)||void 0===t?void 0:t.restrictions)||void 0===n?void 0:n.reorderDeniedRanges)&&void 0!==i?i:[]).some((t=>e===t.End+1))}isPositionalDropAllowed(){return this.grid.enablePositionalItemDrop}onDroppableAreaMove(e){const t=document.elementFromPoint(e.clientX,e.clientY),n=this.getItemDropArea(),i=P(t,null,(e=>e.matches(fe.TableBodyRows)&&e.closest(fe.DataTableSelector)===n));i&&(this.updateDraggableContext(i),this.refreshUI())}isRowDraggingAllowed(e){return be.getRowType(e)===ue.Edit?this.grid.enableInplaceEditing:be.getRowType(e)===ue.Data}toggleCursorClass(e){document.body.classList.toggle(t.GrabbingCursor,e)}getRowVisibleIndex(e){return this.grid.getRowVisibleIndexByTarget(e,this.grid.enableInplaceEditing)}getTargetIndicator(){return this.grid.getDropTargetIndicator()}updateIndicator(){if(!this.draggableRowContext)return;const e=this.getTargetIndicator(),t=this.getDropPosition();e&&this.draggableRowContext.mouseOverElement&&e.updatePosition(this.draggableRowContext.mouseOverElement,t)}static getDropPositionByCursor(e,t){return e>It.calculateVerticalCenter(t)?tt.After:tt.Before}static calculateVerticalCenter(e){return e.y+e.height/2}static isDataRow(e){return be.getRowType(e)===ue.Data}static isEmptyRow(e){const t=be.getRowType(e);return t===ue.EmptyData||t===ue.EmptyRow}}class ft extends It{getDropPosition(){if(!this.draggableRowContext)return tt.Append;const{currentCursorPosition:e,mouseOverElementRect:t,mouseOverElement:n}=this.draggableRowContext;return n&&t?It.isEmptyRow(n)||It.isDataRow(n)&&!this.isPositionalDropAllowed()?tt.Append:It.isDataRow(n)?this.isRestrictedTopArea()?tt.Before:this.isRestrictedBottomArea()?tt.After:It.getDropPositionByCursor(e.y,t):tt.Inside:tt.Inside}isReorderPossible(e){return this.isPositionalDropAllowed()&&super.isReorderPossible(e)}isWithinDeniedRange(e){return!(!this.isTopAdjacentDeniedRange(e)||!this.isBottomAdjacentDeniedRange(e))||super.isWithinDeniedRange(e)}}class wt extends pt{constructor(){super()}getTagName(){return ct}createRowDraggingHelper(){return new ft(this)}}customElements.define(ct,wt);const bt=Object.freeze({__proto__:null,DxGridTagName:ct,DxGridUidAttributeName:mt,DxGrid:wt,default:{loadModule:function(){}}});export{pt as D,fe as G,It as a,tt as b,Ve as c,Pe as d,ze as e,qe as f,je as g,Xe as h,ve as i,ut as j,bt as k};
