import{L as e}from"./lit-element-base-9288748e.js";import{i as l,L as a}from"./logicaltreehelper-67db40f1.js";class i extends e{constructor(){super(),this._logicalParent=null,this._logicalChildren=new Array}get hasLogicalParent(){return!!this.logicalParent}get logicalParent(){return this._logicalParent}set logicalParent(e){this._logicalParent=e}get logicalChildren(){return this._logicalChildren}addLogicalChild(e){if(e.logicalParent)throw new Error("Child element already added.");this._logicalChildren.push(e),e.logicalParent=this}removeLogicalChild(e){e.logicalParent=null;const l=this._logicalChildren.indexOf(e);l>-1&&this._logicalChildren.splice(l,1)}async processCapturedKeyDownAsync(e,a){a.handled||this.logicalParent&&l(this.logicalParent)&&await this.logicalParent.processCapturedKeyDownAsync(e,a)}async processCapturedPointerAsync(e,i){if(i.handled)return;const t=a.findParent(this,(e=>l(e)));t&&l(t)&&await t.processCapturedPointerAsync(e,i)}isPointedCaptured(e){return null!=a.findParent(this,(e=>l(e)))}}export{i as D};
