import{i as e,g as t,U as s,F as o,t as i}from"./upload-base-993a2317.js";import"./tslib.es6-d65164b3.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./custom-events-helper-e7f279d3.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";import"./css-classes-c63af734.js";class l extends s{processUploadedFilesOptionsCore(e){var t;const s=e.filter((e=>e.status===o.PendingUpload&&e.uploadStartOptions));for(const e of s){const t=this.files.get(e.fileGuid);t&&(this.attachEventsToFileItem(t),t.loadStart())}const i=s.map((e=>e.fileGuid));i.length&&(null===(t=this.dotnetHelper)||void 0===t||t.invokeMethodAsync("UploadFileViaJsInterop",i))}}customElements.define("dxbl-file-input",l);const d={loadModule:function(){},initDotNetReference:e,getFileBytes:function(e,t){var s,o;return null===(o=null===(s=i(e))||void 0===s?void 0:s.getFileBytes)||void 0===o?void 0:o.call(s,t)},updateFileStatus:function(e,t,s,o){var l;null===(l=i(e))||void 0===l||l.updateFileStatus(t,s,o)},getRecentlyAddedFileInfosStream:t};export{l as DxFileInput,d as default};
