import{_ as t}from"./tslib.es6-d65164b3.js";import{d as e,R as i,c as s}from"./dom-utils-d057dcaa.js";import{d as n}from"./dom-554d0cc7.js";import{E as o}from"./eventRegister-fb9b0e47.js";import{S as l}from"./single-slot-element-base-01d93921.js";import{n as a}from"./property-4ec0b52d.js";import{t as h}from"./state-c294470d.js";import{e as r}from"./custom-element-267f9a21.js";import"./browser-3fc721b7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./css-classes-c63af734.js";import"./common-48ec40e2.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";const d="is-animating",m="data-items-container";var p;!function(t){t.None="None",t.Slide="Slide"}(p||(p={}));let u=class extends l{constructor(){super(),this.isAnimating=!1,this.mutationObserver=null,this._expandedState="False",this._previousNodeCount=null,this._getNodeCount=t=>{var e;return t?this.childElementCount:(null===(e=this.firstElementChild)||void 0===e?void 0:e.childElementCount)||null},this.animationType=p.None,this.expanded=!1,this.events=new o(this),this.isRendered=!1,this._expandedState="False"}connectedCallback(){super.connectedCallback(),this.isRendered=!0,this.expanded||this.setElementVisibility(!1)}disconnectedCallback(){super.disconnectedCallback(),this.isRendered=!1}set expandedState(t){const e=this._expandedState;this._expandedState=t,this.expanded="True"===t,this.requestUpdate("expandedState",e),this.isRendered||"True"!==t?this.onExpandedChanged():this.notifyActualExpandedChanged()}get expandedState(){return this._expandedState}onExpandedChanged(){this.applyExpandedStateToElements()}notifyActualExpandedChanged(){this.dispatchEvent(new Event("change",{bubbles:!0}))}get value(){return this.expandedState}applyExpandedStateToElements(){var t,e,i,s;const n=this.expanded;if(this.animationType===p.None)return this.notifyActualExpandedChanged(),void this.setContainerElementVisibility(n);const o=null===(t=this.firstElementChild)||void 0===t?void 0:t.hasAttribute(m),l=this._getNodeCount(!o);if(n){if(0===this.childElementCount)return void this.waitForContent(this);if((null===(e=this.firstElementChild)||void 0===e?void 0:e.hasAttribute(m))&&!l)return void this.waitForContent(this.firstElementChild)}if(n&&this.firstElementChild&&(this._previousNodeCount===l||!o)&&(null===(i=this.mutationObserver)||void 0===i||i.disconnect(),o&&!l))return;this._previousNodeCount=this._getNodeCount(!o);const a=this.token,h=n&&!a?0:this.getContainerStartHeight(n);this.toggleAttribute(d,!1),a&&(this.style.maxHeight=h+"px");const r=this.getContainerEndHeight(n);if(null===(s=this.token)||void 0===s||s.dispose(),this.token=null,this.prepareElementsForAnimation(n,0,!!a),h!==r){const t=()=>{this.onAnimationComplete(),this.token.dispose(),this.token=null};this.startAnimation(h,r,(()=>{t()}))}else this.onAnimationComplete()}waitForContent(t){var e;null===(e=this.mutationObserver)||void 0===e||e.disconnect(),this.mutationObserver=new MutationObserver((t=>{this.applyExpandedStateToElements()})),this.mutationObserver.observe(t,{childList:!0})}setContainerElementVisibility(t){this.setElementVisibility(t)}setElementVisibility(t){t?(this.style.display="",this.style.height=""):(this.style.display="none",this.style.height="0px")}getContainerStartHeight(t){return t?this.getClearClientHeight():this.offsetHeight}getContainerEndHeight(t){if(!t)return 0;const e=this.getClearClientHeight(),i=this.getElementVisibility(!1);this.style.maxHeight="",this.setElementVisibility(!0);const s=this.getClearClientHeight();return this.setElementVisibility(i),this.style.maxHeight=e+"px",s}prepareElementsForAnimation(t,e,i){if(this.style.overflow="hidden",t)this.setContainerElementVisibility(!0),this.style.maxHeight=(i?0:this.getClearClientHeight())+"px";else{const t=this.offsetHeight+e;t>=0&&this.setOffsetHeight(t,null)}}getClearClientHeight(){return this.offsetHeight}getElementVisibility(t){if(t){const t=n.DomUtils.getCurrentStyle(this);if(t)return"none"!==t.display}return"none"!==this.style.display}setOffsetHeight(t,i){i||(i=n.DomUtils.getCurrentStyle(this));let s=t-n.DomUtils.pxToInt(i.marginTop)-n.DomUtils.pxToInt(i.marginBottom);s-=e(this,i),s>-1&&(this.style.maxHeight=s+"px")}startAnimation(t,e,s){this.isAnimating=!0,t!==e?(this.token=this.events.attachEvent(this,"transitionend",s),i((()=>{this.isAnimating&&(this.toggleAttribute(d,!0),setTimeout((()=>this.style.maxHeight=e+"px")))}))):s()}onAnimationComplete(){this.toggleAttribute(d,!1),this.setContainerElementVisibility(this.expanded),s((()=>{this.style.overflow="",this.style.maxHeight=""})),this.notifyActualExpandedChanged(),this.checkChildContent()}checkChildContent(){if(!this.expanded&&0!==this.childElementCount&&null!==this.firstElementChild&&this.firstElementChild.hasAttribute(m)){const t=this.querySelectorAll(" ul[data-items-container]");if(!t)return;for(let e=0;e<t.length;e+=1)t[e].style.overflowY="hidden"}}};t([a({type:String,attribute:"expanded-state"})],u.prototype,"expandedState",null),t([a({type:p,attribute:"animation-type"})],u.prototype,"animationType",void 0),t([h()],u.prototype,"expanded",void 0),u=t([r("dxbl-expandable-container")],u);const c={loadModule:function(){}};export{p as AnimationType,u as ExpandableContainer,c as default};
