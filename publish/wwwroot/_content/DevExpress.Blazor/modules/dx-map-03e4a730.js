import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t}from"./devextreme-widget-wrapper-33881f73.js";import{C as s}from"./custom-events-helper-e7f279d3.js";import{e as i}from"./custom-element-267f9a21.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./utils-b5b2c8a9.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";import"./events-a8fe5872.js";const o="dxbl-map";class r extends CustomEvent{constructor(e,t){super(r.eventName,{detail:new n(e,t),bubbles:!0,composed:!0,cancelable:!0})}}r.eventName=o+".click";class a extends CustomEvent{constructor(e){super(a.eventName,{detail:new c(e),bubbles:!0,composed:!0,cancelable:!0})}}a.eventName=o+".marker-click";class n{constructor(e,t){this.Latitude=e,this.Longitude=t}}class c{constructor(e){this.Key=e}}var l,d;s.register(r.eventName,(e=>e.detail)),s.register(a.eventName,(e=>e.detail)),null!==(l=(d=window).sj_evt)&&void 0!==l||(d.sj_evt={fire:()=>{},bind:()=>{}});const p="markerId";let m=class extends t{createWidgetHandlers(){return{...super.createWidgetHandlers(),onClick:e=>this.onMapClick(e),onReady:e=>this.onMapReady(e),onOptionChanged:e=>this.onMapOptionChanged(e)}}getWidgetModuleName(){return"ui"}getWidgetTypeName(){return"dxMap"}disposeWidget(e){var t;null===(t=null==e?void 0:e._lastAsyncAction)||void 0===t||t.catch((e=>{})),super.disposeWidget(e)}createWidgetDefaultOptions(){return{height:"100%",width:"100%"}}processSpecialOptions(e){super.processSpecialOptions(e),this.preapreMarkerClickHandlers(e.markers)}onMapClick(e){this.dispatchEvent(new r(e.location.lat,e.location.lng))}onMapReady(e){this.changeLoadingPanelVisibility(!1)}onMapOptionChanged(e){this.changeLoadingPanelVisibility(!1)}preapreMarkerClickHandlers(e){null==e||e.forEach((e=>{if(e[p]){const t=e[p];e.onClick=()=>{this.onMarkerClick(t)},delete e[p]}}))}onMarkerClick(e){this.dispatchEvent(new a(e))}};m=e([i("dxbl-map")],m);export{m as DxMap};
