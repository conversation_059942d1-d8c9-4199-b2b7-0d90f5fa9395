import{_ as e}from"./tslib.es6-d65164b3.js";import t from"./adaptivedropdowncomponents-7cb91d74.js";import{a as o,E as s,d as n,D as i}from"./dx-dropdown-base3-726be7be.js";import{r}from"./constants-da6cacac.js";import{F as a,D as l}from"./keyboard-navigation-strategy-ea41c807.js";import{k as m}from"./key-ffa272aa.js";import{E as p}from"./eventhelper-8bcec49f.js";import{d}from"./dom-554d0cc7.js";import{e as c}from"./custom-element-267f9a21.js";import"./dropdowncomponents-3d8f06da.js";import"./dropdown-f5b2318c.js";import"./popup-355ecaa4.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./property-4ec0b52d.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./portal-b3727c25.js";import"./data-qa-utils-8be7c726.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./custom-events-helper-e7f279d3.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./browser-3fc721b7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./common-48ec40e2.js";import"./disposable-d2c2d283.js";import"./thumb-31d768d7.js";import"./query-44b9267f.js";import"./dom-utils-d057dcaa.js";import"./popupportal-bbd2fea0.js";import"./events-interseptor-a522582a.js";import"./modalcomponents-951e20e2.js";import"./masked-input-0c0a9541.js";import"./text-editor-733d5e56.js";import"./single-slot-element-base-01d93921.js";import"./input-66769c52.js";import"./constants-3209ffde.js";class u extends o{createPopupStrategy(e,t){return new h(e,t,this)}}class h extends s{constructor(e,t,o){super(e,t,o),this._focusableElements=[],this._timeoutId=null,this.boundWaitForNavigatorInitialization=this.waitForNavigatorInitialization.bind(this)}get editDropDownElement(){return this.targetElement.querySelector(`.${n.ClassName}`)}updateFocusableElementsList(){if(this.editDropDownElement){const e=a.findFocusableElements(this.editDropDownElement);this._focusableElements=e.length>0?e:[this.editDropDownElement]}}get focusableElements(){return this._focusableElements}handlePopupShown(){super.handlePopupShown(),this.addDropDownElementsEventSubscriptions();const e=this.editDropDownElement.querySelector(l);e&&!e.initialized?this.waitForNavigatorInitialization(e,this.handlePopupShownInternal.bind(this)):this.handlePopupShownInternal()}handlePopupClosed(){super.handlePopupClosed(),this.targetElement.removeEventListener("focus",this.boundDropDownElementFocusHandler),this.editDropDownElement&&this.editDropDownElement.removeEventListener("focus",this.boundEditDropDownElementFocusHandler)}addDropDownElementsEventSubscriptions(){this.boundDropDownElementFocusHandler||(this.boundDropDownElementFocusHandler=this.dropDownElementFocusHandler.bind(this)),this.targetElement.addEventListener("focus",this.boundDropDownElementFocusHandler),this.editDropDownElement&&(this.boundEditDropDownElementFocusHandler||(this.boundEditDropDownElementFocusHandler=this.editDropDownElementFocusHandler.bind(this)),this.editDropDownElement.addEventListener("focus",this.boundEditDropDownElementFocusHandler))}waitForNavigatorInitialization(e,t){this._timeoutId&&clearTimeout(this._timeoutId),document.activeElement===this.targetElement&&(e.initialized?t():this._timeoutId=setTimeout((()=>this.boundWaitForNavigatorInitialization(e,t)),50))}handlePopupShownInternal(){this.updateFocusableElementsList(),this.tryFocusFirstFocusableElement()}tryFocusFirstFocusableElement(){this.focusableElements.length>0&&a.focusElement(this.focusableElements[0])}tryFocusLastFocusableElement(){this.focusableElements.length>0&&a.focusElement(this.focusableElements[this.focusableElements.length-1])}handleKeyDown(e){return m.KeyUtils.getEventKeyCode(e)===m.KeyCode.Tab&&this.focusableElements.length<=1?(p.markHandled(e),!1):super.handleKeyDown(e)}onDropDownEditorPointerDownHandler(){d.DomUtils.isItParent(this.targetElement,document.activeElement)||super.onDropDownEditorPointerDownHandler()}dropDownElementFocusHandler(e){this.tryFocusFirstFocusableElement()}editDropDownElementFocusHandler(e){this.tryFocusLastFocusableElement()}}let b=class extends i{constructor(){super()}processPointerDown(e){const t=e.target===this.editBoxElement,o=this.editBoxTemplateElement&&this.editBoxTemplateElement.contains(e.target)&&!this.isFocusableElementInMainElementTemplate(e.target);return this.isDropDownOpen||!t&&!o||this.tryOpenDropDown(),super.processPointerDown(e)}createKeyboardStrategy(){return new u(this)}connectedCallback(){super.connectedCallback()}disconnectedCallback(){super.disconnectedCallback()}};b=e([c(r)],b);const E={loadModule:function(){},adaptiveDropdownComponents:t};export{b as DxDropdownBox,E as default};
