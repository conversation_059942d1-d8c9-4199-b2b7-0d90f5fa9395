import{k as e}from"./key-ffa272aa.js";import{P as t,S as s,b as n,M as r,c as u,d as o,e as a,T as i}from"./menu-keyboard-strategy-7c257fd3.js";import{removeFocusHiddenAttribute as c}from"./focus-utils-ae044224.js";import{F as l}from"./keyboard-navigation-strategy-ea41c807.js";class h extends t{constructor(e,t,n,r,u=!1){super(e,t,u),this._focusSemaphore=new s,this._menuItemSelector=n,this._parentStrategy=r}get parentStrategy(){return this._parentStrategy}get menuItemSelector(){return this._menuItemSelector}queryItems(){return this.queryItemsBySelector(this._menuItemSelector)}doAction(e){this.doClick(this.selectedItemElement,e),n(this.selectedItemElement)&&this.focusSubMenuItemAsync(r.First)}handleKeyDown(t){return this._focusSemaphore.doAllowedFunc((()=>e.KeyUtils.getEventKeyCode(t)===e.KeyCode.Right&&this.subMenuStrategy&&u(this.targetElement)?(this.subMenuStrategy.focusFirstItem(),!0):super.handleKeyDown(t)))}openSubMenu(e){this.openSubMenuAsync(e)}async openSubMenuAsync(e){this.requestSubMenuOpen(),await this.focusSubMenuItemAsync(e)}async focusSubMenuItemAsync(e){const t=await this.waitSubMenuShown();c(t.targetElement),e===r.Last?t.focusLastItem():t.focusFirstItem()}closeSubMenu(){this.selectedItemElement.dispatchEvent(new o(a.Collapse))}handlePopupShown(){this._parentStrategy.onSubMenuShown(this)}handlePopupClosed(){this._parentStrategy.onSubMenuClosed(this)}focusFirstItem(){this.subMenuStrategy?this.subMenuStrategy.focusFirstItem():super.focusFirstItem()}focusLastItem(){this.subMenuStrategy?this.subMenuStrategy.focusLastItem():super.focusLastItem()}focusItem(e){this._focusSemaphore.doAllowedAction((()=>{super.focusItem(e)}))}restoreFocus(){this._focusSemaphore.doAllowedAction((()=>{super.restoreFocus()}))}focusSelectedItem(){this._focusSemaphore.doIfAllowed((()=>{super.focusSelectedItem()}))}hasTransitContainer(e){return e.hasAttribute(i)}findFocusableElements(){return l.findFocusableElements(this.selectedItemElement).filter((e=>!e.classList.contains("dxbl-dropdown-dialog")))}leaveTransitContainer(e){super.leaveTransitContainer(e),this.parentStrategy.closeSubMenu()}}class m extends h{createSubMenuStrategy(e,t){return new S(e,t,this.menuItemSelector,this,this.hasTransitContainer(t))}closeSelf(){return!1}}class S extends h{createSubMenuStrategy(e,t){return new S(e,t,this.menuItemSelector,this)}closeSelf(){return this.parentStrategy.closeSubMenu(),!0}}export{m as D,h as a,S as b};
