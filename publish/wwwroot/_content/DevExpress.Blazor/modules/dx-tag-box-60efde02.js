import e from"./adaptivedropdowncomponents-7cb91d74.js";import{q as t}from"./constants-da6cacac.js";import{E as s}from"./eventhelper-8bcec49f.js";import{D as o,L as i}from"./dx-dropdown-list-box-3ed16714.js";import{k as r}from"./key-ffa272aa.js";import{isFocusableElement as a,addFocusHiddenAttribute as n}from"./focus-utils-ae044224.js";import{w as p}from"./dom-utils-d057dcaa.js";import{C as l}from"./css-classes-c63af734.js";import{c as m}from"./text-editor-733d5e56.js";import"./dropdowncomponents-3d8f06da.js";import"./dropdown-f5b2318c.js";import"./tslib.es6-d65164b3.js";import"./popup-355ecaa4.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./property-4ec0b52d.js";import"./custom-element-267f9a21.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./portal-b3727c25.js";import"./data-qa-utils-8be7c726.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./custom-events-helper-e7f279d3.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./thumb-31d768d7.js";import"./query-44b9267f.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./popupportal-bbd2fea0.js";import"./events-interseptor-a522582a.js";import"./modalcomponents-951e20e2.js";import"./dx-dropdown-base3-726be7be.js";import"./masked-input-0c0a9541.js";import"./input-66769c52.js";import"./single-slot-element-base-01d93921.js";import"./constants-3209ffde.js";import"./dx-scroll-viewer-da0fb41c.js";import"./dx-html-element-base-3262304e.js";import"./scroll-viewer-css-classes-e724f203.js";import"./dx-list-box-events-6c145567.js";import"./dx-virtual-scroll-viewer-f4a3bc9e.js";import"./grid-scroll-utils-a8c65cf1.js";var c;class d{}c=d,d.TagClass=l.Prefix+"-tag",d.TagTemplateClass=c.TagClass+"-tmpl",d.TagCloseButtonClass=c.TagClass+"-btn-close";class u extends o{constructor(){super()}connectedCallback(){super.connectedCallback()}isFocusableElementInMainElementTemplate(e){return!(!p(e,`.${d.TagTemplateClass}`)||!a(e))||super.isFocusableElementInMainElementTemplate(e)}disconnectedCallback(){super.disconnectedCallback()}get isSearchActive(){return this.searchMode!==i.None}processPointerDown(e){var t;const o=s.containsInComposedPath(e,this.isTagCloseButtonElement),i=s.containsInComposedPath(e,m.isClearButtonElement),r=s.containsInComposedPath(e,m.isEditorButtonElement),a=e.target===this.fieldElement,p=!o&&!this.isFocusableElementInMainElementTemplate(e.target);return(a||p)&&this.toggleDropDownVisibility(),n(this),r&&this.focused&&this.raiseFieldChange(),(i||r||o)&&e.preventDefault(),this.focused||!r||this.useAdaptiveLayout||null===(t=this.editBoxElement)||void 0===t||t.focus(),!0}applyTextPropertyCore(){super.applyTextPropertyCore(),this.fieldElementValue&&(this.selectAll(),this.raiseFieldText()),this.setInputSizeAttribute()}onFieldReady(e,t){super.onFieldReady(e,t),t&&this.setInputSizeAttribute()}updatePlaceholder(){super.updatePlaceholder(),this.setInputSizeAttribute()}onTextInput(e){super.onTextInput(e),this.setInputSizeAttribute()}tryProcessKeyDown(e){return!!super.tryProcessKeyDown(e)||r.KeyUtils.getEventKeyCode(e)===r.KeyCode.Backspace&&(!this.fieldElementValue&&(this.dispatchKeyDownEvent(e),!0))}setInputSizeAttribute(){this.inputElement&&this.inputElement.setAttribute("size",this.getInputSize().toString())}getInputSize(){return this.fieldElementValue?this.fieldElementValue.length:this.placeholder?this.placeholder.length:1}isTagTemplateElement(e){var t;return!!e&&(null===(t=e.classList)||void 0===t?void 0:t.contains(d.TagTemplateClass))}isTagCloseButtonElement(e){var t;return!!e&&(null===(t=e.classList)||void 0===t?void 0:t.contains(d.TagCloseButtonClass))}}customElements.define(t,u);const j={loadModule:function(){},adaptiveDropdownComponents:e};export{u as DxTagBox2,j as default};
