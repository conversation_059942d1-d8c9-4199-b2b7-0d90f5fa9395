import{m as e,d as t}from"./dom-554d0cc7.js";import{c as i}from"./dom-utils-d057dcaa.js";const s="dxbl-chart-first-loading",o="dxbl-chart-legend",n="dxbl-chart-legend-icon",a="dxbl-chart-legend-def-icon",l="dxbl-chart-legend-item",r="--dxbl-chart";class d{}d.axisTitleFontColor=`${r}-axis-title-font-color`,d.axisLineColor=`${r}-axis-line-color`,d.constantLineColor=`${r}-constant-line-color`,d.seriesPointHoverColor=`${r}-point-hover-color`,d.errorBarColor=`${r}-error-bar-color`,d.annotationColor=`${r}-annotation-color`,d.annotationTextColor=`${r}-annotation-text-color`;const c=".dxbl-chart-tooltip";class u{constructor(e){this.currentPointInfo=null,this._owner=e}onTooltipHidden(e){e.isPointerOut&&this.hideTooltip(!1)}onTooltipShown(t,i){const s=t.target;if(!s||!s.series)return;const o=e.MathUtils.generateGuid();this.currentPointInfo={chartPoint:s,x:t.x,y:t.y,id:o};i({seriesIndex:s.series.index,data:s.data,tag:s.tag,id:o})}hideTooltip(e){var t,s;e&&(null===(s=null===(t=this.currentPointInfo)||void 0===t?void 0:t.chartPoint)||void 0===s?void 0:s.series)&&this.currentPointInfo.chartPoint.clearHover();const o=this._owner.querySelector(c);o&&i((()=>o.classList.remove("dxbl-visible"))),this.currentPointInfo=null}positionTooltip(){if(!this.currentPointInfo)return;const e=this.currentPointInfo.x,i=this.currentPointInfo.y,s=this._owner,o=s.getWidgetElement(),n=null==s?void 0:s.querySelector(c);if(!n)return;const a=n.offsetParent,l=i-t.DomUtils.getAbsolutePositionY(a);let r=e-t.DomUtils.getAbsolutePositionX(a),d=0;const u=this.getCorrectedTooltipLeft(r,n,s,o);null!==u&&(d=r-u,r=u),this.applyTooltipPosition(r,l,d,n)}getCorrectedTooltipLeft(e,t,i,s){let o=null;const n=t.getBoundingClientRect().width/2,a=s.getBoundingClientRect();if(e<n){const e=s.offsetParent,t=(null==e?void 0:e.getBoundingClientRect().left)||0;o=a.left-t+n}else{const t=(i.getBoundingClientRect().width+a.width)/2;e+n>t&&(o=t-n)}return o}applyTooltipPosition(e,t,s,o){const n=o.querySelector(".dxbl-chart-tooltip-pointer"),a=`calc(${t}px - 0.625rem)`,l=`${e}px`,r=o.classList,d=0!==s?`calc(50% + ${s}px)`:"50%";i((function(){o.style.top=a,o.style.left=l,n.style.left=d,r.contains("dxbl-visible")||r.add("dxbl-visible")}))}}class g{}g.argumentAxisKey="argumentAxis",g.commonAnnotationSettingsKey="commonAnnotationSettings",g.commonAxisSettingsKey="commonAxisSettings",g.commonSeriesSettingsKey="commonSeriesSettings",g.colorKey="color",g.constantLineStyleKey="constantLineStyle",g.customizeLabelKey="customizeLabel",g.customizePointKey="customizePoint",g.customizeTextKey="customizeText",g.customLabelTextKey="texts",g.dataSourceKey="dataSource",g.legendKey="legend",g.seriesKey="series",g.seriesDataKey="seriesData",g.seriesPointsKey="points",g.seriesTemplateKey="seriesTemplate",g.titleKey="title",g.tooltipKey="tooltip",g.visualRangeKey="visualRange",g.defaultLegendHoverMode="includePoints",g.defaultSeriesNameField="seriesId",g.defaultSeriesArgumentField="argument",g.defaultSeriesValueField="value",g.defaultOpenValueField="openValue",g.defaultHighValueField="highValue",g.defaultLowValueField="lowValue",g.defaultCloseValueField="closeValue",g.defaultRangeValue1Field="startValue",g.defaultRangeValue2Field="endValue",g.defaultSizeField="size",g.seriesTypeRangeBar="rangebar",g.seriesTypeRangeArea="rangearea",g.seriesTypeBar="bar",g.seriesTypeDonut="donut",g.defaultAxisLabelOverlappingBehavior="rotate",g.defaultAxisLabelRotationAngle=45,g.defaultAxisLabelFontOpacity=.75,g.defaultAnnotationType="text",g.defaultStripLabelAlignment="auto";class p{convert(e,t=!1){const i=t?this.createDefaultSettings():{};return this.processServerSettings(i,e),i}createDefaultSettings(){return{dataPrepareSettings:{sortingMethod:!1},adaptiveLayout:{width:0,height:0},pathModified:!1,commonSeriesSettings:this.getDefaultCommonSeriesSettings(),commonAnnotationSettings:this.getDefaultAnnotationSettings(),legend:{visible:!1,hoverMode:g.defaultLegendHoverMode}}}processServerSettings(e,t){e&&(this.processSettingsInCommon(e,t),this.processSpecialSettings(e,t))}processSettingsInCommon(e,t){const i=this.getSpecialProcessingOptionNames();for(const s in t){Object.prototype.hasOwnProperty.call(t,s)&&!i.includes(s)&&this.addOption(e,s,t[s])}}getSpecialProcessingOptionNames(){return[g.legendKey,g.tooltipKey,g.seriesTemplateKey,g.dataSourceKey,g.seriesKey,g.seriesDataKey,g.titleKey]}isSeriesDeleted(e){var t;return 0===(null===(t=e.series)||void 0===t?void 0:t.length)&&!e[g.seriesDataKey]}processSpecialSettings(e,t){var i,s;this.processSeriesSettings(e,t.series),this.processSeriesDataIfNeeded(e,t),(null===(i=t.legend)||void 0===i?void 0:i.hoverMode)&&this.addOption(e,g.legendKey,{hoverMode:null===(s=t.legend)||void 0===s?void 0:s.hoverMode}),void 0!==t[g.tooltipKey]&&this.addOption(e,g.tooltipKey,function(e){var t,i;return{enabled:!1,forceEvents:!!(null===(t=e.tooltip)||void 0===t?void 0:t.enabled),location:(null===(i=e.tooltip)||void 0===i?void 0:i.location)||void 0}}(t),!0)}processSeriesDataIfNeeded(e,t){this.isSeriesDeleted(t)?e[g.dataSourceKey]=[]:this.processSeriesData(e,t[g.seriesDataKey])}getDefaultCommonSeriesSettings(){return{argumentField:g.defaultSeriesArgumentField,valueField:g.defaultSeriesValueField}}getDefaultAnnotationSettings(){return{type:g.defaultAnnotationType}}addOption(e,t,i,s=!1){s||!(void 0!==e[t])||!("object"==typeof i)||i instanceof Array?e[t]=i:Object.assign(e[t],i)}processSeriesSettings(e,t){if(t){this.addOption(e,g.seriesTemplateKey,{nameField:g.defaultSeriesNameField,customizeSeries:function(e){return t.filter((function(t){return t.seriesId===e}))[0]}},!0)}}processSeriesData(e,t){if(!t)return;const i=[];let s=!1,o=!1;for(let e=0;e<t.length;e++){const n=t[e],a=n[g.defaultSeriesNameField],l=n[g.seriesPointsKey];null==l||l.forEach((e=>{s||(s=!!e.pointLabel),o||(o=!!e.pointAppearance);const t=this.createWidgetDataitem(a,e);i.push(t)}))}e[g.dataSourceKey]=i,e[g.customizeLabelKey]=s?e=>this.createWidgetPointLabelSettings(e):null,e[g.customizePointKey]=o?e=>e.data.pointAppearance:null}createWidgetDataitem(e,t){const i=Object.assign({},t);return i[g.defaultSeriesNameField]=e,i}createWidgetPointLabelSettings(e){const t={...e.data.pointLabel},i=t[g.customLabelTextKey];if(i){delete t[g.customLabelTextKey];const s=e.series,o=s.type===g.seriesTypeRangeBar||s.type===g.seriesTypeRangeArea;t[g.customizeTextKey]=t=>o&&i.length>1?t.value===e.minValue?i[0]:i[1]:i[0]}return t}}class S extends p{getDefaultCommonSeriesSettings(){const e={openValueField:g.defaultOpenValueField,highValueField:g.defaultHighValueField,lowValueField:g.defaultLowValueField,closeValueField:g.defaultCloseValueField,rangeValue1Field:g.defaultRangeValue1Field,rangeValue2Field:g.defaultRangeValue2Field,sizeField:g.defaultSizeField,type:g.seriesTypeBar};return Object.assign({},super.getDefaultCommonSeriesSettings(),e)}}class f extends S{createDefaultSettings(){const e=super.createDefaultSettings();return e.commonAxisSettings=this.getDefaultCommonAxisSettings(),e.resizePanesOnZoom=!0,e}processSpecialSettings(e,t){var i,s;super.processSpecialSettings(e,t),e.argumentAxis&&(null!==(i=(s=e.argumentAxis).categories)&&void 0!==i||(s.categories=[])),this.processStripSettings(e)}processStripSettings(e){var t,i,s;const o=null!==(i=null===(t=e.argumentAxis)||void 0===t?void 0:t.strips)&&void 0!==i?i:[],n=(null!==(s=e.valueAxis)&&void 0!==s?s:[]).reduce(((e,t)=>{var i;return e.concat(null!==(i=t.strips)&&void 0!==i?i:[])}),[]);o.concat(n).forEach((e=>{const t=e.label;(null==t?void 0:t.horizontalAlignment)===g.defaultStripLabelAlignment&&(t.horizontalAlignment=void 0),(null==t?void 0:t.verticalAlignment)===g.defaultStripLabelAlignment&&(t.verticalAlignment=void 0)}))}getDefaultCommonAxisSettings(){const e=window.getComputedStyle(document.body).getPropertyValue("color");return{label:{overlappingBehavior:g.defaultAxisLabelOverlappingBehavior,rotationAngle:g.defaultAxisLabelRotationAngle,font:{color:e,opacity:g.defaultAxisLabelFontOpacity}}}}}class m extends p{getDefaultCommonSeriesSettings(){const e=super.getDefaultCommonSeriesSettings();return e.type=g.seriesTypeDonut,e}createDefaultSettings(){const e=super.createDefaultSettings();return e.innerRadius=0,e}}class h extends p{getDefaultCommonSeriesSettings(){const e=super.getDefaultCommonSeriesSettings();return e.type=g.seriesTypeBar,e}}class y extends S{processSpecialChartSettings(e,t){this.processSeriesSettings(e.chart,t.series),this.processSeriesDataIfNeeded(e,t)}processSeriesSettings(e,t){super.processSeriesSettings(e,t),delete e.series}processSeriesDataIfNeeded(e,t){super.processSeriesDataIfNeeded(e,t),delete e.chart.seriesData,delete e.customizeLabel,delete e.customizePoint}}export{g as C,m as P,y as R,f as X,d as a,l as b,n as c,a as d,u as e,o as f,s as g,h};
