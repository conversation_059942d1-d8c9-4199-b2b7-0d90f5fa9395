var e;class a{}a.visible="visible",a.hidden="hidden",a.collapse="collapse";class l{}l.block="block",l.inline="inline",l.inlineBlock="inline-block",l.inlineTable="inline-table",l.listItem="list-item",l.none="none",l.runIn="run-in",l.table="table",l.tableCaption="table-caption",l.tableCell="table-cell",l.tableColumnGroup="table-column-group",l.tableColumn="table-column",l.tableFooterGroup="table-footer-group",l.tableHeaderGroup="table-header-group",l.tableRow="table-row",l.tableRowGroup="table-row-group",l.flex="flex";class t{}t.input="INPUT",t.textarea="TEXTAREA",t.video="VIDEO",t.audio="AUDIO",t.details="DETAILS",t.textArea="TEXTAREA",t.table="table",t.tableCell="td",t.tableHeaderCell="th",t.mark="mark";class i{}e=i,i.aria="aria",i.ariaExpanded=`${e.aria}-expanded`,i.ariaLevel=`${e.aria}-level`,i.ariaChecked=`${e.aria}-checked`,i.ariaSelected=`${e.aria}-selected`;class n{}n.hidden="hidden";class o{}o.textTrimmed="text-trimmed",o.title="title";class r{}r.ellipsis="ellipsis";const s="a[href], input:not([disabled]), button:not([disabled]), textarea:not([disabled]), select:not([disabled]), area[href], iframe, object, embed, [tabindex]:not([tabindex='-1'])";export{o as A,l as D,s as F,n as I,t as T,a as V,r as a,i as b};
