import{d as e}from"./define-custom-element-7c2e65e2.js";import{g as t}from"./devices-17b9ba08.js";class d extends HTMLElement{constructor(){super()}static get observedAttributes(){return["menu-id"]}attributeChangedCallback(e,t,d){"menu-id"===e&&this.updateAttributes(d)}updateAttributes(e){const d=document.querySelector(`[data-dxmenu-id=${e}]`);if(d){t().isMobileDevice&&d.toggleAttribute("data-dx-menu-mobile",!0),d.toggleAttribute("data-dx-menu-loaded",!0)}}}e(customElements,"dxbl-menu-preloader",d);export{d as default};
