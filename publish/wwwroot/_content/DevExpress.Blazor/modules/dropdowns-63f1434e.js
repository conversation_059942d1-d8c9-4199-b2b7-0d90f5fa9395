import{k as t}from"./key-ffa272aa.js";import{t as e}from"./touch-6a322081.js";import{c as o,m as n,t as i,n as r,g as s,o as l,p as c,e as a,a as d,b as h,q as u,v as f,i as m,R as p}from"./dom-utils-d057dcaa.js";import{d as g,r as w}from"./disposable-d2c2d283.js";import{scrollToFocusedItem as v}from"./grid-985dd353.js";import{d as y}from"./dom-554d0cc7.js";import{C as b}from"./css-classes-c63af734.js";import{addFocusHiddenAttribute as x,initFocusHidingEvents as M}from"./focus-utils-ae044224.js";import{E as z}from"./eventRegister-fb9b0e47.js";import{getClientRectWithMargins as C,getClientRect as E,PointBlz as T,geometry as B}from"./dragAndDropUnit-a8432e5f.js";import{F as R}from"./constants-7c047c0d.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./evt-ef2dea65.js";import"./svg-utils-3c1b4ead.js";import"./column-resize-7a55fd84.js";import"./dx-style-helper-d10228f6.js";import"./tslib.es6-d65164b3.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./data-qa-utils-8be7c726.js";import"./lit-element-462e7ad3.js";import"./custom-element-267f9a21.js";import"./dx-listbox-9345a0b2.js";import"./dx-ui-element-0c1e122f.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./eventhelper-8bcec49f.js";import"./devices-17b9ba08.js";import"./property-4ec0b52d.js";const j=document.body,H=new WeakMap,O=new Map,P={subtree:!0,childList:!0},k=new MutationObserver((function(t){t.forEach(L)}));function L(t){t.removedNodes.forEach(S)}function S(t){const e=O.get(t);O.delete(t)&&(0===O.size&&k.disconnect(),e())}const D="show",I="\\s*matrix\\(\\s*"+[0,0,0,0,0,0].map((function(){return"(\\-?\\d+\\.?\\d*)"})).join(",\\s*")+"\\)\\s*",N="above",W="below",F="above-inner",A="below-inner",U="top-sides",V="bottom-sides",q="outside-left",K="outside-right",_="left-sides",Y="right-sides",G="resizing";function X(t){let e=0;if(null!=t&&""!==t)try{const o=t.indexOf("px");o>-1&&(e=parseFloat(t.substr(0,o)))}catch(t){}return Math.ceil(e)}function J(t,e,o){const n=t.getBoundingClientRect(),i={left:e(n.left),top:e(n.top),right:o(n.right),bottom:o(n.bottom)};return i.width=i.right-i.left,i.height=i.bottom-i.top,i}function Q(t){return J(t,Math.floor,Math.ceil)}function Z(t){return J(t,Math.ceil,Math.floor)}class ${constructor(t,e){this.key=t,this.info=e}checkMargin(){return!0}allowScroll(){return"height"===this.info.size}canApplyToElement(t){return t.className.indexOf("dxbs-align-"+this.key)>-1}getRange(t){const e=this.getTargetBox(t)[this.info.to],o=e+this.info.sizeM*(t.elementBox[this.info.size]+(this.checkMargin()?t.margin:0));return{from:Math.min(e,o),to:Math.max(e,o),windowOverflow:0}}getTargetBox(t){return null}validate(t,e){const o=e[this.info.size];return t.windowOverflow=Math.abs(Math.min(0,t.from-o.from)+Math.min(0,o.to-t.to)),t.validTo=Math.min(t.to,o.to),t.validFrom=Math.max(t.from,o.from),0===t.windowOverflow}applyRange(t,e){e.appliedModifierKeys[this.info.size]=this.key;const o="width"===this.info.size?"left":"top",n=e.styles;let i=t.from;this.allowScroll()&&t.windowOverflow>0&&(e.limitBox.scroll.width||(e.limitBox.scroll.width=!0,e.limitBox.width.to-=l()),e.isScrollable&&(n["max-height"]=e.height-t.windowOverflow+"px",e.width+=l(),e.elementBox.width+=l(),i=t.validFrom)),n.width=e.width+"px",this.checkMargin()&&(i+=Math.max(0,this.info.sizeM)*e.margin),e.elementBox[o]+=i,n.transform="matrix(1, 0, 0, 1, "+e.elementBox.left+", "+e.elementBox.top+")"}dockElementToTarget(t){const e=this.getRange(t);this.dockElementToTargetInternal(e,t)||this.applyRange(e,t)}dockElementToTargetInternal(t,e){}}class tt extends ${constructor(t,e,o){super(t,e,o),this.oppositePointName=o||null}getTargetBox(t){return this.info.useInnerTargetBox?t.targetBox.inner:t.targetBox.outer}getOppositePoint(){return this._oppositePoint||(this._oppositePoint=ot.filter(function(t){return t.key===this.oppositePointName}.bind(this))[0])}dockElementToTargetInternal(t,e){if(this.validate(t,e.limitBox))this.applyRange(t,e);else{const o=this.getOppositePoint(),n=o.getRange(e);if(!(o.validate(n,e.limitBox)||n.windowOverflow<t.windowOverflow))return!1;o.applyRange(n,e)}return!0}}class et extends ${checkMargin(){return!1}getTargetBox(t){return t.targetBox.inner}dockElementToTargetInternal(t,e){return(this.info.isHorizontal&&e.fitHorizontally||!this.info.isHorizontal&&e.fitVertically)&&this.validate(t,e.limitBox),!1}validate(t,e){const o=Math.min(t.from,Math.max(0,t.to-e[this.info.size].to));return o>0&&(t.from-=o,t.to-=o),super.validate(t,e)}}const ot=[new tt(N,{to:"top",from:"bottom",size:"height",sizeM:-1,isHorizontal:!1},W),new tt(W,{to:"bottom",from:"top",size:"height",sizeM:1,isHorizontal:!1},N),new tt(F,{to:"top",from:"bottom",size:"height",sizeM:-1,isHorizontal:!1,useInnerTargetBox:!1},A),new tt(A,{to:"bottom",from:"top",size:"height",sizeM:1,isHorizontal:!1,useInnerTargetBox:!0},F),new et(U,{to:"top",from:"top",size:"height",sizeM:1,isHorizontal:!1}),new et(V,{to:"bottom",from:"bottom",size:"height",sizeM:-1,isHorizontal:!1}),new tt(q,{to:"left",from:"right",size:"width",sizeM:-1,isHorizontal:!0},K),new tt(K,{to:"right",from:"left",size:"width",sizeM:1,isHorizontal:!0},q),new et(_,{to:"left",from:"left",size:"width",sizeM:1,isHorizontal:!0}),new et(Y,{to:"right",from:"right",size:"width",sizeM:-1,isHorizontal:!0})];function nt(t,e,o){const n=s(),i=Q(t),r=Z(e),l=t.ownerDocument.documentElement,c={isScrollable:y.DomUtils.hasClassName(t,"dxbs-scrollable"),specifiedOffsetModifiers:ot.filter((function(e){return e.canApplyToElement(t)})),margin:X(n.marginTop),width:o?Math.max(o.width,Math.ceil(t.offsetWidth)):Math.ceil(t.offsetWidth),height:Math.ceil(t.offsetHeight),appliedModifierKeys:{height:null,width:null},fitHorizontally:o.fitHorizontally,fitVertically:o.fitVertically},a=function(t){const e=new RegExp(I).exec(t.transform);return e?{left:parseInt(e[5]),top:parseInt(e[6])}:{left:0,top:0}}(n),d=t.classList.contains(b.InvisibleOffScreen)||t[G]?r.left:i.left;var h,u,f,m;c.elementBox={left:h=a.left-d,top:u=a.top-i.top,right:h+(f=i.width),bottom:u+(m=i.height),width:f,height:m},c.targetBox={outer:Q(e),inner:Z(e)},c.limitBox={scroll:{width:l.clientWidth<window.innerWidth,height:l.clientHeight<window.innerHeight},width:{from:0,to:l.clientWidth},height:{from:0,to:l.clientHeight}},c.styles={};const p=(t.getAttribute("data-popup-align")||o&&o.align).split(/\s+/);return c.offsetModifiers=ot.filter((function(t){return p.some((function(e){return t.key===e}))})),c}function it(t,e,s,l=!0,c=!0,a=(t=>{})){null!==e&&(!function(t,e,i,r){o((function(){const o=nt(t,e,i);for(let t=0;t<o.offsetModifiers.length;t++)o.offsetModifiers[t].dockElementToTarget(o);r(o),n(t,o.styles)}))}(t,e,{align:s,fitHorizontally:l,fitVertically:c},a),i(t,D,!0),r(t))}function rt(t){return parseFloat(window.getComputedStyle(t,null).getPropertyValue("padding-right"))}function st(){return window.innerWidth-document.body.getBoundingClientRect().width}class lt{constructor(t,e){this.element=t,this.getClientRect=e}get leftTopCorner(){const t=this;return new ct(this.element,(function(e){return t.getClientRect(e)}),(function(t){return{x:0,y:0}}))}get leftBottomCorner(){const t=this;return new ct(this.element,(function(e){const o=t.getClientRect(e);return new T(o.x,o.bottom)}),(function(e){const o=t.getClientRect(e);return new T(0,-o.height)}))}get rightTopCorner(){const t=this;return new ct(this.element,(function(e){const o=t.getClientRect(e);return new T(o.right,o.y)}),(function(e){const o=t.getClientRect(e);return new T(-o.width,0)}))}get rightBottomCorner(){const t=this;return new ct(this.element,(function(e){const o=t.getClientRect(e);return new T(o.right,o.bottom)}),(function(e){const o=t.getClientRect(e);return new T(-o.width,-o.height)}))}get center(){const t=this;return new ct(this.element,(function(e){return t.getClientRect(e).center}))}}class ct{constructor(t,e,o){this.element=t,this.getLocation=e,this.getDelta=o}get location(){return this.getLocation(this.element)}get delta(){return this.getDelta(this.element)}anchorTo(t){return new at(this,t)}}class at{constructor(t,e){this.point=t,this.anchor=e,this.events=new z(this);const o=[];if(o.push([window,"resize"]),o.push([window,"scroll"]),this.containers=function(t,e){const o=[];for(;null!==t&&"BODY"!==t.tagName&&"#document"!==t.nodeName;)e(t)&&o.push(t),t=t.parentNode;return 0===o.length?null:o}(this.anchor.element.parentNode,this.isElementScrollable),this.containers&&this.containers.forEach((function(t){o.push([t,"scroll"])})),this.checkInCasesInt(o),"undefined"!=typeof ResizeObserver){const t=this;this.resizeObserver=new window.ResizeObserver((function(){t.update()})),this.resizeObserver.observe(this.anchor.element),this.resizeObserver.observe(this.point.element)}else this.resizeObserver=null;let n=this.point.element.offsetParent;this.notStaticParent=null===n?{x:0,y:0,scrollTop:0,scrollLeft:0}:c(n)?n:window,this.update()}isElementScrollable(t){const e=window.getComputedStyle(t);return"static"===e.position&&("scroll"===e["overflow-x"]||"scroll"===e["overflow-y"]||"auto"===e["overflow-x"]||"auto"===e["overflow-y"])}update(){const t=this.notStaticParent===window?{x:window.scrollX,y:window.scrollY}:{x:this.notStaticParent.scrollLeft,y:this.notStaticParent.scrollTop},e=B(this.anchor.location,"+",this.point.delta,"-",this.notStaticParent,"+",t),n=this.point.element;o((function(){n.style.left=e.x+"px",n.style.top=e.y+"px"}))}checkInCasesInt(t){const e=this.events;t.forEach((function(t){e.attachEvent(t[0],t[1],(function(t){this.update()}))}))}checkInCases(){return this.containers?(this.checkInCasesInt(Array.from(arguments)),this.update(),this):this}dispose(){this.events&&(this.events.dispose(),this.events=null,this.dropDownStartPos=null,this.containers=null,this.resizeObserver&&this.resizeObserver.disconnect())}}function dt(t){return new lt(t,C)}function ht(t){return new lt(t,E)}var ut,ft;!function(t){t[t.Popup=0]="Popup",t[t.Modal=1]="Modal"}(ut||(ut={})),function(t){t[t.Down=1]="Down",t[t.Up=2]="Up"}(ft||(ft={}));const mt=8,pt=new WeakMap;let gt;const wt=[];function vt(t){let e=(t=a(t)).querySelector(".dxbs-dm.dropdown-menu");return e||(e=t.querySelector(".dxgvCSD.dxbs-grid-vsd")),e}function yt(t,e,o,n){return new Promise(((o,i)=>{let r=vt(t);r=a(r),r?(0===n&&(r.style.minWidth=t.offsetWidth+"px"),2===n&&(r.style.width=t.offsetWidth+"px"),bt(t,r,e),v(r),o()):o()}))}function bt(t,e,n){pt.has(t)&&(pt.get(t).disconnect(),pt.delete(t));const i=e.offsetParent;if(!i)return void o((function(){e.style.visibility=""}));const r=i.getBoundingClientRect(),s=t.getBoundingClientRect(),l=s.top-r.top,c=r.bottom-s.bottom;let a;const d=window.getComputedStyle(e),h=e.offsetHeight+Math.max(parseFloat(d.marginTop),0)+Math.max(parseFloat(d.marginBottom),0)+mt;switch(n){case ft.Up:a=!0,r.top+(l-h)<=0&&r.top+l+t.offsetHeight+h+window.pageYOffset<=Math.max(document.body.scrollHeight,window.innerHeight)&&(a=!1);break;default:a=!1,r.bottom-(c-h)>=window.innerHeight&&r.top+l-h+window.pageYOffset>=0&&(a=!0);break}const u=s.left+e.offsetWidth+mt>=document.body.clientWidth&&s.right-e.offsetWidth-mt>0;o((()=>{gt&&gt.dispose(),gt=a?u?dt(e).rightBottomCorner.anchorTo(ht(t).rightTopCorner):dt(e).leftBottomCorner.anchorTo(ht(t).leftTopCorner):u?dt(e).rightTopCorner.anchorTo(ht(t).rightBottomCorner):dt(e).leftTopCorner.anchorTo(ht(t).leftBottomCorner),e.style.visibility=""}))}const xt=[{value:0,text:""},{value:1,text:N},{value:2,text:W},{value:4,text:U},{value:8,text:V},{value:16,text:q},{value:32,text:K},{value:64,text:_},{value:128,text:Y},{value:256,text:F},{value:512,text:A}];function Mt(t,e,o){let n=t.target;for(;n;){if(n===e)return;n=n.parentElement}o&&o()}function zt(t){return"hidden"!==t.style.visibility||t.classList.contains("dxbs-visually-hidden")}function Ct(t,e){if(!e)return!1;if(d(t.srcElement,"modal-header"))return!0;const o=d(t.srcElement,"column-chooser-elements-container"),n=d(o,"modal-body");if(!o||!n)return!1;if(n.clientHeight>=o.clientHeight)return!0;const i=t.touches[0].pageY-e.touches[0].pageY,r=t=>t.getBoundingClientRect().top,s=o.querySelector(".column-chooser-element-container");if(s&&r(s)===r(n)&&i>=0&&t.cancelable)return!0;const l=t=>Math.round(t.getBoundingClientRect().bottom),c=o.querySelector(".column-chooser-element-container:last-child");return c&&l(c)===l(n)&&i<=0&&t.cancelable}function Et(t,e,o){if(o!==ut.Modal)return-1;const n=t.getBoundingClientRect(),i=e.getBoundingClientRect(),r=d(t,"dxbs-gridview"),s=r&&r.querySelector("thead");if(!s)return-1;const l=s.getBoundingClientRect(),c=(l||n).bottom;return c>.5*e.clientHeight?c-n.top<.5*e.clientHeight?c-i.top-.5*e.clientHeight:n.top-i.top-2:-1}function Tt(t){if(!(t=a(t)))return;const e=t.getElementsByClassName("modal-body")[0];e.style.width=e.offsetWidth+"px",e.style.height=e.offsetHeight+"px"}const Bt={init:function(t,o,n,i){if(t=a(t),o=a(o),n=a(n),!t)return Promise.reject("failed");if(g(t),n){const r=e=>{Mt(e,t,(function(){u(t)||g(t);const e=document.activeElement===o,r=n&&zt(n);(e||r)&&i.invokeMethodAsync("OnDropDownLostFocus",o.value).catch((t=>console.error(t)))}))};document.addEventListener(e.TouchUtils.touchMouseDownEventName,r),w(t,(function(){document.removeEventListener(e.TouchUtils.touchMouseDownEventName,r)}))}return Promise.resolve("ok")},dispose:function(t){return(t=a(t))&&g(t),gt&&gt.dispose(),Promise.resolve("ok")},showAdaptiveDropdown:function(n,s,l,c,u,g){if(!(n=a(n)))return Promise.reject();const w=d(n,l);if(!w)return Promise.reject();const v=document.documentElement,y=document.documentElement.style.overflow,b=document.body.style.overflow,z=document.body.scroll,C=s.dialogType,E=s.alignment,T=function(t){let e="";return xt.forEach((o=>{0!=(o.value&t)&&(e&&(e+=" "),e+=o.text)})),e}(E),B=C===ut.Modal,L=new ResizeObserver((function(t,e){if(t.length<1)return;D&&D.height!==t[0].contentRect.height&&(r(n),f(n,G,!0),it(n,w,T,s.fitHorizontally,s.fitVertically,I),o((()=>{f(n,G,!1)})));D=t[0].contentRect}));if(C===ut.Popup)0===E?bt(w,n,s.dropDownDirection):it(n,w,T,s.fitHorizontally,s.fitVertically,I),s.handleResize&&L.observe(n);else{wt.push(n),n.style.paddingRight=rt(n)+st()+"px";const t=rt(document.body)+st();document.body.style.paddingRight=String(t),document.body.classList.add("dxbl-modal-open")}let S=!1,D=null;function I(t){let e;t.appliedModifierKeys.height===W||t.appliedModifierKeys.height===A?e=s.bottomAlignmentCssClass:t.appliedModifierKeys.height!==N&&t.appliedModifierKeys.height!==F||(e=s.topAlignmentCssClass),e&&i(n,e,!0)}function U(t){const e=t.srcElement;n&&e&&(!n.contains(e)||C===ut.Modal&&n===e)&&q(n)}function V(t){const e=t.target;e&&(v.removeEventListener("focusin",V),null===t.relatedTarget&&n&&n.contains(e)&&e.focus())}function q(t){if(!S){if(S=!0,m(t))return;K(),u.invokeMethodAsync("CloseDialog").catch((function(e){m(t)||console.error(e)}))}}function K(){if(v.removeEventListener(e.TouchUtils.touchMouseDownEventName,U),window.removeEventListener("resize",Y),L.disconnect(),C===ut.Modal){if(n){const t=wt.indexOf(n);t>-1&&wt.splice(t,1)}wt.length||(X(C,!1),document.body.classList.remove("dxbl-modal-open"),document.body.style.paddingRight=String(rt(document.body)-st()))}n=null}function _(){if(!n)return;const t=n.querySelector(R);t&&t.focus()}function Y(){if(!n||!d(n,"modal-dialog-owner"))return;const t=n.firstElementChild;if(!t)return;const e=t.classList,o=Et(w,v,C),i=v.clientHeight>v.clientWidth?"topVertical":"topHorizontal";p((function(){e.contains("topVertical")||e.contains("topHorizontal")?e.remove("transition"):e.add("transition"),o&&(v.scrollTop=o),e.remove("topVertical","topHorizontal"),e.add(i)}))}function X(t,e){if(t!==ut.Modal)return;let o,n,i;e?(o="hidden",n="hidden",i=()=>{}):(o=y,n=b,i=z),document.documentElement.style.overflow=o,document.body.style.overflow=n,document.body.scroll=i}return v.addEventListener(e.TouchUtils.touchMouseDownEventName,U),n.addEventListener("keydown",(function(e){n&&e.keyCode===t.KeyCode.Esc&&q(n)})),n.addEventListener("focusout",(function(t){const e=t.relatedTarget;!S&&n&&(e&&!n.contains(e)?function(t){if(!n)return;const e=n.compareDocumentPosition(t);e&window.Node.DOCUMENT_POSITION_PRECEDING?function(){if(!n)return;const t=function(t){const e=t.querySelectorAll(R);return e[e.length-1]}(n);t&&t.focus()}():e&window.Node.DOCUMENT_POSITION_FOLLOWING&&_()}(e):null===e&&v.addEventListener("focusin",V))})),function(t,e,o,n){t&&e.addEventListener("touchmove",(t=>{t.srcElement===e&&t.preventDefault()}));if(n===ut.Modal){let t;o.addEventListener("touchstart",(e=>{t=e})),o.addEventListener("touchmove",(e=>{Ct(e,t)&&e.preventDefault(),t=e}))}}(B,n,w,C),function(t){if(H.has(t))return H.get(t);const e=new Promise((function(e){0===O.size&&k.observe(j,P),O.set(t,(()=>{e(void 0)}))}));return H.set(t,e),e}(n).then((()=>{K(),n=null})),X(C,!0),o((function(){n&&(s.showFocus||(x(n),M(n)),C===ut.Popup&&(_(),function(t,e){const o=d(t,e);if(t&&o){const e=d(t,"dx-menu-bar");e&&e.classList.contains("vertical")||(h(o,n),n())}function n(){const e=t.getBoundingClientRect(),n=o.getBoundingClientRect(),i=parseFloat(t.style.marginLeft);if(i&&(e.x=e.x-i),Math.round(e.x+e.width)>Math.round(n.x+n.width)&&n.width>e.width){const o=d(t.parentNode,"dropdown-menu");if(o){const n=o.getBoundingClientRect(),i=parseFloat(window.getComputedStyle(o,null).getPropertyValue("border-right-width"));t.style.marginLeft="-"+(e.x+e.width-n.x-i)+"px"}else t.style.marginLeft="-"+(e.x+e.width-n.x-n.width)+"px"}else i&&(t.style.marginLeft="")}}(n,g)))})),C===ut.Modal&&(window.addEventListener("resize",Y),Y()),Promise.resolve()},updateGridDropDown:yt,setInlineModalSize:Tt};export{ut as DialogType,Bt as default,vt as getDropDownElement,zt as isDropDownVisible,Mt as onOutsideClick,Et as scrollDropDown,Tt as setInlineModalSize,bt as setPositionOfDropDown,Ct as shouldPreventTouchMove,yt as updateGridDropDown};
