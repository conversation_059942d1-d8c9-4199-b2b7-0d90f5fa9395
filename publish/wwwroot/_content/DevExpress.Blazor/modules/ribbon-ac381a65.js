import{_ as t}from"./tslib.es6-d65164b3.js";import{d as e}from"./dom-554d0cc7.js";import{J as s,f as i,j as o,L as r,i as n,R as l,b as a,u as h}from"./dom-utils-d057dcaa.js";import{d as u}from"./disposable-d2c2d283.js";import{U as c}from"./ribbon-utils-2b6a64cb.js";import{initFocusHidingEvents as d}from"./focus-utils-ae044224.js";import{T as p}from"./toolbar-css-classes-d303c118.js";import{C as m}from"./css-classes-c63af734.js";import{S as g}from"./single-slot-element-base-01d93921.js";import{D as f}from"./keyboard-navigation-strategy-ea41c807.js";import{D as y}from"./root-keyboard-strategy-d1e519a3.js";import{n as b}from"./property-4ec0b52d.js";import{e as k}from"./custom-element-267f9a21.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./key-ffa272aa.js";import"./touch-6a322081.js";import"./constants-7c047c0d.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./focushelper-2eea96ca.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./devices-17b9ba08.js";import"./menu-keyboard-strategy-7c257fd3.js";import"./popup-355ecaa4.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./portal-b3727c25.js";import"./constants-a4904a3f.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./capture-manager-2454adc2.js";import"./nameof-factory-64d95f5b.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./constants-ed56e953.js";import"./constants-3209ffde.js";import"./modal-keyboard-strategy-05da34d9.js";import"./dropdown-menu-keyboard-strategy-60d36909.js";class B{constructor(t){this.container=null,this.owner=t}querySelectorAll(t){var e;return this.querySelectorAllInternal(t||"#"+(null===(e=this.getContainer())||void 0===e?void 0:e.id))}toggleClassName(t,e,s){return this.owner?this.owner.toggleClassName(t,e,s):this.toggleClassNameInternal(t,e,s)}toggleClassNameInternal(t,e,s){}querySelectorAllInternal(t){return this.owner?this.owner.querySelectorAll(t):this.getNodes(t)}getNodes(t){const e=this.container;let s=e.querySelectorAll(t);return s.length||(t="#"+e.id+t,e.parentNode&&(s=e.parentNode.querySelectorAll(t))),s}getContainer(){return this.owner?this.owner.getContainer():this.container}getBoxSize(t){return Math.ceil(s(t))+this.getBoxOuterOffset(t)}getBoxInnerOffset(t){return i(t)}getBoxOuterOffset(t){return o(t)}getBoxOffset(t){return this.getBoxOuterOffset(t)+this.getBoxInnerOffset(t)}getNodeWidth(t,e=!1){const s=t;return s?Math.ceil(s.offsetWidth+(e?0:this.getBoxOuterOffset(s)))+r(s):0}dispose(){}setActive(t){}createLayoutEntity(t,...e){return null!==this.owner?this.owner.createLayoutEntity(t,...e):this.createLayoutEntityCore(t,...e)}createLayoutEntityCore(t,...e){return new(this.resolveLayoutEntityType(t))(...e)}resolveLayoutEntityType(t){return t}}const C=`.${p.ButtonToolbar}`,x=`.${p.ButtonEllipsis}`;class v extends B{constructor(t,e,s){super(t),this.nextBlock=null,this.prevBlock=null,this.ribbonOwner=t,this.group=e,this.element=s,this.width=0,this.isActiveValue=null,this.index=e.blocks.length,this.nextBlock=null,this.prevBlock=s.layoutBlockObj||null,this._element=s,this._element.layoutBlockObj=this._element.layoutBlockObj?this._element.layoutBlockObj.nextBlock=this:this,this._element.onHide=()=>{this.toggleClassName(this.element,p.ToolbarHiddenItem,!0)},this._element.onShow=()=>{this.toggleClassName(this.element,p.ToolbarHiddenItem,!1)}}dispose(){delete this._element.onHide,delete this._element.onShow,this.ribbonOwner=null,super.dispose()}afterCreate(t){delete this._element.layoutBlockObj,t.maxWidth+=this.width,t.minWidth+=this.getMinWidth(),0===this.width&&this.raiseVisibilityChange(!1)}setActive(t){this.isActiveValue!==t&&(this.isActiveValue=t,this.group.activeBlock=t?this:null,this.group.groupsCollection.lastGroup=this.group,this.group.state.prevState&&this.toggleClassName(this.group.element.parentNode,this.group.state.prevState.name,this.isActiveValue),this.toggleClassName(this.element,this.group.state.name,this.isActiveValue),this.toggleClassName(this.group.element,this.group.state.name,this.isActiveValue))}raiseVisibilityChange(t){var e,s;const i=t?this._element.onShow:this._element.onHide;i&&(null===(e=this.owner)||void 0===e||e.domChanges.push(i)),null===(s=this.ribbonOwner)||void 0===s||s.toggleVisibility(this.element,t)}getMinWidth(){return this.nextBlock?this.nextBlock.getMinWidth():this.width}getNextBlock(t){let e=this.group,s=e.blocks[this.index-t];return!s&&(e=e.groupsCollection.blockGroups[e.totalIndex+t])&&(s=e.blocks[Math.pow(e.blocks.length,t>0?1:0)-1]),s}}class j extends B{constructor(t,e,s,i){super(t),this.builders=e||[],this.name=s,this.index=i?i.index+1:0,this.prevState=i,this.nextState=null,i&&(i.nextState=this)}for(t){return this.builders[this.builders.length]=this.createLayoutEntity(S,this.owner,this.name,t)}}class w extends B{constructor(t,e,s,i,o){super(t),this.groupsCollection=e,this.state=i,this.element=s,this.elementOffset=this.getBoxOffset(s)+this.getMarginLeftOffset(s),this.blocks=[],this.activeBlock=null,this.fullWidth=this.elementOffset,this.isSmallest=!i.nextState,this.isLargest=!i.prevState,this.domIndex=o,this.totalIndex=this.groupsCollection.blockGroups.length,this.createBlocks(i.builders);const r=s;i.prevState&&(this.isSmallest||(r.layoutBlockGroupObj.isSmallest=0===this.blocks.length))?delete r.layoutBlockGroupObj:r.layoutBlockGroupObj=this}dispose(){for(let t=0;t<this.blocks.length;t++)this.blocks[t].dispose();super.dispose()}afterCreate(t){this.blocks.forEach((function(e){e.afterCreate(t)})),t.groupsOffset+=this.elementOffset,t.groupBlocksLengthLookup[this.domIndex]=this.blocks.length,t.lastGroup=this}setActive(t){let e=0;!this.isSmallest&&this.isLargest&&(e=this.blocks.length-1),this.blocks[e].setActive(t)}createBlocks(t){for(let e=0;e<t.length;e++){const s=t[e],i=s.findBlockElements(this.element);for(let t=0;t<i.length;t++){const e=this.createLayoutEntity(v,this.owner,this,i[t]);this.fullWidth+=e.width=s.getBlockWidth(e),this.blocks.push(e)}}this.isLargest&&this.setActive(!0)}calculateWidth(t){return this!==t.group?this.fullWidth:this.getActualBlocks(t).reduce((function(t,e){return t+e.width}),this.elementOffset)}getActualBlocks(t){return this.blocks.map(function(e){return e.prevBlock&&e.index<t.index?e.prevBlock:e}.bind(this))}getBeforeOffset(t){const s=window.getComputedStyle(t,":before");return e.DomUtils.pxToInt(s.width)+e.DomUtils.pxToInt(s.marginRight)}getMarginLeftOffset(t){const s=window.getComputedStyle(t);return e.DomUtils.pxToInt(s.marginLeft)}}class A extends B{constructor(t,e,s){super(t),this.ribbonOwner=t,this.groupsContainer=e,this.testElement="function"==typeof s?s:function(t){return c.elementMatchesSelector(t,s)},this.blockGroups=[],this.states=[],this.width=0,this.groupsOffset=0,this.maxWidth=0,this.minWidth=0,this.groupElementsCount=0,this.currentLayoutElements=[],this.lastGroup=null,this.selector=s,this.groupLookupMap={},this.groupBlocksLengthLookup={}}createBlockGroup(t,e,s){let i=this.createLayoutEntity(w,this.owner,this,t,e,s);return i&&(i=i.blocks.length>0?this.blockGroups[this.blockGroups.length]=i:null),i&&(this.groupLookupMap[s]||(this.groupLookupMap[s]=[])).splice(0,0,i.totalIndex),i}createBlockGroups(){var t;const e=null===(t=this.ribbonOwner)||void 0===t?void 0:t.getGroupElements(this.groupsContainer,this.selector);if(e){this.groupElementsCount=e.length;for(let t=0;t<this.states.length;t++)for(let s=this.groupElementsCount-1;s>=0;s--){const i=e[s];!i||this.testElement(i)&&this.createBlockGroup(i,this.states[t],s)||(e[s]=null)}this.states=[];for(let t=0;t<this.blockGroups.length&&this.blockGroups[t].isLargest;t++)this.blockGroups[t].afterCreate(this);this.minWidth+=this.groupsOffset,this.maxWidth+=this.groupsOffset,this.width=this.maxWidth,this.currentLayoutElements=this.findActiveBlocks()}}defineState(t,e){const s=this.createLayoutEntity(j,this.owner,[],t,this.states[this.states.length-1]||null);this.states.push(s),e(s)}initialize(){}applyLayout(t){this.currentLayoutElements&&this.currentLayoutElements.forEach((function(t){t.setActive(!1)})),this.currentLayoutElements=t,this.currentLayoutElements.forEach((function(t){t.setActive(!0)})),this.detectBlockItemVisibilityChanges()}adjust(t){if(t===this.width||t<=this.minWidth&&this.width===this.minWidth||t>=this.maxWidth&&this.width===this.maxWidth)return;const e=this.width;if(this.width=Math.max(this.minWidth,Math.min(this.maxWidth,t)),this.width===this.maxWidth)return this.applyLayout(this.blockGroups.filter((function(t){return t.isLargest})));if(this.width===this.minWidth)return this.applyLayout(this.blockGroups.filter((function(t){return t.isSmallest})));const s=e-this.width,i=s/Math.abs(s),o=this.findActiveBlocks();let r=o?o[0]:null,n=r;const l=r;for(;r;){if(t=this.calculateWidth(r),i>0&&t<=this.width||i<0&&t>=this.width){const t=i<0?n:r;t!==l&&null!==t&&this.applyLayout([t]);break}n=r,r=r.getNextBlock(i)}}detectBlockItemVisibilityChanges(){const t=this.findActualBlockGroups();for(let e=0;e<t.length;e++){const s=t[e];let i=!1;for(let t=0;t<s.blocks.length;t++){let e=s.blocks[t];s!==this.lastGroup||(i=i||e.isActiveValue)||(e=e.prevBlock||e),e.raiseVisibilityChange(e.width>0)}}}calculateWidth(t){if(!t){const e=this.findActiveBlocks();e&&(t=e[0])}return this.findActualBlockGroups(t.group).reduce((function(e,s){return e+s.calculateWidth(t)}),0)}findActualBlockGroups(t=null){const e=[];if(!(t=t||this.lastGroup))return e;for(let s=0;s<this.groupElementsCount;s++){const i=this.groupLookupMap[s];for(let o=0;o<i.length;o++){const r=this.blockGroups[i[o]];if(s<t.domIndex?r.state.index<t.state.index:r.state.index<=t.state.index){e.push(r);break}}}return e}findActiveBlocks(){return this.lastGroup?this.lastGroup.blocks.filter((function(t){return t.isActiveValue})):null}dispose(){for(let t=0;t<this.blockGroups.length;t++)this.blockGroups[t].dispose();this.blockGroups=[],super.dispose()}}class S extends B{constructor(t,e,s){super(t),this.name=e,this.selectorOrFunc=s,this.prepareBlockFunc=null}getBlockWidth(t){return this.prepareBlockFunc?this.prepareBlockFunc(t):0}findBlockElements(t){const e=c.getChildElementNodesByPredicate(t,(t=>c.elementMatchesSelector(t,this.selectorOrFunc)));return null!=e?e:[]}setPrepareFunc(t){return this.prepareBlockFunc=t,this}setWidth(){return this.setPrepareFunc((t=>this.getBoxSize(t.element)))}setHidden(){return this.setPrepareFunc((function(t){return 0}))}setOnlyImageWidth(){return this.setPrepareFunc((t=>{let e=this.getBoxSize(t.element),s=c.getChildByClassName(t.element,`${m.Image}:not(.${p.AdaptivePreviewImage})`);s||(s=c.getChildByClassName(c.getChildByClassName(t.element,p.DropDownToggle),`${m.Image}:not(.${p.AdaptivePreviewImage})`));let i=c.getChildByClassName(t.element,p.AdaptivePreviewImage);if(i||(i=c.getChildByClassName(c.getChildByClassName(t.element,p.DropDownToggle),p.AdaptivePreviewImage)),s){if(e=this.getBoxSize(s)-this.getBoxOuterOffset(s),i){const t=this.getBoxSize(i)-this.getBoxOuterOffset(i);e=Math.max(e,t)}let o=this.getBoxOffset(t.element),r=s.parentNode;for(;r!==t.element;)o+=this.getBoxOffset(r),r=r.parentNode;return e+o}return e}))}setNoTextWidth(){return this.setPrepareFunc((t=>{let e=this.getBoxSize(t.element);if(c.getChildByClassName(t.element,p.ToolbarEdit))return e;const s=c.getChildByClassName(t.element,p.ToolbarItem);if(c.getChildByClassName(s,p.ToolbarEdit))return e;const i=this.findImageElement(t.element);if(i){const t=i.nextElementSibling;t&&(e-=this.getBoxSize(t),e-=this.getBoxOuterOffset(i))}return e}))}findImageElement(t){var e;let s=c.getChildByClassName(t,m.Image);if(s||(s=c.getChildByClassName(c.getChildByClassName(t,m.Button),m.Image)),!s&&(null===(e=null==t?void 0:t.classList)||void 0===e?void 0:e.contains(p.ToolbarItem))){const e=c.getChildByClassName(t,p.ToolbarItem);return this.findImageElement(e)}return s}}class O extends B{constructor(t){super(null),this.clientStateMap=new Map,this.dotNetReference=t.dotNetReference,this.container=t.getMainElement(),this.containerOffsets=this.calculateContainerOffsets(),this.blockGroupsArray=[],this.isReady=!1,this.classesToApply=[],this.domChanges=[],this.nextAdjustGroupWidth=null}getClientState(t){if(null===t.getAttribute("dxbl-toolbar-adaptive-item"))return null;const e=t.id;if(!e)return null;let s=this.clientStateMap.get(e);return s||(s={id:e},this.clientStateMap.set(e,s)),s}toggleClassNameInternal(t,e,s){this.getBatchCssUpdateCache(t)[e]=s;const i=this.getClientState(t);i&&(i[e]=s)}getBatchCssUpdateCache(t){let e=t._layoutBuilderCache;return e||(e=t._layoutBuilderCache={},this.classesToApply.push(this.createBatchCssUpdateDelegate(t))),e}createBatchCssUpdateDelegate(t){return function(){const s=t._layoutBuilderCache;if(s){delete t._layoutBuilderCache;for(const i in s)Object.prototype.hasOwnProperty.call(s,i)&&e.DomUtils.toggleClassName(t,i,s[i])}}}createBlockGroupsCore(t,e,s){const i=[],o=this.querySelectorAll(t);for(let t=0;t<o.length;t++){const r=this.createLayoutEntity(A,this,o[t],e);this.blockGroupsArray.push(r),s&&s(r),r.createBlockGroups(),i.push(r)}return this.nextAdjustGroupWidth=this.getGroupsWidth(),i}initialize(){for(let t=0;t<this.blockGroupsArray.length;t++)this.blockGroupsArray[t].initialize();this.isReady=!0,this.adjust()}adjust(t=null){if(this.isReady){const e=this.classesToApply,s=this.domChanges;let i=this.classesToApply=[],o=this.domChanges=[];const r=this.clientStateMap;let l;null!==this.nextAdjustGroupWidth?(l=this.nextAdjustGroupWidth,this.nextAdjustGroupWidth=null):l=this.getGroupsWidth();for(let t=0;t<this.blockGroupsArray.length;t++)this.blockGroupsArray[t].adjust(l);t&&t(),i=e.concat(i),o=s.concat(o);const a=Array.from(r.values()).concat(Array.from(this.clientStateMap.values()));this.queueUpdates((()=>{for(;i.length;){const t=i.shift();t&&t()}this.queueUpdates((()=>{for(;o.length;){const t=o.shift();t&&t()}a.length&&this.dotNetReference.invokeMethodAsync("OnModelUpdated",a).catch((t=>{n(this.container)||console.error(t)}))}))}))}}toggleVisibility(t,e){const s=this.getClientState(t);s&&(s.isVisible=e)}queueUpdates(t){l(t)}getGroupsWidth(){const t=this.getContainer();return t?t.offsetWidth-this.containerOffsets:0}dispose(){this.containerOffsets=this.calculateContainerOffsets();for(let t=0;t<this.blockGroupsArray.length;t++)this.blockGroupsArray[t].dispose();this.blockGroupsArray=[],this.classesToApply=[],this.isReady=!1,super.dispose()}getGroupElements(t,e){return c.getChildElementNodesByPredicate(t,(function(t){return c.elementMatchesSelector(t,e)}))}createBlockGroups(){this.createBlockGroupsCore(`.${p.Toolbar} > ${C}`,`.${p.ToolbarGroup}`,(function(t){t.defineState(p.AdaptiveItem,(function(t){t.for(`:not(${x})`).setWidth(),t.for(x).setHidden()})),t.defineState(p.AdaptiveItemTextHidden,(function(t){t.for(`:not(${x})`).setNoTextWidth(),t.for(x).setHidden()})),t.defineState(p.AdaptiveItemHidden,(function(t){t.for(`:only-child:not(${x})`).setWidth(),t.for(`:not(:only-child):not(${x})`).setHidden(),t.for(x).setWidth()})),t.defineState(p.AdaptiveItemAllHidden,(function(t){t.for(`:only-child:not(${x})`).setNoTextWidth(),t.for(`:not(:only-child):not(${x})`).setHidden(),t.for(x).setNoTextWidth()}))}))}calculateContainerOffsets(){var t;if(!this.container)return 0;let e=this.getBoxInnerOffset(this.container),s=null!==(t=this.container.querySelector(".dxbl-toolbar"))&&void 0!==t?t:this.container;for(;s!==this.container&&s;)e+=this.getBoxOffset(s),s=s.parentNode;return e}}class W{constructor(t,e){this.currentWidth=null,this.currentHeight=null,this.elementContentWidthSubscription=null,this.mainElement=t,this.layoutBreakPoints=null,this.dotNetReference=e}getMainElement(){return this.mainElement}initialize(){l((()=>{this.buildLayout()})),l((()=>{this.adjustLayout()})),this.elementContentWidthSubscription=a(this.getMainElement(),(t=>{this.currentWidth===t.width&&this.currentHeight===t.height||(this.currentWidth=t.width,this.currentHeight=t.height,this.onBrowserWindowResize())}))}applyLayoutStateAppearance(){var t;e.DomUtils.addClassName(this.getMainElement(),p.Loaded),null===(t=this.layoutBreakPoints)||void 0===t||t.initialize(),setTimeout((()=>{e.DomUtils.removeClassName(this.getMainElement(),p.Loading)}),500)}onBrowserWindowResize(){this.layoutBreakPoints&&this.layoutBreakPoints.adjust()}update(){this.layoutBreakPoints&&this.layoutBreakPoints.adjust()}dispose(){this.elementContentWidthSubscription&&h(this.elementContentWidthSubscription),this.layoutBreakPoints&&this.layoutBreakPoints.dispose()}buildLayout(){this.layoutBreakPoints=this.layoutBreakPoints||new O(this),this.layoutBreakPoints.createBlockGroups()}adjustLayout(){var t;this.applyLayoutStateAppearance(),null===(t=this.layoutBreakPoints)||void 0===t||t.adjust()}}let G=class extends g{constructor(){super(),this._keyboardNavigator=null,this.loading=!1}willUpdate(t){t.has("loading")&&!this.loading&&this.initializeNavigator()}initializeNavigator(){this._keyboardNavigator||(this._keyboardNavigator=this.querySelector(`:scope > ${f}`)),this._keyboardNavigator.initialized||this._keyboardNavigator.initialize(this,new y(this._keyboardNavigator,this))}};t([b({type:Boolean,attribute:"loading"})],G.prototype,"loading",void 0),G=t([k("dxbl-ribbon-internal")],G);const N=new Map;function E(t,e,s){if(!t)return Promise.reject("failed");let i=N.get(t);i?i.update():(i=new W(t,s),i.initialize(),N.set(t,i));const o=t.querySelector(C)||t;return d(o),l((()=>{t.setAttribute("data-dx-ribbon-toolbar-loaded","")})),Promise.resolve("ok")}function L(t){return e(t,N),Array.from(N.keys()).forEach((t=>{n(t)&&e(t,N)})),Promise.resolve("ok");function e(t,e){if(!t)return;const s=t.querySelector(C);s&&u(s);const i=e.get(t);null==i||i.dispose(),e.delete(t)}}const I={init:E,dispose:L};export{I as default,L as dispose,E as init};
