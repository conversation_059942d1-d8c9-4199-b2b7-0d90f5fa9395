import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t,C as s,L as r}from"./baseChart-9211a850.js";import{P as o}from"./settings-c0192d16.js";import{e as i}from"./custom-element-267f9a21.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./disposable-d2c2d283.js";import"./devextreme-widget-wrapper-33881f73.js";import"./utils-b5b2c8a9.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";import"./events-a8fe5872.js";import"./custom-events-helper-e7f279d3.js";import"./create-after-timeout-fn-executor-38b3d79d.js";import"./client-component-style-helper-195fa7c3.js";let m=class extends t{getWidgetTypeName(){return s.pieChartName}createWidgetSettingsConverter(){return new o}getLegendItemType(){return r.point}getChartOnPointClickHandler(){const e=this.selectionController;return t=>{const s=t.target;this.onSeriesClick(s.series.index,s.data,s.tag),e.togglePointSelection(s)}}};m=e([i("dxbl-pie-chart")],m);export{m as DxPieChart};
