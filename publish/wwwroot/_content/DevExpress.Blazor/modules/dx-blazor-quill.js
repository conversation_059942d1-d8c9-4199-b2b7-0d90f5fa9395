import{g as t,c as e,a as r}from"./_commonjsHelpers-41cdd1e7.js";var n=e((function(t,e){var r;r=()=>(()=>{var t={1674:(t,e,r)=>{r.d(e,{Z:()=>p});var n=r(1233),o=r(3866),i=r(3398);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function u(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==a(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===a(o)?o:String(o)),n)}var o}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=f(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},l.apply(this,arguments)}function c(t,e){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},c(t,e)}function s(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=f(t);if(e){var o=f(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===a(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function f(t){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},f(t)}var p=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e)}(p,t);var e,r,n,a=s(p);function p(t,e){var r,n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{allowedTags:[]};return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,p),(n=a.call(this,t,e,o)).allowedTags=null!==(r=o.allowedTags)&&void 0!==r?r:[],n}return e=p,n=[{key:"keys",value:function(t){return(0,o.dj)(l(f(p),"keys",this),t,i.WT.attribute)}}],(r=[{key:"add",value:function(t,e){return o.Mx.call(this,l(f(p.prototype),"add",this),t,e)}},{key:"remove",value:function(t){return o.Mx.call(this,l(f(p.prototype),"remove",this),t)}},{key:"value",value:function(t){return o.Mx.call(this,l(f(p.prototype),"value",this),t)}},{key:"canAdd",value:function(t,e){return o.ud.call(this,l(f(p.prototype),"canAdd",this),t,e)}}])&&u(e.prototype,r),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),p}(n.Attributor)},3866:(t,e,r)=>{r.d(e,{Mx:()=>o,dj:()=>a,ud:()=>i});var n=r(3398);function o(t){var e=this.keyName;this.keyName=(0,n.mQ)(this.keyName);for(var r=arguments.length,o=new Array(r>1?r-1:0),i=1;i<r;i++)o[i-1]=arguments[i];var a=t.call.apply(t,[this].concat(o));return this.keyName=e,a}function i(t,e,r){return this.allowedTags.indexOf(e.tagName)>-1&&t.call(this,e,r)}function a(t,e,r){return t(e).map((function(t){return(0,n.jf)(e.tagName,t,r)}))}},4569:(t,e,r)=>{r.d(e,{Z:()=>p});var n=r(1233),o=r(3866),i=r(3398);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function u(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==a(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===a(o)?o:String(o)),n)}var o}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=f(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},l.apply(this,arguments)}function c(t,e){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},c(t,e)}function s(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=f(t);if(e){var o=f(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===a(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function f(t){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},f(t)}var p=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e)}(p,t);var e,r,n,a=s(p);function p(t,e){var r,n,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{allowedTags:[]};return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,p),(n=a.call(this,t,e,o)).allowedTags=null!==(r=o.allowedTags)&&void 0!==r?r:[],n}return e=p,n=[{key:"keys",value:function(t){return(0,o.dj)(l(f(p),"keys",this),t,i.WT.style)}}],(r=[{key:"add",value:function(t,e){return o.Mx.call(this,l(f(p.prototype),"add",this),t,e)}},{key:"remove",value:function(t){return o.Mx.call(this,l(f(p.prototype),"remove",this),t)}},{key:"value",value:function(t){return o.Mx.call(this,l(f(p.prototype),"value",this),t)}},{key:"canAdd",value:function(t,e){return o.ud.call(this,l(f(p.prototype),"canAdd",this),t,e)}}])&&u(e.prototype,r),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),p}(n.StyleAttributor)},3398:(t,e,r)=>{r.d(e,{WT:()=>c,jf:()=>f,mQ:()=>p});var n=r(19),o=r(8252);function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?a(Object(r),!0).forEach((function(e){l(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):a(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function l(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==i(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===i(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var c={attribute:"attr",style:"style"},s=u(u({},n.Sp.allowedTags.reduce((function(t,e){return t[e]={name:n.Sp.name,keyNamesSet:n.bc},t}),{})),o.Pw.allowedTags.reduce((function(t,e){return t[e]={name:o.Pw.name,keyNamesSet:o.fU},t}),{}));function f(t,e,r){var n=s[t];return n&&n.keyNamesSet.has(e)?"".concat(r).concat(n.name,"_").concat(e):e}function p(t){return t.replace(/([^]*_)/,"")}},6446:(t,e,r)=>{r.d(e,{i2:()=>P,qz:()=>_,E2:()=>N,ZP:()=>S});var n=r(9098),o=r.n(n),i=r(1233),a=r(4122),u=r(6603),l=r(8222),c=r(3398);function s(t,e,r,n){return r.map((r=>{const o=n?(0,c.jf)(t,r,n):r;return e.scroll.query(o,i.Scope.ATTRIBUTE)})).filter((t=>t instanceof i.Attributor)).reduce(((t,e)=>(t[e.attrName]=e,t)),{})}function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function h(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?p(Object(r),!0).forEach((function(e){var n,o,i;n=t,i=r[e],(o=v(o=e))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):p(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function y(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function d(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,v(n.key),n)}}function b(t,e,r){return e&&d(t.prototype,e),r&&d(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function v(t){var e=function(t,e){if("object"!==f(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===f(e)?e:String(e)}function m(){return m="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=j(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},m.apply(this,arguments)}function g(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&w(t,e)}function w(t,e){return w=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},w(t,e)}function O(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=j(t);if(e){var o=j(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function j(t){return j=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},j(t)}i.AttributorStore.prototype.build=function(){const{tagName:t}=this.domNode,e=i.Registry.find(this.domNode);if(null==e)return;const r=i.Attributor.keys(this.domNode),n=i.ClassAttributor.keys(this.domNode),o=i.StyleAttributor.keys(this.domNode);this.attributes={...s(t,e,r,c.WT.attribute),...s(t,e,n),...s(t,e,o,c.WT.style)}};var S=function(t){g(r,t);var e=O(r);function r(t,n){var o;return y(this,r),(o=e.call(this,t,n)).cache={},o}return b(r,[{key:"delta",value:function(){return null==this.cache.delta&&(this.cache.delta=_(this)),this.cache.delta}},{key:"deleteAt",value:function(t,e){m(j(r.prototype),"deleteAt",this).call(this,t,e),this.cache={}}},{key:"formatAt",value:function(t,e,n,o){e<=0||(this.scroll.query(n,i.Scope.BLOCK)?t+e===this.length()&&this.format(n,o):m(j(r.prototype),"formatAt",this).call(this,t,Math.min(e,this.length()-t-1),n,o),this.cache={})}},{key:"insertAt",value:function(t,e,n){if(null!=n)return m(j(r.prototype),"insertAt",this).call(this,t,e,n),void(this.cache={});if(0!==e.length){var o=e.split("\n"),i=o.shift();i.length>0&&(t<this.length()-1||null==this.children.tail?m(j(r.prototype),"insertAt",this).call(this,Math.min(t,this.length()-1),i):this.children.tail.insertAt(this.children.tail.length(),i),this.cache={});var a=this;o.reduce((function(t,e){return(a=a.split(t,!0)).insertAt(0,e),e.length}),t+i.length)}}},{key:"insertBefore",value:function(t,e){var n=this.children.head;e&&!this.domNode.contains(e.domNode)||m(j(r.prototype),"insertBefore",this).call(this,t,e),n instanceof a.Z&&n.remove(),this.cache={}}},{key:"length",value:function(){return null==this.cache.length&&(this.cache.length=m(j(r.prototype),"length",this).call(this)+1),this.cache.length}},{key:"moveChildren",value:function(t,e){m(j(r.prototype),"moveChildren",this).call(this,t,e),this.cache={}}},{key:"optimize",value:function(t){m(j(r.prototype),"optimize",this).call(this,t),this.cache={}}},{key:"path",value:function(t){return m(j(r.prototype),"path",this).call(this,t,!0)}},{key:"removeChild",value:function(t){m(j(r.prototype),"removeChild",this).call(this,t),this.cache={}}},{key:"split",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e&&(0===t||t>=this.length()-1)){var n=this.clone();return 0===t?(this.parent.insertBefore(n,this),this):(this.parent.insertBefore(n,this.next),n)}var o=m(j(r.prototype),"split",this).call(this,t,e);return this.cache={},o}}]),r}(i.BlockBlot);S.blotName="block",S.tagName="P",S.defaultChild=a.Z,S.allowedChildren=[a.Z,u.Z,i.EmbedBlot,l.Z];var P=function(t){g(r,t);var e=O(r);function r(){return y(this,r),e.apply(this,arguments)}return b(r,[{key:"attach",value:function(){m(j(r.prototype),"attach",this).call(this),this.attributes=new i.AttributorStore(this.domNode)}},{key:"delta",value:function(){return(new(o())).insert(this.value(),h(h({},this.formats()),this.attributes.values()))}},{key:"format",value:function(t,e){var r=this.scroll.query(t,i.Scope.BLOCK_ATTRIBUTE);null!=r&&this.attributes.attribute(r,e)}},{key:"formatAt",value:function(t,e,r,n){this.format(r,n)}},{key:"insertAt",value:function(t,e,n){if("string"==typeof e&&e.endsWith("\n")){var o=this.scroll.create(S.blotName);this.parent.insertBefore(o,0===t?this:this.next),o.insertAt(0,e.slice(0,-1))}else m(j(r.prototype),"insertAt",this).call(this,t,e,n)}}]),r}(i.EmbedBlot);function _(t){var e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return t.descendants(i.LeafBlot).reduce((function(t,r){return 0===r.length()?t:t.insert(r.value(),N(r,{},e))}),new(o())).insert("\n",N(t))}function N(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];return null==t?e:("function"==typeof t.formats&&(e=h(h({},e),t.formats()),r&&delete e["code-token"]),null==t.parent||"scroll"===t.parent.statics.blotName||t.parent.statics.scope!==t.statics.scope?e:N(t.parent,e,r))}P.scope=i.Scope.BLOCK_BLOT},4122:(t,e,r)=>{function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,(i=void 0,i=function(t,e){if("object"!==n(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o.key,"string"),"symbol"===n(i)?i:String(i)),o)}var i}function i(t,e){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},i(t,e)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,o=u(t);if(e){var i=u(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return function(t,e){if(e&&("object"===n(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function u(t){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},u(t)}r.d(e,{Z:()=>c});var l=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&i(t,e)}(l,t);var e,r,n,u=a(l);function l(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,l),u.apply(this,arguments)}return e=l,n=[{key:"value",value:function(){}}],(r=[{key:"optimize",value:function(){(this.prev||this.next)&&this.remove()}},{key:"length",value:function(){return 0}},{key:"value",value:function(){return""}}])&&o(e.prototype,r),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),l}(r(1233).EmbedBlot);l.blotName="break",l.tagName="BR";const c=l},3553:(t,e,r)=>{function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,(i=void 0,i=function(t,e){if("object"!==n(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o.key,"string"),"symbol"===n(i)?i:String(i)),o)}var i}function i(t,e){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},i(t,e)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,o=u(t);if(e){var i=u(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return function(t,e){if(e&&("object"===n(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function u(t){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},u(t)}r.d(e,{Z:()=>c});var l=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&i(t,e)}(l,t);var e,r,n,u=a(l);function l(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,l),u.apply(this,arguments)}return e=l,r&&o(e.prototype,r),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(r(1233).ContainerBlot);const c=l},3657:(t,e,r)=>{r.d(e,{Z:()=>p});var n=r(1233),o=r(8222);function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==i(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===i(o)?o:String(o)),n)}var o}function u(){return u="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=s(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},u.apply(this,arguments)}function l(t,e){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},l(t,e)}function c(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=s(t);if(e){var o=s(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===i(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function s(t){return s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},s(t)}var f=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&l(t,e)}(p,t);var e,r,i,f=c(p);function p(t,e,r){var n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,p),(n=f.call(this,t,e)).selection=r,n.textNode=document.createTextNode(p.CONTENTS),n.domNode.appendChild(n.textNode),n.savedLength=0,n}return e=p,i=[{key:"value",value:function(){}}],(r=[{key:"detach",value:function(){null!=this.parent&&this.parent.removeChild(this)}},{key:"format",value:function(t,e){if(0===this.savedLength){for(var r=this,o=0;null!=r&&r.statics.scope!==n.Scope.BLOCK_BLOT;)o+=r.offset(r.parent),r=r.parent;null!=r&&(this.savedLength=p.CONTENTS.length,r.optimize(),r.formatAt(o,p.CONTENTS.length,t,e),this.savedLength=0)}else u(s(p.prototype),"format",this).call(this,t,e)}},{key:"index",value:function(t,e){return t===this.textNode?0:u(s(p.prototype),"index",this).call(this,t,e)}},{key:"length",value:function(){return this.savedLength}},{key:"position",value:function(){return[this.textNode,this.textNode.data.length]}},{key:"remove",value:function(){u(s(p.prototype),"remove",this).call(this),this.parent=null}},{key:"restore",value:function(){if(this.selection.composing||null==this.parent)return null;for(var t=this.selection.getNativeRange();null!=this.domNode.lastChild&&this.domNode.lastChild!==this.textNode;)this.domNode.parentNode.insertBefore(this.domNode.lastChild,this.domNode);var e,r=this.prev instanceof o.Z?this.prev:null,n=r?r.length():0,i=this.next instanceof o.Z?this.next:null,a=i?i.text:"",u=this.textNode,l=u.data.split(p.CONTENTS).join("");if(u.data=p.CONTENTS,r)e=r,(l||i)&&(r.insertAt(r.length(),l+a),i&&i.remove());else if(i)e=i,i.insertAt(0,l);else{var c=document.createTextNode(l);e=this.scroll.create(c),this.parent.insertBefore(e,this)}if(this.remove(),t){var s=function(t,e){return r&&t===r.domNode?e:t===u?n+e-1:i&&t===i.domNode?n+l.length+e:null},f=s(t.start.node,t.start.offset),h=s(t.end.node,t.end.offset);if(null!==f&&null!==h)return{startNode:e.domNode,startOffset:f,endNode:e.domNode,endOffset:h}}return null}},{key:"update",value:function(t,e){var r=this;if(t.some((function(t){return"characterData"===t.type&&t.target===r.textNode}))){var n=this.restore();n&&(e.range=n)}}},{key:"value",value:function(){return""}}])&&a(e.prototype,r),i&&a(e,i),Object.defineProperty(e,"prototype",{writable:!1}),p}(n.EmbedBlot);f.blotName="cursor",f.className="ql-cursor",f.tagName="span",f.CONTENTS="\ufeff";const p=f},7452:(t,e,r)=>{r.d(e,{Z:()=>p});var n=r(1233),o=r(8222);function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==i(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===i(o)?o:String(o)),n)}var o}function u(){return u="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=s(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},u.apply(this,arguments)}function l(t,e){return l=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},l(t,e)}function c(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=s(t);if(e){var o=s(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===i(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function s(t){return s=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},s(t)}var f="\ufeff";const p=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&l(t,e)}(p,t);var e,r,n,i=c(p);function p(t,e){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,p),(r=i.call(this,t,e)).contentNode=document.createElement("span"),r.contentNode.setAttribute("contenteditable",!1),Array.from(r.domNode.childNodes).forEach((function(t){r.contentNode.appendChild(t)})),r.leftGuard=document.createTextNode(f),r.rightGuard=document.createTextNode(f),r.domNode.appendChild(r.leftGuard),r.domNode.appendChild(r.contentNode),r.domNode.appendChild(r.rightGuard),r}return e=p,(r=[{key:"index",value:function(t,e){return t===this.leftGuard?0:t===this.rightGuard?1:u(s(p.prototype),"index",this).call(this,t,e)}},{key:"restore",value:function(t){var e,r,n=t.data.split(f).join("");if(t===this.leftGuard)if(this.prev instanceof o.Z){var i=this.prev.length();this.prev.insertAt(i,n),e={startNode:this.prev.domNode,startOffset:i+n.length}}else r=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(r),this),e={startNode:r,startOffset:n.length};else t===this.rightGuard&&(this.next instanceof o.Z?(this.next.insertAt(0,n),e={startNode:this.next.domNode,startOffset:n.length}):(r=document.createTextNode(n),this.parent.insertBefore(this.scroll.create(r),this.next),e={startNode:r,startOffset:n.length}));return t.data=f,e}},{key:"update",value:function(t,e){var r=this;t.forEach((function(t){if("characterData"===t.type&&(t.target===r.leftGuard||t.target===r.rightGuard)){var n=r.restore(t.target);n&&(e.range=n)}}))}}])&&a(e.prototype,r),n&&a(e,n),Object.defineProperty(e,"prototype",{writable:!1}),p}(n.EmbedBlot)},6603:(t,e,r)=>{r.d(e,{Z:()=>h});var n=r(1233),o=r(4122),i=r(8222);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function u(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==a(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===a(o)?o:String(o)),n)}var o}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=f(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},l.apply(this,arguments)}function c(t,e){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},c(t,e)}function s(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=f(t);if(e){var o=f(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===a(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function f(t){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},f(t)}var p=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e)}(a,t);var e,r,o,i=s(a);function a(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a),i.apply(this,arguments)}return e=a,o=[{key:"compare",value:function(t,e){var r=a.order.indexOf(t),n=a.order.indexOf(e);return r>=0||n>=0?r-n:t===e?0:t<e?-1:1}}],(r=[{key:"formatAt",value:function(t,e,r,o){if(a.compare(this.statics.blotName,r)<0&&this.scroll.query(r,n.Scope.BLOT)){var i=this.isolate(t,e);o&&i.wrap(r,o)}else l(f(a.prototype),"formatAt",this).call(this,t,e,r,o)}},{key:"optimize",value:function(t){if(l(f(a.prototype),"optimize",this).call(this,t),this.parent instanceof a&&a.compare(this.statics.blotName,this.parent.statics.blotName)>0){var e=this.parent.isolate(this.offset(),this.length());this.moveChildren(e),e.wrap(this)}}}])&&u(e.prototype,r),o&&u(e,o),Object.defineProperty(e,"prototype",{writable:!1}),a}(n.InlineBlot);p.allowedChildren=[p,o.Z,n.EmbedBlot,i.Z],p.order=["cursor","inline","link","underline","strike","italic","bold","script","code"];const h=p},3272:(t,e,r)=>{r.d(e,{Z:()=>g});var n=r(1233),o=r(2069),i=r(6446),a=r(4122),u=r(3553),l=r(1969);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function s(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return f(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?f(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==c(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===c(o)?o:String(o)),n)}var o}function h(){return h="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=b(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},h.apply(this,arguments)}function y(t,e){return y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},y(t,e)}function d(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=b(t);if(e){var o=b(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===c(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function b(t){return b=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},b(t)}function v(t){return t instanceof i.ZP||t instanceof i.i2}var m=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&y(t,e)}(f,t);var e,r,u,c=d(f);function f(t,e,r){var n,o=r.emitter,i=r.toggleBlankClass;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,f),(n=c.call(this,t,e)).emitter=o,n.toggleBlankClass=i,n.batch=!1,n.optimize(),n.enable(),n.domNode.addEventListener("dragstart",(function(t){return n.handleDragStart(t)})),n}return e=f,r=[{key:"batchStart",value:function(){Array.isArray(this.batch)||(this.batch=[])}},{key:"batchEnd",value:function(){var t=this.batch;this.batch=!1,this.update(t)}},{key:"emitMount",value:function(t){this.emitter.emit(o.Z.events.SCROLL_BLOT_MOUNT,t)}},{key:"emitUnmount",value:function(t){this.emitter.emit(o.Z.events.SCROLL_BLOT_UNMOUNT,t)}},{key:"deleteAt",value:function(t,e){var r=s(this.line(t),2),n=r[0],o=r[1],u=s(this.line(t+e),1)[0];h(b(f.prototype),"deleteAt",this).call(this,t,e),null!=u&&n!==u&&o>0&&(n instanceof i.i2||u instanceof i.i2||(n instanceof l.zW||u instanceof l.zW)&&n.parent!==u.parent||(n.moveChildren(u,u.children.head instanceof a.Z?null:u.children.head),n.remove())),this.optimize()}},{key:"enable",value:function(){this.domNode.setAttribute("contenteditable",!(arguments.length>0&&void 0!==arguments[0])||arguments[0])}},{key:"formatAt",value:function(t,e,r,n){h(b(f.prototype),"formatAt",this).call(this,t,e,r,n),this.optimize()}},{key:"handleDragStart",value:function(t){t.preventDefault()}},{key:"insertAt",value:function(t,e,r){if(t>=this.length())if(null==r||null==this.scroll.query(e,n.Scope.BLOCK)){var o=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(o),null==r&&e.endsWith("\n")?o.insertAt(0,e.slice(0,-1),r):o.insertAt(0,e,r)}else{var i=this.scroll.create(e,r);this.appendChild(i)}else h(b(f.prototype),"insertAt",this).call(this,t,e,r);this.optimize()}},{key:"insertBefore",value:function(t,e){if(t.statics.scope===n.Scope.INLINE_BLOT){var r=this.scroll.create(this.statics.defaultChild.blotName);r.appendChild(t),h(b(f.prototype),"insertBefore",this).call(this,r,e)}else h(b(f.prototype),"insertBefore",this).call(this,t,e)}},{key:"isEnabled",value:function(){return"true"===this.domNode.getAttribute("contenteditable")}},{key:"leaf",value:function(t){return this.path(t).pop()||[null,-1]}},{key:"line",value:function(t){return t===this.length()?this.line(t-1):this.descendant(v,t)}},{key:"lines",value:function(){return function t(e,r,o){var i=[],a=o;return e.children.forEachAt(r,o,(function(e,r,o){v(e)?i.push(e):e instanceof n.ContainerBlot&&(i=i.concat(t(e,r,a))),a-=o})),i}(this,arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE)}},{key:"optimize",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.batch||(h(b(f.prototype),"optimize",this).call(this,t,e),t.length>0&&this.emitter.emit(o.Z.events.SCROLL_OPTIMIZE,t,e))}},{key:"path",value:function(t){return h(b(f.prototype),"path",this).call(this,t).slice(1)}},{key:"remove",value:function(){}},{key:"update",value:function(t){var e=this;if(this.batch)Array.isArray(t)&&(this.batch=this.batch.concat(t),this.toggleBlankClass());else{var r=o.Z.sources.USER;"string"==typeof t&&(r=t),Array.isArray(t)||(t=this.observer.takeRecords()),(t=t.filter((function(t){var r=e.find(t.target,!0);return r&&r.scroll===e}))).length>0&&this.emitter.emit(o.Z.events.SCROLL_BEFORE_UPDATE,r,t),h(b(f.prototype),"update",this).call(this,t.concat([])),t.length>0&&this.emitter.emit(o.Z.events.SCROLL_UPDATE,r,t)}}}],r&&p(e.prototype,r),u&&p(e,u),Object.defineProperty(e,"prototype",{writable:!1}),f}(n.ScrollBlot);m.blotName="scroll",m.className="ql-editor",m.tagName="DIV",m.defaultChild=i.ZP,m.allowedChildren=[i.ZP,i.i2,u.Z];const g=m},8222:(t,e,r)=>{function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,(i=void 0,i=function(t,e){if("object"!==n(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o.key,"string"),"symbol"===n(i)?i:String(i)),o)}var i}function i(t,e){return i=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},i(t,e)}function a(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,o=u(t);if(e){var i=u(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return function(t,e){if(e&&("object"===n(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function u(t){return u=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},u(t)}r.d(e,{Z:()=>l,b:()=>c});var l=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&i(t,e)}(l,t);var e,r,n,u=a(l);function l(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,l),u.apply(this,arguments)}return e=l,r&&o(e.prototype,r),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(r(1233).TextBlot);function c(t){return t.replace(/[&<>"']/g,(function(t){return{"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}[t]}))}},2432:(t,e,r)=>{r.d(e,{default:()=>v});var n=r(281),o=r(6446),i=r(4122),a=r(3553),u=r(3657),l=r(7452),c=r(6603),s=r(3272),f=r(8222),p=r(5635),h=r(2613),y=r(3071),d=r(3859),b=r(990);n.ZP.register({"blots/block":o.ZP,"blots/block/embed":o.i2,"blots/break":i.Z,"blots/container":a.Z,"blots/cursor":u.Z,"blots/embed":l.Z,"blots/inline":c.Z,"blots/scroll":s.Z,"blots/text":f.Z,"modules/clipboard":p.ZP,"modules/history":h.Z,"modules/keyboard":y.ZP,"modules/uploader":d.Z,"modules/input":b.Z});const v=n.ZP},6229:(t,e,r)=>{r.d(e,{Z:()=>u});var n=r(7452),o=r(2069);function i(t){return i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}function a(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==i(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==i(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===i(o)?o:String(o)),n)}var o}const u=function(){function t(e,r){var n=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scroll=e,this.emitter=r,this.isComposing=!1,e.domNode.addEventListener("compositionstart",(function(t){n.isComposing||n.handleCompositionStart(t)})),e.domNode.addEventListener("compositionend",(function(t){n.isComposing&&n.handleCompositionEnd(t)}))}var e,r,i;return e=t,(r=[{key:"isCompositionInProgress",value:function(){return this.isComposing}},{key:"handleCompositionStart",value:function(t){var e=t.target instanceof Node?this.scroll.find(t.target,!0):null;!e||e instanceof n.Z||(this.emitter.emit(o.Z.events.COMPOSITION_BEFORE_START,t),this.scroll.batchStart(),this.emitter.emit(o.Z.events.COMPOSITION_START,t),this.isComposing=!0)}},{key:"handleCompositionEnd",value:function(t){this.emitter.emit(o.Z.events.COMPOSITION_BEFORE_END,t),this.scroll.batchEnd(),this.emitter.emit(o.Z.events.COMPOSITION_END,t),this.isComposing=!1}}])&&a(e.prototype,r),i&&a(e,i),Object.defineProperty(e,"prototype",{writable:!1}),t}()},2861:(t,e,r)=>{r.d(e,{Z:()=>I});var n=r(8805),o=r.n(n),i=r(2722),a=r.n(i),u=r(732),l=r.n(u),c=r(9098),s=r.n(c),f=r(1233),p=r(7800),h=r(3657),y=r(6446),d=r(4122),b=r(8222),v=r(5874);function m(t){return E(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||_(t)||P()}function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach((function(e){O(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function O(t,e,r){return(e=x(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function j(t){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(t)}function S(t,e){return E(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||_(t,e)||P()}function P(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(t,e){if(t){if("string"==typeof t)return N(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?N(t,e):void 0}}function N(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function E(t){if(Array.isArray(t))return t}function k(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,x(n.key),n)}}function x(t){var e=function(t,e){if("object"!==j(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===j(e)?e:String(e)}var T=/^[ -~]*$/,A=function(){function t(e){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.scroll=e,this.delta=this.getDelta(),this.immediateFormats=new Set}var e,r,n;return e=t,r=[{key:"addImmediateFormat",value:function(t){this.immediateFormats.add(t)}},{key:"applyDelta",value:function(t){var e=this;this.scroll.update();var r=this.scroll.length();this.scroll.batchStart();var n=function(t){return t.reduce((function(t,e){if("string"==typeof e.insert){var r=e.insert.replace(/\r\n/g,"\n").replace(/\r/g,"\n");return t.insert(r,e.attributes)}return t.push(e)}),new(s()))}(t),o=new(s());return n.reduce((function(t,n){var i=c.Op.length(n),a=n.attributes||{},u=!1;if(null!=n.insert){if(o.retain(i),"string"==typeof n.insert){var s=n.insert;u=!s.endsWith("\n")&&(r<=t||e.scroll.descendant(y.i2,t)[0]),e.scroll.insertAt(t,s);var p=S(e.scroll.line(t),2),h=p[0],d=p[1],b=l()({},(0,y.E2)(h));if(h instanceof y.ZP){var v=S(h.descendant(f.LeafBlot,d),1)[0];b=l()(b,(0,y.E2)(v))}a=c.AttributeMap.diff(b,a)||{}}else if("object"===j(n.insert)){var m=Object.keys(n.insert)[0];if(null==m)return t;u=null!=e.scroll.query(m,f.Scope.INLINE)&&(r<=t||e.scroll.descendant(y.i2,t)[0]),e.scroll.insertAt(t,m,n.insert[m])}r+=i}else o.push(n);var g=Object.keys(a);e.immediateFormats.forEach((function(r){g.indexOf(r)>-1&&(e.scroll.formatAt(t,i,r,a[r]),delete a[r])})),Object.keys(a).forEach((function(r){e.scroll.formatAt(t,i,r,a[r])}));var w=u?1:0;return r+=w,o.delete(w),t+i+w}),0),o.reduce((function(t,r){return"number"==typeof r.delete?(e.scroll.deleteAt(t,r.delete),t):t+c.Op.length(r)}),0),this.scroll.batchEnd(),this.scroll.optimize(),this.update(n)}},{key:"deleteText",value:function(t,e){return this.scroll.deleteAt(t,e),this.update((new(s())).retain(t).delete(e))}},{key:"formatLine",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.scroll.update(),Object.keys(n).forEach((function(o){r.scroll.lines(t,Math.max(e,1)).forEach((function(t){t.format(o,n[o])}))})),this.scroll.optimize();var i=(new(s())).retain(t).retain(e,o()(n));return this.update(i)}},{key:"formatText",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};Object.keys(n).forEach((function(o){r.scroll.formatAt(t,e,o,n[o])}));var i=(new(s())).retain(t).retain(e,o()(n));return this.update(i)}},{key:"getContents",value:function(t,e){return this.delta.slice(t,t+e)}},{key:"getDelta",value:function(){return this.scroll.lines().reduce((function(t,e){return t.concat(e.delta())}),new(s()))}},{key:"getFormat",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,r=[],n=[];0===e?this.scroll.path(t).forEach((function(t){var e=S(t,1)[0];e instanceof y.ZP?r.push(e):e instanceof f.LeafBlot&&n.push(e)})):(r=this.scroll.lines(t,e),n=this.scroll.descendants(f.LeafBlot,t,e));var o=S([r,n].map((function(t){if(0===t.length)return{};for(var e=(0,y.E2)(t.shift());Object.keys(e).length>0;){var r=t.shift();if(null==r)return e;e=Z((0,y.E2)(r),e)}return e})),2);return n=o[1],w(w({},r=o[0]),n)}},{key:"getHTML",value:function(t,e){var r=S(this.scroll.line(t),2),n=r[0],o=r[1];return n.length()>o+e?C(n,o,e,!0):C(this.scroll,t,e,!0)}},{key:"getText",value:function(t,e){return this.getContents(t,e).filter((function(t){return"string"==typeof t.insert})).map((function(t){return t.insert})).join("")}},{key:"insertEmbed",value:function(t,e,r){return this.scroll.insertAt(t,e,r),this.update((new(s())).retain(t).insert(O({},e,r)))}},{key:"insertText",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return e=e.replace(/\r\n/g,"\n").replace(/\r/g,"\n"),this.scroll.insertAt(t,e),Object.keys(n).forEach((function(o){r.scroll.formatAt(t,e.length,o,n[o])})),this.update((new(s())).retain(t).insert(e,o()(n)))}},{key:"isBlank",value:function(){if(0===this.scroll.children.length)return!0;if(arguments.length>0&&void 0!==arguments[0]&&arguments[0])return!1;if(this.scroll.children.length>1)return!1;var t=this.scroll.children.head;return t.statics.blotName===y.ZP.blotName&&!(t.children.length>1)&&t.children.head instanceof d.Z}},{key:"removeFormat",value:function(t,e){var r=this.getText(t,e),n=S(this.scroll.line(t+e),2),o=n[0],i=n[1],a=0,u=new(s());null!=o&&(a=o.length()-i,u=o.delta().slice(i,i+a-1).insert("\n"));var l=this.getContents(t,e+a).diff((new(s())).insert(r).concat(u)),c=(new(s())).retain(t).concat(l);return this.applyDelta(c)}},{key:"update",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:void 0,n=this.delta;if(1===e.length&&"characterData"===e[0].type&&e[0].target.data.match(T)&&this.scroll.find(e[0].target)){var o=this.scroll.find(e[0].target),i=(0,y.E2)(o),u=o.offset(this.scroll),l=e[0].oldValue.replace(h.Z.CONTENTS,""),c=(new(s())).insert(l),f=(new(s())).insert(o.value()),p=r&&{oldRange:q(r.oldRange,-u),newRange:q(r.newRange,-u)};t=(new(s())).retain(u).concat(c.diff(f,p)).reduce((function(t,e){return e.insert?t.insert(e.insert,i):t.push(e)}),new(s())),this.delta=n.compose(t)}else this.delta=this.getDelta(),t&&a()(n.compose(t),this.delta)||this.deltaContainsRetain(n)||(t=n.diff(this.delta,r));return t}},{key:"deltaContainsRetain",value:function(t){return t.ops.some((function(t){return(0,v.Z)(t.retain)}))}}],r&&k(e.prototype,r),n&&k(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function R(t,e,r){if(0===t.length){var n=S(L(r.pop()),1)[0];return e<=0?"</li></".concat(n,">"):"</li></".concat(n,">").concat(R([],e-1,r))}var o=m(t),i=o[0],a=i.child,u=i.offset,l=i.length,c=i.indent,s=i.type,f=o.slice(1),p=S(L(s,a),2),h=p[0],y=p[1];if(c>e)return r.push(s),c===e+1?"<".concat(h,"><li").concat(y,">").concat(C(a,u,l)).concat(R(f,c,r)):"<".concat(h,"><li>").concat(R(t,e+1,r));if(c===e&&s===r[r.length-1])return"</li><li".concat(y,">").concat(C(a,u,l)).concat(R(f,c,r));var d=S(L(r.pop()),1);return"</li></".concat(d[0],">").concat(R(t,e-1,r))}function C(t,e,r){var n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];if("function"==typeof t.html)return t.html(e,r);if(t instanceof b.Z)return(0,b.b)(t.value().slice(e,e+r));if(t.children){if("list-container"===t.statics.blotName){var o=[];return t.children.forEachAt(e,r,(function(t,e,r){var n=t.formats();o.push({child:t,offset:e,length:r,indent:n.indent||0,type:n.list})})),R(o,-1,[])}var i=[];if(t.children.forEachAt(e,r,(function(t,e,r){i.push(C(t,e,r))})),function(t,e){1===t.length&&t.head instanceof d.Z&&e.push("<br>")}(t.children,i),n||"list"===t.statics.blotName)return i.join("");var a=function(t){var e=t.domNode.cloneNode(!0);return function(t,e){var r;return["tableCellLine","tableHeaderCellLine","tableCell","tableHeaderCell"].includes(t.statics.blotName)&&((r=e).classList.remove(t.statics.className),0===r.classList.length&&r.removeAttribute("class")),e}(t,e)}(t),u=S(a.outerHTML.split(">".concat(a.innerHTML,"<")),2),l=u[0],c=u[1];return 0===l.indexOf("<table")?"".concat(l.replace(/(\sdata-.+?=["'].*?["'])/g,""),">").concat(i.join("").replace(/(\sdata-table.+?=["'].*?["'])/g,""),"<").concat(c):"".concat(l,">").concat(i.join(""),"<").concat(c)}return t.domNode.outerHTML}function Z(t,e){return Object.keys(e).reduce((function(r,n){return null==t[n]||(e[n]===t[n]?r[n]=e[n]:Array.isArray(e[n])?e[n].indexOf(t[n])<0&&(r[n]=e[n].concat([t[n]])):r[n]=[e[n],t[n]]),r}),{})}function L(t,e){var r="ordered"===t?"ol":"ul",n=e?"".concat(function(t){var e=t.domNode;if(!e.hasAttributes())return"";for(var r=e.attributes,n=" ",o=0;o<r.length;o+=1){var i=r[o].name,a=r[o].value;"class"===i&&(a=B(a)),a.length&&-1===i.indexOf("data-")&&(n+="".concat(i,'="').concat(a,'"'))}return n.length>1?n:""}(e)):"";switch(t){case"checked":return[r,"".concat(n,' data-list="checked"')];case"unchecked":return[r,"".concat(n,' data-list="unchecked"')];default:return[r,n]}}function B(t){return t.replace(/ql-indent-\d/g,"").trim()}function q(t,e){return new p.e(t.index+e,t.length)}const I=A},2069:(t,e,r)=>{r.d(e,{Z:()=>d});var n=r(3034),o=r.n(n),i=r(8034),a=r(2598);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==u(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===u(o)?o:String(o)),n)}var o}function c(){return c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=p(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},c.apply(this,arguments)}function s(t,e){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},s(t,e)}function f(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=p(t);if(e){var o=p(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function p(t){return p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},p(t)}var h=(0,r(3122).Z)("quill:events");(0,i.Z)()&&["selectionchange","mousedown","mouseup","click"].forEach((function(t){document.addEventListener(t,(function(){for(var t,e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];var o=r[0],i=null==o||null===(t=o.target)||void 0===t?void 0:t.shadowRoot,u=(null!=i?i:document).querySelectorAll(".ql-container");Array.from(u).forEach((function(t){var e,n=a.Z.get(t);n&&n.emitter&&(e=n.emitter).handleDOM.apply(e,r)}))}))}));var y=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&s(t,e)}(i,t);var e,r,n,o=f(i);function i(){var t;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),(t=o.call(this)).listeners={},t.on("error",h.error),t}return e=i,r=[{key:"emit",value:function(){for(var t,e,r=arguments.length,n=new Array(r),o=0;o<r;o++)n[o]=arguments[o];(t=h.log).call.apply(t,[h].concat(n)),(e=c(p(i.prototype),"emit",this)).call.apply(e,[this].concat(n))}},{key:"handleDOM",value:function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),n=1;n<e;n++)r[n-1]=arguments[n];(this.listeners[t.type]||[]).forEach((function(e){var n=e.node,o=e.handler;(t.target===n||n.contains(t.target))&&o.apply(void 0,[t].concat(r))}))}},{key:"listenDOM",value:function(t,e,r){this.listeners[t]||(this.listeners[t]=[]),this.listeners[t].push({node:e,handler:r})}}],r&&l(e.prototype,r),n&&l(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(o());y.events={EDITOR_CHANGE:"editor-change",SCROLL_BEFORE_UPDATE:"scroll-before-update",SCROLL_BLOT_MOUNT:"scroll-blot-mount",SCROLL_BLOT_UNMOUNT:"scroll-blot-unmount",SCROLL_OPTIMIZE:"scroll-optimize",SCROLL_UPDATE:"scroll-update",SELECTION_CHANGE:"selection-change",TEXT_CHANGE:"text-change",CONTENT_SETTED:"content-setted",COMPOSITION_BEFORE_START:"composition-before-start",COMPOSITION_START:"composition-start",COMPOSITION_BEFORE_END:"composition-before-end",COMPOSITION_END:"composition-end"},y.sources={API:"api",SILENT:"silent",USER:"user"};const d=y},2598:(t,e,r)=>{r.d(e,{Z:()=>n});const n=new WeakMap},3122:(t,e,r)=>{r.d(e,{Z:()=>u});var n=["error","warn","log","info"],o="warn";function i(t){if(n.indexOf(t)<=n.indexOf(o)){for(var e,r=arguments.length,i=new Array(r>1?r-1:0),a=1;a<r;a++)i[a-1]=arguments[a];(e=console)[t].apply(e,i)}}function a(t){return n.reduce((function(e,r){return e[r]=i.bind(console,r,t),e}),{})}i.level=a.level=function(t){o=t};const u=a},7094:(t,e,r)=>{function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,(i=void 0,i=function(t,e){if("object"!==n(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o.key,"string"),"symbol"===n(i)?i:String(i)),o)}var i}r.d(e,{Z:()=>c});var i,a,u,l=(i=function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.quill=e,this.options=r},a&&o(i.prototype,a),u&&o(i,u),Object.defineProperty(i,"prototype",{writable:!1}),i);l.DEFAULTS={};const c=l},281:(t,e,r)=>{r.d(e,{ZP:()=>k});var n=r(9098),o=r.n(n),i=r(8805),a=r.n(i),u=r(732),l=r.n(u),c=r(1233),s=r(2861),f=r(2069),p=r(7094),h=r(7800),y=r(2598),d=r(3122),b=r(1690),v=r(5874),m=r(6229);function g(t){return g="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},g(t)}function w(t,e,r){return(e=P(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function O(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return j(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?j(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function j(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function S(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,P(n.key),n)}}function P(t){var e=function(t,e){if("object"!==g(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==g(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===g(e)?e:String(e)}var _=(0,d.Z)("quill"),N="style-data-key",E=new c.Registry;c.ParentBlot.uiClass="ql-ui";var k=function(){function t(e){var r=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=x(e,n),this.container=this.options.container,null==this.container)return _.error("Invalid Quill container",e);this.options.debug&&t.debug(this.options.debug);var o=this.container.innerHTML.trim();this.container.classList.add("ql-container"),this.container.innerHTML="",y.Z.set(this.container,this),this.root=this.addContainer("ql-editor"),this.root.classList.add("ql-blank"),this.scrollingContainer=this.options.scrollingContainer||this.root,this.emitter=new f.Z;var i=this.options.registry.query(c.ScrollBlot.blotName);this.scroll=new i(this.options.registry,this.root,{emitter:this.emitter,toggleBlankClass:this.toggleBlankClass.bind(this)}),this.editor=new s.Z(this.scroll),this.composition=new m.Z(this.scroll,this.emitter),this.selection=new h.Z(this.scroll,this.emitter,this.composition),this.theme=new this.options.theme(this,this.options),this.keyboard=this.theme.addModule("keyboard"),this.clipboard=this.theme.addModule("clipboard"),this.history=this.theme.addModule("history"),this.uploader=this.theme.addModule("uploader"),this.theme.addModule("input"),this.theme.init(),this.emitter.on(f.Z.events.EDITOR_CHANGE,(function(t){t===f.Z.events.TEXT_CHANGE&&r.toggleBlankClass()})),this.emitter.on(f.Z.events.SCROLL_UPDATE,(function(t,e){var n=r.selection.lastRange,o=O(r.selection.getRange(),1)[0],i=n&&o?{oldRange:n,newRange:o}:void 0;T.call(r,(function(){return r.editor.update(null,e,i)}),t)})),this.setContents(this.getInitialContent(o)),this.history.clear(),this.options.placeholder&&this.root.setAttribute("data-placeholder",this.options.placeholder),this.options.readOnly&&this.disable(),this.allowReadOnlyEdits=!1}var e,r,n;return e=t,r=[{key:"getInitialContent",value:function(t){return this.clipboard.convert({html:"".concat(t,"<p><br></p>"),text:"\n"})}},{key:"toggleBlankClass",value:function(){var t=this.composition.isCompositionInProgress();this.root.classList.toggle("ql-blank",this.editor.isBlank(t))}},{key:"addContainer",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if("string"==typeof t){var r=t;(t=document.createElement("div")).classList.add(r)}return this.container.insertBefore(t,e),t}},{key:"blur",value:function(){this.selection.setRange(null)}},{key:"deleteText",value:function(t,e,r){var n=this,o=O(A(t,e,r),4);return T.call(this,(function(){return n.editor.deleteText(t,e)}),r=o[3],t=o[0],-1*(e=o[1]))}},{key:"disable",value:function(){this.enable(!1)}},{key:"editReadOnly",value:function(t){this.allowReadOnlyEdits=!0;var e=t();return this.allowReadOnlyEdits=!1,e}},{key:"enable",value:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.scroll.enable(t),this.container.classList.toggle("ql-disabled",!t)}},{key:"focus",value:function(){var t=this.scrollingContainer.scrollTop;this.selection.focus(),this.scrollingContainer.scrollTop=t,this.scrollIntoView()}},{key:"format",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:f.Z.sources.API;return this.applyCompositionChanges(),T.call(this,(function(){var n=r.getSelection(!0),i=new(o());if(null==n)return i;if(r.scroll.query(t,c.Scope.BLOCK))i=r.editor.formatLine(n.index,n.length,w({},t,e));else{if(0===n.length)return r.selection.format(t,e),i;i=r.editor.formatText(n.index,n.length,w({},t,e))}return r.setSelection(n,f.Z.sources.SILENT),i}),n)}},{key:"formatLine",value:function(t,e,r,n,o){var i,a=this,u=O(A(t,e,r,n,o),4);return e=u[1],i=u[2],T.call(this,(function(){return a.editor.formatLine(t,e,i)}),o=u[3],t=u[0],0)}},{key:"formatText",value:function(t,e,r,n,o){var i,a=this,u=O(A(t,e,r,n,o),4);return e=u[1],i=u[2],T.call(this,(function(){return a.editor.formatText(t,e,i)}),o=u[3],t=u[0],0)}},{key:"getBounds",value:function(t){var e;e="number"==typeof t?this.selection.getBounds(t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:0):this.selection.getBounds(t.index,t.length);var r=this.container.getBoundingClientRect();return{bottom:e.bottom-r.top,height:e.height,left:e.left-r.left,right:e.right-r.left,top:e.top-r.top,width:e.width}}},{key:"getContents",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t,r=O(A(t,e),2);return this.editor.getContents(t=r[0],e=r[1])}},{key:"getFormat",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.getSelection(!0),e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return(0,v.Z)(t)?"number"==typeof t?this.editor.getFormat(t,e):this.editor.getFormat(t.index,t.length):{}}},{key:"getIndex",value:function(t){return t.offset(this.scroll)}},{key:"getLength",value:function(){return this.scroll.length()}},{key:"getLeaf",value:function(t){return this.scroll.leaf(t)}},{key:"getLine",value:function(t){return this.scroll.line(t)}},{key:"getLines",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MAX_VALUE;return"number"!=typeof t?this.scroll.lines(t.index,t.length):this.scroll.lines(t,e)}},{key:"getModule",value:function(t){return this.theme.modules[t]}},{key:"getSelection",value:function(){return arguments.length>0&&void 0!==arguments[0]&&arguments[0]&&this.focus(),this.update(),this.selection.getRange()[0]}},{key:"getSemanticHTML",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t,r=O(A(t,e),2);return this.editor.getHTML(t=r[0],e=r[1])}},{key:"getText",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.getLength()-t,r=O(A(t,e),2);return this.editor.getText(t=r[0],e=r[1])}},{key:"hasFocus",value:function(){return this.selection.hasFocus()}},{key:"insertEmbed",value:function(e,r,n){var o=this;return T.call(this,(function(){return o.editor.insertEmbed(e,r,n)}),arguments.length>3&&void 0!==arguments[3]?arguments[3]:t.sources.API,e)}},{key:"insertText",value:function(t,e,r,n,o){var i,a=this,u=O(A(t,0,r,n,o),4);return i=u[2],T.call(this,(function(){return a.editor.insertText(t,e,i)}),o=u[3],t=u[0],e.length)}},{key:"isEnabled",value:function(){return this.scroll.isEnabled()}},{key:"off",value:function(){var t;return(t=this.emitter).off.apply(t,arguments)}},{key:"on",value:function(){var t;return(t=this.emitter).on.apply(t,arguments)}},{key:"once",value:function(){var t;return(t=this.emitter).once.apply(t,arguments)}},{key:"removeFormat",value:function(t,e,r){var n=this,o=O(A(t,e,r),4);return e=o[1],T.call(this,(function(){return n.editor.removeFormat(t,e)}),r=o[3],t=o[0])}},{key:"scrollIntoView",value:function(){this.selection.scrollIntoView(this.scrollingContainer)}},{key:"setContents",value:function(e){var r=this;return T.call(this,(function(){e=new(o())(e);var n=r.getLength(),i=r.editor.deleteText(0,n),a=r.editor.applyDelta(e),u=r.editor.deleteText(r.getLength()-1,1);return r.emitter.emit(t.events.CONTENT_SETTED),i.compose(a).compose(u)}),arguments.length>1&&void 0!==arguments[1]?arguments[1]:f.Z.sources.API)}},{key:"setSelection",value:function(e,r,n){if(null==e)this.selection.setRange(null,r||t.sources.API);else{var o=O(A(e,r,n),4);e=o[0],r=o[1],n=o[3],this.selection.setRange(new h.e(Math.max(0,e),r),n),n!==f.Z.sources.SILENT&&this.selection.scrollIntoView(this.scrollingContainer)}}},{key:"setText",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f.Z.sources.API,r=(new(o())).insert(t);return this.setContents(r,e)}},{key:"update",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:f.Z.sources.USER,e=this.scroll.update(t);return this.selection.update(t),e}},{key:"updateContents",value:function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:f.Z.sources.API;return T.call(this,(function(){return t=new(o())(t),e.editor.applyDelta(t,r)}),r,!0)}},{key:"applyCompositionChanges",value:function(){this.composition.handleCompositionEnd({})}}],n=[{key:"debug",value:function(t){!0===t&&(t="log"),d.Z.level(t)}},{key:"find",value:function(t){return y.Z.get(t)||E.find(t)}},{key:"import",value:function(t){return null==this.imports[t]&&_.error("Cannot import ".concat(t,". Are you sure it was registered?")),this.imports[t]}},{key:"register",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if("string"!=typeof t){var o=t.attrName||t.blotName;"string"==typeof o?this.register("formats/".concat(o),t,e):Object.keys(t).forEach((function(n){r.register(n,t[n],e)}))}else null==this.imports[t]||n||_.warn("Overwriting ".concat(t," with"),e),this.imports[t]=e,0!==t.indexOf("blots/")&&0!==t.indexOf("formats/")||"abstract"===e.blotName||E.register(e),"function"==typeof e.register&&e.register(E)}}],r&&S(e.prototype,r),n&&S(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();function x(t,e){if((e=l()({container:t,modules:{clipboard:!0,keyboard:!0,history:!0,uploader:!0}},e)).theme&&e.theme!==k.DEFAULTS.theme){if(e.theme=k.import("themes/".concat(e.theme)),null==e.theme)throw new Error("Invalid theme ".concat(e.theme,". Did you register it?"))}else e.theme=b.Z;var r=a()(e.theme.DEFAULTS);[r,e].forEach((function(t){t.modules=t.modules||{},Object.keys(t.modules).forEach((function(e){!0===t.modules[e]&&(t.modules[e]={})}))}));var n=Object.keys(r.modules).concat(Object.keys(e.modules)).reduce((function(t,e){var r=k.import("modules/".concat(e));return null==r?_.error("Cannot load ".concat(e," module. Are you sure you registered it?")):t[e]=r.DEFAULTS||{},t}),{});return null!=e.modules&&e.modules.toolbar&&e.modules.toolbar.constructor!==Object&&(e.modules.toolbar={container:e.modules.toolbar}),e=l()({},k.DEFAULTS,{modules:n},r,e),["bounds","container","scrollingContainer"].forEach((function(t){"string"==typeof e[t]&&(e[t]=document.querySelector(e[t]))})),e.modules=Object.keys(e.modules).reduce((function(t,r){return e.modules[r]&&(t[r]=e.modules[r]),t}),{}),e}function T(t,e,r,n){if(!this.isEnabled()&&e===f.Z.sources.USER&&!this.allowReadOnlyEdits)return new(o());var i=null==r?null:this.getSelection(),a=this.editor.delta,u=t();if(null!=i&&(!0===r&&(r=i.index),null==n?i=R(i,u,e):0!==n&&(i=R(i,r,n,e)),this.setSelection(i,f.Z.sources.SILENT)),u.length()>0){var l,c,s=[f.Z.events.TEXT_CHANGE,u,a,e];(l=this.emitter).emit.apply(l,[f.Z.events.EDITOR_CHANGE].concat(s)),e!==f.Z.sources.SILENT&&(c=this.emitter).emit.apply(c,s)}return u}function A(t,e,r,n,o){var i={};return"number"==typeof t.index&&"number"==typeof t.length?"number"!=typeof e?(o=n,n=r,r=e,e=t.length,t=t.index):(e=t.length,t=t.index):"number"!=typeof e&&(o=n,n=r,r=e,e=0),"object"===g(r)?(i=r,o=n):"string"==typeof r&&(null!=n?i[r]=n:o=r),[t,e,i,o=o||f.Z.sources.API]}function R(t,e,r,n){if(null==t)return null;var i,a;if(e instanceof o()){var u=O([t.index,t.index+t.length].map((function(t){return e.transformPosition(t,n!==f.Z.sources.USER)})),2);i=u[0],a=u[1]}else{var l=O([t.index,t.index+t.length].map((function(t){return t<e||t===e&&n===f.Z.sources.USER?t:r>=0?t+r:Math.max(e,t+r)})),2);i=l[0],a=l[1]}return new h.e(i,a-i)}k.DEFAULTS={bounds:null,modules:{},placeholder:"",readOnly:!1,registry:E,scrollingContainer:null,theme:"default"},k.events=f.Z.events,k.sources=f.Z.sources,k.version="1.7.1",k.MS_LIST_DATA_KEY="mso-list-data",k.replaceStyleAttribute=function(t){return t.replace(/(?:(<[a-z0-9]+\s*))([\s\S]*?)(>|\/>)/gi,(function(t,e,r,n){return e+r.replace(/style\s*=/gi,"".concat(N,"="))+n}))},k.restoreStyleAttribute=function(t){t.querySelectorAll("[".concat(N,"]")).forEach((function(t){var e=t.getAttribute(N);t.style=e,t.setAttribute(k.MS_LIST_DATA_KEY,e),t.removeAttribute(N)}))},k.imports={delta:o(),parchment:c,"core/module":p.Z,"core/theme":b.Z}},7800:(t,e,r)=>{r.d(e,{Z:()=>w,e:()=>g});var n=r(1233),o=r(8805),i=r.n(o),a=r(2722),u=r.n(a),l=r(2069);function c(t){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},c(t)}function s(t){return function(t){if(Array.isArray(t))return h(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||p(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function f(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||p(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function p(t,e){if(t){if("string"==typeof t)return h(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?h(t,e):void 0}}function h(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function y(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==c(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===c(o)?o:String(o)),n)}var o}function d(t,e,r){return e&&y(t.prototype,e),r&&y(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function b(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var v=(0,r(3122).Z)("quill:selection");function m(t){return t<0?0:t}var g=d((function t(e){var r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;b(this,t),this.index=e,this.length=r})),w=function(){function t(e,r,n){var o=this;b(this,t),this.emitter=r,this.scroll=e,this.composition=n,this.mouseDown=!1,this.root=this.scroll.domNode,this.cursor=this.scroll.create("cursor",this),this.savedRange=new g(0,0),this.lastRange=this.savedRange,this.lastNative=null,this.handleDragging(),this.emitter.listenDOM("selectionchange",document,(function(){o.mouseDown||o.composition.isCompositionInProgress()||setTimeout(o.update.bind(o,l.Z.sources.USER),1)})),this.emitter.on(l.Z.events.SCROLL_BEFORE_UPDATE,(function(){if(o.hasFocus()){var t=o.getNativeRange();null!=t&&t.start.node!==o.cursor.textNode&&o.emitter.once(l.Z.events.SCROLL_UPDATE,(function(){try{o.root.contains(t.start.node)&&o.root.contains(t.end.node)&&o.setNativeRange(t.start.node,t.start.offset,t.end.node,t.end.offset),o.update(l.Z.sources.SILENT)}catch(t){}}))}})),this.emitter.on(l.Z.events.SCROLL_OPTIMIZE,(function(t,e){if(e.range){var r=e.range;o.setNativeRange(r.startNode,r.startOffset,r.endNode,r.endOffset),o.update(l.Z.sources.SILENT)}})),this.update(l.Z.sources.SILENT)}return d(t,[{key:"handleDragging",value:function(){var t=this;this.emitter.listenDOM("mousedown",document.body,(function(){t.mouseDown=!0})),this.emitter.listenDOM("mouseup",document.body,(function(){t.mouseDown=!1,t.update(l.Z.sources.USER)}))}},{key:"focus",value:function(){this.hasFocus()||(this.root.focus(),this.setRange(this.savedRange))}},{key:"format",value:function(t,e){this.scroll.update();var r=this.getNativeRange();if(null!=r&&r.native.collapsed&&!this.scroll.query(t,n.Scope.BLOCK)){if(r.start.node!==this.cursor.textNode){var o=this.scroll.find(r.start.node,!1);if(null==o)return;if(o instanceof n.LeafBlot){var i=o.split(r.start.offset);o.parent.insertBefore(this.cursor,i)}else o.insertBefore(this.cursor,r.start.node);this.cursor.attach()}this.cursor.format(t,e),this.scroll.optimize(),this.setNativeRange(this.cursor.textNode,this.cursor.textNode.data.length),this.update()}}},{key:"getBounds",value:function(t){var e,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=this.scroll.length();t=Math.min(t,n-1),r=Math.min(t+r,n-1)-t;var o=f(this.scroll.leaf(t),2),i=o[0],a=o[1];if(null==i)return null;var u=f(this.getPositionData(i,a,!0),2);e=u[0],a=u[1];var l=document.createRange();if(r>0){l.setStart(e,a);var c=f(this.scroll.leaf(t+r),2);if(null==(i=c[0]))return null;var s=f(this.getPositionData(i,a=c[1],!0),2);return l.setEnd(e=s[0],a=s[1]),l.getBoundingClientRect()}var p,h="left";if(e instanceof Text){if(a<e.data.length)l.setStart(e,a),l.setEnd(e,a+1);else{var y=m(a-1);l.setStart(e,y),l.setEnd(e,a),h="right"}p=l.getBoundingClientRect()}else p=i.domNode.getBoundingClientRect(),a>0&&(h="right");return{bottom:p.top+p.height,height:p.height,left:p[h],right:p[h],top:p.top,width:0}}},{key:"getNativeRange",value:function(){var t,e,r=this.root.getRootNode(),n=null!==(t=null===(e=r.getSelection)||void 0===e?void 0:e.call(r))&&void 0!==t?t:document.getSelection();if(null==n||n.rangeCount<=0)return null;var o=n.getRangeAt(0);if(null==o)return null;var i=this.normalizeNative(o);return v.info("getNativeRange",i),i}},{key:"getRange",value:function(){var t=this.getNativeRange();return null==t?[null,null]:[this.normalizedToRange(t),t]}},{key:"hasFocus",value:function(){var t=this.root.getRootNode();return t.activeElement===this.root||O(this.root,t.activeElement)}},{key:"normalizedToRange",value:function(t){var e=this,r=[[t.start.node,t.start.offset]];t.native.collapsed||r.push([t.end.node,t.end.offset]);var o=r.map((function(t){var r=f(t,2),o=r[0],i=r[1],a=e.scroll.find(o,!0),u=a.offset(e.scroll);return 0===i?u:a instanceof n.LeafBlot?u+a.index(o,i):u+a.length()})),i=Math.min(Math.max.apply(Math,s(o)),this.scroll.length()-1),a=Math.min.apply(Math,[i].concat(s(o)));return new g(a,i-a)}},{key:"normalizeNative",value:function(t){if(!O(this.root,t.startContainer)||!t.collapsed&&!O(this.root,t.endContainer))return null;var e={start:{node:t.startContainer,offset:t.startOffset},end:{node:t.endContainer,offset:t.endOffset},native:t};return[e.start,e.end].forEach((function(t){for(var e=t.node,r=t.offset;!(e instanceof Text)&&e.childNodes.length>0;)if(e.childNodes.length>r)e=e.childNodes[r],r=0;else{if(e.childNodes.length!==r)break;r=(e=e.lastChild)instanceof Text?e.data.length:e.childNodes.length>0?e.childNodes.length:e.childNodes.length+1}t.node=e,t.offset=r})),e}},{key:"rangeToNative",value:function(t){var e=this,r=t.collapsed?[t.index]:[t.index,t.index+t.length],n=[],o=this.scroll.length();return r.forEach((function(t,r){t=Math.min(o-1,t);var i=f(e.scroll.leaf(t),2),a=f(e.getPositionData(i[0],i[1],0!==r),2);n.push(a[0],a[1])})),n.length<2?n.concat(n):n}},{key:"getPositionData",value:function(t,e,r){return t instanceof n.LeafBlot?t.position(e,r):[t.domNode,e]}},{key:"scrollIntoView",value:function(t){var e=this.lastRange;if(null!=e){var r=this.getBounds(e.index,e.length);if(null!=r){var n=this.scroll.length()-1,o=f(this.scroll.line(Math.min(e.index,n)),1)[0],i=o;if(e.length>0&&(i=f(this.scroll.line(Math.min(e.index+e.length,n)),1)[0]),null!=o&&null!=i){var a=t.getBoundingClientRect();r.top<a.top?t.scrollTop-=a.top-r.top:r.bottom>a.bottom&&(t.scrollTop+=r.bottom-a.bottom)}}}}},{key:"setNativeRange",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:e,o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];if(v.info("setNativeRange",t,e,r,n),null==t||null!=this.root.parentNode&&null!=t.parentNode&&null!=r.parentNode){var i=document.getSelection();if(null!=i)if(null!=t){this.hasFocus()||this.root.focus();var a=(this.getNativeRange()||{}).native;if(null==a||o||t!==a.startContainer||e!==a.startOffset||r!==a.endContainer||n!==a.endOffset){"BR"===t.tagName&&(e=Array.from(t.parentNode.childNodes).indexOf(t),t=t.parentNode),"BR"===r.tagName&&(n=Array.from(r.parentNode.childNodes).indexOf(r),r=r.parentNode);var u=document.createRange();e=m(e),n=m(n),u.setStart(t,e),u.setEnd(r,n),i.removeAllRanges(),i.addRange(u)}}else i.removeAllRanges(),this.root.blur(),document.body.focus()}}},{key:"setRange",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:l.Z.sources.API;if("string"==typeof e&&(r=e,e=!1),v.info("setRange",t),null!=t){var n=this.rangeToNative(t);this.setNativeRange.apply(this,s(n).concat([e]))}else this.setNativeRange(null);this.update(r)}},{key:"update",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:l.Z.sources.USER,e=this.lastRange,r=f(this.getRange(),2),n=r[1];if(this.lastRange=r[0],this.lastNative=n,null!=this.lastRange&&(this.savedRange=this.lastRange),!u()(e,this.lastRange)){var o;if(!this.composition.isCompositionInProgress()&&null!=n&&n.native.collapsed&&n.start.node!==this.cursor.textNode){var a=this.cursor.restore();a&&this.setNativeRange(a.startNode,a.startOffset,a.endNode,a.endOffset)}var c,s=[l.Z.events.SELECTION_CHANGE,i()(this.lastRange),i()(e),t];(o=this.emitter).emit.apply(o,[l.Z.events.EDITOR_CHANGE].concat(s)),t!==l.Z.sources.SILENT&&(c=this.emitter).emit.apply(c,s)}}}]),t}();function O(t,e){return e instanceof Text&&(e=e.parentNode),t.contains(e)}},1690:(t,e,r)=>{function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,(i=void 0,i=function(t,e){if("object"!==n(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o.key,"string"),"symbol"===n(i)?i:String(i)),o)}var i}r.d(e,{Z:()=>a});var i=function(){function t(e,r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this.quill=e,this.options=r,this.modules={}}var e,r,n;return e=t,(r=[{key:"init",value:function(){var t=this;Object.keys(this.options.modules).forEach((function(e){null==t.modules[e]&&t.addModule(e)}))}},{key:"addModule",value:function(t){var e=this.quill.constructor.import("modules/".concat(t));return this.modules[t]=new e(this.quill,this.options.modules[t]||{}),this.modules[t]}}])&&o(e.prototype,r),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),t}();i.DEFAULTS={modules:{}},i.themes={default:i};const a=i},715:(t,e,r)=>{r.d(e,{HE:()=>u,dk:()=>a,if:()=>i});var n=r(1233),o={scope:n.Scope.BLOCK,whitelist:["right","center","justify"]},i=new n.Attributor("align","align",o),a=new n.ClassAttributor("align","ql-align",o),u=new n.StyleAttributor("align","text-align",o)},7898:(t,e,r)=>{r.d(e,{Y:()=>i,w:()=>a});var n=r(1233),o=r(6039),i=new n.ClassAttributor("background","ql-bg",{scope:n.Scope.INLINE}),a=new o.OO("background","background-color",{scope:n.Scope.INLINE})},3991:(t,e,r)=>{function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,(i=void 0,i=function(t,e){if("object"!==n(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o.key,"string"),"symbol"===n(i)?i:String(i)),o)}var i}function i(){return i="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=l(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},i.apply(this,arguments)}function a(t,e){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},a(t,e)}function u(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,o=l(t);if(e){var i=l(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return function(t,e){if(e&&("object"===n(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function l(t){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},l(t)}r.d(e,{Z:()=>s});var c=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&a(t,e)}(s,t);var e,r,n,c=u(s);function s(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),c.apply(this,arguments)}return e=s,n=[{key:"create",value:function(){return i(l(s),"create",this).call(this)}},{key:"formats",value:function(){return!0}}],(r=[{key:"optimize",value:function(t){i(l(s.prototype),"optimize",this).call(this,t),this.domNode.tagName!==this.statics.tagName[0]&&this.replaceWith(this.statics.blotName)}}])&&o(e.prototype,r),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),s}(r(6603).Z);c.blotName="bold",c.tagName=["STRONG","B"];const s=c},7309:(t,e,r)=>{r.d(e,{EK:()=>O,ZP:()=>w,se:()=>g});var n=r(6446),o=r(4122),i=r(3657),a=r(6603),u=r(8222),l=r(3553),c=r(281);function s(t){return s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},s(t)}function f(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function p(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==s(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==s(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===s(o)?o:String(o)),n)}var o}function h(t,e,r){return e&&p(t.prototype,e),r&&p(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function y(){return y="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=m(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},y.apply(this,arguments)}function d(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&b(t,e)}function b(t,e){return b=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},b(t,e)}function v(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=m(t);if(e){var o=m(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===s(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function m(t){return m=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},m(t)}var g=function(t){d(r,t);var e=v(r);function r(){return f(this,r),e.apply(this,arguments)}return h(r,[{key:"code",value:function(t,e){var r=this.children.map((function(t){return t.length()<=1?"":t.domNode.textContent})).join("\n").slice(t,t+e);return(0,u.b)(r)}},{key:"html",value:function(t,e){return"<pre>\n".concat(this.code(t,e),"\n</pre>")}}],[{key:"create",value:function(t){var e=y(m(r),"create",this).call(this,t);return e.setAttribute("spellcheck",!1),e}}]),r}(l.Z),w=function(t){d(r,t);var e=v(r);function r(){return f(this,r),e.apply(this,arguments)}return h(r,null,[{key:"register",value:function(){c.ZP.register(g)}}]),r}(n.ZP),O=function(t){d(r,t);var e=v(r);function r(){return f(this,r),e.apply(this,arguments)}return h(r)}(a.Z);O.blotName="code",O.tagName="CODE",w.blotName="code-block",w.className="ql-code-block",w.tagName="DIV",g.blotName="code-block-container",g.className="ql-code-block-container",g.tagName="DIV",g.allowedChildren=[w],w.allowedChildren=[u.Z,o.Z,i.Z],w.requiredContainer=g,w.TAB="  "},6039:(t,e,r)=>{r.d(e,{HQ:()=>p,Hn:()=>f,OO:()=>s});var n=r(1233);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=void 0,i=function(t,e){if("object"!==o(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===o(i)?i:String(i)),n)}var i}function a(){return a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=c(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},a.apply(this,arguments)}function u(t,e){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},u(t,e)}function l(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=c(t);if(e){var i=c(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function c(t){return c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},c(t)}var s=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(s,t);var e,r,n,o=l(s);function s(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),o.apply(this,arguments)}return e=s,(r=[{key:"value",value:function(t){var e=a(c(s.prototype),"value",this).call(this,t);if(0!==e.indexOf("rgb("))return e;e=e.replace(/^[^\d]+/,"").replace(/[^\d]+$/,"");var r=e.split(",").map((function(t){return"00".concat(parseInt(t,10).toString(16)).slice(-2)})).join("");return"#".concat(r)}}])&&i(e.prototype,r),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),s}(n.StyleAttributor),f=new n.ClassAttributor("color","ql-color",{scope:n.Scope.INLINE}),p=new s("color","color",{scope:n.Scope.INLINE})},4048:(t,e,r)=>{r.d(e,{H8:()=>u,IF:()=>i,hY:()=>a});var n=r(1233),o={scope:n.Scope.BLOCK,whitelist:["rtl"]},i=new n.Attributor("direction","dir",o),a=new n.ClassAttributor("direction","ql-direction",o),u=new n.StyleAttributor("direction","direction",o)},5832:(t,e,r)=>{r.d(e,{H:()=>h,_:()=>f});var n=r(1233);function o(t){return o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},o(t)}function i(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(i=void 0,i=function(t,e){if("object"!==o(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==o(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===o(i)?i:String(i)),n)}var i}function a(){return a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=c(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},a.apply(this,arguments)}function u(t,e){return u=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},u(t,e)}function l(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=c(t);if(e){var i=c(this).constructor;r=Reflect.construct(n,arguments,i)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===o(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function c(t){return c=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},c(t)}var s={scope:n.Scope.INLINE,whitelist:["serif","monospace"]},f=new n.ClassAttributor("font","ql-font",s),p=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&u(t,e)}(s,t);var e,r,n,o=l(s);function s(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,s),o.apply(this,arguments)}return e=s,(r=[{key:"value",value:function(t){return a(c(s.prototype),"value",this).call(this,t).replace(/["']/g,"")}}])&&i(e.prototype,r),n&&i(e,n),Object.defineProperty(e,"prototype",{writable:!1}),s}(n.StyleAttributor),h=new p("font","font-family",s)},7256:(t,e,r)=>{function n(t){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},n(t)}function o(t,e){for(var r=0;r<e.length;r++){var o=e[r];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(t,(i=void 0,i=function(t,e){if("object"!==n(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var o=r.call(t,e||"default");if("object"!==n(o))return o;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(o.key,"string"),"symbol"===n(i)?i:String(i)),o)}var i}function i(){return i="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=l(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},i.apply(this,arguments)}function a(t,e){return a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},a(t,e)}function u(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,o=l(t);if(e){var i=l(this).constructor;r=Reflect.construct(o,arguments,i)}else r=o.apply(this,arguments);return function(t,e){if(e&&("object"===n(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function l(t){return l=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},l(t)}r.d(e,{N:()=>s,Z:()=>c});var c=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&a(t,e)}(f,t);var e,r,n,c=u(f);function f(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,f),c.apply(this,arguments)}return e=f,n=[{key:"create",value:function(t){var e=i(l(f),"create",this).call(this,t);return e.setAttribute("href",this.sanitize(t)),e.setAttribute("rel","noopener noreferrer"),e.setAttribute("target","_blank"),e}},{key:"formats",value:function(t){return t.getAttribute("href")}},{key:"sanitize",value:function(t){return s(t,this.PROTOCOL_WHITELIST)?t:this.SANITIZED_URL}}],(r=[{key:"format",value:function(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("href",this.constructor.sanitize(e)):i(l(f.prototype),"format",this).call(this,t,e)}}])&&o(e.prototype,r),n&&o(e,n),Object.defineProperty(e,"prototype",{writable:!1}),f}(r(6603).Z);function s(t,e){var r=document.createElement("a");r.href=t;var n=r.href.slice(0,r.href.indexOf(":"));return e.indexOf(n)>-1}c.blotName="link",c.tagName="A",c.SANITIZED_URL="about:blank",c.PROTOCOL_WHITELIST=["http","https","mailto","tel"]},1629:(t,e,r)=>{r.d(e,{Z:()=>i,m:()=>o});var n=r(1233),o=new n.ClassAttributor("size","ql-size",{scope:n.Scope.INLINE,whitelist:["small","large","huge"]}),i=new n.StyleAttributor("size","font-size",{scope:n.Scope.INLINE,whitelist:["10px","18px","32px"]})},1342:(t,e,r)=>{r.d(e,{Du:()=>s,h6:()=>f,kk:()=>c,yA:()=>l});var n=r(6062),o=r(1801),i=r(8252);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var l=i.F1.map((function(t){return(0,n.Z)(i.Pw,t)})),c=i.Mu.map((function(t){return(0,o.Z)(i.Pw,t)})),s=c.reduce((function(t,e){return t[e.attrName]=e,t}),{}),f=[].concat(a(l),a(c)).reduce((function(t,e){return t[e.keyName]=e,t}),{})},8252:(t,e,r)=>{r.d(e,{F1:()=>o,Mu:()=>i,Pw:()=>n,fU:()=>a});var n={name:"cell",allowedTags:["TH","TD","TR"]},o=["height","width"],i=["height","width","vertical-align","text-align","background-color","border","border-style","border-width","border-color","padding","padding-top","padding-right","padding-bottom","padding-left"],a=new Set([].concat(o,i))},6062:(t,e,r)=>{r.d(e,{Z:()=>l});var n=r(1674),o=r(2141),i=r(3398),a=["name"];function u(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)e.indexOf(r=i[n])>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)e.indexOf(r=i[n])>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function l(t,e){var r=t.name,l=u(t,a),c="".concat(r).concat((0,o.Z)(e)),s="".concat(i.WT.attribute).concat(r,"_").concat(e);return new n.Z(c,s,l)}},1801:(t,e,r)=>{r.d(e,{Z:()=>s});var n=r(4569),o=r(2141),i=r(3398),a=["name","formatName"];function u(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return l(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?l(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function l(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function c(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)e.indexOf(r=i[n])>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)e.indexOf(r=i[n])>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}function s(t,e){var r=t.name,l=t.formatName,s=c(t,a),f=u(e.split("-"),2),p=f[0],h=f[1],y="".concat(r).concat((0,o.Z)(null!=l?l:p)).concat(h?(0,o.Z)(h):""),d="".concat(i.WT.style).concat(r,"_").concat(e);return new n.Z(y,d,s)}},319:(t,e,r)=>{r.d(e,{Qu:()=>l,VT:()=>c,Zt:()=>f,li:()=>s});var n=r(6062),o=r(1801),i=r(19);function a(t){return function(t){if(Array.isArray(t))return u(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return u(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?u(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function u(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var l=i.N2.map((function(t){return(0,n.Z)(i.Sp,t)})),c=i.GX.map((function(t){return(0,o.Z)(i.Sp,t)})),s=c.reduce((function(t,e){return t[e.attrName]=e,t}),{}),f=[].concat(a(l),a(c)).reduce((function(t,e){return t[e.keyName]=e,t}),{})},19:(t,e,r)=>{r.d(e,{GX:()=>i,N2:()=>o,Sp:()=>n,bc:()=>a});var n={name:"table",allowedTags:["TABLE"]},o=["height","width"],i=["height","width","text-align","background-color","border","border-style","border-width","border-color"],a=new Set([].concat(o,i))},8536:(t,e,r)=>{function n(){return Math.random().toString(36).slice(2,6)}r.d(e,{Z:()=>n})},1969:(t,e,r)=>{r.d(e,{KA:()=>Z,Lv:()=>D,RM:()=>B,SC:()=>C,iD:()=>k,pj:()=>T,xD:()=>q,xJ:()=>I,xs:()=>A,zW:()=>E});var n=r(6446),o=r(4122),i=r(3553),a=r(5874),u=r(1342),l=r(319),c=r(8536),s=r(2795);function f(t){return f="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},f(t)}function p(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||y(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function h(t){return function(t){if(Array.isArray(t))return d(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||y(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function y(t,e){if(t){if("string"==typeof t)return d(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?d(t,e):void 0}}function d(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function b(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function v(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==f(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==f(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===f(o)?o:String(o)),n)}var o}function m(t,e,r){return e&&v(t.prototype,e),r&&v(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function g(){return g="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=S(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},g.apply(this,arguments)}function w(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&O(t,e)}function O(t,e){return O=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},O(t,e)}function j(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=S(t);if(e){var o=S(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===f(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function S(t){return S=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},S(t)}var P=["row","cell"],_="data-table-";function N(t,e,r){t.forEachAt(e,r,(function(t,e,r){t.deleteAt(e,r)}))}var E=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r,[{key:"optimize",value:function(){var t,e=this.domNode.getAttribute("".concat(_,"row"));if(this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)){var n=this.domNode,o={row:e};Object.keys(u.Du).forEach((function(t){var e=n.dataset[t.toLowerCase()];e&&(o[t]=e)})),this.wrap(this.statics.requiredContainer.blotName,o)}for(var i=arguments.length,a=new Array(i),l=0;l<i;l++)a[l]=arguments[l];(t=g(S(r.prototype),"optimize",this)).call.apply(t,[this].concat(a))}},{key:"format",value:function(t,e){"tableCellLine"===t&&null===e&&(e=this.formats().tableCellLine);var n=P.indexOf(t)>-1;if(n||l.li[t]||u.Du[t]){var o,i,a="data-".concat(n?"table-":"").concat(t.toLowerCase());(0,s.Z)(this.domNode,a,e),u.Du[t]&&(null===(o=this.cell())||void 0===o||o.format(t,e)),l.li[t]&&(null===(i=this.cell())||void 0===i||null===(i=i.table())||void 0===i||i.format(t,e))}else g(S(r.prototype),"format",this).call(this,t,e)}},{key:"cell",value:function(){return"row"in this.parent?this.parent:null}}],[{key:"create",value:function(t){var e=g(S(r),"create",this).call(this,t);return P.forEach((function(r){var n,o="row"===r?D:M;e.setAttribute("".concat(_).concat(r),null!==(n=null==t?void 0:t[r])&&void 0!==n?n:o())})),e}},{key:"formats",value:function(t){return P.reduce((function(e,r){var n="".concat(_).concat(r);return t.hasAttribute(n)&&(e[r]=t.getAttribute(n)||void 0),e}),{})}}]),r}(n.ZP);E.blotName="tableCellLine",E.className="ql-table-cell-line",E.tagName="P";var k=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r)}(E);k.blotName="tableHeaderCellLine",k.className="ql-table-header-cell-line";var x=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r,[{key:"format",value:function(t,e){var r;null===(r=u.Du[t])||void 0===r||r.add(this.domNode,e)}},{key:"checkMerge",value:function(){if(g(S(r.prototype),"checkMerge",this).call(this)&&null!=this.next.children.head){var t=this.children.head.formats()[this.children.head.statics.blotName],e=this.children.tail.formats()[this.children.tail.statics.blotName],n=this.next.children.head.formats()[this.next.children.head.statics.blotName],o=this.next.children.tail.formats()[this.next.children.tail.statics.blotName];return t.cell===e.cell&&t.cell===n.cell&&t.cell===o.cell}return!1}},{key:"formats",value:function(){return r.cellFormats(this.domNode)}},{key:"cellOffset",value:function(){return this.parent?this.parent.children.indexOf(this):-1}},{key:"row",value:function(){return"table"in this.parent?this.parent:null}},{key:"rowOffset",value:function(){return this.row()?this.row().rowOffset():-1}},{key:"table",value:function(){var t;return null===(t=this.row())||void 0===t?void 0:t.table()}},{key:"optimize",value:function(){var t,e,n=null!==(t=this.domNode.getAttribute("".concat(_,"row")))&&void 0!==t?t:this.domNode.getAttribute("".concat(_,"header-row"));!this.statics.requiredContainer||this.parent instanceof this.statics.requiredContainer||this.wrap(this.statics.requiredContainer.blotName,{row:n});for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];(e=g(S(r.prototype),"optimize",this)).call.apply(e,[this].concat(i))}},{key:"deleteAt",value:function(t,e){N(this.children,t,e)}}],[{key:"create",value:function(t){var e=g(S(r),"create",this).call(this,t);return t&&Object.keys(t).forEach((function(r){var n;null===(n=u.Du[r])||void 0===n||n.add(e,t[r])})),e}},{key:"cellFormats",value:function(t){var e,r={};return(t.hasAttribute("".concat(_,"row"))||t.hasAttribute("".concat(_,"header-row")))&&(r.row=null!==(e=t.getAttribute("".concat(_,"row")))&&void 0!==e?e:t.getAttribute("".concat(_,"header-row"))),Object.keys(u.Du).forEach((function(e){var n,o=null===(n=t.firstElementChild)||void 0===n?void 0:n.dataset[e.toLowerCase()];o&&(r[e]=o)})),r}}]),r}(i.Z);x.tagName=["TD","TH"];var T=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r,[{key:"format",value:function(t,e){"row"===t?(this.domNode.setAttribute("".concat(_).concat(t),e),this.children.forEach((function(r){r.format(t,e)}))):g(S(r.prototype),"format",this).call(this,t,e)}}],[{key:"create",value:function(t){var e=g(S(r),"create",this).call(this,t),n="".concat(_,"row");return null!=t&&t.row&&e.setAttribute(n,t.row),e}}]),r}(x);T.blotName="tableCell",T.className="ql-table-data-cell",T.dataAttribute="".concat(_,"row"),T.defaultChild=E;var A=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r,[{key:"format",value:function(t,e){"row"===t?(this.domNode.setAttribute("".concat(_).concat(t),e),this.children.forEach((function(r){r.format(t,e)}))):g(S(r.prototype),"format",this).call(this,t,e)}}],[{key:"create",value:function(t){var e=g(S(r),"create",this).call(this,t),n="".concat(_,"header-row");return t&&t.row&&e.setAttribute(n,t.row),e}}]),r}(x);A.tagName=["TH","TD"],A.className="ql-table-header-cell",A.blotName="tableHeaderCell",A.dataAttribute="".concat(_,"header-row"),A.defaultChild=k;var R=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r,[{key:"checkMerge",value:function(){if(g(S(r.prototype),"checkMerge",this).call(this)&&(0,a.Z)(this.next.children.head)){var t="row",e=this.children.head.formats(),n=this.children.tail.formats(),o=this.next.children.head.formats(),i=this.next.children.tail.formats();return e[t]===n[t]&&e[t]===o[t]&&e[t]===i[t]}return!1}},{key:"optimize",value:function(){for(var t,e=this,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];(t=g(S(r.prototype),"optimize",this)).call.apply(t,[this].concat(o));var u=this.childFormatName;this.children.forEach((function(t){if((0,a.Z)(t.next)){var r=t.formats(),n=t.next.formats();if(r[u]!==n[u]){var o=e.splitAfter(t);o&&o.optimize(),e.prev&&e.prev.optimize()}}}))}},{key:"rowOffset",value:function(){return this.parent?this.parent.children.indexOf(this):-1}},{key:"table",value:function(){var t;return null===(t=this.parent)||void 0===t?void 0:t.parent}},{key:"formats",value:function(){var t={},e="".concat(_,"row");return this.domNode.hasAttribute(e)&&(t[e]=this.domNode.getAttribute(e)),t}}],[{key:"create",value:function(t){var e=g(S(r),"create",this).call(this,t);return null!=t&&t.row&&e.setAttribute("".concat(_,"row"),t.row),e}}]),r}(i.Z);R.tagName="TR";var C=function(t){w(r,t);var e=j(r);function r(t,n){var o;return b(this,r),(o=e.call(this,t,n)).childFormatName="table",o}return m(r,[{key:"deleteAt",value:function(t,e){N(this.children,t,e)}}]),r}(R);C.blotName="tableRow";var Z=function(t){w(r,t);var e=j(r);function r(t,n){var o;return b(this,r),(o=e.call(this,t,n)).childFormatName="tableHeaderCell",o}return m(r)}(R);Z.blotName="tableHeaderRow";var L=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r,[{key:"optimize",value:function(){var t;if(this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)){var e,n=(null===(e=this.children.head.children.head.children)||void 0===e||null===(e=e.head)||void 0===e?void 0:e.domNode)||null,o={};n&&Object.keys(l.li).forEach((function(t){var e=n.dataset[t.toLowerCase()];e&&(o[t]=e)})),this.wrap(this.statics.requiredContainer.blotName,o)}for(var i=arguments.length,a=new Array(i),u=0;u<i;u++)a[u]=arguments[u];(t=g(S(r.prototype),"optimize",this)).call.apply(t,[this].concat(a))}}]),r}(i.Z),B=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r)}(L);B.blotName="tableBody",B.tagName="TBODY";var q=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r)}(L);q.blotName="tableHeader",q.tagName="THEAD";var I=function(t){w(r,t);var e=j(r);function r(){return b(this,r),e.apply(this,arguments)}return m(r,[{key:"balanceCells",value:function(){var t=this.descendants(Z),e=this.descendants(C),r=this.getMaxTableColCount(t,e);this.balanceRows(r,t,A),this.balanceRows(r,e,T)}},{key:"getMaxTableColCount",value:function(t,e){return Math.max(this.getMaxRowColCount(t),this.getMaxRowColCount(e))}},{key:"getMaxRowColCount",value:function(t){return Math.max.apply(Math,h(t.map((function(t){return t.children.length}))))}},{key:"balanceRows",value:function(t,e,r){var n=this;e.forEach((function(e){new Array(t-e.children.length).fill(0).forEach((function(){var t;(0,a.Z)(e.children.head)&&(t=r.cellFormats(e.children.head.domNode));var o=n.scroll.create(r.blotName,t),i=n.scroll.create(r.allowedChildren[0].blotName,t);o.appendChild(i),e.appendChild(o),o.optimize()}))}))}},{key:"cells",value:function(t){return this.rows().map((function(e){return e.children.at(t)}))}},{key:"deleteColumn",value:function(t){var e=this;[q,B].forEach((function(r){var n=p(e.descendants(r),1)[0];(0,a.Z)(n)&&(0,a.Z)(n.children.head)&&n.children.forEach((function(e){var r=e.children.at(t);(0,a.Z)(r)&&r.remove()}))}))}},{key:"insertColumn",value:function(t){var e=this;[q,B].forEach((function(r){var n=p(e.descendants(r),1)[0];if((0,a.Z)(n)&&(0,a.Z)(n.children.head)){var i=r===q?A:T,u=r===q?k:E;n.children.forEach((function(r){var n=r.children.at(t),a=u.formats(r.children.head.children.head.domNode),l=e.scroll.create(i.blotName,{row:a.row}),c=e.scroll.create(u.blotName,{row:a.row}),s=e.scroll.create(o.Z.blotName);c.appendChild(s),l.appendChild(c),r.insertBefore(l,n)}))}}))}},{key:"insertRow",value:function(t){var e=this,r=p(this.descendants(B),1)[0];if((0,a.Z)(r)&&(0,a.Z)(r.children.head)){var n=D(),i=this.scroll.create(C.blotName,{row:n});r.children.head.children.forEach((function(){var t=e.scroll.create(T.blotName,{row:n}),r=e.scroll.create(E.blotName,{row:n}),a=e.scroll.create(o.Z.blotName);r.appendChild(a),t.appendChild(r),i.appendChild(t)}));var u=r.children.at(t);r.insertBefore(i,u)}}},{key:"insertHeaderRow",value:function(){var t=this,e=p(this.descendants(q),1)[0],r=p(this.descendants(B),1)[0];if(!(0,a.Z)(e)&&(0,a.Z)(r)&&(0,a.Z)(r.children.head)){var n=D(),i=this.scroll.create(q.blotName),u=this.scroll.create(Z.blotName),l=this.children.at(0);i.appendChild(u),r.children.head.children.forEach((function(){var e=t.scroll.create(A.blotName,{row:n}),r=t.scroll.create(k.blotName,{row:n}),i=t.scroll.create(o.Z.blotName);r.appendChild(i),e.appendChild(r),u.appendChild(e),e.optimize()})),this.insertBefore(i,l)}}},{key:"rows",value:function(){var t=this.children.head;return(0,a.Z)(t)?t.children.map((function(t){return t})):[]}},{key:"formats",value:function(){var t={},e=this.cells()[0].domNode.firstElementChild;return Object.keys(l.li).forEach((function(r){var n=null==e?void 0:e.dataset[r.toLowerCase()];n&&(t[r]=n)})),t}},{key:"format",value:function(t,e){var r=l.li[t];if(r){var n="data-".concat(t.toLowerCase());this.cells().forEach((function(t){(0,s.Z)(t.children.head.domNode,n,e)})),r.add(this.domNode,e)}}}],[{key:"create",value:function(t){var e=g(S(r),"create",this).call(this,t);return t&&Object.keys(t).forEach((function(r){var n;null===(n=l.li[r])||void 0===n||n.add(e,t[r])})),e}}]),r}(i.Z);function D(){return"row-".concat((0,c.Z)())}function M(){return"cell-".concat((0,c.Z)())}I.blotName="tableContainer",I.tagName="TABLE",I.allowedChildren=[q,B],B.requiredContainer=I,q.requiredContainer=I,B.allowedChildren=[C],C.requiredContainer=B,C.allowedChildren=[T],T.requiredContainer=C,E.requiredContainer=T,T.allowedChildren=[E],q.allowedChildren=[Z],Z.requiredContainer=q,k.requiredContainer=A,A.allowedChildren=[k],Z.allowedChildren=[A],A.requiredContainer=Z},2795:(t,e,r)=>{function n(t,e,r){r?t.setAttribute(e,r):t.removeAttribute(e)}r.d(e,{Z:()=>n})},5635:(t,e,r)=>{r.d(e,{HI:()=>q,PF:()=>I,ZP:()=>B,fw:()=>z});var n=r(9098),o=r.n(n),i=r(1233),a=r(6446),u=r(281),l=r(3122),c=r(7094),s=r(715),f=r(7898),p=r(7309),h=r(6039),y=r(4048),d=r(5832),b=r(1629),v=r(3071),m=r(2141);function g(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function w(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?g(Object(r),!0).forEach((function(e){j(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):g(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function O(t){return O="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},O(t)}function j(t,e,r){return(e=N(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function S(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return P(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?P(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function P(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function _(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,N(n.key),n)}}function N(t){var e=function(t,e){if("object"!==O(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==O(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===O(e)?e:String(e)}function E(t,e){return E=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},E(t,e)}function k(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=T(t);if(e){var o=T(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===O(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return x(t)}(this,r)}}function x(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function T(t){return T=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},T(t)}var A=(0,l.Z)("quill:clipboard"),R=[[3,W],[3,H],["br",function(t,e){return I(e,"\n")||e.insert("\n"),e}],[1,H],[1,function(t,e,r){var n=r.query(t);if(null==n)return e;if(n.prototype instanceof i.EmbedBlot){var a={},u=n.value(t);if(null!=u)return a[n.blotName]=u,(new(o())).insert(a,n.formats(t,r))}else if(n.prototype instanceof i.BlockBlot&&!I(e,"\n")&&e.insert("\n"),"function"==typeof n.formats)return q(e,n.blotName,n.formats(t,r));return e}],[1,function(t,e,r){if(-1===["TD","TH","TR","TABLE"].indexOf(t.tagName)){var n=i.Attributor.keys(t),o=i.ClassAttributor.keys(t),a=i.StyleAttributor.keys(t),u={};if(n.concat(o).concat(a).forEach((function(e){var n=r.query(e,i.Scope.ATTRIBUTE);null!=n&&(u[n.attrName]=n.value(t),u[n.attrName])||(null==(n=Z[e])||n.attrName!==e&&n.keyName!==e||(u[n.attrName]=n.value(t)||void 0),null==(n=L[e])||n.attrName!==e&&n.keyName!==e||(u[(n=L[e]).attrName]=n.value(t)||void 0))})),Object.keys(u).length>0)return q(e,u)}return e}],[1,function(t,e){var r={},n=t.style||{};return["height","width"].forEach((function(e){var o=-1!==["TD","TH"].indexOf(t.tagName),i="TABLE"===t.tagName;if((o||i)&&n[e]){var a="".concat(i?"table":"cell").concat((0,m.Z)(e));r[a]=n[e]}})),"italic"===n.fontStyle&&(r.italic=!0),-1!==n.textDecoration.indexOf("underline")&&(r.underline=!0),-1!==n.textDecoration.indexOf("line-through")&&(r.strike=!0),(0===n.fontWeight.indexOf("bold")||parseInt(n.fontWeight,10)>=700)&&(r.bold=!0),Object.keys(r).length>0&&(e=q(e,r)),parseFloat(n.textIndent||0)>0?(new(o())).insert("\t").concat(e):e}],["li",function(t,e,r){var n=r.query(t);if(null==n||"list"!==n.blotName||!I(e,"\n"))return e;for(var i=-1,a=t.parentNode;null!=a;)-1!==["OL","UL"].indexOf(a.tagName)&&(i+=1),a=a.parentNode;return i<=0?e:e.reduce((function(t,e){return e.attributes&&"number"==typeof e.attributes.indent?t.push(e):t.insert(e.insert,w({indent:i},e.attributes||{}))}),new(o()))}],["ol, ul",function(t,e){return q(e,"list","OL"===t.tagName?"ordered":"bullet")}],["pre",function(t,e,r){var n=r.query("code-block"),o=!n||n.formats(t,r);return q(e,"code-block",o)}],["b",F.bind(F,"bold")],["i",F.bind(F,"italic")],["strike",F.bind(F,"strike")],["style",function(){return new(o())}]],C=[W,H],Z=[s.if,y.IF].reduce((function(t,e){return t[e.keyName]=e,t}),{}),L=[s.HE,f.w,h.HQ,y.H8,d.H,b.Z].reduce((function(t,e){return t[e.keyName]=e,t}),{}),B=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&E(t,e)}(a,t);var e,r,n,i=k(a);function a(t,e){var r,n;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,a),(n=i.call(this,t,e)).quill.root.addEventListener("copy",(function(t){return n.onCaptureCopy(t,!1)})),n.quill.root.addEventListener("cut",(function(t){return n.onCaptureCopy(t,!0)})),n.quill.root.addEventListener("paste",n.onCapturePaste.bind(x(n))),n.matchers=[],n.tableBlots=null!==(r=e.tableBlots)&&void 0!==r?r:[],n.multilineParagraph=!1,R.concat(n.options.matchers).forEach((function(t){var e=S(t,2);n.addMatcher(e[0],e[1])})),n}return e=a,r=[{key:"addMatcher",value:function(t,e){this.matchers.push([t,e])}},{key:"addTableBlot",value:function(t){this.tableBlots.push(t)}},{key:"convert",value:function(t){var e=t.html,r=t.text,n=t.keepLastNewLine,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return i[p.ZP.blotName]?(new(o())).insert(r,j({},p.ZP.blotName,i[p.ZP.blotName])):e?this.applyMatchers(e,n,i):this.applyTextMatchers(r)}},{key:"applyTextMatchers",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";if(0===e.length)return new(o());var r=this.prepareTextMatching(),n=this.quill.root.ownerDocument.createElement("div");n.textContent=e;var i=n.childNodes[0];return r.reduce((function(e,r){return r(i,e,t.quill.scroll)}),new(o()))}},{key:"applyMatchers",value:function(t,e){var r=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=u.ZP.replaceStyleAttribute(t),a=(new DOMParser).parseFromString(i,"text/html");u.ZP.restoreStyleAttribute(a);var l=a.body,c=new WeakMap,s=S(this.prepareMatching(l,c),2),f=z(this.quill.scroll,l,s[0],s[1],c,this.multilineParagraph);return e||!I(f,"\n")||null!=f.ops[f.ops.length-1].attributes&&!Object.values(n).some((function(t){return r.tableBlots.includes(t)}))?f:f.compose((new(o())).retain(f.length()-1).delete(1))}},{key:"dangerouslyPasteHTML",value:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:u.ZP.sources.API;if("string"==typeof t){var n=this.convert({html:t,text:""});this.quill.setContents(n,e),this.quill.setSelection(0,u.ZP.sources.SILENT)}else{var i=this.convert({html:e,text:""});this.quill.updateContents((new(o())).retain(t).concat(i),r),this.quill.setSelection(t+i.length(),u.ZP.sources.SILENT)}}},{key:"onCaptureCopy",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!t.defaultPrevented&&t.clipboardData){t.preventDefault();var r=S(this.quill.selection.getRange(),1)[0];if(null!=r){var n=this.onCopy(r,e),o=n.html;t.clipboardData.setData("text/plain",n.text),t.clipboardData.setData("text/html",o),e&&(this.raiseCallback("onCut",t),(0,v.WQ)({range:r,quill:this.quill}))}}}},{key:"onCapturePaste",value:function(t){if(!t.defaultPrevented&&this.quill.isEnabled()&&(this.raiseCallback("onPaste",t),t.clipboardData)){t.preventDefault();var e=this.quill.getSelection(!0);if(null!=e){var r=t.clipboardData.getData("text/html"),n=Array.from(t.clipboardData.files||[]);if(!r&&n.length>0)this.quill.uploader.upload(e,n);else{if(r&&n.length>0){var o=(new DOMParser).parseFromString(r,"text/html").body;if(1===o.childElementCount&&"IMG"===o.firstElementChild.tagName)return void this.quill.uploader.upload(e,n)}var i=t.clipboardData.getData("text/plain");this.onPaste(e,{html:r,text:i,keepLastNewLine:!0})}}}}},{key:"raiseCallback",value:function(t,e){var r=this.options[t];r&&"function"==typeof r&&r(e)}},{key:"onCopy",value:function(t){var e=t.index,r=t.length,n=this.quill.getText(e,r);return{html:this.quill.getSemanticHTML(e,r),text:n}}},{key:"onPaste",value:function(t,e){var r=e.text,n=e.html,i=e.keepLastNewLine,a=this.quill.getFormat(t.index),l=this.convert({text:r,html:n,keepLastNewLine:i},a);A.log("onPaste",l,{text:r,html:n});var c=(new(o())).retain(t.index).delete(t.length).concat(l);this.quill.updateContents(c,u.ZP.sources.USER),this.quill.setSelection(c.length()-t.length,u.ZP.sources.SILENT),this.quill.scrollIntoView()}},{key:"prepareMatching",value:function(t,e){var r=[],n=[];return this.matchers.forEach((function(o){var i=S(o,2),a=i[0],u=i[1];switch(a){case 3:n.push(u);break;case 1:r.push(u);break;default:Array.from(t.querySelectorAll(a)).forEach((function(t){e.has(t)?e.get(t).push(u):e.set(t,[u])}))}})),[r,n]}},{key:"prepareTextMatching",value:function(){var t=[K];return this.matchers.forEach((function(e){var r=S(e,2),n=r[0],o=r[1];-1===C.indexOf(o)&&3===n&&t.push(o)})),t}}],r&&_(e.prototype,r),n&&_(e,n),Object.defineProperty(e,"prototype",{writable:!1}),a}(c.Z);function q(t,e,r){return"object"===O(e)?Object.keys(e).reduce((function(t,r){return q(t,r,e[r])}),t):t.reduce((function(t,n){if(n.attributes&&n.attributes[e])return t.push(n);var o=r?j({},e,r):{};return t.insert(n.insert,w(w({},o),n.attributes))}),new(o()))}function I(t,e){for(var r="",n=t.ops.length-1;n>=0&&r.length<e.length;--n){var o=t.ops[n];if("string"!=typeof o.insert)break;r=o.insert+r}return r.slice(-1*e.length)===e}function D(t){return 0!==t.childNodes.length&&-1!==["address","article","blockquote","canvas","dd","div","dl","dt","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","iframe","li","main","nav","ol","output","p","pre","section","table","td","tr","ul","video"].indexOf(t.tagName.toLowerCase())}B.DEFAULTS={matchers:[]};var M=new WeakMap;function U(t){return null!=t&&(M.has(t)||M.set(t,"PRE"===t.tagName||U(t.parentNode)),M.get(t))}function z(t,e,r,n,i,a){return e.nodeType===e.TEXT_NODE?n.reduce((function(r,n){return n(e,r,t)}),new(o())):e.nodeType===e.ELEMENT_NODE?Array.from(e.childNodes||[]).reduce((function(o,u,l,c){var s=z(t,u,r,n,i,a),f=l<c.length-1&&c[l+1],p=f&&f.nodeType===e.ELEMENT_NODE&&["ul","ol"].indexOf(f.tagName.toLowerCase())>-1;u.nodeType===e.ELEMENT_NODE&&(a="br"===u.tagName.toLowerCase(),s=r.reduce((function(e,r){return r(u,e,t)}),s),s=(i.get(u)||[]).reduce((function(e,r){return r(u,e,t)}),s));var h=o.concat(s);return a&&p&&h.insert("\n"),h}),new(o())):new(o())}function F(t,e,r){return q(r,t,!0)}function H(t,e,r){if(!I(e,"\n")){if(D(t))return e.insert("\n");if(e.length()>0&&t.nextSibling)for(var n=t.nextSibling;null!=n;){if(D(n))return e.insert("\n");var o=r.query(n);if((null==o?void 0:o.prototype)instanceof a.i2)return e.insert("\n");n=n.firstChild}}return e}function K(t,e){var r=t.data||"";return r=r.replace(/\r\n/g,"\n"),e.insert(r)}function W(t,e){var r=t.data;if("O:P"===t.parentNode.tagName)return e.insert(r.trim());if(0===r.trim().length&&-1!==r.indexOf("\n"))return e;if(!U(t)){var n=function(t,e){var r=e.replace(/[^\u00a0]/g,"");return r.length<1&&t?" ":r};r=(r=r.replace(/\r\n/g," ").replace(/\n/g," ")).replace(/\s\s+/g,n.bind(n,!0)),(null==t.previousSibling&&D(t.parentNode)||null!=t.previousSibling&&D(t.previousSibling))&&(r=r.replace(/^\s+/,n.bind(n,!1))),(null==t.nextSibling&&D(t.parentNode)||null!=t.nextSibling&&D(t.nextSibling))&&(r=r.replace(/\s+$/,n.bind(n,!1)))}return e.insert(r)}},2613:(t,e,r)=>{r.d(e,{Z:()=>h});var n=r(1233),o=r(281),i=r(7094),a=r(8034);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==u(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===u(o)?o:String(o)),n)}var o}function c(t,e){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},c(t,e)}function s(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=p(t);if(e){var o=p(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===u(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return f(t)}(this,r)}}function f(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function p(t){return p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},p(t)}var h=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e)}(p,t);var e,r,i,u=s(p);function p(t,e){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,p),(r=u.call(this,t,e)).lastRecorded=0,r.ignoreChange=!1,r.clear(),r.quill.on(o.ZP.events.EDITOR_CHANGE,(function(t,e,n,i){t!==o.ZP.events.TEXT_CHANGE||r.ignoreChange||(r.options.userOnly&&i!==o.ZP.sources.USER?r.transform(e):r.record(e,n))})),r.quill.keyboard.addBinding({key:"z",shortKey:!0},r.undo.bind(f(r))),r.quill.keyboard.addBinding({key:"z",shortKey:!0,shiftKey:!0},r.redo.bind(f(r))),(0,a.Z)()&&/Win/i.test(navigator.platform)&&r.quill.keyboard.addBinding({key:"y",shortKey:!0},r.redo.bind(f(r))),r.quill.root.addEventListener("beforeinput",(function(t){"historyUndo"===t.inputType?(r.undo(),t.preventDefault()):"historyRedo"===t.inputType&&(r.redo(),t.preventDefault())})),r}return e=p,(r=[{key:"change",value:function(t,e){if(0!==this.stack[t].length){var r=this.stack[t].pop(),i=this.quill.getContents(),a=r.invert(i);this.stack[e].push(a),this.lastRecorded=0,this.ignoreChange=!0,this.quill.updateContents(r,o.ZP.sources.USER),this.ignoreChange=!1;var u=function(t,e){var r=e.reduce((function(t,e){return t+(e.delete||0)}),0),o=e.length()-r;return function(t,e){var r=e.ops[e.ops.length-1];return null!=r&&(null!=r.insert?"string"==typeof r.insert&&r.insert.endsWith("\n"):null!=r.attributes&&Object.keys(r.attributes).some((function(e){return null!=t.query(e,n.Scope.BLOCK)})))}(t,e)&&(o-=1),o}(this.quill.scroll,r);this.quill.setSelection(u)}}},{key:"clear",value:function(){this.stack={undo:[],redo:[]}}},{key:"cutoff",value:function(){this.lastRecorded=0}},{key:"record",value:function(t,e){if(0!==t.ops.length){this.stack.redo=[];var r=t.invert(e),n=Date.now();if(this.lastRecorded+this.options.delay>n&&this.stack.undo.length>0){var o=this.stack.undo.pop();r=r.compose(o)}else this.lastRecorded=n;0!==r.length()&&(this.stack.undo.push(r),this.stack.undo.length>this.options.maxStack&&this.stack.undo.shift())}}},{key:"redo",value:function(){this.change("redo","undo")}},{key:"transform",value:function(t){y(this.stack.undo,t),y(this.stack.redo,t)}},{key:"undo",value:function(){this.change("undo","redo")}}])&&l(e.prototype,r),i&&l(e,i),Object.defineProperty(e,"prototype",{writable:!1}),p}(i.Z);function y(t,e){for(var r=e,n=t.length-1;n>=0;n-=1){var o=t[n];t[n]=r.transform(o,!0),r=o.transform(r),0===t[n].length()&&t.splice(n,1)}}h.DEFAULTS={delay:1e3,maxStack:100,userOnly:!1}},990:(t,e,r)=>{r.d(e,{Z:()=>d});var n=r(9098),o=r.n(n),i=r(7094),a=r(281),u=r(3071);function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function c(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==l(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===l(o)?o:String(o)),n)}var o}function s(t,e){return s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},s(t,e)}function f(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=p(t);if(e){var o=p(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===l(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function p(t){return p=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},p(t)}var h=["insertText","insertReplacementText"],y=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&s(t,e)}(l,t);var e,r,n,i=f(l);function l(t,e){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,l),r=i.call(this,t,e),t.root.addEventListener("beforeinput",(function(t){r.handleBeforeInput(t)})),/Android/i.test(navigator.userAgent)||t.on(a.ZP.events.COMPOSITION_BEFORE_START,(function(){r.handleCompositionStart()})),r}return e=l,r=[{key:"deleteRange",value:function(t){(0,u.WQ)({range:t,quill:this.quill})}},{key:"replaceText",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(0===t.length)return!1;if(e){var r=this.quill.getFormat(t.index,1);this.deleteRange(t),this.quill.updateContents((new(o())).retain(t.index).insert(e,r),a.ZP.sources.USER)}else this.deleteRange(t);return this.quill.setSelection(t.index+e.length,0,a.ZP.sources.SILENT),!0}},{key:"handleBeforeInput",value:function(t){if(!this.quill.composition.isCompositionInProgress()&&!t.defaultPrevented&&h.includes(t.inputType)){var e=t.getTargetRanges?t.getTargetRanges()[0]:null;if(e&&!0!==e.collapsed){var r=function(t){var e;return"string"==typeof t.data?t.data:null!==(e=t.dataTransfer)&&void 0!==e&&e.types.includes("text/plain")?t.dataTransfer.getData("text/plain"):null}(t);if(null!=r){var n=this.quill.selection.normalizeNative(e),o=n?this.quill.selection.normalizedToRange(n):null;o&&this.replaceText(o,r)&&t.preventDefault()}}}}},{key:"handleCompositionStart",value:function(){var t=this.quill.getSelection();t&&this.replaceText(t)}}],r&&c(e.prototype,r),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),l}(i.Z);const d=y},3071:(t,e,r)=>{r.d(e,{ZP:()=>x,WQ:()=>C});var n=r(8805),o=r.n(n),i=r(2722),a=r.n(i),u=r(9098),l=r.n(u),c=r(1233),s=r(281),f=r(3122),p=r(7094),h=r(8034);function y(t){return y="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},y(t)}function d(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return b(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?b(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function v(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function m(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?v(Object(r),!0).forEach((function(e){g(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):v(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function g(t,e,r){return(e=O(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function w(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,O(n.key),n)}}function O(t){var e=function(t,e){if("object"!==y(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==y(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===y(e)?e:String(e)}function j(t,e){return j=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},j(t,e)}function S(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=P(t);if(e){var o=P(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===y(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function P(t){return P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},P(t)}var _=(0,f.Z)("quill:keyboard"),N={backspace:"backspace",tab:"tab",enter:"enter",escape:"escape",pageup:"pageUp",pagedown:"pageDown",end:"end",home:"home",arrowleft:"leftArrow",arrowup:"upArrow",arrowright:"rightArrow",arrowdown:"downArrow",delete:"del"," ":"space","*":"asterisk","-":"minus",alt:"alt",control:"control",shift:"shift",left:"leftArrow",up:"upArrow",right:"rightArrow",down:"downArrow",multiply:"asterisk",spacebar:"space",del:"del",subtract:"minus",esc:"escape"},E={8:"backspace",9:"tab",13:"enter",27:"escape",33:"pageUp",34:"pageDown",35:"end",36:"home",37:"leftArrow",38:"upArrow",39:"rightArrow",40:"downArrow",46:"del",32:"space",106:"asterisk",109:"minus",189:"minus",173:"minus",16:"shift",17:"control",18:"alt"},k=(0,h.Z)()&&/Mac/i.test(navigator.platform)?"metaKey":"ctrlKey",x=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&j(t,e)}(f,t);var e,r,n,i=S(f);function f(t,e){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,f),(r=i.call(this,t,e)).bindings={},Object.keys(r.options.bindings).forEach((function(t){r.options.bindings[t]&&r.addBinding(r.options.bindings[t])})),r.addInternalBindings(),r.listen(),r}return e=f,r=[{key:"addInternalBindings",value:function(){var t=this;this.quill.once(s.ZP.events.CONTENT_SETTED,(function(){t.addBinding({key:"enter",shiftKey:null},t.handleEnter),t.addBinding({key:"enter",metaKey:null,ctrlKey:null,altKey:null},(function(){})),(0,h.Z)()&&/Firefox/i.test(navigator.userAgent)?(t.addBinding({key:"backspace"},{collapsed:!0},t.handleBackspace),t.addBinding({key:"del"},{collapsed:!0},t.handleDelete)):(t.addBinding({key:"backspace"},{collapsed:!0,prefix:/^.?$/},t.handleBackspace),t.addBinding({key:"del"},{collapsed:!0,suffix:/^.?$/},t.handleDelete)),t.addBinding({key:"backspace"},{collapsed:!1},t.handleDeleteRange),t.addBinding({key:"del"},{collapsed:!1},t.handleDeleteRange),t.addBinding({key:"backspace",altKey:null,ctrlKey:null,metaKey:null,shiftKey:null},{collapsed:!0,offset:0},t.handleBackspace)}))}},{key:"addBinding",value:function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=function(t){if("string"==typeof t||"number"==typeof t)t={key:t};else{if("object"!==y(t))return null;t=o()(t)}return t.shortKey&&(t[k]=t.shortKey,delete t.shortKey),t}(t);if(null!=i){"function"==typeof r&&(r={handler:r}),"function"==typeof n&&(n={handler:n});var a=i.which?"which":"key";(Array.isArray(i[a])?i[a]:[i[a]]).forEach((function(t){var o=m(m(m({},i),{},{key:t},r),n);e.bindings[o.key]=e.bindings[o.key]||[],e.bindings[o.key].push(o)}))}else _.warn("Attempted to add invalid keyboard binding",i)}},{key:"listen",value:function(){var t=this;this.quill.root.addEventListener("keydown",(function(e){if(!e.defaultPrevented&&!e.isComposing){t.raiseOnKeydownCallback(e);var r=f.normalizeKeyName(e),n=(t.bindings[r]||[]).concat(t.bindings[e.which]||[]).filter((function(t){return f.match(e,t)}));if(0!==n.length){var o=t.quill.getSelection();if(null!=o&&t.quill.hasFocus()){var i=d(t.quill.getLine(o.index),2),u=i[0],l=i[1],s=d(t.quill.getLeaf(o.index),2),p=s[0],h=s[1],b=d(0===o.length?[p,h]:t.quill.getLeaf(o.index+o.length),2),v=b[0],m=b[1],g=p instanceof c.TextBlot?p.value().slice(0,h):"",w=v instanceof c.TextBlot?v.value().slice(m):"",O={collapsed:0===o.length,empty:0===o.length&&u.length()<=1,format:t.quill.getFormat(o),line:u,offset:l,prefix:g,suffix:w,event:e},j=!1;n.some((function(e){if(null!=e.collapsed&&e.collapsed!==O.collapsed)return!1;if(null!=e.empty&&e.empty!==O.empty)return!1;if(null!=e.offset&&e.offset!==O.offset)return!1;if(Array.isArray(e.format)){if(e.format.every((function(t){return null==O.format[t]})))return!1}else if("object"===y(e.format)&&!Object.keys(e.format).every((function(t){return!0===e.format[t]?null!=O.format[t]:!1===e.format[t]?null==O.format[t]:a()(e.format[t],O.format[t])})))return!1;if(null!=e.prefix&&!e.prefix.test(O.prefix))return!1;if(null!=e.suffix&&!e.suffix.test(O.suffix))return!1;var r=e.handler.call(t,o,O,e),n=null==r?void 0:r.preventAfterAllMatches;return(j=!0!==r||n)&&!n})),j&&e.preventDefault()}}}}))}},{key:"raiseOnKeydownCallback",value:function(t){var e=this.options.onKeydown;e&&"function"==typeof e&&e(t)}},{key:"handleBackspace",value:function(t,e){var r=/[\uD800-\uDBFF][\uDC00-\uDFFF]$/.test(e.prefix)?2:1;if(!(0===t.index||this.quill.getLength()<=1)){var n={},o=d(this.quill.getLine(t.index),1)[0],i=(new(l())).retain(t.index-r).delete(r);if(0===e.offset){var a=d(this.quill.getLine(t.index-1),1)[0];if(a){var c="block"===a.statics.blotName&&a.length()<=1,f=a.statics.blotName.startsWith("table"),p="block"===o.statics.blotName&&o.length()<=1;if(f&&(p&&o.remove(),this.quill.setSelection(t.index-1)),!c&&!f){var h=o.formats(),y=this.quill.getFormat(t.index-1,1);if(n=u.AttributeMap.diff(h,y)||{},Object.keys(n).length>0){var b=(new(l())).retain(t.index+o.length()-2).retain(1,n);i=i.compose(b)}}}}this.quill.updateContents(i,s.ZP.sources.USER),this.quill.focus()}}},{key:"handleDelete",value:function(t,e){var r=/^[\uD800-\uDBFF][\uDC00-\uDFFF]/.test(e.suffix)?2:1;if(!(t.index>=this.quill.getLength()-r)){var n={},o=d(this.quill.getLine(t.index),1)[0],i=(new(l())).retain(t.index).delete(r);if(e.offset>=o.length()-1){var a=d(this.quill.getLine(t.index+1),1)[0];if(a){var c=o.formats(),f=this.quill.getFormat(t.index,1);n=u.AttributeMap.diff(c,f)||{},Object.keys(n).length>0&&(i=i.retain(a.length()-1).retain(1,n))}}this.quill.updateContents(i,s.ZP.sources.USER),this.quill.focus()}}},{key:"handleDeleteRange",value:function(t,e){this.raiseOnKeydownCallback(e.event),C({range:t,quill:this.quill}),this.quill.focus()}},{key:"handleEnter",value:function(t,e){var r=this,n=Object.keys(e.format).reduce((function(t,n){return r.quill.scroll.query(n,c.Scope.BLOCK)&&!Array.isArray(e.format[n])&&(t[n]=e.format[n]),t}),{}),o=(new(l())).retain(t.index).delete(t.length).insert("\n",n);this.quill.updateContents(o,s.ZP.sources.USER),this.quill.setSelection(t.index+1,s.ZP.sources.SILENT),this.quill.focus();var i,a,u=d(this.quill.getLine(t.index+1),1)[0],f=(i=window.innerHeight,(a=u.domNode.getBoundingClientRect()).y<0||!(a.bottom>=i)&&null);null!==f&&u.domNode.scrollIntoView(f),Object.keys(e.format).forEach((function(t){null==n[t]&&(Array.isArray(e.format[t])||"code"!==t&&"link"!==t&&(r.raiseOnKeydownCallback(e.event),r.quill.format(t,e.format[t],s.ZP.sources.USER)))}))}}],n=[{key:"match",value:function(t,e){return!["altKey","ctrlKey","metaKey","shiftKey"].some((function(r){return!!e[r]!==t[r]&&null!==e[r]}))&&(e.key===f.normalizeKeyName(t)||e.key===t.which)}},{key:"normalizeKeyName",value:function(t){var e=t.key,r=!!e,n=r?e:t.which;return n&&(n=r?N[n.toLowerCase()]||n:E[n]||String.fromCharCode(n)),n}}],r&&w(e.prototype,r),n&&w(e,n),Object.defineProperty(e,"prototype",{writable:!1}),f}(p.Z);function T(t){return{key:"tab",shiftKey:!t,format:{"code-block":!0},handler:function(e){var r=this.quill.scroll.query("code-block"),n=0===e.length?this.quill.getLines(e.index,1):this.quill.getLines(e),o=e.index,i=e.length;n.forEach((function(e,n){t?(e.insertAt(0,r.TAB),0===n?o+=r.TAB.length:i+=r.TAB.length):0===e.domNode.textContent.indexOf(r.TAB)&&(e.deleteAt(0,r.TAB.length),0===n?o-=r.TAB.length:i-=r.TAB.length)})),this.quill.update(s.ZP.sources.USER),this.quill.setSelection(o,i,s.ZP.sources.SILENT)}}}function A(t,e){return g(g({key:t,shiftKey:e,altKey:null},"leftArrow"===t?"prefix":"suffix",/^$/),"handler",(function(r){var n=r.index;return"rightArrow"===t&&(n+=r.length+1),!(d(this.quill.getLeaf(n),1)[0]instanceof c.EmbedBlot&&("leftArrow"===t?e?this.quill.setSelection(r.index-1,r.length+1,s.ZP.sources.USER):this.quill.setSelection(r.index-1,s.ZP.sources.USER):e?this.quill.setSelection(r.index,r.length+1,s.ZP.sources.USER):this.quill.setSelection(r.index+r.length+1,s.ZP.sources.USER),1))}))}function R(t,e){return{key:t[0],which:e,shortKey:!0,handler:function(e,r){return this.quill.format(t,!r.format[t],s.ZP.sources.USER),{preventAfterAllMatches:!0}}}}function C(t){var e=t.quill,r=t.range,n=e.getLines(r),o={};if(n.length>1){var i=n[0].formats(),a=n[n.length-1].formats();o=u.AttributeMap.diff(a,i)||{}}e.deleteText(r,s.ZP.sources.USER),Object.keys(o).length>0&&e.formatLine(r.index,1,o,s.ZP.sources.USER),e.setSelection(r.index,s.ZP.sources.SILENT)}x.DEFAULTS={bindings:{bold:R("bold",66),italic:R("italic",73),underline:R("underline",85),indent:{key:"tab",format:["blockquote","indent","list"],handler:function(t,e){return!(!e.collapsed||0===e.offset)||(this.quill.format("indent","+1",s.ZP.sources.USER),!1)}},outdent:{key:"tab",shiftKey:!0,format:["blockquote","indent","list"],handler:function(t,e){return!(!e.collapsed||0===e.offset)||(this.quill.format("indent","-1",s.ZP.sources.USER),!1)}},"outdent backspace":{key:"backspace",collapsed:!0,shiftKey:null,metaKey:null,ctrlKey:null,altKey:null,format:["indent","list"],offset:0,handler:function(t,e){null!=e.format.indent?this.quill.format("indent","-1",s.ZP.sources.USER):null!=e.format.list&&this.quill.format("list",!1,s.ZP.sources.USER)}},"indent code-block":T(!0),"outdent code-block":T(!1),"remove tab":{key:"tab",shiftKey:!0,collapsed:!0,prefix:/\t$/,handler:function(t){this.quill.deleteText(t.index-1,1,s.ZP.sources.USER)}},tab:{key:"tab",handler:function(t,e){var r=e.format;if(r.tableCellLine||r.tableHeaderCellLine||r.tableHeaderCell||r.table)return!0;this.quill.history.cutoff();var n=(new(l())).retain(t.index).delete(t.length).insert("\t");return this.quill.updateContents(n,s.ZP.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index+1,s.ZP.sources.SILENT),!1}},"blockquote empty enter":{key:"enter",collapsed:!0,format:["blockquote"],empty:!0,handler:function(){this.quill.format("blockquote",!1,s.ZP.sources.USER)}},"list empty enter":{key:"enter",collapsed:!0,format:["list"],empty:!0,handler:function(t,e){var r={list:!1};e.format.indent&&(r.indent=!1),this.quill.formatLine(t.index,t.length,r,s.ZP.sources.USER)}},"checklist enter":{key:"enter",collapsed:!0,format:{list:"checked"},handler:function(t){var e=d(this.quill.getLine(t.index),2),r=e[0],n=e[1],o=m(m({},r.formats()),{},{list:"checked"}),i=(new(l())).retain(t.index).insert("\n",o).retain(r.length()-n-1).retain(1,{list:"unchecked"});this.quill.updateContents(i,s.ZP.sources.USER),this.quill.setSelection(t.index+1,s.ZP.sources.SILENT),this.quill.scrollIntoView()}},"header enter":{key:"enter",collapsed:!0,format:["header"],suffix:/^$/,handler:function(t,e){var r=d(this.quill.getLine(t.index),2),n=r[0],o=r[1],i=(new(l())).retain(t.index).insert("\n",e.format).retain(n.length()-o-1).retain(1,{header:null});this.quill.updateContents(i,s.ZP.sources.USER),this.quill.setSelection(t.index+1,s.ZP.sources.SILENT),this.quill.scrollIntoView()}},"list autofill":{key:"space",shiftKey:null,collapsed:!0,format:{"code-block":!1,blockquote:!1,table:!1},prefix:/^\s*?(\d+\.|-|\*|\[ ?\]|\[x\])$/,handler:function(t,e){if(null==this.quill.scroll.query("list"))return!0;var r,n=e.prefix.length,o=d(this.quill.getLine(t.index),2),i=o[0],a=o[1];if(a>n)return!0;switch(e.prefix.trim()){case"[]":case"[ ]":r="unchecked";break;case"[x]":r="checked";break;case"-":case"*":r="bullet";break;default:r="ordered"}this.quill.insertText(t.index," ",s.ZP.sources.USER),this.quill.history.cutoff();var u=(new(l())).retain(t.index-a).delete(n+1).retain(i.length()-2-a).retain(1,{list:r});return this.raiseOnKeydownCallback(e.event),this.quill.updateContents(u,s.ZP.sources.USER),this.quill.history.cutoff(),this.quill.setSelection(t.index-n,s.ZP.sources.SILENT),!1}},"code exit":{key:"enter",collapsed:!0,format:["code-block"],prefix:/^$/,suffix:/^\s*$/,handler:function(t){for(var e=d(this.quill.getLine(t.index),2),r=e[0],n=e[1],o=2,i=r;null!=i&&i.length()<=1&&i.formats()["code-block"];)if(i=i.prev,(o-=1)<=0){var a=(new(l())).retain(t.index+r.length()-n-2).retain(1,{"code-block":null}).delete(1);return this.quill.updateContents(a,s.ZP.sources.USER),this.quill.setSelection(t.index-1,s.ZP.sources.SILENT),!1}return!0}},"embed left":A("leftArrow",!1),"embed left shift":A("leftArrow",!0),"embed right":A("rightArrow",!1),"embed right shift":A("rightArrow",!0)}}},9072:(t,e,r)=>{r.d(e,{ZP:()=>Z});var n=r(9098),o=r.n(n),i=r(1233),a=r(6603),u=r(281),l=r(7094),c=r(6446),s=r(4122),f=r(3657),p=r(8222),h=r(7309),y=r(5635),d=r(8034);function b(t){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},b(t)}function v(t,e,r){return(e=S(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return g(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function w(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function O(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,S(n.key),n)}}function j(t,e,r){return e&&O(t.prototype,e),r&&O(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function S(t){var e=function(t,e){if("object"!==b(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===b(e)?e:String(e)}function P(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_(t,e)}function _(t,e){return _=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},_(t,e)}function N(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=x(t);if(e){var o=x(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===b(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return E(t)}(this,r)}}function E(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function k(){return k="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=x(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},k.apply(this,arguments)}function x(t){return x=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},x(t)}var T=new i.ClassAttributor("code-token","hljs",{scope:i.Scope.INLINE}),A=function(t){P(r,t);var e=N(r);function r(t,n,o){var i;return w(this,r),i=e.call(this,t,n,o),T.add(i.domNode,o),i}return j(r,[{key:"format",value:function(t,e){t!==r.blotName?k(x(r.prototype),"format",this).call(this,t,e):e?T.add(this.domNode,e):(T.remove(this.domNode),this.domNode.classList.remove(this.statics.className))}},{key:"optimize",value:function(){for(var t,e=arguments.length,n=new Array(e),o=0;o<e;o++)n[o]=arguments[o];(t=k(x(r.prototype),"optimize",this)).call.apply(t,[this].concat(n)),T.value(this.domNode)||this.unwrap()}}],[{key:"formats",value:function(t,e){for(;null!=t&&t!==e.domNode;){if(t.classList&&t.classList.contains(h.ZP.className))return k(x(r),"formats",this).call(this,t,e);t=t.parentNode}}}]),r}(a.Z);A.blotName="code-token",A.className="ql-token";var R=function(t){P(r,t);var e=N(r);function r(){return w(this,r),e.apply(this,arguments)}return j(r,[{key:"format",value:function(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-language",e):k(x(r.prototype),"format",this).call(this,t,e)}},{key:"replaceWith",value:function(t,e){return this.formatAt(0,this.length(),A.blotName,!1),k(x(r.prototype),"replaceWith",this).call(this,t,e)}}],[{key:"create",value:function(t){var e=k(x(r),"create",this).call(this,t);return"string"==typeof t&&e.setAttribute("data-language",t),e}},{key:"formats",value:function(t){return t.getAttribute("data-language")||"plain"}},{key:"register",value:function(){}}]),r}(h.ZP),C=function(t){P(r,t);var e=N(r);function r(){return w(this,r),e.apply(this,arguments)}return j(r,[{key:"attach",value:function(){k(x(r.prototype),"attach",this).call(this),this.forceNext=!1,this.scroll.emitMount(this)}},{key:"format",value:function(t,e){t===R.blotName&&(this.forceNext=!0,this.children.forEach((function(r){r.format(t,e)})))}},{key:"formatAt",value:function(t,e,n,o){n===R.blotName&&(this.forceNext=!0),k(x(r.prototype),"formatAt",this).call(this,t,e,n,o)}},{key:"highlight",value:function(t){var e=this,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(null!=this.children.head){var n=Array.from(this.domNode.childNodes).filter((function(t){return t!==e.uiNode})),i="".concat(n.map((function(t){return t.textContent})).join("\n"),"\n"),a=R.formats(this.children.head.domNode);if(r||this.forceNext||this.cachedText!==i){if(i.trim().length>0||null==this.cachedText){var u=this.children.reduce((function(t,e){return t.concat((0,c.qz)(e,!1))}),new(o())),l=t(i,a);u.diff(l).reduce((function(t,r){var n=r.retain,o=r.attributes;return n?(o&&Object.keys(o).forEach((function(r){-1!==[R.blotName,A.blotName].indexOf(r)&&e.formatAt(t,n,r,o[r])})),t+n):t}),0)}this.cachedText=i,this.forceNext=!1}}}},{key:"html",value:function(t,e){var r=m(this.children.find(t),1)[0],n=r?R.formats(r.domNode):"plain";return'<pre data-language="'.concat(n,'">\n').concat(this.code(t,e),"\n</pre>")}},{key:"optimize",value:function(t){if(k(x(r.prototype),"optimize",this).call(this,t),null!=this.parent&&null!=this.children.head&&null!=this.uiNode){var e=R.formats(this.children.head.domNode);e!==this.uiNode.value&&(this.uiNode.value=e)}}}]),r}(h.se);C.allowedChildren=[R],R.requiredContainer=C,R.allowedChildren=[A,f.Z,p.Z,s.Z];var Z=function(t){P(r,t);var e=N(r);function r(t,n){var o;if(w(this,r),null==(o=e.call(this,t,n)).options.hljs)throw new Error("Syntax module requires highlight.js. Please include the library on the page before Quill.");return o.languages=o.options.languages.reduce((function(t,e){return t[e.key]=!0,t}),{}),o.highlightBlot=o.highlightBlot.bind(E(o)),o.initListener(),o.initTimer(),o}return j(r,[{key:"initListener",value:function(){var t=this;this.quill.on(u.ZP.events.SCROLL_BLOT_MOUNT,(function(e){if(e instanceof C){var r=t.quill.root.ownerDocument.createElement("select");t.options.languages.forEach((function(t){var e=t.key,n=t.label,o=r.ownerDocument.createElement("option");o.textContent=n,o.setAttribute("value",e),r.appendChild(o)})),r.addEventListener("change",(function(){e.format(R.blotName,r.value),t.quill.root.focus(),t.highlight(e,!0)})),null==e.uiNode&&(e.attachUI(r),e.children.head&&(r.value=R.formats(e.children.head.domNode)))}}))}},{key:"initTimer",value:function(){var t=this,e=null;this.quill.on(u.ZP.events.SCROLL_OPTIMIZE,(function(){clearTimeout(e),e=setTimeout((function(){t.highlight(),e=null}),t.options.interval)}))}},{key:"highlight",value:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,r=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!this.quill.selection.composing){this.quill.update(u.ZP.sources.USER);var n=this.quill.getSelection();(null==e?this.quill.scroll.descendants(C):[e]).forEach((function(e){e.highlight(t.highlightBlot,r)})),this.quill.update(u.ZP.sources.SILENT),null!=n&&this.quill.setSelection(n,u.ZP.sources.SILENT)}}},{key:"highlightBlot",value:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"plain";if("plain"===(e=this.languages[e]?e:"plain"))return(0,p.b)(t).split("\n").reduce((function(t,r,n){return 0!==n&&t.insert("\n",v({},h.ZP.blotName,e)),t.insert(r)}),new(o()));var r=this.quill.root.ownerDocument.createElement("div");return r.classList.add(h.ZP.className),r.innerHTML=this.options.hljs.highlight(e,t).value,(0,y.fw)(this.quill.scroll,r,[function(t,e){var r=T.value(t);return r?e.compose((new(o())).retain(e.length(),v({},A.blotName,r))):e}],[function(t,r){return t.data.split("\n").reduce((function(t,r,n){return 0!==n&&t.insert("\n",v({},h.ZP.blotName,e)),t.insert(r)}),r)}],new WeakMap)}}],[{key:"register",value:function(){u.ZP.register(A,!0),u.ZP.register(R,!0),u.ZP.register(C,!0)}}]),r}(l.Z);Z.DEFAULTS={hljs:(0,d.Z)()?window.hljs:null,interval:1e3,languages:[{key:"plain",label:"Plain"},{key:"bash",label:"Bash"},{key:"cpp",label:"C++"},{key:"cs",label:"C#"},{key:"css",label:"CSS"},{key:"diff",label:"Diff"},{key:"xml",label:"HTML/XML"},{key:"java",label:"Java"},{key:"javascript",label:"Javascript"},{key:"markdown",label:"Markdown"},{key:"php",label:"PHP"},{key:"python",label:"Python"},{key:"ruby",label:"Ruby"},{key:"sql",label:"SQL"}]}},867:(t,e,r)=>{r.d(e,{Z:()=>k});var n=r(9098),o=r.n(n),i=r(1233),a=r(281),u=r(7094),l=r(1969),c=r(5874),s=r(5635),f=r(9466),p=r(7896),h=r(319),y=r(1342),d=r(2480);function b(t){return b="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},b(t)}function v(t,e,r){return(e=O(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function m(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return g(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?g(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function g(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function w(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,O(n.key),n)}}function O(t){var e=function(t,e){if("object"!==b(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==b(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===b(e)?e:String(e)}function j(t,e){return j=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},j(t,e)}function S(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=P(t);if(e){var o=P(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===b(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function P(t){return P=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},P(t)}var _=[null,null,null,-1],N=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&j(t,e)}(u,t);var e,r,n,i=S(u);function u(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return(t=i.call.apply(i,[this].concat(r))).tableBlots=[l.zW.blotName,l.iD.blotName],t.tableBlots.forEach((function(e){t.quill.editor.addImmediateFormat(e)})),t.integrateClipboard(),t.addKeyboardHandlers(),t.listenBalanceCells(),t}return e=u,r=[{key:"integrateClipboard",value:function(){var t=this;this.tableBlots.forEach((function(e){t.quill.clipboard.addTableBlot(e)})),this.quill.clipboard.addMatcher("td, th",E),this.quill.clipboard.addMatcher("table",(0,p.F)(h.Zt)),this.quill.clipboard.addMatcher("td, th",(0,p.a)(y.h6))}},{key:"addKeyboardHandlers",value:function(){var t=this,e=u.keyboardBindings;Object.keys(e).forEach((function(r){e[r]&&t.quill.keyboard.addBinding(e[r])}))}},{key:"balanceTables",value:function(){this.quill.scroll.descendants(l.xJ).forEach((function(t){t.balanceCells()}))}},{key:"deleteColumn",value:function(){var t=m(this.getTable(),3),e=t[0],r=t[2];(0,c.Z)(r)&&(e.deleteColumn(r.cellOffset()),this.quill.update(a.ZP.sources.USER))}},{key:"deleteRow",value:function(){var t=m(this.getTable(),2)[1];(0,c.Z)(t)&&(t.remove(),this.quill.update(a.ZP.sources.USER))}},{key:"deleteTable",value:function(){var t=m(this.getTable(),1)[0];if((0,c.Z)(t)){var e=t.offset();t.remove(),this.quill.update(a.ZP.sources.USER),this.quill.setSelection(e,a.ZP.sources.SILENT)}}},{key:"getTable",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.quill.getSelection();if(!(0,c.Z)(t))return _;var e=m(this.quill.getLine(t.index),2),r=e[0],n=e[1];if(!(0,c.Z)(r)||-1===this.tableBlots.indexOf(r.statics.blotName))return _;var o=r.parent,i=o.parent;return[i.parent.parent,i,o,n]}},{key:"insertColumn",value:function(t){var e=this.quill.getSelection(),r=m(this.getTable(e),3),n=r[0],o=r[1],i=r[2];if((0,c.Z)(i)){var u=i.cellOffset();n.insertColumn(u+t),this.quill.update(a.ZP.sources.USER);var l=o.rowOffset();0===t&&(l+=1),this.quill.setSelection(e.index+l,e.length,a.ZP.sources.SILENT)}}},{key:"insertColumnLeft",value:function(){this.insertColumn(0)}},{key:"insertColumnRight",value:function(){this.insertColumn(1)}},{key:"insertRow",value:function(t){var e=this.quill.getSelection(),r=m(this.getTable(e),3),n=r[0],o=r[1];if((0,c.Z)(r[2])){var i=o.rowOffset();n.insertRow(i+t),this.quill.update(a.ZP.sources.USER),t>0?this.quill.setSelection(e,a.ZP.sources.SILENT):this.quill.setSelection(e.index+o.children.length,e.length,a.ZP.sources.SILENT)}}},{key:"insertRowAbove",value:function(){this.insertRow(0)}},{key:"insertRowBelow",value:function(){this.insertRow(1)}},{key:"insertHeaderRow",value:function(){var t=this.quill.getSelection(),e=m(this.getTable(t),3),r=e[0];(0,c.Z)(e[2])&&(r.insertHeaderRow(),this.quill.update(a.ZP.sources.USER))}},{key:"insertTable",value:function(t,e){var r=this.quill.getSelection();if((0,c.Z)(r)){var n=new Array(t).fill(0).reduce((function(t){var r=(0,l.Lv)();return new Array(e).fill("\n").forEach((function(e){t.insert(e,{tableCellLine:{row:r,cell:(0,l.Lv)()}})})),t}),(new(o())).retain(r.index));this.quill.updateContents(n,a.ZP.sources.USER),this.quill.setSelection(r.index,a.ZP.sources.SILENT),this.balanceTables()}}},{key:"tableFormats",value:function(){return this.tableBlots}},{key:"listenBalanceCells",value:function(){var t=this;this.quill.on(a.ZP.events.SCROLL_OPTIMIZE,(function(e){e.some((function(e){return-1!==["TD","TH","TR","TBODY","THEAD","TABLE"].indexOf(e.target.tagName)&&(t.quill.once(a.ZP.events.TEXT_CHANGE,(function(e,r,n){n===a.ZP.sources.USER&&t.balanceTables()})),!0)}))})),this.quill.on(a.ZP.events.CONTENT_SETTED,(function(){t.quill.once(a.ZP.events.TEXT_CHANGE,(function(){t.balanceTables()}))}))}}],n=[{key:"register",value:function(){a.ZP.register(l.zW,!0),a.ZP.register(l.iD,!0),a.ZP.register(l.xs,!0),a.ZP.register(l.pj,!0),a.ZP.register(l.KA,!0),a.ZP.register(l.SC,!0),a.ZP.register(l.RM,!0),a.ZP.register(l.xD,!0),a.ZP.register(l.xJ,!0),[h.li,y.Du].forEach((function(t){Object.keys(t).forEach((function(e){a.ZP.register(v({},"formats/".concat(e),t[e]),!0)}))}))}}],r&&w(e.prototype,r),n&&w(e,n),Object.defineProperty(e,"prototype",{writable:!1}),u}(u.Z);function E(t,e){var r=t.parentNode,n="THEAD"===r.parentNode.tagName||null,i=Array.from(("TABLE"===r.parentNode.tagName?r.parentNode:r.parentNode.parentNode).querySelectorAll("tr")),a=Array.from(r.querySelectorAll("th,td")),u=i.indexOf(r)+1,l=a.indexOf(t)+1,c=n?"tableHeaderCellLine":"tableCellLine";return 0===e.length()?e=(new(o())).insert("\n",v({},c,{row:u,cell:l})):((0,s.PF)(e,"\n")||e.insert("\n"),(0,s.HI)(e,c,{row:u,cell:l}))}N.keyboardBindings={"table backspace":{key:"backspace",format:["tableCellLine","tableHeaderCellLine"],collapsed:!0,offset:0,handler:function(t){var e=m(this.quill.getLine(t.index),1)[0];return!(!e.prev||-1===["tableCellLine","tableHeaderCellLine"].indexOf(e.prev.statics.blotName))}},"table delete":{key:"del",format:["tableCellLine","tableHeaderCellLine"],collapsed:!0,suffix:/^$/,handler:function(){}},"table enter":{key:"enter",shiftKey:null,format:["tableCellLine","tableHeaderCellLine"],handler:function(t,e){var r,n,o=this,u=this.quill.getModule("table");if(u){var l=this.quill,c=m(u.getTable(t),1)[0];if(1===(null===(r=this.quill.selection)||void 0===r?void 0:r.lastNative.native.endContainer.nodeType)){var s=c.offset();(0,d.Z)({quill:l,index:s,table:c})}else if(null===(n=this.quill.selection)||void 0===n||!n.composing){t.length>0&&this.quill.scroll.deleteAt(t.index,t.length);var f=Object.keys(e.format).reduce((function(t,r){return o.quill.scroll.query(r,i.Scope.BLOCK)&&!Array.isArray(e.format[r])&&(t[r]=e.format[r]),t}),{});this.quill.insertText(t.index,"\n",f.tableCellLine,a.ZP.sources.USER),this.quill.setSelection(t.index+1,a.ZP.sources.SILENT),this.quill.focus(),Object.keys(e.format).forEach((function(t){null==f[t]&&(Array.isArray(e.format[t])||"link"!==t&&o.quill.format(t,e.format[t],a.ZP.sources.USER))}))}}}},"table tab":{key:"tab",shiftKey:null,format:["tableCellLine","tableHeaderCellLine"],handler:function(t,e){var r=e.event,n=e.line,o=n.offset(this.quill.scroll);this.quill.setSelection(r.shiftKey?o-1:o+n.length(),a.ZP.sources.USER)}},"table down":(0,f.Z)(!1,["tableCellLine","tableHeaderCellLine"]),"table up":(0,f.Z)(!0,["tableCellLine","tableHeaderCellLine"])};const k=N},6054:(t,e,r)=>{r.d(e,{Z:()=>rt});var n=r(9098),o=r.n(n),i=r(281),a=r(7094),u=r(6446),l=r(3553),c=r(5874),s=r(319),f=r(8536),p=r(2795);function h(t){return h="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},h(t)}function y(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||b(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function d(t){return function(t){if(Array.isArray(t))return v(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||b(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function b(t,e){if(t){if("string"==typeof t)return v(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?v(t,e):void 0}}function v(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function m(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function g(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==h(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==h(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===h(o)?o:String(o)),n)}var o}function w(t,e,r){return e&&g(t.prototype,e),r&&g(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function O(){return O="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=_(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},O.apply(this,arguments)}function j(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&S(t,e)}function S(t,e){return S=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},S(t,e)}function P(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=_(t);if(e){var o=_(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===h(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function _(t){return _=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},_(t)}var N="data-tablelite-",E=function(t){j(r,t);var e=P(r);function r(){return m(this,r),e.apply(this,arguments)}return w(r,[{key:"format",value:function(t,e){if(s.li[t]){var n,o="data-".concat(t.toLowerCase());(0,p.Z)(this.domNode,o,e),null===(n=this.row())||void 0===n||null===(n=n.table())||void 0===n||n.format(t,e)}else O(_(r.prototype),"format",this).call(this,t,e)}},{key:"cellOffset",value:function(){return this.parent?this.parent.children.indexOf(this):-1}},{key:"row",value:function(){return"table"in this.parent?this.parent:null}},{key:"rowOffset",value:function(){return this.row()?this.row().rowOffset():-1}},{key:"table",value:function(){var t;return null===(t=this.row())||void 0===t?void 0:t.table()}}],[{key:"create",value:function(t){var e=O(_(r),"create",this).call(this);return(0,p.Z)(e,this.dataAttribute,null!=t?t:q()),e}},{key:"formats",value:function(t){var e=this.dataAttribute;if(t.hasAttribute(e))return t.getAttribute(e)}}]),r}(u.ZP);E.tagName=["TD","TH"];var k=function(t){j(r,t);var e=P(r);function r(){return m(this,r),e.apply(this,arguments)}return w(r,[{key:"format",value:function(t,e){t===r.blotName&&e?this.domNode.setAttribute(r.dataAttribute,e):O(_(r.prototype),"format",this).call(this,t,e)}}]),r}(E);k.blotName="table",k.dataAttribute="".concat(N,"row");var x=function(t){j(r,t);var e=P(r);function r(){return m(this,r),e.apply(this,arguments)}return w(r,[{key:"format",value:function(t,e){t===r.blotName&&e?this.domNode.setAttribute(r.dataAttribute,e):O(_(r.prototype),"format",this).call(this,t,e)}}]),r}(E);x.tagName=["TH","TD"],x.blotName="tableHeaderCell",x.dataAttribute="".concat(N,"header-row");var T=function(t){j(r,t);var e=P(r);function r(){return m(this,r),e.apply(this,arguments)}return w(r,[{key:"checkMerge",value:function(){if(O(_(r.prototype),"checkMerge",this).call(this)&&(0,c.Z)(this.next.children.head)){var t=this.childFormatName,e=this.children.head.formats(),n=this.children.tail.formats(),o=this.next.children.head.formats(),i=this.next.children.tail.formats();return e[t]===n[t]&&e[t]===o[t]&&e[t]===i[t]}return!1}},{key:"optimize",value:function(){for(var t,e=this,n=arguments.length,o=new Array(n),i=0;i<n;i++)o[i]=arguments[i];(t=O(_(r.prototype),"optimize",this)).call.apply(t,[this].concat(o));var a=this.childFormatName;this.children.forEach((function(t){if((0,c.Z)(t.next)){var r=t.formats(),n=t.next.formats();if(r[a]!==n[a]){var o=e.splitAfter(t);o&&o.optimize(),e.prev&&e.prev.optimize()}}}))}},{key:"rowOffset",value:function(){return this.parent?this.parent.children.indexOf(this):-1}},{key:"table",value:function(){var t;return null===(t=this.parent)||void 0===t?void 0:t.parent}}]),r}(l.Z);T.tagName="TR";var A=function(t){j(r,t);var e=P(r);function r(t,n){var o;return m(this,r),(o=e.call(this,t,n)).childFormatName="table",o}return w(r)}(T);A.blotName="tableRow";var R=function(t){j(r,t);var e=P(r);function r(t,n){var o;return m(this,r),(o=e.call(this,t,n)).childFormatName="tableHeaderCell",o}return w(r)}(T);R.blotName="tableHeaderRow";var C=function(t){j(r,t);var e=P(r);function r(){return m(this,r),e.apply(this,arguments)}return w(r,[{key:"optimize",value:function(){var t;if(this.statics.requiredContainer&&!(this.parent instanceof this.statics.requiredContainer)){var e=this.children.head.children.head.domNode,n={};Object.keys(s.li).forEach((function(t){var r=e.dataset[t.toLowerCase()];r&&(n[t]=r)})),this.wrap(this.statics.requiredContainer.blotName,n)}for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];(t=O(_(r.prototype),"optimize",this)).call.apply(t,[this].concat(i))}}]),r}(l.Z),Z=function(t){j(r,t);var e=P(r);function r(){return m(this,r),e.apply(this,arguments)}return w(r)}(C);Z.blotName="tableBody",Z.tagName=["TBODY"];var L=function(t){j(r,t);var e=P(r);function r(){return m(this,r),e.apply(this,arguments)}return w(r)}(C);L.blotName="tableHeader",L.tagName=["THEAD"];var B=function(t){j(r,t);var e=P(r);function r(){return m(this,r),e.apply(this,arguments)}return w(r,[{key:"balanceCells",value:function(){var t=this.descendants(R),e=this.descendants(A),r=this.getMaxTableColCount(t,e);this.balanceRows(r,t,x),this.balanceRows(r,e,k)}},{key:"getMaxTableColCount",value:function(t,e){return Math.max(this.getMaxRowColCount(t),this.getMaxRowColCount(e))}},{key:"getMaxRowColCount",value:function(t){return Math.max.apply(Math,d(t.map((function(t){return t.children.length}))))}},{key:"balanceRows",value:function(t,e,r){var n=this;e.forEach((function(e){new Array(t-e.children.length).fill(0).forEach((function(){var t;(0,c.Z)(e.children.head)&&(t=r.formats(e.children.head.domNode));var o=n.scroll.create(r.blotName,t);e.appendChild(o),o.optimize()}))}))}},{key:"cells",value:function(t){return this.rows().map((function(e){return e.children.at(t)}))}},{key:"deleteColumn",value:function(t){var e=this;[L,Z].forEach((function(r){var n=y(e.descendants(r),1)[0];(0,c.Z)(n)&&(0,c.Z)(n.children.head)&&n.children.forEach((function(e){var r=e.children.at(t);(0,c.Z)(r)&&r.remove()}))}))}},{key:"insertColumn",value:function(t){var e=this;[L,Z].forEach((function(r){var n=y(e.descendants(r),1)[0];if((0,c.Z)(n)&&(0,c.Z)(n.children.head)){var o=r===L?x:k;n.children.forEach((function(r){var n=r.children.at(t),i=o.formats(r.children.head.domNode),a=e.scroll.create(o.blotName,i);r.insertBefore(a,n)}))}}))}},{key:"insertRow",value:function(t){var e=this,r=y(this.descendants(Z),1)[0];if((0,c.Z)(r)&&(0,c.Z)(r.children.head)){var n=q(),o=this.scroll.create(A.blotName);r.children.head.children.forEach((function(){var t=e.scroll.create(k.blotName,n);o.appendChild(t)}));var i=r.children.at(t);r.insertBefore(o,i)}}},{key:"insertHeaderRow",value:function(){var t=this,e=y(this.descendants(L),1)[0],r=y(this.descendants(Z),1)[0];if(!(0,c.Z)(e)&&(0,c.Z)(r)&&(0,c.Z)(r.children.head)){var n=q(),o=this.scroll.create(L.blotName),i=this.scroll.create(R.blotName),a=this.children.at(0);o.appendChild(i),r.children.head.children.forEach((function(){var e=t.scroll.create(x.blotName,n);i.appendChild(e),e.optimize()})),this.insertBefore(o,a)}}},{key:"rows",value:function(){var t=this.children.head;return(0,c.Z)(t)?t.children.map((function(t){return t})):[]}},{key:"formats",value:function(){var t={},e=this.cells()[0].domNode;return Object.keys(s.li).forEach((function(r){var n=e.dataset[r.toLowerCase()];n&&(t[r]=n)})),t}},{key:"format",value:function(t,e){var r=s.li[t];if(r){var n="data-".concat(t.toLowerCase());this.cells().forEach((function(t){(0,p.Z)(t.domNode,n,e)})),r.add(this.domNode,e)}}}],[{key:"create",value:function(t){var e=O(_(r),"create",this).call(this,t);return t&&Object.keys(t).forEach((function(r){var n;null===(n=s.li[r])||void 0===n||n.add(e,t[r])})),e}}]),r}(l.Z);function q(){return"row-".concat((0,f.Z)())}B.blotName="tableContainer",B.tagName="TABLE",B.allowedChildren=[L,Z],Z.requiredContainer=B,L.requiredContainer=B,Z.allowedChildren=[A],A.requiredContainer=Z,A.allowedChildren=[k],k.requiredContainer=A,L.allowedChildren=[R],R.requiredContainer=L,R.allowedChildren=[x],x.requiredContainer=R;var I=r(5635),D=r(9466);function M(t){var e=t.quill,r=t.range,n=t.index-1,a=(new(o())).retain(n).insert("\n");e.updateContents(a,i.ZP.sources.USER),e.setSelection(r.index+1,r.length,i.ZP.sources.SILENT)}var U=r(2480);function z(t,e,r){return null==t.prev&&null==t.next?null==e.prev&&null==e.next?0===r?-1:1:null==e.prev?-1:1:null==t.prev?-1:null==t.next?1:null}var F=r(7896),H=r(1342);function K(t){return K="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},K(t)}function W(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],l=!0,c=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;l=!1}else for(;!(l=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);l=!0);}catch(t){c=!0,o=t}finally{try{if(!l&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(c)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return $(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?$(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function G(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,V(n.key),n)}}function V(t){var e=function(t,e){if("object"!==K(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==K(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===K(e)?e:String(e)}function Y(t,e){return Y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Y(t,e)}function X(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Q(t);if(e){var o=Q(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===K(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function Q(t){return Q=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Q(t)}var J=[null,null,null,-1],tt=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Y(t,e)}(u,t);var e,r,n,a=X(u);function u(){var t;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u);for(var e=arguments.length,r=new Array(e),n=0;n<e;n++)r[n]=arguments[n];return(t=a.call.apply(a,[this].concat(r))).tableBlots=[k.blotName,x.blotName],t.tableBlots.forEach((function(e){t.quill.editor.addImmediateFormat(e)})),t.integrateClipboard(),t.addKeyboardHandlers(),t.listenBalanceCells(),t}return e=u,r=[{key:"integrateClipboard",value:function(){var t=this;this.tableBlots.forEach((function(e){return t.quill.clipboard.addTableBlot(e)})),this.quill.clipboard.addMatcher("tr",et),this.quill.clipboard.addMatcher("table",(0,F.F)(s.Zt)),this.quill.clipboard.addMatcher("td, th",(0,F.a)(H.h6))}},{key:"addKeyboardHandlers",value:function(){var t=this,e=u.keyboardBindings;Object.keys(e).forEach((function(r){e[r]&&t.quill.keyboard.addBinding(e[r])}))}},{key:"balanceTables",value:function(){this.quill.scroll.descendants(B).forEach((function(t){t.balanceCells()}))}},{key:"deleteColumn",value:function(){var t=W(this.getTable(),3),e=t[0],r=t[2];(0,c.Z)(r)&&(e.deleteColumn(r.cellOffset()),this.quill.update(i.ZP.sources.USER))}},{key:"deleteRow",value:function(){var t=W(this.getTable(),2)[1];(0,c.Z)(t)&&(t.remove(),this.quill.update(i.ZP.sources.USER))}},{key:"deleteTable",value:function(){var t=W(this.getTable(),1)[0];if((0,c.Z)(t)){var e=t.offset();t.remove(),this.quill.update(i.ZP.sources.USER),this.quill.setSelection(e,i.ZP.sources.SILENT)}}},{key:"getTable",value:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.quill.getSelection();if(!(0,c.Z)(t))return J;var e=W(this.quill.getLine(t.index),2),r=e[0],n=e[1];if(!(0,c.Z)(r)||-1===this.tableBlots.indexOf(r.statics.blotName))return J;var o=r.parent;return[o.parent.parent,o,r,n]}},{key:"insertColumn",value:function(t){var e=this.quill.getSelection(),r=W(this.getTable(e),3),n=r[0],o=r[1],a=r[2];if((0,c.Z)(a)){var u=a.cellOffset();n.insertColumn(u+t),this.quill.update(i.ZP.sources.USER);var l=o.rowOffset();0===t&&(l+=1),this.quill.setSelection(e.index+l,e.length,i.ZP.sources.SILENT)}}},{key:"insertColumnLeft",value:function(){this.insertColumn(0)}},{key:"insertColumnRight",value:function(){this.insertColumn(1)}},{key:"insertRow",value:function(t){var e=this.quill.getSelection(),r=W(this.getTable(e),3),n=r[0],o=r[1];if((0,c.Z)(r[2])){var a=o.rowOffset();n.insertRow(a+t),this.quill.update(i.ZP.sources.USER),t>0?this.quill.setSelection(e,i.ZP.sources.SILENT):this.quill.setSelection(e.index+o.children.length,e.length,i.ZP.sources.SILENT)}}},{key:"insertRowAbove",value:function(){this.insertRow(0)}},{key:"insertRowBelow",value:function(){this.insertRow(1)}},{key:"insertHeaderRow",value:function(){var t=this.quill.getSelection(),e=W(this.getTable(t),3),r=e[0];(0,c.Z)(e[2])&&(r.insertHeaderRow(),this.quill.update(i.ZP.sources.USER))}},{key:"insertTable",value:function(t,e){var r=this.quill.getSelection();if((0,c.Z)(r)){var n=new Array(t).fill(0).reduce((function(t){var r=new Array(e).fill("\n").join("");return t.insert(r,{table:q()})}),(new(o())).retain(r.index));this.quill.updateContents(n,i.ZP.sources.USER),this.quill.setSelection(r.index,i.ZP.sources.SILENT),this.balanceTables()}}},{key:"tableFormats",value:function(){return this.tableBlots}},{key:"listenBalanceCells",value:function(){var t=this;this.quill.on(i.ZP.events.SCROLL_OPTIMIZE,(function(e){e.some((function(e){return-1!==["TD","TH","TR","TBODY","THEAD","TABLE"].indexOf(e.target.tagName)&&(t.quill.once(i.ZP.events.TEXT_CHANGE,(function(e,r,n){n===i.ZP.sources.USER&&t.balanceTables()})),!0)}))})),this.quill.on(i.ZP.events.CONTENT_SETTED,(function(){t.quill.once(i.ZP.events.TEXT_CHANGE,(function(){t.balanceTables()}))}))}}],n=[{key:"register",value:function(){i.ZP.register(x,!0),i.ZP.register(k,!0),i.ZP.register(R,!0),i.ZP.register(A,!0),i.ZP.register(Z,!0),i.ZP.register(L,!0),i.ZP.register(B,!0),[s.li,H.Du].forEach((function(t){Object.keys(t).forEach((function(e){var r,n,o;i.ZP.register((r={},n="formats/".concat(e),o=t[e],(n=V(n))in r?Object.defineProperty(r,n,{value:o,enumerable:!0,configurable:!0,writable:!0}):r[n]=o,r),!0)}))}))}}],r&&G(e.prototype,r),n&&G(e,n),Object.defineProperty(e,"prototype",{writable:!1}),u}(a.Z);function et(t,e){var r="THEAD"===t.parentNode.tagName||null,n=Array.from(("TABLE"===t.parentNode.tagName?t.parentNode:t.parentNode.parentNode).querySelectorAll("tr")).indexOf(t)+1;return(0,I.HI)(e,r?"tableHeaderCell":"table",n)}tt.keyboardBindings={"table backspace":{key:"backspace",format:["table","tableHeaderCell"],collapsed:!0,offset:0,handler:function(){}},"table delete":{key:"del",format:["table","tableHeaderCell"],collapsed:!0,suffix:/^$/,handler:function(){}},"table enter":{key:"enter",shiftKey:null,format:["table"],handler:function(t){var e=this.quill.getModule("table");if(e){var r=this.quill,n=W(e.getTable(t),4),o=n[0],i=z(n[1],n[2],n[3]);if(null==i||i<0&&o.children.length>1&&o.children.head)return;var a=o.offset();i<0?M({quill:r,index:a,range:t}):(0,U.Z)({quill:r,index:a,table:o})}}},"table header enter":{key:"enter",shiftKey:null,format:["tableHeaderCell"],handler:function(t){var e=this.quill.getModule("table");if(e){var r=this.quill,n=W(e.getTable(t),4),o=n[0],i=z(n[1],n[2],n[3]);if(null==i)return;var a=o.offset();i<0||i>0&&o.children.length>1&&o.children.tail?M({quill:r,index:a,range:t}):(0,U.Z)({quill:r,index:a,table:o})}}},"table tab":{key:"tab",shiftKey:null,format:["table","tableHeaderCell"],handler:function(t,e){var r=e.event,n=e.line,o=n.offset(this.quill.scroll);this.quill.setSelection(r.shiftKey?o-1:o+n.length(),i.ZP.sources.USER)}},"table down":(0,D.Z)(!1,["table","tableHeaderCell"]),"table up":(0,D.Z)(!0,["table","tableHeaderCell"])};const rt=tt},2480:(t,e,r)=>{r.d(e,{Z:()=>a});var n=r(9098),o=r.n(n),i=r(281);function a(t){var e=t.quill,r=t.index+t.table.length(),n=(new(o())).retain(r).insert("\n");e.updateContents(n,i.ZP.sources.USER),e.setSelection(r,i.ZP.sources.USER)}},9466:(t,e,r)=>{r.d(e,{Z:()=>o});var n=r(281);function o(t,e){return{key:t?"upArrow":"downArrow",collapsed:!0,format:e,handler:function(e,r){var o=t?"prev":"next",i=r.line,a=i.statics.blotName.indexOf("Line")>-1?i.parent:i,u=a.parent.parent[o],l=a.parent[o]||(null==u?void 0:u.children.head);if(null!=l){if("tableRow"===l.statics.blotName||"tableHeaderRow"===l.statics.blotName){for(var c=l.children.head,s=a;null!=s.prev;)s=s.prev,c=c.next;var f=c.offset(this.quill.scroll)+Math.min(r.offset,c.length()-1);this.quill.setSelection(f,0,n.ZP.sources.USER)}}else{var p=a.table()[o];null!=p&&this.quill.setSelection(t?p.offset(this.quill.scroll)+p.length()-1:p.offset(this.quill.scroll),0,n.ZP.sources.USER)}return!1}}}},7896:(t,e,r)=>{r.d(e,{F:()=>h,a:()=>y});var n=r(1233),o=r(1674),i=r(4569),a=r(5635);function u(t){return u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},u(t)}function l(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function c(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?l(Object(r),!0).forEach((function(e){s(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):l(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function s(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==u(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==u(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===u(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function f(t,e,r,n){t[e]=!n&&t[e]?t[e]:r}function p(t,e,r,o,i,a){return t.filter((function(t){return!!t})).forEach((function(t){var u=r.query(t,n.Scope.ATTRIBUTE);if(null!==u){var l=u.value(e);if(l)return void f(i,u.attrName,l,a)}var c=o[t];if(null!=c&&(c.attrName===t||c.keyName===t)){var s=c.value(e)||void 0;f(i,c.attrName,s,a)}})),i}function h(t){return function(e,r,n){var u=o.Z.keys(e),l=i.Z.keys(e),s=c(c({},p(u,e,n,t,{},!0)),p(l,e,n,t,{},!0));return Object.keys(s).length>0?(0,a.HI)(r,s):r}}function y(t){return function(e,r,n){var u,l=o.Z.keys(e),s=i.Z.keys(e),f="TR"===(null===(u=e.parentNode)||void 0===u?void 0:u.tagName)?e.parentNode:void 0,h=c(c({},p(l,e,n,t,{},!0)),p(s,e,n,t,{},!0));return f&&(h=p(i.Z.keys(f),f,n,t,h,!1)),Object.keys(h).length>0?(0,a.HI)(r,h):r}}},3859:(t,e,r)=>{r.d(e,{Z:()=>d});var n=r(9098),o=r.n(n),i=r(2069),a=r(7094),u=r(8034);function l(t){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},l(t)}function c(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,s(n.key),n)}}function s(t){var e=function(t,e){if("object"!==l(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==l(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===l(e)?e:String(e)}function f(t,e){return f=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},f(t,e)}function p(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=h(t);if(e){var o=h(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===l(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function h(t){return h=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},h(t)}var y=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&f(t,e)}(i,t);var e,r,n,o=p(i);function i(t,e){var r;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),(r=o.call(this,t,e)).preventImageUploading(!1),r.addDragOverHandler(),r.addDropHandler(),r}return e=i,(r=[{key:"addDragOverHandler",value:function(){if((0,u.Z)()){var t=window.navigator.userAgent.toLowerCase();(-1!==t.indexOf("msie ")||-1!==t.indexOf("trident/")||-1!==t.indexOf("edge/"))&&this.quill.root.addEventListener("dragover",(function(t){t.preventDefault()}))}}},{key:"addDropHandler",value:function(){var t=this;this.quill.root.addEventListener("drop",(function(e){var r=0===e.dataTransfer.files.length,n=t.options.onDrop;if(n&&"function"==typeof n&&n(e),!r&&!t.preventImageUpload){var o;if(e.preventDefault(),document.caretRangeFromPoint)o=document.caretRangeFromPoint(e.clientX,e.clientY);else{if(!document.caretPositionFromPoint)return;var i=document.caretPositionFromPoint(e.clientX,e.clientY);(o=document.createRange()).setStart(i.offsetNode,i.offset),o.setEnd(i.offsetNode,i.offset)}var a=t.quill.selection.normalizeNative(o),u=t.quill.selection.normalizedToRange(a);t.upload(u,e.dataTransfer.files)}}))}},{key:"preventImageUploading",value:function(t){return void 0!==t&&(this.preventImageUpload=t),this.preventImageUpload}},{key:"upload",value:function(t,e,r){var n=this;if(!this.preventImageUpload||r){var o=[];Array.from(e).forEach((function(t){t&&-1!==n.options.mimetypes.indexOf(t.type)&&o.push(t)})),o.length>0&&this.options.handler.call(this,t,o,this.options.imageBlot)}}}])&&c(e.prototype,r),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(a.Z);y.DEFAULTS={mimetypes:["image/png","image/jpeg","image/pjpeg","image/gif","image/webp","image/bmp","image/svg+xml","image/vnd.microsoft.icon"],imageBlot:"image",handler:function(t,e,r){var n=this,a=e.map((function(t){return new Promise((function(e){var r=new FileReader;r.onload=function(t){e(t.target.result)},r.readAsDataURL(t)}))}));Promise.all(a).then((function(e){var a=e.reduce((function(t,e){return t.insert((n={},i=e,(o=s(o=r))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i,n));var n,o,i}),(new(o())).retain(t.index).delete(t.length));n.quill.updateContents(a,i.Z.sources.USER),n.quill.setSelection(t.index+e.length,i.Z.sources.SILENT)}))}};const d=y},2141:(t,e,r)=>{function n(t){return t?t.substring(0,1).toUpperCase()+t.substring(1):""}r.d(e,{Z:()=>n})},8034:(t,e,r)=>{r.d(e,{Z:()=>n});const n=function(){return"undefined"!=typeof window}},5874:(t,e,r)=>{function n(t){return null!=t}r.d(e,{Z:()=>n})},1233:(t,e,r)=>{var n;r.r(e),r.d(e,{Attributor:()=>j,AttributorStore:()=>A,BlockBlot:()=>q,ClassAttributor:()=>N,ContainerBlot:()=>m,EmbedBlot:()=>M,InlineBlot:()=>Z,LeafBlot:()=>O,ParentBlot:()=>d,Registry:()=>f,Scope:()=>o,ScrollBlot:()=>H,StyleAttributor:()=>T,TextBlot:()=>W}),function(t){t[t.TYPE=3]="TYPE",t[t.LEVEL=12]="LEVEL",t[t.ATTRIBUTE=13]="ATTRIBUTE",t[t.BLOT=14]="BLOT",t[t.INLINE=7]="INLINE",t[t.BLOCK=11]="BLOCK",t[t.BLOCK_BLOT=10]="BLOCK_BLOT",t[t.INLINE_BLOT=6]="INLINE_BLOT",t[t.BLOCK_ATTRIBUTE=9]="BLOCK_ATTRIBUTE",t[t.INLINE_ATTRIBUTE=5]="INLINE_ATTRIBUTE",t[t.ANY=15]="ANY"}(n||(n={}));const o=n;var i=function(){function t(){this.head=null,this.tail=null,this.length=0}return t.prototype.append=function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(this.insertBefore(t[0],null),t.length>1){var r=t.slice(1);this.append.apply(this,r)}},t.prototype.at=function(t){for(var e=this.iterator(),r=e();r&&t>0;)t-=1,r=e();return r},t.prototype.contains=function(t){for(var e=this.iterator(),r=e();r;){if(r===t)return!0;r=e()}return!1},t.prototype.indexOf=function(t){for(var e=this.iterator(),r=e(),n=0;r;){if(r===t)return n;n+=1,r=e()}return-1},t.prototype.insertBefore=function(t,e){null!=t&&(this.remove(t),t.next=e,null!=e?(t.prev=e.prev,null!=e.prev&&(e.prev.next=t),e.prev=t,e===this.head&&(this.head=t)):null!=this.tail?(this.tail.next=t,t.prev=this.tail,this.tail=t):(t.prev=null,this.head=this.tail=t),this.length+=1)},t.prototype.offset=function(t){for(var e=0,r=this.head;null!=r;){if(r===t)return e;e+=r.length(),r=r.next}return-1},t.prototype.remove=function(t){this.contains(t)&&(null!=t.prev&&(t.prev.next=t.next),null!=t.next&&(t.next.prev=t.prev),t===this.head&&(this.head=t.next),t===this.tail&&(this.tail=t.prev),this.length-=1)},t.prototype.iterator=function(t){return void 0===t&&(t=this.head),function(){var e=t;return null!=t&&(t=t.next),e}},t.prototype.find=function(t,e){void 0===e&&(e=!1);for(var r=this.iterator(),n=r();n;){var o=n.length();if(t<o||e&&t===o&&(null==n.next||0!==n.next.length()))return[n,t];t-=o,n=r()}return[null,0]},t.prototype.forEach=function(t){for(var e=this.iterator(),r=e();r;)t(r),r=e()},t.prototype.forEachAt=function(t,e,r){if(!(e<=0))for(var n=this.find(t),o=t-n[1],i=this.iterator(n[0]),a=i();a&&o<t+e;){var u=a.length();t>o?r(a,t-o,Math.min(e,o+u-t)):r(a,0,Math.min(u,t+e-o)),o+=u,a=i()}},t.prototype.map=function(t){return this.reduce((function(e,r){return e.push(t(r)),e}),[])},t.prototype.reduce=function(t,e){for(var r=this.iterator(),n=r();n;)e=t(e,n),n=r();return e},t}();const a=i;var u,l=(u=function(t,e){return u=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},u(t,e)},function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}u(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)});const c=function(t){function e(e){var r=this;return(r=t.call(this,e="[Parchment] "+e)||this).message=e,r.name=r.constructor.name,r}return l(e,t),e}(Error);var s=function(){function t(){this.attributes={},this.classes={},this.tags={},this.types={}}return t.find=function(t,e){if(void 0===e&&(e=!1),null==t)return null;if(this.blots.has(t))return this.blots.get(t)||null;if(e){var r=null;try{r=t.parentNode}catch(t){return null}return this.find(r,e)}return null},t.prototype.create=function(e,r,n){var o=this.query(r);if(null==o)throw new c("Unable to create ".concat(r," blot"));var i=o,a=r instanceof Node||r.nodeType===Node.TEXT_NODE?r:i.create(n),u=new i(e,a,n);return t.blots.set(u.domNode,u),u},t.prototype.find=function(e,r){return void 0===r&&(r=!1),t.find(e,r)},t.prototype.query=function(t,e){var r,n=this;return void 0===e&&(e=o.ANY),"string"==typeof t?r=this.types[t]||this.attributes[t]:t instanceof Text||t.nodeType===Node.TEXT_NODE?r=this.types.text:"number"==typeof t?t&o.LEVEL&o.BLOCK?r=this.types.block:t&o.LEVEL&o.INLINE&&(r=this.types.inline):t instanceof Element&&((t.getAttribute("class")||"").split(/\s+/).some((function(t){return!!(r=n.classes[t])})),r=r||this.tags[t.tagName]),null==r?null:e&o.LEVEL&r.scope&&e&o.TYPE&r.scope?r:null},t.prototype.register=function(){for(var t=this,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];if(e.length>1)return e.map((function(e){return t.register(e)}));var n=e[0];if("string"!=typeof n.blotName&&"string"!=typeof n.attrName)throw new c("Invalid definition");if("abstract"===n.blotName)throw new c("Cannot register abstract class");return this.types[n.blotName||n.attrName]=n,"string"==typeof n.keyName?this.attributes[n.keyName]=n:(null!=n.className&&(this.classes[n.className]=n),null!=n.tagName&&(n.tagName=Array.isArray(n.tagName)?n.tagName.map((function(t){return t.toUpperCase()})):n.tagName.toUpperCase(),(Array.isArray(n.tagName)?n.tagName:[n.tagName]).forEach((function(e){null!=t.tags[e]&&null!=n.className||(t.tags[e]=n)})))),n},t.blots=new WeakMap,t}();const f=s,p=function(){function t(t,e){this.scroll=t,this.domNode=e,f.blots.set(e,this),this.prev=null,this.next=null}return t.create=function(t){if(null==this.tagName)throw new c("Blot definition missing tagName");var e;return Array.isArray(this.tagName)?("string"==typeof t&&(t=t.toUpperCase(),parseInt(t,10).toString()===t&&(t=parseInt(t,10))),e="number"==typeof t?document.createElement(this.tagName[t-1]):this.tagName.indexOf(t)>-1?document.createElement(t):document.createElement(this.tagName[0])):e=document.createElement(this.tagName),this.className&&e.classList.add(this.className),e},Object.defineProperty(t.prototype,"statics",{get:function(){return this.constructor},enumerable:!1,configurable:!0}),t.prototype.attach=function(){},t.prototype.clone=function(){var t=this.domNode.cloneNode(!1);return this.scroll.create(t)},t.prototype.detach=function(){null!=this.parent&&this.parent.removeChild(this),f.blots.delete(this.domNode)},t.prototype.deleteAt=function(t,e){this.isolate(t,e).remove()},t.prototype.formatAt=function(t,e,r,n){var i=this.isolate(t,e);if(null!=this.scroll.query(r,o.BLOT)&&n)i.wrap(r,n);else if(null!=this.scroll.query(r,o.ATTRIBUTE)){var a=this.scroll.create(this.statics.scope);i.wrap(a),a.format(r,n)}},t.prototype.insertAt=function(t,e,r){var n=null==r?this.scroll.create("text",e):this.scroll.create(e,r),o=this.split(t);this.parent.insertBefore(n,o||void 0)},t.prototype.isolate=function(t,e){var r=this.split(t);if(null==r)throw new Error("Attempt to isolate at end");return r.split(e),r},t.prototype.length=function(){return 1},t.prototype.offset=function(t){return void 0===t&&(t=this.parent),null==this.parent||this===t?0:this.parent.children.offset(this)+this.parent.offset(t)},t.prototype.optimize=function(t){!this.statics.requiredContainer||this.parent instanceof this.statics.requiredContainer||this.wrap(this.statics.requiredContainer.blotName)},t.prototype.remove=function(){null!=this.domNode.parentNode&&this.domNode.parentNode.removeChild(this.domNode),this.detach()},t.prototype.replaceWith=function(t,e){var r="string"==typeof t?this.scroll.create(t,e):t;return null!=this.parent&&(this.parent.insertBefore(r,this.next||void 0),this.remove()),r},t.prototype.split=function(t,e){return 0===t?this:this.next},t.prototype.update=function(t,e){},t.prototype.wrap=function(t,e){var r="string"==typeof t?this.scroll.create(t,e):t;if(null!=this.parent&&this.parent.insertBefore(r,this.next||void 0),"function"!=typeof r.appendChild)throw new c("Cannot wrap ".concat(t));return r.appendChild(this),r},t.blotName="abstract",t}();var h=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();function y(t,e){var r=e.find(t);if(null==r)try{r=e.create(t)}catch(n){r=e.create(o.INLINE),Array.from(t.childNodes).forEach((function(t){r.domNode.appendChild(t)})),t.parentNode&&t.parentNode.replaceChild(r.domNode,t),r.attach()}return r}const d=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n.uiNode=null,n.build(),n}return h(e,t),e.prototype.appendChild=function(t){this.insertBefore(t)},e.prototype.attach=function(){t.prototype.attach.call(this),this.children.forEach((function(t){t.attach()}))},e.prototype.attachUI=function(t){null!=this.uiNode&&this.uiNode.remove(),this.uiNode=t,e.uiClass&&this.uiNode.classList.add(e.uiClass),this.uiNode.setAttribute("contenteditable","false"),this.domNode.insertBefore(this.uiNode,this.domNode.firstChild)},e.prototype.build=function(){var t=this;this.children=new a,Array.from(this.domNode.childNodes).filter((function(e){return e!==t.uiNode})).reverse().forEach((function(e){try{var r=y(e,t.scroll);t.insertBefore(r,t.children.head||void 0)}catch(t){if(t instanceof c)return;throw t}}))},e.prototype.deleteAt=function(t,e){if(0===t&&e===this.length())return this.remove();this.children.forEachAt(t,e,(function(t,e,r){t.deleteAt(e,r)}))},e.prototype.descendant=function(t,r){void 0===r&&(r=0);var n=this.children.find(r),o=n[0],i=n[1];return null==t.blotName&&t(o)||null!=t.blotName&&o instanceof t?[o,i]:o instanceof e?o.descendant(t,i):[null,-1]},e.prototype.descendants=function(t,r,n){void 0===r&&(r=0),void 0===n&&(n=Number.MAX_VALUE);var o=[],i=n;return this.children.forEachAt(r,n,(function(r,n,a){(null==t.blotName&&t(r)||null!=t.blotName&&r instanceof t)&&o.push(r),r instanceof e&&(o=o.concat(r.descendants(t,n,i))),i-=a})),o},e.prototype.detach=function(){this.children.forEach((function(t){t.detach()})),t.prototype.detach.call(this)},e.prototype.enforceAllowedChildren=function(){var t=this,r=!1;this.children.forEach((function(n){r||t.statics.allowedChildren.some((function(t){return n instanceof t}))||(n.statics.scope===o.BLOCK_BLOT?(null!=n.next&&t.splitAfter(n),null!=n.prev&&t.splitAfter(n.prev),n.parent.unwrap(),r=!0):n instanceof e?n.unwrap():n.remove())}))},e.prototype.formatAt=function(t,e,r,n){this.children.forEachAt(t,e,(function(t,e,o){t.formatAt(e,o,r,n)}))},e.prototype.insertAt=function(t,e,r){var n=this.children.find(t),o=n[0];if(o)o.insertAt(n[1],e,r);else{var i=null==r?this.scroll.create("text",e):this.scroll.create(e,r);this.appendChild(i)}},e.prototype.insertBefore=function(t,e){null!=t.parent&&t.parent.children.remove(t);var r=null;this.children.insertBefore(t,e||null),t.parent=this,null!=e&&(r=e.domNode),this.domNode.parentNode===t.domNode&&this.domNode.nextSibling===r||this.domNode.insertBefore(t.domNode,r),t.attach()},e.prototype.length=function(){return this.children.reduce((function(t,e){return t+e.length()}),0)},e.prototype.moveChildren=function(t,e){this.children.forEach((function(r){t.insertBefore(r,e)}))},e.prototype.optimize=function(e){if(t.prototype.optimize.call(this,e),this.enforceAllowedChildren(),null!=this.uiNode&&this.uiNode!==this.domNode.firstChild&&this.domNode.insertBefore(this.uiNode,this.domNode.firstChild),0===this.children.length)if(null!=this.statics.defaultChild){var r=this.scroll.create(this.statics.defaultChild.blotName);this.appendChild(r)}else this.remove()},e.prototype.path=function(t,r){void 0===r&&(r=!1);var n=this.children.find(t,r),o=n[0],i=n[1],a=[[this,t]];return o instanceof e?a.concat(o.path(i,r)):(null!=o&&a.push([o,i]),a)},e.prototype.removeChild=function(t){this.children.remove(t)},e.prototype.replaceWith=function(r,n){var o="string"==typeof r?this.scroll.create(r,n):r;return o instanceof e&&this.moveChildren(o),t.prototype.replaceWith.call(this,o)},e.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var r=this.clone();return this.parent&&this.parent.insertBefore(r,this.next||void 0),this.children.forEachAt(t,this.length(),(function(t,n,o){var i=t.split(n,e);null!=i&&r.appendChild(i)})),r},e.prototype.splitAfter=function(t){for(var e=this.clone();null!=t.next;)e.appendChild(t.next);return this.parent&&this.parent.insertBefore(e,this.next||void 0),e},e.prototype.unwrap=function(){this.parent&&this.moveChildren(this.parent,this.next||void 0),this.remove()},e.prototype.update=function(t,e){var r=this,n=[],o=[];t.forEach((function(t){t.target===r.domNode&&"childList"===t.type&&(n.push.apply(n,t.addedNodes),o.push.apply(o,t.removedNodes))})),o.forEach((function(t){if(!(null!=t.parentNode&&"IFRAME"!==t.tagName&&document.body.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY)){var e=r.scroll.find(t);null!=e&&(null!=e.domNode.parentNode&&e.domNode.parentNode!==r.domNode||e.detach())}})),n.filter((function(t){return t.parentNode===r.domNode||t===r.uiNode})).sort((function(t,e){return t===e?0:t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_FOLLOWING?1:-1})).forEach((function(t){var e=null;null!=t.nextSibling&&(e=r.scroll.find(t.nextSibling));var n=y(t,r.scroll);n.next===e&&null!=n.next||(null!=n.parent&&n.parent.removeChild(r),r.insertBefore(n,e||void 0))})),this.enforceAllowedChildren()},e.uiClass="",e}(p);var b=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),v=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return b(e,t),e.prototype.checkMerge=function(){return null!==this.next&&this.next.statics.blotName===this.statics.blotName},e.prototype.deleteAt=function(e,r){t.prototype.deleteAt.call(this,e,r),this.enforceAllowedChildren()},e.prototype.formatAt=function(e,r,n,o){t.prototype.formatAt.call(this,e,r,n,o),this.enforceAllowedChildren()},e.prototype.insertAt=function(e,r,n){t.prototype.insertAt.call(this,e,r,n),this.enforceAllowedChildren()},e.prototype.optimize=function(e){t.prototype.optimize.call(this,e),this.children.length>0&&null!=this.next&&this.checkMerge()&&(this.next.moveChildren(this),this.next.remove())},e.blotName="container",e.scope=o.BLOCK_BLOT,e}(d);const m=v;var g=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),w=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return g(e,t),e.value=function(t){return!0},e.prototype.index=function(t,e){return this.domNode===t||this.domNode.compareDocumentPosition(t)&Node.DOCUMENT_POSITION_CONTAINED_BY?Math.min(e,1):-1},e.prototype.position=function(t,e){var r=Array.from(this.parent.domNode.childNodes).indexOf(this.domNode);return t>0&&(r+=1),[this.parent.domNode,r]},e.prototype.value=function(){var t;return(t={})[this.statics.blotName]=this.statics.value(this.domNode)||!0,t},e.scope=o.INLINE_BLOT,e}(p);const O=w,j=function(){function t(t,e,r){void 0===r&&(r={}),this.attrName=t,this.keyName=e,this.scope=null!=r.scope?r.scope&o.LEVEL|o.TYPE&o.ATTRIBUTE:o.ATTRIBUTE,null!=r.whitelist&&(this.whitelist=r.whitelist)}return t.keys=function(t){return Array.from(t.attributes).map((function(t){return t.name}))},t.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.setAttribute(this.keyName,e),!0)},t.prototype.canAdd=function(t,e){return null==this.whitelist||("string"==typeof e?this.whitelist.indexOf(e.replace(/["']/g,""))>-1:this.whitelist.indexOf(e)>-1)},t.prototype.remove=function(t){t.removeAttribute(this.keyName)},t.prototype.value=function(t){var e=t.getAttribute(this.keyName);return this.canAdd(t,e)&&e?e:""},t}();var S=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();function P(t,e){return(t.getAttribute("class")||"").split(/\s+/).filter((function(t){return 0===t.indexOf("".concat(e,"-"))}))}var _=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return S(e,t),e.keys=function(t){return(t.getAttribute("class")||"").split(/\s+/).map((function(t){return t.split("-").slice(0,-1).join("-")}))},e.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(this.remove(t),t.classList.add("".concat(this.keyName,"-").concat(e)),!0)},e.prototype.remove=function(t){P(t,this.keyName).forEach((function(e){t.classList.remove(e)})),0===t.classList.length&&t.removeAttribute("class")},e.prototype.value=function(t){var e=(P(t,this.keyName)[0]||"").slice(this.keyName.length+1);return this.canAdd(t,e)?e:""},e}(j);const N=_;var E=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();function k(t){var e=t.split("-"),r=e.slice(1).map((function(t){return t[0].toUpperCase()+t.slice(1)})).join("");return e[0]+r}var x=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return E(e,t),e.keys=function(t){return(t.getAttribute("style")||"").split(";").map((function(t){return t.split(":")[0].trim()}))},e.prototype.add=function(t,e){return!!this.canAdd(t,e)&&(t.style[k(this.keyName)]=e,!0)},e.prototype.remove=function(t){t.style[k(this.keyName)]="",t.getAttribute("style")||t.removeAttribute("style")},e.prototype.value=function(t){var e=t.style[k(this.keyName)];return this.canAdd(t,e)?e:""},e}(j);const T=x,A=function(){function t(t){this.attributes={},this.domNode=t,this.build()}return t.prototype.attribute=function(t,e){e?t.add(this.domNode,e)&&(null!=t.value(this.domNode)?this.attributes[t.attrName]=t:delete this.attributes[t.attrName]):(t.remove(this.domNode),delete this.attributes[t.attrName])},t.prototype.build=function(){var t=this;this.attributes={};var e=f.find(this.domNode);if(null!=e){var r=j.keys(this.domNode),n=N.keys(this.domNode),i=T.keys(this.domNode);r.concat(n).concat(i).forEach((function(r){var n=e.scroll.query(r,o.ATTRIBUTE);n instanceof j&&(t.attributes[n.attrName]=n)}))}},t.prototype.copy=function(t){var e=this;Object.keys(this.attributes).forEach((function(r){var n=e.attributes[r].value(e.domNode);t.format(r,n)}))},t.prototype.move=function(t){var e=this;this.copy(t),Object.keys(this.attributes).forEach((function(t){e.attributes[t].remove(e.domNode)})),this.attributes={}},t.prototype.values=function(){var t=this;return Object.keys(this.attributes).reduce((function(e,r){return e[r]=t.attributes[r].value(t.domNode),e}),{})},t}();var R=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),C=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n.attributes=new A(n.domNode),n}return R(e,t),e.formats=function(t,r){var n=r.query(e.blotName);if(null==n||t.tagName!==n.tagName)return"string"==typeof this.tagName||(Array.isArray(this.tagName)?t.tagName.toLowerCase():void 0)},e.prototype.format=function(t,r){var n=this;if(t!==this.statics.blotName||r){var i=this.scroll.query(t,o.INLINE);if(null==i)return;i instanceof j?this.attributes.attribute(i,r):!r||t===this.statics.blotName&&this.formats()[t]===r||this.replaceWith(t,r)}else this.children.forEach((function(t){t instanceof e||(t=t.wrap(e.blotName,!0)),n.attributes.copy(t)})),this.unwrap()},e.prototype.formats=function(){var t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t},e.prototype.formatAt=function(e,r,n,i){null!=this.formats()[n]||this.scroll.query(n,o.ATTRIBUTE)?this.isolate(e,r).format(n,i):t.prototype.formatAt.call(this,e,r,n,i)},e.prototype.optimize=function(r){t.prototype.optimize.call(this,r);var n=this.formats();if(0===Object.keys(n).length)return this.unwrap();var o=this.next;o instanceof e&&o.prev===this&&function(t,e){if(Object.keys(t).length!==Object.keys(e).length)return!1;for(var r in t)if(t[r]!==e[r])return!1;return!0}(n,o.formats())&&(o.moveChildren(this),o.remove())},e.prototype.replaceWith=function(e,r){var n=t.prototype.replaceWith.call(this,e,r);return this.attributes.copy(n),n},e.prototype.update=function(e,r){var n=this;t.prototype.update.call(this,e,r),e.some((function(t){return t.target===n.domNode&&"attributes"===t.type}))&&this.attributes.build()},e.prototype.wrap=function(r,n){var o=t.prototype.wrap.call(this,r,n);return o instanceof e&&this.attributes.move(o),o},e.allowedChildren=[e,O],e.blotName="inline",e.scope=o.INLINE_BLOT,e.tagName="SPAN",e}(d);const Z=C;var L=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),B=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n.attributes=new A(n.domNode),n}return L(e,t),e.formats=function(t,r){var n=r.query(e.blotName);if(null==n||t.tagName!==n.tagName)return"string"==typeof this.tagName||(Array.isArray(this.tagName)?t.tagName.toLowerCase():void 0)},e.prototype.format=function(t,r){var n=this.scroll.query(t,o.BLOCK);null!=n&&(n instanceof j?this.attributes.attribute(n,r):t!==this.statics.blotName||r?!r||t===this.statics.blotName&&this.formats()[t]===r||this.replaceWith(t,r):this.replaceWith(e.blotName))},e.prototype.formats=function(){var t=this.attributes.values(),e=this.statics.formats(this.domNode,this.scroll);return null!=e&&(t[this.statics.blotName]=e),t},e.prototype.formatAt=function(e,r,n,i){null!=this.scroll.query(n,o.BLOCK)?this.format(n,i):t.prototype.formatAt.call(this,e,r,n,i)},e.prototype.insertAt=function(e,r,n){if(null==n||null!=this.scroll.query(r,o.INLINE))t.prototype.insertAt.call(this,e,r,n);else{var i=this.split(e);if(null==i)throw new Error("Attempt to insertAt after block boundaries");var a=this.scroll.create(r,n);i.parent.insertBefore(a,i)}},e.prototype.replaceWith=function(e,r){var n=t.prototype.replaceWith.call(this,e,r);return this.attributes.copy(n),n},e.prototype.update=function(e,r){var n=this;t.prototype.update.call(this,e,r),e.some((function(t){return t.target===n.domNode&&"attributes"===t.type}))&&this.attributes.build()},e.blotName="block",e.scope=o.BLOCK_BLOT,e.tagName="P",e.allowedChildren=[Z,e,O],e}(d);const q=B;var I=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),D=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return I(e,t),e.formats=function(t,e){},e.prototype.format=function(e,r){t.prototype.formatAt.call(this,0,this.length(),e,r)},e.prototype.formatAt=function(e,r,n,o){0===e&&r===this.length()?this.format(n,o):t.prototype.formatAt.call(this,e,r,n,o)},e.prototype.formats=function(){return this.statics.formats(this.domNode,this.scroll)},e}(O);const M=D;var U=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),z={attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0},F=function(t){function e(e,r){var n=t.call(this,null,r)||this;return n.registry=e,n.scroll=n,n.build(),n.observer=new MutationObserver((function(t){n.update(t)})),n.observer.observe(n.domNode,z),n.attach(),n}return U(e,t),e.prototype.create=function(t,e){return this.registry.create(this,t,e)},e.prototype.find=function(t,e){void 0===e&&(e=!1);var r=this.registry.find(t,e);return r?r.scroll===this?r:e?this.find(r.scroll.domNode.parentNode,!0):null:null},e.prototype.query=function(t,e){return void 0===e&&(e=o.ANY),this.registry.query(t,e)},e.prototype.register=function(){for(var t,e=[],r=0;r<arguments.length;r++)e[r]=arguments[r];return(t=this.registry).register.apply(t,e)},e.prototype.build=function(){null!=this.scroll&&t.prototype.build.call(this)},e.prototype.detach=function(){t.prototype.detach.call(this),this.observer.disconnect()},e.prototype.deleteAt=function(e,r){this.update(),0===e&&r===this.length()?this.children.forEach((function(t){t.remove()})):t.prototype.deleteAt.call(this,e,r)},e.prototype.formatAt=function(e,r,n,o){this.update(),t.prototype.formatAt.call(this,e,r,n,o)},e.prototype.insertAt=function(e,r,n){this.update(),t.prototype.insertAt.call(this,e,r,n)},e.prototype.optimize=function(e,r){var n=this;void 0===e&&(e=[]),void 0===r&&(r={}),t.prototype.optimize.call(this,r);for(var o=r.mutationsMap||new WeakMap,i=Array.from(this.observer.takeRecords());i.length>0;)e.push(i.pop());for(var a=function t(e,r){void 0===r&&(r=!0),null!=e&&e!==n&&null!=e.domNode.parentNode&&(o.has(e.domNode)||o.set(e.domNode,[]),r&&t(e.parent))},u=function t(e){o.has(e.domNode)&&(e instanceof d&&e.children.forEach(t),o.delete(e.domNode),e.optimize(r))},l=e,c=0;l.length>0;c+=1){if(c>=100)throw new Error("[Parchment] Maximum optimize iterations reached");for(l.forEach((function(t){var e=n.find(t.target,!0);null!=e&&(e.domNode===t.target&&("childList"===t.type?(a(n.find(t.previousSibling,!1)),Array.from(t.addedNodes).forEach((function(t){var e=n.find(t,!1);a(e,!1),e instanceof d&&e.children.forEach((function(t){a(t,!1)}))}))):"attributes"===t.type&&a(e.prev)),a(e))})),this.children.forEach(u),i=(l=Array.from(this.observer.takeRecords())).slice();i.length>0;)e.push(i.pop())}},e.prototype.update=function(e,r){var n=this;void 0===r&&(r={}),e=e||this.observer.takeRecords();var o=new WeakMap;e.map((function(t){var e=n.find(t.target,!0);return null==e?null:o.has(e.domNode)?(o.get(e.domNode).push(t),null):(o.set(e.domNode,[t]),e)})).forEach((function(t){null!=t&&t!==n&&o.has(t.domNode)&&t.update(o.get(t.domNode)||[],r)})),r.mutationsMap=o,o.has(this.domNode)&&t.prototype.update.call(this,o.get(this.domNode),r),this.optimize(e,r)},e.blotName="scroll",e.defaultChild=q,e.allowedChildren=[q,m],e.scope=o.BLOCK_BLOT,e.tagName="DIV",e}(d);const H=F;var K=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!=typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}();const W=function(t){function e(e,r){var n=t.call(this,e,r)||this;return n.text=n.statics.value(n.domNode),n}return K(e,t),e.create=function(t){return document.createTextNode(t)},e.value=function(t){return t.data},e.prototype.deleteAt=function(t,e){this.domNode.data=this.text=this.text.slice(0,t)+this.text.slice(t+e)},e.prototype.index=function(t,e){return this.domNode===t?e:-1},e.prototype.insertAt=function(e,r,n){null==n?(this.text=this.text.slice(0,e)+r+this.text.slice(e),this.domNode.data=this.text):t.prototype.insertAt.call(this,e,r,n)},e.prototype.length=function(){return this.text.length},e.prototype.optimize=function(r){t.prototype.optimize.call(this,r),this.text=this.statics.value(this.domNode),0===this.text.length?this.remove():this.next instanceof e&&this.next.prev===this&&(this.insertAt(this.length(),this.next.value()),this.next.remove())},e.prototype.position=function(t,e){return[this.domNode,t]},e.prototype.split=function(t,e){if(void 0===e&&(e=!1),!e){if(0===t)return this;if(t===this.length())return this.next}var r=this.scroll.create(this.domNode.splitText(t));return this.parent.insertBefore(r,this.next||void 0),this.text=this.statics.value(this.domNode),r},e.prototype.update=function(t,e){var r=this;t.some((function(t){return"characterData"===t.type&&t.target===r.domNode}))&&(this.text=this.statics.value(this.domNode))},e.prototype.value=function(){return this.text},e.blotName="text",e.scope=o.INLINE_BLOT,e}(O)},3034:function(t,e){var r=Object.prototype.hasOwnProperty,n="~";function o(){}function i(t,e,r){this.fn=t,this.context=e,this.once=r||!1}function a(t,e,r,o,a){if("function"!=typeof r)throw new TypeError("The listener must be a function");var u=new i(r,o||t,a),l=n?n+e:e;return t._events[l]?t._events[l].fn?t._events[l]=[t._events[l],u]:t._events[l].push(u):(t._events[l]=u,t._eventsCount++),t}function u(t,e){0==--t._eventsCount?t._events=new o:delete t._events[e]}function l(){this._events=new o,this._eventsCount=0}Object.create&&(o.prototype=Object.create(null),(new o).__proto__||(n=!1)),l.prototype.eventNames=function(){var t,e,o=[];if(0===this._eventsCount)return o;for(e in t=this._events)r.call(t,e)&&o.push(n?e.slice(1):e);return Object.getOwnPropertySymbols?o.concat(Object.getOwnPropertySymbols(t)):o},l.prototype.listeners=function(t){var e=this._events[n?n+t:t];if(!e)return[];if(e.fn)return[e.fn];for(var r=0,o=e.length,i=new Array(o);r<o;r++)i[r]=e[r].fn;return i},l.prototype.listenerCount=function(t){var e=this._events[n?n+t:t];return e?e.fn?1:e.length:0},l.prototype.emit=function(t,e,r,o,i,a){var u=n?n+t:t;if(!this._events[u])return!1;var l,c,s=this._events[u],f=arguments.length;if(s.fn){switch(s.once&&this.removeListener(t,s.fn,void 0,!0),f){case 1:return s.fn.call(s.context),!0;case 2:return s.fn.call(s.context,e),!0;case 3:return s.fn.call(s.context,e,r),!0;case 4:return s.fn.call(s.context,e,r,o),!0;case 5:return s.fn.call(s.context,e,r,o,i),!0;case 6:return s.fn.call(s.context,e,r,o,i,a),!0}for(c=1,l=new Array(f-1);c<f;c++)l[c-1]=arguments[c];s.fn.apply(s.context,l)}else{var p,h=s.length;for(c=0;c<h;c++)switch(s[c].once&&this.removeListener(t,s[c].fn,void 0,!0),f){case 1:s[c].fn.call(s[c].context);break;case 2:s[c].fn.call(s[c].context,e);break;case 3:s[c].fn.call(s[c].context,e,r);break;case 4:s[c].fn.call(s[c].context,e,r,o);break;default:if(!l)for(p=1,l=new Array(f-1);p<f;p++)l[p-1]=arguments[p];s[c].fn.apply(s[c].context,l)}}return!0},l.prototype.on=function(t,e,r){return a(this,t,e,r,!1)},l.prototype.once=function(t,e,r){return a(this,t,e,r,!0)},l.prototype.removeListener=function(t,e,r,o){var i=n?n+t:t;if(!this._events[i])return this;if(!e)return u(this,i),this;var a=this._events[i];if(a.fn)a.fn!==e||o&&!a.once||r&&a.context!==r||u(this,i);else{for(var l=0,c=[],s=a.length;l<s;l++)(a[l].fn!==e||o&&!a[l].once||r&&a[l].context!==r)&&c.push(a[l]);c.length?this._events[i]=1===c.length?c[0]:c:u(this,i)}return this},l.prototype.removeAllListeners=function(t){var e;return t?this._events[e=n?n+t:t]&&u(this,e):(this._events=new o,this._eventsCount=0),this},l.prototype.off=l.prototype.removeListener,l.prototype.addListener=l.prototype.on,l.prefixed=n,l.EventEmitter=l,void 0!==t&&(t.exports=l)},1456:t=>{var e=-1,r=1,n=0;function o(t,c,s,f){if(t===c)return t?[[n,t]]:[];if(null!=s){var p=function(t,e,r){var n="number"==typeof r?{index:r,length:0}:r.oldRange,o="number"==typeof r?null:r.newRange,i=t.length,a=e.length;if(0===n.length&&(null===o||0===o.length)){var u=n.index,l=t.slice(0,u),c=t.slice(u),s=o?o.index:null,f=u+a-i;if((null===s||s===f)&&!(f<0||f>a)){var p=e.slice(0,f);if((b=e.slice(f))===c){var y=Math.min(u,f);if((m=l.slice(0,y))===(w=p.slice(0,y)))return h(m,l.slice(y),p.slice(y),c)}}if(null===s||s===u){var d=u,b=(p=e.slice(0,d),e.slice(d));if(p===l){var v=Math.min(i-d,a-d);if((g=c.slice(c.length-v))===(O=b.slice(b.length-v)))return h(l,c.slice(0,c.length-v),b.slice(0,b.length-v),g)}}}if(n.length>0&&o&&0===o.length){var m=t.slice(0,n.index),g=t.slice(n.index+n.length);if(!(a<(y=m.length)+(v=g.length))){var w=e.slice(0,y),O=e.slice(a-v);if(m===w&&g===O)return h(m,t.slice(y,i-v),e.slice(y,a-v),g)}}return null}(t,c,s);if(p)return p}var y=a(t,c),d=t.substring(0,y);y=u(t=t.substring(y),c=c.substring(y));var b=t.substring(t.length-y),v=function(t,l){var c;if(!t)return[[r,l]];if(!l)return[[e,t]];var s=t.length>l.length?t:l,f=t.length>l.length?l:t,p=s.indexOf(f);if(-1!==p)return c=[[r,s.substring(0,p)],[n,f],[r,s.substring(p+f.length)]],t.length>l.length&&(c[0][0]=c[2][0]=e),c;if(1===f.length)return[[e,t],[r,l]];var h=function(t,e){var r=t.length>e.length?t:e,n=t.length>e.length?e:t;if(r.length<4||2*n.length<r.length)return null;function o(t,e,r){for(var n,o,i,l,c=t.substring(r,r+Math.floor(t.length/4)),s=-1,f="";-1!==(s=e.indexOf(c,s+1));){var p=a(t.substring(r),e.substring(s)),h=u(t.substring(0,r),e.substring(0,s));f.length<h+p&&(f=e.substring(s-h,s)+e.substring(s,s+p),n=t.substring(0,r-h),o=t.substring(r+p),i=e.substring(0,s-h),l=e.substring(s+p))}return 2*f.length>=t.length?[n,o,i,l,f]:null}var i,l,c,s,f,p=o(r,n,Math.ceil(r.length/4)),h=o(r,n,Math.ceil(r.length/2));if(!p&&!h)return null;i=h?p&&p[4].length>h[4].length?p:h:p,t.length>e.length?(l=i[0],c=i[1],s=i[2],f=i[3]):(s=i[0],f=i[1],l=i[2],c=i[3]);var y=i[4];return[l,c,s,f,y]}(t,l);if(h){var y=h[1],d=h[3],b=h[4],v=o(h[0],h[2]),m=o(y,d);return v.concat([[n,b]],m)}return function(t,n){for(var o=t.length,a=n.length,u=Math.ceil((o+a)/2),l=u,c=2*u,s=new Array(c),f=new Array(c),p=0;p<c;p++)s[p]=-1,f[p]=-1;s[l+1]=0,f[l+1]=0;for(var h=o-a,y=h%2!=0,d=0,b=0,v=0,m=0,g=0;g<u;g++){for(var w=-g+d;w<=g-b;w+=2){for(var O=l+w,j=(E=w===-g||w!==g&&s[O-1]<s[O+1]?s[O+1]:s[O-1]+1)-w;E<o&&j<a&&t.charAt(E)===n.charAt(j);)E++,j++;if(s[O]=E,E>o)b+=2;else if(j>a)d+=2;else if(y&&(_=l+h-w)>=0&&_<c&&-1!==f[_]&&E>=(P=o-f[_]))return i(t,n,E,j)}for(var S=-g+v;S<=g-m;S+=2){for(var P,_=l+S,N=(P=S===-g||S!==g&&f[_-1]<f[_+1]?f[_+1]:f[_-1]+1)-S;P<o&&N<a&&t.charAt(o-P-1)===n.charAt(a-N-1);)P++,N++;if(f[_]=P,P>o)m+=2;else if(N>a)v+=2;else if(!y){var E;if((O=l+h-S)>=0&&O<c&&-1!==s[O])if(j=l+(E=s[O])-O,E>=(P=o-P))return i(t,n,E,j)}}}return[[e,t],[r,n]]}(t,l)}(t=t.substring(0,t.length-y),c=c.substring(0,c.length-y));return d&&v.unshift([n,d]),b&&v.push([n,b]),l(v,f),v}function i(t,e,r,n){var i=t.substring(0,r),a=e.substring(0,n),u=t.substring(r),l=e.substring(n),c=o(i,a),s=o(u,l);return c.concat(s)}function a(t,e){if(!t||!e||t.charAt(0)!==e.charAt(0))return 0;for(var r=0,n=Math.min(t.length,e.length),o=n,i=0;r<o;)t.substring(i,o)==e.substring(i,o)?i=r=o:n=o,o=Math.floor((n-r)/2+r);return c(t.charCodeAt(o-1))&&o--,o}function u(t,e){if(!t||!e||t.slice(-1)!==e.slice(-1))return 0;for(var r=0,n=Math.min(t.length,e.length),o=n,i=0;r<o;)t.substring(t.length-o,t.length-i)==e.substring(e.length-o,e.length-i)?i=r=o:n=o,o=Math.floor((n-r)/2+r);return s(t.charCodeAt(t.length-o))&&o--,o}function l(t,o){t.push([n,""]);for(var i,c=0,s=0,h=0,y="",d="";c<t.length;)if(c<t.length-1&&!t[c][1])t.splice(c,1);else switch(t[c][0]){case r:h++,d+=t[c][1],c++;break;case e:s++,y+=t[c][1],c++;break;case n:var b=c-h-s-1;if(o){if(b>=0&&p(t[b][1])){var v=t[b][1].slice(-1);if(t[b][1]=t[b][1].slice(0,-1),y=v+y,d=v+d,!t[b][1]){t.splice(b,1),c--;var m=b-1;t[m]&&t[m][0]===r&&(h++,d=t[m][1]+d,m--),t[m]&&t[m][0]===e&&(s++,y=t[m][1]+y,m--),b=m}}f(t[c][1])&&(v=t[c][1].charAt(0),t[c][1]=t[c][1].slice(1),y+=v,d+=v)}if(c<t.length-1&&!t[c][1]){t.splice(c,1);break}if(y.length>0||d.length>0){y.length>0&&d.length>0&&(0!==(i=a(d,y))&&(b>=0?t[b][1]+=d.substring(0,i):(t.splice(0,0,[n,d.substring(0,i)]),c++),d=d.substring(i),y=y.substring(i)),0!==(i=u(d,y))&&(t[c][1]=d.substring(d.length-i)+t[c][1],d=d.substring(0,d.length-i),y=y.substring(0,y.length-i)));var g=h+s;0===y.length&&0===d.length?(t.splice(c-g,g),c-=g):0===y.length?(t.splice(c-g,g,[r,d]),c=c-g+1):0===d.length?(t.splice(c-g,g,[e,y]),c=c-g+1):(t.splice(c-g,g,[e,y],[r,d]),c=c-g+2)}0!==c&&t[c-1][0]===n?(t[c-1][1]+=t[c][1],t.splice(c,1)):c++,h=0,s=0,y="",d=""}""===t[t.length-1][1]&&t.pop();var w=!1;for(c=1;c<t.length-1;)t[c-1][0]===n&&t[c+1][0]===n&&(t[c][1].substring(t[c][1].length-t[c-1][1].length)===t[c-1][1]?(t[c][1]=t[c-1][1]+t[c][1].substring(0,t[c][1].length-t[c-1][1].length),t[c+1][1]=t[c-1][1]+t[c+1][1],t.splice(c-1,1),w=!0):t[c][1].substring(0,t[c+1][1].length)==t[c+1][1]&&(t[c-1][1]+=t[c+1][1],t[c][1]=t[c][1].substring(t[c+1][1].length)+t[c+1][1],t.splice(c+1,1),w=!0)),c++;w&&l(t,o)}function c(t){return t>=55296&&t<=56319}function s(t){return t>=56320&&t<=57343}function f(t){return s(t.charCodeAt(0))}function p(t){return c(t.charCodeAt(t.length-1))}function h(t,o,i,a){return p(t)||f(a)?null:function(t){for(var e=[],r=0;r<t.length;r++)t[r][1].length>0&&e.push(t[r]);return e}([[n,t],[e,o],[r,i],[n,a]])}function y(t,e,r){return o(t,e,r,!0)}y.INSERT=r,y.DELETE=e,y.EQUAL=n,t.exports=y},8805:(t,e,r)=>{t=r.nmd(t);var n="__lodash_hash_undefined__",o=9007199254740991,i="[object Arguments]",a="[object Boolean]",u="[object Date]",l="[object Function]",c="[object GeneratorFunction]",s="[object Map]",f="[object Number]",p="[object Object]",h="[object Promise]",y="[object RegExp]",d="[object Set]",b="[object String]",v="[object Symbol]",m="[object WeakMap]",g="[object ArrayBuffer]",w="[object DataView]",O="[object Float32Array]",j="[object Float64Array]",S="[object Int8Array]",P="[object Int16Array]",_="[object Int32Array]",N="[object Uint8Array]",E="[object Uint8ClampedArray]",k="[object Uint16Array]",x="[object Uint32Array]",T=/\w*$/,A=/^\[object .+?Constructor\]$/,R=/^(?:0|[1-9]\d*)$/,C={};C[i]=C["[object Array]"]=C[g]=C[w]=C[a]=C[u]=C[O]=C[j]=C[S]=C[P]=C[_]=C[s]=C[f]=C[p]=C[y]=C[d]=C[b]=C[v]=C[N]=C[E]=C[k]=C[x]=!0,C["[object Error]"]=C[l]=C[m]=!1;var Z="object"==typeof self&&self&&self.Object===Object&&self,L="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g||Z||Function("return this")(),B=e&&!e.nodeType&&e,q=B&&t&&!t.nodeType&&t,I=q&&q.exports===B;function D(t,e){return t.set(e[0],e[1]),t}function M(t,e){return t.add(e),t}function U(t,e,r,n){var o=-1,i=t?t.length:0;for(n&&i&&(r=t[++o]);++o<i;)r=e(r,t[o],o,t);return r}function z(t){var e=!1;if(null!=t&&"function"!=typeof t.toString)try{e=!!(t+"")}catch(t){}return e}function F(t){var e=-1,r=Array(t.size);return t.forEach((function(t,n){r[++e]=[n,t]})),r}function H(t,e){return function(r){return t(e(r))}}function K(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}var W,$=Array.prototype,G=Function.prototype,V=Object.prototype,Y=L["__core-js_shared__"],X=(W=/[^.]+$/.exec(Y&&Y.keys&&Y.keys.IE_PROTO||""))?"Symbol(src)_1."+W:"",Q=G.toString,J=V.hasOwnProperty,tt=V.toString,et=RegExp("^"+Q.call(J).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),rt=I?L.Buffer:void 0,nt=L.Symbol,ot=L.Uint8Array,it=H(Object.getPrototypeOf,Object),at=Object.create,ut=V.propertyIsEnumerable,lt=$.splice,ct=Object.getOwnPropertySymbols,st=rt?rt.isBuffer:void 0,ft=H(Object.keys,Object),pt=qt(L,"DataView"),ht=qt(L,"Map"),yt=qt(L,"Promise"),dt=qt(L,"Set"),bt=qt(L,"WeakMap"),vt=qt(Object,"create"),mt=zt(pt),gt=zt(ht),wt=zt(yt),Ot=zt(dt),jt=zt(bt),St=nt?nt.prototype:void 0,Pt=St?St.valueOf:void 0;function _t(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function Nt(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function Et(t){var e=-1,r=t?t.length:0;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function kt(t){this.__data__=new Nt(t)}function xt(t,e){var r=Ht(t)||function(t){return function(t){return function(t){return!!t&&"object"==typeof t}(t)&&Kt(t)}(t)&&J.call(t,"callee")&&(!ut.call(t,"callee")||tt.call(t)==i)}(t)?function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}(t.length,String):[],n=r.length,o=!!n;for(var a in t)!e&&!J.call(t,a)||o&&("length"==a||Mt(a,n))||r.push(a);return r}function Tt(t,e,r){var n=t[e];J.call(t,e)&&Ft(n,r)&&(void 0!==r||e in t)||(t[e]=r)}function At(t,e){for(var r=t.length;r--;)if(Ft(t[r][0],e))return r;return-1}function Rt(t,e,r,n,o,h,m){var A;if(n&&(A=h?n(t,o,h,m):n(t)),void 0!==A)return A;if(!Gt(t))return t;var R=Ht(t);if(R){if(A=function(t){var e=t.length,r=t.constructor(e);return e&&"string"==typeof t[0]&&J.call(t,"index")&&(r.index=t.index,r.input=t.input),r}(t),!e)return function(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}(t,A)}else{var Z=Dt(t),L=Z==l||Z==c;if(Wt(t))return function(t,e){if(e)return t.slice();var r=new t.constructor(t.length);return t.copy(r),r}(t,e);if(Z==p||Z==i||L&&!h){if(z(t))return h?t:{};if(A=function(t){return"function"!=typeof t.constructor||Ut(t)?{}:Gt(e=it(t))?at(e):{};var e}(L?{}:t),!e)return function(t,e){return Lt(t,It(t),e)}(t,function(t,e){return t&&Lt(e,Vt(e),t)}(A,t))}else{if(!C[Z])return h?t:{};A=function(t,e,r,n){var o,i=t.constructor;switch(e){case g:return Zt(t);case a:case u:return new i(+t);case w:return function(t,e){var r=e?Zt(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}(t,n);case O:case j:case S:case P:case _:case N:case E:case k:case x:return function(t,e){var r=e?Zt(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.length)}(t,n);case s:return function(t,e,r){var n=e?r(F(t),!0):F(t);return U(n,D,new t.constructor)}(t,n,r);case f:case b:return new i(t);case y:return function(t){var e=new t.constructor(t.source,T.exec(t));return e.lastIndex=t.lastIndex,e}(t);case d:return function(t,e,r){var n=e?r(K(t),!0):K(t);return U(n,M,new t.constructor)}(t,n,r);case v:return o=t,Pt?Object(Pt.call(o)):{}}}(t,Z,Rt,e)}}m||(m=new kt);var B=m.get(t);if(B)return B;if(m.set(t,A),!R)var q=r?function(t){return function(t,e,r){var n=e(t);return Ht(t)?n:function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}(n,r(t))}(t,Vt,It)}(t):Vt(t);return function(t,e){for(var r=-1,n=t?t.length:0;++r<n&&!1!==e(t[r],r,t););}(q||t,(function(o,i){q&&(o=t[i=o]),Tt(A,i,Rt(o,e,r,n,i,t,m))})),A}function Ct(t){return!(!Gt(t)||(e=t,X&&X in e))&&($t(t)||z(t)?et:A).test(zt(t));var e}function Zt(t){var e=new t.constructor(t.byteLength);return new ot(e).set(new ot(t)),e}function Lt(t,e,r,n){r||(r={});for(var o=-1,i=e.length;++o<i;){var a=e[o],u=n?n(r[a],t[a],a,r,t):void 0;Tt(r,a,void 0===u?t[a]:u)}return r}function Bt(t,e){var r,n,o=t.__data__;return("string"==(n=typeof(r=e))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof e?"string":"hash"]:o.map}function qt(t,e){var r=function(t,e){return null==t?void 0:t[e]}(t,e);return Ct(r)?r:void 0}_t.prototype.clear=function(){this.__data__=vt?vt(null):{}},_t.prototype.delete=function(t){return this.has(t)&&delete this.__data__[t]},_t.prototype.get=function(t){var e=this.__data__;if(vt){var r=e[t];return r===n?void 0:r}return J.call(e,t)?e[t]:void 0},_t.prototype.has=function(t){var e=this.__data__;return vt?void 0!==e[t]:J.call(e,t)},_t.prototype.set=function(t,e){return this.__data__[t]=vt&&void 0===e?n:e,this},Nt.prototype.clear=function(){this.__data__=[]},Nt.prototype.delete=function(t){var e=this.__data__,r=At(e,t);return!(r<0||(r==e.length-1?e.pop():lt.call(e,r,1),0))},Nt.prototype.get=function(t){var e=this.__data__,r=At(e,t);return r<0?void 0:e[r][1]},Nt.prototype.has=function(t){return At(this.__data__,t)>-1},Nt.prototype.set=function(t,e){var r=this.__data__,n=At(r,t);return n<0?r.push([t,e]):r[n][1]=e,this},Et.prototype.clear=function(){this.__data__={hash:new _t,map:new(ht||Nt),string:new _t}},Et.prototype.delete=function(t){return Bt(this,t).delete(t)},Et.prototype.get=function(t){return Bt(this,t).get(t)},Et.prototype.has=function(t){return Bt(this,t).has(t)},Et.prototype.set=function(t,e){return Bt(this,t).set(t,e),this},kt.prototype.clear=function(){this.__data__=new Nt},kt.prototype.delete=function(t){return this.__data__.delete(t)},kt.prototype.get=function(t){return this.__data__.get(t)},kt.prototype.has=function(t){return this.__data__.has(t)},kt.prototype.set=function(t,e){var r=this.__data__;if(r instanceof Nt){var n=r.__data__;if(!ht||n.length<199)return n.push([t,e]),this;r=this.__data__=new Et(n)}return r.set(t,e),this};var It=ct?H(ct,Object):function(){return[]},Dt=function(t){return tt.call(t)};function Mt(t,e){return!!(e=null==e?o:e)&&("number"==typeof t||R.test(t))&&t>-1&&t%1==0&&t<e}function Ut(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||V)}function zt(t){if(null!=t){try{return Q.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function Ft(t,e){return t===e||t!=t&&e!=e}(pt&&Dt(new pt(new ArrayBuffer(1)))!=w||ht&&Dt(new ht)!=s||yt&&Dt(yt.resolve())!=h||dt&&Dt(new dt)!=d||bt&&Dt(new bt)!=m)&&(Dt=function(t){var e=tt.call(t),r=e==p?t.constructor:void 0,n=r?zt(r):void 0;if(n)switch(n){case mt:return w;case gt:return s;case wt:return h;case Ot:return d;case jt:return m}return e});var Ht=Array.isArray;function Kt(t){return null!=t&&function(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=o}(t.length)&&!$t(t)}var Wt=st||function(){return!1};function $t(t){var e=Gt(t)?tt.call(t):"";return e==l||e==c}function Gt(t){var e=typeof t;return!!t&&("object"==e||"function"==e)}function Vt(t){return Kt(t)?xt(t):function(t){if(!Ut(t))return ft(t);var e=[];for(var r in Object(t))J.call(t,r)&&"constructor"!=r&&e.push(r);return e}(t)}t.exports=function(t){return Rt(t,!0,!0)}},2722:(t,e,r)=>{t=r.nmd(t);var n="__lodash_hash_undefined__",o=1,i=2,a=9007199254740991,u="[object Arguments]",l="[object Array]",c="[object AsyncFunction]",s="[object Boolean]",f="[object Date]",p="[object Error]",h="[object Function]",y="[object GeneratorFunction]",d="[object Map]",b="[object Number]",v="[object Null]",m="[object Object]",g="[object Promise]",w="[object Proxy]",O="[object RegExp]",j="[object Set]",S="[object String]",P="[object Symbol]",_="[object Undefined]",N="[object WeakMap]",E="[object ArrayBuffer]",k="[object DataView]",x=/^\[object .+?Constructor\]$/,T=/^(?:0|[1-9]\d*)$/,A={};A["[object Float32Array]"]=A["[object Float64Array]"]=A["[object Int8Array]"]=A["[object Int16Array]"]=A["[object Int32Array]"]=A["[object Uint8Array]"]=A["[object Uint8ClampedArray]"]=A["[object Uint16Array]"]=A["[object Uint32Array]"]=!0,A[u]=A[l]=A[E]=A[s]=A[k]=A[f]=A[p]=A[h]=A[d]=A[b]=A[m]=A[O]=A[j]=A[S]=A[N]=!1;var R="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,C="object"==typeof self&&self&&self.Object===Object&&self,Z=R||C||Function("return this")(),L=e&&!e.nodeType&&e,B=L&&t&&!t.nodeType&&t,q=B&&B.exports===L,I=q&&R.process,D=function(){try{return I&&I.binding&&I.binding("util")}catch(t){}}(),M=D&&D.isTypedArray;function U(t,e){for(var r=-1,n=null==t?0:t.length;++r<n;)if(e(t[r],r,t))return!0;return!1}function z(t){var e=-1,r=Array(t.size);return t.forEach((function(t,n){r[++e]=[n,t]})),r}function F(t){var e=-1,r=Array(t.size);return t.forEach((function(t){r[++e]=t})),r}var H,K,W,$=Array.prototype,G=Object.prototype,V=Z["__core-js_shared__"],Y=Function.prototype.toString,X=G.hasOwnProperty,Q=(H=/[^.]+$/.exec(V&&V.keys&&V.keys.IE_PROTO||""))?"Symbol(src)_1."+H:"",J=G.toString,tt=RegExp("^"+Y.call(X).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),et=q?Z.Buffer:void 0,rt=Z.Symbol,nt=Z.Uint8Array,ot=G.propertyIsEnumerable,it=$.splice,at=rt?rt.toStringTag:void 0,ut=Object.getOwnPropertySymbols,lt=et?et.isBuffer:void 0,ct=(K=Object.keys,W=Object,function(t){return K(W(t))}),st=It(Z,"DataView"),ft=It(Z,"Map"),pt=It(Z,"Promise"),ht=It(Z,"Set"),yt=It(Z,"WeakMap"),dt=It(Object,"create"),bt=zt(st),vt=zt(ft),mt=zt(pt),gt=zt(ht),wt=zt(yt),Ot=rt?rt.prototype:void 0,jt=Ot?Ot.valueOf:void 0;function St(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function Pt(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function _t(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function Nt(t){var e=-1,r=null==t?0:t.length;for(this.__data__=new _t;++e<r;)this.add(t[e])}function Et(t){var e=this.__data__=new Pt(t);this.size=e.size}function kt(t,e){var r=Kt(t),n=!r&&Ht(t),o=!r&&!n&&Wt(t),i=!r&&!n&&!o&&Xt(t),a=r||n||o||i,u=a?function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}(t.length,String):[],l=u.length;for(var c in t)!e&&!X.call(t,c)||a&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||Ut(c,l))||u.push(c);return u}function xt(t,e){for(var r=t.length;r--;)if(Ft(t[r][0],e))return r;return-1}function Tt(t){return null==t?void 0===t?_:v:at&&at in Object(t)?function(t){var e=X.call(t,at),r=t[at];try{t[at]=void 0;var n=!0}catch(t){}var o=J.call(t);return n&&(e?t[at]=r:delete t[at]),o}(t):function(t){return J.call(t)}(t)}function At(t){return Yt(t)&&Tt(t)==u}function Rt(t,e,r,n,a){return t===e||(null==t||null==e||!Yt(t)&&!Yt(e)?t!=t&&e!=e:function(t,e,r,n,a,c){var h=Kt(t),y=Kt(e),v=h?l:Mt(t),g=y?l:Mt(e),w=(v=v==u?m:v)==m,_=(g=g==u?m:g)==m,N=v==g;if(N&&Wt(t)){if(!Wt(e))return!1;h=!0,w=!1}if(N&&!w)return c||(c=new Et),h||Xt(t)?Lt(t,e,r,n,a,c):function(t,e,r,n,a,u,l){switch(r){case k:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case E:return!(t.byteLength!=e.byteLength||!u(new nt(t),new nt(e)));case s:case f:case b:return Ft(+t,+e);case p:return t.name==e.name&&t.message==e.message;case O:case S:return t==e+"";case d:var c=z;case j:if(c||(c=F),t.size!=e.size&&!(n&o))return!1;var h=l.get(t);if(h)return h==e;n|=i,l.set(t,e);var y=Lt(c(t),c(e),n,a,u,l);return l.delete(t),y;case P:if(jt)return jt.call(t)==jt.call(e)}return!1}(t,e,v,r,n,a,c);if(!(r&o)){var x=w&&X.call(t,"__wrapped__"),T=_&&X.call(e,"__wrapped__");if(x||T){var A=x?t.value():t,R=T?e.value():e;return c||(c=new Et),a(A,R,r,n,c)}}return!!N&&(c||(c=new Et),function(t,e,r,n,i,a){var u=r&o,l=Bt(t),c=l.length,s=Bt(e);if(c!=s.length&&!u)return!1;for(var f=c;f--;){var p=l[f];if(!(u?p in e:X.call(e,p)))return!1}var h=a.get(t);if(h&&a.get(e))return h==e;var y=!0;a.set(t,e),a.set(e,t);for(var d=u;++f<c;){var b=t[p=l[f]],v=e[p];if(n)var m=u?n(v,b,p,e,t,a):n(b,v,p,t,e,a);if(!(void 0===m?b===v||i(b,v,r,n,a):m)){y=!1;break}d||(d="constructor"==p)}if(y&&!d){var g=t.constructor,w=e.constructor;g==w||!("constructor"in t)||!("constructor"in e)||"function"==typeof g&&g instanceof g&&"function"==typeof w&&w instanceof w||(y=!1)}return a.delete(t),a.delete(e),y}(t,e,r,n,a,c))}(t,e,r,n,Rt,a))}function Ct(t){return!(!Vt(t)||function(t){return!!Q&&Q in t}(t))&&($t(t)?tt:x).test(zt(t))}function Zt(t){if((e=t)!==("function"==typeof(r=e&&e.constructor)&&r.prototype||G))return ct(t);var e,r,n=[];for(var o in Object(t))X.call(t,o)&&"constructor"!=o&&n.push(o);return n}function Lt(t,e,r,n,a,u){var l=r&o,c=t.length,s=e.length;if(c!=s&&!(l&&s>c))return!1;var f=u.get(t);if(f&&u.get(e))return f==e;var p=-1,h=!0,y=r&i?new Nt:void 0;for(u.set(t,e),u.set(e,t);++p<c;){var d=t[p],b=e[p];if(n)var v=l?n(b,d,p,e,t,u):n(d,b,p,t,e,u);if(void 0!==v){if(v)continue;h=!1;break}if(y){if(!U(e,(function(t,e){if(!y.has(e)&&(d===t||a(d,t,r,n,u)))return y.push(e)}))){h=!1;break}}else if(d!==b&&!a(d,b,r,n,u)){h=!1;break}}return u.delete(t),u.delete(e),h}function Bt(t){return function(t,e,r){var n=e(t);return Kt(t)?n:function(t,e){for(var r=-1,n=e.length,o=t.length;++r<n;)t[o+r]=e[r];return t}(n,r(t))}(t,Qt,Dt)}function qt(t,e){var r,n,o=t.__data__;return("string"==(n=typeof(r=e))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof e?"string":"hash"]:o.map}function It(t,e){var r=function(t,e){return null==t?void 0:t[e]}(t,e);return Ct(r)?r:void 0}St.prototype.clear=function(){this.__data__=dt?dt(null):{},this.size=0},St.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},St.prototype.get=function(t){var e=this.__data__;if(dt){var r=e[t];return r===n?void 0:r}return X.call(e,t)?e[t]:void 0},St.prototype.has=function(t){var e=this.__data__;return dt?void 0!==e[t]:X.call(e,t)},St.prototype.set=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=dt&&void 0===e?n:e,this},Pt.prototype.clear=function(){this.__data__=[],this.size=0},Pt.prototype.delete=function(t){var e=this.__data__,r=xt(e,t);return!(r<0||(r==e.length-1?e.pop():it.call(e,r,1),--this.size,0))},Pt.prototype.get=function(t){var e=this.__data__,r=xt(e,t);return r<0?void 0:e[r][1]},Pt.prototype.has=function(t){return xt(this.__data__,t)>-1},Pt.prototype.set=function(t,e){var r=this.__data__,n=xt(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this},_t.prototype.clear=function(){this.size=0,this.__data__={hash:new St,map:new(ft||Pt),string:new St}},_t.prototype.delete=function(t){var e=qt(this,t).delete(t);return this.size-=e?1:0,e},_t.prototype.get=function(t){return qt(this,t).get(t)},_t.prototype.has=function(t){return qt(this,t).has(t)},_t.prototype.set=function(t,e){var r=qt(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this},Nt.prototype.add=Nt.prototype.push=function(t){return this.__data__.set(t,n),this},Nt.prototype.has=function(t){return this.__data__.has(t)},Et.prototype.clear=function(){this.__data__=new Pt,this.size=0},Et.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},Et.prototype.get=function(t){return this.__data__.get(t)},Et.prototype.has=function(t){return this.__data__.has(t)},Et.prototype.set=function(t,e){var r=this.__data__;if(r instanceof Pt){var n=r.__data__;if(!ft||n.length<199)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new _t(n)}return r.set(t,e),this.size=r.size,this};var Dt=ut?function(t){return null==t?[]:(t=Object(t),function(t,e){for(var r=-1,n=null==t?0:t.length,o=0,i=[];++r<n;){var a=t[r];e(a,r,t)&&(i[o++]=a)}return i}(ut(t),(function(e){return ot.call(t,e)})))}:function(){return[]},Mt=Tt;function Ut(t,e){return!!(e=null==e?a:e)&&("number"==typeof t||T.test(t))&&t>-1&&t%1==0&&t<e}function zt(t){if(null!=t){try{return Y.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function Ft(t,e){return t===e||t!=t&&e!=e}(st&&Mt(new st(new ArrayBuffer(1)))!=k||ft&&Mt(new ft)!=d||pt&&Mt(pt.resolve())!=g||ht&&Mt(new ht)!=j||yt&&Mt(new yt)!=N)&&(Mt=function(t){var e=Tt(t),r=e==m?t.constructor:void 0,n=r?zt(r):"";if(n)switch(n){case bt:return k;case vt:return d;case mt:return g;case gt:return j;case wt:return N}return e});var Ht=At(function(){return arguments}())?At:function(t){return Yt(t)&&X.call(t,"callee")&&!ot.call(t,"callee")},Kt=Array.isArray,Wt=lt||function(){return!1};function $t(t){if(!Vt(t))return!1;var e=Tt(t);return e==h||e==y||e==c||e==w}function Gt(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=a}function Vt(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function Yt(t){return null!=t&&"object"==typeof t}var Xt=M?function(t){return function(e){return t(e)}}(M):function(t){return Yt(t)&&Gt(t.length)&&!!A[Tt(t)]};function Qt(t){return null!=(e=t)&&Gt(e.length)&&!$t(e)?kt(t):Zt(t);var e}t.exports=function(t,e){return Rt(t,e)}},732:(t,e,r)=>{t=r.nmd(t);var n="__lodash_hash_undefined__",o=9007199254740991,i="[object Arguments]",a="[object AsyncFunction]",u="[object Function]",l="[object GeneratorFunction]",c="[object Null]",s="[object Object]",f="[object Proxy]",p="[object Undefined]",h=/^\[object .+?Constructor\]$/,y=/^(?:0|[1-9]\d*)$/,d={};d["[object Float32Array]"]=d["[object Float64Array]"]=d["[object Int8Array]"]=d["[object Int16Array]"]=d["[object Int32Array]"]=d["[object Uint8Array]"]=d["[object Uint8ClampedArray]"]=d["[object Uint16Array]"]=d["[object Uint32Array]"]=!0,d[i]=d["[object Array]"]=d["[object ArrayBuffer]"]=d["[object Boolean]"]=d["[object DataView]"]=d["[object Date]"]=d["[object Error]"]=d[u]=d["[object Map]"]=d["[object Number]"]=d[s]=d["[object RegExp]"]=d["[object Set]"]=d["[object String]"]=d["[object WeakMap]"]=!1;var b,v,m,g="object"==typeof r.g&&r.g&&r.g.Object===Object&&r.g,w="object"==typeof self&&self&&self.Object===Object&&self,O=g||w||Function("return this")(),j=e&&!e.nodeType&&e,S=j&&t&&!t.nodeType&&t,P=S&&S.exports===j,_=P&&g.process,N=function(){try{var t=S&&S.require&&S.require("util").types;return t||_&&_.binding&&_.binding("util")}catch(t){}}(),E=N&&N.isTypedArray,k=Array.prototype,x=Object.prototype,T=O["__core-js_shared__"],A=Function.prototype.toString,R=x.hasOwnProperty,C=(b=/[^.]+$/.exec(T&&T.keys&&T.keys.IE_PROTO||""))?"Symbol(src)_1."+b:"",Z=x.toString,L=A.call(Object),B=RegExp("^"+A.call(R).replace(/[\\^$.*+?()[\]{}|]/g,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),q=P?O.Buffer:void 0,I=O.Symbol,D=O.Uint8Array,M=q?q.allocUnsafe:void 0,U=(v=Object.getPrototypeOf,m=Object,function(t){return v(m(t))}),z=Object.create,F=x.propertyIsEnumerable,H=k.splice,K=I?I.toStringTag:void 0,W=function(){try{var t=mt(Object,"defineProperty");return t({},"",{}),t}catch(t){}}(),$=q?q.isBuffer:void 0,G=Math.max,V=Date.now,Y=mt(O,"Map"),X=mt(Object,"create"),Q=function(){function t(){}return function(e){if(!Tt(e))return{};if(z)return z(e);t.prototype=e;var r=new t;return t.prototype=void 0,r}}();function J(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function tt(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function et(t){var e=-1,r=null==t?0:t.length;for(this.clear();++e<r;){var n=t[e];this.set(n[0],n[1])}}function rt(t){var e=this.__data__=new tt(t);this.size=e.size}function nt(t,e){var r=_t(t),n=!r&&Pt(t),o=!r&&!n&&Et(t),i=!r&&!n&&!o&&Rt(t),a=r||n||o||i,u=a?function(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}(t.length,String):[],l=u.length;for(var c in t)!e&&!R.call(t,c)||a&&("length"==c||o&&("offset"==c||"parent"==c)||i&&("buffer"==c||"byteLength"==c||"byteOffset"==c)||gt(c,l))||u.push(c);return u}function ot(t,e,r){(void 0!==r&&!St(t[e],r)||void 0===r&&!(e in t))&&ut(t,e,r)}function it(t,e,r){var n=t[e];R.call(t,e)&&St(n,r)&&(void 0!==r||e in t)||ut(t,e,r)}function at(t,e){for(var r=t.length;r--;)if(St(t[r][0],e))return r;return-1}function ut(t,e,r){"__proto__"==e&&W?W(t,e,{configurable:!0,enumerable:!0,value:r,writable:!0}):t[e]=r}J.prototype.clear=function(){this.__data__=X?X(null):{},this.size=0},J.prototype.delete=function(t){var e=this.has(t)&&delete this.__data__[t];return this.size-=e?1:0,e},J.prototype.get=function(t){var e=this.__data__;if(X){var r=e[t];return r===n?void 0:r}return R.call(e,t)?e[t]:void 0},J.prototype.has=function(t){var e=this.__data__;return X?void 0!==e[t]:R.call(e,t)},J.prototype.set=function(t,e){var r=this.__data__;return this.size+=this.has(t)?0:1,r[t]=X&&void 0===e?n:e,this},tt.prototype.clear=function(){this.__data__=[],this.size=0},tt.prototype.delete=function(t){var e=this.__data__,r=at(e,t);return!(r<0||(r==e.length-1?e.pop():H.call(e,r,1),--this.size,0))},tt.prototype.get=function(t){var e=this.__data__,r=at(e,t);return r<0?void 0:e[r][1]},tt.prototype.has=function(t){return at(this.__data__,t)>-1},tt.prototype.set=function(t,e){var r=this.__data__,n=at(r,t);return n<0?(++this.size,r.push([t,e])):r[n][1]=e,this},et.prototype.clear=function(){this.size=0,this.__data__={hash:new J,map:new(Y||tt),string:new J}},et.prototype.delete=function(t){var e=vt(this,t).delete(t);return this.size-=e?1:0,e},et.prototype.get=function(t){return vt(this,t).get(t)},et.prototype.has=function(t){return vt(this,t).has(t)},et.prototype.set=function(t,e){var r=vt(this,t),n=r.size;return r.set(t,e),this.size+=r.size==n?0:1,this},rt.prototype.clear=function(){this.__data__=new tt,this.size=0},rt.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},rt.prototype.get=function(t){return this.__data__.get(t)},rt.prototype.has=function(t){return this.__data__.has(t)},rt.prototype.set=function(t,e){var r=this.__data__;if(r instanceof tt){var n=r.__data__;if(!Y||n.length<199)return n.push([t,e]),this.size=++r.size,this;r=this.__data__=new et(n)}return r.set(t,e),this.size=r.size,this};var lt,ct=function(t,e,r){for(var n=-1,o=Object(t),i=r(t),a=i.length;a--;){var u=i[lt?a:++n];if(!1===e(o[u],u,o))break}return t};function st(t){return null==t?void 0===t?p:c:K&&K in Object(t)?function(t){var e=R.call(t,K),r=t[K];try{t[K]=void 0;var n=!0}catch(t){}var o=Z.call(t);return n&&(e?t[K]=r:delete t[K]),o}(t):function(t){return Z.call(t)}(t)}function ft(t){return At(t)&&st(t)==i}function pt(t){return!(!Tt(t)||function(t){return!!C&&C in t}(t))&&(kt(t)?B:h).test(function(t){if(null!=t){try{return A.call(t)}catch(t){}try{return t+""}catch(t){}}return""}(t))}function ht(t){if(!Tt(t))return function(t){var e=[];if(null!=t)for(var r in Object(t))e.push(r);return e}(t);var e=wt(t),r=[];for(var n in t)("constructor"!=n||!e&&R.call(t,n))&&r.push(n);return r}function yt(t,e,r,n,o){t!==e&&ct(e,(function(i,a){if(o||(o=new rt),Tt(i))!function(t,e,r,n,o,i,a){var u=Ot(t,r),l=Ot(e,r),c=a.get(l);if(c)ot(t,r,c);else{var f,p,h,y,d=i?i(u,l,r+"",t,e,a):void 0,b=void 0===d;if(b){var v=_t(l),m=!v&&Et(l),g=!v&&!m&&Rt(l);d=l,v||m||g?_t(u)?d=u:At(y=u)&&Nt(y)?d=function(t,e){var r=-1,n=t.length;for(e||(e=Array(n));++r<n;)e[r]=t[r];return e}(u):m?(b=!1,d=function(t,e){if(e)return t.slice();var r=t.length,n=M?M(r):new t.constructor(r);return t.copy(n),n}(l,!0)):g?(b=!1,h=new(p=(f=l).buffer).constructor(p.byteLength),new D(h).set(new D(p)),d=new f.constructor(h,f.byteOffset,f.length)):d=[]:function(t){if(!At(t)||st(t)!=s)return!1;var e=U(t);if(null===e)return!0;var r=R.call(e,"constructor")&&e.constructor;return"function"==typeof r&&r instanceof r&&A.call(r)==L}(l)||Pt(l)?(d=u,Pt(u)?d=function(t){return function(t,e,r,n){var o=!r;r||(r={});for(var i=-1,a=e.length;++i<a;){var u=e[i],l=n?n(r[u],t[u],u,r,t):void 0;void 0===l&&(l=t[u]),o?ut(r,u,l):it(r,u,l)}return r}(t,Ct(t))}(u):Tt(u)&&!kt(u)||(d=function(t){return"function"!=typeof t.constructor||wt(t)?{}:Q(U(t))}(l))):b=!1}b&&(a.set(l,d),o(d,l,n,i,a),a.delete(l)),ot(t,r,d)}}(t,e,a,r,yt,n,o);else{var u=n?n(Ot(t,a),i,a+"",t,e,o):void 0;void 0===u&&(u=i),ot(t,a,u)}}),Ct)}function dt(t,e){return jt(function(t,e,r){return e=G(void 0===e?t.length-1:e,0),function(){for(var n=arguments,o=-1,i=G(n.length-e,0),a=Array(i);++o<i;)a[o]=n[e+o];o=-1;for(var u=Array(e+1);++o<e;)u[o]=n[o];return u[e]=r(a),function(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}(t,this,u)}}(t,e,Bt),t+"")}var bt=W?function(t,e){return W(t,"toString",{configurable:!0,enumerable:!1,value:(r=e,function(){return r}),writable:!0});var r}:Bt;function vt(t,e){var r,n,o=t.__data__;return("string"==(n=typeof(r=e))||"number"==n||"symbol"==n||"boolean"==n?"__proto__"!==r:null===r)?o["string"==typeof e?"string":"hash"]:o.map}function mt(t,e){var r=function(t,e){return null==t?void 0:t[e]}(t,e);return pt(r)?r:void 0}function gt(t,e){var r=typeof t;return!!(e=null==e?o:e)&&("number"==r||"symbol"!=r&&y.test(t))&&t>-1&&t%1==0&&t<e}function wt(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||x)}function Ot(t,e){if(("constructor"!==e||"function"!=typeof t[e])&&"__proto__"!=e)return t[e]}var jt=function(t){var e=0,r=0;return function(){var n=V(),o=16-(n-r);if(r=n,o>0){if(++e>=800)return arguments[0]}else e=0;return t.apply(void 0,arguments)}}(bt);function St(t,e){return t===e||t!=t&&e!=e}var Pt=ft(function(){return arguments}())?ft:function(t){return At(t)&&R.call(t,"callee")&&!F.call(t,"callee")},_t=Array.isArray;function Nt(t){return null!=t&&xt(t.length)&&!kt(t)}var Et=$||function(){return!1};function kt(t){if(!Tt(t))return!1;var e=st(t);return e==u||e==l||e==a||e==f}function xt(t){return"number"==typeof t&&t>-1&&t%1==0&&t<=o}function Tt(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function At(t){return null!=t&&"object"==typeof t}var Rt=E?function(t){return function(e){return t(e)}}(E):function(t){return At(t)&&xt(t.length)&&!!d[st(t)]};function Ct(t){return Nt(t)?nt(t,!0):ht(t)}var Zt,Lt=(Zt=function(t,e,r){yt(t,e,r)},dt((function(t,e){var r=-1,n=e.length,o=n>1?e[n-1]:void 0,i=n>2?e[2]:void 0;for(o=Zt.length>3&&"function"==typeof o?(n--,o):void 0,i&&function(t,e,r){if(!Tt(r))return!1;var n=typeof e;return!!("number"==n?Nt(r)&&gt(e,r.length):"string"==n&&e in r)&&St(r[e],t)}(e[0],e[1],i)&&(o=n<3?void 0:o,n=1),t=Object(t);++r<n;){var a=e[r];a&&Zt(t,a,r,o)}return t})));function Bt(t){return t}t.exports=Lt},5143:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0});const n=r(8805),o=r(2722);var i;!function(t){t.compose=function(t={},e={},r=!1){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});let o=n(e);r||(o=Object.keys(o).reduce(((t,e)=>(null!=o[e]&&(t[e]=o[e]),t)),{}));for(const r in t)void 0!==t[r]&&void 0===e[r]&&(o[r]=t[r]);return Object.keys(o).length>0?o:void 0},t.diff=function(t={},e={}){"object"!=typeof t&&(t={}),"object"!=typeof e&&(e={});const r=Object.keys(t).concat(Object.keys(e)).reduce(((r,n)=>(o(t[n],e[n])||(r[n]=void 0===e[n]?null:e[n]),r)),{});return Object.keys(r).length>0?r:void 0},t.invert=function(t={},e={}){t=t||{};const r=Object.keys(e).reduce(((r,n)=>(e[n]!==t[n]&&void 0!==t[n]&&(r[n]=e[n]),r)),{});return Object.keys(t).reduce(((r,n)=>(t[n]!==e[n]&&void 0===e[n]&&(r[n]=null),r)),r)},t.transform=function(t,e,r=!1){if("object"!=typeof t)return e;if("object"!=typeof e)return;if(!r)return e;const n=Object.keys(e).reduce(((r,n)=>(void 0===t[n]&&(r[n]=e[n]),r)),{});return Object.keys(n).length>0?n:void 0}}(i||(i={})),e.default=i},9098:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AttributeMap=e.OpIterator=e.Op=void 0;const n=r(1456),o=r(8805),i=r(2722),a=r(5143);e.AttributeMap=a.default;const u=r(6237);e.Op=u.default;const l=r(2849);e.OpIterator=l.default;const c=String.fromCharCode(0),s=(t,e)=>{if("object"!=typeof t||null===t)throw new Error("cannot retain a "+typeof t);if("object"!=typeof e||null===e)throw new Error("cannot retain a "+typeof e);const r=Object.keys(t)[0];if(!r||r!==Object.keys(e)[0])throw new Error(`embed types not matched: ${r} != ${Object.keys(e)[0]}`);return[r,t[r],e[r]]};class f{constructor(t){this.ops=Array.isArray(t)?t:null!=t&&Array.isArray(t.ops)?t.ops:[]}static registerEmbed(t,e){this.handlers[t]=e}static unregisterEmbed(t){delete this.handlers[t]}static getHandler(t){const e=this.handlers[t];if(!e)throw new Error(`no handlers for embed type "${t}"`);return e}insert(t,e){const r={};return"string"==typeof t&&0===t.length?this:(r.insert=t,null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(r.attributes=e),this.push(r))}delete(t){return t<=0?this:this.push({delete:t})}retain(t,e){if("number"==typeof t&&t<=0)return this;const r={retain:t};return null!=e&&"object"==typeof e&&Object.keys(e).length>0&&(r.attributes=e),this.push(r)}push(t){let e=this.ops.length,r=this.ops[e-1];if(t=o(t),"object"==typeof r){if("number"==typeof t.delete&&"number"==typeof r.delete)return this.ops[e-1]={delete:r.delete+t.delete},this;if("number"==typeof r.delete&&null!=t.insert&&(e-=1,r=this.ops[e-1],"object"!=typeof r))return this.ops.unshift(t),this;if(i(t.attributes,r.attributes)){if("string"==typeof t.insert&&"string"==typeof r.insert)return this.ops[e-1]={insert:r.insert+t.insert},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this;if("number"==typeof t.retain&&"number"==typeof r.retain)return this.ops[e-1]={retain:r.retain+t.retain},"object"==typeof t.attributes&&(this.ops[e-1].attributes=t.attributes),this}}return e===this.ops.length?this.ops.push(t):this.ops.splice(e,0,t),this}chop(){const t=this.ops[this.ops.length-1];return t&&"number"==typeof t.retain&&!t.attributes&&this.ops.pop(),this}filter(t){return this.ops.filter(t)}forEach(t){this.ops.forEach(t)}map(t){return this.ops.map(t)}partition(t){const e=[],r=[];return this.forEach((n=>{(t(n)?e:r).push(n)})),[e,r]}reduce(t,e){return this.ops.reduce(t,e)}changeLength(){return this.reduce(((t,e)=>e.insert?t+u.default.length(e):e.delete?t-e.delete:t),0)}length(){return this.reduce(((t,e)=>t+u.default.length(e)),0)}slice(t=0,e=1/0){const r=[],n=new l.default(this.ops);let o=0;for(;o<e&&n.hasNext();){let i;o<t?i=n.next(t-o):(i=n.next(e-o),r.push(i)),o+=u.default.length(i)}return new f(r)}compose(t){const e=new l.default(this.ops),r=new l.default(t.ops),n=[],o=r.peek();if(null!=o&&"number"==typeof o.retain&&null==o.attributes){let t=o.retain;for(;"insert"===e.peekType()&&e.peekLength()<=t;)t-=e.peekLength(),n.push(e.next());o.retain-t>0&&r.next(o.retain-t)}const u=new f(n);for(;e.hasNext()||r.hasNext();)if("insert"===r.peekType())u.push(r.next());else if("delete"===e.peekType())u.push(e.next());else{const t=Math.min(e.peekLength(),r.peekLength()),n=e.next(t),o=r.next(t);if(o.retain){const l={};if("number"==typeof n.retain)l.retain="number"==typeof o.retain?t:o.retain;else if("number"==typeof o.retain)null==n.retain?l.insert=n.insert:l.retain=n.retain;else{const t=null==n.retain?"insert":"retain",[e,r,i]=s(n[t],o.retain),a=f.getHandler(e);l[t]={[e]:a.compose(r,i,"retain"===t)}}const c=a.default.compose(n.attributes,o.attributes,"number"==typeof n.retain);if(c&&(l.attributes=c),u.push(l),!r.hasNext()&&i(u.ops[u.ops.length-1],l)){const t=new f(e.rest());return u.concat(t).chop()}}else"number"==typeof o.delete&&("number"==typeof n.retain||"object"==typeof n.retain&&null!==n.retain)&&u.push(o)}return u.chop()}concat(t){const e=new f(this.ops.slice());return t.ops.length>0&&(e.push(t.ops[0]),e.ops=e.ops.concat(t.ops.slice(1))),e}diff(t,e){if(this.ops===t.ops)return new f;const r=[this,t].map((e=>e.map((r=>{if(null!=r.insert)return"string"==typeof r.insert?r.insert:c;throw new Error("diff() called "+(e===t?"on":"with")+" non-document")})).join(""))),o=new f,u=n(r[0],r[1],e),s=new l.default(this.ops),p=new l.default(t.ops);return u.forEach((t=>{let e=t[1].length;for(;e>0;){let r=0;switch(t[0]){case n.INSERT:r=Math.min(p.peekLength(),e),o.push(p.next(r));break;case n.DELETE:r=Math.min(e,s.peekLength()),s.next(r),o.delete(r);break;case n.EQUAL:r=Math.min(s.peekLength(),p.peekLength(),e);const t=s.next(r),u=p.next(r);i(t.insert,u.insert)?o.retain(r,a.default.diff(t.attributes,u.attributes)):o.push(u).delete(r)}e-=r}})),o.chop()}eachLine(t,e="\n"){const r=new l.default(this.ops);let n=new f,o=0;for(;r.hasNext();){if("insert"!==r.peekType())return;const i=r.peek(),a=u.default.length(i)-r.peekLength(),l="string"==typeof i.insert?i.insert.indexOf(e,a)-a:-1;if(l<0)n.push(r.next());else if(l>0)n.push(r.next(l));else{if(!1===t(n,r.next(1).attributes||{},o))return;o+=1,n=new f}}n.length()>0&&t(n,{},o)}invert(t){const e=new f;return this.reduce(((r,n)=>{if(n.insert)e.delete(u.default.length(n));else{if("number"==typeof n.retain&&null==n.attributes)return e.retain(n.retain),r+n.retain;if(n.delete||"number"==typeof n.retain){const o=n.delete||n.retain;return t.slice(r,r+o).forEach((t=>{n.delete?e.push(t):n.retain&&n.attributes&&e.retain(u.default.length(t),a.default.invert(n.attributes,t.attributes))})),r+o}if("object"==typeof n.retain&&null!==n.retain){const o=t.slice(r,r+1),i=new l.default(o.ops).next(),[u,c,p]=s(n.retain,i.insert),h=f.getHandler(u);return e.retain({[u]:h.invert(c,p)},a.default.invert(n.attributes,i.attributes)),r+1}}return r}),0),e.chop()}transform(t,e=!1){if(e=!!e,"number"==typeof t)return this.transformPosition(t,e);const r=t,n=new l.default(this.ops),o=new l.default(r.ops),i=new f;for(;n.hasNext()||o.hasNext();)if("insert"!==n.peekType()||!e&&"insert"===o.peekType())if("insert"===o.peekType())i.push(o.next());else{const t=Math.min(n.peekLength(),o.peekLength()),r=n.next(t),u=o.next(t);if(r.delete)continue;if(u.delete)i.push(u);else{const n=r.retain,o=u.retain;let l="object"==typeof o&&null!==o?o:t;if("object"==typeof n&&null!==n&&"object"==typeof o&&null!==o){const t=Object.keys(n)[0];if(t===Object.keys(o)[0]){const r=f.getHandler(t);r&&(l={[t]:r.transform(n[t],o[t],e)})}}i.retain(l,a.default.transform(r.attributes,u.attributes,e))}}else i.retain(u.default.length(n.next()));return i.chop()}transformPosition(t,e=!1){e=!!e;const r=new l.default(this.ops);let n=0;for(;r.hasNext()&&n<=t;){const o=r.peekLength(),i=r.peekType();r.next(),"delete"!==i?("insert"===i&&(n<t||!e)&&(t+=o),n+=o):t-=Math.min(o,t-n)}return t}}f.Op=u.default,f.OpIterator=l.default,f.AttributeMap=a.default,f.handlers={},e.default=f,t.exports=f,t.exports.default=f},6237:(t,e)=>{var r;Object.defineProperty(e,"__esModule",{value:!0}),function(t){t.length=function(t){return"number"==typeof t.delete?t.delete:"number"==typeof t.retain?t.retain:"object"==typeof t.retain&&null!==t.retain?1:"string"==typeof t.insert?t.insert.length:1}}(r||(r={})),e.default=r},2849:(t,e,r)=>{Object.defineProperty(e,"__esModule",{value:!0});const n=r(6237);e.default=class{constructor(t){this.ops=t,this.index=0,this.offset=0}hasNext(){return this.peekLength()<1/0}next(t){t||(t=1/0);const e=this.ops[this.index];if(e){const r=this.offset,o=n.default.length(e);if(t>=o-r?(t=o-r,this.index+=1,this.offset=0):this.offset+=t,"number"==typeof e.delete)return{delete:t};{const n={};return e.attributes&&(n.attributes=e.attributes),"number"==typeof e.retain?n.retain=t:"object"==typeof e.retain&&null!==e.retain?n.retain=e.retain:n.insert="string"==typeof e.insert?e.insert.substr(r,t):e.insert,n}}return{retain:1/0}}peek(){return this.ops[this.index]}peekLength(){return this.ops[this.index]?n.default.length(this.ops[this.index])-this.offset:1/0}peekType(){const t=this.ops[this.index];return t?"number"==typeof t.delete?"delete":"number"==typeof t.retain||"object"==typeof t.retain&&null!==t.retain?"retain":"insert":"retain"}rest(){if(this.hasNext()){if(0===this.offset)return this.ops.slice(this.index);{const t=this.offset,e=this.index,r=this.next(),n=this.ops.slice(this.index);return this.offset=t,this.index=e,[r].concat(n)}}return[]}}},1863:(t,e,r)=>{var n=r(9731);t.exports=n},6572:(t,e,r)=>{var n=r(4401);t.exports=n},2318:(t,e,r)=>{var n=r(3661);t.exports=n},576:(t,e,r)=>{var n=r(5606);t.exports=n},2327:(t,e,r)=>{r(5445);var n=r(6731);t.exports=n("Array","fill")},3274:(t,e,r)=>{r(9477),r(6272);var n=r(577);t.exports=n.Array.from},5339:(t,e,r)=>{r(2947);var n=r(577);t.exports=n.Object.values},1189:(t,e,r)=>{r(538);var n=r(6731);t.exports=n("String","endsWith")},5142:(t,e,r)=>{r(297)},1019:(t,e,r)=>{r(4660)},1183:(t,e,r)=>{r(5275)},2570:(t,e,r)=>{r(2839)},297:(t,e,r)=>{var n=r(1863);t.exports=n},4660:(t,e,r)=>{var n=r(6572);t.exports=n},5275:(t,e,r)=>{var n=r(2318);t.exports=n},2839:(t,e,r)=>{var n=r(576);t.exports=n},6324:(t,e,r)=>{var n=r(2539),o=r(7471),i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not a function")}},2162:(t,e,r)=>{var n=r(2539),o=String,i=TypeError;t.exports=function(t){if("object"==typeof t||n(t))return t;throw new i("Can't set "+o(t)+" as a prototype")}},2572:(t,e,r)=>{var n=r(5485),o=r(3830),i=r(8503).f,a=n("unscopables"),u=Array.prototype;void 0===u[a]&&i(u,a,{configurable:!0,value:o(null)}),t.exports=function(t){u[a][t]=!0}},8874:(t,e,r)=>{var n=r(3988),o=String,i=TypeError;t.exports=function(t){if(n(t))return t;throw new i(o(t)+" is not an object")}},9236:(t,e,r)=>{var n=r(6015),o=r(7234),i=r(3235);t.exports=function(t){for(var e=n(this),r=i(e),a=arguments.length,u=o(a>1?arguments[1]:void 0,r),l=a>2?arguments[2]:void 0,c=void 0===l?r:o(l,r);c>u;)e[u++]=t;return e}},8459:(t,e,r)=>{var n=r(9794),o=r(2977),i=r(6015),a=r(9154),u=r(7024),l=r(6467),c=r(3235),s=r(3003),f=r(2387),p=r(7874),h=Array;t.exports=function(t){var e=i(t),r=l(this),y=arguments.length,d=y>1?arguments[1]:void 0,b=void 0!==d;b&&(d=n(d,y>2?arguments[2]:void 0));var v,m,g,w,O,j,S=p(e),P=0;if(!S||this===h&&u(S))for(v=c(e),m=r?new this(v):h(v);v>P;P++)j=b?d(e[P],P):e[P],s(m,P,j);else for(O=(w=f(e,S)).next,m=r?new this:[];!(g=o(O,w)).done;P++)j=b?a(w,d,[g.value,P],!0):g.value,s(m,P,j);return m.length=P,m}},5644:(t,e,r)=>{var n=r(7625),o=r(7234),i=r(3235),a=function(t){return function(e,r,a){var u,l=n(e),c=i(l),s=o(a,c);if(t&&r!=r){for(;c>s;)if((u=l[s++])!=u)return!0}else for(;c>s;s++)if((t||s in l)&&l[s]===r)return t||s||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},9154:(t,e,r)=>{var n=r(8874),o=r(8880);t.exports=function(t,e,r,i){try{return i?e(n(r)[0],r[1]):e(r)}catch(e){o(t,"throw",e)}}},3346:(t,e,r)=>{var n=r(5485)("iterator"),o=!1;try{var i=0,a={next:function(){return{done:!!i++}},return:function(){o=!0}};a[n]=function(){return this},Array.from(a,(function(){throw 2}))}catch(t){}t.exports=function(t,e){try{if(!e&&!o)return!1}catch(t){return!1}var r=!1;try{var i={};i[n]=function(){return{next:function(){return{done:r=!0}}}},t(i)}catch(t){}return r}},4545:(t,e,r)=>{var n=r(4038),o=n({}.toString),i=n("".slice);t.exports=function(t){return i(o(t),8,-1)}},2702:(t,e,r)=>{var n=r(1923),o=r(2539),i=r(4545),a=r(5485)("toStringTag"),u=Object,l="Arguments"===i(function(){return arguments}());t.exports=n?i:function(t){var e,r,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(r=function(t,e){try{return t[e]}catch(t){}}(e=u(t),a))?r:l?i(e):"Object"===(n=i(e))&&o(e.callee)?"Arguments":n}},1358:(t,e,r)=>{var n=r(2623),o=r(3054),i=r(3037),a=r(8503);t.exports=function(t,e,r){for(var u=o(e),l=a.f,c=i.f,s=0;s<u.length;s++){var f=u[s];n(t,f)||r&&n(r,f)||l(t,f,c(e,f))}}},6775:(t,e,r)=>{var n=r(5485)("match");t.exports=function(t){var e=/./;try{"/./"[t](e)}catch(r){try{return e[n]=!1,"/./"[t](e)}catch(t){}}return!1}},6141:(t,e,r)=>{var n=r(5202);t.exports=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},3585:t=>{t.exports=function(t,e){return{value:t,done:e}}},8868:(t,e,r)=>{var n=r(5007),o=r(8503),i=r(4238);t.exports=n?function(t,e,r){return o.f(t,e,i(1,r))}:function(t,e,r){return t[e]=r,t}},4238:t=>{t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},3003:(t,e,r)=>{var n=r(3164),o=r(8503),i=r(4238);t.exports=function(t,e,r){var a=n(e);a in t?o.f(t,a,i(0,r)):t[a]=r}},916:(t,e,r)=>{var n=r(2539),o=r(8503),i=r(6614),a=r(2136);t.exports=function(t,e,r,u){u||(u={});var l=u.enumerable,c=void 0!==u.name?u.name:e;if(n(r)&&i(r,c,u),u.global)l?t[e]=r:a(e,r);else{try{u.unsafe?t[e]&&(l=!0):delete t[e]}catch(t){}l?t[e]=r:o.f(t,e,{value:r,enumerable:!1,configurable:!u.nonConfigurable,writable:!u.nonWritable})}return t}},2136:(t,e,r)=>{var n=r(730),o=Object.defineProperty;t.exports=function(t,e){try{o(n,t,{value:e,configurable:!0,writable:!0})}catch(r){n[t]=e}return e}},5007:(t,e,r)=>{var n=r(5202);t.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},8629:t=>{var e="object"==typeof document&&document.all;t.exports={all:e,IS_HTMLDDA:void 0===e&&void 0!==e}},2215:(t,e,r)=>{var n=r(730),o=r(3988),i=n.document,a=o(i)&&o(i.createElement);t.exports=function(t){return a?i.createElement(t):{}}},932:t=>{t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},7626:(t,e,r)=>{var n,o,i=r(730),a=r(932),u=i.process,l=i.Deno,c=u&&u.versions||l&&l.version,s=c&&c.v8;s&&(o=(n=s.split("."))[0]>0&&n[0]<4?1:+(n[0]+n[1])),!o&&a&&(!(n=a.match(/Edge\/(\d+)/))||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/))&&(o=+n[1]),t.exports=o},6731:(t,e,r)=>{var n=r(730),o=r(4038);t.exports=function(t,e){return o(n[t].prototype[e])}},8552:t=>{t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},2189:(t,e,r)=>{var n=r(730),o=r(3037).f,i=r(8868),a=r(916),u=r(2136),l=r(1358),c=r(3455);t.exports=function(t,e){var r,s,f,p,h,y=t.target,d=t.global,b=t.stat;if(r=d?n:b?n[y]||u(y,{}):(n[y]||{}).prototype)for(s in e){if(p=e[s],f=t.dontCallGetSet?(h=o(r,s))&&h.value:r[s],!c(d?s:y+(b?".":"#")+s,t.forced)&&void 0!==f){if(typeof p==typeof f)continue;l(p,f)}(t.sham||f&&f.sham)&&i(p,"sham",!0),a(r,s,p,t)}}},5202:t=>{t.exports=function(t){try{return!!t()}catch(t){return!0}}},9794:(t,e,r)=>{var n=r(6192),o=r(6324),i=r(2865),a=n(n.bind);t.exports=function(t,e){return o(t),void 0===e?t:i?a(t,e):function(){return t.apply(e,arguments)}}},2865:(t,e,r)=>{var n=r(5202);t.exports=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},2977:(t,e,r)=>{var n=r(2865),o=Function.prototype.call;t.exports=n?o.bind(o):function(){return o.apply(o,arguments)}},9895:(t,e,r)=>{var n=r(5007),o=r(2623),i=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,u=o(i,"name"),l=u&&"something"===function(){}.name,c=u&&(!n||n&&a(i,"name").configurable);t.exports={EXISTS:u,PROPER:l,CONFIGURABLE:c}},6589:(t,e,r)=>{var n=r(4038),o=r(6324);t.exports=function(t,e,r){try{return n(o(Object.getOwnPropertyDescriptor(t,e)[r]))}catch(t){}}},6192:(t,e,r)=>{var n=r(4545),o=r(4038);t.exports=function(t){if("Function"===n(t))return o(t)}},4038:(t,e,r)=>{var n=r(2865),o=Function.prototype,i=o.call,a=n&&o.bind.bind(i,i);t.exports=n?a:function(t){return function(){return i.apply(t,arguments)}}},7300:(t,e,r)=>{var n=r(730),o=r(2539);t.exports=function(t,e){return arguments.length<2?o(r=n[t])?r:void 0:n[t]&&n[t][e];var r}},7874:(t,e,r)=>{var n=r(2702),o=r(3923),i=r(2219),a=r(7817),u=r(5485)("iterator");t.exports=function(t){if(!i(t))return o(t,u)||o(t,"@@iterator")||a[n(t)]}},2387:(t,e,r)=>{var n=r(2977),o=r(6324),i=r(8874),a=r(7471),u=r(7874),l=TypeError;t.exports=function(t,e){var r=arguments.length<2?u(t):e;if(o(r))return i(n(r,t));throw new l(a(t)+" is not iterable")}},3923:(t,e,r)=>{var n=r(6324),o=r(2219);t.exports=function(t,e){var r=t[e];return o(r)?void 0:n(r)}},730:function(t,e,r){var n=function(t){return t&&t.Math===Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof r.g&&r.g)||n("object"==typeof this&&this)||function(){return this}()||Function("return this")()},2623:(t,e,r)=>{var n=r(4038),o=r(6015),i=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return i(o(t),e)}},7467:t=>{t.exports={}},845:(t,e,r)=>{var n=r(7300);t.exports=n("document","documentElement")},2470:(t,e,r)=>{var n=r(5007),o=r(5202),i=r(2215);t.exports=!n&&!o((function(){return 7!==Object.defineProperty(i("div"),"a",{get:function(){return 7}}).a}))},6854:(t,e,r)=>{var n=r(4038),o=r(5202),i=r(4545),a=Object,u=n("".split);t.exports=o((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===i(t)?u(t,""):a(t)}:a},7821:(t,e,r)=>{var n=r(4038),o=r(2539),i=r(6838),a=n(Function.toString);o(i.inspectSource)||(i.inspectSource=function(t){return a(t)}),t.exports=i.inspectSource},5378:(t,e,r)=>{var n,o,i,a=r(2543),u=r(730),l=r(3988),c=r(8868),s=r(2623),f=r(6838),p=r(3511),h=r(7467),y="Object already initialized",d=u.TypeError;if(a||f.state){var b=f.state||(f.state=new(0,u.WeakMap));b.get=b.get,b.has=b.has,b.set=b.set,n=function(t,e){if(b.has(t))throw new d(y);return e.facade=t,b.set(t,e),e},o=function(t){return b.get(t)||{}},i=function(t){return b.has(t)}}else{var v=p("state");h[v]=!0,n=function(t,e){if(s(t,v))throw new d(y);return e.facade=t,c(t,v,e),e},o=function(t){return s(t,v)?t[v]:{}},i=function(t){return s(t,v)}}t.exports={set:n,get:o,has:i,enforce:function(t){return i(t)?o(t):n(t,{})},getterFor:function(t){return function(e){var r;if(!l(e)||(r=o(e)).type!==t)throw new d("Incompatible receiver, "+t+" required");return r}}}},7024:(t,e,r)=>{var n=r(5485),o=r(7817),i=n("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(o.Array===t||a[i]===t)}},2539:(t,e,r)=>{var n=r(8629),o=n.all;t.exports=n.IS_HTMLDDA?function(t){return"function"==typeof t||t===o}:function(t){return"function"==typeof t}},6467:(t,e,r)=>{var n=r(4038),o=r(5202),i=r(2539),a=r(2702),u=r(7300),l=r(7821),c=function(){},s=[],f=u("Reflect","construct"),p=/^\s*(?:class|function)\b/,h=n(p.exec),y=!p.test(c),d=function(t){if(!i(t))return!1;try{return f(c,s,t),!0}catch(t){return!1}},b=function(t){if(!i(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return y||!!h(p,l(t))}catch(t){return!0}};b.sham=!0,t.exports=!f||o((function(){var t;return d(d.call)||!d(Object)||!d((function(){t=!0}))||t}))?b:d},3455:(t,e,r)=>{var n=r(5202),o=r(2539),i=/#|\.prototype\./,a=function(t,e){var r=l[u(t)];return r===s||r!==c&&(o(e)?n(e):!!e)},u=a.normalize=function(t){return String(t).replace(i,".").toLowerCase()},l=a.data={},c=a.NATIVE="N",s=a.POLYFILL="P";t.exports=a},2219:t=>{t.exports=function(t){return null==t}},3988:(t,e,r)=>{var n=r(2539),o=r(8629),i=o.all;t.exports=o.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:n(t)||t===i}:function(t){return"object"==typeof t?null!==t:n(t)}},4708:t=>{t.exports=!1},184:(t,e,r)=>{var n=r(3988),o=r(4545),i=r(5485)("match");t.exports=function(t){var e;return n(t)&&(void 0!==(e=t[i])?!!e:"RegExp"===o(t))}},8820:(t,e,r)=>{var n=r(7300),o=r(2539),i=r(7676),a=r(3713),u=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return o(e)&&i(e.prototype,u(t))}},8880:(t,e,r)=>{var n=r(2977),o=r(8874),i=r(3923);t.exports=function(t,e,r){var a,u;o(t);try{if(!(a=i(t,"return"))){if("throw"===e)throw r;return r}a=n(a,t)}catch(t){u=!0,a=t}if("throw"===e)throw r;if(u)throw a;return o(a),r}},3450:(t,e,r)=>{var n=r(344).IteratorPrototype,o=r(3830),i=r(4238),a=r(3506),u=r(7817),l=function(){return this};t.exports=function(t,e,r,c){var s=e+" Iterator";return t.prototype=o(n,{next:i(+!c,r)}),a(t,s,!1,!0),u[s]=l,t}},3174:(t,e,r)=>{var n=r(2189),o=r(2977),i=r(4708),a=r(9895),u=r(2539),l=r(3450),c=r(8059),s=r(5502),f=r(3506),p=r(8868),h=r(916),y=r(5485),d=r(7817),b=r(344),v=a.PROPER,m=a.CONFIGURABLE,g=b.IteratorPrototype,w=b.BUGGY_SAFARI_ITERATORS,O=y("iterator"),j="keys",S="values",P="entries",_=function(){return this};t.exports=function(t,e,r,a,y,b,N){l(r,e,a);var E,k,x,T=function(t){if(t===y&&L)return L;if(!w&&t&&t in C)return C[t];switch(t){case j:case S:case P:return function(){return new r(this,t)}}return function(){return new r(this)}},A=e+" Iterator",R=!1,C=t.prototype,Z=C[O]||C["@@iterator"]||y&&C[y],L=!w&&Z||T(y),B="Array"===e&&C.entries||Z;if(B&&(E=c(B.call(new t)))!==Object.prototype&&E.next&&(i||c(E)===g||(s?s(E,g):u(E[O])||h(E,O,_)),f(E,A,!0,!0),i&&(d[A]=_)),v&&y===S&&Z&&Z.name!==S&&(!i&&m?p(C,"name",S):(R=!0,L=function(){return o(Z,this)})),y)if(k={values:T(S),keys:b?L:T(j),entries:T(P)},N)for(x in k)(w||R||!(x in C))&&h(C,x,k[x]);else n({target:e,proto:!0,forced:w||R},k);return i&&!N||C[O]===L||h(C,O,L,{name:y}),d[e]=L,k}},344:(t,e,r)=>{var n,o,i,a=r(5202),u=r(2539),l=r(3988),c=r(3830),s=r(8059),f=r(916),p=r(5485),h=r(4708),y=p("iterator"),d=!1;[].keys&&("next"in(i=[].keys())?(o=s(s(i)))!==Object.prototype&&(n=o):d=!0),!l(n)||a((function(){var t={};return n[y].call(t)!==t}))?n={}:h&&(n=c(n)),u(n[y])||f(n,y,(function(){return this})),t.exports={IteratorPrototype:n,BUGGY_SAFARI_ITERATORS:d}},7817:t=>{t.exports={}},3235:(t,e,r)=>{var n=r(3627);t.exports=function(t){return n(t.length)}},6614:(t,e,r)=>{var n=r(4038),o=r(5202),i=r(2539),a=r(2623),u=r(5007),l=r(9895).CONFIGURABLE,c=r(7821),s=r(5378),f=s.enforce,p=s.get,h=String,y=Object.defineProperty,d=n("".slice),b=n("".replace),v=n([].join),m=u&&!o((function(){return 8!==y((function(){}),"length",{value:8}).length})),g=String(String).split("String"),w=t.exports=function(t,e,r){"Symbol("===d(h(e),0,7)&&(e="["+b(h(e),/^Symbol\(([^)]*)\)/,"$1")+"]"),r&&r.getter&&(e="get "+e),r&&r.setter&&(e="set "+e),(!a(t,"name")||l&&t.name!==e)&&(u?y(t,"name",{value:e,configurable:!0}):t.name=e),m&&r&&a(r,"arity")&&t.length!==r.arity&&y(t,"length",{value:r.arity});try{r&&a(r,"constructor")&&r.constructor?u&&y(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(t){}var n=f(t);return a(n,"source")||(n.source=v(g,"string"==typeof e?e:"")),t};Function.prototype.toString=w((function(){return i(this)&&p(this).source||c(this)}),"toString")},3041:t=>{var e=Math.ceil,r=Math.floor;t.exports=Math.trunc||function(t){var n=+t;return(n>0?r:e)(n)}},50:(t,e,r)=>{var n=r(184),o=TypeError;t.exports=function(t){if(n(t))throw new o("The method doesn't accept regular expressions");return t}},3830:(t,e,r)=>{var n,o=r(8874),i=r(3944),a=r(8552),u=r(7467),l=r(845),c=r(2215),s=r(3511),f="prototype",p="script",h=s("IE_PROTO"),y=function(){},d=function(t){return"<"+p+">"+t+"</"+p+">"},b=function(t){t.write(d("")),t.close();var e=t.parentWindow.Object;return t=null,e},v=function(){try{n=new ActiveXObject("htmlfile")}catch(t){}var t,e,r;v="undefined"!=typeof document?document.domain&&n?b(n):(e=c("iframe"),r="java"+p+":",e.style.display="none",l.appendChild(e),e.src=String(r),(t=e.contentWindow.document).open(),t.write(d("document.F=Object")),t.close(),t.F):b(n);for(var o=a.length;o--;)delete v[f][a[o]];return v()};u[h]=!0,t.exports=Object.create||function(t,e){var r;return null!==t?(y[f]=o(t),r=new y,y[f]=null,r[h]=t):r=v(),void 0===e?r:i.f(r,e)}},3944:(t,e,r)=>{var n=r(5007),o=r(2268),i=r(8503),a=r(8874),u=r(7625),l=r(4254);e.f=n&&!o?Object.defineProperties:function(t,e){a(t);for(var r,n=u(e),o=l(e),c=o.length,s=0;c>s;)i.f(t,r=o[s++],n[r]);return t}},8503:(t,e,r)=>{var n=r(5007),o=r(2470),i=r(2268),a=r(8874),u=r(3164),l=TypeError,c=Object.defineProperty,s=Object.getOwnPropertyDescriptor,f="enumerable",p="configurable",h="writable";e.f=n?i?function(t,e,r){if(a(t),e=u(e),a(r),"function"==typeof t&&"prototype"===e&&"value"in r&&h in r&&!r[h]){var n=s(t,e);n&&n[h]&&(t[e]=r.value,r={configurable:p in r?r[p]:n[p],enumerable:f in r?r[f]:n[f],writable:!1})}return c(t,e,r)}:c:function(t,e,r){if(a(t),e=u(e),a(r),o)try{return c(t,e,r)}catch(t){}if("get"in r||"set"in r)throw new l("Accessors not supported");return"value"in r&&(t[e]=r.value),t}},3037:(t,e,r)=>{var n=r(5007),o=r(2977),i=r(6251),a=r(4238),u=r(7625),l=r(3164),c=r(2623),s=r(2470),f=Object.getOwnPropertyDescriptor;e.f=n?f:function(t,e){if(t=u(t),e=l(e),s)try{return f(t,e)}catch(t){}if(c(t,e))return a(!o(i.f,t,e),t[e])}},5974:(t,e,r)=>{var n=r(7862),o=r(8552).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,o)}},1526:(t,e)=>{e.f=Object.getOwnPropertySymbols},8059:(t,e,r)=>{var n=r(2623),o=r(2539),i=r(6015),a=r(3511),u=r(6141),l=a("IE_PROTO"),c=Object,s=c.prototype;t.exports=u?c.getPrototypeOf:function(t){var e=i(t);if(n(e,l))return e[l];var r=e.constructor;return o(r)&&e instanceof r?r.prototype:e instanceof c?s:null}},7676:(t,e,r)=>{var n=r(4038);t.exports=n({}.isPrototypeOf)},7862:(t,e,r)=>{var n=r(4038),o=r(2623),i=r(7625),a=r(5644).indexOf,u=r(7467),l=n([].push);t.exports=function(t,e){var r,n=i(t),c=0,s=[];for(r in n)!o(u,r)&&o(n,r)&&l(s,r);for(;e.length>c;)o(n,r=e[c++])&&(~a(s,r)||l(s,r));return s}},4254:(t,e,r)=>{var n=r(7862),o=r(8552);t.exports=Object.keys||function(t){return n(t,o)}},6251:(t,e)=>{var r={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,o=n&&!r.call({1:2},1);e.f=o?function(t){var e=n(this,t);return!!e&&e.enumerable}:r},5502:(t,e,r)=>{var n=r(6589),o=r(8874),i=r(2162);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,r={};try{(t=n(Object.prototype,"__proto__","set"))(r,[]),e=r instanceof Array}catch(t){}return function(r,n){return o(r),i(n),e?t(r,n):r.__proto__=n,r}}():void 0)},7830:(t,e,r)=>{var n=r(5007),o=r(5202),i=r(4038),a=r(8059),u=r(4254),l=r(7625),c=i(r(6251).f),s=i([].push),f=n&&o((function(){var t=Object.create(null);return t[2]=2,!c(t,2)})),p=function(t){return function(e){for(var r,o=l(e),i=u(o),p=f&&null===a(o),h=i.length,y=0,d=[];h>y;)r=i[y++],n&&!(p?r in o:c(o,r))||s(d,t?[r,o[r]]:o[r]);return d}};t.exports={entries:p(!0),values:p(!1)}},532:(t,e,r)=>{var n=r(2977),o=r(2539),i=r(3988),a=TypeError;t.exports=function(t,e){var r,u;if("string"===e&&o(r=t.toString)&&!i(u=n(r,t)))return u;if(o(r=t.valueOf)&&!i(u=n(r,t)))return u;if("string"!==e&&o(r=t.toString)&&!i(u=n(r,t)))return u;throw new a("Can't convert object to primitive value")}},3054:(t,e,r)=>{var n=r(7300),o=r(4038),i=r(5974),a=r(1526),u=r(8874),l=o([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=i.f(u(t)),r=a.f;return r?l(e,r(t)):e}},577:(t,e,r)=>{var n=r(730);t.exports=n},6373:(t,e,r)=>{var n=r(2219),o=TypeError;t.exports=function(t){if(n(t))throw new o("Can't call method on "+t);return t}},3506:(t,e,r)=>{var n=r(8503).f,o=r(2623),i=r(5485)("toStringTag");t.exports=function(t,e,r){t&&!r&&(t=t.prototype),t&&!o(t,i)&&n(t,i,{configurable:!0,value:e})}},3511:(t,e,r)=>{var n=r(6568),o=r(2858),i=n("keys");t.exports=function(t){return i[t]||(i[t]=o(t))}},6838:(t,e,r)=>{var n=r(730),o=r(2136),i="__core-js_shared__",a=n[i]||o(i,{});t.exports=a},6568:(t,e,r)=>{var n=r(4708),o=r(6838);(t.exports=function(t,e){return o[t]||(o[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.34.0",mode:n?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.34.0/LICENSE",source:"https://github.com/zloirock/core-js"})},6365:(t,e,r)=>{var n=r(4038),o=r(3550),i=r(2532),a=r(6373),u=n("".charAt),l=n("".charCodeAt),c=n("".slice),s=function(t){return function(e,r){var n,s,f=i(a(e)),p=o(r),h=f.length;return p<0||p>=h?t?"":void 0:(n=l(f,p))<55296||n>56319||p+1===h||(s=l(f,p+1))<56320||s>57343?t?u(f,p):n:t?c(f,p,p+2):s-56320+(n-55296<<10)+65536}};t.exports={codeAt:s(!1),charAt:s(!0)}},3370:(t,e,r)=>{var n=r(7626),o=r(5202),i=r(730).String;t.exports=!!Object.getOwnPropertySymbols&&!o((function(){var t=Symbol("symbol detection");return!i(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},7234:(t,e,r)=>{var n=r(3550),o=Math.max,i=Math.min;t.exports=function(t,e){var r=n(t);return r<0?o(r+e,0):i(r,e)}},7625:(t,e,r)=>{var n=r(6854),o=r(6373);t.exports=function(t){return n(o(t))}},3550:(t,e,r)=>{var n=r(3041);t.exports=function(t){var e=+t;return e!=e||0===e?0:n(e)}},3627:(t,e,r)=>{var n=r(3550),o=Math.min;t.exports=function(t){return t>0?o(n(t),9007199254740991):0}},6015:(t,e,r)=>{var n=r(6373),o=Object;t.exports=function(t){return o(n(t))}},634:(t,e,r)=>{var n=r(2977),o=r(3988),i=r(8820),a=r(3923),u=r(532),l=r(5485),c=TypeError,s=l("toPrimitive");t.exports=function(t,e){if(!o(t)||i(t))return t;var r,l=a(t,s);if(l){if(void 0===e&&(e="default"),r=n(l,t,e),!o(r)||i(r))return r;throw new c("Can't convert object to primitive value")}return void 0===e&&(e="number"),u(t,e)}},3164:(t,e,r)=>{var n=r(634),o=r(8820);t.exports=function(t){var e=n(t,"string");return o(e)?e:e+""}},1923:(t,e,r)=>{var n={};n[r(5485)("toStringTag")]="z",t.exports="[object z]"===String(n)},2532:(t,e,r)=>{var n=r(2702),o=String;t.exports=function(t){if("Symbol"===n(t))throw new TypeError("Cannot convert a Symbol value to a string");return o(t)}},7471:t=>{var e=String;t.exports=function(t){try{return e(t)}catch(t){return"Object"}}},2858:(t,e,r)=>{var n=r(4038),o=0,i=Math.random(),a=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++o+i,36)}},3713:(t,e,r)=>{var n=r(3370);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},2268:(t,e,r)=>{var n=r(5007),o=r(5202);t.exports=n&&o((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},2543:(t,e,r)=>{var n=r(730),o=r(2539),i=n.WeakMap;t.exports=o(i)&&/native code/.test(String(i))},5485:(t,e,r)=>{var n=r(730),o=r(6568),i=r(2623),a=r(2858),u=r(3370),l=r(3713),c=n.Symbol,s=o("wks"),f=l?c.for||c:c&&c.withoutSetter||a;t.exports=function(t){return i(s,t)||(s[t]=u&&i(c,t)?c[t]:f("Symbol."+t)),s[t]}},5445:(t,e,r)=>{var n=r(2189),o=r(9236),i=r(2572);n({target:"Array",proto:!0},{fill:o}),i("fill")},6272:(t,e,r)=>{var n=r(2189),o=r(8459);n({target:"Array",stat:!0,forced:!r(3346)((function(t){Array.from(t)}))},{from:o})},2947:(t,e,r)=>{var n=r(2189),o=r(7830).values;n({target:"Object",stat:!0},{values:function(t){return o(t)}})},538:(t,e,r)=>{var n,o=r(2189),i=r(6192),a=r(3037).f,u=r(3627),l=r(2532),c=r(50),s=r(6373),f=r(6775),p=r(4708),h=i("".endsWith),y=i("".slice),d=Math.min,b=f("endsWith");o({target:"String",proto:!0,forced:!(!p&&!b&&(n=a(String.prototype,"endsWith"),n&&!n.writable)||b)},{endsWith:function(t){var e=l(s(this));c(t);var r=arguments.length>1?arguments[1]:void 0,n=e.length,o=void 0===r?n:d(u(r),n),i=l(t);return h?h(e,i,o):y(e,o-i.length,o)===i}})},9477:(t,e,r)=>{var n=r(6365).charAt,o=r(2532),i=r(5378),a=r(3174),u=r(3585),l="String Iterator",c=i.set,s=i.getterFor(l);a(String,"String",(function(t){c(this,{type:l,string:o(t),index:0})}),(function(){var t,e=s(this),r=e.string,o=e.index;return o>=r.length?u(void 0,!0):(t=n(r,o),e.index+=t.length,u(t,!1))}))},9731:(t,e,r)=>{var n=r(2327);t.exports=n},4401:(t,e,r)=>{var n=r(3274);t.exports=n},3661:(t,e,r)=>{var n=r(5339);t.exports=n},5606:(t,e,r)=>{var n=r(1189);t.exports=n}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={id:n,loaded:!1,exports:{}};return t[n].call(i.exports,i,i.exports,r),i.loaded=!0,i.exports}r.n=t=>{var e=t&&t.__esModule?()=>t.default:()=>t;return r.d(e,{a:e}),e},r.d=(t,e)=>{for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nmd=t=>(t.paths=[],t.children||(t.children=[]),t);var n={};return(()=>{r.d(n,{default:()=>ke}),r(1019),r(5142),r(1183),r(2570);var t=r(2432),e=r(715),o=r(4048),i=r(1233);function a(t){return a="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},a(t)}function u(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==a(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==a(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===a(o)?o:String(o)),n)}var o}function l(){return l="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=f(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},l.apply(this,arguments)}function c(t,e){return c=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},c(t,e)}function s(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=f(t);if(e){var o=f(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===a(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function f(t){return f=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},f(t)}var p=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&c(t,e)}(i,t);var e,r,n,o=s(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,r=[{key:"add",value:function(t,e){if("+1"===e||"-1"===e){var r=this.value(t)||0;e="+1"===e?r+1:r-1}return 0===e?(this.remove(t),!0):l(f(i.prototype),"add",this).call(this,t,e)}},{key:"canAdd",value:function(t,e){return l(f(i.prototype),"canAdd",this).call(this,t,e)||l(f(i.prototype),"canAdd",this).call(this,t,parseInt(e,10))}},{key:"value",value:function(t){return parseInt(l(f(i.prototype),"value",this).call(this,t),10)||void 0}}],r&&u(e.prototype,r),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(i.ClassAttributor);const h=new p("indent","ql-indent",{scope:i.Scope.BLOCK,whitelist:[1,2,3,4,5,6,7,8]});var y=r(6446);function d(t){return d="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},d(t)}function b(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==d(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==d(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===d(o)?o:String(o)),n)}var o}function v(t,e){return v=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},v(t,e)}function m(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=g(t);if(e){var o=g(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===d(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function g(t){return g=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},g(t)}var w=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&v(t,e)}(i,t);var e,r,n,o=m(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,r&&b(e.prototype,r),n&&b(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(y.ZP);w.blotName="blockquote",w.tagName="blockquote";const O=w;function j(t){return j="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j(t)}function S(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==j(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==j(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===j(o)?o:String(o)),n)}var o}function P(t,e){return P=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},P(t,e)}function _(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=N(t);if(e){var o=N(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===j(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function N(t){return N=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},N(t)}var E=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&P(t,e)}(i,t);var e,r,n,o=_(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,n=[{key:"formats",value:function(t){return this.tagName.indexOf(t.tagName)+1}}],(r=null)&&S(e.prototype,r),n&&S(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(y.ZP);E.blotName="header",E.tagName=["H1","H2","H3","H4","H5","H6"];const k=E;var x=r(3553),T=r(281);function A(t){return A="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},A(t)}function R(){return R="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=D(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},R.apply(this,arguments)}function C(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==A(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==A(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===A(o)?o:String(o)),n)}var o}function Z(t,e,r){return e&&C(t.prototype,e),r&&C(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function L(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function B(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&q(t,e)}function q(t,e){return q=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},q(t,e)}function I(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=D(t);if(e){var o=D(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===A(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function D(t){return D=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},D(t)}var M=function(t){B(r,t);var e=I(r);function r(){return L(this,r),e.apply(this,arguments)}return Z(r)}(x.Z);M.blotName="list-container",M.tagName="OL";var U=function(t){B(r,t);var e=I(r);function r(t,n){var o;L(this,r),o=e.call(this,t,n);var i=n.ownerDocument.createElement("span"),a=function(e){if(t.isEnabled()){var r=o.statics.formats(n,t);"checked"===r?(o.format("list","unchecked"),e.preventDefault()):"unchecked"===r&&(o.format("list","checked"),e.preventDefault())}};return i.addEventListener("mousedown",a),i.addEventListener("touchstart",a),o.attachUI(i),o}return Z(r,[{key:"format",value:function(t,e){t===this.statics.blotName&&e?this.domNode.setAttribute("data-list",e):R(D(r.prototype),"format",this).call(this,t,e)}}],[{key:"create",value:function(t){var e=R(D(r),"create",this).call(this);return e.setAttribute("data-list",t),e}},{key:"formats",value:function(t){return t.getAttribute("data-list")||void 0}},{key:"register",value:function(){T.ZP.register(M)}}]),r}(y.ZP);U.blotName="list",U.tagName="LI",M.allowedChildren=[U],U.requiredContainer=M;var z=r(7898),F=r(6039),H=r(5832),K=r(1629),W=r(3991);function $(t){return $="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},$(t)}function G(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==$(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==$(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===$(o)?o:String(o)),n)}var o}function V(t,e){return V=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},V(t,e)}function Y(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=X(t);if(e){var o=X(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===$(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function X(t){return X=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},X(t)}var Q=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&V(t,e)}(i,t);var e,r,n,o=Y(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,r&&G(e.prototype,r),n&&G(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(W.Z);Q.blotName="italic",Q.tagName=["EM","I"];const J=Q;var tt=r(7256),et=r(6603);function rt(t){return rt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},rt(t)}function nt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==rt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==rt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===rt(o)?o:String(o)),n)}var o}function ot(){return ot="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=ut(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},ot.apply(this,arguments)}function it(t,e){return it=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},it(t,e)}function at(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=ut(t);if(e){var o=ut(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===rt(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function ut(t){return ut=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ut(t)}var lt=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&it(t,e)}(i,t);var e,r,n,o=at(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,n=[{key:"create",value:function(t){return"super"===t?document.createElement("sup"):"sub"===t?document.createElement("sub"):ot(ut(i),"create",this).call(this,t)}},{key:"formats",value:function(t){return"SUB"===t.tagName?"sub":"SUP"===t.tagName?"super":void 0}}],(r=null)&&nt(e.prototype,r),n&&nt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(et.Z);lt.blotName="script",lt.tagName=["SUB","SUP"];const ct=lt;function st(t){return st="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},st(t)}function ft(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==st(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==st(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===st(o)?o:String(o)),n)}var o}function pt(t,e){return pt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},pt(t,e)}function ht(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=yt(t);if(e){var o=yt(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===st(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function yt(t){return yt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},yt(t)}var dt=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&pt(t,e)}(i,t);var e,r,n,o=ht(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,r&&ft(e.prototype,r),n&&ft(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(W.Z);dt.blotName="strike",dt.tagName=["S","STRIKE"];const bt=dt;function vt(t){return vt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},vt(t)}function mt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==vt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==vt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===vt(o)?o:String(o)),n)}var o}function gt(t,e){return gt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},gt(t,e)}function wt(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Ot(t);if(e){var o=Ot(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===vt(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function Ot(t){return Ot=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Ot(t)}var jt=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&gt(t,e)}(i,t);var e,r,n,o=wt(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,r&&mt(e.prototype,r),n&&mt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}(et.Z);jt.blotName="underline",jt.tagName="U";const St=jt;var Pt=r(7452),_t=r(8034);function Nt(t){return Nt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Nt(t)}function Et(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==Nt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Nt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===Nt(o)?o:String(o)),n)}var o}function kt(){return kt="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=At(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},kt.apply(this,arguments)}function xt(t,e){return xt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},xt(t,e)}function Tt(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=At(t);if(e){var o=At(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===Nt(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function At(t){return At=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},At(t)}var Rt=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&xt(t,e)}(i,t);var e,r,n,o=Tt(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,n=[{key:"create",value:function(t){var e=null;if((0,_t.Z)()&&(e=window.katex),null==e)throw new Error("Formula module requires KaTeX.");var r=kt(At(i),"create",this).call(this,t);return"string"==typeof t&&(e.render(t,r,{throwOnError:!1,errorColor:"#f00"}),r.setAttribute("data-value",t)),r}},{key:"value",value:function(t){return t.getAttribute("data-value")}}],(r=[{key:"html",value:function(){var t=this.value();return"<span>".concat(t.formula,"</span>")}}])&&Et(e.prototype,r),n&&Et(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(Pt.Z);Rt.blotName="formula",Rt.className="ql-formula",Rt.tagName="SPAN";const Ct=Rt;function Zt(t){return Zt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Zt(t)}function Lt(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==Zt(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Zt(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===Zt(o)?o:String(o)),n)}var o}function Bt(){return Bt="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=Dt(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},Bt.apply(this,arguments)}function qt(t,e){return qt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},qt(t,e)}function It(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Dt(t);if(e){var o=Dt(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===Zt(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function Dt(t){return Dt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Dt(t)}var Mt=["alt","height","width"],Ut=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&qt(t,e)}(i,t);var e,r,n,o=It(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,n=[{key:"create",value:function(t){var e=Bt(Dt(i),"create",this).call(this,t);return"string"==typeof t&&e.setAttribute("src",this.sanitize(t)),e}},{key:"formats",value:function(t){return Mt.reduce((function(e,r){return t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e}),{})}},{key:"match",value:function(t){return/\.(jpe?g|gif|png)$/.test(t)||/^data:image\/.+;base64/.test(t)}},{key:"register",value:function(){(0,_t.Z)()&&/Firefox/i.test(navigator.userAgent)&&setTimeout((function(){document.execCommand("enableObjectResizing",!1,!1)}),1)}},{key:"sanitize",value:function(t){return(0,tt.N)(t,["http","https","data"])?t:"//:0"}},{key:"value",value:function(t){return t.getAttribute("src")}}],(r=[{key:"format",value:function(t,e){Mt.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):Bt(Dt(i.prototype),"format",this).call(this,t,e)}}])&&Lt(e.prototype,r),n&&Lt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(i.EmbedBlot);Ut.blotName="image",Ut.tagName="IMG";const zt=Ut;function Ft(t){return Ft="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Ft(t)}function Ht(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==Ft(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Ft(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===Ft(o)?o:String(o)),n)}var o}function Kt(){return Kt="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(t,e,r){var n=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=Gt(t)););return t}(t,e);if(n){var o=Object.getOwnPropertyDescriptor(n,e);return o.get?o.get.call(arguments.length<3?t:r):o.value}},Kt.apply(this,arguments)}function Wt(t,e){return Wt=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Wt(t,e)}function $t(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=Gt(t);if(e){var o=Gt(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===Ft(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function Gt(t){return Gt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Gt(t)}var Vt=["height","width"],Yt=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Wt(t,e)}(i,t);var e,r,n,o=$t(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,r=[{key:"format",value:function(t,e){Vt.indexOf(t)>-1?e?this.domNode.setAttribute(t,e):this.domNode.removeAttribute(t):Kt(Gt(i.prototype),"format",this).call(this,t,e)}},{key:"html",value:function(){var t=this.value().video;return'<a href="'.concat(t,'">').concat(t,"</a>")}}],n=[{key:"create",value:function(t){var e=Kt(Gt(i),"create",this).call(this,t);return e.setAttribute("frameborder","0"),e.setAttribute("allowfullscreen",!0),e.setAttribute("src",this.sanitize(t)),e}},{key:"formats",value:function(t){return Vt.reduce((function(e,r){return t.hasAttribute(r)&&(e[r]=t.getAttribute(r)),e}),{})}},{key:"sanitize",value:function(t){return tt.Z.sanitize(t)}},{key:"value",value:function(t){return t.getAttribute("src")}}],r&&Ht(e.prototype,r),n&&Ht(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(y.i2);Yt.blotName="video",Yt.className="ql-video",Yt.tagName="IFRAME";const Xt=Yt;var Qt=r(7309),Jt=r(9072),te=r(867),ee=r(9098),re=r.n(ee);function ne(t){return ne="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},ne(t)}function oe(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(o=void 0,o=function(t,e){if("object"!==ne(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==ne(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(n.key,"string"),"symbol"===ne(o)?o:String(o)),n)}var o}function ie(t,e){return ie=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ie(t,e)}function ae(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=ue(t);if(e){var o=ue(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===ne(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,r)}}function ue(t){return ue=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ue(t)}var le=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ie(t,e)}(i,t);var e,r,n,o=ae(i);function i(){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),o.apply(this,arguments)}return e=i,n=[{key:"value",value:function(){return"\n"}}],(r=[{key:"length",value:function(){return 1}},{key:"value",value:function(){return"\n"}},{key:"optimize",value:function(){this.prev||this.next||this.remove()}}])&&oe(e.prototype,r),n&&oe(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(i.EmbedBlot);le.blotName="multilineBreak",le.tagName="BR";const ce=le;function se(t){return se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},se(t)}function fe(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pe(n.key),n)}}function pe(t){var e=function(t,e){if("object"!==se(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==se(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===se(e)?e:String(e)}function he(t,e){return he=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},he(t,e)}function ye(t){var e=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}();return function(){var r,n=be(t);if(e){var o=be(this).constructor;r=Reflect.construct(n,arguments,o)}else r=n.apply(this,arguments);return function(t,e){if(e&&("object"===se(e)||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return de(t)}(this,r)}}function de(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function be(t){return be=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},be(t)}function ve(t){return t.nextSibling||t.previousSibling?(new(re())).insert({multilineBreak:""}):(new(re())).insert("\n")}var me=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&he(t,e)}(i,t);var e,r,n,o=ye(i);function i(t,e){var r,n,a,u;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,i),r=o.call(this,t,e),T.ZP.register((n={},u=ce,(a=pe(a="blots/multilineBreak"))in n?Object.defineProperty(n,a,{value:u,enumerable:!0,configurable:!0,writable:!0}):n[a]=u,n),!0),t.keyboard.addBinding({key:"enter",shiftKey:!0},r.enterHandler.bind(de(r))),t.keyboard.bindings.enter.unshift(t.keyboard.bindings.enter.pop()),t.clipboard.addMatcher("BR",ve),r}return e=i,(r=[{key:"enterHandler",value:function(t){var e=this.quill.getLeaf(t.index)[0],r=this.quill.getLeaf(t.index+1)[0];this.quill.insertEmbed(t.index,"multilineBreak",!0,"user"),null!==r&&e.parent===r.parent||this.quill.insertEmbed(t.index,"multilineBreak",!0,"user"),this.quill.setSelection(t.index+1,T.ZP.sources.SILENT)}}])&&fe(e.prototype,r),n&&fe(e,n),Object.defineProperty(e,"prototype",{writable:!1}),i}(r(7094).Z);const ge=me;var we=r(6054),Oe=r(1342),je=r(319);function Se(t){return Se="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},Se(t)}function Pe(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function _e(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Pe(Object(r),!0).forEach((function(e){Ne(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Pe(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Ne(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==Se(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==Se(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}(t,"string");return"symbol"===Se(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Ee(t,e){return e.reduce((function(e,r){return e["".concat(t).concat(r.attrName)]=r,e}),{})}t.default.register(_e(_e(_e(_e({"attributors/attribute/direction":o.IF},Ee("attributors/attribute/",je.Qu)),Ee("attributors/attribute/",Oe.yA)),{},{"attributors/class/align":e.dk,"attributors/class/background":z.Y,"attributors/class/color":F.Hn,"attributors/class/direction":o.hY,"attributors/class/font":H._,"attributors/class/size":K.m,"attributors/style/align":e.HE,"attributors/style/background":z.w,"attributors/style/color":F.HQ,"attributors/style/direction":o.H8,"attributors/style/font":H.H,"attributors/style/size":K.Z},Ee("attributors/style/",je.VT)),Ee("attributors/style/",Oe.kk)),!0),t.default.register({"formats/align":e.dk,"formats/direction":o.hY,"formats/indent":h,"formats/background":z.w,"formats/color":F.HQ,"formats/font":H._,"formats/size":K.m,"formats/blockquote":O,"formats/code-block":Qt.ZP,"formats/header":k,"formats/list":U,"formats/bold":W.Z,"formats/code":Qt.EK,"formats/italic":J,"formats/link":tt.Z,"formats/script":ct,"formats/strike":bt,"formats/underline":St,"formats/formula":Ct,"formats/image":zt,"formats/video":Xt,"tableModules/lite":we.Z,"tableModules/main":te.Z,"modules/syntax":Jt.ZP,"modules/multiline":ge,"modules/table":te.Z},!0);const ke=t.default})(),n=n.default})(),t.exports=r()}));const o=t(n),i=window;i.DevExpress={...i.DevExpress,Quill:o};
