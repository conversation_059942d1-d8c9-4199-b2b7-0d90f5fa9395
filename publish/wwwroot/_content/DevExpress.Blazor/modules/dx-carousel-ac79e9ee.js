import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{K as i,D as s}from"./keyboard-navigation-strategy-ea41c807.js";import{k as n}from"./key-ffa272aa.js";import{C as o}from"./custom-events-helper-e7f279d3.js";import{C as r}from"./events-a8fe5872.js";import{n as a}from"./property-4ec0b52d.js";import{e as h}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./focushelper-2eea96ca.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./eventhelper-8bcec49f.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./devices-17b9ba08.js";class l extends i{constructor(e){super(e.getKeyboardNavigator(),e),this.component=e}handleKeyDown(e){const t=n.KeyUtils.getEventKeyCode(e);if(super.handleKeyDown(e))return!0;if(this.nestedContentSelected)return!1;switch(t){case n.KeyCode.Space:case n.KeyCode.Right:case n.KeyCode.Left:return this.performShortcutEvent(e),!0;case n.KeyCode.Tab:return this.handleTabKeyDown(e);default:return!1}}queryItems(){return new Array(this.component)}handleTabKeyDown(e){return e.shiftKey?this.leaveBackward():this.leaveForward(),!0}isImmediatelyClickEnabled(){return!1}}const d="dxbl-carousel";class c extends r{constructor(){super(c.eventName)}}c.eventName=d+".update-slide-show";class p extends CustomEvent{constructor(e,t){super(p.eventName,{detail:new m(e,t),bubbles:!0,composed:!0,cancelable:!0})}}p.eventName=d+".swipe-end";class m{constructor(e,t){this.Diff=e,this.HalfWidth=t}}class v extends CustomEvent{constructor(e){super(v.eventName,{detail:new u(e),bubbles:!0,composed:!0,cancelable:!0})}}v.eventName=d+".mouse-scroll";class u{constructor(e){this.Delta=e}}o.register(c.eventName,(e=>e.detail)),o.register(p.eventName,(e=>e.detail)),o.register(v.eventName,(e=>e.detail));const b="none";let y=class extends t{get content(){return this.querySelector(".dxbl-carousel-content")}constructor(){super(),this.swipingStartX=0,this.isStartSwipe=!1,this.width=0,this.isDragging=!1,this.lastWheelExecutionTime=null,this.allowMouseWheel=!1,this.animation="",this.activeIndex=-1,this.displayIndex=-1,this.loop=!1,this.resizeObserver=new ResizeObserver((e=>{for(const t of e)this.width=t.contentRect.width}))}moveNext(e=this.animation){var t;if(this.setTransition(e),this.activeIndex===this.displayIndex){const e=this.getTransform(this.activeIndex);this.content&&this.content.style.transform!==e&&this.applyTransform(e)}else{const e=this.getTransform(this.displayIndex);if(this.content&&this.content.style.transform!==e){const i=this.activeIndex;this._moveFast=()=>{var e,t;null===(e=this.content)||void 0===e||e.removeEventListener("transitionend",this._moveFast),this.setTransition(b),this.applyTransform(this.getTransform(i)),null===(t=this.content)||void 0===t||t.getBoundingClientRect(),this._moveFast=void 0},null===(t=this.content)||void 0===t||t.addEventListener("transitionend",this._moveFast),this.applyTransform(e)}}}updated(e){super.updated(e),e.has("loop")?this.moveNext(b):e.has("activeIndex")&&(this._moveFast&&this._moveFast(),this.moveNext())}connectedCallback(){this.addEventListener("wheel",this._onMouseWheel),this.addEventListener("pointerdown",this._onPointerDown),this.addEventListener("pointermove",this._onPointerMove),this.addEventListener("pointerup",this._onPointerUp),this.addEventListener("pointerleave",this._onPointerUp),super.connectedCallback(),this.resizeObserver.observe(this)}disconnectedCallback(){var e;this.removeEventListener("wheel",this._onMouseWheel),this.removeEventListener("pointerdown",this._onPointerDown),this.removeEventListener("pointermove",this._onPointerMove),this.removeEventListener("pointerup",this._onPointerUp),this.removeEventListener("pointerleave",this._onPointerUp),delete this.keyboardNavigator,super.disconnectedCallback(),null===(e=this.resizeObserver)||void 0===e||e.unobserve(this)}_onMouseWheel(e){if(this.allowMouseWheel){e.preventDefault();const t=new Date;if(this.lastWheelExecutionTime&&t.getTime()-this.lastWheelExecutionTime.getTime()<100)return;this.lastWheelExecutionTime=t,this.dispatchEvent(new v(e.deltaY))}}_onPointerDown(e){e.preventDefault(),this.swipingStartX=e.clientX,this.isStartSwipe=!0}_onPointerMove(e){if(this.isStartSwipe&&Math.abs(e.clientX-this.swipingStartX)>0){this.isDragging||(this.isDragging=!0,this.dispatchEvent(new c)),this.setTransition(b);const t=Math.round(100*(e.clientX-this.swipingStartX)/this.width);this.applyTransform(`translateX(${-100*this.activeIndex+t}%)`)}}_onPointerUp(e){if(this.isStartSwipe=!1,this.isDragging){this.isDragging=!1;const t=e.clientX-this.swipingStartX,i=this.width/2;this.setTransition(this.animation),(Math.abs(t)<i||this.isExtremeIndex())&&this.applyTransform(this.getTransform(this.activeIndex)),this.dispatchEvent(new p(t,i))}}isExtremeIndex(){return!this.loop&&(0===this.activeIndex||this.activeIndex===this.content.childElementCount-1)}initializeKeyboardNavigator(){this.keyboardNavigator||(this.keyboardNavigator=this.querySelector(s),this.keyboardNavigator&&!this.keyboardNavigator.initialized&&this.keyboardNavigator.initialize(this,new l(this)))}getKeyboardNavigator(){return this.keyboardNavigator}connectedOrContentChanged(){super.connectedOrContentChanged(),this.applyTransform(this.getTransform(this.activeIndex)),this.initializeKeyboardNavigator()}getTransform(e){return`translateX(${-100*e}%)`}applyTransform(e){this.content&&(this.content.style.transform=e)}setTransition(e){this.content&&(this.content.style.transition=e)}createRenderRoot(){return this}};e([a({attribute:"allow-mouse-wheel",type:Boolean})],y.prototype,"allowMouseWheel",void 0),e([a({attribute:"animation",type:String})],y.prototype,"animation",void 0),e([a({attribute:"active-index",type:Number})],y.prototype,"activeIndex",void 0),e([a({attribute:"display-index",type:Number})],y.prototype,"displayIndex",void 0),e([a({attribute:"loop",type:Boolean})],y.prototype,"loop",void 0),y=e([h("dxbl-carousel")],y);export{y as DxCarousel};
