function t(t,n){return function(e,o){return o=Array.prototype.slice.call(o),t().then((function(t){return new Promise((function(r,i){n&&(r(),r=()=>{},i=t=>console.warn(t));try{const n=t.default[e].apply(t,o);n&&n.then?n.then(r).catch(i):r(n)}catch(t){i(t)}}))}))}}const n=t((function(){return import("./dropdowns-63f1434e.js")})),e=t((function(){return import("./dropdowns-63f1434e.js")}),!0);const o={init:function(){return n("init",arguments)},dispose:function(){return e("dispose",arguments)},showAdaptiveDropdown:function(){return n("showAdaptiveDropdown",arguments)},updateGridDropDown:function(){return n("updateGridDropDown",arguments)},setInlineModalSize:function(){return n("setInlineModalSize",arguments)}},r=t((function(){return import("./dx-combobox-5b8c7dc7.js")}));const i={loadModule:function(){return r("loadModule",arguments)},adaptiveDropdownComponents:function(){return r("adaptiveDropdownComponents",arguments)}},d=t((function(){return import("./dx-date-edit-0d8ef0d7.js")}));const u={loadModule:function(){return d("loadModule",arguments)},adaptiveDropdownComponents:function(){return d("adaptiveDropdownComponents",arguments)},calendarComponent:function(){return d("calendarComponent",arguments)}},a=t((function(){return import("./dx-date-range-picker-65d1882e.js")}));const l={loadModule:function(){return a("loadModule",arguments)},adaptiveDropdownComponents:function(){return a("adaptiveDropdownComponents",arguments)},calendarComponent:function(){return a("calendarComponent",arguments)}},c=t((function(){return import("./dx-date-time-edit-2a3f5354.js")}));const s={loadModule:function(){return c("loadModule",arguments)},adaptiveDropdownComponents:function(){return c("adaptiveDropdownComponents",arguments)},calendarComponent:function(){return c("calendarComponent",arguments)}},p=t((function(){return import("./dx-tagbox-ea5a2465.js")}));const f={loadModule:function(){return p("loadModule",arguments)},forceInputValue:function(){return p("forceInputValue",arguments)}},m=t((function(){return import("./dx-listbox-9345a0b2.js")}));const b={loadModule:function(){return m("loadModule",arguments)}},x=t((function(){return import("./dx-memo-dc5bd05d.js")}));const g={loadModule:function(){return x("loadModule",arguments)}},M=t((function(){return import("./dragAndDropUnit-a8432e5f.js")}));const j={DragAndDropUnit:function(){return M("DragAndDropUnit",arguments)},FocusUnit:function(){return M("FocusUnit",arguments)},RectBlz:function(){return M("RectBlz",arguments)},PointBlz:function(){return M("PointBlz",arguments)},getClientRect:function(){return M("getClientRect",arguments)},getClientRectWithMargins:function(){return M("getClientRectWithMargins",arguments)},geometry:function(){return M("geometry",arguments)}},D=t((function(){return import("./dx-check-internal-a6f9ec37.js")}));const h={loadModule:function(){return D("loadModule",arguments)}},C=t((function(){return import("./columnchooser-de0542af.js")})),w=t((function(){return import("./columnchooser-de0542af.js")}),!0);const v={init:function(){return C("init",arguments)},dispose:function(){return w("dispose",arguments)}},T=t((function(){return import("./dom-utils-d057dcaa.js").then((t=>t.M))}));const R={focusElement:function(){return T("focusElement",arguments)},setInputAttribute:function(){return T("setInputAttribute",arguments)},setCheckInputIndeterminate:function(){return T("setCheckInputIndeterminate",arguments)}},y=t((function(){return import("./grid-985dd353.js")})),B=t((function(){return import("./grid-985dd353.js")}),!0);const S={init:function(){return y("init",arguments)},dispose:function(){return B("dispose",arguments)}},k=t((function(){return import("./dx-grid-444da71f.js").then((t=>t.k))}));const P={loadModule:function(){return k("loadModule",arguments)}},I=t((function(){return import("./dx-tree-list-ba2309b4.js")}));const E={loadModule:function(){return I("loadModule",arguments)}},N=t((function(){return import("./column-resize-7a55fd84.js")}));const z={initResizeColumn:function(){return N("initResizeColumn",arguments)}},A=t((function(){return import("./scheduler-8335afbb.js")}));const F={loadModule:function(){return A("loadModule",arguments)}},L=t((function(){return import("./utils-b5b2c8a9.js")}));const U={getReference:function(){return L("getReference",arguments)}},G=t((function(){return import("./dx-calendar-1293eea4.js").then((t=>t.d))}));const V={loadModule:function(){return G("loadModule",arguments)}},H=t((function(){return import("./roller-e212033a.js")}));const W={initializeDateRoller:function(){return H("initializeDateRoller",arguments)},initializeTimeRoller:function(){return H("initializeTimeRoller",arguments)},updateRoller:function(){return H("updateRoller",arguments)}},O=t((function(){return import("./positiontracker-754c1e75.js")}));const K={init:function(){return O("init",arguments)},PositionTracker:function(){return O("PositionTracker",arguments)}},q=t((function(){return import("./positionlistener-bcff17ed.js")}));const J={init:function(){return q("init",arguments)},PositionListener:function(){return q("PositionListener",arguments)}},Q=t((function(){return import("./draggable-0c5d4ff1.js")}));const X={init:function(){return Q("init",arguments)},DxDraggable:function(){return Q("DxDraggable",arguments)},dxDraggableTagName:function(){return Q("dxDraggableTagName",arguments)}},Y=t((function(){return import("./modalcomponents-951e20e2.js")}));const Z={getReference:function(){return Y("getReference",arguments)},registeredComponents:function(){return Y("registeredComponents",arguments)}},$=t((function(){return import("./branch-aebd078a.js")}));const _={init:function(){return $("init",arguments)},DxBranch:function(){return $("DxBranch",arguments)},dxBranchTagName:function(){return $("dxBranchTagName",arguments)},BranchUpdatedEvent:function(){return $("BranchUpdatedEvent",arguments)}},tt=t((function(){return import("./popup-355ecaa4.js").then((t=>t.N))}));const nt={init:function(){return tt("init",arguments)},getReference:function(){return tt("getReference",arguments)},dxPopupTagName:function(){return tt("dxPopupTagName",arguments)},DxPopup:function(){return tt("DxPopup",arguments)}},et=t((function(){return import("./flyoutcomponents-879758ab.js")}));const ot={getReference:function(){return et("getReference",arguments)},registeredComponents:function(){return et("registeredComponents",arguments)}},rt=t((function(){return import("./dropdowncomponents-3d8f06da.js").then((t=>t.d))}));const it={getReference:function(){return rt("getReference",arguments)},registeredComponents:function(){return rt("registeredComponents",arguments)}},dt=t((function(){return import("./thumb-31d768d7.js")}));const ut={init:function(){return dt("init",arguments)},dxThumbTagName:function(){return dt("dxThumbTagName",arguments)},ThumbDragStartedEvent:function(){return dt("ThumbDragStartedEvent",arguments)},ThumbDragDeltaEvent:function(){return dt("ThumbDragDeltaEvent",arguments)},ThumbDragCompletedEvent:function(){return dt("ThumbDragCompletedEvent",arguments)},DragContext:function(){return dt("DragContext",arguments)}},at=t((function(){return import("./portal-b3727c25.js").then((t=>t.p))}));const lt={init:function(){return at("init",arguments)},DxPortal:function(){return at("DxPortal",arguments)},dxPortalTagName:function(){return at("dxPortalTagName",arguments)}},ct=t((function(){return import("./capture-manager-2454adc2.js").then((t=>t.c))}));const st={LightCaptureManager:function(){return ct("LightCaptureManager",arguments)},getCaptureManagerSingletonForTests:function(){return ct("getCaptureManagerSingletonForTests",arguments)}},pt=t((function(){return import("./adaptivedropdowncomponents-7cb91d74.js")}));const ft={dropDownRegisteredComponents:function(){return pt("dropDownRegisteredComponents",arguments)},modalRegisteredComponents:function(){return pt("modalRegisteredComponents",arguments)},init:function(){return pt("init",arguments)},dxAdaptiveDropDownTagName:function(){return pt("dxAdaptiveDropDownTagName",arguments)},dxEventsInterceptorTagName:function(){return pt("dxEventsInterceptorTagName",arguments)}},mt=t((function(){return import("./upload-840161c0.js")}));const bt={loadModule:function(){return mt("loadModule",arguments)},initDotNetReference:function(){return mt("initDotNetReference",arguments)},getRecentlyAddedFileInfosStream:function(){return mt("getRecentlyAddedFileInfosStream",arguments)}},xt=t((function(){return import("./file-input-01f4e96d.js")}));const gt={loadModule:function(){return xt("loadModule",arguments)},initDotNetReference:function(){return xt("initDotNetReference",arguments)},getFileBytes:function(){return xt("getFileBytes",arguments)},updateFileStatus:function(){return xt("updateFileStatus",arguments)},getRecentlyAddedFileInfosStream:function(){return xt("getRecentlyAddedFileInfosStream",arguments)}},Mt=t((function(){return import("./toolbar-83f5a0c7.js")})),jt=t((function(){return import("./toolbar-83f5a0c7.js")}),!0);const Dt={init:function(){return Mt("init",arguments)},dispose:function(){return jt("dispose",arguments)}},ht=t((function(){return import("./spinedit-a2f64d23.js")}));const Ct={loadModule:function(){return ht("loadModule",arguments)}},wt=t((function(){return import("./masked-input-0c0a9541.js")}));const vt={loadModule:function(){return wt("loadModule",arguments)}},Tt=t((function(){return import("./focus-utils-ae044224.js")}));const Rt={initFocusHidingEvents:function(){return Tt("initFocusHidingEvents",arguments)}},yt=t((function(){return import("./window-resize-helper-8b32f302.js")})),Bt=t((function(){return import("./window-resize-helper-8b32f302.js")}),!0);const St={init:function(){return yt("init",arguments)},dispose:function(){return Bt("dispose",arguments)}},kt=t((function(){return import("./dx-style-helper-d10228f6.js")}));const Pt={ensureAccentColorStyle:function(){return kt("ensureAccentColorStyle",arguments)},showDeprecatedStyleSheetWarningIfNeeded:function(){return kt("showDeprecatedStyleSheetWarningIfNeeded",arguments)}},It=t((function(){return import("./dx-menu-48481ff0.js")}));const Et={loadModule:function(){return It("loadModule",arguments)}},Nt=t((function(){return import("./dx-menu-item-4f0de325.js").then((t=>t.d))}));const zt={loadModule:function(){return Nt("loadModule",arguments)}},At=t((function(){return import("./dx-context-menu-8ee9f551.js")}));const Ft={loadModule:function(){return At("loadModule",arguments)}},Lt=t((function(){return import("./custom-color-area-100d17b7.js")}));const Ut={loadModule:function(){return Lt("loadModule",arguments)}},Gt=t((function(){return import("./ribbon-ac381a65.js")})),Vt=t((function(){return import("./ribbon-ac381a65.js")}),!0);const Ht={init:function(){return Gt("init",arguments)},dispose:function(){return Vt("dispose",arguments)}},Wt=t((function(){return import("./ribbon-tabs-1043ab2f.js")}));const Ot={loadModule:function(){return Wt("loadModule",arguments)}},Kt=t((function(){return import("./index-2fbd27c3.js")}));const qt={loadModule:function(){return Kt("loadModule",arguments)},registeredComponents:function(){return Kt("registeredComponents",arguments)}},Jt=t((function(){return import("./group-control-b78a6b47.js")}));const Qt={loadModule:function(){return Jt("loadModule",arguments)}},Xt=t((function(){return import("./dx-ui-handlers-bridge-c2148178.js")}));const Yt={loadModule:function(){return Xt("loadModule",arguments)}},Zt=t((function(){return import("./input-66769c52.js").then((t=>t.i))}));const $t={loadModule:function(){return Zt("loadModule",arguments)}},_t=t((function(){return import("./pager-64a19cb6.js")}));const tn={loadModule:function(){return _t("loadModule",arguments)}},nn=t((function(){return import("./expandable-container-f4a1ccde.js")}));const en={loadModule:function(){return nn("loadModule",arguments)}},on=t((function(){return import("./dx-css-runtime-b3e20aad.js")}));const rn={initializeThemeCssRuntimeVariables:function(){return on("initializeThemeCssRuntimeVariables",arguments)}},dn=t((function(){return import("./dx-list-box-4a9879cc.js")}));const un={loadModule:function(){return dn("loadModule",arguments)}},an=t((function(){return import("./dx-combo-box-51cbb701.js")}));const ln={loadModule:function(){return an("loadModule",arguments)},adaptiveDropdownComponents:function(){return an("adaptiveDropdownComponents",arguments)}},cn=t((function(){return import("./dx-tag-box-60efde02.js")}));const sn={loadModule:function(){return cn("loadModule",arguments)},adaptiveDropdownComponents:function(){return cn("adaptiveDropdownComponents",arguments)}},pn=t((function(){return import("./dx-dropdown-box-4205d2fa.js")}));const fn={loadModule:function(){return pn("loadModule",arguments)},adaptiveDropdownComponents:function(){return pn("adaptiveDropdownComponents",arguments)}},mn=t((function(){return import("./dx-color-palette-a90e9402.js")}));const bn={loadModule:function(){return mn("loadModule",arguments)}},xn=t((function(){return import("./dx-pivot-table-007c8d18.js")}));const gn={loadModule:function(){return xn("loadModule",arguments)}};rn.initializeThemeCssRuntimeVariables();const Mn={ClientComponentUtils:U,EditorsDropDown:o,ComboBox:i,DateEdit:u,DateRangePicker:l,DateTimeEdit:s,TagBox:f,ListBoxLegacy:b,CheckBox:h,Memo:g,Dom:R,DataGrid:S,DataGridColumnResize:z,Grid:P,TreeList:E,Scheduler:F,Calendar:V,Roller:W,PositionTracker:K,PositionListener:J,Draggable:X,Branch:_,Modal:Z,Popup:nt,DropDown:it,Thumb:ut,Flyout:ot,Portal:lt,CaptureManager:st,AdaptiveDropDown:ft,Upload:bt,FileInput:gt,DataGridColumnChooser:v,DragAndDrop:j,SpinEdit:Ct,MaskedInput:vt,FocusUtils:Rt,Toolbar:Dt,WindowResizeHelper:St,StyleHelper:Pt,MenuComponent:Et,MenuItem:zt,ContextMenu:Ft,CustomColorArea:Ut,Ribbon:Ht,Tabs:Ot,ScrollViewer:qt,UiHandlersBridge:Yt,Input:$t,Pager:tn,GroupControl:Qt,ExpandableContainer:en,ListBox:un,ComboBox2:ln,TagBox2:sn,DropDownBox:fn,ColorPalette:bn,PivotTable:gn};window.DxBlazor=Mn,window.DxBlazor.StyleHelper.showDeprecatedStyleSheetWarningIfNeeded();const jn=Mn;(t=>{let n=null;const e=()=>{Object.keys(t).forEach((e=>{document.getElementsByTagName(e).length>0&&(t[e](),delete t[e],Object.keys(t).length||null==n||n.disconnect())}))};Object.keys(t).length>0&&(n=new MutationObserver((()=>{e()})),n.observe(document,{childList:!0,subtree:!0})),e()})({"dxbl-grid":()=>jn.Grid.loadModule(),"dxbl-tree-list":()=>jn.TreeList.loadModule(),"dxbl-chart":()=>import("./chart-072aeaa9.js"),"dxbl-pie-chart":()=>import("./pie-chart-7fd0edce.js"),"dxbl-polar-chart":()=>import("./polar-chart-2e7c3a92.js"),"dxbl-map":()=>import("./dx-map-03e4a730.js"),"dxbl-html-editor":()=>import("./dx-html-editor-2c188713.js"),"dxbl-bar-gauge":()=>import("./dx-bar-gauge-da563340.js"),"dxbl-sankey":()=>import("./dx-sankey-b6251c4f.js"),"dxbl-sparkline":()=>import("./dx-sparkline-b9aca8ed.js"),"dxbl-carousel":()=>import("./dx-carousel-ac79e9ee.js"),"dxbl-range-selector":()=>import("./dx-range-selector-ee5ba2fb.js"),"dxbl-input-editor":()=>jn.Input.loadModule(),"dxbl-masked-input":()=>jn.MaskedInput.loadModule(),"dxbl-date-edit":()=>jn.DateEdit.loadModule(),"dxbl-date-range-picker":()=>jn.DateRangePicker.loadModule(),"dxbl-combobox":()=>jn.ComboBox.loadModule(),"dxbl-combo-box":()=>jn.ComboBox2.loadModule(),"dxbl-dropdown-box":()=>jn.DropDownBox.loadModule(),"dxbl-memo-editor":()=>jn.Memo.loadModule(),"dxbl-date-time-edit":()=>jn.DateTimeEdit.loadModule(),"dxbl-listbox":()=>jn.ListBoxLegacy.loadModule(),"dxbl-list-box":()=>jn.ListBox.loadModule(),"dxbl-tagbox":()=>jn.TagBox.loadModule(),"dxbl-tag-box":()=>jn.TagBox2.loadModule(),"dxbl-spinedit":()=>jn.SpinEdit.loadModule(),"dxbl-calendar":()=>jn.Calendar.loadModule(),"dxbl-check":()=>jn.CheckBox.loadModule(),"dxbl-color-palette":()=>jn.ColorPalette.loadModule(),"dxbl-dropdown":()=>import("./dropdown-f5b2318c.js"),"dxbl-flyout":()=>import("./flyout-6f5849c7.js"),"dxbl-modal":()=>import("./popup-355ecaa4.js").then((t=>t.L)),"dxbl-window":()=>import("./dxbl-window-89004b38.js"),"dxbl-popup-root":()=>import("./popup-355ecaa4.js").then((t=>t.K)),"dxbl-upload":()=>jn.Upload.loadModule(),"dxbl-file-input":()=>jn.FileInput.loadModule(),"dxbl-drawer":()=>import("./dx-drawer-e8714a47.js"),"dxbl-form-layout":()=>import("./dx-form-layout-2fff6968.js"),"dxbl-grid-layout":()=>import("./dx-grid-layout-bc194128.js"),"dxbl-splitter":()=>import("./dx-splitter-bb096bb4.js"),"dxbl-splitter-separator":()=>import("./dx-splitter-separator-a3fe93bf.js"),"dxbl-stack-layout":()=>import("./dx-stack-layout-4a3ce892.js"),"dxbl-tabs":()=>jn.Tabs.loadModule(),"dxbl-tab-list":()=>import("./dx-tab-list-702d1e0b.js"),"dxbl-tab-item":()=>import("./dx-tab-item-7f316c41.js"),"dxbl-menu":async()=>{await import("./dxbl-itemlistdropdown-75b612ca.js"),await jn.MenuComponent.loadModule()},"dxbl-menu-preloader":()=>import("./dx-menu-preloader-c40bcae2.js"),"dxbl-treeview":()=>import("./dx-tree-view-1fb293a4.js"),"dxbl-accordion":()=>import("./dx-accordion-b000d8bf.js"),"dxbl-context-menu":()=>jn.ContextMenu.loadModule(),"dxbl-pivot-table":()=>jn.PivotTable.loadModule(),"dxbl-scheduler":async()=>{await jn.Calendar.loadModule(),await jn.Scheduler.loadModule()},"dxbl-loading-panel":()=>import("./dx-loading-panel-130808b7.js"),"dxbl-pager":()=>jn.Pager.loadModule(),"dxbl-toast-portal":()=>import("./dx-toast-portal-f0e45140.js"),"dxbl-toast":()=>import("./dx-toast-9c01ccf3.js"),"dxbl-progress-bar":()=>import("./dx-progress-bar-fc2a6b94.js"),"dxbl-button":()=>import("./dx-button-9d1a9f04.js"),"dxbl-ui-handlers-bridge":()=>jn.UiHandlersBridge.loadModule(),"dxbl-custom-color-area":()=>jn.CustomColorArea.loadModule(),"dxbl-expandable-container":()=>jn.ExpandableContainer.loadModule(),"dxbl-group-control":()=>jn.GroupControl.loadModule(),"dx-license-trigger":()=>import("./dx-license-30fd02d1.js"),"dxbl-adaptive-dropdown":()=>jn.AdaptiveDropDown.init(),"dxbl-scroll-viewer":()=>jn.ScrollViewer.loadModule(),"dxbl-virtual-scroll-viewer":()=>jn.ScrollViewer.loadModule(),"dxbl-dynamic-stylesheet":()=>import("./dynamic-stylesheet-component-0ddead18.js"),"dxbl-itemlist-dropdown":()=>import("./dxbl-itemlistdropdown-75b612ca.js"),"dxbl-webview-sprite-preloader":()=>import("./webview-svg-loader-7cd1bef2.js"),"dxbl-adaptive-container":()=>import("./dx-adaptive-container-92815f61.js"),"dxbl-ribbon-item":()=>import("./ribbon-item-fb1bb1fe.js"),"dxbl-ribbon":()=>import("./dx-ribbon-d4d7213b.js")});export{jn as default};
