import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t}from"./data-qa-utils-8be7c726.js";import{d as n}from"./constants-a4904a3f.js";import{G as s}from"./const-90026e45.js";import{s as o}from"./lit-element-462e7ad3.js";import{n as r}from"./property-4ec0b52d.js";import{e as a}from"./custom-element-267f9a21.js";const i=new class{_doWithElementCheck(e){const t=document.querySelector(n);return e(null!=t?t:this._createPopupElement())}_createPopupElement(){const e=document.createElement(n);return document.body.appendChild(e),e}constructor(){this._rootElementPromise=customElements.whenDefined(n)}assign(e){this._rootElementPromise.then((()=>this._doWithElementCheck((t=>t.assign(e)))))}getPopup(e){return this._rootElementPromise.then((()=>this._doWithElementCheck((t=>t.getPopup(e)))))}release(e){this._rootElementPromise.then((()=>this._doWithElementCheck((t=>t.release(e)))))}async subscribeCaptureManager(e){return await this._rootElementPromise,this._doWithElementCheck((t=>t.subscribe(e)))}};function l(){return i}const d="dxbl-portal";class c{constructor(e){this.elements=e}}class h extends CustomEvent{constructor(e){super(h.eventName,{detail:e,bubbles:!0})}}h.eventName="dxbl-portable-elements-changed";let m=class extends o{constructor(){super(...arguments),this._portable=null,this._elementsPorted=!1,this.slotChangedHandler=this.handleSlotChange.bind(this),this.boundOnFocusInHandler=this.handleFocusIn.bind(this),this._reassignPortal=()=>l().assign(this)}get portable(){return this._portable}createRenderRoot(){const e=super.createRenderRoot(),t=document.createElement("slot");return e.appendChild(t),t.addEventListener("slotchange",this.slotChangedHandler),e}handleSlotChange(e){const t=e.target.assignedNodes();this._elementsPorted&&0===t.length||(this.assignElements(t),this._elementsPorted=!0)}assignElements(e){this._portable=e,this.raiseElementsChanged(e)}connectedCallback(){var e;super.connectedCallback(),t.setLoaded(this),l().assign(this),(null===(e=window.Blazor)||void 0===e?void 0:e.addEventListener)&&window.Blazor.addEventListener("enhancedload",this._reassignPortal),this.addEventListener("focusin",this.boundOnFocusInHandler,{capture:!0})}handleFocusIn(e){const t=document.querySelector(`:not(.dxbl-popup-portal)[branch-id=${this.branchId}]`);t&&t.activatePopupContent()}disconnectedCallback(){var e;super.disconnectedCallback(),l().release(this),t.removeLoaded(this),(null===(e=window.Blazor)||void 0===e?void 0:e.removeEventListener)&&window.Blazor.removeEventListener("enhancedload",this._reassignPortal),this.removeEventListener("focusin",this.boundOnFocusInHandler,{capture:!0})}raiseElementsChanged(e){const t=new h(new c(e));this.dispatchEvent(t)}updated(e){if(!this.branchId&&!this.hasAttribute(s.virtualElementAttributeName))throw new Error("branch-id must be specified")}};function u(){}e([r({type:String,attribute:"branch-id"})],m.prototype,"branchId",void 0),m=e([a(d)],m);const p=Object.freeze({__proto__:null,dxPortalTagName:d,PortableElementsChangedEvent:h,get DxPortal(){return m},init:u,default:{init:u,DxPortal:m,dxPortalTagName:d}});export{m as D,h as P,l as g,p};
