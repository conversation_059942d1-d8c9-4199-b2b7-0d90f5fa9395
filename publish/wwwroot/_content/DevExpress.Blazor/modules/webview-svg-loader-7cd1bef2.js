import{d as e}from"./define-custom-element-7c2e65e2.js";let t=!1;const n=["src"],a=document.createElement("DIV");a.style.display="none",requestAnimationFrame((()=>{document.body.appendChild(a)}));class s extends HTMLElement{connectedCallback(){this.style.display="none"}static get observedAttributes(){return n}attributeChangedCallback(e,n,a){t||(t=!0,"src"===e&&s.loadAndEmbedSpriteAsync(a).then((e=>{})).catch((e=>{})))}static async loadAndEmbedSpriteAsync(e){const t=await fetch(e).then((e=>e.text())),n=document.createElement("DIV");n.innerHTML=t,a.appendChild(n)}}e(customElements,"dxbl-webview-sprite-preloader",s);export{s as default};
