import{_ as t}from"./tslib.es6-d65164b3.js";import{DxDropDownBase as e}from"./dropdown-f5b2318c.js";import{o}from"./popup-355ecaa4.js";import{r}from"./dropdowncomponents-3d8f06da.js";import{n as s}from"./property-4ec0b52d.js";import{e as i}from"./custom-element-267f9a21.js";import"./thumb-31d768d7.js";import"./point-e4ec110e.js";import"./data-qa-utils-8be7c726.js";import"./eventhelper-8bcec49f.js";import"./browser-3fc721b7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./layouthelper-67dd777a.js";import"./constants-7c047c0d.js";import"./custom-events-helper-e7f279d3.js";import"./query-44b9267f.js";import"./lit-element-462e7ad3.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./logicaltreehelper-67db40f1.js";import"./portal-b3727c25.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./common-48ec40e2.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./key-ffa272aa.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./dom-utils-d057dcaa.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./popupportal-bbd2fea0.js";import"./events-interseptor-a522582a.js";var p;!function(t){t.Left="left",t.Right="right",t.FitPositionTarget="fitpositiontarget"}(p||(p={}));let m=class extends e{constructor(){super(...arguments),this.windowAlignment=p.Left}get dropFromRight(){return this.windowAlignment===p.Right}createKeyboardNavigationStrategy(){return new o(this.keyboardNavigator,this)}};t([s({type:String,attribute:"window-alignment"})],m.prototype,"windowAlignment",void 0),m=t([i("dxbl-itemlist-dropdown")],m);
