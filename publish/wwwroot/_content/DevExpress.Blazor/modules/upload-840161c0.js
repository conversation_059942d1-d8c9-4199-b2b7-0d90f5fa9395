import{i as t,g as e,U as s,F as o,a as i,M as a,C as l,W as d}from"./upload-base-993a2317.js";import"./tslib.es6-d65164b3.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./custom-events-helper-e7f279d3.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";import"./css-classes-c63af734.js";class r extends s{constructor(){super(),this.uploaders=new Map,this.uploadPromises=new Set,this.pendingFiles=new Array}processUploadedFilesOptionsCore(t){for(const e of t)e.status===o.PendingUpload&&e.uploadStartOptions&&this.onUploadStartCallback(e.uploadStartOptions,e.customFormDataOptions)}onUploadStartCallback(t,e){const{guid:s,requestJSOptions:o}=t,a=this.files.get(s);a&&(this.attachEventsToFileItem(a),o?this.uploadTechnology!==i.Http||o.uploadUrl?this.startUpload(a,o,(null==e?void 0:e.customData)||{}):a.error(new EventTarget):a.abort())}startUpload(t,e,s){t.status=o.PendingUpload,this.scheduleUpload(t,e,s)}scheduleUpload(t,e,s){if(this.uploadPromises.size<a){const o=this.uploadFileCore(t,e,s);this.uploadPromises.add(o),o.catch((e=>{var s;e&&e.status&&e.statusText&&e.responseText&&(null===(s=t.onError)||void 0===s||s.call(t,e))})).finally((()=>{this.onAfterUpload(o)}))}else this.pendingFiles.push({file:t,requestOptions:e,customData:s})}onAfterUpload(t){if(this.uploadPromises.delete(t),this.pendingFiles.length>0){const e=this.pendingFiles.shift(),s=this.uploadFileCore(e.file,e.requestOptions,e.customData);this.uploadPromises.add(t),s.finally((()=>{this.onAfterUpload(s)}))}}getUploadStrategy(t){const e=this.chunkSize>0?new l(this):new d(this),s=this.uploaders.get(t.fileInfo.guid);return(s!==e&&t.status!==o.Uploading||!s)&&this.uploaders.set(t.fileInfo.guid,e),this.uploaders.get(t.fileInfo.guid)}uploadFileCore(t,e,s){return this.getUploadStrategy(t).upload(t,e,s,(t=>(t.loadEnd(),t.isUploadComplete()||t.status!==o.Uploading?Promise.resolve():this.uploadFileCore(t,e,null))))}onCustomizeFormDataCallback(t){var e;const s=t.fileGuid;null===(e=this.uploaders.get(s))||void 0===e||e.onCustomizeChunkMetadataResponse(t.customData),this.uploaders.delete(s)}}customElements.define("dxbl-upload",r);const p={loadModule:function(){},initDotNetReference:t,getRecentlyAddedFileInfosStream:e};export{r as DxUpload,p as default};
