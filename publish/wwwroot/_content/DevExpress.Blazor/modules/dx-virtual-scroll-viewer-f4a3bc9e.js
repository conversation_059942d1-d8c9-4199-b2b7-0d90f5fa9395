import{ThumbDragStartedEvent as e,ThumbDragDeltaEvent as t,ThumbDragCompletedEvent as s}from"./thumb-31d768d7.js";import{D as i,R as n}from"./dx-scroll-viewer-da0fb41c.js";import{C as r}from"./css-classes-c63af734.js";import{C as o}from"./custom-events-helper-e7f279d3.js";import{b as a}from"./browser-3fc721b7.js";const h="dxbl-virtual-scroll-viewer",l="data-virtual-item-index",c="dxbl-top-virtual-spacer-element",m="dxbl-bottom-virtual-spacer-element",d="dxbl-skeleton-with-variants-data-type";class u extends CustomEvent{constructor(e){super(u.eventName,{detail:new b(e),bubbles:!0,composed:!0,cancelable:!0})}}u.eventName="dxbl-virtual-scroll-viewer.scroll";class b{constructor(e){this.Position=e}}class p extends CustomEvent{constructor(e){super(p.eventName,{detail:e,bubbles:!0,composed:!0,cancelable:!0})}}p.eventName="dxbl-virtual-scroll-viewer.scrollparamschanged";class g{constructor(e,t,s,i,n,r,o){this.AverageHeight=e,this.Heights=t,this.ViewportHeight=s,this.ContentHeightConstraint=i,this.SkeletonHeight=n,this.HeaderElementHeight=r,this.FooterElementHeight=o}equals(e){return!!e&&(e.AverageHeight===this.AverageHeight&&(e.ViewportHeight===this.ViewportHeight&&(e.ContentHeightConstraint===this.ContentHeightConstraint&&(!!this.sequenceEquals(e.Heights,this.Heights)&&e.SkeletonHeight===this.SkeletonHeight))))}sequenceEquals(e,t){if(!e)return!1;if(!t)return!1;if(e.length!==t.length)return!1;for(let s=0;s<e.length;s++)if(e[s]!==t[s])return!1;return!0}}class v extends CustomEvent{constructor(e,t,s){super(v.eventName,{detail:new C(e,t,s),bubbles:!0,composed:!0,cancelable:!0})}}v.eventName="dxbl-virtual-scroll-viewer.intersectioncallback";class C{constructor(e,t,s){this.Position=e,this.SpacerHeight=t,this.RenderStateHashCode=s}}o.register(u.eventName,(e=>e.detail)),o.register(p.eventName,(e=>e.detail)),o.register(v.eventName,(e=>e.detail));class E extends i{constructor(){super(),this.skeletonScrollContainers=new Set,this.positionUpdateLocked=!1,this._rootMargin="0px",this._renderStateHashCode=-1,this._scrollTop=-1,this._viewportHeight=0,this._isThumbDragging=!1,this._averageItemHeight=-1,this._contentHeightConstraint=-1,this._thumbMoveTimerId=-1,this._scrollParams=null,this._renderTypeChanged=!1,this._requireInitialize=!1,this._totalItemCount=0,this._skeletonViewportItemsCount=0,this._skeletonPatternContainerClasses=null,this._skeletonHeight=0,this._headerElementHeight=0,this._footerElementHeight=0,this._lastExpectedTopVisibleIndex=0,this.pendingMakeElementVisibleParams=null,this.boundOnThumbMoveStarted=this.onThumbMoveStarted.bind(this),this.boundOnThumbMove=this.onThumbMove.bind(this),this.boundOnThumbMoveCompleted=this.onThumbMoveCompleted.bind(this),this.visibleElementMutationObserver=new MutationObserver(this.visibleElementMutationCallback.bind(this))}initializeComponent(){super.initializeComponent(),this.subscribeScrollBarEvents(),this.initialize()&&this.dispatchScrollParams()}disposeComponent(){var e,t,s;super.disposeComponent(),this.unsubscribeScrollBarEvents(),this.reset(),null===(e=this.spacerIntersectionObserver)||void 0===e||e.disconnect(),null===(t=this.spacerResizeObserver)||void 0===t||t.disconnect(),null===(s=this.contentContainerResizeObserver)||void 0===s||s.disconnect(),this.visibleElementMutationObserver.disconnect()}initialize(){if(this.contentContainer=this.getContentContainerElement(),this.itemsContainer=this.getItemsContainerElement(),this.spacerTop=this.getTopSpacer(),this.spacerBottom=this.getBottomSpacer(),this.headerElementRectangle=this.getElementsRectangle(this.getHeaderElements()),this.footerElementRectangle=this.getElementsRectangle(this.getFooterElements()),this._headerElementHeight=0,this._footerElementHeight=0,this._averageItemHeight=-1,this.headerElementRectangle&&(this._headerElementHeight=E.roundHeight(this.headerElementRectangle.height)),this.footerElementRectangle&&(this._footerElementHeight=E.roundHeight(this.footerElementRectangle.height)),this.contentContainer&&this.spacerTop&&this.spacerBottom){this._viewportHeight=this.contentContainer.getBoundingClientRect().height,this.contentContainerResizeObserver&&(this.contentContainerResizeObserver.disconnect(),this.contentContainerResizeObserver=void 0),this.contentContainerResizeObserver=this.createContentContainerResizeObserver(this.contentContainer),this.spacerIntersectionObserver&&(this.spacerIntersectionObserver.disconnect(),this.spacerIntersectionObserver=void 0);const e={root:this.contentContainer,rootMargin:this._rootMargin};return this.spacerIntersectionObserver=new IntersectionObserver(this.intersectionCallback.bind(this),e),this.spacerIntersectionObserver.observe(this.spacerTop),this.spacerIntersectionObserver.observe(this.spacerBottom),this.spacerResizeObserver&&(this.spacerResizeObserver.disconnect(),this.spacerResizeObserver=void 0),this.spacerResizeObserver=new ResizeObserver(this.resizeObserverCallback.bind(this)),this.spacerResizeObserver.observe(this.spacerTop),this.spacerResizeObserver.observe(this.spacerBottom),!0}return!1}get averageHeight(){return this._averageItemHeight}get skeletonHeight(){return this._skeletonHeight}get viewportHeight(){return this._viewportHeight}get totalItemCount(){return this._totalItemCount}getItemsContainerElement(){return this.querySelector("[data-virtual-items-container]")}getTopSpacer(){return this.querySelector(`[${c}]`)}getBottomSpacer(){return this.querySelector(`[${m}]`)}findContentHeightConstraint(){if(this._contentHeightConstraint<0){const e=document.createElement("div");e.setAttribute("style","overflow: auto; height: 0; visibility: hidden;");const t=document.createElement("div");t.setAttribute("style","position: relative;"),e.appendChild(t),document.body.appendChild(e),this._contentHeightConstraint=this.findElementMaximumHeight(t)/2,document.body.removeChild(e)}return this._contentHeightConstraint}queryElementsSizeArray(){const e=new Array;if(this.itemsContainer){let t=0;const s=this.groupElements(this.itemsContainer.children);let i=0,n=0;for(const[r,o]of s){let a=0;for(let e=0;e<o.length;e++)a+=this.queryElementHeight(o[e]);e.push(r),e.push(a),0!==i&&i!==s.size-1&&(t+=a,n++),i++}this._averageItemHeight<0&&n>0&&t>0&&(this._averageItemHeight=E.roundHeight(t/n))}return e}queryElementHeight(e){if(!e)return 0;const t=e.getBoundingClientRect().height;return E.roundHeight(t)}findElementMaximumHeight(e){let t=1e6;const s=a.Browser.Firefox?8e6:Number.MAX_VALUE;for(;;){const i=2*t;e.style.height=`${i}px`;const n=e.getBoundingClientRect().height;if(i>s||n!==i)break;t=i}return t}groupElements(e){const t=new Map;let s=[];for(let i=0;i<e.length;i++){const n=e[i],r=n.getAttribute(l);this.skipElement(r)||(r&&(s=[],t.set(parseInt(r),s)),s&&s.push(n))}return t}canDispatchPositionUpdate(){return!this._isThumbDragging&&!this.positionUpdateLocked}skipElement(e){return!!e&&parseInt(e)<0}clearSelection(){if(null!==document.activeElement&&("INPUT"===document.activeElement.tagName||"TEXTAREA"===document.activeElement.tagName))return;const e=window.getSelection?window.getSelection():document.getSelection();e&&e.rangeCount>0&&!e.isCollapsed&&e.collapseToStart()}onRefresh(e,t){super.onRefresh(e,t);const s=Math.ceil(e);Math.abs(this._scrollTop-s)>1&&(this.dispatchScrollEvent(s),this._scrollTop=s,this.clearSelection(),this.shiftSkeletonContainers())}intersectionCallback(e){for(let t=0;t<e.length;t++){const s=e[t];if(s.isIntersecting&&this.canDispatchPositionUpdate()){if(s.target===this.spacerTop){this.dispatchEvent(new v(0,s.intersectionRect.top-s.boundingClientRect.top,this._renderStateHashCode)),this.clearSelection()}if(s.target===this.spacerBottom){this.dispatchEvent(new v(1,s.boundingClientRect.bottom-s.intersectionRect.bottom,this._renderStateHashCode)),this.clearSelection()}}}}resizeObserverCallback(e){for(let t=0;t<e.length;t++){const s=e[t].target;s.hasAttribute(c)&&this.reconnectIntersectionObserver(this.spacerTop),s.hasAttribute(m)&&this.reconnectIntersectionObserver(this.spacerBottom)}}reconnectIntersectionObserver(e){e&&this.spacerIntersectionObserver&&(this.spacerIntersectionObserver.unobserve(e),this.spacerIntersectionObserver.observe(e))}reconnectSpacerContentIntersectionObserver(){if(this._spacerContentIntersectionObserver&&(this._spacerContentIntersectionObserver.disconnect(),this._spacerContentIntersectionObserver=void 0,this.skeletonScrollContainers=new Set),!this.contentContainer||!this.itemsContainer)return;const e=this.itemsContainer.getBoundingClientRect().width,t=Math.max(0,this._skeletonHeight-this._headerElementHeight)+1,s=Math.max(0,this._skeletonViewportItemsCount*this._skeletonHeight+this._headerElementHeight-this._viewportHeight)+1,i={root:this.contentContainer,rootMargin:`${t}px ${e}px ${s}px ${e}px`,threshold:.999};this._spacerContentIntersectionObserver=new IntersectionObserver(this.intersectionSkeletonsCallback.bind(this),i),this.observeSkeletonContainers(this.spacerTop),this.observeSkeletonContainers(this.spacerBottom)}observeSkeletonContainers(e){if(e&&this._spacerContentIntersectionObserver){const t=e.querySelectorAll("[dxbl-skeleton-items-container]");for(const e of t)this._spacerContentIntersectionObserver.observe(e)}}lockSpacersIntersection(){this.positionUpdateLocked=!0}unlockSpacersIntersection(){this.positionUpdateLocked=!1,this.reconnectIntersectionObserver(this.spacerTop),this.reconnectIntersectionObserver(this.spacerBottom)}scheduleMakeElementVisible(e,t,s){this.lockSpacersIntersection(),super.scheduleMakeElementVisible(e,t,s)}makeElementVisible(e,t,s){super.makeElementVisible(e,t,s),this.unlockSpacersIntersection()}makeUnrenderedElementVisible(e,t,s){super.makeUnrenderedElementVisible(e,t,s),this.pendingMakeElementVisibleParams={elementSelector:e,alignVert:t,alignHor:s}}createContentContainerResizeObserver(e){const t=new ResizeObserver((e=>{this._viewportHeight=e[0].contentRect.height,this.dispatchScrollParams(),this.dispatchScrollEvent(this._scrollTop)}));return t.observe(e),t}subscribeScrollBarEvents(){const e=this.getVerticalScrollBarElement();e&&this.subscribeThumbEvents(e,this.boundOnThumbMoveStarted,this.boundOnThumbMove,this.boundOnThumbMoveCompleted)}unsubscribeScrollBarEvents(){const e=this.getVerticalScrollBarElement();e&&this.unsubscribeThumbEvents(e,this.boundOnThumbMoveStarted,this.boundOnThumbMove,this.boundOnThumbMoveCompleted)}subscribeThumbEvents(i,n,r,o){i.addEventListener(e.eventName,n),i.addEventListener(t.eventName,r),i.addEventListener(s.eventName,o)}unsubscribeThumbEvents(i,n,r,o){i.removeEventListener(e.eventName,n),i.removeEventListener(t.eventName,r),i.removeEventListener(s.eventName,o)}onThumbMoveStarted(e){this._isThumbDragging=!0}onThumbMove(e){-1!==this._thumbMoveTimerId&&clearTimeout(this._thumbMoveTimerId);this._thumbMoveTimerId=setTimeout((()=>{this.dispatchPositionUpdate()}).bind(this),700),this._isThumbDragging=!0}onThumbMoveCompleted(e){this.dispatchPositionUpdate()}dispatchPositionUpdate(){this._isThumbDragging=!1,clearTimeout(this._thumbMoveTimerId),this._thumbMoveTimerId=-1,this.dispatchScrollEvent(this._scrollTop),this.reconnectIntersectionObserver(this.spacerTop),this.reconnectIntersectionObserver(this.spacerBottom)}dispatchScrollEvent(e){this.canDispatchPositionUpdate()&&this.dispatchEvent(new u(e))}dispatchScrollParams(){const e=this.queryElementsSizeArray(),t=this.findContentHeightConstraint(),s=new g(this._averageItemHeight,e,this._viewportHeight,t,this._skeletonHeight,this._headerElementHeight,this._footerElementHeight);s.equals(this._scrollParams)||(this._scrollParams=s,this.dispatchEvent(new p(this._scrollParams)))}onStateChanged(e){e&&(this._renderTypeChanged&&(this._requireInitialize&&this.initialize(),this.calculateSkeletonHeight(),this._renderTypeChanged=!1),this.reconnectSpacerContentIntersectionObserver(),this.rerunSkeletonAnimation(),this.dispatchScrollParams())}rerunSkeletonAnimation(){this.classList.remove(r.SkeletonAnimateClassName),setTimeout((()=>this.classList.add(r.SkeletonAnimateClassName)))}onRenderStateChanged(e){e&&(this._renderStateHashCode=parseInt(e))}reset(){this._scrollParams=null}calculateSkeletonHeight(){if(!this._skeletonPatternContainerClasses)return;const e=document.createElement("div");e.innerHTML="&nbsp;";const t=document.createElement("div");t.appendChild(e);const s=document.createElement("div");s.setAttribute("class",`${r.VirtualSpacerPatternClassName} ${this._skeletonPatternContainerClasses}`),s.appendChild(t),document.body.appendChild(s),this._skeletonHeight=E.roundHeight(t.getBoundingClientRect().height),document.body.removeChild(s)}updateSkeletonClasses(e,t){if(e.children.length>0){const s=t-this.getSkeletonItemIndex(e.children[0]);for(const t of e.children){const e=this.getSkeletonItemIndex(t)+s,i=t.querySelectorAll(`[${d}]`);for(const t of i){const s=t.getAttribute(d);t.setAttribute("class",`${r.SkeletonBaseClassName}-${s}-${e%5}`)}}}}shiftSkeletonContainers(){if(!this.itemsContainer)return;const e=this._scrollTop,t=this._headerElementHeight-e%this._skeletonHeight;let s=Math.floor(e/this._skeletonHeight);const i=Math.min(this._totalItemCount-this._skeletonViewportItemsCount,this._totalItemCount-1);this._averageItemHeight===this._skeletonHeight&&i>0&&(s=Math.min(s,i));for(const e of this.skeletonScrollContainers)e.setAttribute("style",`top: ${t}px`),this._lastExpectedTopVisibleIndex!==s&&this.updateSkeletonClasses(e,s);this._lastExpectedTopVisibleIndex=s}getSkeletonItemIndex(e){if(e){const t=e.getAttribute(l);if(t)return parseInt(t)}return-1}intersectionSkeletonsCallback(e){for(const t of e)t.isIntersecting?this.skeletonScrollContainers.add(t.target):this.skeletonScrollContainers.delete(t.target)}static roundHeight(e){return isNaN(e)?0:parseFloat(e.toFixed(2))}onSkeletonPatternContainerClassesChanged(e){this._requireInitialize=null!==this._skeletonPatternContainerClasses,this._skeletonPatternContainerClasses!==e&&(this._skeletonPatternContainerClasses=e,this._renderTypeChanged=!0)}onSkeletonViewportItemsCountChanged(e){this._requireInitialize=0!==this._skeletonViewportItemsCount;const t=parseInt(e);this._skeletonViewportItemsCount!==t&&(this._skeletonViewportItemsCount=t,this._renderTypeChanged=!0)}reconnectVisibleElementMutationObserver(){this.visibleElementMutationObserver.disconnect(),this.visibleElementMutationObserver.observe(this,{attributes:!0,attributeFilter:[n],attributeOldValue:!0,subtree:!0})}cancelRequestMakeElementVisible(){super.cancelRequestMakeElementVisible(),this.visibleElementMutationObserver.disconnect()}execRequestMakeElementVisible(e,t,s){this.reconnectVisibleElementMutationObserver(),super.execRequestMakeElementVisible(e,t,s)}visibleElementMutationCallback(e,t){if(this.pendingMakeElementVisibleParams){for(const t of e)if(t.attributeName&&t.target){const e=t.target.getAttribute(t.attributeName);if(e){const t=this.pendingMakeElementVisibleParams.elementSelector,s=this.pendingMakeElementVisibleParams.alignVert,i=this.pendingMakeElementVisibleParams.alignHor;if(t&&-1!==t.indexOf(e)){this.makeElementVisible(t,s,i);break}}}this.pendingMakeElementVisibleParams=null}}static get observedAttributes(){return["reset-v-scroll-guid","reset-h-scroll-guid","request-make-element-visible","state-has-changed","render-state-changed","header-selector","footer-selector","left-selector","right-selector","skeleton-pattern-container-classes","total-item-count","skeleton-viewport-items-count"]}attributeChangedCallback(e,t,s){switch(super.attributeChangedCallback(e,t,s),e){case"state-has-changed":setTimeout((()=>this.onStateChanged(s)));break;case"render-state-changed":this.onRenderStateChanged(s);break;case"total-item-count":this._totalItemCount=parseInt(s);break;case"skeleton-pattern-container-classes":this.onSkeletonPatternContainerClassesChanged(s);break;case"skeleton-viewport-items-count":this.onSkeletonViewportItemsCountChanged(s)}}}customElements.define(h,E);export{m as B,h as D,u as S,c as T,l as V,E as a};
