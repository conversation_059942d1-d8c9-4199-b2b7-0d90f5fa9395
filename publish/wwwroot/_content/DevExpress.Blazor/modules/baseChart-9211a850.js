import{e as A,i as e,t}from"./dom-utils-d057dcaa.js";import{d as r}from"./dom-554d0cc7.js";import{r as n,d as B}from"./disposable-d2c2d283.js";import{b as s,c as o,d as i,e as c,C as a,f as g,g as Q}from"./settings-c0192d16.js";import{W as w,D as l}from"./devextreme-widget-wrapper-33881f73.js";import{c as u}from"./create-after-timeout-fn-executor-38b3d79d.js";import{C}from"./client-component-style-helper-195fa7c3.js";import{registerTrialPanelComponents as U}from"./dx-license-30fd02d1.js";import{C as F}from"./custom-events-helper-e7f279d3.js";import{C as h}from"./events-a8fe5872.js";const d="dxbl-chart-legend-listeners";var f;function p(A,e,t,n,B,s){if(!A)return;const c=H(e,t,n);if(!c)return;const a=A.querySelector("."+o+" rect");if(a){const A=c.getColor();r.DomUtils.hasClassName(a,i)&&(a.style.fill=A),a.style.color=A}A.getAttribute(d)||(E(A,e,t,n,["pointerover"],(function(A){const t=e.option("legend.hoverMode");B===f.series?A.hover(t):"includePoints"===t&&A.hover()})),E(A,e,t,n,["pointerout"],(function(A){A.clearHover()})),E(A,e,t,n,["click"],(function(A){s.selectLegendItem(A,B)})),A.setAttribute(d,"true"))}function H(A,e,t){const r=A.getSeriesByPos(e);return t&&r?r.getPoints().filter((function(A){return A.tag===t}))[0]:r}function E(A,e,t,r,B,s){B.forEach((function(B){const o=function(){const A=H(e,t,r);A&&s(A)};A.addEventListener(B,o),n(A,(()=>{A.removeEventListener(B,o)}))}))}!function(A){A[A.series=0]="series",A[A.point=1]="point"}(f||(f={}));class I{}I.pieChartName="dxPieChart",I.polarChartName="dxPolarChart",I.xyChartName="dxChart";class y{static compareValues(A,e){return A instanceof Date&&e instanceof Date?A.getTime()===e.getTime():A===e}get seriesIndex(){return this._seriesIndex}get pointArgument(){return this._pointArgument}get pointValue(){return this._pointValue}constructor(A,e,t){this._seriesIndex=A,this._pointArgument=e,this._pointValue=t}}class m{get seriesIndex(){return this._seriesIndex}constructor(A){this._seriesIndex=A}}function K(A){return A===I.pieChartName?new L:new v}class v{comparePoint(A,e){return A.seriesIndex===e.seriesIndex&&y.compareValues(A.pointArgument,e.pointArgument)&&y.compareValues(A.pointValue,e.pointValue)}compareSeries(A,e){return A.seriesIndex===e.seriesIndex}}class L extends v{comparePoint(A,e){return A.seriesIndex===e.seriesIndex&&y.compareValues(A.pointArgument,e.pointArgument)}}class b{constructor(){this._seriesSelectionMode=null,this._pointSelectionMode=null,this._selectedSeries=new Array,this._selectedPoints=new Array,this._hoveredPoint=null,this._hoveredSeries=null,this._compareStrategy=K()}get seriesSelectionMode(){return this._seriesSelectionMode}set seriesSelectionMode(A){this._seriesSelectionMode=A}get pointSelectionMode(){return this._pointSelectionMode}set pointSelectionMode(A){this._pointSelectionMode=A}setCompareStrategy(A){this._compareStrategy=A}changePointSelectionState(A,e){const t=this._compareStrategy.comparePoint,r=this._selectedPoints.findIndex((e=>t(e,A))),n=r>-1;e?n||this.appendPoint(A):n&&this._selectedPoints.splice(r,1)}changeSeriesSelectionState(A,e){const t=this._compareStrategy.compareSeries,r=this._selectedSeries.findIndex((e=>t(e,A))),n=r>-1;e?n||this.appendSeries(A):n&&this._selectedSeries.splice(r,1)}changePointHoverState(A,e){this._hoveredPoint=e?A:null}changeSeriesHoverState(A,e){this._hoveredSeries=e?A:null}restoreSelection(A){this._selectedPoints=this._selectedPoints.filter(A.selectPoint),this._selectedSeries=this._selectedSeries.filter(A.selectSeries),null===this._hoveredPoint||A.hoverPoint(this._hoveredPoint)||(this._hoveredPoint=null),null===this._hoveredSeries||A.hoverSeries(this._hoveredSeries)||(this._hoveredSeries=null)}reset(){this.resetSelectedPoints(),this.resetSelectedSeries(),this._hoveredPoint=null,this._hoveredSeries=null}isPointSingleSelectionMode(){return this.pointSelectionMode===b.singleSelectionModeKey}isSeriesSingleSelectionMode(){return this.seriesSelectionMode===b.singleSelectionModeKey}appendPoint(A){this.isPointSingleSelectionMode()&&this.resetSelectedPoints(),this._selectedPoints.push(A)}appendSeries(A){this.isSeriesSingleSelectionMode()&&this.resetSelectedSeries(),this._selectedSeries.push(A)}resetSelectedPoints(){this._selectedPoints=[]}resetSelectedSeries(){this._selectedSeries=[]}}b.singleSelectionModeKey="single";class D{constructor(A){this._eventsLocked=!1,this._disposed=!1,this._lastJob=Promise.resolve(),this._state=new b,this._owner=A}setSeriesSelectionMode(A){this._state.seriesSelectionMode=A}setPointSelectionMode(A){this._state.pointSelectionMode=A}setChartType(A){const e=K(A);this._state.setCompareStrategy(e)}dispose(){this._lastJob=this._lastJob.catch((A=>{A.message!==D.disposedErrorKey&&console.error(A)})),this._disposed=!0}selectSeriesAndPoint(A,e){this.pointSelectionEnabled&&e?this.togglePointSelection(e):this.toggleSeriesSelection(A)}selectLegendItem(A,e){e===f.series?this.toggleSeriesSelection(A):this.togglePointSelection(A)}togglePointSelection(A){this.pointSelectionEnabled&&A&&this.addJobToQueue((()=>this.selectPointInternal(A)))}toggleSeriesSelection(A){this.seriesSelectionEnabled&&A&&this.addJobToQueue((()=>this.selectSeriesInternal(A)))}restoreChartSelection(A){this.addJobToQueue((()=>this.restoreChartSelectionInternal(A)))}seriesSelectionChanged(A){if(!this.eventsAreLocked()){const e=A.target;e&&this.onSelectionChanged(e.index,e.isSelected())}}pointSelectionChanged(A){if(!this.eventsAreLocked()){const e=A.target;e&&this.onSelectionChanged(e.series.index,e.series.isSelected(),e.data,e.tag,e.isSelected())}}pointHoverChanged(A){if(!this.eventsAreLocked()){const e=A.target;if(e&&void 0!==e.originalArgument&&void 0!==e.originalValue){const A=new y(e.series.index,e.originalArgument,e.originalValue);this._state.changePointHoverState(A,e.isHovered())}}}seriesHoverChanged(A){if(!this.eventsAreLocked()){const e=A.target;if(e){const A=new m(e.index);this._state.changeSeriesHoverState(A,e.isHovered())}}}resetSelection(){this._state.reset()}onSelectionChanged(A,e,t=null,r=null,n=!1){this._owner.onSelectionChanged({seriesIndex:A,isSeriesSelected:e,data:t,tag:r,isPointSelected:n})}addJobToQueue(A){return this.disposed||(this._lastJob=this._lastJob.then((()=>{if(this.disposed)throw Error(D.disposedErrorKey);A()}))),this._lastJob}get disposed(){return this._disposed}get seriesSelectionEnabled(){return null!=this._state.seriesSelectionMode}get pointSelectionEnabled(){return null!=this._state.pointSelectionMode}eventsAreLocked(){return this._eventsLocked}lockEventsAction(A){this.eventsAreLocked()||(this._eventsLocked=!0,A(),this._eventsLocked=!1)}selectSeriesInternal(A){const e=new m(A.index),t=!A.isSelected();this._state.changeSeriesSelectionState(e,t),t?A.select():A.clearSelection()}selectPointInternal(A){if(void 0===A.originalArgument||void 0===A.originalValue)return;const e=new y(A.series.index,A.originalArgument,A.originalValue),t=!A.isSelected();this._state.changePointSelectionState(e,t),t?A.select():A.clearSelection()}restoreChartSelectionInternal(A){this.lockEventsAction((()=>{const e=this.getSelectedStateRestoreActions(A);this._state.restoreSelection(e)}))}getSelectedStateRestoreActions(A){const e=e=>{const t=A.getSeriesByPos(e.seriesIndex);return(null==t?void 0:t.getPointsByArg(e.pointArgument))||[]},t=e=>A.getSeriesByPos(e.seriesIndex);return{selectPoint(A){const t=e(A);return t.forEach((A=>A.select())),(null==t?void 0:t.length)>0},selectSeries(A){const e=t(A);return null==e||e.select(),!!e},hoverPoint(A){var t;const r=null===(t=e(A))||void 0===t?void 0:t[0];return null==r||r.hover(),!!r},hoverSeries(A){const e=t(A);return null==e||e.hover(),!!e}}}}D.disposedErrorKey="Disposed";var S=function(A,e){return S=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(A,e){A.__proto__=e}||function(A,e){for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&(A[t]=e[t])},S(A,e)};function x(A,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function t(){this.constructor=A}S(A,e),A.prototype=null===e?Object.create(e):(t.prototype=e.prototype,new t)}var M=function(){return M=Object.assign||function(A){for(var e,t=1,r=arguments.length;t<r;t++)for(var n in e=arguments[t])Object.prototype.hasOwnProperty.call(e,n)&&(A[n]=e[n]);return A},M.apply(this,arguments)};function T(A,e,t,r){return new(t||(t=Promise))((function(n,B){function s(A){try{i(r.next(A))}catch(A){B(A)}}function o(A){try{i(r.throw(A))}catch(A){B(A)}}function i(A){var e;A.done?n(A.value):(e=A.value,e instanceof t?e:new t((function(A){A(e)}))).then(s,o)}i((r=r.apply(A,e||[])).next())}))}function O(A,e){var t,r,n,B,s={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return B={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(B[Symbol.iterator]=function(){return this}),B;function o(B){return function(o){return function(B){if(t)throw new TypeError("Generator is already executing.");for(;s;)try{if(t=1,r&&(n=2&B[0]?r.return:B[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,B[1])).done)return n;switch(r=0,n&&(B=[2&B[0],n.value]),B[0]){case 0:case 1:n=B;break;case 4:return s.label++,{value:B[1],done:!1};case 5:s.label++,r=B[1],B=[0];continue;case 7:B=s.ops.pop(),s.trys.pop();continue;default:if(!(n=s.trys,(n=n.length>0&&n[n.length-1])||6!==B[0]&&2!==B[0])){s=0;continue}if(3===B[0]&&(!n||B[1]>n[0]&&B[1]<n[3])){s.label=B[1];break}if(6===B[0]&&s.label<n[1]){s.label=n[1],n=B;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(B);break}n[2]&&s.ops.pop(),s.trys.pop();continue}B=e.call(A,s)}catch(A){B=[6,A],r=0}finally{t=n=0}if(5&B[0])throw B[1];return{value:B[0]?B[1]:void 0,done:!0}}([B,o])}}}function G(A,e,t){if(t||2===arguments.length)for(var r,n=0,B=e.length;n<B;n++)!r&&n in e||(r||(r=Array.prototype.slice.call(e,0,n)),r[n]=e[n]);return A.concat(r||e)}for(var V=function(){function A(A,e,t,r){this.left=A,this.top=e,this.width=t,this.height=r}return A.prototype.add=function(e,t,r,n){return new A(this.left+e,this.top+t,this.width+r,this.height+n)},A.fromClientRect=function(e,t){return new A(t.left+e.windowBounds.left,t.top+e.windowBounds.top,t.width,t.height)},A.fromDOMRectList=function(e,t){var r=Array.from(t).find((function(A){return 0!==A.width}));return r?new A(r.left+e.windowBounds.left,r.top+e.windowBounds.top,r.width,r.height):A.EMPTY},A.EMPTY=new A(0,0,0,0),A}(),P=function(A,e){return V.fromClientRect(A,e.getBoundingClientRect())},k=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var B=A.charCodeAt(t++);56320==(64512&B)?e.push(((1023&n)<<10)+(1023&B)+65536):(e.push(n),t--)}else e.push(n)}return e},R=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,B="";++n<t;){var s=A[n];s<=65535?r.push(s):r.push(55296+((s-=65536)>>10),s%1024+56320),(n+1===t||r.length>16384)&&(B+=String.fromCharCode.apply(String,r),r.length=0)}return B},N="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",J="undefined"==typeof Uint8Array?[]:new Uint8Array(256),X=0;X<64;X++)J[N.charCodeAt(X)]=X;for(var Y="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",W="undefined"==typeof Uint8Array?[]:new Uint8Array(256),_=0;_<64;_++)W[Y.charCodeAt(_)]=_;for(var Z=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},q=function(){function A(A,e,t,r,n,B){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=B}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return this.data[e=((e=this.index[A>>5])<<2)+(31&A)];if(A<=65535)return this.data[e=((e=this.index[2048+(A-55296>>5)])<<2)+(31&A)];if(A<this.highStart)return e=this.index[e=2080+(A>>11)],this.data[e=((e=this.index[e+=A>>5&63])<<2)+(31&A)];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),j="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",z="undefined"==typeof Uint8Array?[]:new Uint8Array(256),$=0;$<64;$++)z[j.charCodeAt($)]=$;var AA=10,eA=13,tA=15,rA=17,nA=18,BA=19,sA=20,oA=21,iA=22,cA=24,aA=25,gA=26,QA=27,wA=28,lA=30,uA=32,CA=33,UA=34,FA=35,hA=37,dA=38,fA=39,pA=40,HA=42,EA=[9001,65288],IA="×",yA="÷",mA=function(A,e){var t,r,n,B=function(A){var e,t,r,n,B,s=.75*A.length,o=A.length,i=0;"="===A[A.length-1]&&(s--,"="===A[A.length-2]&&s--);var c="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(s):new Array(s),a=Array.isArray(c)?c:new Uint8Array(c);for(e=0;e<o;e+=4)t=W[A.charCodeAt(e)],r=W[A.charCodeAt(e+1)],n=W[A.charCodeAt(e+2)],B=W[A.charCodeAt(e+3)],a[i++]=t<<2|r>>4,a[i++]=(15&r)<<4|n>>2,a[i++]=(3&n)<<6|63&B;return c}(A),s=Array.isArray(B)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(B):new Uint32Array(B),o=Array.isArray(B)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(B):new Uint16Array(B),i=Z(o,12,s[4]/2),c=2===s[5]?Z(o,(24+s[4])/2):(t=s,r=Math.ceil((24+s[4])/4),t.slice?t.slice(r,n):new Uint32Array(Array.prototype.slice.call(t,r,n)));return new q(s[0],s[1],s[2],s[3],i,c)}("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"),KA=[lA,36],vA=[1,2,3,5],LA=[AA,8],bA=[QA,gA],DA=vA.concat(LA),SA=[dA,fA,pA,UA,FA],xA=[tA,eA],MA=function(A,e,t,r){var n=r[t];if(Array.isArray(A)?-1!==A.indexOf(n):A===n)for(var B=t;B<=r.length;){if((i=r[++B])===e)return!0;if(i!==AA)break}if(n===AA)for(B=t;B>0;){var s=r[--B];if(Array.isArray(A)?-1!==A.indexOf(s):A===s)for(var o=t;o<=r.length;){var i;if((i=r[++o])===e)return!0;if(i!==AA)break}if(s!==AA)break}return!1},TA=function(A,e){for(var t=A;t>=0;){var r=e[t];if(r!==AA)return r;t--}return 0},OA=function(A,e,t,r,n){if(0===t[r])return IA;var B=r-1;if(Array.isArray(n)&&!0===n[B])return IA;var s=B-1,o=B+1,i=e[B],c=s>=0?e[s]:0,a=e[o];if(2===i&&3===a)return IA;if(-1!==vA.indexOf(i))return"!";if(-1!==vA.indexOf(a))return IA;if(-1!==LA.indexOf(a))return IA;if(8===TA(B,e))return yA;if(11===mA.get(A[B]))return IA;if((i===uA||i===CA)&&11===mA.get(A[o]))return IA;if(7===i||7===a)return IA;if(9===i)return IA;if(-1===[AA,eA,tA].indexOf(i)&&9===a)return IA;if(-1!==[rA,nA,BA,cA,wA].indexOf(a))return IA;if(TA(B,e)===iA)return IA;if(MA(23,iA,B,e))return IA;if(MA([rA,nA],oA,B,e))return IA;if(MA(12,12,B,e))return IA;if(i===AA)return yA;if(23===i||23===a)return IA;if(16===a||16===i)return yA;if(-1!==[eA,tA,oA].indexOf(a)||14===i)return IA;if(36===c&&-1!==xA.indexOf(i))return IA;if(i===wA&&36===a)return IA;if(a===sA)return IA;if(-1!==KA.indexOf(a)&&i===aA||-1!==KA.indexOf(i)&&a===aA)return IA;if(i===QA&&-1!==[hA,uA,CA].indexOf(a)||-1!==[hA,uA,CA].indexOf(i)&&a===gA)return IA;if(-1!==KA.indexOf(i)&&-1!==bA.indexOf(a)||-1!==bA.indexOf(i)&&-1!==KA.indexOf(a))return IA;if(-1!==[QA,gA].indexOf(i)&&(a===aA||-1!==[iA,tA].indexOf(a)&&e[o+1]===aA)||-1!==[iA,tA].indexOf(i)&&a===aA||i===aA&&-1!==[aA,wA,cA].indexOf(a))return IA;if(-1!==[aA,wA,cA,rA,nA].indexOf(a))for(var g=B;g>=0;){if((Q=e[g])===aA)return IA;if(-1===[wA,cA].indexOf(Q))break;g--}if(-1!==[QA,gA].indexOf(a))for(g=-1!==[rA,nA].indexOf(i)?s:B;g>=0;){var Q;if((Q=e[g])===aA)return IA;if(-1===[wA,cA].indexOf(Q))break;g--}if(dA===i&&-1!==[dA,fA,UA,FA].indexOf(a)||-1!==[fA,UA].indexOf(i)&&-1!==[fA,pA].indexOf(a)||-1!==[pA,FA].indexOf(i)&&a===pA)return IA;if(-1!==SA.indexOf(i)&&-1!==[sA,gA].indexOf(a)||-1!==SA.indexOf(a)&&i===QA)return IA;if(-1!==KA.indexOf(i)&&-1!==KA.indexOf(a))return IA;if(i===cA&&-1!==KA.indexOf(a))return IA;if(-1!==KA.concat(aA).indexOf(i)&&a===iA&&-1===EA.indexOf(A[o])||-1!==KA.concat(aA).indexOf(a)&&i===nA)return IA;if(41===i&&41===a){for(var w=t[B],l=1;w>0&&41===e[--w];)l++;if(l%2!=0)return IA}return i===uA&&a===CA?IA:yA},GA=function(A,e){e||(e={lineBreak:"normal",wordBreak:"normal"});var t=function(A,e){void 0===e&&(e="strict");var t=[],r=[],n=[];return A.forEach((function(A,B){var s=mA.get(A);if(s>50?(n.push(!0),s-=50):n.push(!1),-1!==["normal","auto","loose"].indexOf(e)&&-1!==[8208,8211,12316,12448].indexOf(A))return r.push(B),t.push(16);if(4===s||11===s){if(0===B)return r.push(B),t.push(lA);var o=t[B-1];return-1===DA.indexOf(o)?(r.push(r[B-1]),t.push(o)):(r.push(B),t.push(lA))}return r.push(B),31===s?t.push("strict"===e?oA:hA):s===HA||29===s?t.push(lA):43===s?t.push(A>=131072&&A<=196605||A>=196608&&A<=262141?hA:lA):void t.push(s)})),[r,t,n]}(A,e.lineBreak),r=t[0],n=t[1],B=t[2];"break-all"!==e.wordBreak&&"break-word"!==e.wordBreak||(n=n.map((function(A){return-1!==[aA,lA,HA].indexOf(A)?hA:A})));var s="keep-all"===e.wordBreak?B.map((function(e,t){return e&&A[t]>=19968&&A[t]<=40959})):void 0;return[r,n,s]},VA=function(){function A(A,e,t,r){this.codePoints=A,this.required="!"===e,this.start=t,this.end=r}return A.prototype.slice=function(){return R.apply(void 0,this.codePoints.slice(this.start,this.end))},A}(),PA=45,kA=43,RA=-1,NA=function(A){return A>=48&&A<=57},JA=function(A){return NA(A)||A>=65&&A<=70||A>=97&&A<=102},XA=function(A){return 10===A||9===A||32===A},YA=function(A){return function(A){return function(A){return A>=97&&A<=122}(A)||function(A){return A>=65&&A<=90}(A)}(A)||function(A){return A>=128}(A)||95===A},WA=function(A){return YA(A)||NA(A)||A===PA},_A=function(A){return A>=0&&A<=8||11===A||A>=14&&A<=31||127===A},ZA=function(A,e){return 92===A&&10!==e},qA=function(A,e,t){return A===PA?YA(e)||ZA(e,t):!!YA(A)||!(92!==A||!ZA(A,e))},jA=function(A,e,t){return A===kA||A===PA?!!NA(e)||46===e&&NA(t):NA(46===A?e:A)},zA=function(A){var e=0,t=1;A[e]!==kA&&A[e]!==PA||(A[e]===PA&&(t=-1),e++);for(var r=[];NA(A[e]);)r.push(A[e++]);var n=r.length?parseInt(R.apply(void 0,r),10):0;46===A[e]&&e++;for(var B=[];NA(A[e]);)B.push(A[e++]);var s=B.length,o=s?parseInt(R.apply(void 0,B),10):0;69!==A[e]&&101!==A[e]||e++;var i=1;A[e]!==kA&&A[e]!==PA||(A[e]===PA&&(i=-1),e++);for(var c=[];NA(A[e]);)c.push(A[e++]);var a=c.length?parseInt(R.apply(void 0,c),10):0;return t*(n+o*Math.pow(10,-s))*Math.pow(10,i*a)},$A={type:2},Ae={type:3},ee={type:4},te={type:13},re={type:8},ne={type:21},Be={type:9},se={type:10},oe={type:11},ie={type:12},ce={type:14},ae={type:23},ge={type:1},Qe={type:25},we={type:24},le={type:26},ue={type:27},Ce={type:28},Ue={type:29},Fe={type:31},he={type:32},de=function(){function A(){this._value=[]}return A.prototype.write=function(A){this._value=this._value.concat(k(A))},A.prototype.read=function(){for(var A=[],e=this.consumeToken();e!==he;)A.push(e),e=this.consumeToken();return A},A.prototype.consumeToken=function(){var A=this.consumeCodePoint();switch(A){case 34:return this.consumeStringToken(34);case 35:var e=this.peekCodePoint(0),t=this.peekCodePoint(1),r=this.peekCodePoint(2);if(WA(e)||ZA(t,r)){var n=qA(e,t,r)?2:1;return{type:5,value:this.consumeName(),flags:n}}break;case 36:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),te;break;case 39:return this.consumeStringToken(39);case 40:return $A;case 41:return Ae;case 42:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),ce;break;case kA:if(jA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 44:return ee;case PA:var B=A,s=this.peekCodePoint(0),o=this.peekCodePoint(1);if(jA(B,s,o))return this.reconsumeCodePoint(A),this.consumeNumericToken();if(qA(B,s,o))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();if(s===PA&&62===o)return this.consumeCodePoint(),this.consumeCodePoint(),we;break;case 46:if(jA(A,this.peekCodePoint(0),this.peekCodePoint(1)))return this.reconsumeCodePoint(A),this.consumeNumericToken();break;case 47:if(42===this.peekCodePoint(0))for(this.consumeCodePoint();;){var i=this.consumeCodePoint();if(42===i&&47===(i=this.consumeCodePoint()))return this.consumeToken();if(i===RA)return this.consumeToken()}break;case 58:return le;case 59:return ue;case 60:if(33===this.peekCodePoint(0)&&this.peekCodePoint(1)===PA&&this.peekCodePoint(2)===PA)return this.consumeCodePoint(),this.consumeCodePoint(),Qe;break;case 64:var c=this.peekCodePoint(0),a=this.peekCodePoint(1),g=this.peekCodePoint(2);if(qA(c,a,g))return{type:7,value:this.consumeName()};break;case 91:return Ce;case 92:if(ZA(A,this.peekCodePoint(0)))return this.reconsumeCodePoint(A),this.consumeIdentLikeToken();break;case 93:return Ue;case 61:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),re;break;case 123:return oe;case 125:return ie;case 117:case 85:var Q=this.peekCodePoint(0),w=this.peekCodePoint(1);return Q!==kA||!JA(w)&&63!==w||(this.consumeCodePoint(),this.consumeUnicodeRangeToken()),this.reconsumeCodePoint(A),this.consumeIdentLikeToken();case 124:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),Be;if(124===this.peekCodePoint(0))return this.consumeCodePoint(),ne;break;case 126:if(61===this.peekCodePoint(0))return this.consumeCodePoint(),se;break;case RA:return he}return XA(A)?(this.consumeWhiteSpace(),Fe):NA(A)?(this.reconsumeCodePoint(A),this.consumeNumericToken()):YA(A)?(this.reconsumeCodePoint(A),this.consumeIdentLikeToken()):{type:6,value:R(A)}},A.prototype.consumeCodePoint=function(){var A=this._value.shift();return void 0===A?-1:A},A.prototype.reconsumeCodePoint=function(A){this._value.unshift(A)},A.prototype.peekCodePoint=function(A){return A>=this._value.length?-1:this._value[A]},A.prototype.consumeUnicodeRangeToken=function(){for(var A=[],e=this.consumeCodePoint();JA(e)&&A.length<6;)A.push(e),e=this.consumeCodePoint();for(var t=!1;63===e&&A.length<6;)A.push(e),e=this.consumeCodePoint(),t=!0;if(t)return{type:30,start:parseInt(R.apply(void 0,A.map((function(A){return 63===A?48:A}))),16),end:parseInt(R.apply(void 0,A.map((function(A){return 63===A?70:A}))),16)};var r=parseInt(R.apply(void 0,A),16);if(this.peekCodePoint(0)===PA&&JA(this.peekCodePoint(1))){this.consumeCodePoint(),e=this.consumeCodePoint();for(var n=[];JA(e)&&n.length<6;)n.push(e),e=this.consumeCodePoint();return{type:30,start:r,end:parseInt(R.apply(void 0,n),16)}}return{type:30,start:r,end:r}},A.prototype.consumeIdentLikeToken=function(){var A=this.consumeName();return"url"===A.toLowerCase()&&40===this.peekCodePoint(0)?(this.consumeCodePoint(),this.consumeUrlToken()):40===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:19,value:A}):{type:20,value:A}},A.prototype.consumeUrlToken=function(){var A=[];if(this.consumeWhiteSpace(),this.peekCodePoint(0)===RA)return{type:22,value:""};var e=this.peekCodePoint(0);if(39===e||34===e){var t=this.consumeStringToken(this.consumeCodePoint());return 0===t.type&&(this.consumeWhiteSpace(),this.peekCodePoint(0)===RA||41===this.peekCodePoint(0))?(this.consumeCodePoint(),{type:22,value:t.value}):(this.consumeBadUrlRemnants(),ae)}for(;;){var r=this.consumeCodePoint();if(r===RA||41===r)return{type:22,value:R.apply(void 0,A)};if(XA(r))return this.consumeWhiteSpace(),this.peekCodePoint(0)===RA||41===this.peekCodePoint(0)?(this.consumeCodePoint(),{type:22,value:R.apply(void 0,A)}):(this.consumeBadUrlRemnants(),ae);if(34===r||39===r||40===r||_A(r))return this.consumeBadUrlRemnants(),ae;if(92===r){if(!ZA(r,this.peekCodePoint(0)))return this.consumeBadUrlRemnants(),ae;A.push(this.consumeEscapedCodePoint())}else A.push(r)}},A.prototype.consumeWhiteSpace=function(){for(;XA(this.peekCodePoint(0));)this.consumeCodePoint()},A.prototype.consumeBadUrlRemnants=function(){for(;;){var A=this.consumeCodePoint();if(41===A||A===RA)return;ZA(A,this.peekCodePoint(0))&&this.consumeEscapedCodePoint()}},A.prototype.consumeStringSlice=function(A){for(var e="";A>0;){var t=Math.min(5e4,A);e+=R.apply(void 0,this._value.splice(0,t)),A-=t}return this._value.shift(),e},A.prototype.consumeStringToken=function(A){for(var e="",t=0;;){var r=this._value[t];if(r===RA||void 0===r||r===A)return{type:0,value:e+=this.consumeStringSlice(t)};if(10===r)return this._value.splice(0,t),ge;if(92===r){var n=this._value[t+1];n!==RA&&void 0!==n&&(10===n?(e+=this.consumeStringSlice(t),t=-1,this._value.shift()):ZA(r,n)&&(e+=this.consumeStringSlice(t),e+=R(this.consumeEscapedCodePoint()),t=-1))}t++}},A.prototype.consumeNumber=function(){var A=[],e=4,t=this.peekCodePoint(0);for(t!==kA&&t!==PA||A.push(this.consumeCodePoint());NA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0);var r=this.peekCodePoint(1);if(46===t&&NA(r))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;NA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());t=this.peekCodePoint(0),r=this.peekCodePoint(1);var n=this.peekCodePoint(2);if((69===t||101===t)&&((r===kA||r===PA)&&NA(n)||NA(r)))for(A.push(this.consumeCodePoint(),this.consumeCodePoint()),e=8;NA(this.peekCodePoint(0));)A.push(this.consumeCodePoint());return[zA(A),e]},A.prototype.consumeNumericToken=function(){var A=this.consumeNumber(),e=A[0],t=A[1],r=this.peekCodePoint(0),n=this.peekCodePoint(1),B=this.peekCodePoint(2);return qA(r,n,B)?{type:15,number:e,flags:t,unit:this.consumeName()}:37===r?(this.consumeCodePoint(),{type:16,number:e,flags:t}):{type:17,number:e,flags:t}},A.prototype.consumeEscapedCodePoint=function(){var A=this.consumeCodePoint();if(JA(A)){for(var e=R(A);JA(this.peekCodePoint(0))&&e.length<6;)e+=R(this.consumeCodePoint());XA(this.peekCodePoint(0))&&this.consumeCodePoint();var t=parseInt(e,16);return 0===t||function(A){return A>=55296&&A<=57343}(t)||t>1114111?65533:t}return A===RA?65533:A},A.prototype.consumeName=function(){for(var A="";;){var e=this.consumeCodePoint();if(WA(e))A+=R(e);else{if(!ZA(e,this.peekCodePoint(0)))return this.reconsumeCodePoint(e),A;A+=R(this.consumeEscapedCodePoint())}}},A}(),fe=function(){function A(A){this._tokens=A}return A.create=function(e){var t=new de;return t.write(e),new A(t.read())},A.parseValue=function(e){return A.create(e).parseComponentValue()},A.parseValues=function(e){return A.create(e).parseComponentValues()},A.prototype.parseComponentValue=function(){for(var A=this.consumeToken();31===A.type;)A=this.consumeToken();if(32===A.type)throw new SyntaxError("Error parsing CSS component value, unexpected EOF");this.reconsumeToken(A);var e=this.consumeComponentValue();do{A=this.consumeToken()}while(31===A.type);if(32===A.type)return e;throw new SyntaxError("Error parsing CSS component value, multiple values found when expecting only one")},A.prototype.parseComponentValues=function(){for(var A=[];;){var e=this.consumeComponentValue();if(32===e.type)return A;A.push(e),A.push()}},A.prototype.consumeComponentValue=function(){var A=this.consumeToken();switch(A.type){case 11:case 28:case 2:return this.consumeSimpleBlock(A.type);case 19:return this.consumeFunction(A)}return A},A.prototype.consumeSimpleBlock=function(A){for(var e={type:A,values:[]},t=this.consumeToken();;){if(32===t.type||Le(t,A))return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue()),t=this.consumeToken()}},A.prototype.consumeFunction=function(A){for(var e={name:A.value,values:[],type:18};;){var t=this.consumeToken();if(32===t.type||3===t.type)return e;this.reconsumeToken(t),e.values.push(this.consumeComponentValue())}},A.prototype.consumeToken=function(){var A=this._tokens.shift();return void 0===A?he:A},A.prototype.reconsumeToken=function(A){this._tokens.unshift(A)},A}(),pe=function(A){return 15===A.type},He=function(A){return 17===A.type},Ee=function(A){return 20===A.type},Ie=function(A){return 0===A.type},ye=function(A,e){return Ee(A)&&A.value===e},me=function(A){return 31!==A.type},Ke=function(A){return 31!==A.type&&4!==A.type},ve=function(A){var e=[],t=[];return A.forEach((function(A){if(4===A.type){if(0===t.length)throw new Error("Error parsing function args, zero tokens for arg");return e.push(t),void(t=[])}31!==A.type&&t.push(A)})),t.length&&e.push(t),e},Le=function(A,e){return 11===e&&12===A.type||(28===e&&29===A.type||2===e&&3===A.type)},be=function(A){return 17===A.type||15===A.type},De=function(A){return 16===A.type||be(A)},Se=function(A){return A.length>1?[A[0],A[1]]:[A[0]]},xe={type:17,number:0,flags:4},Me={type:16,number:50,flags:4},Te={type:16,number:100,flags:4},Oe=function(A,e,t){var r=A[0],n=A[1];return[Ge(r,e),Ge(void 0!==n?n:r,t)]},Ge=function(A,e){if(16===A.type)return A.number/100*e;if(pe(A))switch(A.unit){case"rem":case"em":return 16*A.number;default:return A.number}return A.number},Ve="grad",Pe="turn",ke=function(A,e){if(15===e.type)switch(e.unit){case"deg":return Math.PI*e.number/180;case Ve:return Math.PI/200*e.number;case"rad":return e.number;case Pe:return 2*Math.PI*e.number}throw new Error("Unsupported angle type")},Re=function(A){return 15===A.type&&("deg"===A.unit||A.unit===Ve||"rad"===A.unit||A.unit===Pe)},Ne=function(A){switch(A.filter(Ee).map((function(A){return A.value})).join(" ")){case"to bottom right":case"to right bottom":case"left top":case"top left":return[xe,xe];case"to top":case"bottom":return Je(0);case"to bottom left":case"to left bottom":case"right top":case"top right":return[xe,Te];case"to right":case"left":return Je(90);case"to top left":case"to left top":case"right bottom":case"bottom right":return[Te,Te];case"to bottom":case"top":return Je(180);case"to top right":case"to right top":case"left bottom":case"bottom left":return[Te,xe];case"to left":case"right":return Je(270)}return 0},Je=function(A){return Math.PI*A/180},Xe=function(A,e){if(18===e.type){var t=$e[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported color function "'+e.name+'"');return t(A,e.values)}if(5===e.type){if(3===e.value.length){var r=e.value.substring(0,1),n=e.value.substring(1,2),B=e.value.substring(2,3);return _e(parseInt(r+r,16),parseInt(n+n,16),parseInt(B+B,16),1)}if(4===e.value.length){r=e.value.substring(0,1),n=e.value.substring(1,2),B=e.value.substring(2,3);var s=e.value.substring(3,4);return _e(parseInt(r+r,16),parseInt(n+n,16),parseInt(B+B,16),parseInt(s+s,16)/255)}if(6===e.value.length){r=e.value.substring(0,2),n=e.value.substring(2,4),B=e.value.substring(4,6);return _e(parseInt(r,16),parseInt(n,16),parseInt(B,16),1)}if(8===e.value.length){r=e.value.substring(0,2),n=e.value.substring(2,4),B=e.value.substring(4,6),s=e.value.substring(6,8);return _e(parseInt(r,16),parseInt(n,16),parseInt(B,16),parseInt(s,16)/255)}}if(20===e.type){var o=et[e.value.toUpperCase()];if(void 0!==o)return o}return et.TRANSPARENT},Ye=function(A){return 0==(255&A)},We=function(A){var e=255&A,t=255&A>>8,r=255&A>>16,n=255&A>>24;return e<255?"rgba("+n+","+r+","+t+","+e/255+")":"rgb("+n+","+r+","+t+")"},_e=function(A,e,t,r){return(A<<24|e<<16|t<<8|Math.round(255*r)<<0)>>>0},Ze=function(A,e){if(17===A.type)return A.number;if(16===A.type){var t=3===e?1:255;return 3===e?A.number/100*t:Math.round(A.number/100*t)}return 0},qe=function(A,e){var t=e.filter(Ke);if(3===t.length){var r=t.map(Ze);return _e(r[0],r[1],r[2],1)}if(4===t.length){var n=t.map(Ze);return _e(n[0],n[1],n[2],n[3])}return 0};function je(A,e,t){return t<0&&(t+=1),t>=1&&(t-=1),t<1/6?(e-A)*t*6+A:t<.5?e:t<2/3?6*(e-A)*(2/3-t)+A:A}var ze=function(A,e){var t=e.filter(Ke),r=t[0],n=t[1],B=t[2],s=t[3],o=(17===r.type?Je(r.number):ke(A,r))/(2*Math.PI),i=De(n)?n.number/100:0,c=De(B)?B.number/100:0,a=void 0!==s&&De(s)?Ge(s,1):1;if(0===i)return _e(255*c,255*c,255*c,1);var g=c<=.5?c*(i+1):c+i-c*i,Q=2*c-g,w=je(Q,g,o+1/3),l=je(Q,g,o),u=je(Q,g,o-1/3);return _e(255*w,255*l,255*u,a)},$e={hsl:ze,hsla:ze,rgb:qe,rgba:qe},At=function(A,e){return Xe(A,fe.create(e).parseComponentValue())},et={ALICEBLUE:4042850303,ANTIQUEWHITE:4209760255,AQUA:16777215,AQUAMARINE:2147472639,AZURE:4043309055,BEIGE:4126530815,BISQUE:4293182719,BLACK:255,BLANCHEDALMOND:4293643775,BLUE:65535,BLUEVIOLET:2318131967,BROWN:2771004159,BURLYWOOD:3736635391,CADETBLUE:1604231423,CHARTREUSE:2147418367,CHOCOLATE:3530104575,CORAL:4286533887,CORNFLOWERBLUE:1687547391,CORNSILK:4294499583,CRIMSON:3692313855,CYAN:16777215,DARKBLUE:35839,DARKCYAN:9145343,DARKGOLDENROD:3095837695,DARKGRAY:2846468607,DARKGREEN:6553855,DARKGREY:2846468607,DARKKHAKI:3182914559,DARKMAGENTA:2332068863,DARKOLIVEGREEN:1433087999,DARKORANGE:4287365375,DARKORCHID:2570243327,DARKRED:2332033279,DARKSALMON:3918953215,DARKSEAGREEN:2411499519,DARKSLATEBLUE:1211993087,DARKSLATEGRAY:793726975,DARKSLATEGREY:793726975,DARKTURQUOISE:13554175,DARKVIOLET:2483082239,DEEPPINK:4279538687,DEEPSKYBLUE:12582911,DIMGRAY:1768516095,DIMGREY:1768516095,DODGERBLUE:512819199,FIREBRICK:2988581631,FLORALWHITE:4294635775,FORESTGREEN:579543807,FUCHSIA:4278255615,GAINSBORO:3705462015,GHOSTWHITE:4177068031,GOLD:4292280575,GOLDENROD:3668254975,GRAY:2155905279,GREEN:8388863,GREENYELLOW:2919182335,GREY:2155905279,HONEYDEW:4043305215,HOTPINK:4285117695,INDIANRED:3445382399,INDIGO:1258324735,IVORY:4294963455,KHAKI:4041641215,LAVENDER:3873897215,LAVENDERBLUSH:4293981695,LAWNGREEN:2096890111,LEMONCHIFFON:4294626815,LIGHTBLUE:2916673279,LIGHTCORAL:4034953471,LIGHTCYAN:3774873599,LIGHTGOLDENRODYELLOW:4210742015,LIGHTGRAY:3553874943,LIGHTGREEN:2431553791,LIGHTGREY:3553874943,LIGHTPINK:4290167295,LIGHTSALMON:4288707327,LIGHTSEAGREEN:548580095,LIGHTSKYBLUE:2278488831,LIGHTSLATEGRAY:2005441023,LIGHTSLATEGREY:2005441023,LIGHTSTEELBLUE:2965692159,LIGHTYELLOW:4294959359,LIME:16711935,LIMEGREEN:852308735,LINEN:4210091775,MAGENTA:4278255615,MAROON:2147483903,MEDIUMAQUAMARINE:1724754687,MEDIUMBLUE:52735,MEDIUMORCHID:3126187007,MEDIUMPURPLE:2473647103,MEDIUMSEAGREEN:1018393087,MEDIUMSLATEBLUE:2070474495,MEDIUMSPRINGGREEN:16423679,MEDIUMTURQUOISE:1221709055,MEDIUMVIOLETRED:3340076543,MIDNIGHTBLUE:421097727,MINTCREAM:4127193855,MISTYROSE:4293190143,MOCCASIN:4293178879,NAVAJOWHITE:4292783615,NAVY:33023,OLDLACE:4260751103,OLIVE:2155872511,OLIVEDRAB:1804477439,ORANGE:4289003775,ORANGERED:4282712319,ORCHID:3664828159,PALEGOLDENROD:4008225535,PALEGREEN:2566625535,PALETURQUOISE:2951671551,PALEVIOLETRED:3681588223,PAPAYAWHIP:4293907967,PEACHPUFF:4292524543,PERU:3448061951,PINK:4290825215,PLUM:3718307327,POWDERBLUE:2967529215,PURPLE:2147516671,REBECCAPURPLE:1714657791,RED:4278190335,ROSYBROWN:3163525119,ROYALBLUE:1097458175,SADDLEBROWN:2336560127,SALMON:4202722047,SANDYBROWN:4104413439,SEAGREEN:780883967,SEASHELL:4294307583,SIENNA:2689740287,SILVER:3233857791,SKYBLUE:2278484991,SLATEBLUE:1784335871,SLATEGRAY:1887473919,SLATEGREY:1887473919,SNOW:4294638335,SPRINGGREEN:16744447,STEELBLUE:1182971135,TAN:3535047935,TEAL:8421631,THISTLE:3636451583,TOMATO:4284696575,TRANSPARENT:0,TURQUOISE:1088475391,VIOLET:4001558271,WHEAT:4125012991,WHITE:4294967295,WHITESMOKE:4126537215,YELLOW:4294902015,YELLOWGREEN:2597139199},tt={name:"background-clip",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(Ee(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},rt={name:"background-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},nt=function(A,e){var t=Xe(A,e[0]),r=e[1];return r&&De(r)?{color:t,stop:r}:{color:t,stop:null}},Bt=function(A,e){var t=A[0],r=A[A.length-1];null===t.stop&&(t.stop=xe),null===r.stop&&(r.stop=Te);for(var n=[],B=0,s=0;s<A.length;s++){var o=A[s].stop;if(null!==o){var i=Ge(o,e);n.push(i>B?i:B),B=i}else n.push(null)}var c=null;for(s=0;s<n.length;s++){var a=n[s];if(null===a)null===c&&(c=s);else if(null!==c){for(var g=s-c,Q=(a-n[c-1])/(g+1),w=1;w<=g;w++)n[c+w-1]=Q*w;c=null}}return A.map((function(A,t){return{color:A.color,stop:Math.max(Math.min(1,n[t]/e),0)}}))},st=function(A,e,t){var r="number"==typeof A?A:function(A,e,t){var r=e/2,n=t/2,B=Ge(A[0],e)-r,s=n-Ge(A[1],t);return(Math.atan2(s,B)+2*Math.PI)%(2*Math.PI)}(A,e,t),n=Math.abs(e*Math.sin(r))+Math.abs(t*Math.cos(r)),B=e/2,s=t/2,o=n/2,i=Math.sin(r-Math.PI/2)*o,c=Math.cos(r-Math.PI/2)*o;return[n,B-c,B+c,s-i,s+i]},ot=function(A,e){return Math.sqrt(A*A+e*e)},it=function(A,e,t,r,n){return[[0,0],[0,e],[A,0],[A,e]].reduce((function(A,e){var B=ot(t-e[0],r-e[1]);return(n?B<A.optimumDistance:B>A.optimumDistance)?{optimumCorner:e,optimumDistance:B}:A}),{optimumDistance:n?1/0:-1/0,optimumCorner:null}).optimumCorner},ct=function(A,e){var t=Je(180),r=[];return ve(e).forEach((function(e,n){if(0===n){var B=e[0];if(20===B.type&&-1!==["top","left","right","bottom"].indexOf(B.value))return void(t=Ne(e));if(Re(B))return void(t=(ke(A,B)+Je(270))%Je(360))}var s=nt(A,e);r.push(s)})),{angle:t,stops:r,type:1}},at="closest-side",gt="farthest-side",Qt="closest-corner",wt="farthest-corner",lt="circle",ut="ellipse",Ct="cover",Ut="contain",Ft=function(A,e){var t=0,r=3,n=[],B=[];return ve(e).forEach((function(e,s){var o=!0;if(0===s?o=e.reduce((function(A,e){if(Ee(e))switch(e.value){case"center":return B.push(Me),!1;case"top":case"left":return B.push(xe),!1;case"right":case"bottom":return B.push(Te),!1}else if(De(e)||be(e))return B.push(e),!1;return A}),o):1===s&&(o=e.reduce((function(A,e){if(Ee(e))switch(e.value){case lt:return t=0,!1;case ut:return t=1,!1;case Ut:case at:return r=0,!1;case gt:return r=1,!1;case Qt:return r=2,!1;case Ct:case wt:return r=3,!1}else if(be(e)||De(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),o)),o){var i=nt(A,e);n.push(i)}})),{size:r,shape:t,stops:n,position:B,type:2}},ht=function(A,e){if(22===e.type){var t={url:e.value,type:0};return A.cache.addImage(e.value),t}if(18===e.type){var r=ft[e.name];if(void 0===r)throw new Error('Attempting to parse an unsupported image function "'+e.name+'"');return r(A,e.values)}throw new Error("Unsupported image type "+e.type)};var dt,ft={"linear-gradient":function(A,e){var t=Je(180),r=[];return ve(e).forEach((function(e,n){if(0===n){var B=e[0];if(20===B.type&&"to"===B.value)return void(t=Ne(e));if(Re(B))return void(t=ke(A,B))}var s=nt(A,e);r.push(s)})),{angle:t,stops:r,type:1}},"-moz-linear-gradient":ct,"-ms-linear-gradient":ct,"-o-linear-gradient":ct,"-webkit-linear-gradient":ct,"radial-gradient":function(A,e){var t=0,r=3,n=[],B=[];return ve(e).forEach((function(e,s){var o=!0;if(0===s){var i=!1;o=e.reduce((function(A,e){if(i)if(Ee(e))switch(e.value){case"center":return B.push(Me),A;case"top":case"left":return B.push(xe),A;case"right":case"bottom":return B.push(Te),A}else(De(e)||be(e))&&B.push(e);else if(Ee(e))switch(e.value){case lt:return t=0,!1;case ut:return t=1,!1;case"at":return i=!0,!1;case at:return r=0,!1;case Ct:case gt:return r=1,!1;case Ut:case Qt:return r=2,!1;case wt:return r=3,!1}else if(be(e)||De(e))return Array.isArray(r)||(r=[]),r.push(e),!1;return A}),o)}if(o){var c=nt(A,e);n.push(c)}})),{size:r,shape:t,stops:n,position:B,type:2}},"-moz-radial-gradient":Ft,"-ms-radial-gradient":Ft,"-o-radial-gradient":Ft,"-webkit-radial-gradient":Ft,"-webkit-gradient":function(A,e){var t=Je(180),r=[],n=1;return ve(e).forEach((function(e,t){var B=e[0];if(0===t){if(Ee(B)&&"linear"===B.value)return void(n=1);if(Ee(B)&&"radial"===B.value)return void(n=2)}if(18===B.type)if("from"===B.name){var s=Xe(A,B.values[0]);r.push({stop:xe,color:s})}else if("to"===B.name){s=Xe(A,B.values[0]);r.push({stop:Te,color:s})}else if("color-stop"===B.name){var o=B.values.filter(Ke);if(2===o.length){s=Xe(A,o[1]);var i=o[0];He(i)&&r.push({stop:{type:16,number:100*i.number,flags:i.flags},color:s})}}})),1===n?{angle:(t+Je(180))%Je(360),stops:r,type:n}:{size:3,shape:0,stops:r,position:[],type:n}}},pt={name:"background-image",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e.filter((function(A){return Ke(A)&&function(A){return!(20===A.type&&"none"===A.value||18===A.type&&!ft[A.name])}(A)})).map((function(e){return ht(A,e)}))}},Ht={name:"background-origin",initialValue:"border-box",prefix:!1,type:1,parse:function(A,e){return e.map((function(A){if(Ee(A))switch(A.value){case"padding-box":return 1;case"content-box":return 2}return 0}))}},Et={name:"background-position",initialValue:"0% 0%",type:1,prefix:!1,parse:function(A,e){return ve(e).map((function(A){return A.filter(De)})).map(Se)}},It={name:"background-repeat",initialValue:"repeat",prefix:!1,type:1,parse:function(A,e){return ve(e).map((function(A){return A.filter(Ee).map((function(A){return A.value})).join(" ")})).map(yt)}},yt=function(A){switch(A){case"no-repeat":return 1;case"repeat-x":case"repeat no-repeat":return 2;case"repeat-y":case"no-repeat repeat":return 3;default:return 0}};!function(A){A.AUTO="auto",A.CONTAIN="contain",A.COVER="cover"}(dt||(dt={}));var mt,Kt={name:"background-size",initialValue:"0",prefix:!1,type:1,parse:function(A,e){return ve(e).map((function(A){return A.filter(vt)}))}},vt=function(A){return Ee(A)||De(A)},Lt=function(A){return{name:"border-"+A+"-color",initialValue:"transparent",prefix:!1,type:3,format:"color"}},bt=Lt("top"),Dt=Lt("right"),St=Lt("bottom"),xt=Lt("left"),Mt=function(A){return{name:"border-radius-"+A,initialValue:"0 0",prefix:!1,type:1,parse:function(A,e){return Se(e.filter(De))}}},Tt=Mt("top-left"),Ot=Mt("top-right"),Gt=Mt("bottom-right"),Vt=Mt("bottom-left"),Pt=function(A){return{name:"border-"+A+"-style",initialValue:"solid",prefix:!1,type:2,parse:function(A,e){switch(e){case"none":return 0;case"dashed":return 2;case"dotted":return 3;case"double":return 4}return 1}}},kt=Pt("top"),Rt=Pt("right"),Nt=Pt("bottom"),Jt=Pt("left"),Xt=function(A){return{name:"border-"+A+"-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return pe(e)?e.number:0}}},Yt=Xt("top"),Wt=Xt("right"),_t=Xt("bottom"),Zt=Xt("left"),qt={name:"color",initialValue:"transparent",prefix:!1,type:3,format:"color"},jt={name:"direction",initialValue:"ltr",prefix:!1,type:2,parse:function(A,e){return"rtl"===e?1:0}},zt={name:"display",initialValue:"inline-block",prefix:!1,type:1,parse:function(A,e){return e.filter(Ee).reduce((function(A,e){return A|$t(e.value)}),0)}},$t=function(A){switch(A){case"block":case"-webkit-box":return 2;case"inline":return 4;case"run-in":return 8;case"flow":return 16;case"flow-root":return 32;case"table":return 64;case"flex":case"-webkit-flex":return 128;case"grid":case"-ms-grid":return 256;case"ruby":return 512;case"subgrid":return 1024;case"list-item":return 2048;case"table-row-group":return 4096;case"table-header-group":return 8192;case"table-footer-group":return 16384;case"table-row":return 32768;case"table-cell":return 65536;case"table-column-group":return 131072;case"table-column":return 262144;case"table-caption":return 524288;case"ruby-base":return 1048576;case"ruby-text":return 2097152;case"ruby-base-container":return 4194304;case"ruby-text-container":return 8388608;case"contents":return 16777216;case"inline-block":return 33554432;case"inline-list-item":return 67108864;case"inline-table":return 134217728;case"inline-flex":return 268435456;case"inline-grid":return 536870912}return 0},Ar={name:"float",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"left":return 1;case"right":return 2;case"inline-start":return 3;case"inline-end":return 4}return 0}},er={name:"letter-spacing",initialValue:"0",prefix:!1,type:0,parse:function(A,e){return 20===e.type&&"normal"===e.value?0:17===e.type||15===e.type?e.number:0}};!function(A){A.NORMAL="normal",A.STRICT="strict"}(mt||(mt={}));var tr,rr={name:"line-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"strict"===e?mt.STRICT:mt.NORMAL}},nr={name:"line-height",initialValue:"normal",prefix:!1,type:4},Br=function(A,e){return Ee(A)&&"normal"===A.value?1.2*e:17===A.type?e*A.number:De(A)?Ge(A,e):e},sr={name:"list-style-image",initialValue:"none",type:0,prefix:!1,parse:function(A,e){return 20===e.type&&"none"===e.value?null:ht(A,e)}},or={name:"list-style-position",initialValue:"outside",prefix:!1,type:2,parse:function(A,e){return"inside"===e?0:1}},ir={name:"list-style-type",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"disc":return 0;case"circle":return 1;case"square":return 2;case"decimal":return 3;case"cjk-decimal":return 4;case"decimal-leading-zero":return 5;case"lower-roman":return 6;case"upper-roman":return 7;case"lower-greek":return 8;case"lower-alpha":return 9;case"upper-alpha":return 10;case"arabic-indic":return 11;case"armenian":return 12;case"bengali":return 13;case"cambodian":return 14;case"cjk-earthly-branch":return 15;case"cjk-heavenly-stem":return 16;case"cjk-ideographic":return 17;case"devanagari":return 18;case"ethiopic-numeric":return 19;case"georgian":return 20;case"gujarati":return 21;case"gurmukhi":case"hebrew":return 22;case"hiragana":return 23;case"hiragana-iroha":return 24;case"japanese-formal":return 25;case"japanese-informal":return 26;case"kannada":return 27;case"katakana":return 28;case"katakana-iroha":return 29;case"khmer":return 30;case"korean-hangul-formal":return 31;case"korean-hanja-formal":return 32;case"korean-hanja-informal":return 33;case"lao":return 34;case"lower-armenian":return 35;case"malayalam":return 36;case"mongolian":return 37;case"myanmar":return 38;case"oriya":return 39;case"persian":return 40;case"simp-chinese-formal":return 41;case"simp-chinese-informal":return 42;case"tamil":return 43;case"telugu":return 44;case"thai":return 45;case"tibetan":return 46;case"trad-chinese-formal":return 47;case"trad-chinese-informal":return 48;case"upper-armenian":return 49;case"disclosure-open":return 50;case"disclosure-closed":return 51;default:return-1}}},cr=function(A){return{name:"margin-"+A,initialValue:"0",prefix:!1,type:4}},ar=cr("top"),gr=cr("right"),Qr=cr("bottom"),wr=cr("left"),lr={name:"overflow",initialValue:"visible",prefix:!1,type:1,parse:function(A,e){return e.filter(Ee).map((function(A){switch(A.value){case"hidden":return 1;case"scroll":return 2;case"clip":return 3;case"auto":return 4;default:return 0}}))}},ur={name:"overflow-wrap",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){return"break-word"===e?"break-word":"normal"}},Cr=function(A){return{name:"padding-"+A,initialValue:"0",prefix:!1,type:3,format:"length-percentage"}},Ur=Cr("top"),Fr=Cr("right"),hr=Cr("bottom"),dr=Cr("left"),fr={name:"text-align",initialValue:"left",prefix:!1,type:2,parse:function(A,e){switch(e){case"right":return 2;case"center":case"justify":return 1;default:return 0}}},pr={name:"position",initialValue:"static",prefix:!1,type:2,parse:function(A,e){switch(e){case"relative":return 1;case"absolute":return 2;case"fixed":return 3;case"sticky":return 4}return 0}},Hr={name:"text-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&ye(e[0],"none")?[]:ve(e).map((function(e){for(var t={color:et.TRANSPARENT,offsetX:xe,offsetY:xe,blur:xe},r=0,n=0;n<e.length;n++){var B=e[n];be(B)?(0===r?t.offsetX=B:1===r?t.offsetY=B:t.blur=B,r++):t.color=Xe(A,B)}return t}))}},Er={name:"text-transform",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"uppercase":return 2;case"lowercase":return 1;case"capitalize":return 3}return 0}},Ir={name:"transform",initialValue:"none",prefix:!0,type:0,parse:function(A,e){if(20===e.type&&"none"===e.value)return null;if(18===e.type){var t=yr[e.name];if(void 0===t)throw new Error('Attempting to parse an unsupported transform function "'+e.name+'"');return t(e.values)}return null}},yr={matrix:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number}));return 6===e.length?e:null},matrix3d:function(A){var e=A.filter((function(A){return 17===A.type})).map((function(A){return A.number}));return 16===e.length?[e[0],e[1],e[4],e[5],e[12],e[13]]:null}},mr={type:16,number:50,flags:4},Kr=[mr,mr],vr={name:"transform-origin",initialValue:"50% 50%",prefix:!0,type:1,parse:function(A,e){var t=e.filter(De);return 2!==t.length?Kr:[t[0],t[1]]}},Lr={name:"visible",initialValue:"none",prefix:!1,type:2,parse:function(A,e){switch(e){case"hidden":return 1;case"collapse":return 2;default:return 0}}};!function(A){A.NORMAL="normal",A.BREAK_ALL="break-all",A.KEEP_ALL="keep-all"}(tr||(tr={}));for(var br={name:"word-break",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"break-all":return tr.BREAK_ALL;case"keep-all":return tr.KEEP_ALL;default:return tr.NORMAL}}},Dr={name:"z-index",initialValue:"auto",prefix:!1,type:0,parse:function(A,e){if(20===e.type)return{auto:!0,order:0};if(He(e))return{auto:!1,order:e.number};throw new Error("Invalid z-index number parsed")}},Sr=function(A,e){if(15===e.type)switch(e.unit.toLowerCase()){case"s":return 1e3*e.number;case"ms":return e.number}throw new Error("Unsupported time type")},xr={name:"opacity",initialValue:"1",type:0,prefix:!1,parse:function(A,e){return He(e)?e.number:1}},Mr={name:"text-decoration-color",initialValue:"transparent",prefix:!1,type:3,format:"color"},Tr={name:"text-decoration-line",initialValue:"none",prefix:!1,type:1,parse:function(A,e){return e.filter(Ee).map((function(A){switch(A.value){case"underline":return 1;case"overline":return 2;case"line-through":return 3;case"none":return 4}return 0})).filter((function(A){return 0!==A}))}},Or={name:"font-family",initialValue:"",prefix:!1,type:1,parse:function(A,e){var t=[],r=[];return e.forEach((function(A){switch(A.type){case 20:case 0:t.push(A.value);break;case 17:t.push(A.number.toString());break;case 4:r.push(t.join(" ")),t.length=0}})),t.length&&r.push(t.join(" ")),r.map((function(A){return-1===A.indexOf(" ")?A:"'"+A+"'"}))}},Gr={name:"font-size",initialValue:"0",prefix:!1,type:3,format:"length"},Vr={name:"font-weight",initialValue:"normal",type:0,prefix:!1,parse:function(A,e){return He(e)?e.number:Ee(e)&&"bold"===e.value?700:400}},Pr={name:"font-variant",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return e.filter(Ee).map((function(A){return A.value}))}},kr={name:"font-style",initialValue:"normal",prefix:!1,type:2,parse:function(A,e){switch(e){case"oblique":return"oblique";case"italic":return"italic";default:return"normal"}}},Rr=function(A,e){return 0!=(A&e)},Nr={name:"content",initialValue:"none",type:1,prefix:!1,parse:function(A,e){if(0===e.length)return[];var t=e[0];return 20===t.type&&"none"===t.value?[]:e}},Jr={name:"counter-increment",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;for(var r=[],n=e.filter(me),B=0;B<n.length;B++){var s=n[B],o=n[B+1];if(20===s.type){var i=o&&He(o)?o.number:1;r.push({counter:s.value,increment:i})}}return r}},Xr={name:"counter-reset",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return[];for(var t=[],r=e.filter(me),n=0;n<r.length;n++){var B=r[n],s=r[n+1];if(Ee(B)&&"none"!==B.value){var o=s&&He(s)?s.number:0;t.push({counter:B.value,reset:o})}}return t}},Yr={name:"duration",initialValue:"0s",prefix:!1,type:1,parse:function(A,e){return e.filter(pe).map((function(e){return Sr(A,e)}))}},Wr={name:"quotes",initialValue:"none",prefix:!0,type:1,parse:function(A,e){if(0===e.length)return null;var t=e[0];if(20===t.type&&"none"===t.value)return null;var r=[],n=e.filter(Ie);if(n.length%2!=0)return null;for(var B=0;B<n.length;B+=2){r.push({open:n[B].value,close:n[B+1].value})}return r}},_r=function(A,e,t){if(!A)return"";var r=A[Math.min(e,A.length-1)];return r?t?r.open:r.close:""},Zr={name:"box-shadow",initialValue:"none",type:1,prefix:!1,parse:function(A,e){return 1===e.length&&ye(e[0],"none")?[]:ve(e).map((function(e){for(var t={color:255,offsetX:xe,offsetY:xe,blur:xe,spread:xe,inset:!1},r=0,n=0;n<e.length;n++){var B=e[n];ye(B,"inset")?t.inset=!0:be(B)?(0===r?t.offsetX=B:1===r?t.offsetY=B:2===r?t.blur=B:t.spread=B,r++):t.color=Xe(A,B)}return t}))}},qr={name:"paint-order",initialValue:"normal",prefix:!1,type:1,parse:function(A,e){var t=[];return e.filter(Ee).forEach((function(A){switch(A.value){case"stroke":t.push(1);break;case"fill":t.push(0);break;case"markers":t.push(2)}})),[0,1,2].forEach((function(A){-1===t.indexOf(A)&&t.push(A)})),t}},jr={name:"-webkit-text-stroke-color",initialValue:"currentcolor",prefix:!1,type:3,format:"color"},zr={name:"-webkit-text-stroke-width",initialValue:"0",type:0,prefix:!1,parse:function(A,e){return pe(e)?e.number:0}},$r=function(){function A(A,e){var t,r;this.animationDuration=tn(A,Yr,e.animationDuration),this.backgroundClip=tn(A,tt,e.backgroundClip),this.backgroundColor=tn(A,rt,e.backgroundColor),this.backgroundImage=tn(A,pt,e.backgroundImage),this.backgroundOrigin=tn(A,Ht,e.backgroundOrigin),this.backgroundPosition=tn(A,Et,e.backgroundPosition),this.backgroundRepeat=tn(A,It,e.backgroundRepeat),this.backgroundSize=tn(A,Kt,e.backgroundSize),this.borderTopColor=tn(A,bt,e.borderTopColor),this.borderRightColor=tn(A,Dt,e.borderRightColor),this.borderBottomColor=tn(A,St,e.borderBottomColor),this.borderLeftColor=tn(A,xt,e.borderLeftColor),this.borderTopLeftRadius=tn(A,Tt,e.borderTopLeftRadius),this.borderTopRightRadius=tn(A,Ot,e.borderTopRightRadius),this.borderBottomRightRadius=tn(A,Gt,e.borderBottomRightRadius),this.borderBottomLeftRadius=tn(A,Vt,e.borderBottomLeftRadius),this.borderTopStyle=tn(A,kt,e.borderTopStyle),this.borderRightStyle=tn(A,Rt,e.borderRightStyle),this.borderBottomStyle=tn(A,Nt,e.borderBottomStyle),this.borderLeftStyle=tn(A,Jt,e.borderLeftStyle),this.borderTopWidth=tn(A,Yt,e.borderTopWidth),this.borderRightWidth=tn(A,Wt,e.borderRightWidth),this.borderBottomWidth=tn(A,_t,e.borderBottomWidth),this.borderLeftWidth=tn(A,Zt,e.borderLeftWidth),this.boxShadow=tn(A,Zr,e.boxShadow),this.color=tn(A,qt,e.color),this.direction=tn(A,jt,e.direction),this.display=tn(A,zt,e.display),this.float=tn(A,Ar,e.cssFloat),this.fontFamily=tn(A,Or,e.fontFamily),this.fontSize=tn(A,Gr,e.fontSize),this.fontStyle=tn(A,kr,e.fontStyle),this.fontVariant=tn(A,Pr,e.fontVariant),this.fontWeight=tn(A,Vr,e.fontWeight),this.letterSpacing=tn(A,er,e.letterSpacing),this.lineBreak=tn(A,rr,e.lineBreak),this.lineHeight=tn(A,nr,e.lineHeight),this.listStyleImage=tn(A,sr,e.listStyleImage),this.listStylePosition=tn(A,or,e.listStylePosition),this.listStyleType=tn(A,ir,e.listStyleType),this.marginTop=tn(A,ar,e.marginTop),this.marginRight=tn(A,gr,e.marginRight),this.marginBottom=tn(A,Qr,e.marginBottom),this.marginLeft=tn(A,wr,e.marginLeft),this.opacity=tn(A,xr,e.opacity);var n=tn(A,lr,e.overflow);this.overflowX=n[0],this.overflowY=n[n.length>1?1:0],this.overflowWrap=tn(A,ur,e.overflowWrap),this.paddingTop=tn(A,Ur,e.paddingTop),this.paddingRight=tn(A,Fr,e.paddingRight),this.paddingBottom=tn(A,hr,e.paddingBottom),this.paddingLeft=tn(A,dr,e.paddingLeft),this.paintOrder=tn(A,qr,e.paintOrder),this.position=tn(A,pr,e.position),this.textAlign=tn(A,fr,e.textAlign),this.textDecorationColor=tn(A,Mr,null!==(t=e.textDecorationColor)&&void 0!==t?t:e.color),this.textDecorationLine=tn(A,Tr,null!==(r=e.textDecorationLine)&&void 0!==r?r:e.textDecoration),this.textShadow=tn(A,Hr,e.textShadow),this.textTransform=tn(A,Er,e.textTransform),this.transform=tn(A,Ir,e.transform),this.transformOrigin=tn(A,vr,e.transformOrigin),this.visibility=tn(A,Lr,e.visibility),this.webkitTextStrokeColor=tn(A,jr,e.webkitTextStrokeColor),this.webkitTextStrokeWidth=tn(A,zr,e.webkitTextStrokeWidth),this.wordBreak=tn(A,br,e.wordBreak),this.zIndex=tn(A,Dr,e.zIndex)}return A.prototype.isVisible=function(){return this.display>0&&this.opacity>0&&0===this.visibility},A.prototype.isTransparent=function(){return Ye(this.backgroundColor)},A.prototype.isTransformed=function(){return null!==this.transform},A.prototype.isPositioned=function(){return 0!==this.position},A.prototype.isPositionedWithZIndex=function(){return this.isPositioned()&&!this.zIndex.auto},A.prototype.isFloating=function(){return 0!==this.float},A.prototype.isInlineLevel=function(){return Rr(this.display,4)||Rr(this.display,33554432)||Rr(this.display,268435456)||Rr(this.display,536870912)||Rr(this.display,67108864)||Rr(this.display,134217728)},A}(),An=function(A,e){this.content=tn(A,Nr,e.content),this.quotes=tn(A,Wr,e.quotes)},en=function(A,e){this.counterIncrement=tn(A,Jr,e.counterIncrement),this.counterReset=tn(A,Xr,e.counterReset)},tn=function(A,e,t){var r=new de,n=null!=t?t.toString():e.initialValue;r.write(n);var B=new fe(r.read());switch(e.type){case 2:var s=B.parseComponentValue();return e.parse(A,Ee(s)?s.value:e.initialValue);case 0:return e.parse(A,B.parseComponentValue());case 1:return e.parse(A,B.parseComponentValues());case 4:return B.parseComponentValue();case 3:switch(e.format){case"angle":return ke(A,B.parseComponentValue());case"color":return Xe(A,B.parseComponentValue());case"image":return ht(A,B.parseComponentValue());case"length":var o=B.parseComponentValue();return be(o)?o:xe;case"length-percentage":var i=B.parseComponentValue();return De(i)?i:xe;case"time":return Sr(A,B.parseComponentValue())}}},rn=function(A,e){var t=function(A){switch(A.getAttribute("data-html2canvas-debug")){case"all":return 1;case"clone":return 2;case"parse":return 3;case"render":return 4;default:return 0}}(A);return 1===t||e===t},nn=function(A,e){this.context=A,this.textNodes=[],this.elements=[],this.flags=0,rn(e,3),this.styles=new $r(A,window.getComputedStyle(e,null)),cB(e)&&(this.styles.animationDuration.some((function(A){return A>0}))&&(e.style.animationDuration="0s"),null!==this.styles.transform&&(e.style.transform="none")),this.bounds=P(this.context,e),rn(e,4)&&(this.flags|=16)},Bn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",sn="undefined"==typeof Uint8Array?[]:new Uint8Array(256),on=0;on<64;on++)sn[Bn.charCodeAt(on)]=on;for(var cn=function(A,e,t){return A.slice?A.slice(e,t):new Uint16Array(Array.prototype.slice.call(A,e,t))},an=function(){function A(A,e,t,r,n,B){this.initialValue=A,this.errorValue=e,this.highStart=t,this.highValueIndex=r,this.index=n,this.data=B}return A.prototype.get=function(A){var e;if(A>=0){if(A<55296||A>56319&&A<=65535)return this.data[e=((e=this.index[A>>5])<<2)+(31&A)];if(A<=65535)return this.data[e=((e=this.index[2048+(A-55296>>5)])<<2)+(31&A)];if(A<this.highStart)return e=this.index[e=2080+(A>>11)],this.data[e=((e=this.index[e+=A>>5&63])<<2)+(31&A)];if(A<=1114111)return this.data[this.highValueIndex]}return this.errorValue},A}(),gn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",Qn="undefined"==typeof Uint8Array?[]:new Uint8Array(256),wn=0;wn<64;wn++)Qn[gn.charCodeAt(wn)]=wn;var ln,un=8,Cn=9,Un=11,Fn=12,hn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];if(String.fromCodePoint)return String.fromCodePoint.apply(String,A);var t=A.length;if(!t)return"";for(var r=[],n=-1,B="";++n<t;){var s=A[n];s<=65535?r.push(s):r.push(55296+((s-=65536)>>10),s%1024+56320),(n+1===t||r.length>16384)&&(B+=String.fromCharCode.apply(String,r),r.length=0)}return B},dn=function(A,e){var t,r,n,B=function(A){var e,t,r,n,B,s=.75*A.length,o=A.length,i=0;"="===A[A.length-1]&&(s--,"="===A[A.length-2]&&s--);var c="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array&&void 0!==Uint8Array.prototype.slice?new ArrayBuffer(s):new Array(s),a=Array.isArray(c)?c:new Uint8Array(c);for(e=0;e<o;e+=4)t=sn[A.charCodeAt(e)],r=sn[A.charCodeAt(e+1)],n=sn[A.charCodeAt(e+2)],B=sn[A.charCodeAt(e+3)],a[i++]=t<<2|r>>4,a[i++]=(15&r)<<4|n>>2,a[i++]=(3&n)<<6|63&B;return c}(A),s=Array.isArray(B)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=4)t.push(A[r+3]<<24|A[r+2]<<16|A[r+1]<<8|A[r]);return t}(B):new Uint32Array(B),o=Array.isArray(B)?function(A){for(var e=A.length,t=[],r=0;r<e;r+=2)t.push(A[r+1]<<8|A[r]);return t}(B):new Uint16Array(B),i=cn(o,12,s[4]/2),c=2===s[5]?cn(o,(24+s[4])/2):(t=s,r=Math.ceil((24+s[4])/4),t.slice?t.slice(r,n):new Uint32Array(Array.prototype.slice.call(t,r,n)));return new an(s[0],s[1],s[2],s[3],i,c)}("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"),fn="×",pn=function(A){return dn.get(A)},Hn=function(A,e,t){var r=t-2,n=e[r],B=e[t-1],s=e[t];if(2===B&&3===s)return fn;if(2===B||3===B||4===B)return"÷";if(2===s||3===s||4===s)return"÷";if(B===un&&-1!==[un,Cn,Un,Fn].indexOf(s))return fn;if(!(B!==Un&&B!==Cn||s!==Cn&&10!==s))return fn;if((B===Fn||10===B)&&10===s)return fn;if(13===s||5===s)return fn;if(7===s)return fn;if(1===B)return fn;if(13===B&&14===s){for(;5===n;)n=e[--r];if(14===n)return fn}if(15===B&&15===s){for(var o=0;15===n;)o++,n=e[--r];if(o%2==0)return fn}return"÷"},En=function(A){var e=function(A){for(var e=[],t=0,r=A.length;t<r;){var n=A.charCodeAt(t++);if(n>=55296&&n<=56319&&t<r){var B=A.charCodeAt(t++);56320==(64512&B)?e.push(((1023&n)<<10)+(1023&B)+65536):(e.push(n),t--)}else e.push(n)}return e}(A),t=e.length,r=0,n=0,B=e.map(pn);return{next:function(){if(r>=t)return{done:!0,value:null};for(var A=fn;r<t&&(A=Hn(0,B,++r))===fn;);if(A!==fn||r===t){var s=hn.apply(null,e.slice(n,r));return n=r,{value:s,done:!1}}return{done:!0,value:null}}}},In=function(A){return 0===A[0]&&255===A[1]&&0===A[2]&&255===A[3]},yn=function(A,e,t,r,n){var B="http://www.w3.org/2000/svg",s=document.createElementNS(B,"svg"),o=document.createElementNS(B,"foreignObject");return s.setAttributeNS(null,"width",A.toString()),s.setAttributeNS(null,"height",e.toString()),o.setAttributeNS(null,"width","100%"),o.setAttributeNS(null,"height","100%"),o.setAttributeNS(null,"x",t.toString()),o.setAttributeNS(null,"y",r.toString()),o.setAttributeNS(null,"externalResourcesRequired","true"),s.appendChild(o),o.appendChild(n),s},mn=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){return e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},Kn={get SUPPORT_RANGE_BOUNDS(){var A=function(A){if(A.createRange){var e=A.createRange();if(e.getBoundingClientRect){var t=A.createElement("boundtest");t.style.height="123px",t.style.display="block",A.body.appendChild(t),e.selectNode(t);var r=e.getBoundingClientRect(),n=Math.round(r.height);if(A.body.removeChild(t),123===n)return!0}}return!1}(document);return Object.defineProperty(Kn,"SUPPORT_RANGE_BOUNDS",{value:A}),A},get SUPPORT_WORD_BREAKING(){var A=Kn.SUPPORT_RANGE_BOUNDS&&function(A){var e=A.createElement("boundtest");e.style.width="50px",e.style.display="block",e.style.fontSize="12px",e.style.letterSpacing="0px",e.style.wordSpacing="0px",A.body.appendChild(e);var t=A.createRange();e.innerHTML="function"==typeof"".repeat?"&#128104;".repeat(10):"";var r=e.firstChild,n=k(r.data).map((function(A){return R(A)})),B=0,s={},o=n.every((function(A,e){t.setStart(r,B),t.setEnd(r,B+A.length);var n=t.getBoundingClientRect();B+=A.length;var o=n.x>s.x||n.y>s.y;return s=n,0===e||o}));return A.body.removeChild(e),o}(document);return Object.defineProperty(Kn,"SUPPORT_WORD_BREAKING",{value:A}),A},get SUPPORT_SVG_DRAWING(){var A=function(A){var e=new Image,t=A.createElement("canvas"),r=t.getContext("2d");if(!r)return!1;e.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{r.drawImage(e,0,0),t.toDataURL()}catch(A){return!1}return!0}(document);return Object.defineProperty(Kn,"SUPPORT_SVG_DRAWING",{value:A}),A},get SUPPORT_FOREIGNOBJECT_DRAWING(){var A="function"==typeof Array.from&&"function"==typeof window.fetch?function(A){var e=A.createElement("canvas"),t=100;e.width=t,e.height=t;var r=e.getContext("2d");if(!r)return Promise.reject(!1);r.fillStyle="rgb(0, 255, 0)",r.fillRect(0,0,t,t);var n=new Image,B=e.toDataURL();n.src=B;var s=yn(t,t,0,0,n);return r.fillStyle="red",r.fillRect(0,0,t,t),mn(s).then((function(e){r.drawImage(e,0,0);var n=r.getImageData(0,0,t,t).data;r.fillStyle="red",r.fillRect(0,0,t,t);var s=A.createElement("div");return s.style.backgroundImage="url("+B+")",s.style.height=t+"px",In(n)?mn(yn(t,t,0,0,s)):Promise.reject(!1)})).then((function(A){return r.drawImage(A,0,0),In(r.getImageData(0,0,t,t).data)})).catch((function(){return!1}))}(document):Promise.resolve(!1);return Object.defineProperty(Kn,"SUPPORT_FOREIGNOBJECT_DRAWING",{value:A}),A},get SUPPORT_CORS_IMAGES(){var A=void 0!==(new Image).crossOrigin;return Object.defineProperty(Kn,"SUPPORT_CORS_IMAGES",{value:A}),A},get SUPPORT_RESPONSE_TYPE(){var A="string"==typeof(new XMLHttpRequest).responseType;return Object.defineProperty(Kn,"SUPPORT_RESPONSE_TYPE",{value:A}),A},get SUPPORT_CORS_XHR(){var A="withCredentials"in new XMLHttpRequest;return Object.defineProperty(Kn,"SUPPORT_CORS_XHR",{value:A}),A},get SUPPORT_NATIVE_TEXT_SEGMENTATION(){var A=!("undefined"==typeof Intl||!Intl.Segmenter);return Object.defineProperty(Kn,"SUPPORT_NATIVE_TEXT_SEGMENTATION",{value:A}),A}},vn=function(A,e){this.text=A,this.bounds=e},Ln=function(A,e){var t=e.ownerDocument;if(t){var r=t.createElement("html2canvaswrapper");r.appendChild(e.cloneNode(!0));var n=e.parentNode;if(n){n.replaceChild(r,e);var B=P(A,r);return r.firstChild&&n.replaceChild(r.firstChild,r),B}}return V.EMPTY},bn=function(A,e,t){var r=A.ownerDocument;if(!r)throw new Error("Node has no owner document");var n=r.createRange();return n.setStart(A,e),n.setEnd(A,e+t),n},Dn=function(A){if(Kn.SUPPORT_NATIVE_TEXT_SEGMENTATION){var e=new Intl.Segmenter(void 0,{granularity:"grapheme"});return Array.from(e.segment(A)).map((function(A){return A.segment}))}return function(A){for(var e,t=En(A),r=[];!(e=t.next()).done;)e.value&&r.push(e.value.slice());return r}(A)},Sn=function(A,e){return 0!==e.letterSpacing?Dn(A):function(A,e){if(Kn.SUPPORT_NATIVE_TEXT_SEGMENTATION){var t=new Intl.Segmenter(void 0,{granularity:"word"});return Array.from(t.segment(A)).map((function(A){return A.segment}))}return Mn(A,e)}(A,e)},xn=[32,160,4961,65792,65793,4153,4241],Mn=function(A,e){for(var t,r=function(A,e){var t=k(A),r=GA(t,e),n=r[0],B=r[1],s=r[2],o=t.length,i=0,c=0;return{next:function(){if(c>=o)return{done:!0,value:null};for(var A=IA;c<o&&(A=OA(t,B,n,++c,s))===IA;);if(A!==IA||c===o){var e=new VA(t,A,i,c);return i=c,{value:e,done:!1}}return{done:!0,value:null}}}}(A,{lineBreak:e.lineBreak,wordBreak:"break-word"===e.overflowWrap?"break-word":e.wordBreak}),n=[],B=function(){if(t.value){var A=t.value.slice(),e=k(A),r="";e.forEach((function(A){-1===xn.indexOf(A)?r+=R(A):(r.length&&n.push(r),n.push(R(A)),r="")})),r.length&&n.push(r)}};!(t=r.next()).done;)B();return n},Tn=function(A,e,t){this.text=On(e.data,t.textTransform),this.textBounds=function(A,e,t,r){var n=Sn(e,t),B=[],s=0;return n.forEach((function(e){if(t.textDecorationLine.length||e.trim().length>0)if(Kn.SUPPORT_RANGE_BOUNDS){var n=bn(r,s,e.length).getClientRects();if(n.length>1){var o=Dn(e),i=0;o.forEach((function(e){B.push(new vn(e,V.fromDOMRectList(A,bn(r,i+s,e.length).getClientRects()))),i+=e.length}))}else B.push(new vn(e,V.fromDOMRectList(A,n)))}else{var c=r.splitText(e.length);B.push(new vn(e,Ln(A,r))),r=c}else Kn.SUPPORT_RANGE_BOUNDS||(r=r.splitText(e.length));s+=e.length})),B}(A,this.text,t,e)},On=function(A,e){switch(e){case 1:return A.toLowerCase();case 3:return A.replace(Gn,Vn);case 2:return A.toUpperCase();default:return A}},Gn=/(^|\s|:|-|\(|\))([a-z])/g,Vn=function(A,e,t){return A.length>0?e+t.toUpperCase():A},Pn=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.src=t.currentSrc||t.src,r.intrinsicWidth=t.naturalWidth,r.intrinsicHeight=t.naturalHeight,r.context.cache.addImage(r.src),r}return x(e,A),e}(nn),kn=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.canvas=t,r.intrinsicWidth=t.width,r.intrinsicHeight=t.height,r}return x(e,A),e}(nn),Rn=function(A){function e(e,t){var r=A.call(this,e,t)||this,n=new XMLSerializer,B=P(e,t);return t.setAttribute("width",B.width+"px"),t.setAttribute("height",B.height+"px"),r.svg="data:image/svg+xml,"+encodeURIComponent(n.serializeToString(t)),r.intrinsicWidth=t.width.baseVal.value,r.intrinsicHeight=t.height.baseVal.value,r.context.cache.addImage(r.svg),r}return x(e,A),e}(nn),Nn=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return x(e,A),e}(nn),Jn=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.start=t.start,r.reversed="boolean"==typeof t.reversed&&!0===t.reversed,r}return x(e,A),e}(nn),Xn=[{type:15,flags:0,unit:"px",number:3}],Yn=[{type:16,flags:0,number:50}],Wn="checkbox",_n="radio",Zn="password",qn=707406591,jn=function(A){function e(e,t){var r,n,B,s=A.call(this,e,t)||this;switch(s.type=t.type.toLowerCase(),s.checked=t.checked,s.value=0===(n=(r=t).type===Zn?new Array(r.value.length+1).join("•"):r.value).length?r.placeholder||"":n,s.type!==Wn&&s.type!==_n||(s.styles.backgroundColor=3739148031,s.styles.borderTopColor=s.styles.borderRightColor=s.styles.borderBottomColor=s.styles.borderLeftColor=2779096575,s.styles.borderTopWidth=s.styles.borderRightWidth=s.styles.borderBottomWidth=s.styles.borderLeftWidth=1,s.styles.borderTopStyle=s.styles.borderRightStyle=s.styles.borderBottomStyle=s.styles.borderLeftStyle=1,s.styles.backgroundClip=[0],s.styles.backgroundOrigin=[0],s.bounds=(B=s.bounds).width>B.height?new V(B.left+(B.width-B.height)/2,B.top,B.height,B.height):B.width<B.height?new V(B.left,B.top+(B.height-B.width)/2,B.width,B.width):B),s.type){case Wn:s.styles.borderTopRightRadius=s.styles.borderTopLeftRadius=s.styles.borderBottomRightRadius=s.styles.borderBottomLeftRadius=Xn;break;case _n:s.styles.borderTopRightRadius=s.styles.borderTopLeftRadius=s.styles.borderBottomRightRadius=s.styles.borderBottomLeftRadius=Yn}return s}return x(e,A),e}(nn),zn=function(A){function e(e,t){var r=A.call(this,e,t)||this,n=t.options[t.selectedIndex||0];return r.value=n&&n.text||"",r}return x(e,A),e}(nn),$n=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.value=t.value,r}return x(e,A),e}(nn),AB=function(A){function e(e,t){var r=A.call(this,e,t)||this;r.src=t.src,r.width=parseInt(t.width,10)||0,r.height=parseInt(t.height,10)||0,r.backgroundColor=r.styles.backgroundColor;try{if(t.contentWindow&&t.contentWindow.document&&t.contentWindow.document.documentElement){r.tree=nB(e,t.contentWindow.document.documentElement);var n=t.contentWindow.document.documentElement?At(e,getComputedStyle(t.contentWindow.document.documentElement).backgroundColor):et.TRANSPARENT,B=t.contentWindow.document.body?At(e,getComputedStyle(t.contentWindow.document.body).backgroundColor):et.TRANSPARENT;r.backgroundColor=Ye(n)?Ye(B)?r.styles.backgroundColor:B:n}}catch(A){}return r}return x(e,A),e}(nn),eB=["OL","UL","MENU"],tB=function(A,e,t,r){for(var n=e.firstChild,B=void 0;n;n=B)if(B=n.nextSibling,oB(n)&&n.data.trim().length>0)t.textNodes.push(new Tn(A,n,t.styles));else if(iB(n))if(HB(n)&&n.assignedNodes)n.assignedNodes().forEach((function(e){return tB(A,e,t,r)}));else{var s=rB(A,n);s.styles.isVisible()&&(BB(n,s,r)?s.flags|=4:sB(s.styles)&&(s.flags|=2),-1!==eB.indexOf(n.tagName)&&(s.flags|=8),t.elements.push(s),n.shadowRoot?tB(A,n.shadowRoot,s,r):fB(n)||lB(n)||pB(n)||tB(A,n,s,r))}},rB=function(A,e){return FB(e)?new Pn(A,e):CB(e)?new kn(A,e):lB(e)?new Rn(A,e):gB(e)?new Nn(A,e):QB(e)?new Jn(A,e):wB(e)?new jn(A,e):pB(e)?new zn(A,e):fB(e)?new $n(A,e):hB(e)?new AB(A,e):new nn(A,e)},nB=function(A,e){var t=rB(A,e);return t.flags|=4,tB(A,e,t,t),t},BB=function(A,e,t){return e.styles.isPositionedWithZIndex()||e.styles.opacity<1||e.styles.isTransformed()||uB(A)&&t.styles.isTransparent()},sB=function(A){return A.isPositioned()||A.isFloating()},oB=function(A){return A.nodeType===Node.TEXT_NODE},iB=function(A){return A.nodeType===Node.ELEMENT_NODE},cB=function(A){return iB(A)&&void 0!==A.style&&!aB(A)},aB=function(A){return"object"==typeof A.className},gB=function(A){return"LI"===A.tagName},QB=function(A){return"OL"===A.tagName},wB=function(A){return"INPUT"===A.tagName},lB=function(A){return"svg"===A.tagName},uB=function(A){return"BODY"===A.tagName},CB=function(A){return"CANVAS"===A.tagName},UB=function(A){return"VIDEO"===A.tagName},FB=function(A){return"IMG"===A.tagName},hB=function(A){return"IFRAME"===A.tagName},dB=function(A){return"STYLE"===A.tagName},fB=function(A){return"TEXTAREA"===A.tagName},pB=function(A){return"SELECT"===A.tagName},HB=function(A){return"SLOT"===A.tagName},EB=function(A){return A.tagName.indexOf("-")>0},IB=function(){function A(){this.counters={}}return A.prototype.getCounterValue=function(A){var e=this.counters[A];return e&&e.length?e[e.length-1]:1},A.prototype.getCounterValues=function(A){var e=this.counters[A];return e||[]},A.prototype.pop=function(A){var e=this;A.forEach((function(A){return e.counters[A].pop()}))},A.prototype.parse=function(A){var e=this,t=A.counterIncrement,r=A.counterReset,n=!0;null!==t&&t.forEach((function(A){var t=e.counters[A.counter];t&&0!==A.increment&&(n=!1,t.length||t.push(1),t[Math.max(0,t.length-1)]+=A.increment)}));var B=[];return n&&r.forEach((function(A){var t=e.counters[A.counter];B.push(A.counter),t||(t=e.counters[A.counter]=[]),t.push(A.reset)})),B},A}(),yB={integers:[1e3,900,500,400,100,90,50,40,10,9,5,4,1],values:["M","CM","D","CD","C","XC","L","XL","X","IX","V","IV","I"]},mB={integers:[9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["Ք","Փ","Ւ","Ց","Ր","Տ","Վ","Ս","Ռ","Ջ","Պ","Չ","Ո","Շ","Ն","Յ","Մ","Ճ","Ղ","Ձ","Հ","Կ","Ծ","Խ","Լ","Ի","Ժ","Թ","Ը","Է","Զ","Ե","Դ","Գ","Բ","Ա"]},KB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,400,300,200,100,90,80,70,60,50,40,30,20,19,18,17,16,15,10,9,8,7,6,5,4,3,2,1],values:["י׳","ט׳","ח׳","ז׳","ו׳","ה׳","ד׳","ג׳","ב׳","א׳","ת","ש","ר","ק","צ","פ","ע","ס","נ","מ","ל","כ","יט","יח","יז","טז","טו","י","ט","ח","ז","ו","ה","ד","ג","ב","א"]},vB={integers:[1e4,9e3,8e3,7e3,6e3,5e3,4e3,3e3,2e3,1e3,900,800,700,600,500,400,300,200,100,90,80,70,60,50,40,30,20,10,9,8,7,6,5,4,3,2,1],values:["ჵ","ჰ","ჯ","ჴ","ხ","ჭ","წ","ძ","ც","ჩ","შ","ყ","ღ","ქ","ფ","ჳ","ტ","ს","რ","ჟ","პ","ო","ჲ","ნ","მ","ლ","კ","ი","თ","ჱ","ზ","ვ","ე","დ","გ","ბ","ა"]},LB=function(A,e,t,r,n,B){return A<e||A>t?VB(A,n,B.length>0):r.integers.reduce((function(e,t,n){for(;A>=t;)A-=t,e+=r.values[n];return e}),"")+B},bB=function(A,e,t,r){var n="";do{t||A--,n=r(A)+n,A/=e}while(A*e>=e);return n},DB=function(A,e,t,r,n){var B=t-e+1;return(A<0?"-":"")+(bB(Math.abs(A),B,r,(function(A){return R(Math.floor(A%B)+e)}))+n)},SB=function(A,e,t){void 0===t&&(t=". ");var r=e.length;return bB(Math.abs(A),r,!1,(function(A){return e[Math.floor(A%r)]}))+t},xB=function(A,e,t,r,n,B){if(A<-9999||A>9999)return VB(A,4,n.length>0);var s=Math.abs(A),o=n;if(0===s)return e[0]+o;for(var i=0;s>0&&i<=4;i++){var c=s%10;0===c&&Rr(B,1)&&""!==o?o=e[c]+o:c>1||1===c&&0===i||1===c&&1===i&&Rr(B,2)||1===c&&1===i&&Rr(B,4)&&A>100||1===c&&i>1&&Rr(B,8)?o=e[c]+(i>0?t[i-1]:"")+o:1===c&&i>0&&(o=t[i-1]+o),s=Math.floor(s/10)}return(A<0?r:"")+o},MB="十百千萬",TB="拾佰仟萬",OB="マイナス",GB="마이너스",VB=function(A,e,t){var r=t?". ":"",n=t?"、":"",B=t?", ":"",s=t?" ":"";switch(e){case 0:return"•"+s;case 1:return"◦"+s;case 2:return"◾"+s;case 5:var o=DB(A,48,57,!0,r);return o.length<4?"0"+o:o;case 4:return SB(A,"〇一二三四五六七八九",n);case 6:return LB(A,1,3999,yB,3,r).toLowerCase();case 7:return LB(A,1,3999,yB,3,r);case 8:return DB(A,945,969,!1,r);case 9:return DB(A,97,122,!1,r);case 10:return DB(A,65,90,!1,r);case 11:return DB(A,1632,1641,!0,r);case 12:case 49:return LB(A,1,9999,mB,3,r);case 35:return LB(A,1,9999,mB,3,r).toLowerCase();case 13:return DB(A,2534,2543,!0,r);case 14:case 30:return DB(A,6112,6121,!0,r);case 15:return SB(A,"子丑寅卯辰巳午未申酉戌亥",n);case 16:return SB(A,"甲乙丙丁戊己庚辛壬癸",n);case 17:case 48:return xB(A,"零一二三四五六七八九",MB,"負",n,14);case 47:return xB(A,"零壹貳參肆伍陸柒捌玖",TB,"負",n,15);case 42:return xB(A,"零一二三四五六七八九",MB,"负",n,14);case 41:return xB(A,"零壹贰叁肆伍陆柒捌玖",TB,"负",n,15);case 26:return xB(A,"〇一二三四五六七八九","十百千万",OB,n,0);case 25:return xB(A,"零壱弐参四伍六七八九","拾百千万",OB,n,7);case 31:return xB(A,"영일이삼사오육칠팔구","십백천만",GB,B,7);case 33:return xB(A,"零一二三四五六七八九","十百千萬",GB,B,0);case 32:return xB(A,"零壹貳參四五六七八九","拾百千",GB,B,7);case 18:return DB(A,2406,2415,!0,r);case 20:return LB(A,1,19999,vB,3,r);case 21:return DB(A,2790,2799,!0,r);case 22:return DB(A,2662,2671,!0,r);case 22:return LB(A,1,10999,KB,3,r);case 23:return SB(A,"あいうえおかきくけこさしすせそたちつてとなにぬねのはひふへほまみむめもやゆよらりるれろわゐゑをん");case 24:return SB(A,"いろはにほへとちりぬるをわかよたれそつねならむうゐのおくやまけふこえてあさきゆめみしゑひもせす");case 27:return DB(A,3302,3311,!0,r);case 28:return SB(A,"アイウエオカキクケコサシスセソタチツテトナニヌネノハヒフヘホマミムメモヤユヨラリルレロワヰヱヲン",n);case 29:return SB(A,"イロハニホヘトチリヌルヲワカヨタレソツネナラムウヰノオクヤマケフコエテアサキユメミシヱヒモセス",n);case 34:return DB(A,3792,3801,!0,r);case 37:return DB(A,6160,6169,!0,r);case 38:return DB(A,4160,4169,!0,r);case 39:return DB(A,2918,2927,!0,r);case 40:return DB(A,1776,1785,!0,r);case 43:return DB(A,3046,3055,!0,r);case 44:return DB(A,3174,3183,!0,r);case 45:return DB(A,3664,3673,!0,r);case 46:return DB(A,3872,3881,!0,r);default:return DB(A,48,57,!0,r)}},PB="data-html2canvas-ignore",kB=function(){function A(A,e,t){if(this.context=A,this.options=t,this.scrolledElements=[],this.referenceElement=e,this.counters=new IB,this.quoteDepth=0,!e.ownerDocument)throw new Error("Cloned element does not have an owner document");this.documentElement=this.cloneNode(e.ownerDocument.documentElement,!1)}return A.prototype.toIFrame=function(A,e){var t=this,r=NB(A,e);if(!r.contentWindow)return Promise.reject("Unable to find iframe window");var n=A.defaultView.pageXOffset,B=A.defaultView.pageYOffset,s=r.contentWindow,o=s.document,i=YB(r).then((function(){return T(t,void 0,void 0,(function(){var A,t;return O(this,(function(n){switch(n.label){case 0:return this.scrolledElements.forEach(jB),s&&(s.scrollTo(e.left,e.top),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||s.scrollY===e.top&&s.scrollX===e.left||(this.context.logger.warn("Unable to restore scroll position for cloned document"),this.context.windowBounds=this.context.windowBounds.add(s.scrollX-e.left,s.scrollY-e.top,0,0))),A=this.options.onclone,void 0===(t=this.clonedReferenceElement)?[2,Promise.reject("Error finding the "+this.referenceElement.nodeName+" in the cloned document")]:o.fonts&&o.fonts.ready?[4,o.fonts.ready]:[3,2];case 1:n.sent(),n.label=2;case 2:return/(AppleWebKit)/g.test(navigator.userAgent)?[4,XB(o)]:[3,4];case 3:n.sent(),n.label=4;case 4:return"function"==typeof A?[2,Promise.resolve().then((function(){return A(o,t)})).then((function(){return r}))]:[2,r]}}))}))}));return o.open(),o.write(ZB(document.doctype)+"<html></html>"),qB(this.referenceElement.ownerDocument,n,B),o.replaceChild(o.adoptNode(this.documentElement),o.documentElement),o.close(),i},A.prototype.createElementClone=function(A){if(rn(A,2),CB(A))return this.createCanvasClone(A);if(UB(A))return this.createVideoClone(A);if(dB(A))return this.createStyleClone(A);var e=A.cloneNode(!1);return FB(e)&&(FB(A)&&A.currentSrc&&A.currentSrc!==A.src&&(e.src=A.currentSrc,e.srcset=""),"lazy"===e.loading&&(e.loading="eager")),EB(e)?this.createCustomElementClone(e):e},A.prototype.createCustomElementClone=function(A){var e=document.createElement("html2canvascustomelement");return _B(A.style,e),e},A.prototype.createStyleClone=function(A){try{var e=A.sheet;if(e&&e.cssRules){var t=[].slice.call(e.cssRules,0).reduce((function(A,e){return e&&"string"==typeof e.cssText?A+e.cssText:A}),""),r=A.cloneNode(!1);return r.textContent=t,r}}catch(A){if(this.context.logger.error("Unable to access cssRules property",A),"SecurityError"!==A.name)throw A}return A.cloneNode(!1)},A.prototype.createCanvasClone=function(A){var e;if(this.options.inlineImages&&A.ownerDocument){var t=A.ownerDocument.createElement("img");try{return t.src=A.toDataURL(),t}catch(e){this.context.logger.info("Unable to inline canvas contents, canvas is tainted",A)}}var r=A.cloneNode(!1);try{r.width=A.width,r.height=A.height;var n=A.getContext("2d"),B=r.getContext("2d");if(B)if(!this.options.allowTaint&&n)B.putImageData(n.getImageData(0,0,A.width,A.height),0,0);else{var s=null!==(e=A.getContext("webgl2"))&&void 0!==e?e:A.getContext("webgl");if(s){var o=s.getContextAttributes();!1===(null==o?void 0:o.preserveDrawingBuffer)&&this.context.logger.warn("Unable to clone WebGL context as it has preserveDrawingBuffer=false",A)}B.drawImage(A,0,0)}return r}catch(e){this.context.logger.info("Unable to clone canvas as it is tainted",A)}return r},A.prototype.createVideoClone=function(A){var e=A.ownerDocument.createElement("canvas");e.width=A.offsetWidth,e.height=A.offsetHeight;var t=e.getContext("2d");try{return t&&(t.drawImage(A,0,0,e.width,e.height),this.options.allowTaint||t.getImageData(0,0,e.width,e.height)),e}catch(e){this.context.logger.info("Unable to clone video as it is tainted",A)}var r=A.ownerDocument.createElement("canvas");return r.width=A.offsetWidth,r.height=A.offsetHeight,r},A.prototype.appendChildNode=function(A,e,t){iB(e)&&("SCRIPT"===e.tagName||e.hasAttribute(PB)||"function"==typeof this.options.ignoreElements&&this.options.ignoreElements(e))||this.options.copyStyles&&iB(e)&&dB(e)||A.appendChild(this.cloneNode(e,t))},A.prototype.cloneChildNodes=function(A,e,t){for(var r=this,n=A.shadowRoot?A.shadowRoot.firstChild:A.firstChild;n;n=n.nextSibling)if(iB(n)&&HB(n)&&"function"==typeof n.assignedNodes){var B=n.assignedNodes();B.length&&B.forEach((function(A){return r.appendChildNode(e,A,t)}))}else this.appendChildNode(e,n,t)},A.prototype.cloneNode=function(A,e){if(oB(A))return document.createTextNode(A.data);if(!A.ownerDocument)return A.cloneNode(!1);var t=A.ownerDocument.defaultView;if(t&&iB(A)&&(cB(A)||aB(A))){var r=this.createElementClone(A);r.style.transitionProperty="none";var n=t.getComputedStyle(A),B=t.getComputedStyle(A,":before"),s=t.getComputedStyle(A,":after");this.referenceElement===A&&cB(r)&&(this.clonedReferenceElement=r),uB(r)&&es(r);var o=this.counters.parse(new en(this.context,n)),i=this.resolvePseudoContent(A,r,B,ln.BEFORE);EB(A)&&(e=!0),UB(A)||this.cloneChildNodes(A,r,e),i&&r.insertBefore(i,r.firstChild);var c=this.resolvePseudoContent(A,r,s,ln.AFTER);return c&&r.appendChild(c),this.counters.pop(o),(n&&(this.options.copyStyles||aB(A))&&!hB(A)||e)&&_B(n,r),0===A.scrollTop&&0===A.scrollLeft||this.scrolledElements.push([r,A.scrollLeft,A.scrollTop]),(fB(A)||pB(A))&&(fB(r)||pB(r))&&(r.value=A.value),r}return A.cloneNode(!1)},A.prototype.resolvePseudoContent=function(A,e,t,r){var n=this;if(t){var B=t.content,s=e.ownerDocument;if(s&&B&&"none"!==B&&"-moz-alt-content"!==B&&"none"!==t.display){this.counters.parse(new en(this.context,t));var o=new An(this.context,t),i=s.createElement("html2canvaspseudoelement");_B(t,i),o.content.forEach((function(e){if(0===e.type)i.appendChild(s.createTextNode(e.value));else if(22===e.type){var t=s.createElement("img");t.src=e.value,t.style.opacity="1",i.appendChild(t)}else if(18===e.type){if("attr"===e.name){var r=e.values.filter(Ee);r.length&&i.appendChild(s.createTextNode(A.getAttribute(r[0].value)||""))}else if("counter"===e.name){var B=e.values.filter(Ke),c=B[1];if((Q=B[0])&&Ee(Q)){var a=n.counters.getCounterValue(Q.value),g=c&&Ee(c)?ir.parse(n.context,c.value):3;i.appendChild(s.createTextNode(VB(a,g,!1)))}}else if("counters"===e.name){var Q,w=e.values.filter(Ke),l=w[1];c=w[2];if((Q=w[0])&&Ee(Q)){var u=n.counters.getCounterValues(Q.value),C=c&&Ee(c)?ir.parse(n.context,c.value):3,U=l&&0===l.type?l.value:"",F=u.map((function(A){return VB(A,C,!1)})).join(U);i.appendChild(s.createTextNode(F))}}}else if(20===e.type)switch(e.value){case"open-quote":i.appendChild(s.createTextNode(_r(o.quotes,n.quoteDepth++,!0)));break;case"close-quote":i.appendChild(s.createTextNode(_r(o.quotes,--n.quoteDepth,!1)));break;default:i.appendChild(s.createTextNode(e.value))}})),i.className=zB+" "+$B;var c=r===ln.BEFORE?" "+zB:" "+$B;return aB(e)?e.className.baseValue+=c:e.className+=c,i}}},A.destroy=function(A){return!!A.parentNode&&(A.parentNode.removeChild(A),!0)},A}();!function(A){A[A.BEFORE=0]="BEFORE",A[A.AFTER=1]="AFTER"}(ln||(ln={}));var RB,NB=function(A,e){var t=A.createElement("iframe");return t.className="html2canvas-container",t.style.visibility="hidden",t.style.position="fixed",t.style.left="-10000px",t.style.top="0px",t.style.border="0",t.width=e.width.toString(),t.height=e.height.toString(),t.scrolling="no",t.setAttribute(PB,"true"),A.body.appendChild(t),t},JB=function(A){return new Promise((function(e){A.complete?e():A.src?(A.onload=e,A.onerror=e):e()}))},XB=function(A){return Promise.all([].slice.call(A.images,0).map(JB))},YB=function(A){return new Promise((function(e,t){var r=A.contentWindow;if(!r)return t("No window assigned for iframe");var n=r.document;r.onload=A.onload=function(){r.onload=A.onload=null;var t=setInterval((function(){n.body.childNodes.length>0&&"complete"===n.readyState&&(clearInterval(t),e(A))}),50)}}))},WB=["all","d","content"],_B=function(A,e){for(var t=A.length-1;t>=0;t--){var r=A.item(t);-1===WB.indexOf(r)&&e.style.setProperty(r,A.getPropertyValue(r))}return e},ZB=function(A){var e="";return A&&(e+="<!DOCTYPE ",A.name&&(e+=A.name),A.internalSubset&&(e+=A.internalSubset),A.publicId&&(e+='"'+A.publicId+'"'),A.systemId&&(e+='"'+A.systemId+'"'),e+=">"),e},qB=function(A,e,t){A&&A.defaultView&&(e!==A.defaultView.pageXOffset||t!==A.defaultView.pageYOffset)&&A.defaultView.scrollTo(e,t)},jB=function(A){var e=A[0],t=A[2];e.scrollLeft=A[1],e.scrollTop=t},zB="___html2canvas___pseudoelement_before",$B="___html2canvas___pseudoelement_after",As='{\n    content: "" !important;\n    display: none !important;\n}',es=function(A){ts(A,"."+zB+":before"+As+"\n         ."+$B+":after"+As)},ts=function(A,e){var t=A.ownerDocument;if(t){var r=t.createElement("style");r.textContent=e,A.appendChild(r)}},rs=function(){function A(){}return A.getOrigin=function(e){var t=A._link;return t?(t.href=e,t.href=t.href,t.protocol+t.hostname+t.port):"about:blank"},A.isSameOrigin=function(e){return A.getOrigin(e)===A._origin},A.setContext=function(e){A._link=e.document.createElement("a"),A._origin=A.getOrigin(e.location.href)},A._origin="about:blank",A}(),ns=function(){function A(A,e){this.context=A,this._options=e,this._cache={}}return A.prototype.addImage=function(A){var e=Promise.resolve();return this.has(A)?e:gs(A)||is(A)?((this._cache[A]=this.loadImage(A)).catch((function(){})),e):e},A.prototype.match=function(A){return this._cache[A]},A.prototype.loadImage=function(A){return T(this,void 0,void 0,(function(){var e,t,r,n,B=this;return O(this,(function(s){switch(s.label){case 0:return e=rs.isSameOrigin(A),t=!cs(A)&&!0===this._options.useCORS&&Kn.SUPPORT_CORS_IMAGES&&!e,r=!cs(A)&&!e&&!gs(A)&&"string"==typeof this._options.proxy&&Kn.SUPPORT_CORS_XHR&&!t,e||!1!==this._options.allowTaint||cs(A)||gs(A)||r||t?(n=A,r?[4,this.proxy(n)]:[3,2]):[2];case 1:n=s.sent(),s.label=2;case 2:return this.context.logger.debug("Added image "+A.substring(0,256)),[4,new Promise((function(A,e){var r=new Image;r.onload=function(){return A(r)},r.onerror=e,(as(n)||t)&&(r.crossOrigin="anonymous"),r.src=n,!0===r.complete&&setTimeout((function(){return A(r)}),500),B._options.imageTimeout>0&&setTimeout((function(){return e("Timed out ("+B._options.imageTimeout+"ms) loading image")}),B._options.imageTimeout)}))];case 3:return[2,s.sent()]}}))}))},A.prototype.has=function(A){return void 0!==this._cache[A]},A.prototype.keys=function(){return Promise.resolve(Object.keys(this._cache))},A.prototype.proxy=function(A){var e=this,t=this._options.proxy;if(!t)throw new Error("No proxy defined");var r=A.substring(0,256);return new Promise((function(n,B){var s=Kn.SUPPORT_RESPONSE_TYPE?"blob":"text",o=new XMLHttpRequest;o.onload=function(){if(200===o.status)if("text"===s)n(o.response);else{var A=new FileReader;A.addEventListener("load",(function(){return n(A.result)}),!1),A.addEventListener("error",(function(A){return B(A)}),!1),A.readAsDataURL(o.response)}else B("Failed to proxy resource "+r+" with status code "+o.status)},o.onerror=B;var i=t.indexOf("?")>-1?"&":"?";if(o.open("GET",""+t+i+"url="+encodeURIComponent(A)+"&responseType="+s),"text"!==s&&o instanceof XMLHttpRequest&&(o.responseType=s),e._options.imageTimeout){var c=e._options.imageTimeout;o.timeout=c,o.ontimeout=function(){return B("Timed out ("+c+"ms) proxying "+r)}}o.send()}))},A}(),Bs=/^data:image\/svg\+xml/i,ss=/^data:image\/.*;base64,/i,os=/^data:image\/.*/i,is=function(A){return Kn.SUPPORT_SVG_DRAWING||!Qs(A)},cs=function(A){return os.test(A)},as=function(A){return ss.test(A)},gs=function(A){return"blob"===A.substr(0,4)},Qs=function(A){return"svg"===A.substr(-3).toLowerCase()||Bs.test(A)},ws=function(){function A(A,e){this.type=0,this.x=A,this.y=e}return A.prototype.add=function(e,t){return new A(this.x+e,this.y+t)},A}(),ls=function(A,e,t){return new ws(A.x+(e.x-A.x)*t,A.y+(e.y-A.y)*t)},us=function(){function A(A,e,t,r){this.type=1,this.start=A,this.startControl=e,this.endControl=t,this.end=r}return A.prototype.subdivide=function(e,t){var r=ls(this.start,this.startControl,e),n=ls(this.startControl,this.endControl,e),B=ls(this.endControl,this.end,e),s=ls(r,n,e),o=ls(n,B,e),i=ls(s,o,e);return t?new A(this.start,r,s,i):new A(i,o,B,this.end)},A.prototype.add=function(e,t){return new A(this.start.add(e,t),this.startControl.add(e,t),this.endControl.add(e,t),this.end.add(e,t))},A.prototype.reverse=function(){return new A(this.end,this.endControl,this.startControl,this.start)},A}(),Cs=function(A){return 1===A.type},Us=function(A){var e=A.styles,t=A.bounds,r=Oe(e.borderTopLeftRadius,t.width,t.height),n=r[0],B=r[1],s=Oe(e.borderTopRightRadius,t.width,t.height),o=s[0],i=s[1],c=Oe(e.borderBottomRightRadius,t.width,t.height),a=c[0],g=c[1],Q=Oe(e.borderBottomLeftRadius,t.width,t.height),w=Q[0],l=Q[1],u=[];u.push((n+o)/t.width),u.push((w+a)/t.width),u.push((B+l)/t.height),u.push((i+g)/t.height);var C=Math.max.apply(Math,u);C>1&&(n/=C,B/=C,o/=C,i/=C,a/=C,g/=C,w/=C,l/=C);var U=t.width-o,F=t.height-g,h=t.width-a,d=t.height-l,f=e.borderTopWidth,p=e.borderRightWidth,H=e.borderBottomWidth,E=e.borderLeftWidth,I=Ge(e.paddingTop,A.bounds.width),y=Ge(e.paddingRight,A.bounds.width),m=Ge(e.paddingBottom,A.bounds.width),K=Ge(e.paddingLeft,A.bounds.width);this.topLeftBorderDoubleOuterBox=n>0||B>0?Fs(t.left+E/3,t.top+f/3,n-E/3,B-f/3,RB.TOP_LEFT):new ws(t.left+E/3,t.top+f/3),this.topRightBorderDoubleOuterBox=n>0||B>0?Fs(t.left+U,t.top+f/3,o-p/3,i-f/3,RB.TOP_RIGHT):new ws(t.left+t.width-p/3,t.top+f/3),this.bottomRightBorderDoubleOuterBox=a>0||g>0?Fs(t.left+h,t.top+F,a-p/3,g-H/3,RB.BOTTOM_RIGHT):new ws(t.left+t.width-p/3,t.top+t.height-H/3),this.bottomLeftBorderDoubleOuterBox=w>0||l>0?Fs(t.left+E/3,t.top+d,w-E/3,l-H/3,RB.BOTTOM_LEFT):new ws(t.left+E/3,t.top+t.height-H/3),this.topLeftBorderDoubleInnerBox=n>0||B>0?Fs(t.left+2*E/3,t.top+2*f/3,n-2*E/3,B-2*f/3,RB.TOP_LEFT):new ws(t.left+2*E/3,t.top+2*f/3),this.topRightBorderDoubleInnerBox=n>0||B>0?Fs(t.left+U,t.top+2*f/3,o-2*p/3,i-2*f/3,RB.TOP_RIGHT):new ws(t.left+t.width-2*p/3,t.top+2*f/3),this.bottomRightBorderDoubleInnerBox=a>0||g>0?Fs(t.left+h,t.top+F,a-2*p/3,g-2*H/3,RB.BOTTOM_RIGHT):new ws(t.left+t.width-2*p/3,t.top+t.height-2*H/3),this.bottomLeftBorderDoubleInnerBox=w>0||l>0?Fs(t.left+2*E/3,t.top+d,w-2*E/3,l-2*H/3,RB.BOTTOM_LEFT):new ws(t.left+2*E/3,t.top+t.height-2*H/3),this.topLeftBorderStroke=n>0||B>0?Fs(t.left+E/2,t.top+f/2,n-E/2,B-f/2,RB.TOP_LEFT):new ws(t.left+E/2,t.top+f/2),this.topRightBorderStroke=n>0||B>0?Fs(t.left+U,t.top+f/2,o-p/2,i-f/2,RB.TOP_RIGHT):new ws(t.left+t.width-p/2,t.top+f/2),this.bottomRightBorderStroke=a>0||g>0?Fs(t.left+h,t.top+F,a-p/2,g-H/2,RB.BOTTOM_RIGHT):new ws(t.left+t.width-p/2,t.top+t.height-H/2),this.bottomLeftBorderStroke=w>0||l>0?Fs(t.left+E/2,t.top+d,w-E/2,l-H/2,RB.BOTTOM_LEFT):new ws(t.left+E/2,t.top+t.height-H/2),this.topLeftBorderBox=n>0||B>0?Fs(t.left,t.top,n,B,RB.TOP_LEFT):new ws(t.left,t.top),this.topRightBorderBox=o>0||i>0?Fs(t.left+U,t.top,o,i,RB.TOP_RIGHT):new ws(t.left+t.width,t.top),this.bottomRightBorderBox=a>0||g>0?Fs(t.left+h,t.top+F,a,g,RB.BOTTOM_RIGHT):new ws(t.left+t.width,t.top+t.height),this.bottomLeftBorderBox=w>0||l>0?Fs(t.left,t.top+d,w,l,RB.BOTTOM_LEFT):new ws(t.left,t.top+t.height),this.topLeftPaddingBox=n>0||B>0?Fs(t.left+E,t.top+f,Math.max(0,n-E),Math.max(0,B-f),RB.TOP_LEFT):new ws(t.left+E,t.top+f),this.topRightPaddingBox=o>0||i>0?Fs(t.left+Math.min(U,t.width-p),t.top+f,U>t.width+p?0:Math.max(0,o-p),Math.max(0,i-f),RB.TOP_RIGHT):new ws(t.left+t.width-p,t.top+f),this.bottomRightPaddingBox=a>0||g>0?Fs(t.left+Math.min(h,t.width-E),t.top+Math.min(F,t.height-H),Math.max(0,a-p),Math.max(0,g-H),RB.BOTTOM_RIGHT):new ws(t.left+t.width-p,t.top+t.height-H),this.bottomLeftPaddingBox=w>0||l>0?Fs(t.left+E,t.top+Math.min(d,t.height-H),Math.max(0,w-E),Math.max(0,l-H),RB.BOTTOM_LEFT):new ws(t.left+E,t.top+t.height-H),this.topLeftContentBox=n>0||B>0?Fs(t.left+E+K,t.top+f+I,Math.max(0,n-(E+K)),Math.max(0,B-(f+I)),RB.TOP_LEFT):new ws(t.left+E+K,t.top+f+I),this.topRightContentBox=o>0||i>0?Fs(t.left+Math.min(U,t.width+E+K),t.top+f+I,U>t.width+E+K?0:o-E+K,i-(f+I),RB.TOP_RIGHT):new ws(t.left+t.width-(p+y),t.top+f+I),this.bottomRightContentBox=a>0||g>0?Fs(t.left+Math.min(h,t.width-(E+K)),t.top+Math.min(F,t.height+f+I),Math.max(0,a-(p+y)),g-(H+m),RB.BOTTOM_RIGHT):new ws(t.left+t.width-(p+y),t.top+t.height-(H+m)),this.bottomLeftContentBox=w>0||l>0?Fs(t.left+E+K,t.top+d,Math.max(0,w-(E+K)),l-(H+m),RB.BOTTOM_LEFT):new ws(t.left+E+K,t.top+t.height-(H+m))};!function(A){A[A.TOP_LEFT=0]="TOP_LEFT",A[A.TOP_RIGHT=1]="TOP_RIGHT",A[A.BOTTOM_RIGHT=2]="BOTTOM_RIGHT",A[A.BOTTOM_LEFT=3]="BOTTOM_LEFT"}(RB||(RB={}));var Fs=function(A,e,t,r,n){var B=(Math.sqrt(2)-1)/3*4,s=t*B,o=r*B,i=A+t,c=e+r;switch(n){case RB.TOP_LEFT:return new us(new ws(A,c),new ws(A,c-o),new ws(i-s,e),new ws(i,e));case RB.TOP_RIGHT:return new us(new ws(A,e),new ws(A+s,e),new ws(i,c-o),new ws(i,c));case RB.BOTTOM_RIGHT:return new us(new ws(i,e),new ws(i,e+o),new ws(A+s,c),new ws(A,c));default:return new us(new ws(i,c),new ws(i-s,c),new ws(A,e+o),new ws(A,e))}},hs=function(A){return[A.topLeftBorderBox,A.topRightBorderBox,A.bottomRightBorderBox,A.bottomLeftBorderBox]},ds=function(A){return[A.topLeftPaddingBox,A.topRightPaddingBox,A.bottomRightPaddingBox,A.bottomLeftPaddingBox]},fs=function(A,e,t){this.offsetX=A,this.offsetY=e,this.matrix=t,this.type=0,this.target=6},ps=function(A,e){this.path=A,this.target=e,this.type=1},Hs=function(A){this.opacity=A,this.type=2,this.target=6},Es=function(A){return 1===A.type},Is=function(A,e){return A.length===e.length&&A.some((function(A,t){return A===e[t]}))},ys=function(A){this.element=A,this.inlineLevel=[],this.nonInlineLevel=[],this.negativeZIndex=[],this.zeroOrAutoZIndexOrTransformedOrOpacity=[],this.positiveZIndex=[],this.nonPositionedFloats=[],this.nonPositionedInlineLevel=[]},ms=function(){function A(A,e){(this.container=A,this.parent=e,this.effects=[],this.curves=new Us(this.container),this.container.styles.opacity<1&&this.effects.push(new Hs(this.container.styles.opacity)),null!==this.container.styles.transform)&&this.effects.push(new fs(this.container.bounds.left+this.container.styles.transformOrigin[0].number,this.container.bounds.top+this.container.styles.transformOrigin[1].number,this.container.styles.transform));if(0!==this.container.styles.overflowX){var t=hs(this.curves),r=ds(this.curves);Is(t,r)?this.effects.push(new ps(t,6)):(this.effects.push(new ps(t,2)),this.effects.push(new ps(r,4)))}}return A.prototype.getEffects=function(A){for(var e=-1===[2,3].indexOf(this.container.styles.position),t=this.parent,r=this.effects.slice(0);t;){var n=t.effects.filter((function(A){return!Es(A)}));if(e||0!==t.container.styles.position||!t.parent){if(r.unshift.apply(r,n),e=-1===[2,3].indexOf(t.container.styles.position),0!==t.container.styles.overflowX){var B=hs(t.curves),s=ds(t.curves);Is(B,s)||r.unshift(new ps(s,6))}}else r.unshift.apply(r,n);t=t.parent}return r.filter((function(e){return Rr(e.target,A)}))},A}(),Ks=function(A,e,t,r){A.container.elements.forEach((function(n){var B=Rr(n.flags,4),s=Rr(n.flags,2),o=new ms(n,A);Rr(n.styles.display,2048)&&r.push(o);var i=Rr(n.flags,8)?[]:r;if(B||s){var c=B||n.styles.isPositioned()?t:e,a=new ys(o);if(n.styles.isPositioned()||n.styles.opacity<1||n.styles.isTransformed()){var g=n.styles.zIndex.order;if(g<0){var Q=0;c.negativeZIndex.some((function(A,e){return g>A.element.container.styles.zIndex.order?(Q=e,!1):Q>0})),c.negativeZIndex.splice(Q,0,a)}else if(g>0){var w=0;c.positiveZIndex.some((function(A,e){return g>=A.element.container.styles.zIndex.order?(w=e+1,!1):w>0})),c.positiveZIndex.splice(w,0,a)}else c.zeroOrAutoZIndexOrTransformedOrOpacity.push(a)}else n.styles.isFloating()?c.nonPositionedFloats.push(a):c.nonPositionedInlineLevel.push(a);Ks(o,a,B?a:t,i)}else n.styles.isInlineLevel()?e.inlineLevel.push(o):e.nonInlineLevel.push(o),Ks(o,e,t,i);Rr(n.flags,8)&&vs(n,i)}))},vs=function(A,e){for(var t=A instanceof Jn?A.start:1,r=A instanceof Jn&&A.reversed,n=0;n<e.length;n++){var B=e[n];B.container instanceof Nn&&"number"==typeof B.container.value&&0!==B.container.value&&(t=B.container.value),B.listValue=VB(t,B.container.styles.listStyleType,!0),t+=r?-1:1}},Ls=function(A,e){switch(e){case 0:return Ds(A.topLeftBorderBox,A.topLeftPaddingBox,A.topRightBorderBox,A.topRightPaddingBox);case 1:return Ds(A.topRightBorderBox,A.topRightPaddingBox,A.bottomRightBorderBox,A.bottomRightPaddingBox);case 2:return Ds(A.bottomRightBorderBox,A.bottomRightPaddingBox,A.bottomLeftBorderBox,A.bottomLeftPaddingBox);default:return Ds(A.bottomLeftBorderBox,A.bottomLeftPaddingBox,A.topLeftBorderBox,A.topLeftPaddingBox)}},bs=function(A,e){var t=[];return Cs(A)?t.push(A.subdivide(.5,!1)):t.push(A),Cs(e)?t.push(e.subdivide(.5,!0)):t.push(e),t},Ds=function(A,e,t,r){var n=[];return Cs(A)?n.push(A.subdivide(.5,!1)):n.push(A),Cs(t)?n.push(t.subdivide(.5,!0)):n.push(t),Cs(r)?n.push(r.subdivide(.5,!0).reverse()):n.push(r),Cs(e)?n.push(e.subdivide(.5,!1).reverse()):n.push(e),n},Ss=function(A){var e=A.styles;return A.bounds.add(e.borderLeftWidth,e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth),-(e.borderTopWidth+e.borderBottomWidth))},xs=function(A){var e=A.styles,t=A.bounds,r=Ge(e.paddingLeft,t.width),n=Ge(e.paddingRight,t.width),B=Ge(e.paddingTop,t.width),s=Ge(e.paddingBottom,t.width);return t.add(r+e.borderLeftWidth,B+e.borderTopWidth,-(e.borderRightWidth+e.borderLeftWidth+r+n),-(e.borderTopWidth+e.borderBottomWidth+B+s))},Ms=function(A,e,t){var r=function(A,e){return 0===A?e.bounds:2===A?xs(e):Ss(e)}(Vs(A.styles.backgroundOrigin,e),A),n=function(A,e){return 0===A?e.bounds:2===A?xs(e):Ss(e)}(Vs(A.styles.backgroundClip,e),A),B=Gs(Vs(A.styles.backgroundSize,e),t,r),s=B[0],o=B[1],i=Oe(Vs(A.styles.backgroundPosition,e),r.width-s,r.height-o);return[Ps(Vs(A.styles.backgroundRepeat,e),i,B,r,n),Math.round(r.left+i[0]),Math.round(r.top+i[1]),s,o]},Ts=function(A){return Ee(A)&&A.value===dt.AUTO},Os=function(A){return"number"==typeof A},Gs=function(A,e,t){var r=e[0],n=e[1],B=e[2],s=A[0],o=A[1];if(!s)return[0,0];if(De(s)&&o&&De(o))return[Ge(s,t.width),Ge(o,t.height)];var i=Os(B);if(Ee(s)&&(s.value===dt.CONTAIN||s.value===dt.COVER))return Os(B)?t.width/t.height<B!=(s.value===dt.COVER)?[t.width,t.width/B]:[t.height*B,t.height]:[t.width,t.height];var c=Os(r),a=Os(n),g=c||a;if(Ts(s)&&(!o||Ts(o)))return c&&a?[r,n]:i||g?g&&i?[c?r:n*B,a?n:r/B]:[c?r:t.width,a?n:t.height]:[t.width,t.height];if(i){var Q=0,w=0;return De(s)?Q=Ge(s,t.width):De(o)&&(w=Ge(o,t.height)),Ts(s)?Q=w*B:o&&!Ts(o)||(w=Q/B),[Q,w]}var l=null,u=null;if(De(s)?l=Ge(s,t.width):o&&De(o)&&(u=Ge(o,t.height)),null===l||o&&!Ts(o)||(u=c&&a?l/r*n:t.height),null!==u&&Ts(s)&&(l=c&&a?u/n*r:t.width),null!==l&&null!==u)return[l,u];throw new Error("Unable to calculate background-size for element")},Vs=function(A,e){var t=A[e];return void 0===t?A[0]:t},Ps=function(A,e,t,r,n){var B=e[0],s=e[1],o=t[0],i=t[1];switch(A){case 2:return[new ws(Math.round(r.left),Math.round(r.top+s)),new ws(Math.round(r.left+r.width),Math.round(r.top+s)),new ws(Math.round(r.left+r.width),Math.round(i+r.top+s)),new ws(Math.round(r.left),Math.round(i+r.top+s))];case 3:return[new ws(Math.round(r.left+B),Math.round(r.top)),new ws(Math.round(r.left+B+o),Math.round(r.top)),new ws(Math.round(r.left+B+o),Math.round(r.height+r.top)),new ws(Math.round(r.left+B),Math.round(r.height+r.top))];case 1:return[new ws(Math.round(r.left+B),Math.round(r.top+s)),new ws(Math.round(r.left+B+o),Math.round(r.top+s)),new ws(Math.round(r.left+B+o),Math.round(r.top+s+i)),new ws(Math.round(r.left+B),Math.round(r.top+s+i))];default:return[new ws(Math.round(n.left),Math.round(n.top)),new ws(Math.round(n.left+n.width),Math.round(n.top)),new ws(Math.round(n.left+n.width),Math.round(n.height+n.top)),new ws(Math.round(n.left),Math.round(n.height+n.top))]}},ks="Hidden Text",Rs=function(){function A(A){this._data={},this._document=A}return A.prototype.parseMetrics=function(A,e){var t=this._document.createElement("div"),r=this._document.createElement("img"),n=this._document.createElement("span"),B=this._document.body;t.style.visibility="hidden",t.style.fontFamily=A,t.style.fontSize=e,t.style.margin="0",t.style.padding="0",t.style.whiteSpace="nowrap",B.appendChild(t),r.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",r.width=1,r.height=1,r.style.margin="0",r.style.padding="0",r.style.verticalAlign="baseline",n.style.fontFamily=A,n.style.fontSize=e,n.style.margin="0",n.style.padding="0",n.appendChild(this._document.createTextNode(ks)),t.appendChild(n),t.appendChild(r);var s=r.offsetTop-n.offsetTop+2;t.removeChild(n),t.appendChild(this._document.createTextNode(ks)),t.style.lineHeight="normal",r.style.verticalAlign="super";var o=r.offsetTop-t.offsetTop+2;return B.removeChild(t),{baseline:s,middle:o}},A.prototype.getMetrics=function(A,e){var t=A+" "+e;return void 0===this._data[t]&&(this._data[t]=this.parseMetrics(A,e)),this._data[t]},A}(),Ns=function(A,e){this.context=A,this.options=e},Js=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r._activeEffects=[],r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),t.canvas||(r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px"),r.fontMetrics=new Rs(document),r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.ctx.textBaseline="bottom",r._activeEffects=[],r.context.logger.debug("Canvas renderer initialized ("+t.width+"x"+t.height+") with scale "+t.scale),r}return x(e,A),e.prototype.applyEffects=function(A){for(var e=this;this._activeEffects.length;)this.popEffect();A.forEach((function(A){return e.applyEffect(A)}))},e.prototype.applyEffect=function(A){this.ctx.save(),function(A){return 2===A.type}(A)&&(this.ctx.globalAlpha=A.opacity),function(A){return 0===A.type}(A)&&(this.ctx.translate(A.offsetX,A.offsetY),this.ctx.transform(A.matrix[0],A.matrix[1],A.matrix[2],A.matrix[3],A.matrix[4],A.matrix[5]),this.ctx.translate(-A.offsetX,-A.offsetY)),Es(A)&&(this.path(A.path),this.ctx.clip()),this._activeEffects.push(A)},e.prototype.popEffect=function(){this._activeEffects.pop(),this.ctx.restore()},e.prototype.renderStack=function(A){return T(this,void 0,void 0,(function(){return O(this,(function(e){switch(e.label){case 0:return A.element.container.styles.isVisible()?[4,this.renderStackContent(A)]:[3,2];case 1:e.sent(),e.label=2;case 2:return[2]}}))}))},e.prototype.renderNode=function(A){return T(this,void 0,void 0,(function(){return O(this,(function(e){switch(e.label){case 0:return Rr(A.container.flags,16),A.container.styles.isVisible()?[4,this.renderNodeBackgroundAndBorders(A)]:[3,3];case 1:return e.sent(),[4,this.renderNodeContent(A)];case 2:e.sent(),e.label=3;case 3:return[2]}}))}))},e.prototype.renderTextWithLetterSpacing=function(A,e,t){var r=this;0===e?this.ctx.fillText(A.text,A.bounds.left,A.bounds.top+t):Dn(A.text).reduce((function(e,n){return r.ctx.fillText(n,e,A.bounds.top+t),e+r.ctx.measureText(n).width}),A.bounds.left)},e.prototype.createFontStyle=function(A){var e=A.fontVariant.filter((function(A){return"normal"===A||"small-caps"===A})).join(""),t=Zs(A.fontFamily).join(", "),r=pe(A.fontSize)?""+A.fontSize.number+A.fontSize.unit:A.fontSize.number+"px";return[[A.fontStyle,e,A.fontWeight,r,t].join(" "),t,r]},e.prototype.renderTextNode=function(A,e){return T(this,void 0,void 0,(function(){var t,r,n,B,s,o,i,c=this;return O(this,(function(a){return t=this.createFontStyle(e),r=t[1],n=t[2],this.ctx.font=t[0],this.ctx.direction=1===e.direction?"rtl":"ltr",this.ctx.textAlign="left",this.ctx.textBaseline="alphabetic",B=this.fontMetrics.getMetrics(r,n),s=B.baseline,o=B.middle,i=e.paintOrder,A.textBounds.forEach((function(A){i.forEach((function(t){switch(t){case 0:c.ctx.fillStyle=We(e.color),c.renderTextWithLetterSpacing(A,e.letterSpacing,s);var r=e.textShadow;r.length&&A.text.trim().length&&(r.slice(0).reverse().forEach((function(t){c.ctx.shadowColor=We(t.color),c.ctx.shadowOffsetX=t.offsetX.number*c.options.scale,c.ctx.shadowOffsetY=t.offsetY.number*c.options.scale,c.ctx.shadowBlur=t.blur.number,c.renderTextWithLetterSpacing(A,e.letterSpacing,s)})),c.ctx.shadowColor="",c.ctx.shadowOffsetX=0,c.ctx.shadowOffsetY=0,c.ctx.shadowBlur=0),e.textDecorationLine.length&&(c.ctx.fillStyle=We(e.textDecorationColor||e.color),e.textDecorationLine.forEach((function(e){switch(e){case 1:c.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top+s),A.bounds.width,1);break;case 2:c.ctx.fillRect(A.bounds.left,Math.round(A.bounds.top),A.bounds.width,1);break;case 3:c.ctx.fillRect(A.bounds.left,Math.ceil(A.bounds.top+o),A.bounds.width,1)}})));break;case 1:e.webkitTextStrokeWidth&&A.text.trim().length&&(c.ctx.strokeStyle=We(e.webkitTextStrokeColor),c.ctx.lineWidth=e.webkitTextStrokeWidth,c.ctx.lineJoin=window.chrome?"miter":"round",c.ctx.strokeText(A.text,A.bounds.left,A.bounds.top+s)),c.ctx.strokeStyle="",c.ctx.lineWidth=0,c.ctx.lineJoin="miter"}}))})),[2]}))}))},e.prototype.renderReplacedElement=function(A,e,t){if(t&&A.intrinsicWidth>0&&A.intrinsicHeight>0){var r=xs(A),n=ds(e);this.path(n),this.ctx.save(),this.ctx.clip(),this.ctx.drawImage(t,0,0,A.intrinsicWidth,A.intrinsicHeight,r.left,r.top,r.width,r.height),this.ctx.restore()}},e.prototype.renderNodeContent=function(A){return T(this,void 0,void 0,(function(){var t,r,n,B,s,o,i,c,a,g,Q,w,l,u,C,U;return O(this,(function(F){switch(F.label){case 0:this.applyEffects(A.getEffects(4)),r=A.curves,n=(t=A.container).styles,B=0,s=t.textNodes,F.label=1;case 1:return B<s.length?[4,this.renderTextNode(s[B],n)]:[3,4];case 2:F.sent(),F.label=3;case 3:return B++,[3,1];case 4:if(!(t instanceof Pn))return[3,8];F.label=5;case 5:return F.trys.push([5,7,,8]),[4,this.context.cache.match(t.src)];case 6:return l=F.sent(),this.renderReplacedElement(t,r,l),[3,8];case 7:return F.sent(),this.context.logger.error("Error loading image "+t.src),[3,8];case 8:if(t instanceof kn&&this.renderReplacedElement(t,r,t.canvas),!(t instanceof Rn))return[3,12];F.label=9;case 9:return F.trys.push([9,11,,12]),[4,this.context.cache.match(t.svg)];case 10:return l=F.sent(),this.renderReplacedElement(t,r,l),[3,12];case 11:return F.sent(),this.context.logger.error("Error loading svg "+t.svg.substring(0,255)),[3,12];case 12:return t instanceof AB&&t.tree?[4,new e(this.context,{scale:this.options.scale,backgroundColor:t.backgroundColor,x:0,y:0,width:t.width,height:t.height}).render(t.tree)]:[3,14];case 13:o=F.sent(),t.width&&t.height&&this.ctx.drawImage(o,0,0,t.width,t.height,t.bounds.left,t.bounds.top,t.bounds.width,t.bounds.height),F.label=14;case 14:if(t instanceof jn&&(i=Math.min(t.bounds.width,t.bounds.height),t.type===Wn?t.checked&&(this.ctx.save(),this.path([new ws(t.bounds.left+.39363*i,t.bounds.top+.79*i),new ws(t.bounds.left+.16*i,t.bounds.top+.5549*i),new ws(t.bounds.left+.27347*i,t.bounds.top+.44071*i),new ws(t.bounds.left+.39694*i,t.bounds.top+.5649*i),new ws(t.bounds.left+.72983*i,t.bounds.top+.23*i),new ws(t.bounds.left+.84*i,t.bounds.top+.34085*i),new ws(t.bounds.left+.39363*i,t.bounds.top+.79*i)]),this.ctx.fillStyle=We(qn),this.ctx.fill(),this.ctx.restore()):t.type===_n&&t.checked&&(this.ctx.save(),this.ctx.beginPath(),this.ctx.arc(t.bounds.left+i/2,t.bounds.top+i/2,i/4,0,2*Math.PI,!0),this.ctx.fillStyle=We(qn),this.ctx.fill(),this.ctx.restore())),Xs(t)&&t.value.length){switch(c=this.createFontStyle(n),a=this.fontMetrics.getMetrics(C=c[0],c[1]).baseline,this.ctx.font=C,this.ctx.fillStyle=We(n.color),this.ctx.textBaseline="alphabetic",this.ctx.textAlign=Ws(t.styles.textAlign),U=xs(t),g=0,t.styles.textAlign){case 1:g+=U.width/2;break;case 2:g+=U.width}Q=U.add(g,0,0,-U.height/2+1),this.ctx.save(),this.path([new ws(U.left,U.top),new ws(U.left+U.width,U.top),new ws(U.left+U.width,U.top+U.height),new ws(U.left,U.top+U.height)]),this.ctx.clip(),this.renderTextWithLetterSpacing(new vn(t.value,Q),n.letterSpacing,a),this.ctx.restore(),this.ctx.textBaseline="alphabetic",this.ctx.textAlign="left"}if(!Rr(t.styles.display,2048))return[3,20];if(null===t.styles.listStyleImage)return[3,19];if(0!==(w=t.styles.listStyleImage).type)return[3,18];l=void 0,u=w.url,F.label=15;case 15:return F.trys.push([15,17,,18]),[4,this.context.cache.match(u)];case 16:return l=F.sent(),this.ctx.drawImage(l,t.bounds.left-(l.width+10),t.bounds.top),[3,18];case 17:return F.sent(),this.context.logger.error("Error loading list-style-image "+u),[3,18];case 18:return[3,20];case 19:A.listValue&&-1!==t.styles.listStyleType&&(C=this.createFontStyle(n)[0],this.ctx.font=C,this.ctx.fillStyle=We(n.color),this.ctx.textBaseline="middle",this.ctx.textAlign="right",U=new V(t.bounds.left,t.bounds.top+Ge(t.styles.paddingTop,t.bounds.width),t.bounds.width,Br(n.lineHeight,n.fontSize.number)/2+1),this.renderTextWithLetterSpacing(new vn(A.listValue,U),n.letterSpacing,Br(n.lineHeight,n.fontSize.number)/2+2),this.ctx.textBaseline="bottom",this.ctx.textAlign="left"),F.label=20;case 20:return[2]}}))}))},e.prototype.renderStackContent=function(A){return T(this,void 0,void 0,(function(){var e,t,r,n,B,s,o,i,c,a,g,Q,w,l;return O(this,(function(u){switch(u.label){case 0:return Rr(A.element.container.flags,16),[4,this.renderNodeBackgroundAndBorders(A.element)];case 1:u.sent(),e=0,t=A.negativeZIndex,u.label=2;case 2:return e<t.length?[4,this.renderStack(t[e])]:[3,5];case 3:u.sent(),u.label=4;case 4:return e++,[3,2];case 5:return[4,this.renderNodeContent(A.element)];case 6:u.sent(),r=0,n=A.nonInlineLevel,u.label=7;case 7:return r<n.length?[4,this.renderNode(n[r])]:[3,10];case 8:u.sent(),u.label=9;case 9:return r++,[3,7];case 10:B=0,s=A.nonPositionedFloats,u.label=11;case 11:return B<s.length?[4,this.renderStack(s[B])]:[3,14];case 12:u.sent(),u.label=13;case 13:return B++,[3,11];case 14:o=0,i=A.nonPositionedInlineLevel,u.label=15;case 15:return o<i.length?[4,this.renderStack(i[o])]:[3,18];case 16:u.sent(),u.label=17;case 17:return o++,[3,15];case 18:c=0,a=A.inlineLevel,u.label=19;case 19:return c<a.length?[4,this.renderNode(a[c])]:[3,22];case 20:u.sent(),u.label=21;case 21:return c++,[3,19];case 22:g=0,Q=A.zeroOrAutoZIndexOrTransformedOrOpacity,u.label=23;case 23:return g<Q.length?[4,this.renderStack(Q[g])]:[3,26];case 24:u.sent(),u.label=25;case 25:return g++,[3,23];case 26:w=0,l=A.positiveZIndex,u.label=27;case 27:return w<l.length?[4,this.renderStack(l[w])]:[3,30];case 28:u.sent(),u.label=29;case 29:return w++,[3,27];case 30:return[2]}}))}))},e.prototype.mask=function(A){this.ctx.beginPath(),this.ctx.moveTo(0,0),this.ctx.lineTo(this.canvas.width,0),this.ctx.lineTo(this.canvas.width,this.canvas.height),this.ctx.lineTo(0,this.canvas.height),this.ctx.lineTo(0,0),this.formatPath(A.slice(0).reverse()),this.ctx.closePath()},e.prototype.path=function(A){this.ctx.beginPath(),this.formatPath(A),this.ctx.closePath()},e.prototype.formatPath=function(A){var e=this;A.forEach((function(A,t){var r=Cs(A)?A.start:A;0===t?e.ctx.moveTo(r.x,r.y):e.ctx.lineTo(r.x,r.y),Cs(A)&&e.ctx.bezierCurveTo(A.startControl.x,A.startControl.y,A.endControl.x,A.endControl.y,A.end.x,A.end.y)}))},e.prototype.renderRepeat=function(A,e,t,r){this.path(A),this.ctx.fillStyle=e,this.ctx.translate(t,r),this.ctx.fill(),this.ctx.translate(-t,-r)},e.prototype.resizeImage=function(A,e,t){var r;if(A.width===e&&A.height===t)return A;var n=(null!==(r=this.canvas.ownerDocument)&&void 0!==r?r:document).createElement("canvas");return n.width=Math.max(1,e),n.height=Math.max(1,t),n.getContext("2d").drawImage(A,0,0,A.width,A.height,0,0,e,t),n},e.prototype.renderBackgroundImage=function(A){return T(this,void 0,void 0,(function(){var e,t,r,n,B;return O(this,(function(s){switch(s.label){case 0:e=A.styles.backgroundImage.length-1,t=function(t){var n,B,s,o,i,c,a,g,Q,w,l,u,C,U,F,h,d,f,p,H,E,I,y,m,K,v,L,b,D,S,x;return O(this,(function(M){switch(M.label){case 0:if(0!==t.type)return[3,5];n=void 0,B=t.url,M.label=1;case 1:return M.trys.push([1,3,,4]),[4,r.context.cache.match(B)];case 2:return n=M.sent(),[3,4];case 3:return M.sent(),r.context.logger.error("Error loading background-image "+B),[3,4];case 4:return n&&(s=Ms(A,e,[n.width,n.height,n.width/n.height]),h=s[0],I=s[1],y=s[2],U=r.ctx.createPattern(r.resizeImage(n,p=s[3],H=s[4]),"repeat"),r.renderRepeat(h,U,I,y)),[3,6];case 5:1===t.type?(o=Ms(A,e,[null,null,null]),h=o[0],I=o[1],y=o[2],i=st(t.angle,p=o[3],H=o[4]),c=i[0],a=i[1],g=i[2],Q=i[3],w=i[4],(l=document.createElement("canvas")).width=p,l.height=H,u=l.getContext("2d"),C=u.createLinearGradient(a,Q,g,w),Bt(t.stops,c).forEach((function(A){return C.addColorStop(A.stop,We(A.color))})),u.fillStyle=C,u.fillRect(0,0,p,H),p>0&&H>0&&(U=r.ctx.createPattern(l,"repeat"),r.renderRepeat(h,U,I,y))):function(A){return 2===A.type}(t)&&(F=Ms(A,e,[null,null,null]),h=F[0],d=F[1],f=F[2],H=F[4],I=Ge((E=0===t.position.length?[Me]:t.position)[0],p=F[3]),y=Ge(E[E.length-1],H),m=function(A,e,t,r,n){var B=0,s=0;switch(A.size){case 0:0===A.shape?B=s=Math.min(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(B=Math.min(Math.abs(e),Math.abs(e-r)),s=Math.min(Math.abs(t),Math.abs(t-n)));break;case 2:if(0===A.shape)B=s=Math.min(ot(e,t),ot(e,t-n),ot(e-r,t),ot(e-r,t-n));else if(1===A.shape){var o=Math.min(Math.abs(t),Math.abs(t-n))/Math.min(Math.abs(e),Math.abs(e-r)),i=it(r,n,e,t,!0);s=o*(B=ot(i[0]-e,(i[1]-t)/o))}break;case 1:0===A.shape?B=s=Math.max(Math.abs(e),Math.abs(e-r),Math.abs(t),Math.abs(t-n)):1===A.shape&&(B=Math.max(Math.abs(e),Math.abs(e-r)),s=Math.max(Math.abs(t),Math.abs(t-n)));break;case 3:if(0===A.shape)B=s=Math.max(ot(e,t),ot(e,t-n),ot(e-r,t),ot(e-r,t-n));else if(1===A.shape){o=Math.max(Math.abs(t),Math.abs(t-n))/Math.max(Math.abs(e),Math.abs(e-r));var c=it(r,n,e,t,!1);s=o*(B=ot(c[0]-e,(c[1]-t)/o))}}return Array.isArray(A.size)&&(B=Ge(A.size[0],r),s=2===A.size.length?Ge(A.size[1],n):B),[B,s]}(t,I,y,p,H),v=m[1],(K=m[0])>0&&v>0&&(L=r.ctx.createRadialGradient(d+I,f+y,0,d+I,f+y,K),Bt(t.stops,2*K).forEach((function(A){return L.addColorStop(A.stop,We(A.color))})),r.path(h),r.ctx.fillStyle=L,K!==v?(b=A.bounds.left+.5*A.bounds.width,D=A.bounds.top+.5*A.bounds.height,x=1/(S=v/K),r.ctx.save(),r.ctx.translate(b,D),r.ctx.transform(1,0,0,S,0,0),r.ctx.translate(-b,-D),r.ctx.fillRect(d,x*(f-D)+D,p,H*x),r.ctx.restore()):r.ctx.fill())),M.label=6;case 6:return e--,[2]}}))},r=this,n=0,B=A.styles.backgroundImage.slice(0).reverse(),s.label=1;case 1:return n<B.length?[5,t(B[n])]:[3,4];case 2:s.sent(),s.label=3;case 3:return n++,[3,1];case 4:return[2]}}))}))},e.prototype.renderSolidBorder=function(A,e,t){return T(this,void 0,void 0,(function(){return O(this,(function(r){return this.path(Ls(t,e)),this.ctx.fillStyle=We(A),this.ctx.fill(),[2]}))}))},e.prototype.renderDoubleBorder=function(A,e,t,r){return T(this,void 0,void 0,(function(){var n,B;return O(this,(function(s){switch(s.label){case 0:return e<3?[4,this.renderSolidBorder(A,t,r)]:[3,2];case 1:return s.sent(),[2];case 2:return n=function(A,e){switch(e){case 0:return Ds(A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox,A.topRightBorderBox,A.topRightBorderDoubleOuterBox);case 1:return Ds(A.topRightBorderBox,A.topRightBorderDoubleOuterBox,A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox);case 2:return Ds(A.bottomRightBorderBox,A.bottomRightBorderDoubleOuterBox,A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox);default:return Ds(A.bottomLeftBorderBox,A.bottomLeftBorderDoubleOuterBox,A.topLeftBorderBox,A.topLeftBorderDoubleOuterBox)}}(r,t),this.path(n),this.ctx.fillStyle=We(A),this.ctx.fill(),B=function(A,e){switch(e){case 0:return Ds(A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox,A.topRightBorderDoubleInnerBox,A.topRightPaddingBox);case 1:return Ds(A.topRightBorderDoubleInnerBox,A.topRightPaddingBox,A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox);case 2:return Ds(A.bottomRightBorderDoubleInnerBox,A.bottomRightPaddingBox,A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox);default:return Ds(A.bottomLeftBorderDoubleInnerBox,A.bottomLeftPaddingBox,A.topLeftBorderDoubleInnerBox,A.topLeftPaddingBox)}}(r,t),this.path(B),this.ctx.fill(),[2]}}))}))},e.prototype.renderNodeBackgroundAndBorders=function(A){return T(this,void 0,void 0,(function(){var e,t,r,n,B,s,o,i,c=this;return O(this,(function(a){switch(a.label){case 0:return this.applyEffects(A.getEffects(2)),t=!Ye((e=A.container.styles).backgroundColor)||e.backgroundImage.length,r=[{style:e.borderTopStyle,color:e.borderTopColor,width:e.borderTopWidth},{style:e.borderRightStyle,color:e.borderRightColor,width:e.borderRightWidth},{style:e.borderBottomStyle,color:e.borderBottomColor,width:e.borderBottomWidth},{style:e.borderLeftStyle,color:e.borderLeftColor,width:e.borderLeftWidth}],n=Ys(Vs(e.backgroundClip,0),A.curves),t||e.boxShadow.length?(this.ctx.save(),this.path(n),this.ctx.clip(),Ye(e.backgroundColor)||(this.ctx.fillStyle=We(e.backgroundColor),this.ctx.fill()),[4,this.renderBackgroundImage(A.container)]):[3,2];case 1:a.sent(),this.ctx.restore(),e.boxShadow.slice(0).reverse().forEach((function(e){c.ctx.save();var t,r,n,B,s=hs(A.curves),o=e.inset?0:1e4,i=(t=(e.inset?1:-1)*e.spread.number-o,r=(e.inset?1:-1)*e.spread.number,n=e.spread.number*(e.inset?-2:2),B=e.spread.number*(e.inset?-2:2),s.map((function(A,e){switch(e){case 0:return A.add(t,r);case 1:return A.add(t+n,r);case 2:return A.add(t+n,r+B);case 3:return A.add(t,r+B)}return A})));e.inset?(c.path(s),c.ctx.clip(),c.mask(i)):(c.mask(s),c.ctx.clip(),c.path(i)),c.ctx.shadowOffsetX=e.offsetX.number+o,c.ctx.shadowOffsetY=e.offsetY.number,c.ctx.shadowColor=We(e.color),c.ctx.shadowBlur=e.blur.number,c.ctx.fillStyle=e.inset?We(e.color):"rgba(0,0,0,1)",c.ctx.fill(),c.ctx.restore()})),a.label=2;case 2:B=0,s=0,o=r,a.label=3;case 3:return s<o.length?0!==(i=o[s]).style&&!Ye(i.color)&&i.width>0?2!==i.style?[3,5]:[4,this.renderDashedDottedBorder(i.color,i.width,B,A.curves,2)]:[3,11]:[3,13];case 4:return a.sent(),[3,11];case 5:return 3!==i.style?[3,7]:[4,this.renderDashedDottedBorder(i.color,i.width,B,A.curves,3)];case 6:return a.sent(),[3,11];case 7:return 4!==i.style?[3,9]:[4,this.renderDoubleBorder(i.color,i.width,B,A.curves)];case 8:return a.sent(),[3,11];case 9:return[4,this.renderSolidBorder(i.color,B,A.curves)];case 10:a.sent(),a.label=11;case 11:B++,a.label=12;case 12:return s++,[3,3];case 13:return[2]}}))}))},e.prototype.renderDashedDottedBorder=function(A,e,t,r,n){return T(this,void 0,void 0,(function(){var B,s,o,i,c,a,g,Q,w,l,u,C,U,F,h,d;return O(this,(function(f){return this.ctx.save(),B=function(A,e){switch(e){case 0:return bs(A.topLeftBorderStroke,A.topRightBorderStroke);case 1:return bs(A.topRightBorderStroke,A.bottomRightBorderStroke);case 2:return bs(A.bottomRightBorderStroke,A.bottomLeftBorderStroke);default:return bs(A.bottomLeftBorderStroke,A.topLeftBorderStroke)}}(r,t),s=Ls(r,t),2===n&&(this.path(s),this.ctx.clip()),Cs(s[0])?(o=s[0].start.x,i=s[0].start.y):(o=s[0].x,i=s[0].y),Cs(s[1])?(c=s[1].end.x,a=s[1].end.y):(c=s[1].x,a=s[1].y),g=0===t||2===t?Math.abs(o-c):Math.abs(i-a),this.ctx.beginPath(),this.formatPath(3===n?B:s.slice(0,2)),Q=e<3?3*e:2*e,w=e<3?2*e:e,3===n&&(Q=e,w=e),l=!0,g<=2*Q?l=!1:g<=2*Q+w?(Q*=u=g/(2*Q+w),w*=u):(C=Math.floor((g+w)/(Q+w)),U=(g-C*Q)/(C-1),w=(F=(g-(C+1)*Q)/C)<=0||Math.abs(w-U)<Math.abs(w-F)?U:F),l&&this.ctx.setLineDash(3===n?[0,Q+w]:[Q,w]),3===n?(this.ctx.lineCap="round",this.ctx.lineWidth=e):this.ctx.lineWidth=2*e+1.1,this.ctx.strokeStyle=We(A),this.ctx.stroke(),this.ctx.setLineDash([]),2===n&&(Cs(s[0])&&(h=s[3],d=s[0],this.ctx.beginPath(),this.formatPath([new ws(h.end.x,h.end.y),new ws(d.start.x,d.start.y)]),this.ctx.stroke()),Cs(s[1])&&(h=s[1],d=s[2],this.ctx.beginPath(),this.formatPath([new ws(h.end.x,h.end.y),new ws(d.start.x,d.start.y)]),this.ctx.stroke())),this.ctx.restore(),[2]}))}))},e.prototype.render=function(A){return T(this,void 0,void 0,(function(){return O(this,(function(e){switch(e.label){case 0:return this.options.backgroundColor&&(this.ctx.fillStyle=We(this.options.backgroundColor),this.ctx.fillRect(this.options.x,this.options.y,this.options.width,this.options.height)),t=new ms(A,null),r=new ys(t),Ks(t,r,r,n=[]),vs(t.container,n),[4,this.renderStack(r)];case 1:return e.sent(),this.applyEffects([]),[2,this.canvas]}var t,r,n}))}))},e}(Ns),Xs=function(A){return A instanceof $n||(A instanceof zn||A instanceof jn&&A.type!==_n&&A.type!==Wn)},Ys=function(A,e){switch(A){case 0:return hs(e);case 2:return function(A){return[A.topLeftContentBox,A.topRightContentBox,A.bottomRightContentBox,A.bottomLeftContentBox]}(e);default:return ds(e)}},Ws=function(A){switch(A){case 1:return"center";case 2:return"right";default:return"left"}},_s=["-apple-system","system-ui"],Zs=function(A){return/iPhone OS 15_(0|1)/.test(window.navigator.userAgent)?A.filter((function(A){return-1===_s.indexOf(A)})):A},qs=function(A){function e(e,t){var r=A.call(this,e,t)||this;return r.canvas=t.canvas?t.canvas:document.createElement("canvas"),r.ctx=r.canvas.getContext("2d"),r.options=t,r.canvas.width=Math.floor(t.width*t.scale),r.canvas.height=Math.floor(t.height*t.scale),r.canvas.style.width=t.width+"px",r.canvas.style.height=t.height+"px",r.ctx.scale(r.options.scale,r.options.scale),r.ctx.translate(-t.x,-t.y),r.context.logger.debug("EXPERIMENTAL ForeignObject renderer initialized ("+t.width+"x"+t.height+" at "+t.x+","+t.y+") with scale "+t.scale),r}return x(e,A),e.prototype.render=function(A){return T(this,void 0,void 0,(function(){var e,t;return O(this,(function(r){switch(r.label){case 0:return e=yn(this.options.width*this.options.scale,this.options.height*this.options.scale,this.options.scale,this.options.scale,A),[4,js(e)];case 1:return t=r.sent(),this.options.backgroundColor&&(this.ctx.fillStyle=We(this.options.backgroundColor),this.ctx.fillRect(0,0,this.options.width*this.options.scale,this.options.height*this.options.scale)),this.ctx.drawImage(t,-this.options.x*this.options.scale,-this.options.y*this.options.scale),[2,this.canvas]}}))}))},e}(Ns),js=function(A){return new Promise((function(e,t){var r=new Image;r.onload=function(){e(r)},r.onerror=t,r.src="data:image/svg+xml;charset=utf-8,"+encodeURIComponent((new XMLSerializer).serializeToString(A))}))},zs=function(){function A(A){var e=A.enabled;this.id=A.id,this.enabled=e,this.start=Date.now()}return A.prototype.debug=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.debug?console.debug.apply(console,G([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.getTime=function(){return Date.now()-this.start},A.prototype.info=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&"undefined"!=typeof window&&window.console&&"function"==typeof console.info&&console.info.apply(console,G([this.id,this.getTime()+"ms"],A))},A.prototype.warn=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.warn?console.warn.apply(console,G([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.prototype.error=function(){for(var A=[],e=0;e<arguments.length;e++)A[e]=arguments[e];this.enabled&&("undefined"!=typeof window&&window.console&&"function"==typeof console.error?console.error.apply(console,G([this.id,this.getTime()+"ms"],A)):this.info.apply(this,A))},A.instances={},A}(),$s=function(){function A(e,t){var r;this.windowBounds=t,this.instanceName="#"+A.instanceCount++,this.logger=new zs({id:this.instanceName,enabled:e.logging}),this.cache=null!==(r=e.cache)&&void 0!==r?r:new ns(this,e)}return A.instanceCount=1,A}();"undefined"!=typeof window&&rs.setContext(window);var Ao=function(A,e){return T(void 0,void 0,void 0,(function(){var t,r,n,B,s,o,i,c,a,g,Q,w,l,u,C,U,F,h,d,f,p,H,E,I,y,m,K,v,L,b,D,S,x,T,G,k,R,N;return O(this,(function(O){switch(O.label){case 0:if(!A||"object"!=typeof A)return[2,Promise.reject("Invalid element provided as first argument")];if(!(t=A.ownerDocument))throw new Error("Element is not attached to a Document");if(!(r=t.defaultView))throw new Error("Document is not attached to a Window");return n={allowTaint:null!==(H=e.allowTaint)&&void 0!==H&&H,imageTimeout:null!==(E=e.imageTimeout)&&void 0!==E?E:15e3,proxy:e.proxy,useCORS:null!==(I=e.useCORS)&&void 0!==I&&I},B=M({logging:null===(y=e.logging)||void 0===y||y,cache:e.cache},n),s={windowWidth:null!==(m=e.windowWidth)&&void 0!==m?m:r.innerWidth,windowHeight:null!==(K=e.windowHeight)&&void 0!==K?K:r.innerHeight,scrollX:null!==(v=e.scrollX)&&void 0!==v?v:r.pageXOffset,scrollY:null!==(L=e.scrollY)&&void 0!==L?L:r.pageYOffset},o=new V(s.scrollX,s.scrollY,s.windowWidth,s.windowHeight),i=new $s(B,o),c=null!==(b=e.foreignObjectRendering)&&void 0!==b&&b,a={allowTaint:null!==(D=e.allowTaint)&&void 0!==D&&D,onclone:e.onclone,ignoreElements:e.ignoreElements,inlineImages:c,copyStyles:c},i.logger.debug("Starting document clone with size "+o.width+"x"+o.height+" scrolled to "+-o.left+","+-o.top),g=new kB(i,A,a),(Q=g.clonedReferenceElement)?[4,g.toIFrame(t,o)]:[2,Promise.reject("Unable to find element in cloned iframe")];case 1:return w=O.sent(),l=uB(Q)||"HTML"===Q.tagName?function(A){var e=A.body,t=A.documentElement;if(!e||!t)throw new Error("Unable to get document size");var r=Math.max(Math.max(e.scrollWidth,t.scrollWidth),Math.max(e.offsetWidth,t.offsetWidth),Math.max(e.clientWidth,t.clientWidth)),n=Math.max(Math.max(e.scrollHeight,t.scrollHeight),Math.max(e.offsetHeight,t.offsetHeight),Math.max(e.clientHeight,t.clientHeight));return new V(0,0,r,n)}(Q.ownerDocument):P(i,Q),u=l.width,C=l.height,U=l.left,F=l.top,h=eo(i,Q,e.backgroundColor),d={canvas:e.canvas,backgroundColor:h,scale:null!==(x=null!==(S=e.scale)&&void 0!==S?S:r.devicePixelRatio)&&void 0!==x?x:1,x:(null!==(T=e.x)&&void 0!==T?T:0)+U,y:(null!==(G=e.y)&&void 0!==G?G:0)+F,width:null!==(k=e.width)&&void 0!==k?k:Math.ceil(u),height:null!==(R=e.height)&&void 0!==R?R:Math.ceil(C)},c?(i.logger.debug("Document cloned, using foreign object rendering"),[4,new qs(i,d).render(Q)]):[3,3];case 2:return f=O.sent(),[3,5];case 3:return i.logger.debug("Document cloned, element located at "+U+","+F+" with size "+u+"x"+C+" using computed rendering"),i.logger.debug("Starting DOM parsing"),p=nB(i,Q),h===p.styles.backgroundColor&&(p.styles.backgroundColor=et.TRANSPARENT),i.logger.debug("Starting renderer for element at "+d.x+","+d.y+" with size "+d.width+"x"+d.height),[4,new Js(i,d).render(p)];case 4:f=O.sent(),O.label=5;case 5:return(null===(N=e.removeContainer)||void 0===N||N)&&(kB.destroy(w)||i.logger.error("Cannot detach cloned iframe as it is not in the DOM anymore")),i.logger.debug("Finished rendering"),[2,f]}}))}))},eo=function(A,e,t){var r=e.ownerDocument,n=r.documentElement?At(A,getComputedStyle(r.documentElement).backgroundColor):et.TRANSPARENT,B=r.body?At(A,getComputedStyle(r.body).backgroundColor):et.TRANSPARENT,s="string"==typeof t?At(A,t):null===t?et.TRANSPARENT:4294967295;return e===r.documentElement?Ye(n)?Ye(B)?s:B:n:s};const to=function(A,e){return void 0===e&&(e={}),Ao(A,e)};let ro="";function no(A){if(!A)return"";const e=window.getComputedStyle(A);return function(A){const e=((null==A?void 0:A.backgroundColor)||"").replace(/\s+|rgba\(|\)/g,"").toLowerCase();if(""===e||"transparent"===e||"0"===(null==A?void 0:A.opacity))return!0;const t=e.split(",");return 4===t.length&&"0"===t[3]}(e)?no(A.parentElement):e.backgroundColor}async function Bo(e,t,r,n,B,s){if(!(e=A(e)))return Promise.reject();(await w.getInstance().getVizModule()).exportFromMarkup("<div></div>",{fileName:t,format:r,width:e.clientWidth,height:e.clientHeight,backgroundColor:B||no(e),margin:n||void 0,svgToCanvas:async function(A,t){const r=await function(A){return new Promise(((e,t)=>{const r=A.cloneNode(!0);so(A,r);const n=document.createElement("IFRAME");n.style.cssText="position: absolute; left: -10000px; top: -10000px; visibility: hidden; opacity: 0;",n.onload=()=>{n.contentDocument.body.appendChild(r),n.contentDocument.body.style.margin="0px",e(n)},document.body.appendChild(n)}))}(e),n=r.contentDocument.body;await to(n,{canvas:t,backgroundColor:B,scale:1,width:e.clientWidth,height:e.clientHeight}),document.body.removeChild(r)},onFileSaving:s})}function so(A,e){!function(A,e){const t=window.getComputedStyle(A);for(let A=0;A<t.length;A++){const r=t[A],n=t.getPropertyValue(r);e.style.setProperty(r,n)}}(A,e);const t=A.childNodes,r=e.childNodes,n=t.length;for(let A=0;A<n;A++){const e=t[A];if(1===e.nodeType){so(e,r[A])}}}const oo="dxbl-chart-base";class io extends h{constructor(){super(io.eventName)}}io.eventName=oo+".drawn";class co extends CustomEvent{constructor(A){super(co.eventName,{detail:new wo(A),bubbles:!0,composed:!0,cancelable:!0})}}co.eventName=oo+".tooltip-showing";class ao extends CustomEvent{constructor(A){super(ao.eventName,{detail:new lo(A),bubbles:!0,composed:!0,cancelable:!0})}}ao.eventName=oo+".series-click";class go extends CustomEvent{constructor(A){super(go.eventName,{detail:new uo(A),bubbles:!0,composed:!0,cancelable:!0})}}go.eventName=oo+".selection-changed";class Qo{constructor(A){this.SeriesIndex=A.seriesIndex,this.Tag=A.tag,this.Data=A.data}}class wo extends Qo{constructor(A){super(A),this.Id=A.id}}class lo extends Qo{}class uo extends Qo{constructor(A){super(A),this.IsSeriesSelected=A.isSeriesSelected,this.IsPointSelected=A.isPointSelected}}F.register(io.eventName,(A=>A.detail)),F.register(co.eventName,(A=>A.detail)),F.register(ao.eventName,(A=>A.detail)),F.register(go.eventName,(A=>A.detail)),U();const Co="dxChartLegendElementHasMouseoverEvent";class Uo extends l{get widgetSettingsConverter(){return this._widgetSettingsConverter}get styleHelper(){return this._styleHelper}get selectionController(){return this._selectionController}constructor(){super(),this._isChartUpdating=!1,this.legendMouseOverHandler=this.onLegendMouseOver.bind(this),this._styleHelper=C.getInstance(),this._drawnExecutor=u(this.onChartDrawn.bind(this)),this._serverDrawnExecutor=u(this.dispatchServerDrawnEvent.bind(this)),this._selectionController=new D(this),this._tooltipHelper=new c(this),this._selectionController.setChartType(this.getWidgetTypeName()),this._widgetSettingsConverter=this.createWidgetSettingsConverter()}disposeComponent(){super.disposeComponent(),this._drawnExecutor.reset(),this._serverDrawnExecutor.reset(),this._selectionController.dispose(),this.removeLegendMouseHandler();const A=this.querySelectorAll("."+s);for(let e=0;e<A.length;e++){const t=A[e];t&&B(t)}}resetSelection(){this.selectionController.resetSelection(),this.afterWidgetResolved((A=>{A.clearSelection()}))}selectSeries(A){this.afterWidgetResolved((e=>{const t=e.getSeriesByPos(A);t&&!t.isSelected()&&this.selectionController.toggleSeriesSelection(t)}))}deselectSeries(A){this.afterWidgetResolved((e=>{const t=e.getSeriesByPos(A);(null==t?void 0:t.isSelected())&&this.selectionController.toggleSeriesSelection(t)}))}selectPoints(A,e){this.afterWidgetResolved((t=>{const r=t.getSeriesByPos(A);if(r)for(let A=0;A<e.length;A++){const t=Uo.getPointByData(r,e[A]);t&&!t.isSelected()&&this.selectionController.togglePointSelection(t)}}))}deselectPoints(A,e){this.afterWidgetResolved((t=>{const r=t.getSeriesByPos(A);if(r)for(let A=0;A<e.length;A++){const t=Uo.getPointByData(r,e[A]);(null==t?void 0:t.isSelected())&&this.selectionController.togglePointSelection(t)}}))}onSeriesVisibleChanged(A,e,t){var r;null===(r=this._widgetPromise)||void 0===r||r.then((function(r){const n=H(r,A,e);t?n.show():n.hide()})).catch((A=>console.error(A)))}exportToFile(A,e,t,r){return function(A,e,t,r,n){return Bo(A,e,t,r,n,void 0)}(this,A,e,t,r)}exportToBase64AndGetLength(A,e,t){return async function(A,e,t,r){return new Promise(((n,B)=>{Bo(A,"",e,t,r,(function(A){A.cancel=!0;const e=new FileReader;e.readAsDataURL(A.data),e.onloadend=function(){ro=e.result,n(ro.length)}}))}))}(this,A,e,t)}getBase64ImageChunk(A,e){return async function(A,e){return new Promise(((t,r)=>{t(ro.substring(A,e))}))}(A,e)}clearBase64ImageData(){return async function(){return new Promise(((A,e)=>{ro="",A()}))}()}showTooltipForPoint(A,t){var r;null===(r=this._widgetPromise)||void 0===r||r.then((e=>{const r=e.getSeriesByPos(t);if(!r)return;let n=A[a.defaultSeriesArgumentField];"datetime"===r.argumentType&&"string"==typeof n&&(n=new Date(n));const B=r.getPointsByArg(n),s=(null==B?void 0:B.find((e=>e.originalValue===A[a.defaultSeriesValueField])))||B[0];null==s||s.showTooltip()})).catch((A=>{e(this)||console.error(A)}))}correctTooltipPosition(){this._tooltipHelper.positionTooltip()}hideTooltip(A){this._tooltipHelper.hideTooltip(A)}render(){this.executeClientMethod("render")}onSeriesClick(A,e,t){const r=new ao({seriesIndex:A,data:e,tag:t});this.dispatchEvent(r)}onSelectionChanged(A){const e=new go(A);this.dispatchEvent(e)}onTooltipShowing(A){const e=new co(A);this.dispatchEvent(e)}createWidgetDefaultOptions(){return{...this.widgetSettingsConverter.createDefaultSettings(),palette:this.styleHelper.palette}}processSpecialOptions(A){super.processSpecialOptions(A),this.onBeforeChartOptionsCompleted(A);const e=this.widgetSettingsConverter.convert(A);Object.getOwnPropertyNames(A).forEach((e=>delete A[e])),Object.assign(A,e),this.correctChartElementHeight()}correctChartElementHeight(){const A=this.getWidgetElement();this.style.getPropertyValue("height")||A.style.setProperty("min-height","300px")}onBeforeChartOptionsCompleted(A){this.configureSelectionController(A),this.hasTooltip(A)&&this.addLegendMouseHandler()}configureSelectionController(A){void 0!==A.pointSelectionMode&&this._selectionController.setPointSelectionMode(A.pointSelectionMode)}hasTooltip(A){var e;return!!(null===(e=A.tooltip)||void 0===e?void 0:e.enabled)}addLegendMouseHandler(){const A=this.querySelector("."+g);A&&!A.hasAttribute(Co)&&(A.addEventListener("mouseover",this.legendMouseOverHandler),A.setAttribute(Co,"true"))}removeLegendMouseHandler(){const A=this.querySelector("."+g);null==A||A.removeEventListener("mouseover",this.legendMouseOverHandler),null==A||A.removeAttribute(Co)}onLegendMouseOver(){this._tooltipHelper.hideTooltip(!0)}createWidgetHandlers(){const A=this.selectionController;return{...super.createWidgetHandlers(),onPointClick:this.getChartOnPointClickHandler(),onSeriesClick:this.getChartOnSeriesClickHandler(),onPointSelectionChanged:e=>{A.pointSelectionChanged(e)},onSeriesSelectionChanged:e=>{A.seriesSelectionChanged(e)},onPointHoverChanged:e=>{this._isChartUpdating||A.pointHoverChanged(e)},onSeriesHoverChanged:e=>{this._isChartUpdating||A.seriesHoverChanged(e)},onDrawn:this.getChartOnDrawnHandler(),onOptionChanged:()=>this.onChartOptionChanged(),onTooltipShown:A=>this._tooltipHelper.onTooltipShown(A,this.onTooltipShowing.bind(this)),onTooltipHidden:A=>this._tooltipHelper.onTooltipHidden(A)}}getChartOnDrawnHandler(){return A=>{const e=this._selectionController;!function(A,e,t,r){var n;const B=A.querySelectorAll("."+s);for(let A=0;A<B.length;A++){const s=B[A];if(s){const A=+(null!==(n=s.getAttribute("data-series"))&&void 0!==n?n:""),B=s.getAttribute("data-point");p(s,e,A,B,t,r)}}}(this,A.component,this.getLegendItemType(),e),e.restoreChartSelection(A.component),this.onChartDrawn()}}getChartOnSeriesClickHandler(){}onChartDrawn(){this._drawnExecutor.reset(),this.changeLoadingPanelVisibility(!1),this._serverDrawnExecutor.execute(),this.endChartUpdating(),t(this,Q,!1)}dispatchServerDrawnEvent(){this.dispatchEvent(new io)}onChartOptionChanged(){this._isChartUpdating&&this._drawnExecutor.execute()}onlyContainerSizeChanged(){this.onChartDrawn()}updateWidgetOptions(A,e){this.startChartUpdating(),e.animation={enabled:!1},this.prepareSpecialOptions(e,A),super.updateWidgetOptions(A,e)}prepareSpecialOptions(A,e){var t;const r=a.argumentAxisKey,n=a.visualRangeKey,B=null===(t=A[r])||void 0===t?void 0:t[n],s=e.option(`${r}.${n}`);B&&s&&(A[r][n]=Object.assign(s,B))}getSkippedEmptyOptions(){return["dataSource"]}startChartUpdating(){this._isChartUpdating=!0}endChartUpdating(){this._isChartUpdating=!1}static getPointByData(A,e){let t=e[a.defaultSeriesArgumentField],r=e[a.defaultSeriesValueField];const n=A.valueType;return"datetime"===A.argumentType&&"string"==typeof t&&(t=new Date(t)),"datetime"===n&&"string"==typeof r&&(r=new Date(r).getTime()),A.getPointsByArg(t).find((A=>"datetime"===n?A.originalValue.getTime()===r:A.originalValue===r))}}export{I as C,Uo as D,f as L};
