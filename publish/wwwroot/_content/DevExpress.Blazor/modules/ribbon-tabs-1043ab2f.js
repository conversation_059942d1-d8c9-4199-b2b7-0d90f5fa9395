import{_ as t}from"./tslib.es6-d65164b3.js";import{d as e}from"./dom-554d0cc7.js";import{S as i}from"./scroll-viewer-css-classes-e724f203.js";import{T as s}from"./toolbar-css-classes-d303c118.js";import{T as o}from"./constants-ed9663d1.js";import{u as n,R as l,b as r,d as a,f as c,h,j as d,c as u,s as m,k as b}from"./dom-utils-d057dcaa.js";import{U as p}from"./ribbon-utils-2b6a64cb.js";import{a as f,R as S}from"./layouthelper-67dd777a.js";import{attachEventsForFocusHiding as g}from"./focus-utils-ae044224.js";import{S as v}from"./single-slot-element-base-01d93921.js";import{A as T}from"./events-f873e646.js";import{C}from"./css-classes-c63af734.js";import{n as E}from"./property-4ec0b52d.js";import{e as w}from"./custom-element-267f9a21.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./key-ffa272aa.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./logicaltreehelper-67db40f1.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";var L,B;!function(t){t[t.None=0]="None",t[t.Back=1]="Back",t[t.Forward=2]="Forward"}(L||(L={}));class x{static calculate(t,e,i,s,o,n){const l=e?i.height:i.width,r=e?s.bottom:s.right,a=e?s.top:s.left,c=e?i.bottom:i.right,h=e?i.top:i.left,d=n?e?n.bottom:n.right:0,u=o?e?o.top:o.left:0;let m=a-h;switch(t){case L.Forward:m=Math.round(r-c),0===Math.round(m)&&n&&(m=Math.round(d-c)),m=Math.min(Math.max(0,m),l),0===m&&(m=l);break;case L.Back:m=Math.round(a)-Math.round(h),0===Math.round(m)&&o&&(m=Math.round(u-h)),m=-Math.min(Math.max(0,Math.abs(m)),l),0===m&&(m=-l);break;default:{const t=a<h,e=r>c;if(!t&&!e)return 0;if(e){const t=n?d-c:0;m=n&&a-t>=h?m=t:r-c}else if(t){const t=-a+h,e=o?-u+h:0;m=o&&r+e<=c?-e:r+t<=c?-t:0}break}}return Math.ceil(m)}}const k="data-dxtabs-tab-id",y="data-dxtabs-content-id",M=`.${o.Scrollable}`;var N,j,R,I;!function(t){t.Default="Default",t.AllTabs="AllTabs",t.OnDemand="OnDemand"}(N||(N={})),function(t){t.Auto="Auto",t.NavButtons="NavButtons",t.Swipe="Swipe",t.NoScroll="NoScroll"}(j||(j={})),function(t){t.Small="Small",t.Medium="Medium",t.Large="Large"}(R||(R={})),function(t){t.Top="Top",t.Bottom="Bottom",t.Left="Left",t.Right="Right"}(I||(I={}));const D="dxbl-tabs",z="tab-count",A="scroll-mode",O="render-mode",P="active-tab",U="size-mode",F="tabs-position",H="allow-tab-reorder";let q=B=class extends v{constructor(){super(...arguments),this.mainElementContentWidthSubscription=null,this.contentPanelWidthSubscription=null,this.currentSize={width:null,height:null},this.contentSize={width:null,height:null},this.domChanges=[],this.scrollContainerElement=null,this.canTrackScroll=!1,this.tabCount=0,this.scrollMode=j.Auto,this.renderMode=N.Default,this.activeTab="",this.sizeMode=R.Medium,this.tabsPosition=I.Top,this.allowTabReorder=!1,this.tabListElement=null,this.updateOverflowStateTimeout=0,this.minScrollStep=1,this.isReady=!1,this.neededCheckSize=!1,this.onScrollHandler=this.onScroll.bind(this),this.onNextButtonClickHandler=this.onScrollToNext.bind(this),this.onPrevButtonClickHandler=this.onScrollToPrev.bind(this),this.onClickHandler=this.onClick.bind(this),this.unsubscribeFocusUtils=null,this.unsubscribe=null}get isSideTabs(){return this.tabsPosition===I.Left||this.tabsPosition===I.Right}connectedOrContentChanged(){const t=this.querySelector(`:scope > .${o.TabList}`);t&&t!==this.tabListElement&&(this.tabListElement=t,this.dispose(),this.subscribeToEvents(),this.initialize(),this.isReady=!0)}disconnectedCallback(){super.disconnectedCallback(),this.disposeEvents(),this.dispose()}disposeEvents(){var t,e;null===(t=this.unsubscribe)||void 0===t||t.call(this),this.unsubscribe=null,null===(e=this.unsubscribeFocusUtils)||void 0===e||e.call(this),this.unsubscribeFocusUtils=null}subscribeToEvents(){if(!this.tabListElement)return;if(this.disposeEvents(),this.scrollMode!==j.NavButtons&&this.scrollMode!==j.Swipe)return;const t=this.getScrollContent();null==t||t.addEventListener("scroll",this.onScrollHandler),null==t||t.addEventListener("click",this.onClickHandler);const e=this.getPrevButton();null==e||e.addEventListener("click",this.onPrevButtonClickHandler);const i=this.getNextButton();null==i||i.addEventListener("click",this.onNextButtonClickHandler),this.unsubscribe=()=>{null==t||t.removeEventListener("scroll",this.onScrollHandler),null==t||t.removeEventListener("click",this.onClickHandler),null==e||e.removeEventListener("click",this.onPrevButtonClickHandler),null==i||i.removeEventListener("click",this.onNextButtonClickHandler)},this.unsubscribeFocusUtils=g(this.tabListElement)}dispose(){this.isReady=!1,this.updateOverflowStateTimeout&&(clearTimeout(this.updateOverflowStateTimeout),this.updateOverflowStateTimeout=0),this.mainElementContentWidthSubscription&&n(this.mainElementContentWidthSubscription),this.contentPanelWidthSubscription&&n(this.contentPanelWidthSubscription),this.neededCheckSize=!1}activate(t){if(!t)return;const e=document.querySelector(B.getSelector(k,t));if(null!=(null==e?void 0:e.parentNode)){const t=this.getListElement();for(let i=0;i<this.tabCount;i++)if(p.getChildByClassName(t.children[i],o.Item)===e){this.scrollToIndex(i);break}}}getActiveTab(){return this.querySelector(B.getSelector(k,this.activeTab))}getActiveTabIndex(){return Array.from(this.querySelectorAll(`.${o.Item}`)).findIndex((t=>t.classList.contains(C.Active)))}get isHorizontal(){return this.tabsPosition===I.Top||this.tabsPosition===I.Bottom}onScroll(t){this.updateOverflowState()}getContentElement(){return this.querySelector(`:scope > .${o.ContentPanel}`)}initialize(){l((()=>{this.prepareScrollMode(),e.DomUtils.addClassName(this.tabListElement,s.Loaded),this.checkForOverflow()})),this.mainElementContentWidthSubscription=r(this.tabListElement,(t=>{this.currentSize.width===t.width&&this.currentSize.height===t.height||(this.currentSize={width:t.width,height:t.height},this.checkForOverflow())}));const t=this.getContentElement();t&&(this.contentPanelWidthSubscription=r(t,(t=>{this.contentSize.width===t.width&&this.contentSize.height===t.height||(this.contentSize={width:t.width,height:t.height},this.onContentElementResize())})))}onContentElementResize(){this.updateSelectedContent()}updated(t){this.isReady&&((t.has("scrollMode")||t.has("tabCount")||t.has("sizeMode")||t.has("tabsPosition"))&&(l((()=>{this.prepareScrollMode(),this.checkForOverflow()})),this.subscribeToEvents()),t.has("activeTab")&&this.activate(this.activeTab),(t.has("renderMode")||t.has("activeTab"))&&this.updateSelectedContent(this.dispatchActiveTabChanchedEvent.bind(this)),super.updated(t))}dispatchActiveTabChanchedEvent(){this.dispatchEvent(new T)}queryUpdateOverflowState(t){this.updateOverflowStateTimeout&&clearTimeout(this.updateOverflowStateTimeout),this.updateOverflowStateTimeout=setTimeout((()=>{this.updateOverflowStateTimeout=0,this.updateOverflowState()}),t)}prepareScrollMode(){this.neededCheckSize=!!this.tabListElement&&this.scrollMode===j.NavButtons}checkForOverflow(){if(!this.neededCheckSize||!this.tabListElement)return;const t=this.isSideTabs?this.tabListElement.getBoundingClientRect().height-a(this.tabListElement):this.tabListElement.getBoundingClientRect().width-c(this.tabListElement);this.togleScrollState(t-this.calcNeededSize()<=-1)}togleScrollState(t){l((()=>{e.DomUtils.toggleClassName(this.tabListElement,o.HasOverflow,t),this.canTrackScroll=t,t&&this.queryUpdateOverflowState()}))}calcNeededSize(){var t;const e=null===(t=this.tabListElement)||void 0===t?void 0:t.getElementsByTagName("ul")[0];if(!e)return 0;let i=this.isSideTabs?a(e):c(e);const s=e.getElementsByTagName("li");for(const t of s)i+=this.isSideTabs?t.getBoundingClientRect().height+h(t):t.getBoundingClientRect().width+d(t);return i}updateOverflowState(){if(!this.canTrackScroll||this.scrollMode!==j.NavButtons)return;if(!this.findScrollContainer())return;const t=this.getScrollContent();if(!t)return;const[e,i]=this.getScrollAccess(t),s=this.getNextButton(),o=this.getPrevButton();u((()=>{s&&m(s,"disabled",!i),o&&m(o,"disabled",!e)}))}getScrollAccess(t){if(this.isSideTabs){const e=Math.ceil(t.scrollTop);return[e>0,Math.round(t.scrollHeight-e)-Math.round(t.clientHeight)>this.minScrollStep]}{const e=Math.ceil(t.scrollLeft);return[e>0,Math.round(t.scrollWidth-e)-Math.round(t.clientWidth)>this.minScrollStep]}}findScrollContainer(){if(!this.scrollContainerElement){if(!this.tabListElement)return null;this.scrollContainerElement=p.elementMatchesSelector(this.tabListElement,M)?this.tabListElement:this.tabListElement.querySelector(M)}return this.scrollContainerElement}getScrollContent(){return this.tabListElement?e.DomUtils.getNodesByClassName(this.tabListElement,i.ContentContainerClassName)[0]:null}getPrevButton(){var t;return null===(t=this.findScrollContainer())||void 0===t?void 0:t.querySelector(`:scope > .${o.ButtonScrollPrev}`)}getNextButton(){var t;return null===(t=this.findScrollContainer())||void 0===t?void 0:t.querySelector(`:scope > .${o.ButtonScrollNext}`)}onScrollToPrev(t){t.preventDefault();const e=this.findExtremeTabItemIndex(L.Back);this.scrollToIndex(e,L.Back)}onScrollToNext(t){t.preventDefault();const e=this.findExtremeTabItemIndex(L.Forward);this.scrollToIndex(e,L.Forward)}onClick(t){let e=t.target;for(;e;){if(b(e,o.Item)){const t=e.getAttribute(k);t&&this.activate(t);break}e=e.parentElement}}getListElement(){return this.tabListElement.getElementsByTagName("ul")[0]}getTabElement(t){return this.getListElement().querySelectorAll(":scope > li > ."+o.Item)[t]}scrollToIndex(t,e=L.None){var i,s;if(t>-1&&this.getScrollContent()){const o=t=>t.closest("li"),n=this.getTabElement(t),l=null===(i=o(n))||void 0===i?void 0:i.nextElementSibling,r=null===(s=o(n))||void 0===s?void 0:s.previousElementSibling;this.scrollToTab(n,r,l,e)}}findExtremeTabItemIndex(t){if(t===L.None)return-1;const e=this.getScrollContent();if(!e)return-1;const i=this.getListElement().querySelectorAll(":scope > li > ."+o.Item),s=this.getContentFrameRect(e),[n,l,r]=t===L.Forward?[0,this.tabCount,1]:[this.tabCount-1,-1,-1];for(let e=n;e!==l;e+=r){const o=i[e].getBoundingClientRect();if(this.isSideTabs){if(t===L.Back&&o.top<s.top||t===L.Forward&&o.bottom>s.bottom)return e}else if(t===L.Back&&o.left<s.left||t===L.Forward&&o.right>s.right)return e}return-1}getContentFrameRect(t){const i=e.DomUtils.getCurrentStyle(t),s=t.getBoundingClientRect();return new f(s.left+e.DomUtils.pxToInt(i.paddingLeft)+e.DomUtils.pxToInt(i.borderLeftWidth),s.top+e.DomUtils.pxToInt(i.paddingTop)+e.DomUtils.pxToInt(i.borderTopWidth),s.width-e.DomUtils.pxToInt(i.paddingRight)-e.DomUtils.pxToInt(i.borderRightWidth),s.height-e.DomUtils.pxToInt(i.paddingBottom)-e.DomUtils.pxToInt(i.borderBottomWidth))}scrollToTab(t,e,i,s){const o=this.getScrollContent();if(!o)return;const n=x.calculate(s,this.isSideTabs,this.getContentFrameRect(o),S.fromDomRect(t.getBoundingClientRect()),e?S.fromDomRect(e.getBoundingClientRect()):void 0,i?S.fromDomRect(i.getBoundingClientRect()):void 0);0!==n?this.isSideTabs?o.scrollTop+=n:o.scrollLeft+=n:o.scrollLeft>0&&s===L.Back&&(this.isSideTabs?o.scrollTop=0:o.scrollLeft=0)}updateSelectedContent(t=null){const i=this.getContentElement();if(i){const n=this.activeTab?i.querySelector(B.getSelector(y,this.activeTab)):null,r=Array.from(i.querySelectorAll(`:scope > .${o.Content}${this.activeTab?`:not(${B.getSelector(y,this.activeTab)})`:""}`));l((()=>{if(n&&(n.style.cssText="",n.setAttribute("data-dx-tab-loaded","")),this.renderMode!==N.Default){const t=`position:absolute;visibility:hidden;left:-10000px;top:-10000px;width:${i.clientWidth}px;height:${i.clientHeight}px`;r.forEach((e=>{e.style.cssText=t}))}i&&e.DomUtils.addClassName(i,s.Loaded),t&&setTimeout((()=>t()),0)}))}}static getSelector(t,e){return`[${t}=${e}]`}};t([E({type:Number,attribute:z})],q.prototype,"tabCount",void 0),t([E({type:j,attribute:A})],q.prototype,"scrollMode",void 0),t([E({type:N,attribute:O})],q.prototype,"renderMode",void 0),t([E({attribute:P})],q.prototype,"activeTab",void 0),t([E({attribute:U})],q.prototype,"sizeMode",void 0),t([E({attribute:F})],q.prototype,"tabsPosition",void 0),t([E({type:Boolean,attribute:H})],q.prototype,"allowTabReorder",void 0),q=B=t([w(D)],q);const W={loadModule:function(){}};export{q as RibbonTabs,I as TabsPosition,j as TabsScrollMode,W as default,D as dxTabsTag,k as tabIdSelector};
