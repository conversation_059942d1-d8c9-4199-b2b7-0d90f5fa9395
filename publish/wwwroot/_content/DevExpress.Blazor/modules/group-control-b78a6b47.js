import{D as e}from"./dx-html-element-base-3262304e.js";import"./data-qa-utils-8be7c726.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./tslib.es6-d65164b3.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./eventhelper-8bcec49f.js";import"./constants-7c047c0d.js";import"./devices-17b9ba08.js";import"./dx-license-30fd02d1.js";customElements.define("dxbl-group-control",class extends e{});const s={loadModule:function(){}};export{s as default};
