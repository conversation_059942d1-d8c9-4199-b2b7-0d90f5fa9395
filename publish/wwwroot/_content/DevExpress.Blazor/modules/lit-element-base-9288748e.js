import{registerTrialPanelComponents as e}from"./dx-license-30fd02d1.js";import{D as t}from"./data-qa-utils-8be7c726.js";import{s}from"./lit-element-462e7ad3.js";e();class c{constructor(e){this.element=e}}class a extends CustomEvent{constructor(e){super(a.eventName,{detail:e,bubbles:!1,composed:!1,cancelable:!1})}static create(e){return new a(new c(e))}}a.eventName="dxbl-element-connected";class n extends CustomEvent{constructor(e){super(n.eventName,{detail:e,bubbles:!1,composed:!1,cancelable:!1})}static create(e){return new a(new c(e))}}n.eventName="dxbl-element-disconnected";class d extends s{constructor(){super(...arguments),this._isDOMAttached=!1}get isDOMAttached(){return this._isDOMAttached}connectedCallback(){super.connectedCallback(),this._isDOMAttached=!0,this.readyOnConnectedCallback&&t.setLoaded(this),this.dispatchEvent(a.create(this))}disconnectedCallback(){super.disconnectedCallback(),this._isDOMAttached=!1,this.readyOnConnectedCallback&&t.removeLoaded(this),this.dispatchEvent(n.create(this))}get readyOnConnectedCallback(){return!0}}export{d as L};
