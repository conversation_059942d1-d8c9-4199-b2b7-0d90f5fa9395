import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{C as i}from"./css-classes-c63af734.js";import{C as n}from"./custom-events-helper-e7f279d3.js";import{t as s}from"./constants-da6cacac.js";import{E as l}from"./eventhelper-8bcec49f.js";import{g as o}from"./devices-17b9ba08.js";import{d}from"./dom-554d0cc7.js";import{isFocusableElement as a}from"./focus-utils-ae044224.js";import{n as r}from"./property-4ec0b52d.js";class h extends CustomEvent{constructor(e){super(h.eventName,{detail:new m(e),bubbles:!0,composed:!0,cancelable:!0})}}h.eventName=s+".focusin";class u extends CustomEvent{constructor(e,t){super(u.eventName,{detail:new x(e,t),bubbles:!0,composed:!0,cancelable:!0})}}u.eventName=s+".focusout";class c extends CustomEvent{constructor(e){super(c.eventName,{detail:e,bubbles:!0,composed:!0,cancelable:!0})}}c.eventName=s+".keydown";class p extends CustomEvent{constructor(e){super(p.eventName,{detail:e,bubbles:!0,composed:!0,cancelable:!0})}}p.eventName=s+".beforeinput";class m{constructor(e){this.NeedSelection=e}}class x{constructor(e,t){this.Text=e,this.TabKey=t}}class f{constructor(e){this.Text=e}}class E extends f{constructor(e,t){super(e),this.Version=t}}class y extends CustomEvent{constructor(e,t){super(y.eventName,{detail:new E(e,t),bubbles:!0,composed:!0,cancelable:!0})}}y.eventName=s+".textinput";class b extends CustomEvent{constructor(e){super(b.eventName,{detail:new f(e),bubbles:!0,composed:!0,cancelable:!0})}}b.eventName=s+".textchange",n.register(y.eventName,(e=>e.detail)),n.register(b.eventName,(e=>e.detail)),n.register(h.eventName,(e=>e.detail)),n.register(u.eventName,(e=>e.detail)),n.register(c.eventName,(e=>{const t=e.detail;return{Key:t.key,Code:t.code,CtrlKey:t.ctrlKey,AltKey:t.altKey,ShiftKey:t.shiftKey,MetaKey:t.metaKey}})),n.register(p.eventName,(e=>{const t=e.detail;return{InputType:t.inputType,Text:t.data}}));class T{get hasAction(){return!!this.handle}constructor(e){this.action=null,this.handle=null,this.timeout=0,this.timeout=e}execute(e){this.cancel(),this.action=e,this.handle=setTimeout((()=>{var e;null===(e=this.action)||void 0===e||e.call(this),this.handle=null,this.action=null}),this.timeout)}forceExecute(){if(!this.hasAction)return;const e=this.action;this.cancel(),e&&e()}cancel(){this.hasAction&&this.handle&&(clearTimeout(this.handle),this.action=null,this.handle=null)}}var v,g,I;!function(e){e.inputText="field-text",e.inputTextVersion="field-text-version",e.bindValueMode="bind-value-mode",e.fieldInputDelay="field-input-delay",e.customPlaceholder="custom-placeholder"}(v||(v={})),function(e){e.OnLostFocus="OnLostFocus",e.OnInput="OnInput",e.OnDelayedInput="OnDelayedInput"}(g||(g={})),function(e){e.WithoutTemplate="WithoutTemplate",e.WithTemplate="WithTemplate",e.WithEditableTemplate="WithEditableTemplate"}(I||(I={}));const C=500;class D{}D.TextEdit=i.Prefix+"-text-edit",D.ClearButton=i.Prefix+"-edit-btn-clear",D.EditBoxTemplate=i.Prefix+"-text-edit-template",D.TextEditInput=i.Prefix+"-text-edit-input",D.EditorButton=i.Prefix+"-text-edit-btn";class V extends t{constructor(){super(),this.fieldText="",this.fieldTextVersion=-1,this.fieldInputDelay=500,this.bindValueMode=g.OnLostFocus,this.isReadOnly=!1,this.placeholder="",this.boundOnInputInputHandler=this.onTextInput.bind(this),this.boundOnInputChangeHandler=this.onTextChange.bind(this),this.boundOnInputKeyDownHandler=this.onInputKeyDown.bind(this),this.boundOnPointerDownHandler=this.onPointerDown.bind(this),this.inputDelayDeferredAction=new T(500),this._fieldTextHistory={},this._fieldInitialized=!1,this._fieldTextVersion=-1,this._isMobileDevice=o().isMobileDevice,this.mainElementObserver=new MutationObserver(this.mainElementContentChanged.bind(this))}get useAdaptiveLayout(){return this._isMobileDevice}get inputSelectionStart(){var e,t;return null!==(t=null===(e=this.fieldElement)||void 0===e?void 0:e.selectionStart)&&void 0!==t?t:0}get inputSelectionEnd(){var e,t;return null!==(t=null===(e=this.fieldElement)||void 0===e?void 0:e.selectionEnd)&&void 0!==t?t:0}get selectionLength(){return Math.abs(this.inputSelectionEnd-this.inputSelectionStart)}isAllSelected(){const e=this.fieldElementValue.length;return e>0&&0===this.inputSelectionStart&&this.inputSelectionEnd===e}get enabled(){return this.fieldElement?!this.fieldElement.disabled:!d.DomUtils.hasClassName(this,i.Disabled)}get allowInput(){return this.enabled&&!!this.fieldElement&&!this.fieldElement.readOnly}static isClearButtonElement(e){var t;return!!e&&(null===(t=e.classList)||void 0===t?void 0:t.contains(D.ClearButton))}static isEditBoxTemplateElement(e){var t;return!!e&&(null===(t=e.classList)||void 0===t?void 0:t.contains(D.EditBoxTemplate))}static isEditorButtonElement(e){var t;return!!e&&(null===(t=e.classList)||void 0===t?void 0:t.contains(D.EditorButton))}isFocusableElementInMainElementTemplate(e){return!!this.editBoxTemplateElement&&(this.editBoxTemplateElement!==e&&this.editBoxTemplateElement.contains(e)&&a(e))}get fieldElementValue(){var e,t;return null!==(t=null===(e=this.fieldElement)||void 0===e?void 0:e.value)&&void 0!==t?t:""}set fieldElementValue(e){this.fieldElement&&(this.fieldElement.value=e)}get rendered(){return!!this.fieldElement}get focused(){return document.activeElement===this.editBoxElement}get isFieldElementReadonly(){return!!this.fieldElement&&this.fieldElement.readOnly}get editBoxElement(){var e;return null!==(e=this.fieldElement)&&void 0!==e?e:this.editBoxTemplateElement}get editBoxTemplateElement(){return this.querySelector(`:scope > .${D.EditBoxTemplate}`)}raiseFieldText(){let e;const t=this.fieldElementValue;this.shouldProcessFieldTextVersion?(e=++this._fieldTextVersion,this._fieldTextHistory[e]=t):e=this._fieldTextVersion,this.dispatchEvent(new y(t,e))}raiseFieldChange(){this.dispatchEvent(new b(this.fieldElementValue))}applyTextProperty(){this.shouldProcessFieldTextVersion?this.applyTextPropertyByVersion():(this.applyTextPropertyCore(),this._fieldTextVersion=this.fieldTextVersion)}applyTextPropertyByVersion(){const e=this.fieldTextVersion,t=this.fieldText;let i=this._fieldTextVersion;e>i&&(i=e),-1===i||t!==this.fieldElementValue&&this._fieldTextHistory[e]!==t?(this.applyTextPropertyCore(),this._fieldTextVersion=i+1):delete this._fieldTextHistory[e]}applyTextPropertyCore(){this.fieldElementValue=this.fieldText}onBindValueModeChanged(e){e===g.OnLostFocus?(this._fieldTextHistory={},this._fieldTextVersion++):this.bindValueMode===g.OnLostFocus&&(this._fieldTextHistory={},this._fieldTextVersion=-1)}onInputDelayChanged(){this.inputDelayDeferredAction=new T(this.fieldInputDelay)}get shouldProcessFieldTextVersion(){return this.bindValueMode===g.OnInput||this.bindValueMode===g.OnDelayedInput}get shouldForceInputOnEnter(){return!0}onTextInput(e){if(this.shouldRaiseFieldTextEvents)switch(this.bindValueMode){case g.OnInput:this.raiseFieldText();break;case g.OnDelayedInput:this.inputDelayDeferredAction.execute((()=>this.raiseFieldText()))}}onTextChange(e){this.shouldRaiseFieldTextEvents&&(this.bindValueMode===g.OnLostFocus?this.raiseFieldChange():this.bindValueMode===g.OnDelayedInput&&this.tryForceDelayedInput())}onFieldReady(e,t){e.addEventListener("input",this.boundOnInputInputHandler),e.addEventListener("change",this.boundOnInputChangeHandler),e.addEventListener("keydown",this.boundOnInputKeyDownHandler),t&&(this.applyTextProperty(),this.updatePlaceholder())}onTemplateWithoutInputReady(e){e.addEventListener("keydown",this.boundOnInputKeyDownHandler)}onPointerDown(e){this.processPointerDown(e)}processPointerDown(e){var t;const i=e.target,n=l.containsInComposedPath(e,V.isClearButtonElement),s=l.containsInComposedPath(e,V.isEditorButtonElement),o=this.editBoxTemplateElement&&this.editBoxTemplateElement===i&&!a(this.editBoxTemplateElement),d=this.editBoxTemplateElement&&this.editBoxTemplateElement.contains(i)&&!a(i),r=!this.focused&&(n||s&&!this.useAdaptiveLayout||o||d),h=n||s||o||d;return s&&this.focused&&this.raiseFieldChange(),h&&e.preventDefault(),r&&(null===(t=this.editBoxElement)||void 0===t||t.focus()),!0}onInputKeyDown(e){this.isReadOnly||this.processKeyDown(e)}processKeyDown(e){return"Enter"===e.key&&this.shouldForceInputOnEnter&&this.tryForceDelayedInput(),!1}tryForceDelayedInput(){this.shouldRaiseFieldTextEvents&&this.bindValueMode===g.OnDelayedInput&&this.inputDelayDeferredAction.forceExecute()}connectedOrContentChanged(){super.connectedOrContentChanged(),this.mainElementObserver.observe(this,{childList:!0,subtree:!0})}disconnectedCallback(){super.disconnectedCallback(),this.mainElementObserver.disconnect()}contentChanged(){this.fieldElement=this.getFieldElement(),this.fieldElement?(this.onFieldReady(this.fieldElement,!this._fieldInitialized),this._fieldInitialized=!0):this.editBoxTemplateElement&&this.onTemplateWithoutInputReady(this.editBoxTemplateElement),this.editBoxTemplateElement&&this.updateEditBoxTemplateTabIndex(),this.addEventListener("pointerdown",this.boundOnPointerDownHandler)}get shouldRaiseFieldTextEvents(){return!0}get readyOnConnectedCallback(){return!1}updated(e){this.rendered&&(e.has("bindValueMode")&&this.onBindValueModeChanged(e.get("bindValueMode")),(e.has("fieldText")||e.has("fieldTextVersion"))&&this.applyTextProperty(),e.has("placeholder")&&this.updatePlaceholder()),e.has("fieldInputDelay")&&this.onInputDelayChanged(),super.updated(e)}updatePlaceholder(){this.fieldElement&&(this.placeholder&&""!==this.placeholder?this.fieldElement.setAttribute("placeholder",this.placeholder):this.fieldElement.removeAttribute("placeholder"))}updateEditBoxTemplateTabIndex(){var e,t;this.enabled&&!this.fieldElement?null===(e=this.editBoxTemplateElement)||void 0===e||e.setAttribute("tabindex","0"):null===(t=this.editBoxTemplateElement)||void 0===t||t.removeAttribute("tabindex")}mainElementContentChanged(e,t){const i=this.fieldElement;this.contentChanged(),this.fieldElement&&i!==this.fieldElement&&this.applyTextProperty()}}e([r({type:String,attribute:v.inputText})],V.prototype,"fieldText",void 0),e([r({type:Number,attribute:v.inputTextVersion})],V.prototype,"fieldTextVersion",void 0),e([r({type:Number,attribute:v.fieldInputDelay})],V.prototype,"fieldInputDelay",void 0),e([r({type:g,attribute:v.bindValueMode})],V.prototype,"bindValueMode",void 0),e([r({type:Boolean,attribute:"is-read-only"})],V.prototype,"isReadOnly",void 0),e([r({type:String,attribute:v.customPlaceholder})],V.prototype,"placeholder",void 0);export{g as B,C as D,x as F,c as I,D as T,p as a,v as b,V as c,h as d,u as e};
