import{c as e}from"./dx-scroll-viewer-da0fb41c.js";import{D as s}from"./dx-virtual-scroll-viewer-f4a3bc9e.js";import{dxThumbTagName as o}from"./thumb-31d768d7.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./tslib.es6-d65164b3.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./dx-html-element-base-3262304e.js";import"./data-qa-utils-8be7c726.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./eventhelper-8bcec49f.js";import"./constants-7c047c0d.js";import"./devices-17b9ba08.js";import"./dx-license-30fd02d1.js";import"./scroll-viewer-css-classes-e724f203.js";import"./css-classes-c63af734.js";import"./custom-events-helper-e7f279d3.js";import"./point-e4ec110e.js";function t(){}const r={loadModule:t,registeredComponents:[e,s,o]};export{r as default,t as loadModule};
