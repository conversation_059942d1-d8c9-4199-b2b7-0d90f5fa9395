import{_ as e}from"./tslib.es6-d65164b3.js";import{d as t}from"./dom-554d0cc7.js";import{k as n}from"./key-ffa272aa.js";import{E as o}from"./eventhelper-8bcec49f.js";import{C as s}from"./custom-events-helper-e7f279d3.js";import{s as i}from"./constants-da6cacac.js";import{n as r}from"./nameof-factory-64d95f5b.js";import{DxMaskedInputEditor as a}from"./masked-input-0c0a9541.js";import{C as l}from"./css-classes-c63af734.js";import{B as d}from"./text-editor-733d5e56.js";import{n as p}from"./property-4ec0b52d.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./input-66769c52.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./focushelper-2eea96ca.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./dom-utils-d057dcaa.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./devices-17b9ba08.js";import"./lit-element-462e7ad3.js";import"./custom-element-267f9a21.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";class m extends CustomEvent{constructor(e,t){super(m.eventName,{detail:new u(e,t),bubbles:!0,composed:!0,cancelable:!0})}}m.eventName=i+".incrementValue";class u{constructor(e,t){this.Increment=e,this.PreviousValue=t}}var c,h;s.register(m.eventName,(e=>e.detail)),function(e){e.allowMouseWheel="allow-mouse-wheel",e.needExponentialView="need-exponential-view",e.decimalSeparator="decimal-separator"}(h||(h={}));const b=r();class f{}c=f,f.Prefix=l.Prefix+"-spin",f.BtnsContainer=c.Prefix+"-btns",f.IncBtn=c.Prefix+"-btn-inc",f.DecBtn=c.Prefix+"-btn-dec";class w extends a{constructor(){super(...arguments),this.regex=/^-?(\d)*$/,this.pointerDownTimer={id:-1,button:null},this.buttonObserver=new MutationObserver(this.buttonRemovedHandler.bind(this)),this.boundButtonOnPointerUpHandler=this.onButtonPointerUp.bind(this),this.allowMouseWheel=!1,this.needExponentialView=!1}get pointerDownTimerId(){return this.pointerDownTimer.id}get shouldProcessWheel(){return this.allowMouseWheel}disconnectedCallback(){this.buttonObserver.disconnect(),this.stopPointerLongTap(),super.disconnectedCallback()}connectedOrContentChanged(){super.connectedOrContentChanged(),this.buttonObserver.observe(this,{childList:!0})}onTextInput(e){if(this.isMaskDefined)return;if(!this.inputElement||!o.containsInComposedPath(e,(e=>e===this.inputElement)))return;let t=this.fieldElementValue.trim();this.decimalSeparator&&(t=t.replace(/[.|,]/g,this.decimalSeparator)),this.regex.test(t)||(t=t.replace(/[^0-9]/g,"")),t!==this.fieldElementValue&&(this.fieldElementValue=t),super.onTextInput(e)}processKeyDown(e){const t=this.getFieldElement();return!!o.containsInComposedPath(e,(e=>e===t))&&(this.isNavigationKey(e)?(o.markHandled(e),this.incrementValue(e.keyCode===n.KeyCode.Up),!0):!!super.processKeyDown(e))}processWheel(e){this.incrementValue(e.deltaY<0)}processPointerDown(e){const n=this.getButton(e);if(n&&this.inputElement){e.preventDefault(),this.useAdaptiveLayout||t.DomUtils.setFocus(this.inputElement);const o=t.DomUtils.hasClassName(n,f.IncBtn);this.incrementValue(o),this.pointerDownTimer={id:window.setTimeout(this.startPointerLongTap.bind(this),1e3,o),button:n},n.addEventListener("pointerup",this.boundButtonOnPointerUpHandler),n.addEventListener("pointerout",this.boundButtonOnPointerUpHandler)}return super.processPointerDown(e)}getButton(e){const t=e.composedPath();for(const e in t){const n=t[e];if(n&&this.hasSpinBtnClass(n))return n}return null}hasSpinBtnClass(e){return t.DomUtils.hasClassName(e,f.IncBtn)||t.DomUtils.hasClassName(e,f.DecBtn)}startPointerLongTap(e,t){this.pointerDownTimer={id:window.setInterval(this.incrementValue.bind(this),50,e),button:t}}stopPointerLongTap(){-1!==this.pointerDownTimer.id&&(clearTimeout(this.pointerDownTimer.id),this.pointerDownTimer={id:-1,button:null},this.bindValueMode===d.OnDelayedInput?this.applyDefferedValue():this.raiseApplyValue())}onButtonPointerUp(e){let n=e.target;this.hasSpinBtnClass(n)||(n=t.DomUtils.getParentByTagName(n,"button")),this.stopPointerLongTap(),n.removeEventListener("pointerup",this.boundButtonOnPointerUpHandler),n.removeEventListener("pointerout",this.boundButtonOnPointerUpHandler)}buttonRemovedHandler(e,n){if(-1===this.pointerDownTimer.id)return;e.some((e=>Array.from(e.removedNodes).some((e=>{var n,o;return t.DomUtils.isElementNode(e)&&e.classList.contains(f.BtnsContainer)&&((null===(n=this.pointerDownTimer.button)||void 0===n?void 0:n.isEqualNode(t.DomUtils.getChildNodesByClassName(e,f.IncBtn)[0]))||(null===(o=this.pointerDownTimer.button)||void 0===o?void 0:o.isEqualNode(t.DomUtils.getChildNodesByClassName(e,f.DecBtn)[0])))}))))&&this.stopPointerLongTap()}incrementValue(e){this.dispatchEvent(new m(e,this.fieldElementValue))}isNavigationKey(e){return e.keyCode===n.KeyCode.Up||e.keyCode===n.KeyCode.Down}updated(e){super.updated(e),(e.has(b("decimalSeparator"))||e.has(b("needExponentialView")))&&this.applyRegex()}applyRegex(){this.decimalSeparator&&(this.regex=this.needExponentialView?/^-?(\d+|[,.]\d+|\d+[,.]\d+|\d+[,.]|[,.])?([eE]?[+-]?(\d)*)?$/:/^-?(\d+|[,.]\d+|\d+[,.]\d+|\d+[,.]|[,.])?$/)}shouldProcessKeyUp(e){switch(e.key){case"ArrowUp":case"ArrowDown":return this.isMaskDefined||this.bindValueMode===d.OnDelayedInput}return super.shouldProcessKeyUp(e)}}e([p({type:Boolean,attribute:h.allowMouseWheel})],w.prototype,"allowMouseWheel",void 0),e([p({type:Boolean,attribute:h.needExponentialView})],w.prototype,"needExponentialView",void 0),e([p({type:String,attribute:h.decimalSeparator})],w.prototype,"decimalSeparator",void 0),customElements.define(i,w);const v={loadModule:function(){}};export{w as DxSpinEdit,f as SpinEditCssClasses,v as default};
