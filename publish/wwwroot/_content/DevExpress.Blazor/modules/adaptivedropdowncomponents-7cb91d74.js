import{r as e}from"./dropdowncomponents-3d8f06da.js";import{registeredComponents as t}from"./modalcomponents-951e20e2.js";import{_ as i}from"./tslib.es6-d65164b3.js";import{e as s}from"./logicaltreehelper-67db40f1.js";import{n as a}from"./property-4ec0b52d.js";import{e as o}from"./custom-element-267f9a21.js";import{s as r}from"./lit-element-462e7ad3.js";import{d as p}from"./events-interseptor-a522582a.js";import"./dropdown-f5b2318c.js";import"./popup-355ecaa4.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./eventhelper-8bcec49f.js";import"./portal-b3727c25.js";import"./data-qa-utils-8be7c726.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./custom-events-helper-e7f279d3.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./key-ffa272aa.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./dom-utils-d057dcaa.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./thumb-31d768d7.js";import"./query-44b9267f.js";import"./popupportal-bbd2fea0.js";var n;const d="dxbl-adaptive-dropdown";let c=n=class extends r{constructor(){super(...arguments),this.slotChangedHandler=this.handleSlotChanged.bind(this),this.interceptorSlotChangedHandler=this.handleInterceptorSlotChange.bind(this),this.interceptor=null,this.resizeHandler=this.handleResize.bind(this),this._adaptivityEnabled=!1,this._popupAccessor=null,this.adaptiveWidth=576}get popup(){var e;return(null===(e=this._popupAccessor)||void 0===e?void 0:e.popup)||null}get popupBase(){var e;return(null===(e=this._popupAccessor)||void 0===e?void 0:e.popupBase)||null}get adaptivityEnabled(){return this._adaptivityEnabled}set adaptivityEnabled(e){this._adaptivityEnabled!==e&&(this._adaptivityEnabled=e,this.raiseEnableAdaptivity(e))}createRenderRoot(){const e=super.createRenderRoot(),t=document.createElement("slot");e.appendChild(t),t.addEventListener("slotchange",this.slotChangedHandler);const i=document.createElement("slot");return i.name="interceptor",e.appendChild(i),i.addEventListener("slotchange",this.interceptorSlotChangedHandler),e}connectedCallback(){super.connectedCallback(),window.addEventListener("resize",this.resizeHandler,{passive:!0}),setTimeout((()=>this.updateAdaptivity()))}disconnectedCallback(){super.disconnectedCallback(),window.removeEventListener("resize",this.resizeHandler)}handleResize(e){this.updateAdaptivity()}updateAdaptivity(){this.adaptivityEnabled=this.getActualAdaptivityEnabled(),this.raiseEvent("adaptivityChanged",{enabled:this.adaptivityEnabled})}getActualAdaptivityEnabled(){return window.innerWidth<=this.adaptiveWidth}handleSlotChanged(e){const t=e.target.assignedNodes();this._popupAccessor=n.findPopupAccessor(t)}handleInterceptorSlotChange(e){const t=e.target.assignedNodes();this.interceptor=t[0]}raiseEvent(e,t){this.interceptor&&this.interceptor.raise&&this.interceptor.raise(e,t)}raiseEnableAdaptivity(e){this.raiseEvent("adaptivityChanged",{enabled:e})}static findPopupAccessor(e){const t=e.find((e=>s(e)));return t||null}};function l(){}i([a({type:Number,attribute:"adaptive-width"})],c.prototype,"adaptiveWidth",void 0),c=n=i([o(d)],c);const m={dropDownRegisteredComponents:e,modalRegisteredComponents:t,init:l,dxAdaptiveDropDownTagName:d,dxEventsInterceptorTagName:p};export{m as default,l as init};
