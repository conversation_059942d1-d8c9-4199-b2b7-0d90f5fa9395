import{_ as e}from"./tslib.es6-d65164b3.js";import{c as t}from"./text-editor-733d5e56.js";import{h as s}from"./constants-da6cacac.js";import{C as i}from"./css-classes-c63af734.js";import{e as o}from"./custom-element-267f9a21.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./devices-17b9ba08.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./focus-utils-ae044224.js";import"./key-ffa272aa.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";class r{}r.Memo=i.Prefix+"-memo-edit";let n=class extends t{constructor(){super(),this.textAreaObserver=new MutationObserver(this.textAreaSizeChanged.bind(this))}connectedOrContentChanged(){super.connectedOrContentChanged();const e=this.getFieldElement();e&&this.textAreaObserver.observe(e,{attributes:!0})}disconnectedCallback(){this.textAreaObserver.disconnect(),super.disconnectedCallback()}textAreaSizeChanged(e,t){var s,i;const o=e[0].target,r=parseInt(o.style.width);if(!isNaN(r)){const e=this.offsetWidth-this.clientWidth+r;this.offsetWidth!==e&&(this.style.width=e+"px")}const n=parseInt(o.style.height);if(!isNaN(n)){const e=parseInt(getComputedStyle(o).minHeight),t=n>=e?n:e,r=null!==(i=null===(s=this.getButtonsArea())||void 0===s?void 0:s.getBoundingClientRect().height)&&void 0!==i?i:0,c=this.offsetHeight-this.clientHeight+t+r;this.offsetHeight!==c&&(this.style.height=c+"px")}}get shouldForceInputOnEnter(){return!1}getFieldElement(){return this.querySelector("textarea")}getButtonsArea(){return this.querySelector(".dxbl-memo-edit-buttons-area")}};n=e([o(s)],n);const c={loadModule:function(){}};export{n as DxMemoEditor,r as MemoCssClasses,c as default};
