import{g as e,i as t}from"./dom-utils-d057dcaa.js";import{waitForCondition as i}from"./utils-b5b2c8a9.js";import{_ as s}from"./tslib.es6-d65164b3.js";import{S as n}from"./single-slot-element-base-01d93921.js";import{a as r,b as o}from"./events-a8fe5872.js";import{registerTrialPanelComponents as a}from"./dx-license-30fd02d1.js";import{n as d}from"./property-4ec0b52d.js";import{d as l}from"./dom-554d0cc7.js";import{C as c}from"./css-classes-c63af734.js";const h="dx-theme-marker",p="generic.light",g=`.${h} { font-family: "dx.${p}"; }`;let u=!1;class m{constructor(){this._dxtThemeMarkerLoaded=!1,this._customThemesCache={}}static getInstance(){return m.instance||(m.instance=new m),m.instance}async initializeWidget(e,t,i,s){return new(await this.getWidgetClass(t,i))(e,s)}async registerCustomTheme(e){if(!e)return;const t=e.name,i=JSON.stringify(e);if(this._customThemesCache[t]!==i){(await this.getVizModule()).registerTheme(e,p),this._customThemesCache[t]=i}}ensureDxtThemeMarkerExists(){if(this._dxtThemeMarkerLoaded||(this._dxtThemeMarkerLoaded=this.checkDxtThemeMarkerExists()),this._dxtThemeMarkerLoaded)return;const t=e();t&&(t.insertRule(g),this._dxtThemeMarkerLoaded=!0)}checkDxtThemeMarkerExists(){const e=document.createElement("div");e.className=h,window.document.body.appendChild(e);const t=window.getComputedStyle(e).fontFamily;return e.remove(),t.indexOf("dx.")>-1}async getVizModule(){return await this.getDxtModuleOrClass("viz")}async setLocalization(e){(await this.getDxtModuleOrClass("localization")).locale(e)}async getWidgetClass(e,t){return await this.getDxtModuleOrClass(t,e)}async getDxtModuleOrClass(e,t=""){return this.ensureDxtThemeMarkerExists(),u||await this.loadDxtScripts(),this.tryGetDxtModuleOrClass(e,t)}tryGetDxtModuleOrClass(e,t=""){const i=window.DevExpress,s=null==i?void 0:i[e];return t?null==s?void 0:s[t]:s}async loadDxtScripts(){const e=window;try{await i((()=>!!e._dx_loader_promise)),await e._dx_loader_promise,await i((()=>{var t;return!!(null===(t=e.DevExpress)||void 0===t?void 0:t.ui)})),u=!0}catch(e){console.error("Failed to load Devextreme scripts")}}}a();const C=e=>e&&"object"==typeof e&&!Array.isArray(e);class f extends n{constructor(){super(...arguments),this.optionChunks="",this._firstLoading=!0,this._loadedChunks=[]}connectedOrContentChanged(){super.connectedOrContentChanged(),this.createComponentIfRequired()}disconnectedCallback(){super.disconnectedCallback(),this.disposeComponent()}firstUpdated(){this._firstLoading=!1}createComponentIfRequired(){if(!this.canCreateComponent())return;const e={};this.deepAssign(e,this.createInitOptions(),this.getServerOptions()),this.createComponent(e)}processSpecialOptions(e){}willUpdate(e){if(!this._firstLoading&&e.has("optionChunks")){const e=this.getServerOptions();e&&this.updateComponent(e)}}parseServerOptions(e){const t=e&&JSON.parse(e);if(!t)return;const i=Object.keys(t).filter((e=>!this._loadedChunks.includes(e))),s=i.map((e=>t[e])),n=this.deepAssign({},...s);return this.processSpecialOptions(n),this.invokeOptionsLoadedEvent(i),this._loadedChunks.push(...i),n}deepAssign(e,...t){if(!t.length)return e;const i=t.shift();if(C(e)&&C(i))for(const[t,s]of Object.entries(i))C(s)?(e[t]||(e[t]={}),this.deepAssign(e[t],s)):e[t]=s;return this.deepAssign(e,...t)}resetEmptyOptions(e,t,i=""){if(!t)return;const s=Object.entries(t).filter((([e])=>!this.getSkippedEmptyOptions().includes(i+e)));for(const[n,r]of s){const s=i+n;Array.isArray(r)?r.forEach(((t,i)=>{C(t)&&this.resetEmptyOptions(e,t,`${s}[${i}].`)})):C(r)&&this.resetEmptyOptions(e,r,`${s}.`),null===r&&(delete t[n],e(s))}}changeLoadingPanelVisibility(e){this.dispatchEvent(new r(e))}invokeOptionsLoadedEvent(e){this.dispatchEvent(new o(e))}getServerOptions(){return this.parseServerOptions(this.optionChunks)}getSkippedEmptyOptions(){return[]}}s([d({attribute:"option-chunks"})],f.prototype,"optionChunks",void 0);const x={dxBarGauge:"barGauge",dxBullet:"bullet",dxChart:"chart",dxFunnel:"funnel",dxCircularGauge:"gauge",dxLinearGauge:"gauge",dxVectorMap:"map",dxPieChart:"pie",dxPolarChart:"polar",dxRangeSelector:"rangeSelector",dxSankey:"sankey",dxSparkline:"sparkline",dxTreeMap:"treeMap"};function y(e,t,i,s){const n=x[t];if(!e||!n||!i)return null;null!=s||(s=`dx-${n}-blazor-theme`);const r=function(e,t){const i={},s=window.getComputedStyle(t);for(const[t,n]of Object.entries(e)){const e=s.getPropertyValue(n);if(!e)continue;O(t.split("."),e,i)}return i}(e,i);return Object.keys(r).length>0?{name:s,[n]:r}:null}function O(e,t,i){e.reduce(((i,s,n)=>{var r;return n===e.length-1?i[s]=t:null!==(r=i[s])&&void 0!==r?r:i[s]={}}),i)}const v="dxInstance",w="width",S="height",_="visible",k="locale";class W extends f{constructor(){super(...arguments),this._containerSizeChanged=!1}canCreateComponent(){return!this._widgetPromise&&!!this.getWidgetElement()}createComponent(e){this._widgetPromise=this.initializeWidget(e)}disposeComponent(){var e;null===(e=this._widgetPromise)||void 0===e||e.then((e=>this.disposeWidget(e))).catch((e=>{t(this)||console.error(e)}))}afterWidgetResolved(e){if(this._widgetPromise)return this._widgetPromise.then(e).catch((e=>{t(this)||console.error(e)}))}disposeWidget(e){e.dispose()}updateComponent(e){var t;Object.keys(e).length?null===(t=this._widgetPromise)||void 0===t||t.then((t=>this.updateWidgetOptions(t,e))):this._containerSizeChanged&&this.onlyContainerSizeChanged()}createInitOptions(){return{...this.createWidgetDefaultOptions(),...this.createWidgetHandlers()}}createWidgetDefaultOptions(){return{}}createWidgetHandlers(){return{onInitialized:e=>this.onWidgetInitialized(e),onDisposing:e=>this.onWidgetDisposing(e)}}getWidgetCustomTheme(){return y(this.getThemeDependentOptionsDict(),this.getWidgetTypeName(),this)}getThemeDependentOptionsDict(){return{}}async initializeWidget(e){const t=this.getWidgetCustomTheme();t&&(await this.widgetLoader.registerCustomTheme(t),e.theme=t.name),this.processLocalization(e),this.resetEmptyOptions((e=>{}),e);const i=this.getWidgetElement(),s=this.getWidgetTypeName(),n=this.getWidgetModuleName();return this.widgetLoader.initializeWidget(i,s,n,e)}get widgetLoader(){var e;return null!==(e=this._loader)&&void 0!==e||(this._loader=m.getInstance()),this._loader}getWidgetElement(){return this.getElementsByClassName(this.getWidgetElementClassName())[0]}getWidgetElementClassName(){return"dxbl-widget-container"}getWidgetModuleName(){return"viz"}async processLocalization(e){e[k]&&(await this.widgetLoader.setLocalization(e[k]),delete e[k])}updateWidgetOptions(e,t){e.beginUpdate(),this.resetEmptyOptions((t=>{e.resetOption(t)}),t),e.option(t),e.endUpdate()}onWidgetInitialized(e){e.element[v]=e.component}onWidgetDisposing(e){delete e.element[v]}setStyleOptionForElement(e,t){this.getContainerToSetStyle().style.setProperty(e,t)}processSpecialOptions(e){const t=void 0!==e[w],i=void 0!==e[S];this._containerSizeChanged=t||i,t&&(this.setStyleOptionForElement(w,this.getCorrectedSizeValue(e[w])),delete e[w]),i&&(this.setStyleOptionForElement(S,this.getCorrectedSizeValue(e[S])),delete e[S]),void 0!==e[_]&&l.DomUtils.toggleClassName(this,c.Invisible,!e[_])}getCorrectedSizeValue(e){if(/^[0-9]+$/.test(e)){const t=parseFloat(e);return isNaN(t)?e:`${t}px`}return e}getContainerToSetStyle(){return this}onlyContainerSizeChanged(){this.changeLoadingPanelVisibility(!1)}executeClientMethod(e,...t){return this._widgetPromise?this._widgetPromise.then((i=>i[e](...t))).catch((e=>console.error(e))):Promise.resolve()}}export{W as D,m as W};
