import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{D as s,a as o}from"./dx-grid-layout-element-base-9d87dc71.js";import{e as r}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./custom-events-helper-e7f279d3.js";let m=class extends s{createRenderRoot(){return this}};m=e([r("dxbl-grid-layout-item")],m);let i=class extends o{};i=e([r("dxbl-grid-layout-root")],i);let l=class extends t{};l=e([r("dxbl-grid-layout")],l);export{l as DxGridLayout,m as DxGridLayoutItem,i as DxGridLayoutRoot};
