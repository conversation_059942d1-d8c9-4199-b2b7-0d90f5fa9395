import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{DxSplitter as i}from"./dx-splitter-bb096bb4.js";import{C as s}from"./custom-events-helper-e7f279d3.js";import{D as r,P as n,c as a,a as l,H as o,L as p}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{D as h}from"./dragging-helper-863a69d5.js";import{k as d}from"./key-ffa272aa.js";import{n as c}from"./property-4ec0b52d.js";import{e as g}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./enumConverter-6047c3ff.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./eventhelper-8bcec49f.js";import"./devices-17b9ba08.js";import"./css-classes-c63af734.js";class m{constructor(e){const t=e.splitter;this._separator=e,this._prevPane=e.prevPane,this._nextPane=e.nextPane,this._splitterInitialSize=t.isVertical?t.clientHeight:t.clientWidth,this._prevPaneInitialSize=this.getPaneVisibleSize(this._prevPane),this._nextPaneInitialSize=this.getPaneVisibleSize(this._nextPane)}cancel(){this._prevPane.setInitialSize(),this._nextPane.setInitialSize()}stop(){if(0===this.accumulatedOffset)this.cancel();else{const e=new v(this._prevPane.size,this._nextPane.size);this._separator.dispatchEvent(new C(e))}}get accumulatedOffset(){return this.getPaneVisibleSize(this._prevPane)-this._prevPaneInitialSize}resizePanes(e){const[t,i]=this.calculatePreviousOffsetRange(this._prevPane,this._prevPaneInitialSize),[s,r]=this.calculateNextOffsetRange(this._nextPane,this._nextPaneInitialSize),n=Math.max(t,s),a=Math.min(i,r);n>a||(e=Math.min(a,Math.max(n,e)),this.setPaneSize(this._prevPane,this._prevPaneInitialSize+e),this.setPaneSize(this._nextPane,this._nextPaneInitialSize-e))}calculatePreviousOffsetRange(e,t){const[i,s]=this.calculateMinMaxSizes(e);return[i-t,s-t]}calculateNextOffsetRange(e,t){const[i,s]=this.calculateMinMaxSizes(e);return[t-s,t-i]}calculateMinMaxSizes(e){return[e.minSize?this.parseMinMaxSize(e.minSize):0,e.maxSize?this.parseMinMaxSize(e.maxSize):this._splitterInitialSize]}parseMinMaxSize(e){const t=parseFloat(e);return e.includes("%")?t*this._splitterInitialSize/100:t}setPaneSize(e,t){e.size=e.size.includes("%")?t/this._splitterInitialSize*100+"%":t+"px"}getPaneVisibleSize(e){return this._separator.splitter.isVertical?e.offsetHeight:e.offsetWidth}}var u;class v{constructor(e,t){this.prevPaneSize=e,this.nextPaneSize=t}}class z{constructor(e,t){this.prevPaneSize=e,this.nextPaneSize=t}}class C extends CustomEvent{constructor(e){super(C.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!0})}}C.eventName=i.TagName+".separator-dragged";class _ extends CustomEvent{constructor(e){super(_.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!0})}}_.eventName=i.TagName+".separator-collapse-toggled",s.register(C.eventName,(e=>e.detail)),s.register(_.eventName,(e=>e.detail));let f=u=class extends t{constructor(){super(...arguments),this._resizeHelper=null,this.allowCollapse=!1,this.allowResize=!1,this._pointerEventsHelper=new r(this),this._dragStartHandler=this.onDragStart.bind(this),this._dragCancelHandler=this.onDragCancel.bind(this),this._dragStopHandler=this.onDragStop.bind(this),this._keyDownResizeHandler=this.onKeyDownResize.bind(this),this._keyUpResizeHandler=this.onKeyUpResize.bind(this),this._keyDownCollapseHandler=this.onKeyDownCollapse.bind(this),this._dblClickCollapseHandler=this.onDblClickCollapse.bind(this)}disconnectedCallback(){super.disconnectedCallback(),this.allowResize&&this.disposeResizing(),this.allowCollapse&&this.disposeCollapsing()}updated(e){super.updated(e),e.has("allowResize")&&(this.allowResize?this.initializeResizing():this.disposeResizing()),e.has("allowCollapse")&&(this.allowCollapse?this.initializeCollapsing():this.disposeCollapsing())}get splitter(){return this.closest(i.TagName)}get prevPane(){return this.previousElementSibling}get nextPane(){return this.nextElementSibling}initializeResizing(){this._pointerEventsHelper.initialize(),this.addEventListener(n.eventName,this._dragStartHandler),this.addEventListener(a.eventName,this._dragCancelHandler),this.addEventListener(l.eventName,this._dragStopHandler),this.addEventListener("keydown",this._keyDownResizeHandler),this.addEventListener("keyup",this._keyUpResizeHandler)}disposeResizing(){this._pointerEventsHelper.dispose(),this.removeEventListener(n.eventName,this._dragStartHandler),this.removeEventListener(a.eventName,this._dragCancelHandler),this.removeEventListener(l.eventName,this._dragStopHandler),this.removeEventListener("keydown",this._keyDownResizeHandler),this.removeEventListener("keyup",this._keyUpResizeHandler)}initializeCollapsing(){this.addEventListener("keydown",this._keyDownCollapseHandler),this.addEventListener("dblclick",this._dblClickCollapseHandler)}disposeCollapsing(){this.removeEventListener("keydown",this._keyDownCollapseHandler),this.removeEventListener("dblclick",this._dblClickCollapseHandler)}onDragStart(e){u.dragHelper.start(this,e.detail.source),e.stopPropagation()}onDragCancel(e){u.dragHelper.cancel(),e.stopPropagation()}onDragStop(e){u.dragHelper.stop(),e.stopPropagation()}onKeyDownResize(e){var t;const i=d.KeyUtils.getEventKeyCode(e);this.isArrow(i)&&(this._resizeHelper||(this._resizeHelper=new m(this)),(null===(t=this.splitter)||void 0===t?void 0:t.isVertical)?d.KeyUtils.getEventKeyCode(e)===d.KeyCode.Up?(this._resizeHelper.resizePanes(this._resizeHelper.accumulatedOffset-5),e.preventDefault()):d.KeyUtils.getEventKeyCode(e)===d.KeyCode.Down&&(this._resizeHelper.resizePanes(this._resizeHelper.accumulatedOffset+5),e.preventDefault()):d.KeyUtils.getEventKeyCode(e)===d.KeyCode.Left?(this._resizeHelper.resizePanes(this._resizeHelper.accumulatedOffset-5),e.preventDefault()):d.KeyUtils.getEventKeyCode(e)===d.KeyCode.Right&&(this._resizeHelper.resizePanes(this._resizeHelper.accumulatedOffset+5),e.preventDefault()))}onKeyUpResize(e){const t=d.KeyUtils.getEventKeyCode(e);this.isArrow(t)&&this._resizeHelper&&(this._resizeHelper.stop(),this._resizeHelper=null)}onKeyDownCollapse(e){d.KeyUtils.getEventKeyCode(e)===d.KeyCode.Enter&&(this.toggleCollapsed(),e.preventDefault())}onDblClickCollapse(e){this.toggleCollapsed(),e.preventDefault()}toggleCollapsed(){var e;const t=(null===(e=this.splitter)||void 0===e?void 0:e.isVertical)?new z(this.prevPane.offsetHeight,this.nextPane.offsetHeight):new z(this.prevPane.offsetWidth,this.nextPane.offsetWidth);this.dispatchEvent(new _(t))}isArrow(e){return e===d.KeyCode.Left||e===d.KeyCode.Up||e===d.KeyCode.Right||e===d.KeyCode.Down}get handlePointerEventsMode(){return o.Dragging}get handlePointerEventsTarget(){return this}get handlePointerEventsDelay(){return p}get hoverTitleElementsSelector(){return null}get bypassNonInlineHoverTitleElementChildSelector(){return null}shouldProcessPointerEvent(e){return!e.target.closest(".dxbl-splitter-button")}};f.dragHelper=new class extends h{cancel(){this.splitterDragContext.resizeHelper.cancel(),super.stop()}stop(){this.splitterDragContext.resizeHelper.stop(),super.stop()}getDraggableElement(e){return document.createElement("div")}hideDraggableElement(){}get splitterDragContext(){return this.draggableContext}createDraggableContext(e,t,i){const s=e;return{...super.createDraggableContext(e,t,i),resizeHelper:new m(s),isVertical:s.splitter.isVertical}}refreshUICore(){if(super.refreshUICore(),!this.splitterDragContext)return;this.splitterDragContext.resizeHelper.resizePanes(this.splitterDragContext.isVertical?this.splitterDragContext.currentCursorPosition.y-this.splitterDragContext.initialCursorPosition.y:this.splitterDragContext.currentCursorPosition.x-this.splitterDragContext.initialCursorPosition.x)}},e([c({type:Boolean,attribute:"allow-collapse"})],f.prototype,"allowCollapse",void 0),e([c({type:Boolean,attribute:"allow-resize"})],f.prototype,"allowResize",void 0),f=u=e([g("dxbl-splitter-separator")],f);export{f as DxSplitterSeparator,z as SplitterSeparatorCollapseToggledContext,v as SplitterSeparatorDraggedContext,C as SplitterSeparatorDraggedEvent};
