import{_ as e}from"./tslib.es6-d65164b3.js";import{b as t}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{E as o}from"./eventhelper-8bcec49f.js";import{C as n}from"./custom-events-helper-e7f279d3.js";import{d as s}from"./constants-da6cacac.js";import{P as r}from"./portal-b3727c25.js";import{t as i,u as p,q as d,v as l,w as a,M as u,r as h}from"./popup-355ecaa4.js";import{e as c,d as m,f as E}from"./logicaltreehelper-67db40f1.js";import{DxMaskedInputEditor as D}from"./masked-input-0c0a9541.js";import{k as v}from"./key-ffa272aa.js";import{I as w}from"./input-66769c52.js";import{B as b}from"./constants-3209ffde.js";import{C as P}from"./css-classes-c63af734.js";import{containsFocusHiddenAttribute as g,addFocusHiddenAttribute as y}from"./focus-utils-ae044224.js";import{f as C}from"./focustrap-d11cfef9.js";import{d as f}from"./dom-554d0cc7.js";import{n as O}from"./property-4ec0b52d.js";class S extends Event{constructor(){super(S.eventName,{bubbles:!0,composed:!0,cancelable:!0})}}S.eventName=s+".opendropdown";class H extends Event{constructor(){super(H.eventName,{bubbles:!0,composed:!0,cancelable:!0})}}H.eventName=s+".closedropdown";class K extends Event{constructor(){super(K.eventName,{bubbles:!0,composed:!1,cancelable:!0})}}K.eventName=s+".pointerdown";class R extends Event{constructor(){super(R.eventName,{bubbles:!0,composed:!0,cancelable:!0})}}R.eventName=s+".trycleareditorvalue",n.register(S.eventName,(e=>e.detail)),n.register(H.eventName,(e=>e.detail));class _{constructor(e){this.boundOnPopupElementChangedHandler=this.onPopupElementChanged.bind(this),this.boundOnPopupShownHandler=this.onPopupShown.bind(this),this.boundOnPopupClosedHandler=this.onPopupClosed.bind(this),this.boundOnPopupPortalElementsChangedHandler=this.onDropDownPortalElementsChanged.bind(this),this.control=e,this.popupPortal=null,this._popupElement=null,this._adaptiveDropDownElement=null}get popupElement(){return this._popupElement}get useMobileFocusBehaviour(){var e;return(null===(e=this._adaptiveDropDownElement)||void 0===e?void 0:e.adaptivityEnabled)||!1}onConnectedCallback(){this.ensurePopupInfrastructure(),this.control.addEventListener(r.eventName,this.boundOnPopupElementChangedHandler)}onDisconnectedCallback(){this.control.removeEventListener(r.eventName,this.boundOnPopupElementChangedHandler),this.disposePopupInfrastructure()}onSlotChanged(){this.disposePopupInfrastructure(),this.ensurePopupInfrastructure()}getPopupPortal(){if(!this.control.shadowRoot)return null;const e=this.control.shadowRoot.querySelector("slot");if(!e)return null;return e.assignedNodes().find((e=>c(e)))}onPopupElementChanged(e){o.getOriginalSource(e)===this.popupPortal&&(this.disposePopupElement(),this.ensurePopupElement())}onPopupShown(e){this.popupElement&&m(this.popupElement)&&this.control.addLogicalChild(this.popupElement)}onPopupClosed(e){this.popupElement&&m(this.popupElement)&&this.control.removeLogicalChild(this.popupElement)}onDropDownPortalElementsChanged(e){this.ensurePopupElement()}ensurePopupInfrastructure(){var e;this.popupPortal=this.getPopupPortal(),null===(e=this.popupPortal)||void 0===e||e.addEventListener(r.eventName,this.boundOnPopupPortalElementsChangedHandler),this.ensurePopupElement()}ensurePopupElement(){var e,t,o;this.popupPortal&&(this._popupElement=null===(e=this.popupPortal)||void 0===e?void 0:e.popupBase,null===(t=this._popupElement)||void 0===t||t.addEventListener(i.eventName,this.boundOnPopupShownHandler),null===(o=this._popupElement)||void 0===o||o.addEventListener(p.eventName,this.boundOnPopupClosedHandler),this._popupElement&&E(this._popupElement)&&(this._adaptiveDropDownElement=this._popupElement),this.control.ensurePopupElement())}disposePopupInfrastructure(){var e;null===(e=this.popupPortal)||void 0===e||e.removeEventListener(r.eventName,this.boundOnPopupPortalElementsChangedHandler),this.popupPortal=null,this._adaptiveDropDownElement=null,this.disposePopupElement()}disposePopupElement(){var e,t;null===(e=this._popupElement)||void 0===e||e.removeEventListener(i.eventName,this.boundOnPopupShownHandler),null===(t=this._popupElement)||void 0===t||t.removeEventListener(p.eventName,this.boundOnPopupClosedHandler),this._popupElement=null,this.control.disposePopupElement()}}class N{}N.ClassName=P.Prefix+"-edit-dropdown",N.Header=N.ClassName+"-header",N.Body=P.Prefix+"-body",N.DropDownButton=P.Prefix+"-edit-btn-dropdown";class I extends d{constructor(e,t,o){super(e,t),this._focusTrapElement=null,this._parentStrategy=o,this._needRestoreInputFocus=!1}get parentStrategy(){return this._parentStrategy}get dropDownEditor(){return this.parentStrategy.dropDownEditor}getHeaderButtons(){return Array.from(this.targetElement.querySelectorAll(`.${N.Header} > .${b.Button}`))}addEventSubscriptions(){super.addEventSubscriptions(),this.boundOnDropDownEditorPointerDownHandler||(this.boundOnDropDownEditorPointerDownHandler=this.onDropDownEditorPointerDownHandler.bind(this),this.targetElement.addEventListener(K.eventName,this.boundOnDropDownEditorPointerDownHandler)),this.boundOnPopupClosingResultRequestedHandler||(this.boundOnPopupClosingResultRequestedHandler=this.onPopupClosingResultRequested.bind(this),this.targetElement.addEventListener(l.eventName,this.boundOnPopupClosingResultRequestedHandler),this.targetElement.addEventListener(a.eventName,this.boundOnPopupClosingResultRequestedHandler))}onDispose(){this.boundOnDropDownEditorPointerDownHandler&&(this.targetElement.removeEventListener(K.eventName,this.boundOnDropDownEditorPointerDownHandler),this.boundOnDropDownEditorPointerDownHandler=void 0),this.boundOnPopupClosingResultRequestedHandler&&(this.targetElement.removeEventListener(l.eventName,this.boundOnPopupClosingResultRequestedHandler),this.targetElement.removeEventListener(a.eventName,this.boundOnPopupClosingResultRequestedHandler),this.boundOnPopupClosingResultRequestedHandler=void 0)}handleKeyDown(e){const t=v.KeyUtils.getEventKeyCode(e);return v.KeyUtils.getEventKeyCode(e)!==v.KeyCode.Tab&&(t===v.KeyCode.Esc||(e.altKey||e.metaKey)&&t===v.KeyCode.Up?(this._needRestoreInputFocus=!0,this.closeDropDown(),!0):super.handleKeyDown(e))}onDropDownEditorPointerDownHandler(){this.dropDownEditor.focused||this.parentStrategy.closeDropDown()}onPopupClosingResultRequested(){this.targetElement.contains(document.activeElement)?this.dropDownEditor.focusInternal():this.dropDownEditor.raiseFocusOutInternal(),this._needRestoreInputFocus=!1}closeDropDown(){this.parentStrategy.closeDropDown()}focusElementInRootPath(){return!1}handlePopupShown(){this._needRestoreInputFocus=!1;const e=this.getNestedContentElement();this._focusTrapElement=e||this.targetElement,C.activate(this._focusTrapElement,!1,!0),super.handlePopupShown(),g(this.dropDownEditor)&&y(this.targetElement),this.dropDownEditor.classList.add(P.Focused)}onPopupClosed(){this.dropDownEditor.classList.remove(P.Focused),C.deactivate(this._focusTrapElement,!1),this._focusTrapElement=null,this._needRestoreInputFocus&&this.dropDownEditor.focusInternal(),this.handlePopupClosed()}}class x extends u{constructor(e,t,o){super(e,t),this._parentStrategy=o}get parentStrategy(){return this._parentStrategy}handlePopupShown(){super.handlePopupShown(),g(this.parentStrategy.dropDownEditor)&&y(this.targetElement)}}class B extends w{constructor(e){super(e)}get dropDownEditor(){return this._editor}handleKeyDown(e){const t=v.KeyUtils.getEventKeyCode(e);return(e.altKey||e.metaKey)&&t===v.KeyCode.Down?(this.openDropDown(),!0):(e.altKey||e.metaKey)&&t===v.KeyCode.Up?(this.closeDropDown(),!0):e.altKey&&t===v.KeyCode.Delete?(this.tryClearEditorValue(),!0):super.handleKeyDown(e)}closeDropDown(){this.dropDownEditor.closeDropDown()}openDropDown(){this.dropDownEditor.openDropDown()}closeDropDownAndApplyValue(e){this.performShortcutEvent(e)}tryClearEditorValue(){this.dropDownEditor.enabled&&!this.dropDownEditor.isReadOnly&&this.dropDownEditor.dispatchClearEditorValue()}}class L extends B{constructor(e){super(e)}initialize(){super.initialize(),this.subscribeToEvents()}subscribeToEvents(){this.boundOnPopupKeyboardStrategyCreating||(this.boundOnPopupKeyboardStrategyCreating=this.onPopupKeyboardStrategyCreating.bind(this),this.targetElement.addEventListener(h.eventName,this.boundOnPopupKeyboardStrategyCreating))}onPopupKeyboardStrategyCreating(e){e.detail.factory={createPopup:(e,t)=>this.createPopupStrategy(e,t),createModal:(e,t)=>this.createModalStrategy(e,t)}}onDispose(){this.boundOnPopupKeyboardStrategyCreating&&(this.targetElement.removeEventListener(h.eventName,this.boundOnPopupKeyboardStrategyCreating),this.boundOnPopupKeyboardStrategyCreating=void 0),super.onDispose()}createPopupStrategy(e,t){return new I(e,t,this)}createModalStrategy(e,t){return new x(e,t,this)}}class F extends B{constructor(e){super(e)}handleKeyDown(e){const t=this.dropDownEditor;return t&&t.shouldHandleKeyDown?t.shouldHandleKeyDown(e):super.handleKeyDown(e)}}var k;!function(e){e.ContentOrEditorWidth="ContentOrEditorWidth",e.ContentWidth="ContentWidth",e.EditorWidth="EditorWidth"}(k||(k={}));class W extends D{constructor(){super(),this.boundOnClickHandler=this.onClick.bind(this),this.isDropDownOpen=!1,this.popupHelper=new _(this),this.dropDownWidthMode=k.ContentWidth,this._componentResizeObserver=new ResizeObserver(this.onSizeChanged.bind(this))}get useMobileFocusBehaviour(){return this.popupHelper.useMobileFocusBehaviour}get popupElement(){return this.popupHelper.popupElement}get popupDOMElement(){return this.popupElement}get dropDownElement(){return this.popupElement}get shouldProcessFocusOut(){if(!this.enabled||this.isReadOnly)return!1;return!(this.editBoxTemplateElement&&this.inputElement&&!f.DomUtils.isItParent(this.editBoxTemplateElement,this.inputElement))}connectedCallback(){super.connectedCallback(),this.popupHelper.onConnectedCallback(),this._componentResizeObserver.observe(this)}disconnectedCallback(){this.popupHelper.onDisconnectedCallback(),super.disconnectedCallback(),this._componentResizeObserver.disconnect()}contentChanged(){super.contentChanged(),this.popupHelper.onSlotChanged(),m(this.editBoxElement)&&this.addLogicalChild(this.editBoxElement),this.addEventListener("click",this.boundOnClickHandler)}focusInternal(){var e;this.shouldApplySelectionOnFocus=!1,null===(e=this.editBoxElement)||void 0===e||e.focus(),this.shouldApplySelectionOnFocus=!0}processFocusOut(e){var t,n,s;const r=o.getEventSource(e);if(null===(t=this.popupElement)||void 0===t?void 0:t.contains(r))return;const i=null!==(n=e.relatedTarget)&&void 0!==n?n:document.activeElement;this.contains(i)&&!this.isFocusableElementInMainElementTemplate(i)||(null===(s=this.popupElement)||void 0===s?void 0:s.contains(i))||this.raiseFocusOutInternal()}raiseFocusOutInternal(){this.raiseFocusOut(this.fieldElement?this.fieldElementValue:this.fieldText)}processFocusIn(){this.focused&&super.processFocusIn()}processPointerDown(e){return y(this),super.processPointerDown(e)}canHandlePointerDown(e){return this.contains(e.target)&&o.containsInComposedPath(e,this.isDropDownButtonElement.bind(this))||t.containsInComposedPath(e,this.inputElement)}processKeyDownServerCommand(e){return!1}shouldProcessKeyDown(e){return!e.altKey&&super.shouldProcessKeyDown(e)}shouldProcessKeyUp(e){return!e.altKey&&super.shouldProcessKeyUp(e)}ensurePopupElement(){}disposePopupElement(){}openDropDown(){this.dispatchEvent(new S)}closeDropDown(){this.dispatchEvent(new H)}dispatchClearEditorValue(){this.dispatchEvent(new R)}async processCapturedPointerAsync(e,t){return this.canHandlePointerDown(e)&&(t.handled=!0),Promise.resolve()}createKeyboardStrategy(){return new B(this)}updated(e){super.updated(e),e.has("dropDownWidthMode")&&this.reconnectComponentResizeObserver()}toggleDropDownVisibility(){this.isDropDownOpen?this.closeDropDown():this.enabled&&!this.isReadOnly&&this.openDropDown()}tryOpenDropDown(){this.enabled&&!this.isReadOnly&&this.openDropDown()}tryCloseDropDown(){this.isDropDownOpen&&this.closeDropDown()}onSizeChanged(e,t){if(!this.dropDownElement)return;const o=`${e[0].target.getBoundingClientRect().width}px`;this.dropDownElement.desiredWidth=this.dropDownWidthMode===k.EditorWidth?o:null,this.dropDownElement.minDesiredWidth=this.dropDownWidthMode===k.ContentOrEditorWidth?o:null}dispatchDropDownEditorPointerDownEvent(){document.dispatchEvent(new K)}isDropDownButtonElement(e){var t;return!!e&&(null===(t=e.classList)||void 0===t?void 0:t.contains(N.DropDownButton))}isEditBoxElement(e){return e===this.fieldElement||e===this.editBoxTemplateElement}isFieldElement(e){return e===this.fieldElement}isEditBoxTemplateElement(e){return e===this.editBoxTemplateElement}reconnectComponentResizeObserver(){this.isDOMAttached&&(this._componentResizeObserver.disconnect(),this.dropDownElement&&(this.dropDownElement.desiredWidth=null,this.dropDownElement.minDesiredWidth=null),this._componentResizeObserver.observe(this))}onClick(e){this.processClick(e)}processClick(e){return o.containsInComposedPath(e,this.isDropDownButtonElement.bind(this))&&(this.toggleDropDownVisibility(),this.focused||(this.focusInternal(),this.dispatchDropDownEditorPointerDownEvent()),o.markHandled(e)),!0}}e([O({type:Boolean,attribute:"is-dropdown-open"})],W.prototype,"isDropDownOpen",void 0),e([O({type:k,attribute:"dropdown-width-mode"})],W.prototype,"dropDownWidthMode",void 0);export{W as D,I as E,L as a,k as b,F as c,N as d};
