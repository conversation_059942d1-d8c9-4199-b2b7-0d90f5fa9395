import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t}from"./devextreme-widget-wrapper-33881f73.js";import{C as s}from"./custom-events-helper-e7f279d3.js";import{C as r}from"./events-a8fe5872.js";import{c as o}from"./create-after-timeout-fn-executor-38b3d79d.js";import{C as n}from"./client-component-style-helper-195fa7c3.js";import{e as i}from"./custom-element-267f9a21.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./utils-b5b2c8a9.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";const a="dxbl-gauge-base";class p extends r{constructor(){super(p.eventName)}}p.eventName=a+".drawn";class d extends r{constructor(){super(d.eventName)}}d.eventName=a+".exported",s.register(p.eventName,(e=>e.detail)),s.register(d.eventName,(e=>e.detail));class c extends t{constructor(){super(),this._styleHelper=n.getInstance(),this._drawnExecutor=o(this.onGaugeDrawn.bind(this))}createWidgetDefaultOptions(){return{palette:this._styleHelper.palette}}createWidgetHandlers(){return{...super.createWidgetHandlers(),onDrawn:()=>this.onGaugeDrawn(),onOptionChanged:()=>this.onGaugeOptionChanged(),onExported:()=>this.onGaugeExported()}}onGaugeDrawn(){this._drawnExecutor.reset(),this.changeLoadingPanelVisibility(!1),this.dispatchEvent(new p)}onGaugeOptionChanged(){this._drawnExecutor.execute()}onGaugeExported(){this.dispatchEvent(new d)}onlyContainerSizeChanged(){this.onGaugeDrawn()}exportTo(...e){return this.executeClientMethod("exportTo",...e)}print(...e){return this.executeClientMethod("print",...e)}svg(...e){return this.executeClientMethod("svg",...e)}}const l="dxbl-bar-gauge",m="legend",u="itemCaptions",g="color",h="font",$="subtitle",x="title",j=`${h}.${g}`,f=`${m}.${j}`,b=`${x}.${j}`,v=`${x}.${$}.${j}`,w=`${m}.${b}`,C=`${m}.${v}`,D=`--${l}`,G=`${D}-${m}`,y=`${h}-${g}`,E=`${x}-${y}`,O=`${$}-${y}`;let N=class extends c{getWidgetTypeName(){return"dxBarGauge"}processSpecialOptions(e){super.processSpecialOptions(e),this.prepareLegend(e[m])}prepareLegend(e){const t=null==e?void 0:e[u];t&&(e.customizeText=e=>{var s,r;const o=null===(s=e.item)||void 0===s?void 0:s.index;return null!==(r=t[o])&&void 0!==r?r:e.text},delete e[u])}getThemeDependentOptionsDict(){const e={[b]:`${D}-${E}`,[v]:`${D}-${O}`,[f]:`${G}-item-${y}`,[w]:`${G}-${E}`,[C]:`${G}-${O}`};return{...super.getThemeDependentOptionsDict(),...e}}};N=e([i(l)],N);export{N as DxBarGauge};
