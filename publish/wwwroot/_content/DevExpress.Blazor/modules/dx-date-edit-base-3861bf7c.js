import{a as e,E as t,D as n}from"./dx-dropdown-base3-726be7be.js";import{k as i}from"./key-ffa272aa.js";import{T as s}from"./constants-ed9663d1.js";import{A as o}from"./events-f873e646.js";import{C as r,a}from"./dx-calendar-1293eea4.js";import{F as l}from"./keyboard-navigation-strategy-ea41c807.js";import{addFocusHiddenAttribute as d}from"./focus-utils-ae044224.js";import{RollerCssClasses as u,RollerInitializedEvent as c}from"./roller-e212033a.js";class h extends e{createPopupStrategy(e,t){return new m(e,t,this)}}class m extends t{constructor(e,t,n){super(e,t,n)}handleKeyDown(e){const t=i.KeyUtils.getEventKeyCode(e);if(e.shiftKey&&t===i.KeyCode.Tab&&this.isFirstHeaderButtonSelected()&&!this.isRollerFooterExist()){const e=this.getLastRollerItem();if(e)return this.focusItem(e),!0}return t===i.KeyCode.Enter&&this.isTimeRollerSelected()||t===i.KeyCode.Space&&this.isLastTimeRollerSelected()?(this.closeDropDownAndApplyValue(e),!0):super.handleKeyDown(e)}handlePopupShown(){this.editorFocused=this.dropDownEditor.focused,this.editorFocused||d(this.dropDownEditor),super.handlePopupShown(),this.editorFocused&&this.focusFirstRollerItem()}onRollerInitialized(){this.editorFocused&&this.focusFirstRollerItem()}focusFirstRollerItem(){this.focusItem(this.getFirstRollerItem())}focusLastRollerItem(){this.focusItem(this.getLastRollerItem())}focusItem(e){e&&(l.makeElementFocusable(e),l.focusElement(e))}getFirstRollerItem(){return this.targetElement.querySelector(`.${u.RollerAfter}`)}getLastRollerItem(){const e=this.targetElement.querySelectorAll(`.${u.RollerAfter}`);return e[e.length-1]}addEventSubscriptions(){super.addEventSubscriptions(),this.boundOnRollerInitializedHandler||(this.boundOnRollerInitializedHandler=this.onRollerInitialized.bind(this),this.targetElement.addEventListener(c.eventName,this.boundOnRollerInitializedHandler))}onDispose(){this.boundOnRollerInitializedHandler&&(this.targetElement.removeEventListener(c.eventName,this.boundOnRollerInitializedHandler),this.boundOnRollerInitializedHandler=void 0)}isTimeRollerSelected(){var e;return null===(e=document.activeElement)||void 0===e?void 0:e.classList.contains(`${u.RollerAfter}`)}isLastTimeRollerSelected(){var e;return this.isTimeRollerSelected()&&!(null===(e=document.activeElement)||void 0===e?void 0:e.nextElementSibling)}isFirstHeaderButtonSelected(){return this.getHeaderButtons()[0]===document.activeElement}isRollerFooterExist(){return!!this.targetElement.querySelector(`.${u.RollerFooter}`)}closeDropDownAndApplyValue(e){this.parentStrategy.closeDropDownAndApplyValue(e)}}class b extends h{createPopupStrategy(e,t){return new v(e,t,this)}}class v extends m{constructor(e,t,n){super(e,t,n)}handleKeyDown(e){return i.KeyUtils.getEventKeyCode(e)===i.KeyCode.Space&&this.isLastDateRollerSelected()?(this.performShortcutEvent(e),!0):super.handleKeyDown(e)}handlePopupShown(){super.handlePopupShown(),this.editorFocused&&this.focusItem(this.getSelectedDay())}addEventSubscriptions(){super.addEventSubscriptions(),this.boundOnActiveTabChangedHandler||(this.boundOnActiveTabChangedHandler=this.onActiveTabChanged.bind(this),this.targetElement.addEventListener(o.eventName,this.boundOnActiveTabChangedHandler)),this.boundOnCalendarKeyboardNavigationLeavedHandler||(this.boundOnCalendarKeyboardNavigationLeavedHandler=this.onCalendarKeyboardNavigationLeaved.bind(this),this.targetElement.addEventListener(r.eventName,this.boundOnCalendarKeyboardNavigationLeavedHandler))}onDispose(){this.boundOnActiveTabChangedHandler&&(this.targetElement.removeEventListener(o.eventName,this.boundOnActiveTabChangedHandler),this.boundOnActiveTabChangedHandler=void 0),this.boundOnCalendarKeyboardNavigationLeavedHandler&&(this.targetElement.removeEventListener(r.eventName,this.boundOnCalendarKeyboardNavigationLeavedHandler),this.boundOnCalendarKeyboardNavigationLeavedHandler=void 0),super.onDispose()}onActiveTabChanged(){var e;(null===(e=document.activeElement)||void 0===e?void 0:e.classList.contains(s.Item))||this.focusFirstRollerItem()}onCalendarKeyboardNavigationLeaved(){if(!this.getRibbonTabs()){const e=l.findFocusableElements(this.targetElement).pop();e&&this.focusItem(e)}}isLastTimeRollerSelected(){const e=this.getRibbonTabs();return null!==e&&e.getActiveTabIndex()===e.tabCount-1&&super.isLastTimeRollerSelected()}isLastDateRollerSelected(){const e=this.getRibbonTabs();return null!==e&&0===e.getActiveTabIndex()&&super.isLastTimeRollerSelected()}getCalendar(){return this.targetElement.querySelector(`.${a.MainElement}`)}getRibbonTabs(){return this.targetElement.querySelector(`.${s.Prefix}`)}getSelectedDay(){var e;return null===(e=this.getCalendar())||void 0===e?void 0:e.querySelector(`.${a.SelectedItem}`)}}class p extends n{connectedCallback(){super.connectedCallback()}disconnectedCallback(){super.disconnectedCallback()}toggleDropDownVisibility(){this.fieldText!==this.fieldElementValue&&this.raiseFieldChange(),super.toggleDropDownVisibility()}openDropDown(){this.fieldText!==this.fieldElementValue&&this.raiseFieldChange(),super.openDropDown()}createKeyboardStrategy(){return new b(this)}}export{p as D,h as T};
