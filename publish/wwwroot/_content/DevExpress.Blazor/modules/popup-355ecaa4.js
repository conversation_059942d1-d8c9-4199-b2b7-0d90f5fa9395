import{_ as e}from"./tslib.es6-d65164b3.js";import{a as t,L as i,D as s,S as n,R as o}from"./layouthelper-67dd777a.js";import{R as r}from"./rafaction-bba7928b.js";import{P as a,a as l}from"./point-e4ec110e.js";import{S as h}from"./screenhelper-e9ec6e3e.js";import{T as d}from"./transformhelper-ebad0156.js";import{PositionChangingEvent as c}from"./positiontracker-754c1e75.js";import{D as u}from"./constants-7c047c0d.js";import{BranchCreatedEvent as p,BranchUpdatedEvent as g,BranchRemovedEvent as m,BranchActivatedEvent as b,BranchRefreshedEvent as v,BranchIdContext as y}from"./branch-aebd078a.js";import{E as f}from"./eventhelper-8bcec49f.js";import{L as w,i as C,a as x,b as E}from"./logicaltreehelper-67db40f1.js";import{g as P,P as T}from"./portal-b3727c25.js";import{H as S,T as R,P as H,a as N,D as I,b as z}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{D}from"./dx-ui-element-0c1e122f.js";import{L as B,C as L}from"./capture-manager-2454adc2.js";import{n as O}from"./nameof-factory-64d95f5b.js";import{C as _}from"./custom-events-helper-e7f279d3.js";import{f as k}from"./focustrap-d11cfef9.js";import{k as M}from"./key-ffa272aa.js";import{a as A,K as F,F as V,L as W}from"./keyboard-navigation-strategy-ea41c807.js";import{b as q}from"./browser-3fc721b7.js";import{d as j}from"./dom-554d0cc7.js";import{C as $}from"./css-classes-c63af734.js";import{n as K}from"./property-4ec0b52d.js";import{e as Z}from"./custom-element-267f9a21.js";import{s as X,x as Y,i as U}from"./lit-element-462e7ad3.js";import{D as G}from"./data-qa-utils-8be7c726.js";import{containsFocusHiddenAttribute as J}from"./focus-utils-ae044224.js";import{d as Q,a as ee,b as te}from"./constants-a4904a3f.js";const ie=new class{constructor(){this._x=0,this._y=0,this.handlePointerDown=this.pointerEventHandler.bind(this)}get x(){return this._x}get y(){return this._y}subscribe(){window.addEventListener("pointerdown",this.handlePointerDown,{capture:!0,passive:!0}),window.addEventListener("pointermove",this.handlePointerDown,{capture:!0,passive:!0}),window.addEventListener("pointerup",this.handlePointerDown,{capture:!0,passive:!0})}unsubscribe(){window.removeEventListener("pointerdown",this.handlePointerDown),window.removeEventListener("pointermove",this.handlePointerDown),window.removeEventListener("pointerup",this.handlePointerDown)}pointerEventHandler(e){this._x=e.pageX,this._y=e.pageY}};ie.subscribe();class se{get x(){return this.xField}get y(){return this.yField}constructor(e,t){this.xField=e,this.yField=t}}se.zero=new se(0,0);class ne{static add(e,t){return new se(e.x+t.x,e.y+t.y)}static multiply(e,t){return new se(e.x*t,e.y*(1/t))}static divide(e,t){return new se(e.x*(1/t),e.y*(1/t))}static getLength(e){return Math.sqrt(e.x*e.x+e.y*e.y)}static normalize(e){const t=Math.max(Math.abs(e.x),Math.max(e.y)),i=ne.divide(e,t);return ne.divide(i,ne.getLength(i))}}class oe{get position(){return this.callback(this.target)}constructor(e,t){this.target=e,this.callback=t}}class re{get raf(){return this.options.raf}constructor(e,t){this.observer=null,this.elementScrollHandler=this.handleElementScroll.bind(this),this.resizeHandler=this.handleResize.bind(this),this.overflows=[],this.resizing=[],this.targetElement=null,this.rafAction=new r,this.callback=e,this.options=t}observe(e){this.disconnect(),this.targetElement=e,this.subscribeEvents()}disconnect(){var e;this.unsubscribeEvents(),this.rafAction.cancel(),null===(e=this.observer)||void 0===e||e.disconnect(),this.observer=null}getTargetBoundingClientRect(){return this.targetElement?i.getRelativeElementRect(this.targetElement):t.Empty}subscribeEvents(){if(!this.targetElement)return;window.addEventListener("scroll",this.elementScrollHandler);const e=[],t=[];for(const n of i.getRootPathAndSelf(this.targetElement)){const i=n;if(!i)return;if(this.isInShadowDom(i))continue;new ResizeObserver(this.resizeHandler).observe(i,{box:"border-box"}),t.push(),s.isScrollable(i)&&(i.addEventListener("scroll",this.elementScrollHandler,{capture:!0}),e.push(i))}this.overflows=e,this.resizing=t}isInShadowDom(e){return e.getRootNode()instanceof ShadowRoot}unsubscribeEvents(){window.removeEventListener("scroll",this.elementScrollHandler),this.overflows.forEach((e=>{e.removeEventListener("scroll",this.elementScrollHandler)})),this.resizing.forEach((e=>{e.disconnect()}))}handleResize(e,t){this.raisePositionChanged()}handleElementScroll(e){this.raisePositionChanged()}raisePositionChanged(){this.raf?this.rafAction.execute((()=>this.raisePositionChangedCore())):this.raisePositionChangedCore()}raisePositionChangedCore(){this.callback(new oe(this.targetElement,this.getTargetBoundingClientRect),this)}}class ae{get hasObservedElement(){return null!==this.observedElement}constructor(e,t){this.target=null,this.targetMutationObserver=new MutationObserver((e=>this.handleTargetMutations.bind(this))),this.observedElement=null,this.selector=e,this.callback=t}observe(e=null){this.disconnect(),this.target=e;const t=null!=e?e:document;this.observedElement=t.querySelector(this.selector),this.observedElement&&this.raiseElementChanged(null,this.observedElement),this.targetMutationObserver.observe(t,{subtree:!0,childList:!0})}disconnect(){this.targetMutationObserver.disconnect()}raiseElementChanged(e,t){this.callback({oldElement:e,element:t},this)}handleTargetMutations(e,t){this.hasObservedElement?this.processRemovedNodes(e):this.processAddedNodes(e)}processRemovedNodes(e){this.processRemovedNodesInternal(e)&&(this.raiseElementChanged(this.observedElement,null),this.observedElement=null)}processRemovedNodesInternal(e){return e.forEach((e=>{e.removedNodes.forEach((e=>{if(this.observedElement===e)return!0}))})),!1}processAddedNodes(e){const[t,i]=this.processAddedNodesInternal(e);t&&(this.raiseElementChanged(this.observedElement,i),this.observedElement=i)}processAddedNodesInternal(e){var t;const i=(null!==(t=this.target)&&void 0!==t?t:document).querySelector(this.selector);return[null!==i,i]}}class le{constructor(){this._isResolved=!1,this._isRejected=!1,this._promise=new Promise(((e,t)=>{this._resolve=e,this._reject=t}))}get promise(){return this._promise}resolve(e){this._isResolved=!0,this._resolve(e)}reject(e){this._isRejected=!0,this._reject(e)}tryResolve(e){this._isResolved||this._isRejected||this.resolve(e)}tryReject(e){this._isResolved||this._isRejected||this.reject(e)}}var he;!function(e){e.Modal="modal",e.DropDown="dropdown",e.Flyout="flyout",e.Window="window",e.Root="root"}(he||(he={}));class de{get type(){return this._type}get children(){return Array.from(this._children)}get hasChildren(){return this._children.size>0}get id(){return this._id}constructor(e,t,i){this._children=new Set,this._id=e,this._parentId=t,this._type=i}add(e){this._children.add(e)}remove(e){this._children.delete(e)}}class ce{constructor(){this.idToNode=new Map,this.idToParentId=new Map,this.idToNode.set(ce.root,new de(ce.root,ce.root,he.Root))}add(e,t,i){const s=new de(e,t=null!=t?t:ce.root,i);this.idToParentId.set(e,t),this.idToNode.set(e,s);this.idToNode.get(t).add(s)}remove(e){if(!this.idToNode.has(e))return;const t=this.idToParentId.get(e),i=this.idToNode.get(t),s=this.idToNode.get(e);this.idToParentId.delete(e),this.idToNode.delete(e),i.remove(s)}getParentId(e){return this.idToParentId.get(e)}getBranchNode(e){var t;return null!==(t=this.idToNode.get(e))&&void 0!==t?t:null}findRoot(e){let t=e;for(;;){const e=this.idToParentId.get(t);if(e===ce.root)return t;t=e}}}ce.baseIndex=1050,ce.root="";class ue{get type(){return this.branchNode.type}get index(){return this._index}set index(e){this._index=e}get children(){return this._children}get hasChildren(){return this._children.length>0}get id(){return this.branchNode.id}constructor(e){this._children=new Array,this._index=0,this.branchNode=e}add(e){this._children.push(e)}remove(e){this._children=this._children.filter((t=>t!==e))}peek(){return this._children[this._children.length-1]}activate(e){return this.peek()!==e&&(this.remove(e),this.add(e),!0)}}const pe=new ce,ge=new class{constructor(){this.idToNode=new Map,this.rootNode=new ue(pe.getBranchNode("")),this.idToNode.set("",this.rootNode)}getIndex(e){const t=this.idToNode.get(e);return t?t.index:0}register(e){const t=pe.findRoot(e),i=pe.getBranchNode(e),s=new ue(i);if(this.idToNode.set(e,s),e===t)this.rootNode.add(s);else{const t=pe.getParentId(e);this.idToNode.get(t).add(s)}this.refresh()}unregister(e){var t;const i=null!==(t=this.idToNode.get(e))&&void 0!==t?t:null;if(!i)return;const s=pe.getParentId(i.id);this.idToNode.get(s).remove(i);for(const t of this.iterateBranch(e))this.idToNode.delete(t);this.idToNode.delete(e),this.refresh()}activate(e){var t,i,s;let n=null!==(t=this.idToNode.get(e))&&void 0!==t?t:null;if(!n)return!1;let o=!1;for(;n;){const e=pe.getParentId(n.id),t=null!==(i=this.idToNode.get(e))&&void 0!==i?i:null;o=o||null!==(s=null==t?void 0:t.activate(n))&&void 0!==s&&s,n=t}return o}iterateBranch(e){let t=new Array;const i=this.idToNode.get(e);if(!(null==i?void 0:i.hasChildren))return t;for(const e of i.children)t.push(e.id),t=t.concat(this.iterateBranch(e.id));return t.sort(((e,t)=>this.idToNode.get(t).index-this.idToNode.get(e).index))}refresh(){let e=!1,t=ce.baseIndex;for(const i in this.rootNode.children){const s=this.rootNode.children[i];e?t+=this.incrementByType(s.type):e=!0,t=this.calculateZIndices(s,t)}}calculateZIndices(e,t){if(e.index=t,e.hasChildren)for(const i of e.children)t+=this.incrementByType(i.type),t=this.calculateZIndices(i,t);return t}incrementByType(e){switch(e){case he.Flyout:case he.DropDown:return 10;case he.Modal:return 100}return 10}setTopMostBranch(e){let t=this.idToNode.get(e)||null;for(;t&&t.type!==he.Modal;){const e=pe.getParentId(t.id);if(e){const i=this.idToNode.get(e);i.activate(t),t=i}else this.rootNode.activate(t),t=null}this.refresh()}};var me;!function(e){e.OutsideClick="OutsideClick",e.EscapePress="EscapePress",e.Programmatically="Programmatically"}(me||(me={}));class be{static async closeHierarchyAsync(e,t,i,s){var n;let o=!0;if(i.requestClose||e.closeOnOutsideClick)for(const t of ge.iterateBranch(e.branchId)){const e=await P().getPopup(t);if(o=null===(n=await(null==e?void 0:e.closeAsync(i,s)))||void 0===n||n,!o)break}o&&e.closeOnOutsideClick&&e.children.length>0&&await e.closeAsync(i,s)}static async closeRootAsync(e,t,i,s){const n=w.findParent(e,(e=>C(e)));if(n){if(i.requestClose=i.requestClose||e.closeOnOutsideClick,await n.processCapturedPointerAsync(t,i),i.handled)return}await be.closeHierarchyAsync(e,t,i,s)}static async tryCloseCapturedElement(e,t,i){if(i.nextInteractionHandled){if(!e.closeOnOutsideClick)return;await be.closeHierarchyAsync(e,t,i,me.OutsideClick)}else await be.closeRootAsync(e,t,i,me.OutsideClick)}}class ve{constructor(){this.handlers=[],this.raise=e=>{this.handlers.slice(0).forEach((t=>t(e)))}}subscribe(e){this.handlers.push(e)}unsubscribe(e){this.handlers=this.handlers.filter((t=>t!==e))}}class ye{constructor(){this.handlers=[]}subscribe(e){this.handlers.push(e)}unsubscribe(e){this.handlers=this.handlers.filter((t=>t!==e))}raise(e,t){this.handlers.slice(0).forEach((i=>i(e,t)))}}class fe extends D{constructor(){super(...arguments),this.branchId="",this.parentBranchId=null,this._isCaptured=!1,this._locker=null,this.capturingAction=()=>{},this.releasingCaptureAction=()=>{}}get locker(){return this._locker||(this._locker=new B)}get isCaptured(){return this._isCaptured}gotCapture(){this._isCaptured||(this._isCaptured=!0,this.capturingAction())}lostCapture(){this._isCaptured&&(this._isCaptured=!1,this.releasingCaptureAction())}get isCloseOnPointerUp(){return!1}}const we="dxbl-popup-cell";let Ce=class extends X{constructor(){super(...arguments),this._portal=null,this.elements=null,this.elementsChangedHandler=this.handleElementsChanged.bind(this),this.zIndexDeltaChangeHandler=this.handleZIndexDeltaChange.bind(this),this.baseZIndex=0,this.zIndexDelta=0}get zIndex(){return this.baseZIndex+this.zIndexDelta}createRenderRoot(){return this}get portal(){return this._portal}setPortal(e){this.unsubscribe(),this._portal=e,this.subscribe(),this.updateElements(this.elements,e.portable)}connectedCallback(){super.connectedCallback(),this.subscribe()}subscribe(){this.addEventListener(Di.eventName,this.zIndexDeltaChangeHandler),this.portal&&this.portal.addEventListener(T.eventName,this.elementsChangedHandler)}unsubscribe(){this.removeEventListener(Di.eventName,this.zIndexDeltaChangeHandler),this.portal&&this.portal.removeEventListener(T.eventName,this.elementsChangedHandler)}disconnectedCallback(){super.disconnectedCallback(),this.unsubscribe()}handleElementsChanged(e){this.updateElements(this.elements,e.detail.elements)}updateElements(e,t){e&&e.forEach((e=>this.removeChild(e))),this.elements=t,t&&this.append(...t)}handleZIndexDeltaChange(e){this.zIndexDelta=e.detail.zIndex}updated(e){(e.has("zIndex")||e.has("zIndexDelta")||e.has("baseZIndex"))&&(this.style.zIndex=String(this.zIndex))}};e([K({type:Number})],Ce.prototype,"baseZIndex",void 0),e([K({type:Number})],Ce.prototype,"zIndexDelta",void 0),e([K({type:Number,reflect:!0})],Ce.prototype,"zIndex",null),Ce=e([Z(we)],Ce);const xe=document.createElement("template");xe.innerHTML="<"+we+' class="'+we+'" />';class Ee{static getPortal(e){return Pe.getPortal(e)}static getPortalFrom(e){var t;const i=null===(t=Ai.fromElement(e))||void 0===t?void 0:t.getPortal();return i||null}}const Pe=new class{constructor(){this.portals=new Map}addPortal(e,t){this.portals.set(e,t)}removePortal(e){this.portals.delete(e)}getPortal(e){return this.portals.get(e)}};let Te=class extends X{constructor(){super(...arguments),this.activateLocker=new B,this.portalsMapping=new Map,this.portals=new Map,this.rafAction=new r,this.focusInHandler=this.handleFocusIn.bind(this),this.adjustZIndexRequestHandler=this.adjustZIndexRequest.bind(this),this.branchCreatedHandler=this.handleBranchCreated.bind(this),this.branchUpdatedHandler=this.handleBranchUpdated.bind(this),this.branchRemovedHandler=this.handleBranchRemoved.bind(this),this.branchActivatedHandler=this.handleBranchActivated.bind(this),this.branchRefreshedHandler=this.handleBranchRefreshed.bind(this),this.captureManager=new L}createRenderRoot(){return this}assign(e){if(this.portals.has(e)||!e.branchId)return;const t=this.selectTemplate(e);this.appendChild(t);const i=this.children[this.children.length-1];i.setPortal(e),this.portalsMapping.set(e.branchId,e),this.portals.set(e,i),Pe.addPortal(e.branchId,e)}release(e){const t=this.portals.get(e);this.portalsMapping.delete(e.branchId),Pe.removePortal(e.branchId),this.portals.delete(e),t&&this.removeChild(t)}connectedCallback(){super.connectedCallback(),this.tabIndex=0,this.addEventListener("focusin",this.focusInHandler,{capture:!0}),this.addEventListener(Li.eventName,this.adjustZIndexRequestHandler),this.addEventListener(p.eventName,this.branchCreatedHandler),this.addEventListener(g.eventName,this.branchUpdatedHandler),this.addEventListener(m.eventName,this.branchRemovedHandler),this.addEventListener(b.eventName,this.branchActivatedHandler),this.addEventListener(v.eventName,this.branchRefreshedHandler)}disconnectedCallback(){super.disconnectedCallback(),this.removeEventListener("focusin",this.focusInHandler),this.removeEventListener(Li.eventName,this.adjustZIndexRequestHandler),this.removeEventListener(p.eventName,this.branchCreatedHandler),this.removeEventListener(g.eventName,this.branchUpdatedHandler),this.removeEventListener(m.eventName,this.branchRemovedHandler),this.removeEventListener(b.eventName,this.branchActivatedHandler),this.removeEventListener(v.eventName,this.branchRefreshedHandler)}handleFocusIn(e){this.rafAction.cancel();e.target===this&&(f.markHandled(e),this.rafAction.execute((()=>this.blur())))}handleBranchCreated(e){pe.add(e.detail.id,e.detail.parentId,e.detail.type),this.assignZIndices()}handleBranchUpdated(e){pe.remove(e.detail.id),pe.add(e.detail.id,e.detail.parentId,e.detail.type),this.assignZIndices()}handleBranchRemoved(e){pe.remove(e.detail.id),this.assignZIndices()}handleBranchActivated(e){const t=e.detail.id;if(!ge.activate(t)){const e=this.portalsMapping.get(t);if(!e)return;const i=e.popupBase;return i&&this.captureManager.capture(i),void this.assignZIndices()}const i=[];for(const e of ge.iterateBranch(t)){const s=this.portalsMapping.get(e);if(i.push(s.popup),e===t)break}if(i.length>0)i.forEach((e=>this.captureManager.releaseCapture(e))),i.forEach((e=>this.captureManager.capture(e)));else{const e=this.portalsMapping.get(t);this.captureManager.capture(e.popupBase)}this.assignZIndices()}handleBranchRefreshed(e){this.assignZIndices()}assignZIndices(){ge.refresh();for(const e of this.portals.keys()){const t=e.branchId;this.portals.get(e).baseZIndex=ge.getIndex(t)}}adjustZIndexRequest(e){const t=e.detail.branchId;this.isBranchExistsInDom(t)&&(e.detail.isFocused&&ge.setTopMostBranch(t),this.assignZIndices())}isBranchExistsInDom(e){const t=this.portalsMapping.get(e);return!!t&&!!this.portals.get(t)}getPopup(e){const t=this.portalsMapping.get(e);return(null==t?void 0:t.popupBase)||null}selectTemplate(e){return xe.content.cloneNode(!0)}subscribe(e){return this.captureManager.capture(e),()=>{this.captureManager.releaseCapture(e)}}};Te=e([Z(Q)],Te);const Se=Object.freeze({__proto__:null,cellItemTemplate:xe,PortalAccessor:Ee,get DxPopupRoot(){return Te}});class Re{constructor(e){this.action=e}measure(){this.action(this)}}class He{constructor(e){this.action=e}mutate(){this.action(this)}}const Ne=new Set,Ie=new Set;function ze(e,t,i){Be(e,t,t.add,!0,i)}function De(e,t,i){Be(e,t,t.delete,!1,i)}function Be(e,t,i,s,n){if(t.has(e)===s)throw new Error(`Element already ${s?"attached to":"detached from"} to the ${n} pipeline`);i.call(t,e),Oe()?-1===Le&&(Le=requestAnimationFrame(_e)):-1!==Le&&(cancelAnimationFrame(Le),Le=-1)}let Le=-1;function Oe(){return 0!==Ie.size||0!==Ne.size}function _e(e){Ie.forEach((e=>e.measure())),Ne.forEach((e=>e.mutate())),Le=Oe()?requestAnimationFrame(_e):-1}function ke(e){ze(e,Ne,"mutation")}function Me(e){De(e,Ne,"mutation")}function Ae(e,t){return new Promise(((i,s)=>{!function(e){ze(e,Ie,"measure")}(new Re((s=>{!function(e){De(e,Ie,"measure")}(s),i(t(e))})))}))}function Fe(e,t){return new Promise(((i,s)=>{ke(new He((s=>{Me(s),i(t(e))})))}))}const Ve=new class{constructor(){this._shadowBranchEvent=new ve,this._topShadowId=null,this.shadowSet=new Set}get shadowBranchChanged(){return this._shadowBranchEvent}register(e){this.shadowSet.add(e),this.raiseTopShadowStackChanged()}unregister(e){this.shadowSet.delete(e),this.raiseTopShadowStackChanged()}raiseTopShadowStackChanged(){const e=this.getTopShadowModal();if(this._topShadowId===e)return;this._topShadowId=e;this._shadowBranchEvent.raise({branch:e})}getTopShadowModal(){let e=-1,t="";for(const i of Array.from(this.shadowSet)){const s=ge.getIndex(i);s>e&&(e=s,t=i)}return t}},We=$.Prefix+"-prevent-safari-scrolling";const qe=new class{constructor(){this.paddingCoerced=!1,this.bodyScrollTop=0,this.bodyScrollPadding="",this.scrollLocked=!1,this.scrollLockHostSet=new Set}register(e){this.scrollLockHostSet.add(e),this.updateScrollLock()}unregister(e){this.scrollLockHostSet.delete(e),this.updateScrollLock()}updateScrollLock(){this.toggleBodyScroll(document.body,this.scrollLockHostSet.size>0)}toggleBodyScroll(e,t){t!==this.scrollLocked&&(this.scrollLocked=t,this.toggleScrollPadding(e,t),j.DomUtils.toggleClassName(e,"dxbl-modal-open",t),q.Browser.Safari&&this.toggleSafariScroll(e,t))}toggleScrollPadding(e,t){if(t){const t=s.getVerticalScrollbarWidth();if(0===t)return;this.paddingCoerced=!0;const i=parseInt(getComputedStyle(document.body).getPropertyValue("padding-right"),10);return this.bodyScrollPadding=document.body.style.paddingRight,void(e.style.paddingRight=`${i+t}px`)}this.paddingCoerced&&(e.style.paddingRight=this.bodyScrollPadding,this.paddingCoerced=!1)}toggleSafariScroll(e,t){t?(this.bodyScrollTop=window.pageYOffset,j.DomUtils.toggleClassName(e,We,t)):(j.DomUtils.toggleClassName(e,We,t),window.scrollTo(0,this.bodyScrollTop))}};class je{}je.Window=$.Prefix+"-window",je.WindowHeader=je.Window+"-header",je.WindowHeaderButton=je.WindowHeader+"-button";class $e{}$e.Popup=$.Prefix+"-popup",$e.PopupHeader=$e.Popup+"-header",$e.PopupHeaderButton=$e.PopupHeader+"-button",$e.PopupResized=$e.Popup+"-resized";const Ke=`.${je.WindowHeaderButton}, .${$e.PopupHeaderButton}`;class Ze{constructor(e,t){this.x=t.x,this.y=t.y}}class Xe extends CustomEvent{constructor(e,t){super(Xe.eventName,{detail:new Ze(e,t),bubbles:!0,composed:!0,cancelable:!1})}}Xe.eventName="dxbl:popup-dragcompleted",_.register(Xe.eventName,(e=>e.detail));class Ye{constructor(e){this.x=e.x,this.y=e.y}}class Ue extends CustomEvent{constructor(e){super(Ue.eventName,{detail:new Ye(e),bubbles:!0,composed:!0,cancelable:!1})}}Ue.eventName="dxbl:popup-dragstarted",_.register(Ue.eventName,(e=>e.detail));class Ge{constructor(e){this.x=e.x,this.y=e.y}}class Je extends CustomEvent{constructor(e){super(Je.eventName,{detail:new Ge(e),bubbles:!0,composed:!0,cancelable:!0})}}Je.eventName="dxbl-popup.drag";const Qe="dxbl-popup-dragging";let et=class extends X{constructor(){super(),this.allowDrag=!1,this.showCloseButton=!1}updated(e){(e.has("showCloseButton")||this.showCloseButton)&&this.preventCloseButtonDragging()}get handlePointerEventsMode(){return S.Dragging}get handlePointerEventsTarget(){return this}get handlePointerEventsDelay(){return R}get hoverTitleElementsSelector(){return null}get bypassNonInlineHoverTitleElementChildSelector(){return null}shouldProcessPointerEvent(e){return!0}render(){return Y`<slot></slot>`}preventCloseButtonDragging(){const e=this.querySelector(Ke);e&&e.addEventListener("pointerdown",(e=>{e.stopPropagation()}))}};e([K({type:Boolean,attribute:"allow-drag",reflect:!1})],et.prototype,"allowDrag",void 0),e([K({type:Boolean,attribute:"show-close-button",reflect:!1})],et.prototype,"showCloseButton",void 0),et=e([Z("dxbl-popup-header")],et);const tt=new ResizeObserver((e=>{for(const t of e){t.target.actualSize=new n(Math.round(t.contentRect.width),Math.round(t.contentRect.height))}}));class it{constructor(e,t,i,s){this.min=new a(e,t),this.max=new a(i,s)}}function st(e,t,i){const s=i.min,n=i.max;return new a(nt(s.x,n.x,t.clientX+window.scrollX-e.x),nt(s.y,n.y,t.clientY+window.scrollY-e.y))}function nt(e,t,i){return Math.floor(Math.min(t,Math.max(e,i)))}class ot{constructor(e,t,i,s,n=null){this.mainElement=e,this.onStart=t,this.onMove=i,this.onEnd=s,this.checkPermission=n,this.handlersAdded=!1,this.dragHandler=this.handleDragStart.bind(this)}addHandlers(){this.handlersAdded||(this.mainElement.addEventListener(H.eventName,this.dragHandler),this.handlersAdded=!0)}removeHandlers(){this.mainElement.removeEventListener(H.eventName,this.dragHandler),this.handlersAdded=!1}handleDragStart(e){if(null!==this.checkPermission&&!this.checkPermission())return;const t=e,i=function(e,t){const i=e.getBoundingClientRect();return new a(Math.floor(t.clientX-i.x),Math.floor(t.clientY-i.y))}(this.mainElement,t.detail.source),s=(n=i,new it(window.scrollX-n.x,window.scrollY-n.y,window.scrollX+window.innerWidth-n.x,window.scrollY+window.innerHeight-n.y));var n;const o=st(i,t.detail.source,s);let r=o,h=null;const d=e=>{r=st(i,e,s),null!==h&&l.areClose(h,r)||(h=r,this.onMove(h))};document.addEventListener("pointermove",d,{passive:!0}),this.mainElement.addEventListener(N.eventName,(e=>{document.removeEventListener("pointermove",d);const t=st(i,e.detail.source,s);this.onEnd(t,o)}),{once:!0}),this.onStart(o)}}const rt="0.25rem",at="--dxbl-popup-resize-container-width",lt="--dxbl-popup-resize-container-height";class ht{constructor(e){this.width=e.width,this.height=e.height}}class dt extends CustomEvent{constructor(e){super(dt.eventName,{detail:new ht(e),bubbles:!0,composed:!0,cancelable:!1})}}dt.eventName="dxbl:popup-resizestarted",_.register(dt.eventName,(e=>e.detail));class ct{constructor(e){this.width=e.width,this.height=e.height}}class ut extends CustomEvent{constructor(e){super(ut.eventName,{detail:new ct(e),bubbles:!0,composed:!0,cancelable:!1})}}ut.eventName="dxbl:popup-resizecompleted",_.register(ut.eventName,(e=>e.detail));class pt{constructor(e,t,i,s,n,o){this.deltaWidth=e,this.deltaHeight=t,this.deltaX=i,this.deltaY=s,this.resizing=n,this.dragging=o}}class gt extends CustomEvent{constructor(e,t,i,s,n,o){super(gt.eventName,{detail:new pt(e,t,i,s,n,o),bubbles:!0,composed:!0,cancelable:!1})}}gt.eventName="dxbl:popup-layoutchange";class mt{constructor(e,t){this.width=null,this.height=null,this.x=null,this.y=null,this.resizing=e,this.dragging=t}}class bt extends CustomEvent{constructor(e,t){super(bt.eventName,{detail:new mt(e,t),bubbles:!0,composed:!0,cancelable:!1})}}bt.eventName="dxbl:popup-layoutchangestart",_.register(bt.eventName,(e=>e.detail));class vt{constructor(e,t){this.width=null,this.height=null,this.x=null,this.y=null,this.oldWidth=null,this.oldHeight=null,this.oldX=null,this.oldY=null,this.resizing=e,this.dragging=t}}class yt extends CustomEvent{constructor(e,t){super(yt.eventName,{detail:new vt(e,t),bubbles:!0,composed:!0,cancelable:!1})}}yt.eventName="dxbl:popup-layoutchangeend",_.register(yt.eventName,(e=>e.detail));class ft extends CustomEvent{constructor(e){super(ft.eventName,{detail:e,bubbles:!0,composed:!0})}}ft.eventName="dxbl-popup-resizer.onMoving";class wt extends X{get lastX(){if(null===this.currentPosition)throw new Error("invalid state");return this.currentPosition.x}get lastY(){if(null===this.currentPosition)throw new Error("invalid state");return this.currentPosition.y}get topValue(){throw new Error("should be implemented")}get leftValue(){throw new Error("should be implemented")}get widthValue(){return rt}get heightValue(){return rt}get cursorValue(){throw new Error("should be implemented")}get isDragPossible(){return!1}constructor(){super(),this.currentPosition=null,this.pointerEventsHelper=new I(this),this.dragHandler=new ot(this,(e=>{this.dispatchEvent(new ft(!0)),this.currentPosition=e,this.shadowRoot.dispatchEvent(new bt(!0,this.isDragPossible))}),(e=>{const t=this.getDeltaWidth(e.x),i=this.getDeltaHeight(e.y),s=this.getDeltaX(e.x),n=this.getDeltaY(e.y);this.currentPosition=e,this.shadowRoot.dispatchEvent(new gt(t,i,s,n,!0,this.isDragPossible))}),((e,t)=>{this.dispatchEvent(new ft(!1)),this.currentPosition=null,this.shadowRoot.dispatchEvent(new yt(!0,this.isDragPossible))}))}connectedCallback(){super.connectedCallback(),this.pointerEventsHelper.initialize(),this.dragHandler.addHandlers()}disconnectedCallback(){super.disconnectedCallback(),this.pointerEventsHelper.dispose(),this.dragHandler.removeHandlers()}get handlePointerEventsMode(){return S.Dragging}get handlePointerEventsTarget(){return this}get handlePointerEventsDelay(){return R}get hoverTitleElementsSelector(){return null}get bypassNonInlineHoverTitleElementChildSelector(){return null}shouldProcessPointerEvent(e){return!0}getDeltaWidth(e){throw new Error("should be implemented")}getDeltaHeight(e){throw new Error("should be implemented")}getDeltaX(e){throw new Error("should be implemented")}getDeltaY(e){throw new Error("should be implemented")}render(){return Y`
    <style>
        :host {
            position: absolute;
            top: ${this.topValue};
            left: ${this.leftValue};
            width: 0px;
            height: 0px;
            z-index: 1;
            pointer-events: auto;

        }
        .dxbl-resize-handle {
            width: ${this.widthValue};
            height: ${this.heightValue};
            opacity: 0;
            cursor: ${this.cursorValue};
            z-index: 1;
        }
    </style>
    <div class="dxbl-resize-handle"></div>
`}}let Ct=class extends wt{get isDragPossible(){return!0}get topValue(){return"0%"}get leftValue(){return rt}get widthValue(){return`calc(var(${at}) - ${rt})`}get cursorValue(){return"ns-resize"}getDeltaWidth(e){return 0}getDeltaX(e){return 0}getDeltaHeight(e){return this.lastY-e}getDeltaY(e){return e-this.lastY}};Ct=e([Z("dxbl-resize-top-edge")],Ct);let xt=class extends wt{get topValue(){return rt}get leftValue(){return"100%"}get heightValue(){return`calc(var(${lt}) - ${rt})`}get cursorValue(){return"ew-resize"}getDeltaWidth(e){return e-this.lastX}getDeltaX(e){return 0}getDeltaHeight(e){return 0}getDeltaY(e){return 0}};xt=e([Z("dxbl-resize-right-edge")],xt);let Et=class extends wt{get topValue(){return"100%"}get leftValue(){return rt}get widthValue(){return`calc(var(${at}) - ${rt})`}get cursorValue(){return"ns-resize"}getDeltaWidth(e){return 0}getDeltaX(e){return 0}getDeltaHeight(e){return e-this.lastY}getDeltaY(e){return 0}};Et=e([Z("dxbl-resize-bottom-edge")],Et);let Pt=class extends wt{get isDragPossible(){return!0}get topValue(){return rt}get leftValue(){return"0%"}get heightValue(){return`calc(var(${lt}) - ${rt})`}get cursorValue(){return"ew-resize"}getDeltaWidth(e){return this.lastX-e}getDeltaX(e){return e-this.lastX}getDeltaHeight(e){return 0}getDeltaY(e){return 0}};Pt=e([Z("dxbl-resize-left-edge")],Pt);let Tt=class extends wt{get isDragPossible(){return!0}get topValue(){return"0%"}get leftValue(){return"0%"}get cursorValue(){return"se-resize"}getDeltaWidth(e){return this.lastX-e}getDeltaX(e){return e-this.lastX}getDeltaHeight(e){return this.lastY-e}getDeltaY(e){return e-this.lastY}};Tt=e([Z("dxbl-resize-tl-grip")],Tt);let St=class extends wt{get isDragPossible(){return!0}get topValue(){return"0%"}get leftValue(){return"100%"}get cursorValue(){return"ne-resize"}getDeltaWidth(e){return e-this.lastX}getDeltaX(e){return 0}getDeltaHeight(e){return this.lastY-e}getDeltaY(e){return e-this.lastY}};St=e([Z("dxbl-resize-tr-grip")],St);let Rt=class extends wt{get topValue(){return"100%"}get leftValue(){return"100%"}get cursorValue(){return"se-resize"}getDeltaWidth(e){return e-this.lastX}getDeltaX(e){return 0}getDeltaHeight(e){return e-this.lastY}getDeltaY(e){return 0}};Rt=e([Z("dxbl-resize-br-grip")],Rt);let Ht=class extends wt{get isDragPossible(){return!0}get topValue(){return"100%"}get leftValue(){return"0%"}get cursorValue(){return"sw-resize"}getDeltaWidth(e){return this.lastX-e}getDeltaX(e){return e-this.lastX}getDeltaHeight(e){return e-this.lastY}getDeltaY(e){return 0}};Ht=e([Z("dxbl-resize-bl-grip")],Ht);const Nt="dxbl-popup-resizable-container";let It=class extends X{constructor(){super(...arguments),this.actualSize=null,this.appliedSize=null,this.widthAttrName=at,this.heightAttrName=lt,this.resizerMovingHandler=this.resizerMoving.bind(this)}connectedCallback(){super.connectedCallback(),ke(this),tt.observe(this),this.addEventListener(ft.eventName,this.resizerMovingHandler)}disconnectedCallback(){super.disconnectedCallback(),Me(this),tt.unobserve(this),this.removeEventListener(ft.eventName,this.resizerMovingHandler)}resizerMoving(e){this.style.pointerEvents=e.detail?"none":""}mutate(){null!==this.actualSize&&(null!==this.appliedSize&&this.appliedSize.width===this.actualSize.width||this.style.setProperty(at,`calc(${this.actualSize.width}px - ${rt})`),null!==this.appliedSize&&this.appliedSize.height===this.actualSize.height||this.style.setProperty(lt,`calc(${this.actualSize.height}px - ${rt})`),this.appliedSize=this.actualSize)}render(){return Y`
<style>
    :host {
        display: flex;
        flex: 1 1 auto;
        min-width: 0px;
        min-height: 0px;

        ${this.widthAttrName}: 0px;
        ${this.heightAttrName}: 0px;
    }
    .dxbl-popup-resizable-container {
        position: absolute;
        width: var(${this.widthAttrName});
        height: var(${this.heightAttrName});
        pointer-events: none;
    }
    .dxbl-popup-resizable-content {
        display: flex;
        flex-flow: column nowrap;
        flex: 1 1 auto;
        overflow: hidden;
        border-radius: var(--dxbl-popup-border-radius);
    }
</style>
<div class="dxbl-popup-resizable-container">
    <dxbl-resize-top-edge></dxbl-resize-top-edge>
    <dxbl-resize-right-edge></dxbl-resize-right-edge>
    <dxbl-resize-bottom-edge></dxbl-resize-bottom-edge>
    <dxbl-resize-left-edge></dxbl-resize-left-edge>
    <dxbl-resize-tl-grip></dxbl-resize-tl-grip>
    <dxbl-resize-tr-grip></dxbl-resize-tr-grip>
    <dxbl-resize-br-grip></dxbl-resize-br-grip>
    <dxbl-resize-bl-grip></dxbl-resize-bl-grip>
</div>
<div class="dxbl-popup-resizable-content">
    <slot></slot>
</div>
`}};It=e([Z(Nt)],It);const zt="dxbl-modal",Dt=O();class Bt{constructor(e){this.closeReason=e}}class Lt extends CustomEvent{constructor(e){super(Lt.eventName,{detail:new Bt(e),bubbles:!0,composed:!0,cancelable:!0})}}Lt.eventName=zt+".closingRequested",_.register(Lt.eventName,(e=>e.detail));class Ot extends CustomEvent{constructor(e){super(Ot.eventName,{detail:new Bt(e),bubbles:!0,composed:!0,cancelable:!0})}}Ot.eventName=zt+".closingResultRequested",_.register(Ot.eventName,(e=>e.detail));let _t=class extends fe{constructor(){super(...arguments),this._positionChangedEvent=new ye,this.shadowSlotChangedHandler=this.handleShadowSlotChange.bind(this),this.topShadowBranchChangedHandler=this.handleTopShadowBranchChanged.bind(this),this.dragStartHandler=this.handleDragStart.bind(this),this.dragEndHandler=this.handleDragEnd.bind(this),this.resizeStartHandler=this.handleResizeStart.bind(this),this.resizeEndHandler=this.handleResizeEnd.bind(this),this.shadow=null,this.closingResultRequestedTcs=new le,this.shownCompletedTcs=new le,this.popupDialog=null,this._zIndex=0,this.modal=null,this.shadingChangedEvent=new ve,this._capturedElementReleaseCallback=null,this._isOpen=!1,this.shading=!1,this.closeOnOutsideClick=!1,this.closeOnEscape=!1,this.closeOnPointerUp=!1,this.autoZIndex=!1,this.zIndex=0,this.shadowVisible=!1,this.topShadowBranchId=null,this.hasServerSideClosing=!1,this.disableFocusTrap=!1,this.width="",this.height="",this.isDragging=!1,this.isResizing=!1,this.capturingAction=this.capturing,this.releasingCaptureAction=this.releaseFocusTrap}get positionChanged(){return this._positionChangedEvent}get renderedVisible(){return!0}get isOpen(){return this._isOpen}get branchType(){return he.Modal}get preventInteractions(){return!0}get isCloseOnPointerUp(){return this.closeOnPointerUp}render(){return Y`
            <dxbl-branch
                id="branch"
                branch-id="${this.branchId}"
                parent-branch-id="${this.parentBranchId}"
                type="${this.branchType}">
                ${this.renderShadow()}
                ${this.renderDefaultSlot()}
            </dxbl-branch>
        `}renderDefaultSlot(){return Y`<slot></slot>`}renderShadow(){return Y`<slot name="shadow" @slotchange="${this.shadowSlotChangedHandler}"></slot>`}handleShadowSlotChange(e){const t=e.target.assignedNodes();this.shadow=t.find((e=>e instanceof HTMLElement))}get shadingChanged(){return this.shadingChangedEvent}connectedCallback(){super.connectedCallback(),this.subscribeToEvents(),this.raiseZIndexChange()}disconnectedCallback(){super.disconnectedCallback(),this.unsubscribeFromEvents(),this.close()}show(){return this.isOpen||(this._isOpen=!0,this.initializeCapture(),this.initializeBodyScrollLock(),this.addToVisibleBranchTree(),this.raiseAdjustZIndexRequest(!0),this.raiseShown()),this.shownCompletedTcs.promise}raiseAdjustZIndexRequest(e){this.dispatchEvent(new Li(new Bi(this.branchId,e)))}close(){this.isOpen&&(this._isOpen=!1,this.releaseCapture(),this.releaseBodyScrollLock(),this.removeFromVisibleBranchTree(),this.raiseAdjustZIndexRequest(!1),this.raiseClosed())}raiseShown(){const e=new Hi;this.dispatchEvent(new Ni(e))}raiseClosed(){const e=new Hi;this.dispatchEvent(new Ii(e))}willUpdate(e){this.shadowVisible=this.shading&&this.isOpen&&!!this.branchId&&this.branchId===this.topShadowBranchId}updated(e){var t;this.shadow&&e.has(Dt("shadowVisible"))&&(this.shadow.style.display=this.shadowVisible?u.block:u.none),e.has(Dt("zIndex"))&&(this.style.zIndex=this.autoZIndex?"":String(this.zIndex),this.raiseZIndexChange()),(e.has("width")||e.has("height"))&&(null===(t=this.popupDialog)||void 0===t||t.setSize(this.width,this.height))}ensureBranchId(){if(!this.branchId)throw new Error("branchId should not be null")}addToVisibleBranchTree(){this.ensureBranchId(),ge.register(this.branchId),Ve.register(this.branchId),this.raiseBranchRefreshed()}removeFromVisibleBranchTree(){this.ensureBranchId(),ge.unregister(this.branchId),Ve.unregister(this.branchId),this.raiseBranchRefreshed()}initializeBodyScrollLock(){qe.register(this.branchId)}releaseBodyScrollLock(){qe.unregister(this.branchId)}subscribeToEvents(){Ve.shadowBranchChanged.subscribe(this.topShadowBranchChangedHandler),this.addEventListener(Ue.eventName,this.dragStartHandler),this.addEventListener(Xe.eventName,this.dragEndHandler),this.addEventListener(bt.eventName,this.resizeStartHandler),this.addEventListener(yt.eventName,this.resizeEndHandler)}unsubscribeFromEvents(){Ve.shadowBranchChanged.unsubscribe(this.topShadowBranchChangedHandler),this.removeEventListener(Ue.eventName,this.dragStartHandler),this.removeEventListener(Xe.eventName,this.dragEndHandler),this.removeEventListener(bt.eventName,this.resizeStartHandler),this.removeEventListener(yt.eventName,this.resizeEndHandler)}handleTopShadowBranchChanged(e){this.topShadowBranchId=e.branch}handleDragStart(e){this.isDragging=!0}handleDragEnd(e){this.isDragging=!1}handleResizeStart(e){this.isResizing=!0}handleResizeEnd(e){this.isResizing=!1}initializeCapture(){const e=document.querySelector("dxbl-popup-root");e&&(this._capturedElementReleaseCallback=e.subscribe(this))}releaseCapture(){this.processClosingRequested(!0),this._capturedElementReleaseCallback&&(this._capturedElementReleaseCallback(),this._capturedElementReleaseCallback=null)}processClosingRequested(e){this.closingResultRequestedTcs.resolve(e),this.closingResultRequestedTcs=new le}notifyDialogConnected(e){this.popupDialog=e,this.initializeKeyboardNavigator(),this.initializeFocusTrap()}notifyRootConnected(){requestAnimationFrame((()=>{setTimeout((()=>{this.shownCompletedTcs.tryResolve()}))}))}notifyDialogDisconnected(){this.disposeKeyboardNavigator(),this.releaseFocusTrap(!0),this.popupDialog=null}notifyRootDisconnected(){this.shownCompletedTcs.tryReject("cancelled"),this.shownCompletedTcs=new le}capturing(){this.activate(),this.initializeFocusTrap()}initializeFocusTrap(){this.isOpen&&this.popupDialog&&!this.disableFocusTrap&&k.activate(this.popupDialog,!0,!!this.keyboardNavigator)}releaseFocusTrap(e=null){null===e&&(e=!this.isOpen),k.deactivate(this.popupDialog,e)}activate(){const e=new b(new y(this.branchId));this.dispatchEvent(e)}notifyCloseCanceled(){this.processClosingRequested(!1)}async processCapturedPointerAsync(e,t){t.handled||this.isPointedCaptured(e)||(t.nextInteractionHandled=!0,await be.tryCloseCapturedElement(this,e,t))}isPointedCaptured(e){return!(!this.closeOnPointerUp||!this.isDragging&&!this.isResizing)||z.containsInComposedPath(e,this.popupDialog)}async processCapturedKeyDownAsync(e,t){t.handled||this.closeOnEscape&&"Escape"===e.key&&await this.processEscape(e,t)}async processEscape(e,t){const i=await this.raiseClosingResultRequestedAsync(me.EscapePress);this.closingResultRequestedTcs=new le,i||(t.handled=!0)}async closeAsync(e,t){const i=await this.raiseClosingResultRequestedAsync(t);return this.closingResultRequestedTcs=new le,e.nextInteractionHandled=!0,e.handled=!0,Promise.resolve(!!i)}async closeHierarchyAsync(e,t,i){for(const e of ge.iterateBranch(this.branchId)){const s=await P().getPopup(e);await(null==s?void 0:s.closeAsync(t,i))}await this.closeAsync(t,i)}async raiseClosingResultRequestedAsync(e){if(!this.hasServerSideClosing)return this.raiseClosingRequested(e),!0;const t=new le;return this.closingResultRequestedTcs=t,this.raiseClosingResultRequested(e),t.promise}raiseClosingRequested(e){setTimeout((()=>{this.dispatchEvent(new Lt(e))}))}raiseClosingResultRequested(e){this.dispatchEvent(new Ot(e))}raiseBranchRefreshed(){const e=new v(new y(this.branchId));this.dispatchEvent(e)}raiseZIndexChange(){this.updateComplete.then((e=>{this.dispatchEvent(new Di(new zi(this.zIndex)))}))}initializeKeyboardNavigator(){var e,t,i;if(this.keyboardNavigator||(this.keyboardNavigator=this.querySelector(ei)),this.keyboardNavigator&&!this.keyboardNavigator.initialized){const s=new Mi({createModal:(e,t)=>new li(e,t)});null===(e=this.getPortal())||void 0===e||e.dispatchEvent(s);const n=null===(i=null===(t=s.detail.factory)||void 0===t?void 0:t.createModal)||void 0===i?void 0:i.call(t,this.keyboardNavigator,this);n&&this.keyboardNavigator.initialize(this,n)}}disposeKeyboardNavigator(){var e;null===(e=this.keyboardNavigator)||void 0===e||e.disposeComponent()}getPortal(){return Ee.getPortal(this.branchId)}};e([K({type:Boolean,attribute:"x-is-open",reflect:!0,converter:{fromAttribute:(e,t)=>{throw new Error("readonly")}}})],_t.prototype,"_isOpen",void 0),e([K({type:Boolean,attribute:"shading"})],_t.prototype,"shading",void 0),e([K({type:Boolean,attribute:"close-on-outside-click"})],_t.prototype,"closeOnOutsideClick",void 0),e([K({type:Boolean,attribute:"close-on-escape"})],_t.prototype,"closeOnEscape",void 0),e([K({type:Boolean,attribute:"close-on-pointer-up"})],_t.prototype,"closeOnPointerUp",void 0),e([K({type:Boolean,attribute:"auto-z-index"})],_t.prototype,"autoZIndex",void 0),e([K({type:Number,attribute:"z-index"})],_t.prototype,"zIndex",void 0),e([K({type:String,attribute:"branch-id"})],_t.prototype,"branchId",void 0),e([K({type:String,attribute:"parent-branch-id"})],_t.prototype,"parentBranchId",void 0),e([K({reflect:!1})],_t.prototype,"shadowVisible",void 0),e([K({reflect:!1})],_t.prototype,"topShadowBranchId",void 0),e([K({type:Boolean,attribute:"has-serverside-closing"})],_t.prototype,"hasServerSideClosing",void 0),e([K({type:Boolean,attribute:"disable-focus-trap"})],_t.prototype,"disableFocusTrap",void 0),e([K({type:String,attribute:"width"})],_t.prototype,"width",void 0),e([K({type:String,attribute:"height"})],_t.prototype,"height",void 0),_t=e([Z(zt)],_t);const kt=Object.freeze({__proto__:null,dxModalTagName:zt,ModalClosingRequestedEvent:Lt,ModalClosingResultRequestedEvent:Ot,get DxModal(){return _t},default:{DxModal:_t}});class Mt extends X{static get styles(){return U`
            :host {
                display: flex;
            }
        `}connectedCallback(){super.connectedCallback(),G.setLoaded(this)}disconnectedCallback(){super.disconnectedCallback(),G.removeLoaded(this)}render(){return Y`<slot></slot>`}}class At extends Mt{get draggableElement(){return this.allowDragByHeaderOnly?this.header:this}get canDrag(){return this.allowDrag&&(null!==this.header||!this.allowDragByHeaderOnly)}constructor(){super(),this.allowDrag=!1,this.allowDragByHeaderOnly=!1,this.canDragByBody=!1,this.showHeader=!1,this.pointerEventsHelper=new I(this),this.dragHandler=new ot(this,(e=>{this.style.cursor="move",this.style.userSelect="none",this.classList.add(Qe),this.shadowRoot.dispatchEvent(new Ue(e))}),(e=>{this.shadowRoot.dispatchEvent(new Je(e))}),((e,t)=>{this.style.cursor="",this.style.userSelect="",this.classList.remove(Qe),this.shadowRoot.dispatchEvent(new Xe(t,e))}),(()=>this.canDrag))}disconnectedCallback(){super.disconnectedCallback(),this.disposeDrag()}updated(e){(e.has("allowDrag")||e.has("allowDragByHeaderOnly")||e.has("showHeader"))&&this.updateDragging()}updateDragging(){this.allowDrag&&this.canDragByBody?(this.pointerEventsHelper.reinitialize(this.draggableElement),this.dragHandler.addHandlers()):this.disposeDrag()}disposeDrag(){this.dragHandler.removeHandlers(),this.pointerEventsHelper.dispose()}get handlePointerEventsMode(){return S.Dragging}get handlePointerEventsTarget(){return this}get handlePointerEventsDelay(){return R}get hoverTitleElementsSelector(){return null}get bypassNonInlineHoverTitleElementChildSelector(){return null}shouldProcessPointerEvent(e){return!0}}e([K({type:Boolean,attribute:"allow-drag",reflect:!0})],At.prototype,"allowDrag",void 0),e([K({type:Boolean,attribute:"allow-drag-by-header-only",reflect:!0})],At.prototype,"allowDragByHeaderOnly",void 0),e([K({type:Boolean,attribute:"can-drag-by-body"})],At.prototype,"canDragByBody",void 0),e([K({type:Boolean,attribute:"show-header"})],At.prototype,"showHeader",void 0);const Ft=document.createElement("DIV");function Vt(e,t){if(null===e)return 0;const i=Ft.cloneNode();i.style.width=e;(t||document.body).appendChild(i);const s=i.offsetWidth;return i.remove(),s}function Wt(e){e.addEventListener(Je.eventName,(t=>{t.stopImmediatePropagation();const i=t;e.positionX=i.detail.x,e.positionY=i.detail.y}),{passive:!0}),e.addEventListener(bt.eventName,(t=>{const i=t,s=e.getBoundingClientRect();e.classList.add($e.PopupResized),e.sizeDirty=!0,e.actualPosition=new a(window.scrollX+s.x,window.scrollY+s.y),e.actualSize=new n(s.width,s.height),e.initialPosition=e.actualPosition,e.initialSize=e.actualSize,e.actualMinWidth=Vt(e.minWidth,e),e.actualMinHeight=Vt(e.minHeight,e),e.actualMaxWidth=Vt(e.maxWidth,e)||Number.MAX_VALUE,e.actualMaxHeight=Vt(e.maxHeight,e)||Number.MAX_VALUE,i.detail.width=e.initialSize.width,i.detail.height=e.initialSize.height,i.detail.x=e.initialPosition.x,i.detail.y=e.initialPosition.y})),e.addEventListener(yt.eventName,(t=>{const i=t;i.detail.width=e.actualSize.width,i.detail.height=e.actualSize.height,i.detail.x=e.actualPosition.x,i.detail.y=e.actualPosition.y,i.detail.oldWidth=e.initialSize.width,i.detail.oldHeight=e.initialSize.height,i.detail.oldX=e.initialPosition.x,i.detail.oldY=e.initialPosition.y})),e.addEventListener(gt.eventName,(t=>{const i=t.detail,s=e.actualSize;e.actualSize=new n(Math.max(e.actualMinWidth,Math.min(e.actualMaxWidth,e.actualSize.width+i.deltaWidth)),Math.max(e.actualMinHeight,Math.min(e.actualMaxHeight,e.actualSize.height+i.deltaHeight)));e.actualPosition=new a(e.actualPosition.x+(s.width-e.actualSize.width!=0?i.deltaX:0),e.actualPosition.y+(s.height-e.actualSize.height!=0?i.deltaY:0)),e.positionX=e.actualPosition.x,e.positionY=e.actualPosition.y,e.width=e.actualSize.width+"px",e.height=e.actualSize.height+"px"}),{passive:!0})}Ft.style.position="absolute",Ft.style.top="-1000px",Ft.style.left="-1000px",Ft.style.visibility="hidden";const qt="dxbl-modal-dialog";let jt=class extends At{get header(){return this.querySelector(".dxbl-popup-header")}constructor(){super(),this.actualSize=null,this.actualPosition=null,this.initialSize=null,this.initialPosition=null,this.actualMinWidth=null,this.actualMaxWidth=null,this.actualMinHeight=null,this.actualMaxHeight=null,this.sizeDirty=!1,this.cssTextToApply="",this.popup=null,this.positionY=null,this.positionX=null,this.allowResize=!1,this.width=null,this.height=null,this.minWidth=null,this.minHeight=null,this.maxWidth=null,this.maxHeight=null,this.dialogCssStyle="",Wt(this)}connectedCallback(){super.connectedCallback(),ke(this),this.popup=this.closest(zt),this.popup.notifyDialogConnected(this)}disconnectedCallback(){var e;super.disconnectedCallback(),Me(this),null===(e=this.popup)||void 0===e||e.notifyDialogDisconnected(),this.popup=null}updated(e){super.updated(e),this.mapCssTextToApply()}mutate(){this.style.cssText=this.cssTextToApply}render(){return this.allowResize?Y`<dxbl-popup-resizable-container><slot></slot></dxbl-popup-resizable-container>`:Y`<slot></slot>`}setSize(e,t){this.width=e,this.height=t,this.mapCssTextToApply()}mapCssTextToApply(){const e=[],t=[];this.dialogCssStyle&&t.push(this.dialogCssStyle),null!==this.positionX&&(e.push(`translateX(${this.positionX}px)`),t.push("margin-left: 0;"),t.push("margin-right: 0;"),t.push("left: 0;")),null!==this.positionY&&(e.push(`translateY(${this.positionY}px)`),t.push("margin-top: 0;"),t.push("margin-bottom: 0;"),t.push("top: 0;")),this.width&&t.push(`width: ${this.width}`),this.height&&t.push(`height: ${this.height}`),e.length>0&&(t.push("position: absolute"),t.push(`transform: ${e.join(" ")}`)),this.cssTextToApply=t.join("; ")}};e([K({type:Number,attribute:"position-y",reflect:!1})],jt.prototype,"positionY",void 0),e([K({type:Number,attribute:"position-x",reflect:!1})],jt.prototype,"positionX",void 0),e([K({type:Boolean,attribute:"allow-resize",reflect:!1})],jt.prototype,"allowResize",void 0),e([K({type:String,attribute:"width",reflect:!1})],jt.prototype,"width",void 0),e([K({type:String,attribute:"height",reflect:!1})],jt.prototype,"height",void 0),e([K({type:String,attribute:"min-width",reflect:!1})],jt.prototype,"minWidth",void 0),e([K({type:String,attribute:"min-height",reflect:!1})],jt.prototype,"minHeight",void 0),e([K({type:String,attribute:"max-width",reflect:!1})],jt.prototype,"maxWidth",void 0),e([K({type:String,attribute:"max-height",reflect:!1})],jt.prototype,"maxHeight",void 0),e([K({type:String,attribute:"dialog-css-style",reflect:!1})],jt.prototype,"dialogCssStyle",void 0),jt=e([Z(qt)],jt);const $t="dxbl-dropdown-dialog";let Kt=class extends Mt{createRenderRoot(){return this}};var Zt;Kt=e([Z($t)],Kt);const Xt="dxbl-flyout-arrow";var Yt;!function(e){e.top="top",e.bottom="bottom",e.end="end",e.start="start"}(Yt||(Yt={}));let Ut=Zt=class extends X{constructor(){super(...arguments),this.slotChangedHandler=this.handleSlotChanged.bind(this),this.arrow=null,this._alignment=Yt.bottom,this.position=se.zero,this.serverClass=null,this.clientClass=null,this.cssClass=null}get alignment(){return this._alignment}set alignment(e){const t=this._alignment;this._alignment=e,this.clientClass=this.calcClientCssClass(e),this.requestUpdate("alignment",t)}calcClientCssClass(e){return`dxbl-popover-${e}`}get arrowSize(){return this.arrow?Zt.calcSizeWithMargins(this.arrow,i.getRelativeElementRect(this.arrow).size):n.Empty}static calcSizeWithMargins(e,t){const i=window.getComputedStyle(e),s=parseFloat(i.marginLeft),o=parseFloat(i.marginRight),r=parseFloat(i.marginTop),a=parseFloat(i.marginBottom);return new n(t.width+s+o,t.height+r+a)}static get styles(){return U`
            :host {
                display: block;
            }
        }`}willUpdate(e){if(e.has("serverClass")||e.has("clientClass")){const e=[];this.clientClass&&this.clientClass.split(" ").forEach((t=>e.push(t))),this.serverClass&&this.serverClass.split(" ").forEach((t=>e.push(t))),this.cssClass=e.join(" ")}}updated(e){if(this.arrow&&e.has("position"))switch(this.alignment){case Yt.bottom:this.arrow.style.transform=d.translateWithoutFloor(Math.floor(this.position.x),.5);break;case Yt.top:this.arrow.style.transform=d.translateWithoutFloor(Math.floor(this.position.x),-.5);break;case Yt.start:this.arrow.style.transform=d.translateWithoutFloor(-.5,Math.floor(this.position.y));break;case Yt.end:this.arrow.style.transform=d.translateWithoutFloor(.5,Math.floor(this.position.y))}e.has("cssClass")&&(this.className=this.cssClass?this.cssClass:"")}render(){return Y`
            <slot @slotchange="${this.slotChangedHandler}"></slot>
        `}handleSlotChanged(e){const t=e.target.assignedNodes();for(const e in t){const i=t[e];if(i instanceof HTMLElement){this.arrow=i;break}}}};e([K({reflect:!1})],Ut.prototype,"position",void 0),e([K({type:String,attribute:"server-class"})],Ut.prototype,"serverClass",void 0),e([K({reflect:!1})],Ut.prototype,"clientClass",void 0),e([K({reflect:!1})],Ut.prototype,"cssClass",void 0),e([K({reflect:!1})],Ut.prototype,"alignment",null),Ut=Zt=e([Z(Xt)],Ut);const Gt="dxbl-flyout-dialog";var Jt;!function(e){e.Fade="fade",e.None="none"}(Jt||(Jt={}));let Qt=class extends Mt{constructor(){super(...arguments),this.arrowSlotChangeHandler=this.handleArrowSlotChange.bind(this),this._arrow=null,this._arrowAlignment=Yt.bottom,this.serverClass=null,this.cssClass=null,this.animationType=Jt.Fade,this.animationEnabled=!1}get arrow(){return this._arrow}get arrowAlignment(){return this._arrowAlignment}set arrowAlignment(e){this._arrowAlignment=e}calcAnimationTypeCssClass(){return`dxbl-flyout-dialog-${this.animationType} dxbl-flyout-dialog-show`}willUpdate(e){if(e.has("serverClass")||e.has("animationEnabled")||e.has("animationType")){const e=[];if(this.serverClass&&this.serverClass.split(" ").forEach((t=>e.push(t))),this.animationEnabled&&this.animationType!==Jt.None){const t=e.indexOf(`dxbl-flyout-dialog-${this.animationType}`,0),i=this.calcAnimationTypeCssClass();t>-1?e.splice(t,1,i):e.push(i)}this.cssClass=e.join(" ")}}updated(e){e.has("cssClass")&&(this.className=this.cssClass?this.cssClass:"")}render(){return Y`
            <slot></slot>
            <slot name="arrow" @slotchange="${this.arrowSlotChangeHandler}"></slot>
        `}handleArrowSlotChange(e){const t=e.target.assignedNodes();this._arrow=t[0]}};e([K({type:String,attribute:"server-class"})],Qt.prototype,"serverClass",void 0),e([K({reflect:!1})],Qt.prototype,"cssClass",void 0),e([K({type:String,attribute:"animation-type"})],Qt.prototype,"animationType",void 0),e([K({type:Boolean,reflect:!1})],Qt.prototype,"animationEnabled",void 0),Qt=e([Z(Gt)],Qt);const ei="dxbl-popup-keyboard-navigator";class ti extends F{constructor(e,t,i=!1){super(e,t,i),this.portalPath=[],this.addEventSubscriptions()}get popupNavigator(){return super.navigator}get portal(){var e;return null===(e=this.popupNavigator)||void 0===e?void 0:e.getPortal()}canSwitchToNestedContentMode(){return!0}handleKeyDown(e){if(super.handleKeyDown(e))return!0;if(this.nestedContentSelected)return!1;switch(M.KeyUtils.getEventKeyCode(e)){case M.KeyCode.Tab:return this.switchToNestedContent();case M.KeyCode.Esc:return!0}return!1}onPopupShown(){this.handlePopupShown()}handlePopupShown(){this.portal&&(this.portalPath=Array.from(i.getRootPathAndSelf(this.portal))),this.activatePopupContent()}activatePopupContent(){V.makeElementFocusable(this.targetElement),this.activate()}onPopupClosed(){this.handlePopupClosed()}handlePopupClosed(){this.focusElementInRootPath(!0)}focusElementInRootPath(e=!1,t=null){const i=this.portalPath.filter((e=>e.isConnected&&e.tabIndex>=0&&e.tagName.toLowerCase()!==ee));if(i.length>0&&(this.navigator.isActive||this.targetElement.isConnected)){const e=V.findFocusableElementInRootPath(i[0]);return e&&(this.popupNavigator.syncFocusHiddenStateWithOwner(e),V.focusElement(e)),!0}return!1}onDispose(){super.onDispose(),this.removeEventSubscriptions()}}class ii extends ti{constructor(e,t,i=!1){super(e,t,i)}activatePopupContent(){super.activatePopupContent(),this.focusSelectedItem()}onPopupVisibleChanged(e){e.detail.visible&&this.onPopupShown()}addEventSubscriptions(){this.boundOnPopupVisibleChangedHandler&&this.boundOnPopupClosedHandler||(this.boundOnPopupVisibleChangedHandler=this.onPopupVisibleChanged.bind(this),this.boundOnPopupClosedHandler=this.onPopupClosed.bind(this)),this.targetElement.addEventListener(Oi.eventName,this.boundOnPopupVisibleChangedHandler),this.targetElement.addEventListener(Ii.eventName,this.boundOnPopupClosedHandler)}removeEventSubscriptions(){this.targetElement.removeEventListener(Oi.eventName,this.boundOnPopupVisibleChangedHandler),this.targetElement.removeEventListener(Ii.eventName,this.boundOnPopupClosedHandler)}}class si extends ii{constructor(e,t){super(e,t)}handleKeyDown(e){return M.KeyUtils.getEventKeyCode(e)!==M.KeyCode.Tab&&super.handleKeyDown(e)}onPopupShown(){super.onPopupShown(),this.switchToNestedContent()}onPopupClosed(){this.leaveFromNestedContent(),super.onPopupClosed()}switchToNestedContent(){return!!super.switchToNestedContent()&&(k.activate(this.selectedItemElement,!1,!0),!0)}leaveFromNestedContent(){super.leaveFromNestedContent(),k.deactivate(this.selectedItemElement,!1)}}class ni extends si{constructor(e,t){super(e,t)}queryItems(){return this.queryItemsBySelector($t)}onPopupShown(){super.onPopupShown(),this.switchToNestedContent()}}class oi extends ii{constructor(e,t,i=!1){super(e,t,i)}get windowIsOpened(){return this.targetElement.isOpen}queryItems(){return this.queryItemsBySelector(te)}getNestedContentElement(){const e=V.findFocusableElements(this.selectedItemElement);if(e.length>0){const t=e.filter((e=>!e.matches('[data-qa-selector="dx-popup-close-button"]')));return t.length>0?t[0]:e[0]}return null}addEventSubscriptions(){super.addEventSubscriptions(),this.boundOnPopupKeyboardStrategyActivateHandler||(this.boundOnPopupKeyboardStrategyActivateHandler=this.handleWindowKeyboardStrategyActivate.bind(this)),this.boundOnPopupRootBlurHandler||(this.boundOnPopupRootBlurHandler=this.handlePopupRootBlur.bind(this)),this.targetElement.addEventListener(_i.eventName,this.boundOnPopupKeyboardStrategyActivateHandler);const e=document.querySelector(Q);null==e||e.addEventListener("blur",this.boundOnPopupRootBlurHandler)}handleWindowKeyboardStrategyActivate(e){e.detail.visible&&this.activatePopupContent()}handlePopupRootBlur(){this.windowIsOpened&&(this.targetElement.toggleAttribute("inert"),this.boundOnDocumentFocusInHandler=this.handleDocumentFocusIn.bind(this),document.addEventListener("focusin",this.boundOnDocumentFocusInHandler))}handleDocumentFocusIn(e){this.targetElement.toggleAttribute("inert"),document.removeEventListener("focusin",this.boundOnDocumentFocusInHandler)}removeEventSubscriptions(){super.removeEventSubscriptions(),this.targetElement.removeEventListener(_i.eventName,this.boundOnPopupKeyboardStrategyActivateHandler);const e=document.querySelector(Q);null==e||e.removeEventListener("blur",this.boundOnPopupRootBlurHandler)}focusElementInRootPath(e=!1,t=null){if(!super.focusElementInRootPath(e,t)&&this.portal){const i=V.findPrevFocusableElement(this.portal),s=V.findNextFocusableNotChildElement(this.portal),n=e?null!=i?i:s:t===W.Backward?i:s;if(n&&!this.popupNavigator.isFocusHiddenState())return V.focusElement(n),!0}return!1}handleKeyDown(e){return M.KeyUtils.getEventKeyCode(e)!==M.KeyCode.Tab||this.nestedContentSelected?super.handleKeyDown(e):(this.leavePopupContent(e.shiftKey?W.Backward:W.Forward),!0)}activatePopupContent(){super.activatePopupContent(),this.switchToNestedContent()}onPopupShown(){this.portal&&V.makeElementFocusable(this.portal),super.onPopupShown()}onPopupClosed(){this.leaveFromNestedContent(),super.onPopupClosed(),this.portal&&V.removeTabIndex(this.portal)}leaveTransitContainer(e){super.leaveTransitContainer(e),this.leavePopupContent(e)}leavePopupContent(e){this.popupNavigator.leaveFromNavigator(),V.removeTabIndex(this.targetElement),this.focusElementInRootPath(!1,e)}}class ri extends si{constructor(e,t){super(e,t)}queryItems(){return this.queryItemsBySelector(Gt)}onPopupShown(){super.onPopupShown(),this.switchToNestedContent()}}class ai extends ii{constructor(e,t){super(e,t)}queryItems(){return this.queryItemsBySelector($t)}}class li extends ti{constructor(e,t){super(e,t)}queryItems(){return this.queryItemsBySelector(qt)}activatePopupContent(){super.activatePopupContent(),this.switchToNestedContent()}handleKeyDown(e){return M.KeyUtils.getEventKeyCode(e)!==M.KeyCode.Tab&&super.handleKeyDown(e)}onModalFocusChange(e){e.detail.isFocused?this.onPopupShown():this.onPopupClosed()}addEventSubscriptions(){this.boundOnModalFocusChangeHandler||(this.boundOnModalFocusChangeHandler=this.onModalFocusChange.bind(this)),this.targetElement.addEventListener(Li.eventName,this.boundOnModalFocusChangeHandler)}removeEventSubscriptions(){this.targetElement.removeEventListener(Li.eventName,this.boundOnModalFocusChangeHandler)}}customElements.define(ei,class extends A{get popupOwner(){return super.owner}initialize(e,t){super.initialize(e,t)}onContentChanged(){var e;const t="BODY"===(null===(e=document.activeElement)||void 0===e?void 0:e.tagName)&&"DXBL-MODAL"===this.targetElement.tagName&&this.getIsNestedContentSelected(this.targetElement);t&&(this.isActive=!0),super.onContentChanged(),t&&this.rootStrategy&&this.rootStrategy.activatePopupContent()}getPortal(){var e,t;return null!==(t=null===(e=this.popupOwner)||void 0===e?void 0:e.getPortal())&&void 0!==t?t:null}syncFocusHiddenStateWithOwner(e){this.isFocusHiddenState()&&this.passFocusHiddenAttribute(e)}isFocusHiddenState(){return J(this.targetElement)}});class hi{constructor(e){this.reinitCallback=e,this.mutationObserver=new MutationObserver((e=>this.checkViability(e,this.reinitCallback))),this.subscribed=!1}checkViability(e,t){for(const i of e)if(!i.target.isConnected){t();break}}subscribe(e){this.subscribed||(this.mutationObserver.observe(e,{attributes:!0}),this.subscribed=!0)}unsubscribe(){this.subscribed&&(this.mutationObserver.disconnect(),this.subscribed=!1)}}var di;const ci="dxbl-popup",ui=O();class pi{constructor(e,t){this.closeReason=e,this.branchId=t}}class gi extends CustomEvent{constructor(e,t){super(gi.eventName,{detail:new pi(e,t),bubbles:!0,composed:!0,cancelable:!0})}}gi.eventName=ci+".closingRequested",_.register(gi.eventName,(e=>e.detail));class mi extends CustomEvent{constructor(e,t){super(mi.eventName,{detail:new pi(e,t),bubbles:!0,composed:!0,cancelable:!0})}}var bi,vi,yi,fi,wi,Ci,xi;mi.eventName=ci+".closingResultRequested",_.register(mi.eventName,(e=>e.detail)),function(e){e.Viewport="viewport",e.Page="page",e.Rectangle="rectangle",e.TargetElement="targetelement"}(bi||(bi={})),function(e){e.None="none",e.Hide="hide",e.Close="close"}(vi||(vi={})),function(e){e[e.TopLeft=0]="TopLeft",e[e.TopRight=1]="TopRight",e[e.BottomLeft=2]="BottomLeft",e[e.BottomRight=3]="BottomRight",e[e.Center=4]="Center"}(yi||(yi={}));class Ei{constructor(e,t){this.targetInterestPoint=e,this.childInterestPoint=t}}!function(e){e.Absolute="absolute",e.Relative="relative",e.Bottom="bottom",e.Center="center",e.Right="right",e.AbsolutePoint="absolutepoint",e.RelativePoint="relativepoint",e.Mouse="mouse",e.MousePoint="mousepoint",e.Left="left",e.Top="top",e.Custom="custom"}(fi||(fi={})),function(e){e.Near="near",e.Far="far"}(wi||(wi={})),function(e){e.top="top",e.bottom="bottom"}(Ci||(Ci={})),function(e){e[e.None=0]="None",e[e.Horizontal=1]="Horizontal",e[e.Vertical=2]="Vertical"}(xi||(xi={}));class Pi{constructor(e=new a(0,0),t=new n(0,0)){this.childSize=n.Empty,this.mousePosition=null,this.visuallyHidden=!1,this.closingRequested=!1,this.lockPlacement=!1,this.lockedPosition=0,this.topLeft=e,this.size=t}}class Ti{constructor(e,t,i,s,n){this.dropOpposite=!1,this.dropDirection=wi.Near,this.dropAlignment=Ci.bottom,this.axis=t,this.point=e,this.dropOpposite=i,this.dropAlignment=n,this.dropDirection=s}}class Si{get childPoints(){return this._childPoints}get placementTargetPoints(){return this._placementTargetPoints}constructor(e,t){this.customPlacement=null,this._childPoints=e,this._placementTargetPoints=t}}class Ri extends CustomEvent{constructor(e){super(Ri.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!0})}}Ri.eventName="dxbl-popup-custom-placement";class Hi{}class Ni extends CustomEvent{constructor(e){super(Ni.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!0})}}Ni.eventName="dxbl-popup-shown";class Ii extends CustomEvent{constructor(e){super(Ii.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!0})}}Ii.eventName="dxbl-popup-closed";class zi{get zIndex(){return this._zIndex}constructor(e){this._zIndex=null!=e?e:0}}class Di extends CustomEvent{constructor(e){super(Di.eventName,{detail:e,bubbles:!0,composed:!0})}}Di.eventName="dxbl-popup-zindex-delta-change";class Bi{get isFocused(){return this._isFocused}get branchId(){return this._branchId}constructor(e,t){this._branchId=e,this._isFocused=t}}class Li extends CustomEvent{constructor(e){super(Li.eventName,{detail:e,bubbles:!0,composed:!0})}}Li.eventName="dxbl-popup-focus-change";class Oi extends CustomEvent{constructor(e){super(Oi.eventName,{detail:{visible:e},bubbles:!0,composed:!0})}}Oi.eventName="dxbl-popup-visible-change";class _i extends CustomEvent{constructor(e){super(_i.eventName,{detail:{visible:e},bubbles:!0,composed:!0})}}_i.eventName="dxbl-popup-kbd-strategy-activate";class ki extends CustomEvent{constructor(e){super(ki.eventName,{detail:e,bubbles:!0,composed:!0})}}ki.eventName="dxbl-popup-focus-loop";class Mi extends CustomEvent{constructor(e){super(Mi.eventName,{detail:{factory:e},bubbles:!0,composed:!0})}}Mi.eventName="dxbl-popup-keyboard-strategy-creating";let Ai=di=class extends fe{static fromElement(e){for(;null!==e&&!(e instanceof di);)e=e.parentElement;return e}get branchType(){return he.DropDown}get positionChanged(){return this._positionChangedEvent}get initialVisibility(){return this._initialVisibility}set initialVisibility(e){this._initialVisibility=e,this._setVisibilityStyles(this._initialVisibility),this.initializeFocusTrap(),this.initializeKeyboardNavigator(),this.dispatchEvent(new Oi(this._initialVisibility))}_setVisibilitySizing(e){e?null!==this._currentMaxHeight&&(this.style.maxWidth=this._currentMaxWidth,this.style.maxHeight=this._currentMaxHeight,this.style.overflow=this._currentOverflow):(null===this._currentMaxHeight&&(this._currentMaxHeight=this.style.maxHeight,this._currentMaxWidth=this.style.maxWidth,this._currentOverflow=this.style.overflow),this.style.overflow="hidden",this.style.maxWidth="0px",this.style.maxHeight="0px")}_setVisibilityStyles(e){e?(this.style.opacity="1",this.style.pointerEvents="auto"):(this.style.opacity="0",this.style.pointerEvents="none"),this._setVisibilitySizing(e)}enableVisibility(){clearTimeout(this._showingTimeoutId),this._showingTimeoutId=setTimeout((()=>{this.initialVisibility||(this.initialVisibility=!0)}))}static get styles(){return U`
            :host {
                display: flex;
                flex: 1 1 auto;
                flex-direction: column;
                min-height: 0;
            }
            dxbl-branch {
                display: flex;
                flex: 1 1 auto;
                flex-direction: column;
                min-height: 0;
            }
            .content-holder {
                display: flex;
                flex: 1 0 auto;
                flex-direction: column;
                min-height: 0;
            }
        `}get isCloseOnPointerUp(){return this.closeOnPointerUp}get renderedVisible(){var e,t;return this._renderedVisible&&(null===(t=null===(e=this._parentPopup)||void 0===e?void 0:e.renderedVisible)||void 0===t||t)}updated(e){e.has("closeOnOutsideClick")&&this.processCapture(this.closeOnOutsideClick),this.reposition()}initializeFocusTrap(){this.initialVisibility&&this.isOpen&&this.focusLoop&&k.activate(this,!1)}get child(){return this._child}get isOpen(){return this._isOpen}get placementTargetElement(){return this.placementTargetElementField}set placementTargetElement(e){this.unsubscribeFromPlacementTargetPositionObserver(),this.placementTargetElementField=e,this.subscribeToPlacementTargetPositionObserver()}updateTargetElement(){if(!this.placementTarget)return;const e=document.querySelector(this.placementTarget);e&&(this.placementTargetElement=e)}get dropOpposite(){return this._dropOpposite}get autoFocus(){return this._autoFocus}get dropFromRight(){return this._dropFromRight}get offset(){return this._offset}set offset(e){this._offset=e,this.style.transform=d.translateByPoint(e)}get childSize(){return this.positionInfo.childSize}constructor(){super(),this._renderedVisible=!1,this.hasOwnLogicalParent=!1,this.tolerance=.01,this.repositionAction=new r,this.positionInfo=new Pi,this._child=null,this.childResizeObserver=new ResizeObserver(this.handleChildSizeChanged.bind(this)),this._dropOpposite=!1,this._dropFromRight=!1,this._offset=a.zero,this.placementTargetElementField=null,this.focusInHandler=this.handleFocusIn.bind(this),this.focusOutHandler=this.handleFocusOut.bind(this),this.focusLoopHandler=this.handleFocusLoop.bind(this),this.positionChangingHandler=this.handlePositionChanging.bind(this),this.sizeChangedHandler=this.handleSizeChanged.bind(this),this.placementTargetPositionChangedHandler=this.handlePlacementTargetPositionChanged.bind(this),this.placementTargetPositionObserver=new re(this.placementTargetPositionChangedHandler,{raf:!1}),this.placementTargetViabilityObserver=new hi((()=>this.updateTargetElement())),this.placementTargetElementChangedHandler=this.handlePlacementTargetElementChanged.bind(this),this.placementTargetObserver=null,this._autoFocus=!1,this._interceptor=null,this.bridgeSlotChangedHandler=this.handleBridgeSlotChange.bind(this),this.closingResultRequestedTcs=new le,this._positionChangedEvent=new ye,this._parentPopup=null,this.positionChangedHandler=this.handleParentPopupPositionChanged.bind(this),this._initialVisibility=!0,this._showingTimeoutId=void 0,this.isAdjustDialogEnabled=!0,this._capturedElementReleaseCallback=null,this._currentMaxHeight=null,this._currentMaxWidth=null,this._currentOverflow=null,this.closeOnOutsideClick=!1,this.closeOnPointerUp=!1,this.preventCloseOnPositionTargetClick=!1,this.closeMode=vi.Hide,this.fitToRestriction=!1,this.preventInteractions=!1,this.hasServerSideClosing=!1,this.parentBranchId=null,this.rootCssStyle=null,this.placement=fi.Absolute,this.horizontalOffset=0,this.verticalOffset=0,this.placementTarget=null,this.restrictTarget=null,this.restrict=bi.Viewport,this.width=null,this.maxWidth=null,this.minWidth=null,this.height=null,this.maxHeight=null,this.minHeight=null,this.desiredWidth=null,this.desiredHeight=null,this.minDesiredWidth=null,this.minDesiredHeight=null,this.renderWidth=null,this.renderHeight=null,this.restrictRectangle=t.Empty,this.placementRectangle=t.Empty,this.actualDropDirection=wi.Near,this.actualDropAlignment=Ci.bottom,this.actualDropOpposite=!1,this._isOpen=!1,this.animationEnabled=!1,this.resizing=!1,this.focusLoop=!1,this.zIndex=0,this.releasingCaptureAction=this.releaseFocusTrap,this.capturingAction=this.activate}render(){return this.renderTemplate()}activatePopupContent(){}willUpdate(e){this.renderWidth=this.calcRenderWidth(),this.renderHeight=this.calcRenderHeight(),this.shouldUpdateRootCssStyle(e)&&(this.rootCssStyle=this.composeRootCssStyle()),e.has(ui("placementTarget"))&&this.processPlacementTargetChanged()}composeRootCssStyle(){let e="";return this.shouldCalcWidth()&&(e+=`width: ${this.calcWidth()}; `),this.shouldCalcMinWidth()&&(e+=`min-width: ${this.calcMinWidth()}; `),this.shouldCalcMaxWidth()&&(e+=`max-width: ${this.calcMaxWidth()}; `),this.shouldCalcHeight()&&(e+=`height: ${this.calcHeight()}; `),this.shouldCalcMinHeight()&&(e+=`min-height: ${this.calcMinHeight()}; `),this.shouldCalcMaxHeight()&&(e+=`max-height: ${this.calcMaxHeight()}; `),e}processPlacementTargetChanged(){this.unsubscribeFromPlacementTargetObserver(),this.unsubscribeFromPlacementTargetPositionObserver(),this.subscribeToPlacementTargetObserver(),this.subscribeToPlacementTargetPositionObserver()}calcRenderHeight(){var e;return null!==(e=this.desiredHeight)&&void 0!==e?e:this.height}calcRenderWidth(){var e;return null!==(e=this.desiredWidth)&&void 0!==e?e:this.width}calcMaxWidth(){return this.maxWidth}calcMaxHeight(){return this.maxHeight}calcMinWidth(){var e;return null!==(e=this.minDesiredWidth)&&void 0!==e?e:this.minWidth}calcMinHeight(){var e;return null!==(e=this.minDesiredHeight)&&void 0!==e?e:this.minHeight}shouldCalcMinHeight(){return!!this.minHeight||!!this.minDesiredHeight}shouldCalcMinWidth(){return!!this.minWidth||!!this.minDesiredWidth}shouldCalcMaxWidth(){return!!this.maxWidth}shouldCalcMaxHeight(){return!!this.maxHeight}shouldCalcWidth(){return!!this.renderWidth}shouldCalcHeight(){return!!this.renderHeight}calcWidth(){return this.renderWidth}calcHeight(){return this.renderHeight}shouldUpdateRootCssStyle(e){return e.has("width")||e.has("minWidth")||e.has("maxWidth")||e.has("height")||e.has("minHeight")||e.has("maxHeight")||e.has("desiredWidth")||e.has("desiredHeight")||e.has("renderWidth")||e.has("renderHeight")||e.has("minDesiredWidth")||e.has("minDesiredHeight")}renderTemplate(){return Y`
            <div class="${this.rootCssStyle}">
                ${this.renderSlot}
            </div>
        `}renderSlot(){return Y`
            ${this.renderDefaultSlot()}
            ${this.renderAdditionalSlots()}
            ${this.renderBridgeSlot()}
        `}renderDefaultSlot(){return Y`<slot></slot>`}renderBridgeSlot(){return Y`
            <slot name="bridge" @slotchange=${this.bridgeSlotChangedHandler}></slot>
        `}renderAdditionalSlots(){return Y`
            <slot name="top-left" slot="top-left"></slot>
            <slot name="top-right" slot="top-right"></slot>
            <slot name="bottom-left" slot="bottom-left"></slot>
            <slot name="bottom-right" slot="bottom-right"></slot>
        `}firstUpdated(){this.child&&(this.childResizeObserver.observe(this.child),this.reposition())}connectedCallback(){super.connectedCallback(),this.style.display=u.flex}disconnectedCallback(){super.disconnectedCallback(),this.close()}createKeyboardNavigationStrategy(){return null}initializeKeyboardNavigator(){var e,t,i;if(this.keyboardNavigator||(this.keyboardNavigator=this.querySelector(ei)),this.keyboardNavigator&&!this.keyboardNavigator.initialized){const s=new Mi({createPopup:this.createKeyboardNavigationStrategy.bind(this)});null===(e=this.getPortal())||void 0===e||e.dispatchEvent(s);const n=null===(i=null===(t=s.detail.factory)||void 0===t?void 0:t.createPopup)||void 0===i?void 0:i.call(t,this.keyboardNavigator,this);n&&this.keyboardNavigator.initialize(this,n)}}handleBridgeSlotChange(e){const t=e.target.assignedNodes();this._interceptor=t[0]}async processCapturedPointerAsync(e,t){t.handled||this.isPointedCaptured(e)||(t.nextInteractionHandled=t.nextInteractionHandled||this.preventInteractions,await be.tryCloseCapturedElement(this,e,t))}isPointedCaptured(e){return!!z.containsInComposedPath(e,this)||(!!(e.target instanceof Node&&this.isSubItem(e.target))||this.shouldCloseOnPlacementTargetClick(e))}isSubItem(e){return!!w.findParent(e,(e=>e===this))}shouldCloseOnPlacementTargetClick(e){return!(!this.preventCloseOnPositionTargetClick||!this.placementTargetElement)&&f.containsInComposedPath(e,(e=>e===this.placementTargetElement))}async closeAsync(e,t){const i=await this.raiseClosingResultRequestedAsync(t);return this.closingResultRequestedTcs=new le,e.nextInteractionHandled=e.nextInteractionHandled||this.preventInteractions,e.handled=!0,Promise.resolve(!!i)}async closeHierarchyAsync(e,t,i){await be.closeHierarchyAsync(this,e,t,i)}async closeRootAsync(e,t,i){await be.closeRootAsync(this,e,t,i)}async processCapturedKeyDownAsync(e,t){if(await super.processCapturedKeyDownAsync(e,t),!t.handled&&"Escape"===e.key&&this.canProcessEscapeKeyDown()){f.markHandled(e);const i=await this.raiseClosingResultRequestedAsync(me.EscapePress);if(this.closingResultRequestedTcs=new le,!i)return void(t.handled=!0);this.unsubscribeCapture()}}canProcessEscapeKeyDown(){return!0}releaseFocusTrap(){k.deactivate(this,!this.isOpen)}raiseEvent(e,t){var i;null===(i=this._interceptor)||void 0===i||i.raise(e,t)}unsubscribeFromPlacementTargetObserver(){this.placementTargetObserver&&(this.placementTargetObserver.disconnect(),this.placementTargetObserver=null)}subscribeToPlacementTargetObserver(){this.isOpen&&(this.placementTargetObserver=new ae(this.placementTarget,this.placementTargetElementChangedHandler),this.placementTargetObserver.observe())}unsubscribeFromPlacementTargetPositionObserver(){this.placementTargetPositionObserver.disconnect(),this.placementTargetViabilityObserver.unsubscribe()}subscribeToParentPopup(){const e=w.findParent(this,(e=>x(e)));if(e&&x(e)){const t=e;t.positionChanged.subscribe(this.positionChangedHandler),this._parentPopup=t}}unsubscribeFromParentPopup(){this._parentPopup&&(this._parentPopup.positionChanged.unsubscribe(this.positionChangedHandler),this._parentPopup=null)}handleParentPopupPositionChanged(e,t){this.updatePosition()}subscribeToPlacementTargetPositionObserver(){this.placementTargetElement&&this.isOpen&&(this.placementTargetPositionObserver.observe(this.placementTargetElement),this.placementTargetViabilityObserver.subscribe(this.placementTargetElement))}show(){this.isOpen||(this._isOpen=!0,this.initialVisibility=!1,this._isOpen=!0,this.raiseShown(),this.addToLogicalTree(),this.addToVisibleBranchTree(),this.subscribeEvents(),this.initializeCapture(),this.reposition(),this.canAdjustZIndexOnShow&&this.raiseAdjustZIndexRequest(!0))}get canAdjustZIndexOnShow(){return!0}ensureBranchId(){if(!this.branchId)throw new Error("branchId should not be null")}addToVisibleBranchTree(){this.ensureBranchId(),ge.register(this.branchId)}removeFromVisibleBranchTree(){this.ensureBranchId(),ge.unregister(this.branchId)}getPortal(){return Ee.getPortal(this.branchId)}raiseShown(){this._setVisibilitySizing(!0);const e=new Hi;this.dispatchEvent(new Ni(e))}raiseClosed(){const e=new Hi;this.dispatchEvent(new Ii(e))}close(){this.isOpen&&(this._isOpen=!1,this.raiseClosed(),this.removeFromLogicalTree(),this.removeFromVisibleBranchTree(),this.unsubscribeEvents(),this.releaseCapture(),this.cleanUpAfterClose(),this.reposition(),this.canAdjustZIndexOnShow&&this.raiseAdjustZIndexRequest(!1))}notifyCloseCanceled(){this.processClosingRequested(!1),this.positionInfo.closingRequested=!1}cleanUpAfterClose(){this.updateVisibility(!1),this.animationEnabled=!1,this.positionInfo=new Pi}shouldCapture(){return this.closeOnOutsideClick||this.preventInteractions}async addToLogicalTree(){if(this.hasLogicalParent)return void(this.hasOwnLogicalParent=!0);this.ensureBranchId();const e=pe.getParentId(this.branchId),t=await P().getPopup(e);t&&E(t)&&t.addLogicalChild(this)}removeFromLogicalTree(){if(this.hasOwnLogicalParent)return void(this.hasOwnLogicalParent=!1);const e=w.getParent(this,!0);e&&E(e)&&e.removeLogicalChild(this)}initializeCapture(){this.shouldCapture&&this.subscribeCapture()}focusCapture(){}releaseCapture(){this.processClosingRequested(!0),this.unsubscribeCapture()}subscribeCapture(){const e=document.querySelector("dxbl-popup-root");e&&(this._capturedElementReleaseCallback=e.subscribe(this))}unsubscribeCapture(){this._capturedElementReleaseCallback&&(this._capturedElementReleaseCallback(),this._capturedElementReleaseCallback=null)}processClosingRequested(e){this.closingResultRequestedTcs.resolve(e),this.closingResultRequestedTcs=new le,this.positionInfo.closingRequested=!1}subscribeToChild(e){e&&this.childResizeObserver.observe(e,{box:"border-box"})}unsubscribeFromChild(e){e&&this.childResizeObserver.unobserve(e)}subscribeEvents(){this.isAdjustDialogEnabled&&(this.addEventListener("focusin",this.focusInHandler),this.addEventListener("focusout",this.focusOutHandler)),this.focusLoop&&this.addEventListener(ki.eventName,this.focusLoopHandler),document.addEventListener(c.eventName,this.positionChangingHandler),this.addEventListener(c.eventName,this.positionChangingHandler),window.addEventListener("resize",this.sizeChangedHandler),this.subscribeToPlacementTargetObserver(),this.subscribeToPlacementTargetPositionObserver(),this.subscribeToParentPopup()}unsubscribeEvents(){this.isAdjustDialogEnabled&&(this.removeEventListener("focusin",this.focusInHandler),this.removeEventListener("focusout",this.focusOutHandler)),this.focusLoop&&this.removeEventListener(ki.eventName,this.focusLoopHandler),document.removeEventListener(c.eventName,this.positionChangingHandler),this.removeEventListener(c.eventName,this.positionChangingHandler),this.removeEventListener("resize",this.sizeChangedHandler),this.unsubscribeFromPlacementTargetPositionObserver(),this.unsubscribeFromPlacementTargetObserver(),this.unsubscribeFromParentPopup()}reposition(){this.repositionAction.execute(this.updatePosition.bind(this))}activate(){const e=new b(new y(this.branchId));this.dispatchEvent(e)}forceReposition(){this.repositionAction.cancel(),this.updatePosition()}lockPlacement(){this.positionInfo.lockPlacement=!0}unlockPlacement(){this.positionInfo.lockPlacement=!1}handleChildSizeChanged(e,t){this.reposition(),this.contentLoaded()&&this.enableVisibility()}contentLoaded(){return!0}handlePositionChanging(e){this.reposition()}handleSizeChanged(e){this.reposition()}handlePlacementTargetPositionChanged(e,t){this.reposition(),this.enableVisibility()}handlePlacementTargetElementChanged(e,t){this.placementTargetElement=e.element,this.reposition()}handleFocusIn(e){this.raiseAdjustZIndexRequest(!0),this.subscribeCapture()}handleFocusOut(e){this.raiseAdjustZIndexRequest(!1)}handleFocusLoop(){this.initializeFocusTrap()}raiseAdjustZIndexRequest(e){this.dispatchEvent(new Li(new Bi(this.branchId,e))),e&&this.focusCapture()}isAbsolutePlacementMode(e){switch(e){case fi.MousePoint:case fi.Mouse:case fi.AbsolutePoint:case fi.Absolute:return!0}return!1}interestPointsFromRect(e){const t=new Array(5);return t[yi.TopLeft]=e.topLeft,t[yi.TopRight]=e.topRight,t[yi.BottomLeft]=e.bottomLeft,t[yi.BottomRight]=e.bottomRight,t[yi.Center]=new a(e.x+e.width/2,e.y+e.height/2),t}getPlacementTargetInterestPoints(e){let s,r=t.Empty;const h=this.placementTargetElement,d=new se(this.horizontalOffset,this.verticalOffset);if(!h||this.isAbsolutePlacementMode(e))e!==fi.Mouse&&e!==fi.MousePoint||(this.positionInfo.mousePosition||(this.positionInfo.mousePosition=new a(ie.x,ie.y)),r=t.create(this.positionInfo.mousePosition,n.Empty)),r=o.offset(r,d),s=this.interestPointsFromRect(r);else{r=this.placementRectangle,o.areSame(r,t.Empty)&&(r=e!==fi.Relative&&e!==fi.RelativePoint?new t(0,0,h.getBoundingClientRect().width,h.getBoundingClientRect().height):t.Empty),r=o.offset(r,d),s=this.interestPointsFromRect(r);const n=i.getRelativeElementRect(h).topLeft;for(let e=0;e<5;e++)s[e]=l.add(s[e],n)}return s}getChildInterestPoints(e){return this.child?(this._setVisibilitySizing(!0),this.interestPointsFromRect(new t(0,0,this.child.offsetWidth,this.child.offsetHeight))):this.interestPointsFromRect(t.Empty)}getBounds(e){let i=e[0].x,s=e[0].x,n=e[0].y,o=e[0].y;return e.forEach((e=>{const t=e.x,r=e.y;t<i&&(i=t),t>s&&(s=t),r<n&&(n=r),r>o&&(o=r)})),new t(i,n,s-i,o-n)}raiseCustomPlacement(e,t){const i=new Ri(new Si(e,t));return this.dispatchEvent(i),i.detail.customPlacement}getNumberOfCombinations(e){switch(e){case fi.Mouse:return 2;case fi.Bottom:case fi.Top:case fi.Right:case fi.Left:case fi.RelativePoint:case fi.MousePoint:case fi.AbsolutePoint:return 4;case fi.Custom:return 0;default:return 1}}getPointCombination(e,t){const i=this.dropFromRight;switch(e){case fi.Mouse:if(i){if(0===t)return[new Ei(yi.BottomRight,yi.TopRight),xi.Horizontal,!1,wi.Near,Ci.bottom];if(1===t)return[new Ei(yi.TopRight,yi.BottomRight),xi.Horizontal,!1,wi.Near,Ci.bottom]}else{if(0===t)return[new Ei(yi.BottomLeft,yi.TopLeft),xi.Horizontal,!1,wi.Near,Ci.bottom];if(1===t)return[new Ei(yi.TopLeft,yi.BottomLeft),xi.Horizontal,!1,wi.Near,Ci.bottom]}break;case fi.Bottom:if(i){if(0===t)return[new Ei(yi.BottomRight,yi.TopRight),xi.Horizontal,!1,wi.Far,Ci.bottom];if(1===t)return[new Ei(yi.TopRight,yi.BottomRight),xi.Horizontal,!0,wi.Far,Ci.top];if(2===t)return[new Ei(yi.BottomLeft,yi.TopLeft),xi.Horizontal,!1,wi.Near,Ci.bottom];if(3===t)return[new Ei(yi.TopLeft,yi.BottomLeft),xi.Horizontal,!0,wi.Near,Ci.top]}else{if(0===t)return[new Ei(yi.BottomLeft,yi.TopLeft),xi.Horizontal,!1,wi.Near,Ci.bottom];if(1===t)return[new Ei(yi.TopLeft,yi.BottomLeft),xi.Horizontal,!0,wi.Near,Ci.top];if(2===t)return[new Ei(yi.BottomRight,yi.TopRight),xi.Horizontal,!1,wi.Far,Ci.bottom];if(3===t)return[new Ei(yi.TopRight,yi.BottomRight),xi.Horizontal,!0,wi.Far,Ci.top]}break;case fi.Top:if(i){if(0===t)return[new Ei(yi.TopRight,yi.BottomRight),xi.Horizontal,!1,wi.Far,Ci.top];if(1===t)return[new Ei(yi.BottomRight,yi.TopRight),xi.Horizontal,!0,wi.Far,Ci.bottom];if(2===t)return[new Ei(yi.TopLeft,yi.BottomLeft),xi.Horizontal,!1,wi.Near,Ci.top];if(3===t)return[new Ei(yi.BottomLeft,yi.TopLeft),xi.Horizontal,!0,wi.Near,Ci.bottom]}else{if(0===t)return[new Ei(yi.TopLeft,yi.BottomLeft),xi.Horizontal,!1,wi.Near,Ci.top];if(1===t)return[new Ei(yi.BottomLeft,yi.TopLeft),xi.Horizontal,!0,wi.Near,Ci.bottom];if(2===t)return[new Ei(yi.TopRight,yi.BottomRight),xi.Horizontal,!1,wi.Far,Ci.top];if(3===t)return[new Ei(yi.BottomRight,yi.TopRight),xi.Horizontal,!0,wi.Far,Ci.bottom]}break;case fi.Right:case fi.Left:if(i&&e===fi.Right||!i&&e===fi.Left){if(0===t)return[new Ei(yi.TopLeft,yi.TopRight),xi.Vertical,!1,wi.Near,Ci.bottom];if(1===t)return[new Ei(yi.BottomLeft,yi.BottomRight),xi.Vertical,!0,wi.Near,Ci.top];if(2===t)return[new Ei(yi.TopRight,yi.TopLeft),xi.Vertical,!1,wi.Far,Ci.bottom];if(3===t)return[new Ei(yi.BottomRight,yi.BottomLeft),xi.Vertical,!0,wi.Far,Ci.top]}else{if(0===t)return[new Ei(yi.TopRight,yi.TopLeft),xi.Vertical,!1,wi.Near,Ci.bottom];if(1===t)return[new Ei(yi.BottomRight,yi.BottomLeft),xi.Vertical,!0,wi.Near,Ci.top];if(2===t)return[new Ei(yi.TopLeft,yi.TopRight),xi.Vertical,!1,wi.Far,Ci.bottom];if(3===t)return[new Ei(yi.BottomLeft,yi.BottomRight),xi.Vertical,!0,wi.Far,Ci.top]}break;case fi.Relative:case fi.RelativePoint:case fi.MousePoint:case fi.AbsolutePoint:if(i){if(0===t)return[new Ei(yi.TopLeft,yi.TopRight),xi.Horizontal,!1,wi.Far,Ci.bottom];if(1===t)return[new Ei(yi.TopLeft,yi.TopLeft),xi.Horizontal,!1,wi.Near,Ci.bottom];if(2===t)return[new Ei(yi.TopLeft,yi.BottomRight),xi.Horizontal,!0,wi.Far,Ci.top];if(3===t)return[new Ei(yi.TopLeft,yi.BottomLeft),xi.Horizontal,!0,wi.Near,Ci.top]}else{if(0===t)return[new Ei(yi.TopLeft,yi.TopLeft),xi.Horizontal,!1,wi.Near,Ci.bottom];if(1===t)return[new Ei(yi.TopLeft,yi.TopRight),xi.Horizontal,!1,wi.Far,Ci.bottom];if(2===t)return[new Ei(yi.TopLeft,yi.BottomLeft),xi.Horizontal,!0,wi.Near,Ci.top];if(3===t)return[new Ei(yi.TopLeft,yi.BottomRight),xi.Horizontal,!0,wi.Far,Ci.top]}break;case fi.Center:return[new Ei(yi.Center,yi.Center),xi.None,!1,wi.Near,Ci.bottom];default:return[new Ei(yi.TopLeft,yi.TopLeft),xi.None,!1,wi.Near,Ci.bottom]}return[new Ei(yi.TopLeft,yi.TopLeft),xi.None,!1,wi.Near,Ci.bottom]}getRestrictBounds(){switch(this.restrict){case bi.Viewport:return h.viewport();case bi.Page:return h.page();case bi.TargetElement:{if(!this.restrictTarget)throw new Error("restrictTarget should be specified if restrictmode is custom");const e=document.querySelector(this.restrictTarget);return i.getRelativeElementRect(e)}case bi.Rectangle:return this.restrictRectangle}return h.viewport()}updatePosition(){if(!this.isOpen)return this._renderedVisible=!1,void this._setVisibilityStyles(!1);const e=this.renderedVisible;this._renderedVisible=!0;const t=this.renderedVisible;if(!t)return this.updateVisibility(!0),void(e!==t&&this.raisePositionChanged(this.offset,!1));const i=this.placement,s=this.getPlacementTargetInterestPoints(i),n=this.getChildInterestPoints(i),r=this.getBounds(s);let h=this.getBounds(n);const d=h.width*h.height;let c=-1,u=new se(this.positionInfo.topLeft.x,this.positionInfo.topLeft.y),p=!1,g=-1,m=xi.None,b=0,v=this.actualDropDirection,y=this.actualDropOpposite,f=this.actualDropAlignment,w=null,C=null;i===fi.Custom?(C=this.processCustomPlacement(n,s),b=C?C.length:0):b=this.getNumberOfCombinations(i);for(let e=0;e<b;e++){if(this.positionInfo.lockPlacement&&this.positionInfo.lockedPosition!==e)continue;let t=new se(0,0),r=xi.None,a=!1,p=wi.Near,b=Ci.bottom,x=null;if(i===fi.Custom)x=C[e],t=ne.add(s[yi.TopLeft],x.point),r=x.axis,a=x.dropOpposite,p=x.dropDirection,b=x.dropAlignment;else{const[o,r,h,d,c]=this.getPointCombination(i,e);a=h,p=d,b=c,t=l.sub(s[o.targetInterestPoint],n[o.childInterestPoint])}const E=o.offset(h,t),P=this.getRestrictBounds(),T=o.intersect(P,E),S=T.isEmpty?0:T.width*T.height;if(S-g>this.tolerance&&(c=e,u=t,g=S,m=r,v=p,y=a,f=b,w=x,Math.abs(S-d)<this.tolerance))break}this.actualDropOpposite=y,this.actualDropDirection=v,this.actualDropAlignment=f,h=o.offset(h,u);const x=this.getRestrictBounds(),E=o.intersect(x,h);[u,p]=this.calcRestrictedBestTranslation(E,h,s,x,r,u),this.processBestTranslationResults(u,h,r,x,y,v,f,m,w),this.positionInfo.lockedPosition=c,this.positionInfo.childSize=h.size;const P=Math.round(u.x),T=Math.round(u.y);let S=!1;P===this.positionInfo.topLeft.x&&T===this.positionInfo.topLeft.y||(this.positionInfo.topLeft=new a(P,T),this.offset=this.positionInfo.topLeft,S=!0);const R=!t||p,H=this.updateVisibility(R);S=S||H,this.animationEnabled=t&&!p,this._setVisibilityStyles(!R),S&&this.raisePositionChanged(this.offset,R),this.closeMode===vi.Close&&!this.positionInfo.closingRequested&&R&&(this.positionInfo.closingRequested=!0,this.raiseClosingRequested(me.Programmatically))}raisePositionChanged(e,t){this._positionChangedEvent.raise(this,[!t,e])}updateVisibility(e){return this._renderedVisible=!e,this.positionInfo.visuallyHidden!==e&&(this._setVisibilityStyles(!e),this.positionInfo.visuallyHidden=e,!0)}processCustomPlacement(e,t){return this.raiseCustomPlacement(e,t)}raiseClosingRequested(e){this.dispatchEvent(new gi(e,this.branchId))}raiseClosingResultRequested(e){this.dispatchEvent(new mi(e,this.branchId))}async raiseClosingResultRequestedAsync(e){if(!this.hasServerSideClosing)return this.raiseClosingRequested(e),!0;const t=new le;return this.closingResultRequestedTcs=t,this.raiseClosingResultRequested(e),t.promise}calcRestrictedBestTranslation(e,t,i,s,n,o){return[this.fitToRestriction?this.calcRestrictedFitBestTranslation(e,t,i,s,o):o,this.closeMode!==vi.None&&this.calcRestrictedShouldHide(s,n)]}calcRestrictedShouldHide(e,t){if(!this.placementTargetElement)return!di.checkTargetEdgesWithin(e,t);let s=e;return i.getClippingParents(this.placementTargetElement).forEach((e=>{const t=i.getRelativeElementRect(e);s=o.intersect(s,t)})),!!s.isEmpty||!di.checkTargetEdgesWithin(s,t)}static checkTargetEdgesWithin(e,t){return o.contains(e,t.topLeft)||o.contains(e,t.topRight)||o.contains(e,t.bottomLeft)||o.contains(e,t.bottomRight)}calcRestrictedFitBestTranslation(e,t,i,s,n){if(Math.abs(e.width-t.width)>this.tolerance||Math.abs(e.height-t.height)>this.tolerance){const e=i[yi.TopLeft],o=l.sub(i[yi.TopRight],e),r=ne.normalize(new se(o.x,o.y));Number.isNaN(r.y)||Math.abs(r.y)<this.tolerance?t.right>s.right?n=new se(s.right-t.width,n.y):t.left<s.left&&(n=new se(s.left,n.y)):Math.abs(r.x)<this.tolerance&&(t.bottom>s.bottom?n=new se(n.x,s.bottom-t.bottom):t.top<s.top&&(n=new se(n.x,s.top)));const a=l.sub(e,i[yi.BottomLeft]),h=ne.normalize(a);Number.isNaN(h.x)||Math.abs(h.x)<this.tolerance?t.bottom>s.bottom?n=new se(n.x,s.bottom-t.height):t.top<s.top&&(n=new se(n.x,s.top)):Math.abs(h.y)<this.tolerance&&(t.right>s.right?n=new se(s.right-t.width,n.y):t.left<s.left&&(n=new se(s.x,n.y)))}return n}processCapture(e){}processBestTranslationResults(e,t,i,s,n,o,r,a,l){}};function Fi(){}function Vi(e){if(!e)throw new Error("failed");return e}e([K({type:Boolean,attribute:"close-on-outside-click"})],Ai.prototype,"closeOnOutsideClick",void 0),e([K({type:Boolean,attribute:"close-on-pointer-up"})],Ai.prototype,"closeOnPointerUp",void 0),e([K({type:Boolean,attribute:"prevent-close-on-position-target-click"})],Ai.prototype,"preventCloseOnPositionTargetClick",void 0),e([K({type:String,attribute:"close-mode"})],Ai.prototype,"closeMode",void 0),e([K({type:Boolean,attribute:"fit-to-restriction"})],Ai.prototype,"fitToRestriction",void 0),e([K({type:Boolean,attribute:"prevent-interactions"})],Ai.prototype,"preventInteractions",void 0),e([K({type:Boolean,attribute:"has-serverside-closing"})],Ai.prototype,"hasServerSideClosing",void 0),e([K({type:String,attribute:"branch-id"})],Ai.prototype,"branchId",void 0),e([K({type:String,attribute:"parent-branch-id"})],Ai.prototype,"parentBranchId",void 0),e([K({type:String,reflect:!1})],Ai.prototype,"rootCssStyle",void 0),e([K({type:String,attribute:"placement"})],Ai.prototype,"placement",void 0),e([K({type:Number,attribute:"horizontal-offset"})],Ai.prototype,"horizontalOffset",void 0),e([K({type:Number,attribute:"vertical-offset"})],Ai.prototype,"verticalOffset",void 0),e([K({type:String,attribute:"placement-target"})],Ai.prototype,"placementTarget",void 0),e([K({type:String,attribute:"restrict-target"})],Ai.prototype,"restrictTarget",void 0),e([K({type:String,attribute:"restrict"})],Ai.prototype,"restrict",void 0),e([K({type:String,attribute:"width"})],Ai.prototype,"width",void 0),e([K({type:String,attribute:"max-width"})],Ai.prototype,"maxWidth",void 0),e([K({type:String,attribute:"min-width"})],Ai.prototype,"minWidth",void 0),e([K({type:String,attribute:"height"})],Ai.prototype,"height",void 0),e([K({type:String,attribute:"max-height"})],Ai.prototype,"maxHeight",void 0),e([K({type:String,attribute:"min-height"})],Ai.prototype,"minHeight",void 0),e([K({type:String,attribute:"desired-width"})],Ai.prototype,"desiredWidth",void 0),e([K({type:String,attribute:"desired-height"})],Ai.prototype,"desiredHeight",void 0),e([K({type:String,attribute:"min-desired-width"})],Ai.prototype,"minDesiredWidth",void 0),e([K({type:String,attribute:"min-desired-height"})],Ai.prototype,"minDesiredHeight",void 0),e([K({type:String,reflect:!1})],Ai.prototype,"renderWidth",void 0),e([K({type:String,reflect:!1})],Ai.prototype,"renderHeight",void 0),e([K({type:String,attribute:"restrict-rect",converter:e=>e?o.parse(e):t.Empty})],Ai.prototype,"restrictRectangle",void 0),e([K({type:String,attribute:"placement-rect",converter:e=>e?o.parse(e):t.Empty})],Ai.prototype,"placementRectangle",void 0),e([K({type:String,attribute:"x-drop-direction",reflect:!0})],Ai.prototype,"actualDropDirection",void 0),e([K({type:String,attribute:"x-drop-alignment",reflect:!0})],Ai.prototype,"actualDropAlignment",void 0),e([K({type:String,attribute:"x-drop-opposite",reflect:!0,converter:{toAttribute:(e,t)=>e?"true":"false"}})],Ai.prototype,"actualDropOpposite",void 0),e([K({type:Boolean,attribute:"x-is-open",reflect:!0,converter:{fromAttribute:(e,t)=>{throw new Error("readonly")}}})],Ai.prototype,"_isOpen",void 0),e([K({type:Boolean,reflect:!1})],Ai.prototype,"animationEnabled",void 0),e([K({type:Boolean,attribute:"resizing"})],Ai.prototype,"resizing",void 0),e([K({type:Boolean,attribute:"focus-loop"})],Ai.prototype,"focusLoop",void 0),e([K({type:Number,attribute:"z-index"})],Ai.prototype,"zIndex",void 0),Ai=di=e([Z(ci)],Ai);const Wi={init:Fi,getReference:Vi,dxPopupTagName:ci,DxPopup:Ai},qi=Object.freeze({__proto__:null,dxPopupTagName:ci,PopupClosingRequestedEvent:gi,PopupClosingResultRequestedEvent:mi,get CloseMode(){return vi},get InterestPoint(){return yi},get PlacementMode(){return fi},get DropDirection(){return wi},get DropAlignment(){return Ci},get PopupPrimaryAxis(){return xi},CustomPopupPlacement:Ti,CustomPlacementEvent:Ri,PopupContext:Hi,PopupShownEvent:Ni,PopupClosedEvent:Ii,ZIndexDeltaChangeContext:zi,PopupZIndexDeltaChangeEvent:Di,PopupAdjustZIndexRequestContext:Bi,PopupAdjustZIndexRequestEvent:Li,PopupVisibleChangedEvent:Oi,PopupKeyboardStrategyActivateEvent:_i,PopupFocusLoopContext:class{},PopupFocusLoopEvent:ki,PopupKeyboardStrategyCreatingEvent:Mi,get DxPopup(){return Ai},init:Fi,getReference:Vi,default:Wi});export{Ee as A,he as B,Ti as C,Ai as D,me as E,Yt as F,Gt as G,Xt as H,yi as I,Ae as J,Se as K,kt as L,li as M,qi as N,Di as P,ye as S,le as T,se as V,zi as Z,wi as a,Ci as b,Vt as c,ni as d,fi as e,xi as f,Qt as g,ri as h,At as i,vi as j,Wt as k,_i as l,oi as m,$t as n,ai as o,Nt as p,ii as q,Mi as r,Fe as s,Ni as t,Ii as u,mi as v,gi as w,jt as x,zt as y,qt as z};
