import{_ as e}from"./tslib.es6-d65164b3.js";import{n as t}from"./property-4ec0b52d.js";import{e as s}from"./custom-element-267f9a21.js";import{s as i}from"./lit-element-462e7ad3.js";const n="dxbl-events-interceptor";let r=class extends i{constructor(){super(),this.eventsSet=new Set,this.events=null,this.eventListener=this.handleEvent.bind(this),this._value=null}get value(){return this._value}updated(e){var t,s;e.has("events")&&(this.unsubscribe(null!==(t=e.get("events"))&&void 0!==t?t:null),this.eventsSet=new Set(null===(s=this.events)||void 0===s?void 0:s.split(";")),this.subscribe(this.events))}subscribe(e){var t;null===(t=this.events)||void 0===t||t.split(";").forEach((e=>{this.addEventListener(e,this.eventListener)}))}unsubscribe(e){null==e||e.split(";").forEach((e=>{this.removeEventListener(e,this.eventListener)}))}raise(e,t){try{if(!this.eventsSet.has(e))return;this._value=JSON.stringify([e,t]),this.dispatchEvent(new Event("change",{bubbles:!0}))}finally{this._value=null}}handleEvent(e){}};e([t({type:String,attribute:"events"})],r.prototype,"events",void 0),r=e([s(n)],r);export{n as d};
