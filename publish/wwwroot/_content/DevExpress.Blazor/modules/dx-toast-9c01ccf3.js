import{_ as t}from"./tslib.es6-d65164b3.js";import{S as e}from"./single-slot-element-base-01d93921.js";import{C as s}from"./css-classes-c63af734.js";import{n as o}from"./property-4ec0b52d.js";import{e as i}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";class a{}a.Toast=s.Prefix+"-toast",a.ToastText=a.Toast+"-text";let r=class extends e{constructor(){super(...arguments),this.maxHeight=""}updated(t){t.has("maxHeight")&&this.calculateHeight()}calculateHeight(){this.style.maxHeight=this.maxHeight;const t=this.querySelector(`.${a.ToastText}`);if(t){const e=parseInt(window.getComputedStyle(t).lineHeight),s=Math.floor(t.offsetHeight/e);this.style.setProperty("--dxbl-toast-line-clamp",s.toString())}}};t([o({attribute:"max-height"})],r.prototype,"maxHeight",void 0),r=t([i("dxbl-toast")],r);export{r as DxToast,a as ToastCssClasses};
