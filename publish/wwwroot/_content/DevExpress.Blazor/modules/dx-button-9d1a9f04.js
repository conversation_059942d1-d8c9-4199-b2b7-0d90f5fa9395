import{_ as t}from"./tslib.es6-d65164b3.js";import{S as e}from"./single-slot-element-base-01d93921.js";import{D as o,F as i}from"./keyboard-navigation-strategy-ea41c807.js";import{k as r}from"./key-ffa272aa.js";import{D as s,M as n}from"./menu-keyboard-strategy-7c257fd3.js";import{D as a,a as c}from"./modal-keyboard-strategy-05da34d9.js";import{b as d}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{D as m}from"./dx-dropdown-owner-8d389864.js";import{n as l}from"./property-4ec0b52d.js";import{e as u}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./focushelper-2eea96ca.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./devices-17b9ba08.js";import"./popup-355ecaa4.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./portal-b3727c25.js";import"./constants-a4904a3f.js";import"./capture-manager-2454adc2.js";import"./nameof-factory-64d95f5b.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./constants-ed56e953.js";import"./constants-3209ffde.js";import"./dropdown-menu-keyboard-strategy-60d36909.js";const p=".dxbl-btn:not([disabled])",h=".dxbl-btn-dropdown-content-template";class b extends s{constructor(t,e){super(t,e),this._timeoutId=null,this.boundWaitForCustomSubMenuInitialization=this.waitForCustomSubMenuInitialization.bind(this),this.verticalOrientation=e.verticalOrientation}queryItems(){return[...this.targetElement.querySelectorAll(p)]}handleKeyDownCore(t){switch(r.KeyUtils.getEventKeyCode(t)){case r.KeyCode.Right:return!this.verticalOrientation&&(this.moveToNextItem(!0),!0);case r.KeyCode.Left:return!this.verticalOrientation&&(this.moveToPrevItem(!0),!0);case r.KeyCode.Home:return this.moveToFirstItem(),!0;case r.KeyCode.End:return this.moveToLastItem(),!0;case r.KeyCode.Up:return t.altKey&&!t.shiftKey?this.performCloseShortcutEvent(t):!!this.verticalOrientation&&(this.moveToPrevItem(!0),!0);case r.KeyCode.Down:return t.altKey&&!t.shiftKey?this.performOpenShortcutEvent(t):!!this.verticalOrientation&&(this.moveToNextItem(!0),!0);case r.KeyCode.Tab:return this.handleTabKeyDown(t);case r.KeyCode.Enter:case r.KeyCode.Space:return this.performOpenShortcutEvent(t);default:return!1}}performOpenShortcutEvent(t){return!!this.hasMenu()&&(this.performShortcutEvent(t),this.focusSubMenuItemAsync(n.First),!0)}performCloseShortcutEvent(t){return this.isMenuExpanded()&&this.performShortcutEvent(t),!0}handleTabKeyDown(t){return this.performCloseShortcutEvent(t),t.shiftKey?this.leaveBackward():this.leaveForward(),!0}onSubMenuShown(t){const e=t.targetElement.querySelector(o);e&&!e.initialized?this.waitForCustomSubMenuInitialization(e,(()=>super.onSubMenuShown(t))):super.onSubMenuShown(t)}waitForCustomSubMenuInitialization(t,e){this._timeoutId&&clearTimeout(this._timeoutId),t.initialized?e():this._timeoutId=setTimeout((()=>this.boundWaitForCustomSubMenuInitialization(t,e)),50)}createPopupStrategy(t,e){return new a(t,e,this,[p,h].join(","))}createModalStrategy(t,e){return new c(t,e,this,[p,h].join(","))}}const f="data-dxbutton-id";let j=class extends e{constructor(){super(...arguments),this.verticalOrientation=!1}connectedOrContentChanged(){super.connectedOrContentChanged(),this.initializeKeyboardNavigator()}disconnectedCallback(){delete this._keyboardNavigator,super.disconnectedCallback()}initializeKeyboardNavigator(){this._keyboardNavigator=this.querySelector(o),this._keyboardNavigator?this._keyboardNavigator.initialized?this._keyboardNavigator.reinitialize():this._keyboardNavigator.initialize(this,new b(this._keyboardNavigator,this)):i.removeTabIndex(this)}};t([l({attribute:"vertical-orientation",type:Boolean})],j.prototype,"verticalOrientation",void 0),j=t([u("dxbl-button")],j),customElements.define("dxbl-button-dropdown-item",class extends m{constructor(){super(...arguments),this.boundOnButtonClick=this.onButtonClick.bind(this)}connectedCallback(){super.connectedCallback(),this.addEventListener("click",this.boundOnButtonClick)}disconnectedCallback(){this.removeEventListener("click",this.boundOnButtonClick),super.disconnectedCallback()}canHandlePointerDown(t){return d.containsInComposedPath(t,this)}onButtonClick(t){if(null===this.getAttribute("submit-form-on-click"))return;const e=this.getAttribute(f);if(e){const t=document.querySelector(`[${f}=${e}]`);if(t){const e=document.createElement("input");e.type="submit",e.hidden=!0,t.appendChild(e),e.click(),t.removeChild(e)}}}});export{j as DxButton};
