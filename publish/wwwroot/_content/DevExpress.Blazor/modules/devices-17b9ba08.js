class e{static isHoverableDevice(){return window.matchMedia("(hover: hover) and (pointer: fine)").matches}}class i{constructor(e,i,t,s=!1){this.isMobileDevice=!1,this.hasAccurateInput=!1,this.isMac=!1,this.isTouchDevice=!1,this.clientMinutesOffset=-(new Date).getTimezoneOffset(),this.isMobileDevice=e,this.hasAccurateInput=i,this.isMac=t,this.isTouchDevice=s}}class t{constructor(e,i){this.useUAData=null,this.useNativeDetection=null,this.useUAData=e,this.useNativeDetection=i}getInfo(){return new i(this.isMobile(),window.matchMedia("(pointer:fine)").matches,/Mac/.test(navigator.userAgent),window.navigator.maxTouchPoints>0)}isMobile(){const e=window.navigator.userAgentData;if(this.useUAData&&e)return!!e.mobile;const i=this.isMobileByUserAgent(window.navigator.userAgent);if(this.useNativeDetection){const e=window.navigator.maxTouchPoints>0&&window.matchMedia("(pointer:coarse)").matches;return i||e}return i}isMobileByUserAgent(e){return/Mobi|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(e)}}function s(e=!0,i=!0){return new t(e,i).getInfo()}const n={DeviceInfo:i,DeviceInfoService:t,DeviceHelper:e,getDeviceInfo:s};export{e as D,n as d,s as g};
