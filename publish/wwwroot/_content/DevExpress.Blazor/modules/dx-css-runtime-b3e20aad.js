class e{constructor(e,t,o){this.defaultValueSource=e,this.computedValueSource=t,this.valueDestination=o}}const t=[new e("--bs-body-bg","background-color","--dxbl-body-bg"),new e("--bs-body-color","color","--dxbl-body-color")],o=["--primary","--bs-primary"],n=50,r=200;function a(){const e=document.body,o=window.getComputedStyle(e);t.forEach((t=>{e.style.removeProperty(t.valueDestination)})),t.filter((e=>!o.getPropertyValue(e.defaultValueSource))).forEach((t=>{e.style.setProperty(t.valueDestination,o.getPropertyValue(t.computedValueSource))}))}function i(e,t){if(0===t)return e();const n=window.getComputedStyle(document.body);if(o.map((e=>!!n.getPropertyValue(e))).reduce(((e,t)=>e||t),!1))return e();setTimeout((()=>i(e,t-1)),r)}function s(){return new Promise(((e,t)=>{const o=document.createElement("IFRAME");o.style.cssText="position: absolute; display: none; top: -10000px; left: -10000px;",o.onload=()=>{e(o)},document.body.appendChild(o)}))}let u=s();function c(e){return new Promise(((t,o)=>{(async function(){const e=await u;return e.parentNode?e:(u=s(),await u)})().then((o=>{const n=o.contentDocument,r=n.createElement("link");r.href=e,r.rel="stylesheet",r.onload=()=>{t(),r.remove()},r.onerror=()=>{t(),r.remove()},n.head.appendChild(r)}))}))}function d(e){return new Promise(((t,o)=>{c(e).then((()=>{i(t,n)}))}))}async function l(e){if(e.nodeType===Node.ELEMENT_NODE){if("LINK"===e.tagName){const t=e;if("stylesheet"===t.rel)return await d(t.href),!0}}return!1}function m(e){l(e).then((e=>{e&&a()}))}const f={initializeThemeCssRuntimeVariables:function(){const e=document.head;new MutationObserver(((e,t)=>{for(const t of e)"childList"===t.type&&t.addedNodes?t.addedNodes.forEach(m):"attributes"===t.type&&"href"===t.attributeName&&m(t.target)})).observe(e,{attributes:!0,childList:!0,subtree:!0}),Promise.all(Array.from(document.head.childNodes).map((e=>l(e)))).then((()=>a()))}};export{f as default};
