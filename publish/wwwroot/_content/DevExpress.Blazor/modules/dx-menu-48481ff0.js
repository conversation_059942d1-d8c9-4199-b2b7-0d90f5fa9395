import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t,M as s,a as i,b as o}from"./dx-menu-item-4f0de325.js";import{D as n}from"./dx-dropdown-owner-8d389864.js";import{a as r,M as a}from"./constants-ed56e953.js";import{e as l}from"./custom-element-267f9a21.js";import{S as h}from"./single-slot-element-base-01d93921.js";import{d as m}from"./dom-554d0cc7.js";import{dxLicenseTriggerName as c}from"./dx-license-30fd02d1.js";import{J as d,s as u}from"./popup-355ecaa4.js";import{D as p}from"./keyboard-navigation-strategy-ea41c807.js";import{k as I}from"./key-ffa272aa.js";import{D as C,f as v,g as b,T as f,b as y,c as g,M as T,d as M,e as S,i as _,a as w,j as E,k as x}from"./menu-keyboard-strategy-7c257fd3.js";import{D as K,a as L,b as W}from"./dropdown-menu-keyboard-strategy-60d36909.js";import{D}from"./data-qa-utils-8be7c726.js";import{n as j}from"./property-4ec0b52d.js";import{t as z}from"./state-c294470d.js";import"./custom-events-helper-e7f279d3.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./portal-b3727c25.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./eventhelper-8bcec49f.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./devices-17b9ba08.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./dom-utils-d057dcaa.js";import"./constants-3209ffde.js";let O=class extends n{constructor(){super(),this.desiredWidth=null,this.dropDownWidthSourceResizeObserver=new ResizeObserver(this.onDropDownWidthSourceSizeChanged.bind(this))}connectedCallback(){super.connectedCallback(),this.observeForDropDownWidthSourceElementSize()}disconnectedCallback(){this.dropDownWidthSourceResizeObserver.disconnect(),super.disconnectedCallback()}ensureDropDownElement(){super.ensureDropDownElement(),this.updateDropDownDesiredWidth()}onDropDownWidthSourceSizeChanged(e,t){if(e.length<1)return;this.desiredWidth=e[0].contentRect.width+"px",this.updateDropDownDesiredWidth()}updateDropDownDesiredWidth(){this.dropDownElement&&(this.dropDownElement.desiredWidth=this.desiredWidth)}observeForDropDownWidthSourceElementSize(){this.dropDownWidthSourceResizeObserver.disconnect();const e=this.getDropDownWidthSourceElement();e&&this.dropDownWidthSourceResizeObserver.observe(e)}getDropDownWidthSourceElement(){return this}};O=e([l(r.menuNavBarComponentName)],O);class P{constructor(e){var t;this.menuItem=e,this.id=null!==(t=e.id)&&void 0!==t?t:"",this.width=-1,this.adaptivePriority=e.adaptivePriority,this.canCrop=e.canCrop,this.textElement=null,this.textWidth=-1,this.container=e.closest(`[data-dxmenu-item-id="${this.id}"]`),this.initialize()}canCollapse(){return this.canCrop&&null!==this.textElement}canReduce(){return this.canCollapse()&&!m.DomUtils.hasClassName(this.textElement,a.menuItemTextHiddenClass)}canIncrease(){return this.canCollapse()&&m.DomUtils.hasClassName(this.textElement,a.menuItemTextHiddenClass)}showTextElement(){this.toggleCssClass(this.textElement,a.menuItemTextHiddenClass,!1),this.toggleCssClass(this.container,a.menuListItemHiddenClass,!1)}hideTextElement(){this.toggleCssClass(this.textElement,a.menuItemTextHiddenClass,!0),this.toggleCssClass(this.container,a.menuListItemHiddenClass,!0)}initialize(){let e=0;this.container&&(e=this.calculateChildrenOffsetWidth(this.container));const t=this.menuItem.querySelector(`*:not(${c})`);if(t){e+=this.calculateChildrenOffsetWidth(t);for(let s=0;s<t.children.length;s++){const i=t.children[s],o=this.calculateElementWidth(i);i.matches(`div.${a.menuItemTextContainer}`)&&(this.textElement=i,this.textWidth=o),e+=o}}this.width=e}isHTMLElement(e){return!!e.offsetWidth}calculateElementWidth(e){const t=getComputedStyle(e);return(this.isHTMLElement(e)?e.offsetWidth:e.getBoundingClientRect().width)+parseFloat(t.marginLeft)+parseFloat(t.marginRight)}calculateChildrenOffsetWidth(e){const t=m.DomUtils.getCurrentStyle(e);return parseFloat(t.paddingLeft)+parseFloat(t.paddingRight)+parseFloat(t.marginLeft)+parseFloat(t.marginRight)+parseFloat(t.borderLeftWidth)+parseFloat(t.borderRightWidth)}toggleCssClass(e,t,s){e&&e.classList.toggle(t,s)}}class A{constructor(e,t){this.width=e,this.minWidth=t}}class N{constructor(e){this._currentWidth=0,this._itemsWidth=new A(0,0),this._itemsOffset=0,this._collapseToHamburgerWidth=-1,this._menuItemInfos=[],this._resizeObserver=null,this._menuView=e}get isCollapsed(){return this._menuView.isCollapsed}get isVertical(){return this._menuView.isVertical}get canCollapse(){return this._menuView.canCollapse}get collapseToIcons(){return this._menuView.collapseToIcons}get collapseToIconsAll(){return this._menuView.collapseToIconsAll}get collapseToHamburgerMenu(){return this._menuView.collapseToHamburgerMenu}async initialize(e,t){this.unsubsribeFromSizeChanges(),void 0!==t&&this.canCollapse&&(this.collapseToIcons||this.collapseToIconsAll||this.collapseToHamburgerMenu)?(this._menuItemInfos=await d(null,(t=>this.createMenuItemInfos(e))),this._currentWidth=t,this.isCollapsed||(this.isVertical?(this._itemsOffset=0,this._itemsWidth=new A(Math.max(...this._menuItemInfos.map((e=>e.width))),0),this._collapseToHamburgerWidth=-1):(this._itemsOffset=this._menuView.itemsOffset,this._itemsWidth=new A(this.calculateItemsWidth(),this.calculateItemsMinWidth()),this._collapseToHamburgerWidth=this.collapseToIcons||this.collapseToIconsAll?this._itemsWidth.minWidth+this._itemsOffset:this._itemsWidth.width+this._itemsOffset)),await u(null,(e=>this.resize(this._currentWidth))),this.subscribeToSizeChanges()):(this.isCollapsed?this._menuView.onCollapseChanged(!1):await u(null,(e=>this.increaseAllItems())),this._menuItemInfos=[])}resize(e){if(this.isCollapsed)(e>this._collapseToHamburgerWidth||!this.collapseToHamburgerMenu)&&this._menuView.onCollapseChanged(!1);else{const t=e-this._currentWidth;if(this.isVertical)this.collapseToIconsAll&&(t<=0&&this._itemsWidth.width>e?this.reduceAllItems():this._itemsWidth.width<e&&this.increaseAllItems());else{if(this.collapseToIcons){let s=this._itemsOffset;this._menuItemInfos.forEach((e=>{s+=e.width,e.canIncrease()&&(s-=e.textWidth)})),t<=0&&s>e?this.reduceItems(e,s):t>=0&&this.increaseItems(e,s)}else if(this.collapseToIconsAll){const s=this._itemsWidth.width+this._itemsOffset;t<=0&&s>e?this.reduceAllItems():t>=0&&s<e&&this.increaseAllItems()}else this.increaseAllItems();this.collapseToHamburgerMenu&&t<=0&&e<this._collapseToHamburgerWidth&&this._menuView.onCollapseChanged(!0)}}this._currentWidth=e}dispose(){this.unsubsribeFromSizeChanges(),this._menuItemInfos=[]}subscribeToSizeChanges(){this.subscribeToRootElementSizeChange()}unsubsribeFromSizeChanges(){this.unsubscribeFromRootElementSizeChange()}subscribeToRootElementSizeChange(){this._resizeObserver=new ResizeObserver((e=>{const t=e[0];t&&this.resize(t.contentRect.width)})),this._resizeObserver.observe(this._menuView.root)}unsubscribeFromRootElementSizeChange(){var e;null===(e=this._resizeObserver)||void 0===e||e.disconnect(),this._resizeObserver=null}createMenuItemInfos(e){const t=[];return e&&(e.forEach((e=>{t.push(new P(e))})),t.sort(((e,t)=>e.adaptivePriority-t.adaptivePriority))),t}calculateItemsWidth(){let e=0;return this._menuItemInfos.forEach((t=>e+=t.width)),e}calculateItemsMinWidth(){let e=0;return this._menuItemInfos.forEach((t=>{e+=t.width,t.canCollapse()&&(e-=t.textWidth)})),e}reduceItems(e,t){for(let s=this._menuItemInfos.length-1;s>=0&&t>e;s--){const e=this._menuItemInfos[s];e.canReduce()&&(e.hideTextElement(),t-=e.textWidth)}}increaseItems(e,t){for(let s=0;s<this._menuItemInfos.length;s++){const i=this._menuItemInfos[s];if(i.canIncrease()){if(t+i.textWidth>e)break;i.showTextElement(),t+=i.textWidth}}}reduceAllItems(){for(let e=0;e<this._menuItemInfos.length;e++){const t=this._menuItemInfos[e];t.canReduce()&&t.hideTextElement()}}increaseAllItems(){for(let e=0;e<this._menuItemInfos.length;e++){const t=this._menuItemInfos[e];t.canIncrease()&&t.showTextElement()}}}var q;!function(e){e[e.Unknown=0]="Unknown",e[e.MoveToNextItem=2]="MoveToNextItem",e[e.MoveToPrevItem=4]="MoveToPrevItem",e[e.MoveToFirstItem=8]="MoveToFirstItem",e[e.MoveToLastItem=16]="MoveToLastItem",e[e.OpenSubMenu=32]="OpenSubMenu",e[e.Execute=64]="Execute",e[e.Leave=128]="Leave"}(q||(q={}));class F extends C{constructor(e,t){super(e,t),this._keyCodeToCommandMap=this.createKeyCodeToCommandMap()}get menuItemSelector(){return v}queryItems(){return this.needToAddTitle()?this.queryItemsBySelector(`${v}, ${b}`):this.queryItemsBySelector(v)}getCommands(e){var t;return null!==(t=this._keyCodeToCommandMap.get(e))&&void 0!==t?t:q.Unknown}createPopupStrategy(e,t){return new K(e,t,v,this,this.hasTransitContainer(t))}hasTransitContainer(e){return e.hasAttribute(f)}createModalStrategy(e,t){throw new Error("This strategy type is not supported in the current context.")}moveToNextItem(e=!1){this.isTitle(this.selectedItemElement)||(this.selectedItemIndex<this.itemCount-1?this.selectItem(this.selectedItemIndex+1):e&&!this.isTitle(this.items[0])&&this.selectItem(0))}moveToPrevItem(e=!1){this.isTitle(this.selectedItemElement)||(this.selectedItemIndex>0&&!this.isTitle(this.items[this.selectedItemIndex-1])?this.selectItem(this.selectedItemIndex-1):e&&!this.isTitle(this.items[this.itemCount-1])&&this.selectItem(this.itemCount-1))}handleKeyDownCore(e){switch(this.getCommands(I.KeyUtils.getEventKeyCode(e))){case q.MoveToNextItem:return this.subMenuStrategy?this.closeSubMenuAndMoveToNextItem():this.moveToNextItem(!0),!0;case q.MoveToPrevItem:return this.subMenuStrategy?this.closeSubMenuAndMoveToPrevItem():this.moveToPrevItem(!0),!0;case q.OpenSubMenu|q.MoveToLastItem:return!(!y(this.selectedItemElement)||!g(this.targetElement))&&(this.subMenuStrategy?this.subMenuStrategy.focusLastItem():this.openSubMenuAndFocusItem(T.Last),!0);case q.OpenSubMenu|q.MoveToFirstItem:return!(!y(this.selectedItemElement)||!g(this.targetElement))&&(this.subMenuStrategy?this.subMenuStrategy.focusFirstItem():this.openSubMenuAndFocusItem(T.First),!0);case q.MoveToFirstItem:return this.isTitle(this.items[0])?this.itemCount>1&&this.selectItem(1):this.selectItem(0),!0;case q.MoveToLastItem:return this.isTitle(this.items[this.itemCount-1])?this.itemCount>1&&this.selectItem(this.itemCount-2):this.selectItem(this.itemCount-1),this.selectItem(this.itemCount-1),!0;case q.Execute:return this.isTitle(this.selectedItemElement)||this.doAction(e),!0;case q.Leave:return this.subMenuStrategy&&this.requestSubMenuClose(),this.handleTabKeyDown(e);default:return!1}}doAction(e){this.raiseClickEvent(this.selectedItemElement,e.ctrlKey,e.metaKey,e.shiftKey),y(this.selectedItemElement)&&this.focusSubMenuItemAsync(T.First)}closeSubMenu(){this.requestSubMenuClose()}closeSubMenuAndMoveToPrevItem(){this.requestSubMenuClose(),this.moveToPrevItem(!0),this.requestSubMenuOpen()}closeSubMenuAndMoveToNextItem(){this.requestSubMenuClose(),this.moveToNextItem(!0),this.requestSubMenuOpen()}openSubMenuAndFocusItem(e){return this.requestSubMenuOpen(),this.focusSubMenuItemAsync(e)}requestSubMenuOpen(){var e;null===(e=this.selectedItemElement)||void 0===e||e.dispatchEvent(new M(S.Expand))}requestSubMenuClose(){var e;null===(e=this.selectedItemElement)||void 0===e||e.dispatchEvent(new M(S.Collapse))}}class k extends F{createKeyCodeToCommandMap(){return new Map([[I.KeyCode.Down,q.MoveToNextItem],[I.KeyCode.Up,q.MoveToPrevItem],[I.KeyCode.Right,q.OpenSubMenu|q.MoveToFirstItem],[I.KeyCode.Left,q.OpenSubMenu|q.MoveToLastItem],[I.KeyCode.Home,q.MoveToFirstItem],[I.KeyCode.End,q.MoveToLastItem],[I.KeyCode.Enter,q.Execute],[I.KeyCode.Space,q.Execute],[I.KeyCode.Tab,q.Leave]])}closeSubMenuAndMoveToNextItem(){this.requestSubMenuClose()}closeSubMenuAndMoveToPrevItem(){this.requestSubMenuClose()}createPopupStrategy(e,t){return new V(e,t,v,this,this.hasTransitContainer(t))}}class V extends L{createSubMenuStrategy(e,t){return new W(e,t,this.menuItemSelector,this,this.hasTransitContainer(t))}closeSelf(){return this.parentStrategy.closeSubMenu(),!0}}class H extends F{createKeyCodeToCommandMap(){return new Map([[I.KeyCode.Right,q.MoveToNextItem],[I.KeyCode.Left,q.MoveToPrevItem],[I.KeyCode.Down,q.OpenSubMenu|q.MoveToFirstItem],[I.KeyCode.Up,q.OpenSubMenu|q.MoveToLastItem],[I.KeyCode.Home,q.MoveToFirstItem],[I.KeyCode.End,q.MoveToLastItem],[I.KeyCode.Enter,q.Execute],[I.KeyCode.Space,q.Execute],[I.KeyCode.Tab,q.Leave]])}}class U extends _{constructor(){super(...arguments),this._levelController=new w}get menuLevel(){return this._levelController.currentLevel}initialize(){super.initialize(),this._levelController.updateState(this.selectedItemIndex,this.itemCount),this.selectedItemIndex=this._levelController.selectedItemIndex}queryItems(){return this.needToAddTitle()?this.queryItemsBySelector(`${v}, .${a.subMenuTemplate}, ${b}`):this.queryItemsBySelector(`${v}, .${a.subMenuTemplate}`)}moveToNextItem(e=!1){this.isTitle(this.selectedItemElement)||(this.selectedItemIndex<this.itemCount-1?this.selectItem(this.selectedItemIndex+1):e&&!this.isTitle(this.items[0])&&this.selectItem(0))}moveToPrevItem(e=!1){this.isTitle(this.selectedItemElement)||(this.selectedItemIndex>0&&!this.isTitle(this.items[this.selectedItemIndex-1])?this.selectItem(this.selectedItemIndex-1):e&&!this.isTitle(this.items[this.itemCount-1])&&this.selectItem(this.itemCount-1))}handleKeyDownCore(e){switch(I.KeyUtils.getEventKeyCode(e)){case I.KeyCode.Down:return this.moveToNextItem(!0),!0;case I.KeyCode.Up:return this.moveToPrevItem(!0),!0;case I.KeyCode.Enter:case I.KeyCode.Space:return this.isTitle(this.selectedItemElement)||(this.raiseClickEvent(this.selectedItemElement,e.ctrlKey,e.metaKey,e.shiftKey),this.selectedItemElement.hasAttribute("data-header")?this.onMoveToPrevLevel():y(this.selectedItemElement)&&this.onMoveToNextLevel(T.First)),!0;case I.KeyCode.Right:return!!y(this.selectedItemElement)&&(this.performShortcutEvent(e),this.onMoveToNextLevel(T.First),!0);case I.KeyCode.Left:return this.menuLevel>0?(this.performShortcutEvent(e),this.onMoveToPrevLevel(),!0):y(this.selectedItemElement)?(this.performShortcutEvent(e),this.onMoveToNextLevel(T.Last),!0):this.menuLevel>0&&(this.performShortcutEvent(e),this.onMoveToPrevLevel(),!0);case I.KeyCode.Tab:return this.handleTabKeyDown(e);default:return!1}}getShortcutContext(){const e=this.menuLevel>0||this.needToAddTitle()?this.selectedItemIndex-1:this.selectedItemIndex;return{level:this.menuLevel,selectedItemIndex:e}}onMoveToNextLevel(e){this._levelController.moveToNextLevel(e)}onMoveToPrevLevel(){this._levelController.moveToPrevLevel()}}const R=`.${a.menuNav} > .${a.menuNavBar} > .dxbl-btn`;class $ extends C{createModalStrategy(e,t){throw new Error("This strategy type is not supported in the current context.")}createPopupStrategy(e,t){return new B(e,t,this)}queryItems(){return this.needToAddTitle()?this.queryItemsBySelector(`${R}, ${b}`):this.queryItemsBySelector(R)}handleKeyDownCore(e){if(!this.isTitle(this.selectedItemElement))switch(I.KeyUtils.getEventKeyCode(e)){case I.KeyCode.Up:return this.performShortcutEvent(e),this.focusSubMenuItemAsync(T.Last),!0;case I.KeyCode.Down:case I.KeyCode.Enter:case I.KeyCode.Space:return this.performShortcutEvent(e),this.focusSubMenuItemAsync(T.First),!0}return I.KeyUtils.getEventKeyCode(e)===I.KeyCode.Tab&&(this.performShortcutEvent(e),this.handleTabKeyDown(e))}isImmediatelyClickEnabled(){return!1}}class B extends E{constructor(e,t,s){super(e,t),this._itemIdToParentIdMap=new Map,this._parentStrategy=s}initialize(){super.initialize(),this._itemIdToParentIdMap.clear();const e=this.targetElement.querySelector(`.${a.navigation}`);e&&this.mapItemsToParent(e,null)}mapItemsToParent(e,t){for(const s of e.children){const e=s.dataset.dxmenuItemId;if(!e)continue;this._itemIdToParentIdMap.set(e,t);const i=Array.from(s.children).find((e=>e instanceof HTMLUListElement));i&&this.mapItemsToParent(i,e)}}queryItems(){return this.queryItemsBySelector(`${v}, .${a.subMenuTemplate}`)}handleKeyDown(e){const t=I.KeyUtils.getEventKeyCode(e);return!(!this.nestedContentSelected&&t===I.KeyCode.Tab)&&super.handleKeyDown(e)}handleKeyDownCore(e){switch(I.KeyUtils.getEventKeyCode(e)){case I.KeyCode.Down:return this.moveToNextItem(!0),!0;case I.KeyCode.Up:return this.moveToPrevItem(!0),!0;case I.KeyCode.Home:return this.focusFirstItem(),!0;case I.KeyCode.End:return this.focusLastItem(),!0;case I.KeyCode.Right:return y(this.selectedItemElement)&&(x(this.selectedItemElement)?this.moveToNextItem(!0):this.requestSubMenuOpen()),!0;case I.KeyCode.Left:if(x(this.selectedItemElement))this.requestSubMenuClose();else{const e=this.getParentNode(this.selectedItemElement);e&&(this.updateSelectedItemByChildElement(e),this.focusSelectedItem())}return!0;case I.KeyCode.Enter:case I.KeyCode.Space:return this.doClick(this.selectedItemElement,e),!0;default:return!1}}getParentNode(e){var t;const s=null===(t=e.closest(`.${a.menuItem}`))||void 0===t?void 0:t.getAttribute("id");if(s){const e=this._itemIdToParentIdMap.get(s);if(e)return this.targetElement.querySelector(`#${e}${v}`)}return null}requestSubMenuOpen(){var e;null===(e=this.selectedItemElement)||void 0===e||e.dispatchEvent(new M(S.Expand))}requestSubMenuClose(){var e;null===(e=this.selectedItemElement)||void 0===e||e.dispatchEvent(new M(S.Collapse))}handlePopupShown(){this._parentStrategy.onSubMenuShown(this)}handlePopupClosed(){this._parentStrategy.onSubMenuClosed(this)}}var J;!function(e){e[e.Small=0]="Small",e[e.Medium=1]="Medium",e[e.Large=2]="Large"}(J||(J={}));let G=class extends h{constructor(){super(),this._menuItems=[],this._sizeController=null,this._handlePromise=Promise.resolve(),this._keyboardNavigator=null,this.isVertical=!1,this.isCollapsed=!1,this.canCollapse=!1,this.collapseToIcons=!1,this.collapseToIconsAll=!1,this.collapseToHamburgerMenu=!1,this.loadMode=!1,this.hasVisibleItems=!1,this.sizeMode=J.Medium,this.isMobileMode=!1,this._menuItemsChanged=!1,this._firstUpdate=!0,this._intersectionObserver=new IntersectionObserver(this.onItemsIntersectionChanged.bind(this),{root:this,threshold:[0,1]}),this._onItemDisconnected=e=>{const i=e.target;i instanceof t&&(i.removeEventListener(s.eventName,this._onItemDisconnected),this._intersectionObserver.unobserve(i),this._menuItemsChanged=!0)},this._onItemConnected=()=>{this._menuItemsChanged=!0},this.addEventListener(i.eventName,this._onItemConnected)}get root(){return this}get items(){return this._menuItems}get itemsOffset(){return this.calculateItemsOffset()}restoreState(){D.setLoaded(this),this.getAttribute("tabindex")||this.initializeKeyboardNavigatior()}connectedCallback(){super.connectedCallback(),this._sizeController=new N(this)}disconnectedCallback(){var e;super.disconnectedCallback(),null===(e=this._sizeController)||void 0===e||e.dispose(),this._sizeController=null,this._intersectionObserver.disconnect()}initializeKeyboardNavigatior(){this._keyboardNavigator=this.querySelector(p),this._keyboardNavigator&&this._keyboardNavigator.initialize(this,this.createKeyboardStrategy(this._keyboardNavigator))}createKeyboardStrategy(e){return this.isMobileMode?this.isVertical?new U(e,this):new $(e,this):this.isVertical?new k(e,this):new H(e,this)}async getUpdateComplete(){return await this._handlePromise,super.getUpdateComplete()}firstUpdated(){this._firstUpdate=!1}willUpdate(e){this._handlePromise=this.handleChangedProperties(e,this._firstUpdate)}async handleChangedProperties(e,t){var i,o;await this._handlePromise,this.anyOf(e,"isMobileMode","isVertical")&&this.initializeKeyboardNavigatior(),(this.anyOf(e,"canCollapse","isCollapsed","isVertical")||this._menuItemsChanged)&&(this._menuItems=this.isCollapsed?[]:this.collectMenuItems(),this._intersectionObserver.disconnect(),this._menuItems.forEach((e=>{e.addEventListener(s.eventName,(e=>this._onItemDisconnected)),this._intersectionObserver.observe(e)})),this._menuItemsChanged=!1);const n=this.offsetWidth;if(!t&&this.canCollapse&&e.has("sizeMode")&&(this.isCollapsed||await this.toggleItemsContainerCssClass(a.invisible,!0),await(null===(i=this._sizeController)||void 0===i?void 0:i.initialize(this._menuItems))),this.hasVisibleItems){await(null===(o=this._sizeController)||void 0===o?void 0:o.initialize(this._menuItems,n));const e={[a.invisible]:!1};e[a.menuLoading]=!1,await this.toggleItemsContainerCssClasses(e)}}onCollapseChanged(e){this.dispatchEvent(new o(e))}anyOf(e,...t){for(const s of t)if(e.has(s))return!0;return!1}collectMenuItems(){const e=this.querySelectorAll(r.menuItemComponentName),t=[];return e&&e.forEach((e=>t.push(e))),t}onItemsIntersectionChanged(e){e.forEach((e=>e.target.isVisible=e.isIntersecting)),this.hasVisibleItems=this._menuItems.filter((e=>e.isVisible)).length>0}async toggleItemsContainerCssClasses(e){const t=this.querySelector(r.menuItemsContainerSelector);t&&await u(t,(t=>{for(const[s,i]of Object.entries(e))t.classList.toggle(s,i)}))}async toggleItemsContainerCssClass(e,t){await this.toggleItemsContainerCssClasses({[e]:t})}calculateItemsOffset(){const e=this.querySelector(r.menuItemsContainerSelector);return e?e.offsetLeft-this.offsetLeft:0}};e([j({type:Boolean,attribute:"is-vertical"})],G.prototype,"isVertical",void 0),e([j({type:Boolean,attribute:"is-collapsed"})],G.prototype,"isCollapsed",void 0),e([j({type:Boolean,attribute:"can-collapse"})],G.prototype,"canCollapse",void 0),e([j({type:Boolean,attribute:"collapse-to-icons"})],G.prototype,"collapseToIcons",void 0),e([j({type:Boolean,attribute:"collapse-to-icons-all"})],G.prototype,"collapseToIconsAll",void 0),e([j({type:Boolean,attribute:"collapse-to-hamburger-menu"})],G.prototype,"collapseToHamburgerMenu",void 0),e([j({type:Boolean,attribute:"load-mode"})],G.prototype,"loadMode",void 0),e([j({type:J,attribute:"size-mode"})],G.prototype,"sizeMode",void 0),e([j({type:Boolean,attribute:"data-dx-menu-mobile"})],G.prototype,"isMobileMode",void 0),e([z()],G.prototype,"hasVisibleItems",void 0),G=e([l(r.menuComponentName)],G);const Q=[t,O];const X={loadModule:function(){}};export{G as DxMenu,X as default,Q as dependentTypes};
