import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{o as s}from"./constants-da6cacac.js";import{H as i,V as n,a as o,D as r,b as l}from"./dx-scroll-viewer-da0fb41c.js";import{S as a}from"./scroll-viewer-css-classes-e724f203.js";import{L as c,a as d,b as m,c as h,d as u,e as I}from"./dx-list-box-events-6c145567.js";import{d as p}from"./dom-554d0cc7.js";import{b as g}from"./browser-3fc721b7.js";import{k as b}from"./key-ffa272aa.js";import{K as y,F as S}from"./keyboard-navigation-strategy-ea41c807.js";import{D as w}from"./layouthelper-67dd777a.js";import{addFocusHiddenAttribute as v,removeFocusHiddenAttribute as x}from"./focus-utils-ae044224.js";import{D as V}from"./dx-html-element-base-3262304e.js";import{a as f}from"./dx-virtual-scroll-viewer-f4a3bc9e.js";import{e as C}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./property-4ec0b52d.js";import"./css-classes-c63af734.js";import"./text-editor-733d5e56.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./devices-17b9ba08.js";import"./grid-scroll-utils-a8c65cf1.js";import"./screenhelper-e9ec6e3e.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./focushelper-2eea96ca.js";import"./constants-7c047c0d.js";import"./dom-utils-d057dcaa.js";import"./point-e4ec110e.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./thumb-31d768d7.js";class E extends y{constructor(e,t){super(e.getKeyboardNavigator(),t),this.component=e}getTable(){return this.component.getTable()}getList(){return this.component.getList()}getSearchBox(){return this.component.getSearchBox()}getScrollViewer(){return this.component.getScrollViewer()}getScrollViewerContent(){return this.component.getScrollViewerContent()}getColumnInfoByCell(e){return this.component.getColumnInfoByCell(e)}get isFocusedRowEnabled(){return this.component.enableFocusedRow}get isMultipleSelectionEnabled(){return this.component.enableMultipleSelection}canSwitchToNestedContentMode(){return!0}handleTabKeyDown(e,t=!1){if(!t&&e.altKey)return!1;if(e.shiftKey){if(this.firstItemSelected)return!1;this.moveToPrevItem()}else{if(this.lastItemSelected)return!1;this.moveToNextItem()}return!0}}class T extends E{constructor(e,t,s){super(t,s),this.lockScroll=0,this.containerStrategy=e}isImmediatelyClickEnabled(){return!1}get preventScrollOnFocus(){return this.lockScroll>0}getVisibleIndex(){return w.getAttributeIntValue(this.targetElement,c.VisibleIndexAttributeName,-1)}leave(){S.removeTabIndex(this.selectedItemElement)}lockScrollOnFocus(){this.lockScroll++}unlockScrollOnFocus(){this.lockScroll--}getShortcutContext(){return{ElementType:this.getElementType(),VisibleIndex:this.getVisibleIndex()}}}class R extends E{constructor(e,t){super(e,t),this.savedVisibleIndex=-1,this.selectedVisibleIndex=-1,this.firstVisibleIndex=-1,this.lastVisibleIndex=-1,this.firstAvailableVisibleIndex=0,this.lastAvailableVisibleIndex=-1,this.requestRestoreSelectedElementIndex=-1,this.boundOnVisibleElementChangedHandler=this.onMakeRowVisible.bind(this),this._rows=[],this.addEventSubscriptions()}get enableVirtualRenderMode(){return this.component.enableVirtualRenderMode}get isFirstItemSelected(){return this.selectedVisibleIndex<=this.firstAvailableVisibleIndex}get isLastItemSelected(){return this.selectedVisibleIndex>=this.lastAvailableVisibleIndex}get totalItemCount(){const e=this.component.virtualItemCount;return e>=0?e:this._rows.length}get preventScrollOnFocus(){return this.enableVirtualRenderMode}updateSelectedItemByChildElement(e,t=null){const s=this.findItemElementIndexByChild(e);if(s<0)return;const i=t instanceof MouseEvent;if(i&&(this.selectedVisibleIndex=d.getElementVisibleIndex(this.items[s])),this.enableVirtualRenderMode)return!i&&s!==this.selectedItemIndex&&this.isElementInsideViewport(this.selectedVisibleIndex)&&(this.requestRestoreSelectedElementIndex=this.selectedVisibleIndex),void this.updateSelectedItemByIndex(s);super.updateSelectedItemByChildElement(e,t)}initialize(){super.initialize(),this._rows=this.queryRows(),this.resetState(),this.updateBoundaries(),this.updateViewportBoundaries()}activate(){if(this.enableVirtualRenderMode)if(this.requestRestoreSelectedElementIndex>-1)this.notifyMakeElementVisible(this.requestRestoreSelectedElementIndex,null),this.requestRestoreSelectedElementIndex=-1;else if(this.selectedVisibleIndex>-1)if(this.isElementInsideViewport(this.selectedVisibleIndex)){const e=d.findItemIndex(this.selectedVisibleIndex,this.items);e>-1&&this.selectViewportItem(e)}else v(this.targetElement);else this.selectViewportItem(this.selectedItemIndex);else super.activate()}onDispose(){this._rows=[],this.resetState(),this.removeEventSubscriptions(),super.onDispose()}getSelectedItemStrategy(){return super.getSelectedItemStrategy()}selectItem(e){const t=this.getSelectedItemStrategy(),s=this.getStrategy(e),i=s.getVisibleIndex();this.selectedVisibleIndex=i,this.savedVisibleIndex=i,x(this.targetElement),super.selectItem(e),t&&t!==s&&t.leave()}handleKeyDown(e){switch(b.KeyUtils.getEventKeyCode(e)){case b.KeyCode.Up:return this.handleArrowUpKeyDown();case b.KeyCode.Down:return this.handleArrowDownKeyDown();case b.KeyCode.PageDown:return this.handlePageDown();case b.KeyCode.PageUp:return this.handlePageUp();case b.KeyCode.Home:return this.handleHomeKeyDown(e)||this.performShortcutEvent(e),!0;case b.KeyCode.End:return this.handleEndKeyDown(e)||this.performShortcutEvent(e),!0;case b.KeyCode.Space:return this.performShortcutEvent(e),!0}return!1}handleKeyUp(e){switch(b.KeyUtils.getEventKeyCode(e)){case b.KeyCode.Down:case b.KeyCode.Up:case b.KeyCode.PageUp:case b.KeyCode.PageDown:case b.KeyCode.Home:case b.KeyCode.End:this.notifyFocusedRowChanged()}return!1}moveToPrevItem(){super.moveToPrevItem(),this.scrollToElement(null,i.None)}moveToNextItem(){super.moveToNextItem(),this.scrollToElement(null,i.None)}selectViewportItem(e){const t=this.getStrategy(e);t.lockScrollOnFocus(),this.selectItem(e),t.unlockScrollOnFocus()}moveToPrevVirtualItem(){let e=this.selectedVisibleIndex-1;0===e&&0===this.selectedItemIndex||(this.isElementInsideViewport(e)&&d.isGroupRow(this,e)&&e--,this.isElementInsideViewport(e)&&this.selectedItemIndex>0?(this.selectViewportItem(this.selectedItemIndex-1),this.scrollToElement(null,i.None)):this.notifyMakeElementVisible(e,n.Top))}moveToNextVirtualItem(){let e=this.selectedVisibleIndex+1;this.isElementInsideViewport(e)&&d.isGroupRow(this,e)&&e++,this.isElementInsideViewport(e)&&this.selectedItemIndex<this.totalItemCount-1?(this.selectViewportItem(this.selectedItemIndex+1),this.scrollToElement(null,i.None)):this.notifyMakeElementVisible(e,n.Bottom)}handleArrowUpKeyDown(){return!this.isFirstItemSelected&&(this.enableVirtualRenderMode?this.moveToPrevVirtualItem():this.moveToPrevItem(),!0)}handleArrowDownKeyDown(){return!this.isLastItemSelected&&(this.enableVirtualRenderMode?this.moveToNextVirtualItem():this.moveToNextItem(),!0)}handleHomeKeyDown(e){return this.savedVisibleIndex=-1,!this.isFirstItemSelected&&(this.enableVirtualRenderMode?this.moveToFirstVirtualItem():this.moveToFirstItem(),!(e.ctrlKey&&e.shiftKey))}moveToFirstItem(){if(this.getScrollViewer()){const e=d.isGroupRow(this,0);this.selectItem(0);const t=e?d.getRow(this,0):this.selectedItemElement;this.scrollToElement(n.Top,i.None,t)}}moveToFirstVirtualItem(){this.isElementInsideViewport(0)&&this.selectedItemIndex>0?(this.selectViewportItem(0),this.scrollToElement(n.Top,i.None)):this.notifyMakeElementVisible(0,n.Top)}handleEndKeyDown(e){return this.savedVisibleIndex=-1,!this.isLastItemSelected&&(this.enableVirtualRenderMode?this.moveToLastVirtualItem():this.moveToLastItem(),!(e.ctrlKey&&e.shiftKey))}moveToLastItem(){this.component.getScrollViewer()&&(this.selectItem(this.itemCount-1),this.scrollToElement(n.Bottom,i.None))}moveToLastVirtualItem(){const e=this.totalItemCount-1;this.isElementInsideViewport(e)&&this.selectedItemIndex<this.itemCount-1?(this.selectViewportItem(this.itemCount-1),this.scrollToElement(n.Bottom,i.None)):this.notifyMakeElementVisible(e,n.Bottom)}handlePageUp(){return this.savedVisibleIndex=-1,!this.isFirstItemSelected&&(this.enableVirtualRenderMode?this.moveToPrevVirtualPageItem():this.moveToPrevPageItem(),!0)}moveToPrevPageItem(){const e=this.component.getScrollViewer();if(e){const t=d.calculateBoundaryItemVisibleIndex(this,e,!1),s=t<0?0:t,o=d.isGroupRow(this,s),r=d.findItemIndex(o?s+1:s,this.items);this.selectItem(r);const l=o?d.getRow(this,s):this.selectedItemElement;this.scrollToElement(n.Top,i.None,l)}}moveToPrevVirtualPageItem(){const e=this.component.getScrollViewer();if(e){const t=d.calculateBoundaryItemVisibleIndex(this,e,!1);if(this.isElementInsideViewport(t)){const e=d.isGroupRow(this,t),s=d.findItemIndex(e?t+1:t,this.items);this.selectViewportItem(s);const o=e?d.getRow(this,t):this.selectedItemElement;this.scrollToElement(n.Top,i.None,o)}else this.notifyMakeElementVisible(t,n.Top)}}handlePageDown(){return this.savedVisibleIndex=-1,!this.isLastItemSelected&&(this.enableVirtualRenderMode?this.moveToNextVirtualPageItem():this.moveToNextPageItem(),!0)}moveToNextPageItem(){const e=this.lastAvailableVisibleIndex,t=this.component.getScrollViewer();if(t){const s=d.calculateBoundaryItemVisibleIndex(this,t,!0),o=s>e?e:s,r=d.isGroupRow(this,o),l=d.findItemIndex(r?o-1:o,this.items);this.selectItem(l);const a=r?d.getRow(this,o):this.selectedItemElement;this.scrollToElement(n.Bottom,i.None,a)}}moveToNextVirtualPageItem(){const e=this.component.getScrollViewer();if(e){const t=d.calculateBoundaryItemVisibleIndex(this,e,!0);if(this.isElementInsideViewport(t)){const e=d.isGroupRow(this,t),s=d.findItemIndex(e?t-1:t,this.items);this.selectViewportItem(s);const o=e?d.getRow(this,t):this.selectedItemElement;this.scrollToElement(n.Bottom,i.None,o)}else this.notifyMakeElementVisible(t,n.Bottom)}}onMakeRowVisible(e){if(e.detail.isFocusRequired){const t=d.getElementVisibleIndex(e.detail.element),s=d.isGroupRow(this,t),i=d.findItemIndex(s?t+1:t,this.items);this.selectItem(i)}}addEventSubscriptions(){const e=this.getScrollViewer();e&&e.addEventListener(o.eventName,this.boundOnVisibleElementChangedHandler)}removeEventSubscriptions(){const e=this.getScrollViewer();e&&e.removeEventListener(o.eventName,this.boundOnVisibleElementChangedHandler)}getStrategy(e){return this.getItemStrategy(e)}notifyFocusedRowChanged(){this.isFocusedRowEnabled&&this.savedVisibleIndex>-1&&(this.component.notifyFocusedRowChanged(this.savedVisibleIndex),this.savedVisibleIndex=-1)}scrollToElement(e,t,s=this.selectedItemElement){const i=this.getScrollViewer();i&&s&&r.scrollToElementRelyOnStickyDescendants(s,e,t,i)}notifyMakeElementVisible(e,t=null){e<0||this.component.notifyMakeElementVisible(e,d.getVerticalEdge(t))}updateBoundaries(){this.firstAvailableVisibleIndex=0,this.lastAvailableVisibleIndex=this.totalItemCount-1}updateViewportBoundaries(){if(0===this._rows.length)return;this.firstVisibleIndex=d.getElementVisibleIndex(this._rows[0]);this.lastVisibleIndex=d.getElementVisibleIndex(this._rows[this._rows.length-1])}queryRows(){return Array.from(this.targetElement.querySelectorAll(m.VisibleItemSelector))}isElementInsideViewport(e){return e>=this.firstVisibleIndex&&e<=this.lastVisibleIndex}resetState(){this.firstAvailableVisibleIndex=-1,this.lastAvailableVisibleIndex=-1,this.firstVisibleIndex=-1,this.lastVisibleIndex=-1}getLastAvailableVisibleIndex(){return this.lastAvailableVisibleIndex}getSelectedVisibleIndex(){return this.selectedVisibleIndex}getItemsContainer(){return this.targetElement}getRows(){return this._rows}}var K,D;!function(e){e[e.Undefined=0]="Undefined",e[e.HeaderRow=1]="HeaderRow",e[e.DataRow=2]="DataRow",e[e.ListItem=3]="ListItem",e[e.EmptyData=4]="EmptyData"}(K||(K={})),function(e){e[e.None=0]="None",e[e.Table=1]="Table",e[e.List=2]="List",e[e.SearchBox=3]="SearchBox"}(D||(D={}));class N{static getElementType(e){return e.matches(m.HeaderRowSelector)?K.HeaderRow:e.matches(m.TableVisibleRowGeneralSelector)?K.DataRow:e.matches(m.ListVisibleItemGeneralSelector)?K.ListItem:K.Undefined}static getNavigationAreaType(e){return e.matches(m.TableSelector)?D.Table:e.matches(m.ListSelector)?D.List:e.matches(m.SearchBoxSelector)?D.SearchBox:D.None}static isSelectionCell(e){var t;return null!==(t=null==e?void 0:e.matches(`.${h.TableSelectionCellClassName}`))&&void 0!==t&&t}static isItemDisplayTemplateContainer(e){var t;return null!==(t=null==e?void 0:e.matches(`.${h.ItemDisplayTemplateContainer}`))&&void 0!==t&&t}}class B extends E{constructor(e,t){super(e,t),this.savedSelectedColumnUid=null}saveSelectedColumn(e){this.savedSelectedColumnUid=e.getSelectedColumnUid()}syncSelectedColumn(e){e.syncSelectedColumn(this.savedSelectedColumnUid)}queryItems(){return this.queryItemsBySelector(m.TablePartsSelector)}createItemStrategy(e){return e.matches("thead")?new k(this,this.component,e):e.matches("tbody")?new M(this,this.component,e):null}selectItem(e){const t=this.getStrategy(e),s=this.getSelectedItemStrategy();s&&s!==t&&s.leaveTablePart(),t.activate()}handleKeyDown(e){switch(b.KeyUtils.getEventKeyCode(e)){case b.KeyCode.PageUp:case b.KeyCode.PageDown:case b.KeyCode.Home:case b.KeyCode.End:return this.handleBodyShortcut(e)}return!1}handleBodyShortcut(e){const t=this.items.findIndex((e=>e.matches("tbody"))),s=this.getStrategy(t);return!!(t>=0&&s&&s!==this.getSelectedItemStrategy())&&s.handleKeyDown(e)}getStrategy(e){return this.getItemStrategy(e)}syncSelectedColumns(e){const t=this.getSelectedItemStrategy();if(t){const s=t.getSelectedItemStrategy(),i=e.getSelectedItemStrategy();s&&i&&e.syncSelectedColumns(s,i)}}}class j extends R{constructor(e,t,s){super(t,s),this.parentTableStrategy=e}leaveTablePart(){}syncSelectedColumns(e,t){return!(!e||e===t||(this.isColumnSyncRequired(e.getElementType())&&this.parentTableStrategy.saveSelectedColumn(e),!this.isColumnSyncRequired(t.getElementType())))&&(this.parentTableStrategy.syncSelectedColumn(t),!0)}queryItems(){return this.queryItemsBySelector(m.TableDataRowSelector)}isColumnSyncRequired(e){switch(e){case K.HeaderRow:case K.DataRow:return!0}return!1}getShortcutContext(){return{AreaType:D.Table}}getStrategy(e){return this.getItemStrategy(e)}getSelectedItemStrategy(){return super.getSelectedItemStrategy()}}class k extends j{constructor(e,t,s){super(e,t,s)}createItemStrategy(e){if(N.getElementType(e)===K.HeaderRow)return new U(this,this.component,e);throw new Error("Not implemented")}}class M extends j{constructor(e,t,s){super(e,t,s)}createItemStrategy(e){return e.matches(m.TableVisibleRowGeneralSelector)?new L(this,this.component,e):e.matches(m.EmptyDataItemSelector)?new F(this,this.component,e):null}leaveTablePart(){super.leaveTablePart(),this.notifyFocusedRowChanged()}}class P extends T{constructor(e,t,s){super(e,t,s)}getSelectedColumnUid(){var e;return null!==(e=this.getColumnUidByCell(this.selectedItemElement))&&void 0!==e?e:null}syncSelectedColumn(e){const t=null!==e?this.items.findIndex((t=>this.getColumnUidByCell(t)===e)):-1;this.selectedItemIndex=Math.max(t,0)}getColumnUidByCell(e){var t;return null===(t=this.getColumnInfoByCell(e))||void 0===t?void 0:t.uID}}class U extends P{constructor(e,t,s){super(e,t,s)}getElementType(){return K.HeaderRow}isImmediatelyClickEnabled(){return!0}}class L extends P{constructor(e,t,s){super(e,t,s)}getElementType(){return K.DataRow}handleKeyDown(e){return this.nestedContentSelected||b.KeyUtils.getEventKeyCode(e)!==b.KeyCode.Space?super.handleKeyDown(e):(this.performShortcutEvent(e),!0)}}class F extends P{constructor(e,t,s){super(e,t,s)}getElementType(){return K.EmptyData}}class A extends R{constructor(e,t){super(e,t)}getShortcutContext(){return{AreaType:D.List}}queryItems(){return this.queryItemsBySelector(m.ItemSelector)}createItemStrategy(e){return e.matches(m.ListVisibleItemGeneralSelector)?new H(this,this.component,e):e.matches(m.EmptyDataItemSelector)?new q(this,this.component,e):null}}class H extends T{constructor(e,t,s){super(e,t,s)}getElementType(){return K.ListItem}handleKeyDown(e){return this.nestedContentSelected||b.KeyUtils.getEventKeyCode(e)!==b.KeyCode.Space?super.handleKeyDown(e):(this.performShortcutEvent(e),!0)}}class q extends T{constructor(e,t,s){super(e,t,s)}getElementType(){return K.EmptyData}}class O extends E{constructor(e,t){super(e,t)}}class _ extends E{constructor(e){super(e,e)}getShortcutContext(){return{IsMacOSPlatform:this.isMacOSPlatform}}queryItems(){return new Array(this.getSearchBox(),this.getList(),this.getTable())}createItemStrategy(e){switch(N.getNavigationAreaType(e)){case D.SearchBox:return new O(this.component,e);case D.Table:return new B(this.component,e);case D.List:return new A(this.component,e)}return null}moveToPrevItem(){this.selectedItemIndex>0?super.moveToPrevItem():this.leaveBackward()}moveToNextItem(){this.selectedItemIndex<this.itemCount-1?super.moveToNextItem():this.leaveForward()}handleKeyDown(e){const t=b.KeyUtils.getEventKeyCode(e);return t===b.KeyCode.Tab?this.handleTabKeyDown(e):t===b.KeyCode.Up&&e.ctrlKey?(this.moveToPrevItem(),!0):!(t!==b.KeyCode.Down||!e.ctrlKey)&&(this.moveToNextItem(),!0)}handleTabKeyDown(e){return super.handleTabKeyDown(e,e.altKey)||(e.shiftKey?this.leaveBackward():this.leaveForward()),!0}}class z{constructor(e){this._uID=e}get uID(){return this._uID}}let G=class extends t{constructor(){super(),this._focusedRowIndex=-1,this._columnsInfo=[],this.boundOnScrollViewerUpdateHandler=this.onScrollViewerUpdate.bind(this),this.boundOnMouseDownHandler=this.onMouseDown.bind(this),this.resizeObserver=new ResizeObserver(this.onSizeChanged.bind(this))}connectedCallback(){super.connectedCallback(),this.addEventSubscriptions(),this.resizeObserver.observe(this)}disconnectedCallback(){delete this.keyboardNavigator,this.removeEventSubscriptions(),this.resizeObserver.disconnect(),super.disconnectedCallback()}contentChanged(){super.contentChanged(),this.initializeKeyboardNavigator()}getScrollViewer(){return this.querySelector(`.${a.ClassName}`)}getScrollViewerContent(){return this.querySelector(`.${a.ContentContainerClassName}`)}getTable(){return this.querySelector(m.TableSelector)}getList(){return this.querySelector(m.ListSelector)}getSearchBox(){return this.querySelector(m.SearchBoxSelector)}getKeyboardNavigator(){return this.keyboardNavigator}notifyFocusedRowChanged(e){e!==this.focusedRowIndex&&this.enableFocusedRow&&(this.focusedRowIndex=e,this.dispatchEvent(new u(e)))}notifyMakeElementVisible(e,t){e<0||this.dispatchEvent(new I(e,t))}notifyColumnsChanged(e){this._columnsInfo=e}getColumnInfoByCell(e){if(e instanceof HTMLElement){const t=this.getElementIndex(e);if(t>=0&&t<this._columnsInfo.length)return this._columnsInfo[t]}return null}get isTableRender(){return!!this.getTable()}get focusedRowIndex(){return this._focusedRowIndex}set focusedRowIndex(e){this._focusedRowIndex=e}get enableFocusedRow(){return!1}get enableMultipleSelection(){return this.classList.contains(h.MultiSelectClassName)}get enableVirtualRenderMode(){const e=this.getScrollViewer();return null!==e&&e instanceof f}get virtualItemCount(){const e=this.getScrollViewer();return e&&e instanceof f?e.totalItemCount:-1}initializeKeyboardNavigator(){this.keyboardNavigator=this.querySelector(m.KeyboardNavigatorSelector),this.keyboardNavigator&&!this.keyboardNavigator.initialized&&this.keyboardNavigator.initialize(this,new _(this))}addEventSubscriptions(){this.addEventListener(l.eventName,this.boundOnScrollViewerUpdateHandler),g.Browser.Firefox&&this.isTableRender&&this.addEventListener("mousedown",this.boundOnMouseDownHandler)}removeEventSubscriptions(){this.removeEventListener(l.eventName,this.boundOnScrollViewerUpdateHandler),g.Browser.Firefox&&this.isTableRender&&this.removeEventListener("mousedown",this.boundOnMouseDownHandler)}onScrollViewerUpdate(e){const t=this.getScrollViewerContent(),s=this.getTable();t&&s&&(s.offsetHeight<t.clientHeight?p.DomUtils.addClassName(s,h.TableNoScrollClassName):p.DomUtils.removeClassName(s,h.TableNoScrollClassName)),e.stopPropagation()}onSizeChanged(e){const t=this.getScrollViewer();t&&t.refreshUI&&t.refreshUI()}onMouseDown(e){e.ctrlKey&&e.preventDefault()}getElementIndex(e){if(!e)return-1;if(e instanceof HTMLTableCellElement)return e.cellIndex;if(e&&e.parentElement)for(let t=0;t<e.parentElement.children.length;t++)if(e.parentElement.children[t]===e)return t;return-1}};G=e([C(s)],G),customElements.define("dxbl-list-box-columns-info",class extends V{get data(){return this.getAttribute("data")}get componentClassName(){return this.getAttribute("component-class-name")}get columns(){return null!==this.data?this.parseColumns(this.data):[]}constructor(){super()}connectedCallback(){var e;super.connectedCallback(),this.component=this.closest(`.${this.componentClassName}`),null===(e=this.component)||void 0===e||e.notifyColumnsChanged(this.columns)}parseColumns(e){const t=JSON.parse(e),s=[];for(let e=0;e<t.length;e++){const i=new z(t[e].UID);s.push(i)}return s}static get observedAttributes(){return["data"]}attributeChangedCallback(e,t,s){var i;if("data"===e)null===(i=this.component)||void 0===i||i.notifyColumnsChanged(this.parseColumns(s))}});const $={loadModule:function(){}};export{G as DxListBox,$ as default};
