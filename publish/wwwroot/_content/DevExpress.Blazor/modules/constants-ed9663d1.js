import{C as t}from"./css-classes-c63af734.js";class l{}l.Prefix=t.Prefix+"-tabs",l.Item=l.Prefix+"-item",l.Content=l.Prefix+"-content",l.TabList=l.Prefix+"-tablist",l.ContentPanel=l.Content+"-panel",l.<PERSON>=l.<PERSON>+"-scrollable",l.<PERSON>=l.Tab<PERSON>ist+"-has-overflow",l.ButtonScroll=l.Prefix+"-scroll-btn",l.ButtonScrollNext=l.ButtonScroll+"-next",l.ButtonScrollPrev=l.But<PERSON>roll+"-prev";class e{}e.Ribbon=t.Prefix+"-ribbon";export{e as R,l as T};
