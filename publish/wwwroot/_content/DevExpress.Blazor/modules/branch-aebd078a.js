import{_ as e}from"./tslib.es6-d65164b3.js";import{n as t}from"./property-4ec0b52d.js";import{e as r}from"./custom-element-267f9a21.js";import{s as n}from"./lit-element-462e7ad3.js";const s="dxbl-branch";class a{get parentId(){return this._parentId}get id(){return this._id}get type(){return this._type}constructor(e,t,r){this._id=e,this._parentId=t,this._type=r}}class c{get id(){return this._id}constructor(e){this._id=e}}class d extends CustomEvent{constructor(e){super(d.eventName,{detail:e,bubbles:!0,composed:!0})}}d.eventName="dxbranchrefreshed";class i extends CustomEvent{constructor(e){super(i.eventName,{detail:e,bubbles:!0,composed:!0})}}i.eventName="dxbranchactivated";class o extends CustomEvent{constructor(e){super(o.eventName,{detail:e,bubbles:!0,composed:!0})}}o.eventName="dxbranchupdated";class h extends CustomEvent{constructor(e){super(h.eventName,{detail:e,bubbles:!0,composed:!0})}}h.eventName="dxbranchremoved";class p extends CustomEvent{constructor(e){super(p.eventName,{detail:e,bubbles:!0,composed:!0})}}p.eventName="dxbranchcreated";let b=class extends n{constructor(){super(...arguments),this.parentBranchId=null}createRenderRoot(){return this}updated(e){this.branchId&&(e.get("branchId")||e.get("parentBranchId")||e.get("branchType"))&&this.raiseUpdated()}raiseUpdated(){const e=new a(this.branchId,this.parentBranchId,this.branchType),t=new o(e);this.dispatchEvent(t)}connectedCallback(){super.connectedCallback(),this.raiseCreated()}raiseCreated(){if(!this.branchId)return;const e=new a(this.branchId,this.parentBranchId,this.branchType),t=new p(e);this.dispatchEvent(t)}disconnectedCallback(){super.disconnectedCallback(),this.raiseRemoved()}raiseRemoved(){if(!this.branchId)return;const e=new a(this.branchId,this.parentBranchId,this.branchType),t=new h(e);this.dispatchEvent(t)}};function u(){}e([t({type:String,attribute:"branch-id"})],b.prototype,"branchId",void 0),e([t({type:String,attribute:"parent-branch-id"})],b.prototype,"parentBranchId",void 0),e([t({type:String,attribute:"type"})],b.prototype,"branchType",void 0),b=e([r(s)],b);const l={init:u,DxBranch:b,dxBranchTagName:s,BranchUpdatedEvent:o};export{i as BranchActivatedEvent,p as BranchCreatedEvent,c as BranchIdContext,d as BranchRefreshedEvent,h as BranchRemovedEvent,o as BranchUpdatedEvent,l as default,s as dxBranchTagName,u as init};
