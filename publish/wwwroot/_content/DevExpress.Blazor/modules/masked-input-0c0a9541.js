import{_ as e}from"./tslib.es6-d65164b3.js";import{b as t}from"./browser-3fc721b7.js";import{E as s}from"./eventhelper-8bcec49f.js";import{m as i}from"./constants-da6cacac.js";import{F as n,B as o,D as r,a as l}from"./text-editor-733d5e56.js";import{D as a,a as c}from"./input-66769c52.js";import{C as d}from"./custom-events-helper-e7f279d3.js";import{k as p}from"./key-ffa272aa.js";import{n as u}from"./property-4ec0b52d.js";import"./_commonjsHelpers-41cdd1e7.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./css-classes-c63af734.js";import"./devices-17b9ba08.js";import"./dom-554d0cc7.js";import"./common-48ec40e2.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./focushelper-2eea96ca.js";import"./dom-utils-d057dcaa.js";import"./custom-element-267f9a21.js";class h{constructor(e){this.deltaY=e}}var m,y;!function(e){e[e.forward=0]="forward",e[e.backward=1]="backward"}(m||(m={}));class f{constructor(e,t,s,i){this.direction=m.backward,this.selectionStart=e,this.selectionEnd=t,this.selectAll=i,this.direction=s}}class b extends Event{constructor(){super(b.eventName,{bubbles:!0,composed:!0,cancelable:!0})}}b.eventName=i+".applyValue";class v extends CustomEvent{constructor(e){super(v.eventName,{detail:new n(e,!1),bubbles:!0,composed:!0,cancelable:!0})}}v.eventName=i+".autofill";class S extends CustomEvent{constructor(e){super(S.eventName,{detail:new h(e),bubbles:!0,composed:!0,cancelable:!0})}}S.eventName=i+".wheel";class w extends CustomEvent{constructor(e,t,s,i){super(w.eventName,{detail:new f(e,t,s,i),bubbles:!0,composed:!0,cancelable:!0})}}w.eventName=i+".selectionchange";class E extends CustomEvent{constructor(e){super(E.eventName,{detail:e,bubbles:!0,composed:!0,cancelable:!0})}}E.eventName=i+".compositionEnd",d.register(w.eventName,(e=>e.detail)),d.register(v.eventName,(e=>e.detail)),d.register(b.eventName,(e=>e.detail)),d.register(S.eventName,(e=>e.detail)),d.register(E.eventName,(e=>({Text:e.detail.data}))),function(e){e.syncSelectionStart="sync-selection-start",e.syncSelectionEnd="sync-selection-end",e.isMaskDefined="is-mask-defined"}(y||(y={}));const D={...y,...a};class k extends c{constructor(){super(...arguments),this.wheelTimerId=-1,this._shouldApplySelectionOnFocus=!0,this.compositionProcessing=!1,this.compositionEndHandler=this.handleCompositionEnd.bind(this),this.pointerUpHandler=this.handlePointerUp.bind(this),this.clickHandler=this.handleClick.bind(this),this.wheelHandler=this.handleWheel.bind(this),this.syncSelectionStart=null,this.syncSelectionEnd=null,this.isMaskDefined=!1}disconnectedCallback(){super.disconnectedCallback()}get shouldProcessFocusOut(){return!0}get shouldProcessWheel(){return this.isMaskDefined}get shouldProcessFocusIn(){return!0}get shouldApplySelectionOnFocus(){return this._shouldApplySelectionOnFocus}set shouldApplySelectionOnFocus(e){this._shouldApplySelectionOnFocus=e}get inputSelectionDirection(){var e;return"backward"===(null===(e=this.inputElement)||void 0===e?void 0:e.selectionDirection)?m.backward:m.forward}handlePointerUp(e){this.allowInput&&this.isMaskDefined&&this.processPointerUp(e)}handleCompositionEnd(e){this.dispatchEvent(new E(e)),this.inputElement.removeEventListener("compositionend",this.compositionEndHandler),this.compositionProcessing=!1}handleClick(e){this.applyInputSelection()}handleWheel(e){if(this.shouldProcessWheel&&(e.preventDefault(),this.processWheel(e),this.bindValueMode!==o.OnInput)){-1!==this.wheelTimerId&&clearTimeout(this.wheelTimerId);this.wheelTimerId=window.setTimeout((()=>{this.bindValueMode===o.OnDelayedInput?this.applyDefferedValue():this.raiseApplyValue()}).bind(this),this.bindValueMode===o.OnDelayedInput?Math.max(r,this.fieldInputDelay):r)}}processWheel(e){this.dispatchEvent(new S(e.deltaY))}applyTextPropertyCore(){super.applyTextPropertyCore(),this.applyMaskManagerSelection()}applyMaskManagerSelection(){!this.isMaskDefined||!this.focused&&(t.Browser.MacOSPlatform||t.Browser.MacOSMobilePlatform)||this.inputSelectionStart===this.syncSelectionStart&&this.inputSelectionEnd===this.syncSelectionEnd||this.selectInputText(this.syncSelectionStart,this.syncSelectionEnd)}get shouldProcessFieldTextVersion(){return!this.isMaskDefined&&super.shouldProcessFieldTextVersion}get shouldRaiseFieldTextEvents(){return!this.isMaskDefined}processFocusIn(){var e;super.processFocusIn(),this.allowInput&&this.isMaskDefined&&this.shouldApplySelectionOnFocus&&this.applyInputSelection(),this.addEventListener("wheel",this.wheelHandler,{capture:!0,passive:!1}),t.Browser.WebKitTouchUI&&(null===(e=this.inputElement)||void 0===e||e.addEventListener("click",this.clickHandler))}processFocusOut(e){var s;super.processFocusOut(e),this.removeEventListener("wheel",this.wheelHandler,{capture:!0}),t.Browser.WebKitTouchUI&&(null===(s=this.inputElement)||void 0===s||s.removeEventListener("click",this.clickHandler))}processKeyDown(e){return super.processKeyDown(e),!!this.shouldProcessKeyDown(e)&&(s.markHandled(e),this.raiseKeyDown(e),!0)}processPointerDown(e){return super.processPointerDown(e),this.isMaskDefined&&!this.focused&&(this.shouldApplySelectionOnFocus=!1),s.containsInComposedPath(e,(e=>e===this.inputElement))&&document.addEventListener(k.pointerUpEventType,this.pointerUpHandler),!0}processKeyUp(e){if(!this.inputElement||!s.containsInComposedPath(e,(e=>e===this.inputElement)))return!1;const t=this.querySelector("input:-webkit-autofill");return this.isMaskDefined&&t&&t.value&&!this.isOpenDropDownShortcut(e)&&(this.dispatchEvent(new v(t.value)),s.markHandled(e)),this.shouldProcessKeyUp(e)&&(this.bindValueMode===o.OnLostFocus?this.raiseApplyValue():this.applyDefferedValue()),!0}applyDefferedValue(){this.bindValueMode===o.OnDelayedInput&&this.inputDelayDeferredAction.execute((()=>this.raiseApplyValue()))}raiseApplyValue(){this.dispatchEvent(new b)}processBeforeInput(e){return!!super.processBeforeInput(e)||!!this.isMaskDefined&&(e.isComposing&&!t.Browser.AndroidMobilePlatform?(this.compositionProcessing||(this.inputElement.addEventListener("compositionend",this.compositionEndHandler),this.compositionProcessing=!0),!0):(s.markHandled(e),this.shouldProcessBeforeInput(e)&&(this.applyInputSelection(),this.dispatchEvent(new l(e)),this.applyDefferedValue()),!0))}updated(e){super.updated(e)}shouldProcessKeyUp(e){if(!this.isMaskDefined)return!1;switch(p.KeyUtils.getEventKeyCode(e)){case p.KeyCode.Up:case p.KeyCode.Down:case p.KeyCode.Enter:return!0;case p.KeyCode.Key_z:return e.ctrlKey}return!1}shouldProcessKeyDown(e){if(!this.isMaskDefined)return!1;switch(p.KeyUtils.getEventKeyCode(e)){case p.KeyCode.Left:case p.KeyCode.Right:case p.KeyCode.Up:case p.KeyCode.Down:case p.KeyCode.Home:case p.KeyCode.Delete:case p.KeyCode.End:return!0;case p.KeyCode.Key_a:case p.KeyCode.Key_z:return e.ctrlKey}return!1}shouldProcessBeforeInput(e){switch(e.inputType){case"insertText":case"insertReplacementText":case"insertFromPaste":return null!==e.data&&e.data.length>0;case"deleteContentBackward":case"deleteContentForward":case"deleteByCut":return!0;case"insertCompositionText":return t.Browser.AndroidMobilePlatform}return!1}isOpenDropDownShortcut(e){return p.KeyUtils.getEventKeyCode(e)===p.KeyCode.Down&&e.altKey}selectAll(){super.selectAll(),this.focused&&this.applyInputSelection()}processPointerUp(e){return this.applyInputSelection(),this.shouldApplySelectionOnFocus&&(this.shouldApplySelectionOnFocus=!0),document.removeEventListener(k.pointerUpEventType,this.pointerUpHandler),!0}applyInputSelection(){if(this.isMaskDefined&&(this.syncSelectionStart!==this.inputSelectionStart||this.syncSelectionEnd!==this.inputSelectionEnd)){const e=this.fieldElementValue.length===Math.abs(this.inputSelectionEnd-this.inputSelectionStart);this.raiseMaskSelectionChanged(this.inputSelectionStart,this.inputSelectionEnd,this.inputSelectionDirection,e)}}raiseMaskSelectionChanged(e,t,s,i){this.dispatchEvent(new w(e,t,s,i))}}k.pointerUpEventType=t.Browser.WebKitTouchUI?"touchend":"pointerup",e([u({type:Number,attribute:D.syncSelectionStart})],k.prototype,"syncSelectionStart",void 0),e([u({type:Number,attribute:D.syncSelectionEnd})],k.prototype,"syncSelectionEnd",void 0),e([u({type:Boolean,attribute:D.isMaskDefined})],k.prototype,"isMaskDefined",void 0),customElements.define(i,k);const C={loadModule:function(){}};export{k as DxMaskedInputEditor,D as DxMaskedInputEditorAttributes,C as default};
