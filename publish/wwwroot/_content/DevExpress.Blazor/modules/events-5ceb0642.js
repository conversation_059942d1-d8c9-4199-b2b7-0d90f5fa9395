import{C as e}from"./custom-events-helper-e7f279d3.js";class t{constructor(e){this.items=e}}class s extends CustomEvent{constructor(e){super(s.eventName,{detail:new t(e),bubbles:!0})}}s.eventName="dxbl-toolbar.update",e.register(s.eventName,(e=>e.detail));class a{constructor(e){this.state=e}}class o extends CustomEvent{constructor(e){super(o.eventName,{detail:new a(e),bubbles:!0,composed:!0})}}o.eventName="dxbl:toolbar-group.state-change",e.register(o.eventName,(e=>e.detail));class r{constructor(e){this.item=e}}class n extends CustomEvent{constructor(e){super(n.eventName,{detail:new r(e),bubbles:!0})}}n.eventName="dxbl-toolbar-item.update";class c{constructor(e,t=!1){this.size=e,this.force=t}}class l extends CustomEvent{constructor(e,t=!1){super(l.eventName,{detail:new c(e,t),bubbles:!0})}}l.eventName="dxbl-toolbar-item.resized";class b extends CustomEvent{constructor(e){super(b.eventName,{detail:{visible:e},bubbles:!0})}}b.eventName="dxbl-toolbar-item.visibility-changed";class u{constructor(e){this.item=e}}class i extends CustomEvent{constructor(e){super(i.eventName,{detail:new u(e),bubbles:!0,composed:!0})}}i.eventName="dxbl-toolbar-item.ordered";export{o as T,s as a,l as b,n as c,b as d,i as e};
