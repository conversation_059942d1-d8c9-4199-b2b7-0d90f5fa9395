import{_ as e}from"./tslib.es6-d65164b3.js";import{C as t}from"./custom-events-helper-e7f279d3.js";import{D as s,P as i,c as n,a as r,H as a,L as o}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{S as l}from"./single-slot-element-base-01d93921.js";import{dxTabsTag as d,TabsScrollMode as g}from"./ribbon-tabs-1043ab2f.js";import{D as m}from"./dragging-helper-863a69d5.js";import{C as h}from"./css-classes-c63af734.js";import{e as c}from"./custom-element-267f9a21.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./eventhelper-8bcec49f.js";import"./constants-7c047c0d.js";import"./devices-17b9ba08.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./property-4ec0b52d.js";import"./scroll-viewer-css-classes-e724f203.js";import"./toolbar-css-classes-d303c118.js";import"./constants-ed9663d1.js";import"./dom-utils-d057dcaa.js";import"./ribbon-utils-2b6a64cb.js";import"./focus-utils-ae044224.js";import"./key-ffa272aa.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./events-f873e646.js";var p;let b=p=class extends l{constructor(){super(...arguments),this.boundOnDragStartHandler=this.handleDragStart.bind(this),this.boundOnDragStopHandler=this.handleDrop.bind(this),this.boundOnDragCancelHandler=this.handleDragEnd.bind(this),this._pointerEventsHelper=new s(this)}handleDragStart(e){p.draggingHelper.start(this,e.detail.source),e.stopPropagation()}handleDragEnd(e){p.draggingHelper.cancel(),e.stopPropagation()}handleDrop(e){if(p.draggingHelper.isValidTarget(this)){const e=p.draggingHelper.getDragIndex(),t=p.draggingHelper.getDropIndex();e!==t&&this.dispatchEvent(new u(e,t))}p.draggingHelper.stop(),e.stopPropagation()}isDraggableTab(){return this.tabs.allowTabReorder}get tabs(){return this.closest(d)}connectedCallback(){super.connectedCallback(),this.isDraggableTab()&&this.addEventSubscriptions(),this._pointerEventsHelper.initialize()}disconnectedCallback(){this.removeEventSubscriptions(),super.disconnectedCallback()}addEventSubscriptions(){this.addEventListener(i.eventName,this.boundOnDragStartHandler),this.addEventListener(n.eventName,this.boundOnDragCancelHandler),this.addEventListener(r.eventName,this.boundOnDragStopHandler)}removeEventSubscriptions(){this.removeEventListener(i.eventName,this.boundOnDragStartHandler),this.removeEventListener(n.eventName,this.boundOnDragCancelHandler),this.removeEventListener(r.eventName,this.boundOnDragStopHandler)}get handlePointerEventsMode(){return a.Dragging}get handlePointerEventsTarget(){return this}get handlePointerEventsDelay(){return o}get hoverTitleElementsSelector(){return null}get bypassNonInlineHoverTitleElementChildSelector(){return null}shouldProcessPointerEvent(e){return!e.target.closest(".dxbl-tabs-close-button")}};b.draggingHelper=new class extends m{constructor(){super(...arguments),this.spacingElement=null,this.dragIndex=-1,this.dropIndex=-1,this._tabs=null,this._tabsUl=null,this.tabElements=null}start(e,t){super.start(e,t),this._tabs=e.closest(d),this._tabsUl=e.closest("ul"),this.tabElements=Array.from(this._tabsUl.querySelectorAll(":scope > li > .dxbl-tabs-item")),this.dragIndex=this.tabElements.indexOf(e),this.dropIndex=this.dragIndex,this.spacingElement=this.addSpacingElement(e),this.addDraggingState(),document.body.classList.add(h.GrabbingCursor)}stop(){const e=this.draggableContext.srcElement.parentElement;e.remove(),this.removeSpacingElement(),this._tabsUl.insertBefore(e,this._tabsUl.children[this.dropIndex]),this.resetState()}cancel(){this.removeSpacingElement(),this.resetState()}resetState(){var e;super.stop(),this.dropIndex=-1,this.dragIndex=-1,null===(e=this.tabElements)||void 0===e||e.forEach((e=>this.clearAnimationStyles(e))),this.tabElements=null,this.removeDraggingState(),this._tabsUl=null,document.body.classList.remove(h.GrabbingCursor)}isValidTarget(e){return this._tabs===e.tabs}getDragIndex(){return Number(this.tabElements[this.dragIndex].getAttribute("index"))}getDropIndex(){return Number(this.tabElements[this.dropIndex].getAttribute("index"))}addSpacingElement(e){const{width:t,height:s}=e.getBoundingClientRect(),i=document.createElement("li");return i.style.minWidth=`${t}px`,i.style.minHeight=`${s}px`,i.classList.add("dxbl-invisible"),e.parentElement?(e.parentElement.insertBefore(i,e),i):null}removeSpacingElement(){var e;null===(e=this.spacingElement)||void 0===e||e.remove(),this.spacingElement=null}clearAnimationStyles(e){e.style.transform="",e.classList.remove("dxbl-tabs-item-drag-target")}addDraggingState(){this._tabs.classList.add("dxbl-tabs-state-dragging")}removeDraggingState(){this._tabs.classList.remove("dxbl-tabs-state-dragging")}getDraggableElement(e){const t=e.getBoundingClientRect();return e.style.position="fixed",e.style.top=`${t.top}px`,e.style.left=`${t.left}px`,e.classList.add("dxbl-state-dragging"),e}hideDraggableElement(){var e;super.hideDraggableElement();const t=null===(e=this.draggableContext)||void 0===e?void 0:e.dragableElement;t&&(t.style.position="",t.style.top="",t.style.left="",t.classList.remove("dxbl-state-dragging"))}onDocumentMouseMove(e){var t,s,i;super.onDocumentMouseMove(e);const n=e.target.closest(".dxbl-tabs-item");if(!n||!this.isValidTarget(n))return;const r=this.tabElements.indexOf(n);let a,o;const l=n.getBoundingClientRect(),d=null===(t=this._tabs)||void 0===t?void 0:t.scrollMode;(null===(s=this._tabs)||void 0===s?void 0:s.isHorizontal)?(o=l.x+l.width/2,a=e.clientX):(o=l.y+l.height/2,a=e.clientY);const m=n.parentElement,h=m.parentElement,c=this.dropIndex;if(a>o?(d===g.NoScroll&&h.insertBefore(this.spacingElement,m.nextSibling),this.dropIndex=r+1):(d===g.NoScroll&&h.insertBefore(this.spacingElement,m),this.dropIndex=r),this.dragIndex<this.dropIndex&&(this.dropIndex=this.dropIndex-1),d!==g.NoScroll){const e=(null===(i=this._tabs)||void 0===i?void 0:i.isHorizontal)?this.draggableContext.dragableElement.offsetWidth:this.draggableContext.dragableElement.offsetHeight;this.moveTabs(this.tabElements,this.dragIndex,c,this.dropIndex,e)}}moveTabs(e,t,s,i,n){const r=Math.max(t,Math.max(s,i)),a=Math.min(t,Math.min(s,i));if(i===a)for(let r=i;r<Math.min(s,t);++r)this.move(e[r],n);if(i===r)for(let r=Math.max(s,t+1);r<=i;++r)this.move(e[r],-n);if(s===a)for(let n=s;n<Math.min(i,t);++n)this.restorePosition(e[n]);if(s===r)for(let n=Math.max(t,i)+1;n<=s;++n)this.restorePosition(e[n])}move(e,t){var s;e.classList.add("dxbl-tabs-item-drag-target"),e.style.transform=(null===(s=this._tabs)||void 0===s?void 0:s.isHorizontal)?`translate(${t}px, 0px)`:`translate(0px, ${t}px)`}restorePosition(e){e.style.transform=""}updateDraggableElementPosition(){if(super.updateDraggableElementPosition(),!this.draggableContext)return;const e=this.draggableContext;e.draggableElementPosition={x:e.draggableElementPosition.x-e.initialCursorPosition.x,y:e.draggableElementPosition.y-e.initialCursorPosition.y}}},b=p=e([c("dxbl-tab-item")],b);class u extends CustomEvent{constructor(e,t){super(u.eventName,{detail:new v(e,t),bubbles:!0,composed:!0,cancelable:!0})}}u.eventName="dxbl-tab-reorder";class v{constructor(e,t){this.fromIndex=e,this.toIndex=t}}t.register(u.eventName,(e=>e.detail));export{b as DxTabItem,u as DxTabReorderEvent,v as OnTabReorderContext};
