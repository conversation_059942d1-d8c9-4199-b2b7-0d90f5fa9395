import{_ as t}from"./tslib.es6-d65164b3.js";import{DxDropDownBase as e}from"./dropdown-f5b2318c.js";import{p as i,i as o,e as n,j as s,k as r,B as l,l as a,C as h,f as c,a as p,b as d,P as u,Z as m,m as w}from"./popup-355ecaa4.js";import{r as g,D as b}from"./dropdowncomponents-3d8f06da.js";import{P as f}from"./point-e4ec110e.js";import{c as y,b as j,e as C}from"./constants-a4904a3f.js";import{x as v}from"./lit-element-462e7ad3.js";import{n as x}from"./property-4ec0b52d.js";import{e as A}from"./custom-element-267f9a21.js";import"./thumb-31d768d7.js";import"./data-qa-utils-8be7c726.js";import"./eventhelper-8bcec49f.js";import"./browser-3fc721b7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./layouthelper-67dd777a.js";import"./constants-7c047c0d.js";import"./custom-events-helper-e7f279d3.js";import"./query-44b9267f.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./logicaltreehelper-67db40f1.js";import"./portal-b3727c25.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./common-48ec40e2.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./key-ffa272aa.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./dom-utils-d057dcaa.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./popupportal-bbd2fea0.js";import"./events-interseptor-a522582a.js";function S(){return new Promise(((t,e)=>{requestAnimationFrame((()=>{setTimeout(t)}))}))}var z,P,T;!function(t){t.Center="Center",t.Left="Left",t.Right="Right"}(P||(P={})),function(t){t.Top="Top",t.Center="Center",t.Bottom="Bottom"}(T||(T={}));let O=class extends o{constructor(){super(...arguments),this.allowResize=!1}get header(){return this.querySelector(".dxbl-window-header")}render(){return this.allowResize?v`<dxbl-popup-resizable-container><slot></slot></dxbl-popup-resizable-container>`:v`<slot></slot>`}};t([x({type:Boolean,attribute:"allow-resize",reflect:!1})],O.prototype,"allowResize",void 0),O=t([A("dxbl-window-dialog")],O);let R=class extends b{constructor(){super()}};R=t([A(y)],R);let D=z=class extends e{constructor(){super(),this.actualSize=null,this.actualPosition=null,this.initialSize=null,this.initialPosition=null,this.actualMinWidth=null,this.actualMaxWidth=null,this.actualMinHeight=null,this.actualMaxHeight=null,this.sizeDirty=!1,this.positionYDirty=!1,this.positionXDirty=!1,this.showAnchorElement=null,this._showOptionsToApply=null,this.childContentObserver=null,this.isAdjustDialogEnabled=!0,this.horizontalAlignment=P.Center,this.verticalAlignment=T.Center,this.closeOnEscape=!1,this.placement=n.Custom,this.closeMode=s.None,r(this)}get branchType(){return l.Window}get canAdjustZIndexOnShow(){return!1}get positionY(){return this.offset.y}set positionY(t){this.positionYDirty=!0,this.offset=new f(this.positionX||0,t||0)}get positionX(){return this.offset.x}set positionX(t){this.positionXDirty=!0,this.offset=new f(t||0,this.positionY||0)}get showOptionsToApply(){return this._showOptionsToApply}set showOptionsToApply(t){this._showOptionsToApply=t,this.positionXDirty=!1,this.positionYDirty=!1}move(t){return this.showOptionsToApply=t,this.forceReposition(),S()}showAt(t){return this.showOptionsToApply=t,this.observeChildContent(this),super.show(),S()}show(){this.showAt(this.showOptionsToApply).catch((t=>{}))}observeChildContent(t){var e;null===(e=this.childContentObserver)||void 0===e||e.disconnect(),this.contentLoaded()?this.enableVisibility():(this.childContentObserver=new MutationObserver(this.handleChildContentChanged.bind(this)),this.childContentObserver.observe(t,{childList:!0}))}handleChildContentChanged(t){var e;const i=this.querySelector(j);i?i.childElementCount>0?(this.enableVisibility(),null===(e=this.childContentObserver)||void 0===e||e.disconnect()):this.observeChildContent(i):this.observeChildContent(this)}contentLoaded(){const t=this.querySelector(j);return null!==t&&t.childElementCount>0}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this.childContentObserver)||void 0===t||t.disconnect()}static getPlacementTargetClientRect(t){if(null!==t){if(null!==t.targetReference&&t.targetReference instanceof HTMLElement)return t.targetReference.getBoundingClientRect();if(null!==t.targetSelector){const e=document.querySelector(t.targetSelector);if(null!==e)return e.getBoundingClientRect()}}return{x:0,y:0,width:window.innerWidth,height:window.innerHeight}}static getShowAtBox(t){return null===t?null:null!==t.targetReference&&t.targetReference instanceof HTMLElement?z.getElementTopLeftPoint(t.targetReference):null!==t.targetSelector?z.getElementTopLeftPoint(document.querySelector(t.targetSelector)):t.targetPoint}static getElementTopLeftPoint(t){if(null===t)return null;const e=t.getBoundingClientRect();return new f(e.left,e.top)}canProcessEscapeKeyDown(){return this.closeOnEscape}activatePopupContent(){this.dispatchEvent(new a(this.initialVisibility))}renderTemplate(){return v`
            <dxbl-branch
                id="branch"
                branch-id="${this.branchId}"
                parent-branch-id="${this.parentBranchId}"
                type="${this.branchType}"
                style="${this.dragCssStyle}">
                <dxbl-window-root
                    id="root"
                    style="${this.rootCssStyle}"
                    ?resizing="${this.resizing}"
                    drop-opposite="${this.actualDropOpposite}"
                    drop-direction="${this.actualDropDirection}"
                    drop-alignment="${this.actualDropAlignment}">
                    ${this.renderDefaultSlot()}
                    ${this.renderAdditionalSlots()}
                    ${this.renderBridgeSlot()}
                </dxbl-window-root>
            </dxbl-branch>
        `}raiseResizeCompleted(t,e){this.sizeDirty=!0,super.raiseResizeCompleted(t,e)}processCustomPlacement(t,e){if(this.positionXDirty||this.positionYDirty||this.isInDragOperation||this.sizeDirty)return null;const i=this.showOptionsToApply;if(i){const t=i.targetPoint||this.calcShowPoint(i);return[new h(t,c.None,!1,p.Near,d.bottom)]}return null}calcShowPoint(t){const e=z.getPlacementTargetClientRect(t),i=this.getBoundingClientRect(),o=document.body.getBoundingClientRect(),n=null!=window.visualViewport?{x:0,y:0,width:window.visualViewport.width,height:window.visualViewport.height}:o,s=this.calcShowXPoint(i,e),r=this.calcShowYPoint(i,e),l=n.y+n.height-i.height;return new f(Math.max(0,s+window.scrollX-Math.max(0,s-(n.x+n.width-i.width))),Math.max(0,r+window.scrollY-Math.max(0,r-l)))}calcShowXPoint(t,e){return Math.floor((()=>{switch(this.horizontalAlignment){case P.Left:return e.x;case P.Right:return e.x+e.width-t.width;case P.Center:return e.x+e.width/2-t.width/2}throw new Error("calcShowXPoint not supported for horizontalAlignment:"+this.horizontalAlignment)})())}calcShowYPoint(t,e){return Math.floor((()=>{switch(this.verticalAlignment){case T.Top:return e.y;case T.Bottom:return e.y+e.height-t.height;case T.Center:return e.y+e.height/2-t.height/2}throw new Error("calcShowYPoint not supported for horizontalAlignment:"+this.verticalAlignment)})())}updated(t){super.update(t),t.has("zIndex")&&this.zIndex&&this.raiseZIndexChange()}raiseZIndexChange(){this.updateComplete.then((t=>{this.dispatchEvent(new u(new m(this.zIndex)))}))}createKeyboardNavigationStrategy(){return new w(this.keyboardNavigator,this,!0)}};t([x({type:P,attribute:"horizontal-alignment",reflect:!1})],D.prototype,"horizontalAlignment",void 0),t([x({type:P,attribute:"vertical-alignment",reflect:!1})],D.prototype,"verticalAlignment",void 0),t([x({type:Number,attribute:"position-y",reflect:!1})],D.prototype,"positionY",null),t([x({type:Number,attribute:"position-x",reflect:!1})],D.prototype,"positionX",null),t([x({type:Boolean,attribute:"close-on-escape"})],D.prototype,"closeOnEscape",void 0),D=z=t([A(C)],D);export{D as DxWindow};
