import{_ as e}from"./tslib.es6-d65164b3.js";import{b as t}from"./browser-3fc721b7.js";import o from"./adaptivedropdowncomponents-7cb91d74.js";import{D as s}from"./dx-dropdown-base3-726be7be.js";import{ListBoxSelectedItemsChangedEvent as r}from"./dx-listbox-9345a0b2.js";import{C as i}from"./custom-events-helper-e7f279d3.js";import{c as n}from"./constants-da6cacac.js";import{k as p}from"./key-ffa272aa.js";import{E as a}from"./eventhelper-8bcec49f.js";import{T as d}from"./text-editor-733d5e56.js";import{n as m}from"./property-4ec0b52d.js";import"./_commonjsHelpers-41cdd1e7.js";import"./dropdowncomponents-3d8f06da.js";import"./dropdown-f5b2318c.js";import"./popup-355ecaa4.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./custom-element-267f9a21.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./portal-b3727c25.js";import"./data-qa-utils-8be7c726.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./common-48ec40e2.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./dom-utils-d057dcaa.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./thumb-31d768d7.js";import"./query-44b9267f.js";import"./popupportal-bbd2fea0.js";import"./events-interseptor-a522582a.js";import"./modalcomponents-951e20e2.js";import"./masked-input-0c0a9541.js";import"./input-66769c52.js";import"./constants-3209ffde.js";import"./single-slot-element-base-01d93921.js";class l extends CustomEvent{constructor(e,t,o,s){super(l.eventName,{detail:new u(e,t,o,s),bubbles:!0,composed:!0,cancelable:!0})}}l.eventName=n+".keydown";class c extends CustomEvent{constructor(e){super(c.eventName,{detail:new b(e),bubbles:!0,composed:!0,cancelable:!0})}}c.eventName=n+".keyup";class u{constructor(e,t,o,s){this.Key=e,this.AltKey=t,this.CtrlKey=o,this.Text=s}}class b{constructor(e){this.Key=e}}i.register(l.eventName,(e=>e.detail)),i.register(c.eventName,(e=>e.detail));class y extends s{constructor(){super(),this.boundOnDropDownSelectedItemsChangedHandler=this.onDropDownSelectedItemsChanged.bind(this),this.tabKeyPressed=!1,this.filteringEnabled=!1}get useMobileFocusBehaviour(){return t.Browser.MobileUI||super.useMobileFocusBehaviour}processKeyDownServerCommand(e){if(!this.requireSendKeyCommandToServer(e))return this.isToggleDropDownVisibilityKeyCommand(e);return this.dispatchEvent(new l(e.key,e.altKey,e.ctrlKey||e.metaKey,this.fieldElement?this.fieldElementValue:this.fieldText)),!0}processCapturedKeyDownAsync(e,t){return this.processKeyDownServerCommand(e)?(e.preventDefault(),t.handled=!0,Promise.resolve()):super.processCapturedKeyDownAsync(e,t)}processKeyDown(e){e.keyCode===p.KeyCode.Tab&&(this.tabKeyPressed=!0);return this.isDropDownOpen||this.processKeyDownServerCommand(e),super.processKeyDown(e)}processKeyUp(e){return e.keyCode!==p.KeyCode.Up&&e.keyCode!==p.KeyCode.Down||this.dispatchEvent(new c(e.key)),this.tabKeyPressed=!1,!1}raiseFocusOut(e){super.raiseFocusOut(e,this.tabKeyPressed)}onTextInput(e){this.inputElement&&(!this.isDropDownOpen&&e.data&&e.data.length>0&&this.toggleDropDownVisibility(),this.filteringEnabled&&(e.stopPropagation(),this.raiseFieldText()))}onTextChange(e){}onDropDownSelectedItemsChanged(e){e.stopPropagation();const t=e.target;null==t||t.scrollToFocusedItem(!1)}ensurePopupElement(){var e;super.ensurePopupElement(),null===(e=this.popupElement)||void 0===e||e.addEventListener(r.eventName,this.boundOnDropDownSelectedItemsChangedHandler)}disposePopupElement(){var e;null===(e=this.popupElement)||void 0===e||e.removeEventListener(r.eventName,this.boundOnDropDownSelectedItemsChangedHandler),super.disposePopupElement()}requireSendKeyCommandToServer(e){switch(e.keyCode){case p.KeyCode.Enter:case p.KeyCode.Esc:case p.KeyCode.Up:case p.KeyCode.Down:case p.KeyCode.PageUp:case p.KeyCode.PageDown:return!0;case p.KeyCode.Home:case p.KeyCode.End:return e.ctrlKey||e.metaKey}return!1}isToggleDropDownVisibilityKeyCommand(e){return(e.altKey||e.metaKey)&&("ArrowUp"===e.key||"ArrowDown"===e.key)}applyTextPropertyCore(){this.fieldElement&&(super.applyTextPropertyCore(),""===this.fieldText||this.allowInput||this.fieldElement.setSelectionRange(0,0))}processPointerDown(e){return a.containsInComposedPath(e,this.isEditBoxElement.bind(this))&&!this.isDropDownOpen&&this.toggleDropDownVisibility(),super.processPointerDown(e)}getFieldElement(){return this.querySelector(`.${d.TextEditInput}`)}}e([m({type:Boolean,attribute:"filtering-enabled"})],y.prototype,"filteringEnabled",void 0),customElements.define("dxbl-combobox",y);const h={loadModule:function(){},adaptiveDropdownComponents:o};export{y as DxComboBox,h as default};
