const e=(e,i)=>"method"===i.kind&&i.descriptor&&!("value"in i.descriptor)?{...i,finisher(r){r.createProperty(i.key,e)}}:{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:i.key,initializer(){"function"==typeof i.initializer&&(this[i.key]=i.initializer.call(this))},finisher(r){r.createProperty(i.key,e)}};function i(i){return(r,t)=>void 0!==t?((e,i,r)=>{i.constructor.createProperty(r,e)})(i,r,t):e(i,r)}export{i as n};
