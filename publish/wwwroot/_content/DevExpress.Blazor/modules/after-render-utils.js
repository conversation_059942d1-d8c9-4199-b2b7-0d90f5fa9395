import{d as t}from"./devices-17b9ba08.js";function e(t,e){const o=t.ownerDocument.getElementById(e);if(null!==o){const e=o.getBoundingClientRect(),n=t.getBoundingClientRect();return new Promise(((i,r)=>{requestAnimationFrame((r=>{!(t.compareDocumentPosition(document.body)&Node.DOCUMENT_POSITION_DISCONNECTED)&&e.top>n.top&&o.scrollTop>0&&(o.scrollTop=0),i()}))}))}return Promise.resolve()}async function o(t,e){const o=await e.arrayBuffer(),n=URL.createObjectURL(new Blob([o])),i=document.createElement("a");return document.body.appendChild(i),i.href=n,i.download=null!=t?t:"",i.click(),i.remove(),URL.revokeObjectURL(n),Promise.resolve()}async function n(t,e){const o=t.isConnected?t:null;if(null!==o){const t=await e.arrayBuffer(),n=new TextDecoder("utf-8").decode(t),i=JSON.parse(n);o.processUploadedFilesOptions(i)}return Promise.resolve()}async function i(t){customElements.get(t)||await customElements.whenDefined(t)}async function r(t){const e=t.isConnected?t:null;return null!==e&&e.showFileSelectorDialog(),Promise.resolve()}async function l(t,e,...o){return new Promise(((n,i)=>{try{const i=t[e].call(t,o);i.then?i.then(n):n(i)}catch(t){i(t)}}))}async function c(t){if(navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(t);const e=document.createElement("textarea");e.value=t,e.style.position="absolute",e.style.left="-999999px",e.style.height="0px",e.style.width="0px",document.body.prepend(e),e.select();try{document.execCommand("copy")}catch(t){console.error(t)}finally{e.remove()}}class s{constructor(t,e,o,n,i,r,l,c){this.targetWidth=0,this.targetHeight=0,this.targetPositionX=0,this.targetPositionY=0,this.viewPortWidth=0,this.viewPortHeight=0,this.viewPortScrollHeight=0,this.viewPortScrollWidth=0,this.targetWidth=t,this.targetHeight=e,this.targetPositionX=o,this.targetPositionY=n,this.viewPortWidth=i,this.viewPortHeight=r,this.viewPortScrollWidth=l,this.viewPortScrollHeight=c}}function a(t){if(!t)throw new Error("failed");return t}function h(t){const e=document.querySelector(t);if(null===t)throw new Error("Position target does not exist");if(null===e)return console.warn("Could not find an element by the specified position target"),function(){const t=document.querySelector("body").getBoundingClientRect();return new s(Math.floor(t.width/2),Math.floor(t.height),Math.floor(t.width/2),Math.floor(t.height),Math.floor(window.innerWidth),Math.floor(window.innerHeight),Math.floor(window.scrollX),Math.floor(window.scrollY))}();const o=e.getBoundingClientRect();return new s(Math.floor(o.width),Math.floor(o.height),Math.floor(o.x),Math.floor(o.y),Math.floor(window.innerWidth),Math.floor(window.innerHeight),Math.floor(window.scrollX),Math.floor(window.scrollY))}function u(e,o){return t.getDeviceInfo(e,o)}function d(t){return t.value}function w(t,e){t.value=e}export{c as copyToClipboard,o as downloadFileFromStream,u as getDeviceInfo,a as getElement,d as getInputValue,h as getLayoutAttributes,l as invokeMethod,e as scrollIntoView,n as sendFileOptions,w as setInputValue,r as showFileSelectorDialog,i as waitUntilElementLoaded};
