import{c as e,g as t}from"./_commonjsHelpers-41cdd1e7.js";import{d as o}from"./dom-554d0cc7.js";import{t as r}from"./touch-6a322081.js";import{c as n,b as i}from"./common-48ec40e2.js";var c=e((function(e,t){Object.defineProperty(t,"__esModule",{value:!0}),t.EvtUtils=void 0;var c=function(){function e(){}return e.preventEvent=function(e){e.cancelable&&(e.preventDefault?e.preventDefault():e.returnValue=!1)},e.getEventSource=function(t){return n.isDefined(t)?e.getEventSourceCore(t):null},e.getEventSourceByPosition=function(t){if(!n.isDefined(t))return null;if(!document.elementFromPoint)return e.getEventSourceCore(t);var r=e.getEventX(t)-(e.clientEventRequiresDocScrollCorrection()?o.DomUtils.getDocumentScrollLeft():0),i=e.getEventY(t)-(e.clientEventRequiresDocScrollCorrection()?o.DomUtils.getDocumentScrollTop():0);return void 0===r||void 0===i?e.getEventSourceCore(t):document.elementFromPoint(r,i)},e.getEventSourceCore=function(e){return e.srcElement?e.srcElement:e.target},e.getMouseWheelEventName=function(){return i.Browser.Safari?"mousewheel":i.Browser.NetscapeFamily&&i.Browser.MajorVersion<17?"DOMMouseScroll":"wheel"},e.isLeftButtonPressed=function(t){return!!r.TouchUtils.isTouchEvent(t)||!!(t=i.Browser.IE&&n.isDefined(event)?event:t)&&(i.Browser.IE&&i.Browser.Version<11?!!i.Browser.MSTouchUI||t.button%2==1:i.Browser.WebKitFamily?"pointermove"===t.type||"pointerenter"===t.type||"pointerleave"===t.type?1===t.buttons:1===t.which:i.Browser.NetscapeFamily||i.Browser.Edge||i.Browser.IE&&i.Browser.Version>=11?e.isMoveEventName(t.type)?1===t.buttons:1===t.which:!i.Browser.Opera||0===t.button)},e.isMoveEventName=function(t){return t===r.TouchUtils.touchMouseMoveEventName||t===e.getMoveEventName()},e.getMoveEventName=function(){return window.PointerEvent?"pointermove":i.Browser.TouchUI?"touchmove":"mousemove"},e.preventEventAndBubble=function(t){e.preventEvent(t),t.stopPropagation&&t.stopPropagation(),t.cancelBubble=!0},e.clientEventRequiresDocScrollCorrection=function(){return i.Browser.AndroidDefaultBrowser||i.Browser.AndroidChromeBrowser||!(i.Browser.Safari&&i.Browser.Version<3||i.Browser.MacOSMobilePlatform&&i.Browser.Version<5.1)},e.getEventX=function(t){return r.TouchUtils.isTouchEvent(t)?r.TouchUtils.getEventX(t):t.clientX+(e.clientEventRequiresDocScrollCorrection()?o.DomUtils.getDocumentScrollLeft():0)},e.getEventY=function(t){return r.TouchUtils.isTouchEvent(t)?r.TouchUtils.getEventY(t):t.clientY+(e.clientEventRequiresDocScrollCorrection()?o.DomUtils.getDocumentScrollTop():0)},e.cancelBubble=function(e){e.cancelBubble=!0},e.getWheelDelta=function(e){var t;return t=i.Browser.NetscapeFamily&&i.Browser.MajorVersion<17?-e.detail:i.Browser.Safari?e.wheelDelta:-e.deltaY,i.Browser.Opera&&i.Browser.Version<9&&(t=-t),t},e}();t.EvtUtils=c}));t(c);export{c as e};
