import{C as e}from"./css-classes-c63af734.js";class t{static getPointerPosition(e){return{x:e.pageX,y:e.pageY}}static getDocumentScrollPosition(){return{x:document.documentElement.scrollLeft,y:document.documentElement.scrollTop}}static getElementPosition(e){const{left:t,top:r}=e.getBoundingClientRect();return{x:t,y:r}}}const r=new class{constructor(){this.draggingHelpersMap=new Map}register(e){this.draggingHelpersMap.set(e.getKey(),e)}remove(e){this.draggingHelpersMap.delete(e)}find(e){var t;return null!==(t=this.draggingHelpersMap.get(e))&&void 0!==t?t:null}getTargets(){return[...this.draggingHelpersMap.values()]}};class n{constructor(){this.draggableContext=null,this.isRefreshUIRequested=!1,this.boundOnDocumentMouseMoveHandler=this.onDocumentMouseMove.bind(this),this.boundOnDocumentScrollHandler=this.onDocumentScroll.bind(this)}start(e,t){const r=this.getDraggableElement(e);this.draggableContext=this.createDraggableContext(e,r,t),this.addEventSubscriptions(),this.updateDraggableElement(t)}stop(){this.draggableContext&&(this.draggableContext.isStarted=!1),this.isRefreshUIRequested=!1,this.hideDraggableElement(),this.removeEventSubscriptions(),this.draggableContext=null}isStarted(){var e;return!!(null===(e=this.draggableContext)||void 0===e?void 0:e.isStarted)}hideDraggableElement(){this.draggableContext&&(this.draggableContext.dragableElement.style.transform="",this.draggableContext.dragableElement.classList.remove(e.Visible))}addEventSubscriptions(){document.addEventListener("pointermove",this.boundOnDocumentMouseMoveHandler),document.addEventListener("scroll",this.boundOnDocumentScrollHandler)}removeEventSubscriptions(){document.removeEventListener("pointermove",this.boundOnDocumentMouseMoveHandler),document.removeEventListener("scroll",this.boundOnDocumentScrollHandler)}refreshUI(){this.isRefreshUIRequested||(this.isRefreshUIRequested=!0,requestAnimationFrame((()=>this.refreshUICore())))}refreshUICore(){if(this.isRefreshUIRequested=!1,!this.isStarted()||!this.draggableContext)return;const t=this.draggableContext;t.dragableElement.style.transform=`translate(${Math.round(t.draggableElementPosition.x)}px, ${Math.round(t.draggableElementPosition.y)}px)`,t.dragableElement.classList.add(e.Visible)}createDraggableContext(e,r,n){const s=t.getDocumentScrollPosition(),o=t.getPointerPosition(n);return{isStarted:!0,srcElement:e,srcElementPosition:t.getElementPosition(e),dragableElement:r,mouseOverElement:null,mouseOverElementRect:null,isPossibleDropItem:!1,initialCursorPosition:o,currentCursorPosition:{x:0,y:0},draggableElementOffset:t.getElementPosition(r),draggableElementPosition:o,initialScrollPosition:s,currentScrollPosition:s}}clearMoveStateFromDraggableContext(){this.draggableContext&&(this.draggableContext.mouseOverElement=null,this.draggableContext.mouseOverElementRect=null,this.draggableContext.isPossibleDropItem=!1)}setMouseOverElementInfo(e){if(!this.draggableContext)return;const t=e.getBoundingClientRect(),{currentScrollPosition:r}=this.draggableContext;this.draggableContext.mouseOverElement=e,this.draggableContext.mouseOverElementRect={x:t.left+r.x,y:t.top+r.y,width:t.width,height:t.height}}updateDraggableElementPosition(){if(!this.draggableContext)return;const e=this.draggableContext;e.draggableElementPosition={x:e.currentCursorPosition.x-e.currentScrollPosition.x,y:e.currentCursorPosition.y-e.currentScrollPosition.y}}onElementMouseOut(e){this.clearMoveStateFromDraggableContext(),this.refreshUI()}onDocumentMouseMove(e){this.updateDraggableElement(e)}onDocumentScroll(e){if(!this.draggableContext)return;const r=this.draggableContext;r.currentScrollPosition=t.getDocumentScrollPosition(),r.srcElementPosition=t.getElementPosition(r.srcElement),r.mouseOverElement&&this.setMouseOverElementInfo(r.mouseOverElement),this.updateDraggableElementPosition(),this.refreshUI()}updateDraggableElement(e){if(!this.draggableContext)return;const t=this.draggableContext;t.currentCursorPosition={x:e.pageX,y:e.pageY},t.initialScrollPosition=t.currentScrollPosition,this.updateDraggableElementPosition(),this.refreshUI()}}class s extends n{constructor(){super(),this.boundOnElementMouseOutHandler=this.onElementMouseOut.bind(this)}get draggableItemContext(){return this.draggableContext}createItemDraggedHandlerArgs(){if(!this.draggableItemContext||!this.draggableItemContext.isPossibleDropItem||!this.draggableItemContext.target)return null;const e=this.draggableItemContext.target;return{sourceInfo:this.createSourceInfo(),targetInfo:e.createTargetInfo()}}createTargetInfo(){return{key:this.getKey()}}activateDropTarget(e){this.isWithinDragging()||(this.draggableContext=e),this.addElementListeners(this.getItemDropArea())}deactivateDropTarget(){this.removeElementListeners(this.getItemDropArea()),this.isWithinDragging()||(this.draggableContext=null)}start(e,t){super.start(e,t),this.getDroppableTargets().forEach((e=>e.activateDropTarget.bind(e)(this.draggableContext)))}stop(){this.getDroppableTargets().forEach((e=>e.deactivateDropTarget.bind(e)())),super.stop()}createDraggableContext(e,t,r){return{...super.createDraggableContext(e,t,r),source:this,target:this,targetKey:null}}getDroppableTargets(){return r.getTargets()}updateDraggableContext(e){if(!this.draggableItemContext)return;const{isPossibleDropItem:t,target:r}=this.draggableItemContext,n=this.getItemDropArea();r&&t&&s.isDescendantElement(n,r.getItemDropArea())||(this.clearMoveStateFromDraggableContext(),this.setMouseOverElementInfo(e),this.draggableItemContext.target=this,this.draggableItemContext.targetKey=this.getKey(),this.draggableItemContext.isPossibleDropItem=this.isItemDropPossible(e))}isWithinDragging(){var e;return(null===(e=this.draggableItemContext)||void 0===e?void 0:e.source)===this}createSourceInfo(){return{key:this.getKey()}}addElementListeners(e){e.addEventListener("pointerout",this.boundOnElementMouseOutHandler)}removeElementListeners(e){e.removeEventListener("pointerout",this.boundOnElementMouseOutHandler)}isItemDropPossible(e){return!0}static isDescendantElement(e,t){return e!==t&&e.contains(t)}}export{n as D,s as I,r as d};
