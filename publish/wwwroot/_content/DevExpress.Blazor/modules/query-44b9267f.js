function e(e,o){return(({finisher:e,descriptor:o})=>(r,n)=>{var t;if(void 0===n){const n=null!==(t=r.originalKey)&&void 0!==t?t:r.key,i=null!=o?{kind:"method",placement:"prototype",key:n,descriptor:o(r.key)}:{...r,key:n};return null!=e&&(i.finisher=function(o){e(o,n)}),i}{const t=r.constructor;void 0!==o&&Object.defineProperty(r,n,o(n)),null==e||e(t,n)}})({descriptor:r=>{const n={get(){var o,r;return null!==(r=null===(o=this.renderRoot)||void 0===o?void 0:o.querySelector(e))&&void 0!==r?r:null},enumerable:!0,configurable:!0};if(o){const o="symbol"==typeof r?Symbol():"__"+r;n.get=function(){var r,n;return void 0===this[o]&&(this[o]=null!==(n=null===(r=this.renderRoot)||void 0===r?void 0:r.querySelector(e))&&void 0!==n?n:null),this[o]}}return n}})}export{e as i};
