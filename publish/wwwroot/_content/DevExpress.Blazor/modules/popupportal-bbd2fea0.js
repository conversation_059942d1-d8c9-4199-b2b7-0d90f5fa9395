import{_ as o}from"./tslib.es6-d65164b3.js";import{D as r}from"./portal-b3727c25.js";import{c as t,a as e}from"./logicaltreehelper-67db40f1.js";import{e as s}from"./custom-element-267f9a21.js";const l="dxbl-popup-portal";let p=class extends r{get popup(){var o;for(const r of null!==(o=this.portable)&&void 0!==o?o:[])if(t(r))return r;return null}get popupBase(){var o;for(const r of null!==(o=this.portable)&&void 0!==o?o:[])if(e(r))return r;return null}};p=o([s(l)],p);export{l as D};
