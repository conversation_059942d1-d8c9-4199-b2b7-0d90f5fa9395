import{P as t,N as e}from"./point-e4ec110e.js";import{T as i,V as r,D as n}from"./constants-7c047c0d.js";class s{get width(){return this.widthField}get height(){return this.heightField}constructor(t,e){this.widthField=t,this.heightField=e}}s.Empty=new s(0,0);class o{get end(){return this._end}get start(){return this._start}get size(){return Math.abs(this.end-this.start)}constructor(t,e){this._start=t,this._end=e}static rectToRange(t,e){return e?new o(t.left,t.right):new o(t.top,t.bottom)}}class a{get topLeft(){return new t(this.x,this.y)}get topRight(){return new t(this.x+this.width,this.y)}get bottomLeft(){return new t(this.x,this.y+this.height)}get bottomRight(){return new t(this.x+this.width,this.y+this.height)}get x(){return this.xField}get y(){return this.yField}get width(){return this.widthField}get height(){return this.heightField}get isEmpty(){return e.lessThanOrClose(this.width,0)||e.lessThanOrClose(this.height,0)}get left(){return this.x}get top(){return this.y}get right(){return this.isEmpty?Number.NEGATIVE_INFINITY:this.x+this.width}get bottom(){return this.isEmpty?Number.NEGATIVE_INFINITY:this.y+this.height}get size(){return new s(this.width,this.height)}constructor(t,e,i,r){this.xField=t,this.yField=e,this.widthField=i,this.heightField=r}static create(t,e){return new a(t.x,t.y,e.width,e.height)}static createFromPoints(t,e){return new a(Math.min(t.x,e.x),Math.min(t.y,e.y),Math.abs(t.x-e.x),Math.abs(t.y-e.y))}}a.Empty=new a(0,0,0,0);class l{static fromDomRect(t){return new a(t.left,t.top,t.width,t.height)}static offset(t,e){return new a(t.x+e.x,t.y+e.y,t.width,t.height)}static areSame(t,i){return e.areClose(t.x,i.x)&&e.areClose(t.y,i.y)&&e.areClose(t.width,i.width)&&e.areClose(t.height,i.height)}static intersectsWith(t,e){return!t.isEmpty&&!e.isEmpty&&(e.left<=t.right&&e.right>=t.left&&e.top<=t.bottom&&e.bottom>=t.top)}static intersect(t,e){if(!l.intersectsWith(t,e))return a.Empty;const i=Math.max(t.left,e.left),r=Math.max(t.top,e.top),n=Math.max(Math.min(t.right,e.right)-i,0),s=Math.max(Math.min(t.bottom,e.bottom)-r,0);return new a(i,r,n,s)}static contains(t,e){return!t.isEmpty&&(e.x>=t.x&&e.x-t.width<=t.x&&e.y>=t.y&&e.y-t.height<=t.y)}static parse(t){const e=t.split(",");if(4!==e.length)throw new Error("incorrect parameters number");return new a(parseFloat(e[0]),parseFloat(e[1]),parseFloat(e[2]),parseFloat(e[3]))}}class h{static isAbsolutePositioning(t){return"absolute"===window.getComputedStyle(t).getPropertyValue("position")}static isFixedPositioning(t){return"fixed"===window.getComputedStyle(t).getPropertyValue("position")}static isScrollable(t){const{overflow:e,overflowX:i,overflowY:r}=window.getComputedStyle(t);return/auto|scroll|overlay|hidden/.test(e+i+r)}static setBooleanAttribute(t,e,i){i?t.setAttribute(e,""):t.removeAttribute(e)}static isOverflown(t){return t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth}static isTableElement(t){var e;return[i.table,i.tableCell,i.tableHeaderCell].indexOf(null!==(e=h.getNodeName(t))&&void 0!==e?e:"")>=0}static isTextInput(t){return t.tagName===i.input||t.tagName===i.textArea}static isMultipleRowInput(t){const e=t.getAttribute("contenteditable");return e?"false"!==e.toLowerCase():t.tagName===i.textArea}static toPx(t){return Math.floor(t)+"px"}static getNodeName(t){return t?(t.nodeName||"").toLowerCase():null}static getVerticalScrollbarWidth(t=null){return null==t?window.innerWidth-document.body.getBoundingClientRect().width:t.offsetWidth-t.clientWidth}static matches(t,e){return t.matches(e)}static isHidden(t){if(getComputedStyle(t).visibility===r.hidden||t.closest('[inert="true"]'))return!0;let e=t;for(;e;){if(getComputedStyle(t).display===n.none)return!0;e=e.parentElement}return!1}static getAttributeIntValue(t,e,i){return h.getAttributeParsedNumberValue(t,e,parseInt,i)}static getAttributeParsedNumberValue(t,e,i,r){const n=t.getAttribute(e);return n?i(n):r}}class u{static getRelativeElementRect(t,e=null){const i=null!=e?l.fromDomRect(e.getBoundingClientRect()):a.Empty,r=l.fromDomRect(t.getBoundingClientRect());return new a(r.x-i.x+window.scrollX,r.y-i.y+window.scrollY,r.width,r.height)}static getParent(t,e){const i=this.getParentCore(t,e);return"html"===(null==i?void 0:i.nodeName.toLowerCase())?null:i}static getParentCore(t,e){if(e){if(t&&t.assignedSlot)return t.assignedSlot}if(t.parentNode)return t.parentNode;return t&&t.host?t.host:null}static findParent(t,e,i=!0){let r=u.getParent(t,i);for(;r;){if(r&&e(r))return r;r=u.getParent(r,i)}return null}static*getRootPathAndSelf(t,e=null,i=!0){yield t;for(const r of u.getRootPath(t,e,i))yield r}static*getRootPath(t,e=null,i=!0){let r=u.getParent(t,i);for(;r&&r!==e;)yield r,r=u.getParent(r,i)}static getScrollParent(t){var e;return t?["html","body","#document"].indexOf(null!==(e=h.getNodeName(t))&&void 0!==e?e:"")>=0?null:t instanceof HTMLElement&&h.isScrollable(t)?t:u.getScrollParent(u.getParent(t,!0)):null}static getRealOffsetParent(t){return t instanceof HTMLElement&&!h.isFixedPositioning(t)?t.offsetParent:null}static getOffsetParent(t){let e=u.getRealOffsetParent(t);for(;e&&h.isTableElement(e)&&"static"===getComputedStyle(e).position;)e=u.getRealOffsetParent(e);return e}static containsElement(t,e){var i;if(!e)return!1;const r=e.getRootNode();if(t.contains(e))return!0;if(r&&r instanceof ShadowRoot){let r=e;do{if(r&&t.isSameNode(r))return!0;r=null!==(i=null==r?void 0:r.parentNode)&&void 0!==i?i:null}while(r)}return!1}static getScrollParents(t){if(!t)return[];const e=u.getScrollParent(t);if(!e)return[];const i=new Array(e),r=u.getParent(e,!0);if(!r)return i;const n=this.getScrollParents(r);return i.concat(n)}static getClippingRoot(t){return u.findParent(t,(t=>t instanceof HTMLElement&&h.isFixedPositioning(t)),!0)}static getClippingParents(t){const e=u.getScrollParents(u.getParent(t,!0)),i=u.getClippingRoot(t);return e.filter(i?t=>t instanceof HTMLElement&&t!==i&&!u.containsElement(t,i):t=>t instanceof HTMLElement)}}export{h as D,u as L,l as R,s as S,a,o as b};
