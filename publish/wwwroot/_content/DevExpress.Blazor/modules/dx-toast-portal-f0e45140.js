import{_ as t}from"./tslib.es6-d65164b3.js";import{S as e}from"./single-slot-element-base-01d93921.js";import{s as o}from"./lit-element-462e7ad3.js";import{n as s}from"./property-4ec0b52d.js";import{e as i}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";const r="dxbl-toast-portal";let d=class extends e{constructor(){super(...arguments),this.width="",this.zIndex=0}updated(t){t.has("width")&&(this.style.width=this.width),t.has("zIndex")&&(this.style.zIndex=this.zIndex.toString())}};d.shadowRootOptions={...o.shadowRootOptions,mode:"open"},t([s()],d.prototype,"width",void 0),t([s({type:Number,attribute:"z-index"})],d.prototype,"zIndex",void 0),d=t([i(r)],d);export{d as DxToastPortal,r as dxToastPortalTagName};
