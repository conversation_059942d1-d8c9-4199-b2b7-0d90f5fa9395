import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{K as i,D as s}from"./keyboard-navigation-strategy-ea41c807.js";import{k as o}from"./key-ffa272aa.js";import{N as r,a as n}from"./events-70d90aad.js";import{b as a}from"./constants-7c047c0d.js";import{FocusHiddenAttributeName as l,removeFocusHiddenAttribute as c,addFocusHiddenAttribute as d}from"./focus-utils-ae044224.js";import{n as h}from"./property-4ec0b52d.js";import{e as m}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./focushelper-2eea96ca.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./devices-17b9ba08.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";var u;class p{}u=p,p.prefix="dxbl",p.root=`${u.prefix}-treeview`,p.templatedItem=`${u.root}-tmpl`,p.item=`${u.root}-item`,p.itemsContainer=`${u.root}-items-container`,p.itemContent=`${u.item}-content`,p.itemContainer=`${u.item}-container`,p.checkAllBox=`${u.root}-checkbox-check-all`,p.filterPanel=`${u.prefix}-navigation-filter`;class C{}C.parentId="parent-id",C.notTemplatedItemSelector=`li > .${p.itemContent}`,C.templatedItemSelector=`li > .${p.templatedItem}`;const y=`:scope > .${p.itemContainer}, :scope.${p.templatedItem}`;class v extends i{constructor(e,t){super(e.getKeyBoardNavigator(),t),this._treeView=e}canSwitchToNestedContentMode(){return!0}queryItems(){return this.queryItemsBySelector(y)}}const b=`:scope ${C.templatedItemSelector}, :scope ${C.notTemplatedItemSelector}`;class I extends i{activate(){!this._activatedByClick&&this._focusedFromOutside&&null==this._treeView.getAttribute(l)&&(this.selectedItemIndex=this.navigator.focusedFromForwardElement?this.itemCount-1:0),this._focusedFromOutside=!1,this._activatedByClick=!1,super.activate()}onActiveStateChanged(e){this._focusedFromOutside=e,super.onActiveStateChanged(e)}onPointerDown(e){this._activatedByClick=!0}constructor(e,t){super(e.getKeyBoardNavigator(),t),this._focusedFromOutside=!0,this.boundOnPointerDownHandler=this.onPointerDown.bind(this),this._activatedByClick=!0,this._treeView=e,this.targetElement.addEventListener("pointerdown",this.boundOnPointerDownHandler)}queryItems(){return this.queryItemsBySelector(b)}createItemStrategy(e){return new v(this._treeView,e)}getFocusableContainer(){return this.isTemplatedItem()?this.selectedItemElement:this.selectedItemElement.querySelector(`:scope > .${p.itemContainer}`)}selectedItemExpandedState(){const e=this.getFocusableContainer().getAttribute(a.ariaExpanded);return null===e?null:"true"===e}selectedItemIsCheckable(){return this.getFocusableContainer().hasAttribute(a.ariaChecked)}selectedItemIsParentNode(){return"1"===this.getFocusableContainer().getAttribute(a.ariaLevel)}selectedItemIsEndNode(){return null===this.getFocusableContainer().getAttribute(a.ariaExpanded)}getSelectedItemParent(){const e=this.getFocusableContainer(),t=this.isTemplatedItem()?e.parentElement:e,i=null==t?void 0:t.getAttribute(C.parentId),s=document.querySelector(`#${i}`);return this.isTemplatedItem()?s.firstElementChild:s.parentElement}isTemplatedItem(){return this.selectedItemElement.classList.contains(p.templatedItem)}handleKeyDown(e){return super.handleKeyDown(e),!this.nestedContentSelected&&this.handleKeyDownCore(e)}handleKeyDownCore(e){switch(o.KeyUtils.getEventKeyCode(e)){case o.KeyCode.Enter:return this.raiseClickEvent(this.getFocusableContainer(),e.ctrlKey,e.metaKey,e.shiftKey),!0;case o.KeyCode.Space:return this.handleSpace(),!0;case o.KeyCode.Up:return this.moveToPrevItem(!0),!0;case o.KeyCode.Down:return this.moveToNextItem(!0),!0;case o.KeyCode.Left:return!0===this.selectedItemExpandedState()?this.performAction(r.Collapse):this.selectedItemIsParentNode()||this.moveToParent(),!0;case o.KeyCode.Right:return!1===this.selectedItemExpandedState()?this.performAction(r.Expand):this.selectedItemIsEndNode()||this.moveToFirstChild(),!0;case o.KeyCode.Home:return this.selectItem(0),!0;case o.KeyCode.End:return this.selectItem(this.itemCount-1),!0;default:return!1}}onDispose(){this.targetElement.removeEventListener("pointerdown",this.boundOnPointerDownHandler)}moveToParent(){const e=this.getSelectedItemParent(),t=this.items.indexOf(e);this.selectItem(t)}moveToFirstChild(){this.moveToNextItem(!1)}handleSpace(){this.handleChecking()}selectedItemIsSelectable(){return null!==this.getFocusableContainer().getAttribute(a.ariaSelected)}handleChecking(){(this.selectedItemIsCheckable()||this.selectedItemIsSelectable())&&this.performAction(r.Check)}performAction(e){this.getFocusableContainer().dispatchEvent(new n(e))}}class g extends i{constructor(e,t){super(e.getKeyBoardNavigator(),t),this._treeView=e}queryItems(){return this.queryItemsBySelector("input")}}class k extends i{get activatedByCheckAllClick(){return this._activatedByCheckAllClick}set activatedByCheckAllClick(e){this._activatedByCheckAllClick=e}constructor(e){super(e.getKeyBoardNavigator(),e),this.boundOnPointerDownHandler=this.onPointerDown.bind(this),this._activatedByCheckAllClick=!1,this._checkAllBox=null,this._treeView=e}onDispose(){if(this._checkAllBox){this._checkAllBox.querySelector("label").removeEventListener("pointerdown",this.boundOnPointerDownHandler)}}activate(){super.activate(),this.activatedByCheckAllClick||this._checkAllBox&&c(this._checkAllBox)}onActiveStateChanged(e){super.onActiveStateChanged(e),e||(this.activatedByCheckAllClick=!1)}onPointerDown(e){this._checkAllBox&&(d(this._checkAllBox),this.activatedByCheckAllClick=!0)}queryItems(){return this.queryItemsBySelector(`:scope .${p.checkAllBox}, :scope .${p.filterPanel} input, :scope .${p.itemsContainer}[role=tree]`)}isItemsContainer(e){return e.classList.contains(p.itemsContainer)}isCheckAllBox(e){return e.classList.contains(p.checkAllBox)}createItemStrategy(e){if(this.isItemsContainer(e))return new I(this._treeView,e);if(this.isCheckAllBox(e)){this._checkAllBox=e;return this._checkAllBox.querySelector("label").addEventListener("pointerdown",this.boundOnPointerDownHandler),new g(this._treeView,e)}return null}handleKeyDown(e){return super.handleKeyDown(e),this.handleKeyDownCore(e)}handleKeyDownCore(e){return o.KeyUtils.getEventKeyCode(e)===o.KeyCode.Tab&&(e.shiftKey?this.firstItemSelected?this.leaveBackward():this.moveToPrevItem(!1):this.lastItemSelected?this.leaveForward():this.moveToNextItem(!1),!0)}}var f;!function(e){e.Disabled="Disabled",e.Multiple="Multiple",e.Recursive="Recursive"}(f||(f={}));class w extends t{constructor(){super(...arguments),this._keyboardNavigator=null,this.checkMode=f.Disabled}connectedOrContentChanged(){this._keyboardNavigator=this.querySelector(`:scope > ${s}`),this._keyboardNavigator&&this.initializeKeyboardNavigation(),super.connectedOrContentChanged()}initializeKeyboardNavigation(){this._keyboardNavigator&&"initialized"in this._keyboardNavigator&&!this._keyboardNavigator.initialized&&this._keyboardNavigator.initialize(this,new k(this))}disconnectedCallback(){var e;null===(e=this._keyboardNavigator)||void 0===e||e.disposeComponent()}getKeyBoardNavigator(){return this._keyboardNavigator}}e([h({type:f,attribute:"check-mode"})],w.prototype,"checkMode",void 0);const x="dxbl-treeview";let _=class extends w{};_=e([m(x)],_);export{_ as DxTreeView,x as TreeViewTagName};
