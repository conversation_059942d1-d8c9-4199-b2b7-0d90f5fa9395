import{L as t}from"./logicaltreehelper-67db40f1.js";import{L as e}from"./layouthelper-67dd777a.js";class c{constructor(){this.action=null,this.handle=null}execute(t){this.cancel(),this.action=t,this.handle=setTimeout((()=>{var t;null===(t=this.action)||void 0===t||t.call(this),this.handle=null,this.action=null}),0)}cancel(){this.handle&&(clearTimeout(this.handle),this.action=null,this.handle=null)}}class s{static isFocused(t){return t===document.activeElement}static isFocusWithin(c,s=!1){return s?t.containsElement(c,document.activeElement):e.containsElement(c,document.activeElement)}static restoreFocus(t){t.focus({preventScroll:!1})}static cancelRestoreFocus(){this.restoreFocusAction.cancel()}static unfocus(){s.cancelRestoreFocus();const t=document.activeElement;t&&t.blur()}}s.restoreFocusAction=new c;export{c as D,s as F};
