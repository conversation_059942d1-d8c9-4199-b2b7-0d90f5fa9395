import{e as t}from"./evt-ef2dea65.js";import{t as e}from"./touch-6a322081.js";import{k as s,l as i,n as o}from"./constants-da6cacac.js";import{C as r}from"./custom-events-helper-e7f279d3.js";import{D as n}from"./dx-html-element-base-3262304e.js";import{d as a,r as h}from"./disposable-d2c2d283.js";import{K as c}from"./dom-utils-d057dcaa.js";import"./_commonjsHelpers-41cdd1e7.js";import"./dom-554d0cc7.js";import"./tslib.es6-d65164b3.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./data-qa-utils-8be7c726.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./eventhelper-8bcec49f.js";import"./constants-7c047c0d.js";import"./devices-17b9ba08.js";import"./dx-license-30fd02d1.js";import"./css-classes-c63af734.js";class l extends CustomEvent{constructor(t,e,s){super(l.eventName,{detail:new u(t,e,s),bubbles:!0,composed:!0,cancelable:!0})}}l.eventName=s+".oncolorchanged";class u{constructor(t,e,s){this.Red=t,this.Green=e,this.Blue=s}}r.register(l.eventName,(t=>t.detail));class d{static rgbToHtml(t){return"rgb("+t.red+", "+t.green+", "+t.blue+")"}static hsbToHtml(t){return this.rgbToHtml(this.hsbToRgb(t))}static htmlToHsb(t){return this.rgbToHsb(this.htmlToRgb(t))}static htmlToRgb(t){const e=(t=t.replace("rgb","").replace("(","").replace(")","")).split(",").map((t=>parseInt(t.trim())));return{red:e[0],green:e[1],blue:e[2]}}static rgbToHsb(t){const e=t.red/255,s=t.green/255,i=t.blue/255,o=Math.max(e,s,i),r=o-Math.min(e,s,i);let n;n=0===r?0:o===e?(s-i)/r%6:o===s?(i-e)/r+2:(e-s)/r+4,n*=60,n<0&&(n+=360);let a,h=o;return a=0===h?0:r/h,a*=100,h*=100,{hue:n,saturation:a,brightness:h}}static hsbToRgb(t){const e=Math.floor(t.hue/60),s=(d.maxHsb.saturation-t.saturation)*t.brightness/d.maxHsb.brightness,i=t.hue%60*(t.brightness-s)/60,o=s+i,r=t.brightness-i;let n={red:0,green:0,blue:0};switch(e){case 0:n={red:t.brightness,green:o,blue:s};break;case 1:n={red:r,green:t.brightness,blue:s};break;case 2:n={red:s,green:t.brightness,blue:o};break;case 3:n={red:s,green:r,blue:t.brightness};break;case 4:n={red:o,green:s,blue:t.brightness};break;case 5:n={red:t.brightness,green:s,blue:r}}return n.red=Math.round(2.55*n.red),n.green=Math.round(2.55*n.green),n.blue=Math.round(2.55*n.blue),n}}d.maxHsb={hue:359,saturation:100,brightness:100};class g{}g.Prefix="dx-blazor-colorpicker",g.HueAreaIndicator=g.Prefix+"-hue-selection-rect",g.SaturationBrightnessAreaIndicator=g.Prefix+"-color-selection";class b{}b.ColorAttribute="data-color";class m extends n{constructor(){super(),this._onColorChanged=null,this.indicator=this.querySelector(this.getIndicatorCssCelector()),this._containerSize={width:this.offsetWidth,height:this.offsetHeight},this._indicatorSize={width:this.indicator.offsetWidth,height:this.indicator.offsetHeight}}getIndicatorHeight(){return this._indicatorSize.height}getIndicatorWidth(){return this._indicatorSize.width}getContainerHeight(){return this._containerSize.height}getContainerWidth(){return this._containerSize.width}connectedCallback(){super.connectedCallback(),this.initializeEvents()}disposeComponent(){a(this),super.disposeComponent()}setColorChangedCallback(t){this._onColorChanged=t}raiseColorChanged(t){null!=this._onColorChanged&&this._onColorChanged(t)}initializeEvents(){const s=t=>{e.TouchUtils.isTouchEvent(t)&&t.preventDefault(),this.addEventListener(e.TouchUtils.touchMouseUpEventName,i),this.addEventListener(e.TouchUtils.touchMouseMoveEventName,o),this.moveIndicator(t)},i=t=>{this.removeEventListener(e.TouchUtils.touchMouseUpEventName,i),this.removeEventListener(e.TouchUtils.touchMouseMoveEventName,o)},o=s=>{e.TouchUtils.isTouchEvent(s)&&s.preventDefault(),t.EvtUtils.isLeftButtonPressed(s)&&this.moveIndicator(s)};this.addEventListener(e.TouchUtils.touchMouseDownEventName,s),h(this,(()=>{this.removeEventListener(e.TouchUtils.touchMouseDownEventName,s),this.removeEventListener(e.TouchUtils.touchMouseUpEventName,i),this.removeEventListener(e.TouchUtils.touchMouseMoveEventName,o)}))}updateBackground(t){}}class p extends n{constructor(){super(...arguments),this._hueArea=null,this._colorArea=null,this._color=d.maxHsb}initializeComponent(){if(this._hueArea=this.querySelector(i),this._colorArea=this.querySelector(o),!this._hueArea||!this._colorArea)throw new Error("ColorAreas elements not found");this.setupHueArea(),this.setupSaturationBrightnessArea(),this.setupColorValue()}setupHueArea(){var t;null===(t=this._hueArea)||void 0===t||t.setColorChangedCallback((t=>{if(this._color.hue=t.hue,null==this._colorArea)return;this._colorArea.updateBackground(this._color);const e=d.hsbToRgb(this._color);this.raiseOnColorChanged(e.red,e.green,e.blue)}))}setupSaturationBrightnessArea(){var t;null===(t=this._colorArea)||void 0===t||t.setColorChangedCallback((t=>{this._color.saturation=t.saturation,this._color.brightness=t.brightness;const e=d.hsbToRgb(this._color);null!=this._colorArea&&(this._colorArea.indicator.style.backgroundColor=d.rgbToHtml(e),this.raiseOnColorChanged(e.red,e.green,e.blue))}))}setupColorValue(){const t=this.getAttribute(b.ColorAttribute);if(null==t)return;const e=d.htmlToHsb(t);this.updateColor(e)}updateColor(t){var e,s;this._color=t,null===(e=this._hueArea)||void 0===e||e.updateColor(t),null===(s=this._colorArea)||void 0===s||s.updateColor(t)}raiseOnColorChanged(t,e,s){this.dispatchEvent(new l(t,e,s))}}customElements.define(s,p),customElements.define(i,class extends m{getIndicatorCssCelector(){return`.${g.HueAreaIndicator}`}moveIndicator(e){let s=c(t.EvtUtils.getEventY(e),this.indicator,!1);s=Math.min(this.getContainerHeight()-Math.round(this.getIndicatorHeight()/2),Math.max(0-this.getIndicatorHeight()/2,s)),this.indicator.style.top=s+"px";const i={hue:this.getHueValue(s),saturation:d.maxHsb.saturation,brightness:d.maxHsb.brightness};this.indicator.style.backgroundColor=d.hsbToHtml(i),this.raiseColorChanged(i)}updateColor(t){this.indicator.style.top=this.calculateHueIndicatorPosition(t.hue)+"px",this.indicator.style.backgroundColor=d.hsbToHtml({hue:t.hue,saturation:d.maxHsb.saturation,brightness:d.maxHsb.brightness})}calculateHueIndicatorPosition(t){const e=this.getContainerHeight(),s=this.getIndicatorHeight();let i=e-e*t/d.maxHsb.hue;const o=Math.round(s/2);return i=Math.min(e-o,Math.max(0-s/2,i-o)),Math.round(i)}getHueValue(t){let e=(t+=t<0?this.getIndicatorHeight()/2:Math.round(this.getIndicatorHeight()/2))*d.maxHsb.hue/this.getContainerHeight();return e=d.maxHsb.hue-e,Math.round(e)}}),customElements.define(o,class extends m{getIndicatorCssCelector(){return`.${g.SaturationBrightnessAreaIndicator}`}moveIndicator(e){let s=c(t.EvtUtils.getEventX(e),this.indicator,!0),i=c(t.EvtUtils.getEventY(e),this.indicator,!1);s=Math.min(this.getContainerWidth(),Math.max(0,s))-this.getIndicatorWidth()/2,i=Math.min(this.getContainerHeight(),Math.max(0,i))-this.getIndicatorHeight()/2,this.indicator.style.top=i+"px",this.indicator.style.left=s+"px";const o={hue:d.maxHsb.hue,saturation:this.getSaturationValue(s),brightness:this.getBrightnessValue(i)};this.raiseColorChanged(o)}updateColor(t){this.setIndicatorPosition(t),this.updateBackground(t)}updateBackground(t){this.style.backgroundColor=d.hsbToHtml({hue:t.hue,saturation:d.maxHsb.saturation,brightness:d.maxHsb.brightness}),this.indicator.style.backgroundColor=d.hsbToHtml(t)}setIndicatorPosition(t){this.indicator.style.left=this.convertSaturationToXCoordinate(t.saturation)+"px",this.indicator.style.top=this.convertBrightnessToYCoordinate(t.brightness)+"px"}convertSaturationToXCoordinate(t){const e=this.getContainerWidth()*t/d.maxHsb.saturation-this.getIndicatorWidth()/2;return Math.floor(e)}convertBrightnessToYCoordinate(t){const e=this.getContainerHeight(),s=e-e*t/d.maxHsb.brightness-this.getIndicatorHeight()/2;return Math.floor(s)}getSaturationValue(t){const e=(t+this.getIndicatorWidth()/2)*d.maxHsb.saturation/this.getContainerWidth();return Math.floor(e)}getBrightnessValue(t){const e=d.maxHsb.brightness-(t+this.getIndicatorHeight()/2)*d.maxHsb.brightness/this.getContainerHeight();return Math.floor(e)}});const C={loadModule:function(){}};export{p as DxCustomColorArea,C as default};
