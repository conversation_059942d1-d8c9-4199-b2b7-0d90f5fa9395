import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t}from"./dx-ui-element-0c1e122f.js";import{R as s}from"./layouthelper-67dd777a.js";import{C as i}from"./css-classes-c63af734.js";import{H as n,D as o,L as r}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{n as l}from"./property-4ec0b52d.js";import{x as a}from"./lit-element-462e7ad3.js";import{e as c}from"./custom-element-267f9a21.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./data-qa-utils-8be7c726.js";import"./logicaltreehelper-67db40f1.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./eventhelper-8bcec49f.js";import"./devices-17b9ba08.js";var d;const m="data-is-selected-item";class h{}d=h,h.ListBox=i.Prefix+"-listbox",h.Item=d.ListBox+"-item",h.ItemSelected=d.Item+"-selected",h.ItemActive=d.Item+"-active";class p extends Event{constructor(){super(p.eventName,{bubbles:!0,composed:!1,cancelable:!0})}}p.eventName="dxlistboxselecteditemschanged";let u=class extends t{constructor(){super(),this._handlePointerEventsMode=n.None,this.boundSlotChangedHandler=this.onSlotChanged.bind(this),this.displayed=!1,this.isMultipleColumns=!1,this.itemContainerElement=null,this.resizeObserver=new ResizeObserver(this.onResized.bind(this)),this.itemsContainerMutationObserver=new MutationObserver(this.onSelectedItemChanged.bind(this)),this.pointerEventsHelper=new o(this)}connectedCallback(){super.connectedCallback(),this.resizeObserver.observe(this),this.pointerEventsHelper.initialize()}disconnectedCallback(){super.disconnectedCallback(),this.resizeObserver.disconnect(),this.itemsContainerMutationObserver.disconnect(),this.pointerEventsHelper.dispose()}render(){return a`
            <slot @slotchange="${this.boundSlotChangedHandler}">
            </slot>
        `}scrollToFocusedItem(e){const t=this.querySelector(`[${m}]`);if(!t)return;if(!e)return void t.scrollIntoView({block:"nearest"});this.getScrollableContainerElement().scrollTop=t.offsetTop}onSlotChanged(e){this.itemContainerElement=this.getItemContainerElement(),this.itemsContainerMutationObserver.observe(this.itemContainerElement,{attributeFilter:[m],attributeOldValue:!0,subtree:!0})}onResized(e,t){if(e.length<1)return;const i=!s.fromDomRect(e[0].contentRect).isEmpty;i&&!this.displayed&&this.scrollToFocusedItem(!0),this.displayed=i}onSelectedItemChanged(e,t){const s=new p;this.dispatchEvent(s)}getItemContainerElement(){return this.querySelector(this.isMultipleColumns?"table tbody":"ul")}getScrollableContainerElement(){return this.isMultipleColumns?this.querySelector(".dxbs-grid-vsd"):this}get handlePointerEventsMode(){return this._handlePointerEventsMode}get handlePointerEventsTarget(){return this}get handlePointerEventsDelay(){return r}get hoverTitleElementsSelector(){return null}get bypassNonInlineHoverTitleElementChildSelector(){return null}shouldProcessPointerEvent(e){return!0}};e([l({type:Boolean,attribute:"is-multiple-columns"})],u.prototype,"isMultipleColumns",void 0),u=e([c("dxbl-listbox")],u);const b={loadModule:function(){}};export{u as DxListBoxLegacy,h as ListBoxLegacyCssClasses,p as ListBoxSelectedItemsChangedEvent,b as default};
