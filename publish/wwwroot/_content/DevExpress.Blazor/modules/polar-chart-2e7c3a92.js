import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t,C as r,L as s}from"./baseChart-9211a850.js";import{h as o}from"./settings-c0192d16.js";import{e as i}from"./custom-element-267f9a21.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./disposable-d2c2d283.js";import"./devextreme-widget-wrapper-33881f73.js";import"./utils-b5b2c8a9.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";import"./events-a8fe5872.js";import"./custom-events-helper-e7f279d3.js";import"./create-after-timeout-fn-executor-38b3d79d.js";import"./client-component-style-helper-195fa7c3.js";let l=class extends t{constructor(){super(...arguments),this._pointClickArgs=null}getWidgetTypeName(){return r.polarChartName}createWidgetSettingsConverter(){return new o}configureSelectionController(e){void 0!==e.seriesSelectionMode&&this.selectionController.setSeriesSelectionMode(e.seriesSelectionMode),super.configureSelectionController(e)}getLegendItemType(){return s.point}getChartOnPointClickHandler(){return e=>{this._pointClickArgs=e}}getChartOnSeriesClickHandler(){return e=>{var t;const r=e.target,s=null===(t=this._pointClickArgs)||void 0===t?void 0:t.target;this.onSeriesClick(r.index,null==s?void 0:s.data,null==s?void 0:s.tag),this._pointClickArgs=null,this.selectionController.selectSeriesAndPoint(r,s)}}};l=e([i("dxbl-polar-chart")],l);export{l as DxPolarChart};
