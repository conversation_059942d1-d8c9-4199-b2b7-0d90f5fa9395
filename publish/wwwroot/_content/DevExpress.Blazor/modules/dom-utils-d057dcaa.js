import{d as t}from"./dom-554d0cc7.js";import{b as e}from"./browser-3fc721b7.js";import{C as n}from"./css-classes-c63af734.js";let o=-1;function i(){return function(){if(o>-1)return o;const t=document.createElement("DIV");t.style.cssText="position: absolute; top: 0px; left: 0px; visibility: hidden; width: 200px; height: 150px; overflow: hidden; box-sizing: content-box",document.body.appendChild(t);const e=document.createElement("P");t.appendChild(e),e.style.cssText="width: 100%; height: 200px;";const n=e.offsetWidth;t.style.overflow="scroll";let i=e.offsetWidth;return n===i&&(i=t.clientWidth),o=(n-i)*window.devicePixelRatio,document.body.removeChild(t),o}()/window.devicePixelRatio}const r=-1;class s{constructor(t=-1,e=-1){this.width=t,this.height=e}static getDimentionValue(t,e){let n=r;if(e in t){const o=parseFloat(t[e]);n=Number.isNaN(o)?r:o}return n}isInitial(){return this.width===r&&this.height===r}static from(t){if(!t)return new s;const e=this.getDimentionValue(t,"width"),n=this.getDimentionValue(t,"height");return new s(e,n)}}function l(t){return e.Browser.IE?function(t){return null==t||e.Browser.IE&&null==t.parentNode?0:t.getBoundingClientRect().left+gt()}(t):e.Browser.Firefox&&e.Browser.Version>=3||e.Browser.WebKitFamily||e.Browser.Edge?u(t):window.getAbsolutePositionX_Other(t)}function u(t){if(null==t)return 0;return t.getBoundingClientRect().left+gt()}function c(t){return e.Browser.IE?function(t){return null==t||e.Browser.IE&&null==t.parentNode?0:t.getBoundingClientRect().top+pt()}(t):e.Browser.Firefox&&e.Browser.Version>=3||e.Browser.WebKitFamily||e.Browser.Edge?a(t):window.getAbsolutePositionY_Other(t)}function a(t){if(null==t)return 0;return t.getBoundingClientRect().top+pt()}function f(t,e,n){return t-=d(e,n)}function m(e,n){const o=function(e){const n=document.createElement("DIV");return n.style.top="0px",n.style.left="0px",n.visibility="hidden",n.style.position=t.DomUtils.getCurrentStyle(e).position,n}(e);"static"===o.style.position&&(o.style.position="absolute"),e.parentNode.appendChild(o);const i=n?l(o):function(t){return c(t)}(o);return e.parentNode.removeChild(o),i}function d(t,e){return m(t,e)}function h(t,e){try{let n;const o=g(t);if(!o){const e=p(t);if(!e)return!1;n=e.split(" ")}const i=e.split(" ");for(let t=i.length-1;t>=0;t--)if(o){if(-1===o.indexOf(i[t]))return!1}else if(n.indexOf(i[t])<0)return!1;return!0}catch(t){return!1}}function g(t){return t.classList?[].slice.call(t.classList):p(t).replace(/^\s+|\s+$/g,"").split(/\s+/)}function p(t){return"svg"===t.tagName?t.className.baseVal:t.className}function y(t,e){return e||(e=0),null!=t&&t.length>e?t[e]:null}function w(t,n,o){if(null!=t){const i=function(t,n){const o=n.toUpperCase();let i=null;return t&&(t.getElementsByTagName?(i=t.getElementsByTagName(o),0===i.length&&(i=t.getElementsByTagName(n))):t.all&&void 0!==t.all.tags&&(i=e.Browser.Netscape?t.all.tags[o]:t.all.tags(o))),i}(t,n);return y(i,o)}return null}let b=null;function S(){return b&&![...document.styleSheets].every((t=>t!==b))||(b=function(t){if(!t.createStyleSheet){const e=t.createElement("STYLE");return w(t,"HEAD",0).appendChild(e),e.sheet}try{return t.createStyleSheet()}catch(t){throw new Error("The CSS link limit (31) has been exceeded. Please enable CSS merging or reduce the number of CSS files on the page. For details, see https://supportcenter.devexpress.com/ticket/details/k18487/.")}}(document)),b}function C(t,e,n){for(;null!=t;){if("BODY"===t.tagName||"#document"===t.nodeName)return null;if(n(t,e))return t;t=t.parentNode}return null}function E(t,e,n){return C(t,e,n)}function I(t,e){return C(t,e,h)}function B(t,e){const n=[];for(let o=0;o<t.length;o++){const i=t[o];e&&!e(i)||n.push(i)}return n}function N(t){"string"==typeof t&&(t=document.querySelector(t)),t&&t.focus()}function D(t,e,n){t&&(t[e]=n)}function T(t,e){t&&(t.indeterminate=e)}function O(){document.getSelection().removeAllRanges()}function x(t){if(!t)return null;let e=null;if("string"==typeof t){const n=document.querySelector("#"+t);if(n)t=n;else try{const n=JSON.parse(t);n&&n.__internalId&&(e=n.__internalId)}catch(t){}}if(!e&&t.__internalId&&(e=t.__internalId),e){t=document.querySelector("["+("_bl_"+e)+"]")}return t&&t.tagName&&null!==t.parentNode||(t=null),t}function P(t,e,n){(t=x(t))&&D(t,e,n)}function F(t,e){t.dataset.tempUniqueId="tempUniqueId";try{e("[data-temp-unique-id]")}catch(t){}finally{delete t.dataset.tempUniqueId}}function v(t,e){t.removeAttribute?t.removeAttribute(e):t.removeProperty&&t.removeProperty(e)}function _(t,e,n){n?D(t,e,n):v(t,e)}function W(e,n){const o=n||t.DomUtils.getCurrentStyle(e);return t.DomUtils.pxToInt(o.marginLeft)+t.DomUtils.pxToInt(o.marginRight)}function A(e,n){const o=n||t.DomUtils.getCurrentStyle(e);return t.DomUtils.pxToInt(o.marginTop)+t.DomUtils.pxToInt(o.marginBottom)}function U(t,e){return L(t,e)+M(t,e)}function R(t,e){return V(t,e)+H(t,e)}function L(e,n){const o=n||t.DomUtils.getCurrentStyle(e);return parseInt(o.paddingLeft)+parseInt(o.paddingRight)}function V(e,n){const o=n||t.DomUtils.getCurrentStyle(e);return parseInt(o.paddingTop)+parseInt(o.paddingBottom)}function H(n,o){o||(o=e.Browser.IE&&9!==e.Browser.MajorVersion&&window.getComputedStyle?window.getComputedStyle(n):t.DomUtils.getCurrentStyle(n));let i=0;return"none"!==o.borderTopStyle&&(i+=parseFloat(o.borderTopWidth)),"none"!==o.borderBottomStyle&&(i+=parseFloat(o.borderBottomWidth)),i}function M(n,o){o||(o=e.Browser.IE&&window.getComputedStyle?window.getComputedStyle(n):t.DomUtils.getCurrentStyle(n));let i=0;return"none"!==o.borderLeftStyle&&(i+=parseFloat(o.borderLeftWidth)),"none"!==o.borderRightStyle&&(i+=parseFloat(o.borderRightWidth)),i}const q=window.requestAnimationFrame||function(t){t()},j=window.cancelAnimationFrame||function(t){};function k(t){return q(t)}const Y=function(t){this.requestFrame=t,this.cache=[[]],this.isInFrame=!1,this.frameTimestamp=null,this.isWaiting=!1,this.getBuffer=function(t){return t||(t=0),this.cache.length<=t&&(this.cache[t]=[]),this.cache[t]},this.execute=function(t,e){if(!this.isInFrame)return!1;let n=this.cache[e||0];return null===n?t(z,this.frameTimestamp):(n=this.getBuffer(e)).push(t),!0},this.runAll=function(t){this.isWaiting=!1,this.isInFrame=!0,this.frameTimestamp=t;for(let t=0;t<this.cache.length;t++){const e=this.cache[t];if(e)for(this.cache[t]=null;e.length;)e.shift()(z,this.frameTimestamp)}this.waitNextFrame()},this.waitNextFrame=function(){this.cache=[[]],this.isInFrame=!1,this.isWaiting=!1},this.requestExecution=function(t,e){const n=this;return new Promise((function(o){function i(e,n){o(t(e,n))}n.execute(i,e)||(n.getBuffer(e).push(i),!1===n.isWaiting&&(n.isWaiting=!0,n.requestFrame(n.runAll.bind(n))))}))}};let z=null;function K(t){const e=new Y(t);return e.requestExecution.bind(e)}const X=K((function(t){return z=k(t)})),J=K((function(t){return X((function(){setTimeout(t)}))}));function G(t){return X(t)}function $(t){return J(t)}let Q=[];const Z=50;function tt(t){Q.splice(Q.indexOf(t),1)}function et(e,n){const o=function(e,n,o){return function(){if(e.compareDocumentPosition(document.body)&window.Node.DOCUMENT_POSITION_DISCONNECTED)return!1;const i=t.DomUtils.getCurrentStyle(e);if("auto"===i.width)return!0;const r=Math.ceil(parseFloat(i.width))-U(e),l=Math.ceil(parseFloat(i.height))-R(e);if(o.width!==r||o.height!==l){var u=s.from(o);o.width=r,o.height=l,n(o,u)}return!0}}(e,n,{width:-1,height:-1});return rt(o),o}function nt(t,e){const n=function(t,e){let n=t.scrollHeight>t.clientHeight;return function(){if(t.compareDocumentPosition(document.body)&window.Node.DOCUMENT_POSITION_DISCONNECTED)return!1;const o=t.scrollHeight>t.clientHeight;return o!==n&&(e(o),n=o),!0}}(t,e);return rt(n),n}function ot(t){const e=function(t){let e=-1;return function(){const n=i();return e!==n&&(t(n),e=n),!0}}(t);return rt(e),e}function it(t,e){rt(function(t,e){return function(){return!(t.compareDocumentPosition(document.body)&window.Node.DOCUMENT_POSITION_DISCONNECTED&&(e(),1))}}(t,e))}function rt(t){0===Q.length?(Q.push(t),$(st)):Q.push(t)}function st(){Q=Q.filter((function(t){return t()})),Q.length>0&&setTimeout((function(){$(st)}),Z)}function lt(t,e){const n=[];for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.push({attr:t,value:e[t]});if(1===n.length)t.style[n[0].attr]=n[0].value;else{let o="";if(t.style.cssText){const n=t.style.cssText.split(";").map((function(t){return t.trim().split(":")}));for(let t=0;t<n.length;t++){const i=n[t];2===i.length&&void 0===e[i[0]]&&(o+=i[0]+":"+i[1].trim()+";")}}for(let t=0;t<n.length;t++){const e=n[t];""!==e.value&&(o+=e.attr+":"+e.value+";")}_(t,"style",o)}}function ut(t){return{inlineStyles:{},cssClassToggleInfo:{},className:p(t),attributes:{}}}function ct(t,e){if(void 0!==t.length){for(let n=0;n<t.length;n++)ct(t[n],e);return}const n=t;n._dxCurrentFrameElementStateInfo?e(n._dxCurrentFrameElementStateInfo):(e(n._dxCurrentFrameElementStateInfo=ut(n)),G((function(){!function(t,e){null===e.inlineStyles?v(t,"style"):lt(t,e.inlineStyles);for(const n in e.attributes)Object.prototype.hasOwnProperty.call(e,n)&&_(t,n,e.attributes[n]);const n=g(e);if(n){const o=e.cssClassToggleInfo,i=n.filter((function(t){return!1!==o[t]}));for(const t in o)Object.prototype.hasOwnProperty.call(o,t)&&o[t]&&-1===i.indexOf(t)&&i.push(t);const r=i.join(" ");r?function(t,e){"svg"===t.tagName?t.className.baseVal=e.trim():t.className=e.trim()}(t,r):v(t,"class")}}(n,n._dxCurrentFrameElementStateInfo),n._dxCurrentFrameElementStateInfo=null})))}function at(t,e){ct(t,(function(t){if(null===t.inlineStyles)t.inlineStyles=e;else for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t.inlineStyles[n]=e[n])}))}function ft(t){ct(t,(function(t){t.inlineStyles=null,t.cssClassToggleInfo[n.InvisibleOffScreen]=!1}))}function mt(t,e,n){ct(t,(function(t){t.cssClassToggleInfo[e]=n}))}function dt(t){return document.body.contains(t)}function ht(t,e){if(!t)return null;if(t.closest)return t.closest(e);do{if((t.matches||t.msMatchesSelector).call(t,e))return t;t=t.parentElement||t.parentNode}while(t&&"BODY"!==t.tagName);return null}function gt(){return e.Browser.Edge?document.body?document.body.scrollLeft:document.documentElement.scrollLeft:e.Browser.WebKitFamily?document.documentElement.scrollLeft||document.body.scrollLeft:document.documentElement.scrollLeft}function pt(){return e.Browser.WebKitFamily||e.Browser.Edge?e.Browser.MacOSMobilePlatform?window.pageYOffset:e.Browser.WebKitFamily&&document.documentElement.scrollTop||document.body.scrollTop:document.documentElement.scrollTop}function yt(t){if(t.attributes)for(let e=0;e<t.attributes.length;e++){const n=t.attributes[e].name;(n.startsWith("_bl_")||n.startsWith("id"))&&t.removeAttribute(n)}t.childNodes.forEach((t=>{yt(t)}))}function wt(t){return t+"px"}function bt(t){return Boolean(t.compareDocumentPosition(document.body)&window.Node.DOCUMENT_POSITION_DISCONNECTED)}function St(t){if(!t.style)return!1;return function(t){return"static"!==t.position||function(t){return!("none"===t.transform&&!t["will-change"].includes("transform"))||(!("none"===t.perspective&&!t["will-change"].includes("perspective"))||(!("none"===t.filter&&!t["will-change"].includes("filter"))||!!t.contain?.includes("paint")))}(t)}(window.getComputedStyle(t))}function Ct(t){return"svg"===t.tagName?t.getBoundingClientRect().width:t.offsetWidth}const Et=Object.freeze({__proto__:null,getDocumentClientWidth:function(){return 0===document.documentElement.clientWidth?document.body.clientWidth:document.documentElement.clientWidth},getDocumentClientHeight:function(){return e.Browser.Firefox&&window.innerHeight-document.documentElement.clientHeight>i()?window.innerHeight:e.Browser.Opera&&e.Browser.Version<9.6||0===document.documentElement.clientHeight?document.body.clientHeight:document.documentElement.clientHeight},setAbsoluteX:function(t,e){t.style.left=f(e,t,!0)+"px"},setAbsoluteY:function(t,e){t.style.top=f(e,t,!1)+"px"},getAbsolutePositionX:l,getAbsolutePositionY:c,prepareClientPosForElement:f,getPositionElementOffset:d,elementHasCssClass:h,getItemByIndex:y,getCurrentStyleSheet:S,getParentByPredicate:E,getParentByClassName:I,retrieveByPredicate:B,focusElement:N,setAttribute:D,setCheckInputIndeterminate:T,removeSelection:O,log:function(t){},ensureElement:x,setInputAttribute:P,querySelectorFromRoot:F,setOrRemoveAttribute:_,getLeftRightMargins:W,getTopBottomMargins:A,getLeftRightBordersAndPaddingsSummaryValue:U,getTopBottomBordersAndPaddingsSummaryValue:R,getLeftRightPaddings:L,getTopBottomPaddings:V,getVerticalBordersWidth:H,getHorizontalBordersWidth:M,CancelAnimationFrame:function(t){j(t)},RequestAnimationFrame:k,changeDom:G,calculateStyles:$,unsubscribeElement:tt,subscribeElementContentWidth:function(e,n,o){const i=function(e,n,o,i){return function(){if(e.compareDocumentPosition(document.body)&window.Node.DOCUMENT_POSITION_DISCONNECTED)return!1;const r=t.DomUtils.getCurrentStyle(e);if("auto"===r.width)return!0;const s=parseInt(r.width)-U(e);return o!==s&&n(o=s,i&&i.clientHeight),!0}}(e,n,-1,o);return rt(i),i},subscribeElementContentSize:et,subscribeElementVerticalScrollBarVisibility:nt,subscribeElementVerticalScrollBarWidth:ot,subscribeElementDisconnected:it,applyStyles:lt,createElementState:ut,setStyles:at,clearStyles:ft,toggleCssClass:mt,setCssClassName:function(t,e){ct(t,(function(t){t.cssClassToggleInfo={},t.className=e}))},elementIsInDOM:dt,findParentBySelector:ht,getDocumentScrollLeft:gt,getDocumentScrollTop:pt,eraseBlazorIdentificators:yt,toPx:wt,isRemovedFromDOM:bt,isContainingBlockForAbsolutelyPositionedElement:St,getElementOffsetWidth:Ct,default:{focusElement:N,setInputAttribute:P,setCheckInputIndeterminate:T}});export{O as A,nt as B,ot as C,it as D,L as E,wt as F,yt as G,E as H,$ as I,Ct as J,f as K,M as L,Et as M,k as R,I as a,et as b,G as c,R as d,x as e,U as f,S as g,A as h,bt as i,W as j,h as k,y as l,at as m,ft as n,i as o,St as p,dt as q,B as r,_ as s,mt as t,tt as u,D as v,ht as w,pt as x,gt as y,F as z};
