import{_ as t}from"./tslib.es6-d65164b3.js";import{S as e}from"./single-slot-element-base-01d93921.js";import{D as o}from"./keyboard-navigation-strategy-ea41c807.js";import{D as s}from"./root-keyboard-strategy-d1e519a3.js";import{R as r}from"./constants-ed9663d1.js";import{n as a}from"./property-4ec0b52d.js";import{e as i}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./key-ffa272aa.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./focushelper-2eea96ca.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./css-classes-c63af734.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./devices-17b9ba08.js";import"./menu-keyboard-strategy-7c257fd3.js";import"./popup-355ecaa4.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./portal-b3727c25.js";import"./constants-a4904a3f.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./capture-manager-2454adc2.js";import"./nameof-factory-64d95f5b.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./constants-ed56e953.js";import"./constants-3209ffde.js";import"./modal-keyboard-strategy-05da34d9.js";import"./dropdown-menu-keyboard-strategy-60d36909.js";let p=class extends e{constructor(){super(),this._keyboardNavigator=null,this.loading=!1}willUpdate(t){t.has("loading")&&!this.loading&&this.initializeNavigator()}initializeNavigator(){this._keyboardNavigator||(this._keyboardNavigator=this.querySelector(`:scope > ${o}`)),this._keyboardNavigator.initialized||this._keyboardNavigator.initialize(this,new s(this._keyboardNavigator,this))}};t([a({type:Boolean,attribute:"loading"})],p.prototype,"loading",void 0),p=t([i(r.Ribbon)],p);const m=p;export{m as default};
