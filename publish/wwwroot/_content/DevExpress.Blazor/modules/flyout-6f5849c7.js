import{_ as t}from"./tslib.es6-d65164b3.js";import{C as e,D as i,B as o,e as s,f as a,a as n,b as r,V as l,F as c,g as h,h as m}from"./popup-355ecaa4.js";import{P as d}from"./point-e4ec110e.js";import{b as p}from"./layouthelper-67dd777a.js";import{x as u}from"./lit-element-462e7ad3.js";import{i as f}from"./query-44b9267f.js";import{n as g}from"./property-4ec0b52d.js";import{e as b}from"./custom-element-267f9a21.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./constants-7c047c0d.js";import"./branch-aebd078a.js";import"./eventhelper-8bcec49f.js";import"./logicaltreehelper-67db40f1.js";import"./portal-b3727c25.js";import"./data-qa-utils-8be7c726.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./custom-events-helper-e7f279d3.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./key-ffa272aa.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./dom-utils-d057dcaa.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";var y,j;!function(t){t[t.TopStart=1]="TopStart",t[t.Top=2]="Top",t[t.TopEnd=4]="TopEnd",t[t.RightStart=8]="RightStart",t[t.Right=16]="Right",t[t.RightEnd=32]="RightEnd",t[t.BottomStart=64]="BottomStart",t[t.Bottom=128]="Bottom",t[t.BottomEnd=256]="BottomEnd",t[t.LeftStart=512]="LeftStart",t[t.Left=1024]="Left",t[t.LeftEnd=2048]="LeftEnd"}(j||(j={}));class w extends e{constructor(t,e,i,o,s,a){super(e,i,o,s,a),this.flyoutPosition=t}}const P="dxbl-flyout";let S=y=class extends i{get actualDistance(){var t;return null!==(t=this.flyoutDistance)&&void 0!==t?t:2}get actualOffset(){var t;return null!==(t=this.flyoutOffset)&&void 0!==t?t:0}get branchType(){return o.DropDown}constructor(){super(),this.slotChangedHandler=this.handleSlotChange.bind(this),this.dialog=null,this.flyoutDistance=null,this.flyoutOffset=null,this.calcStartAlignment=()=>0,this.calcEndAlignment=(t,e)=>e-t,this.calcAlignment=(t,e)=>e/2-t/2,this.placement=s.Custom,this.fitToRestriction=!0,this.flyoutPosition=0}get child(){return this.root}processCustomPlacement(t,e){return this.calcCustomPlacements(t,e)}calcCustomPlacements(t,e){const i=[];if(!this.dialog||!this.dialog.arrow)return i;const o=this.getBounds(e),s=this.getBounds(t),a=this.dialog.arrow.arrowSize;return this.flyoutPosition&j.Bottom&&i.push(this.calcBottomPlacement(o,a,this.calcAlignment(s.width,o.width),j.Bottom)),this.flyoutPosition&j.BottomStart&&i.push(this.calcBottomPlacement(o,a,this.calcStartAlignment(),j.BottomStart)),this.flyoutPosition&j.BottomEnd&&i.push(this.calcBottomPlacement(o,a,this.calcEndAlignment(s.width,o.width),j.BottomEnd)),this.flyoutPosition&j.Top&&i.push(this.calcTopPlacement(s,a,this.calcAlignment(s.width,o.width),j.Top)),this.flyoutPosition&j.TopStart&&i.push(this.calcTopPlacement(s,a,this.calcStartAlignment(),j.TopStart)),this.flyoutPosition&j.TopEnd&&i.push(this.calcTopPlacement(s,a,this.calcEndAlignment(s.width,o.width),j.TopEnd)),this.flyoutPosition&j.Left&&i.push(this.calcLeftPlacement(s,a,this.calcAlignment(s.height,o.height),j.Left)),this.flyoutPosition&j.LeftStart&&i.push(this.calcLeftPlacement(s,a,this.calcStartAlignment(),j.LeftStart)),this.flyoutPosition&j.LeftEnd&&i.push(this.calcLeftPlacement(s,a,this.calcEndAlignment(s.height,o.height),j.LeftEnd)),this.flyoutPosition&j.Right&&i.push(this.calcRightPlacement(o,a,this.calcAlignment(s.height,o.height),j.Right)),this.flyoutPosition&j.RightStart&&i.push(this.calcRightPlacement(o,a,this.calcStartAlignment(),j.RightStart)),this.flyoutPosition&j.RightEnd&&i.push(this.calcRightPlacement(o,a,this.calcEndAlignment(s.height,o.height),j.RightEnd)),i}calcLeftPlacement(t,e,i,o){const s=new d(-t.width-e.width-this.actualDistance,i+this.actualOffset);return new w(o,s,a.Horizontal,!0,n.Near,r.bottom)}calcTopPlacement(t,e,i,o){const s=new d(i+this.actualOffset,-t.height-e.height-this.actualDistance);return new w(o,s,a.Vertical,!0,n.Near,r.top)}calcRightPlacement(t,e,i,o){const s=new d(t.width+e.width+this.actualDistance,i+this.actualOffset);return new w(o,s,a.Horizontal,!1,n.Far,r.bottom)}calcBottomPlacement(t,e,i,o){const s=new d(i+this.actualOffset,t.height+e.height+this.actualDistance);return new w(o,s,a.Vertical,!1,n.Near,r.bottom)}processBestTranslationResults(t,e,i,o,s,a,n,r,l){if(super.processBestTranslationResults(t,e,i,o,s,a,n,r,l),!this.dialog||!this.dialog.arrow)return;const c=this.calcArrowAlignment(a,n,r);this.dialog.arrowAlignment=c;const h=this.dialog.arrow;h.position=this.getArrowPosition(h,e,i,o,r,null==l?void 0:l.flyoutPosition),h.alignment=c}getArrowPosition(t,e,i,o,s,n){const r=s===a.Vertical,c=r?t.arrowSize.width:t.arrowSize.height,h=p.rectToRange(i,r),m=p.rectToRange(e,r),d=p.rectToRange(o,r);let u=m.start,f=m.end;this.fitToRestriction&&(m.start<=d.start?(u=d.start,f=u+m.size):m.end>=d.end&&(u=d.end-m.size,f=u+m.size));const[g,b]=[Math.max(u,h.start),Math.min(f,h.end)];let y;y=n&(j.RightStart|j.RightEnd|j.LeftStart|j.LeftEnd)?h.start:h.start+h.size/2;const w=this.alignWithinBoundaries(y-c/2,g,b),P=this.calculateArrowOffsetFromEdge(h,c,m);return r?new l(Math.min(Math.max(w-u,P),e.width-P-c),0):new l(0,Math.min(Math.max(w-f,P-e.height),-P-c))}calcArrowAlignment(t,e,i){return i===a.Vertical?e===r.bottom?c.bottom:c.top:t===n.Near?c.start:c.end}alignWithinBoundaries(t,e,i){return e>=i?(e+i)/2:Math.min(Math.max(t,e),i)}calculateArrowOffsetFromEdge(t,e,i){const o=Math.min(t.size-e,t.size/2-e/2);return Math.max(y.DefaultArrowOffsetFromEdge,Math.min(o,.2*i.size))}renderTemplate(){return u`
            <dxbl-branch
                id="branch"
                branch-id="${this.branchId}"
                parent-branch-id="${this.parentBranchId}"
                type="${o.Flyout}">
                <dxbl-flyout-root
                    id="root"
                    style="${this.rootCssStyle}">
                    ${this.renderDefaultSlot()}
                    ${this.renderBridgeSlot()}
                </dxbl-flyout-root>
            </dxbl-branch>
        `}renderDefaultSlot(){return u`<slot @slotchange="${this.slotChangedHandler}"></slot>`}handleSlotChange(t){const e=t.target.assignedNodes();this.dialog=e.find((t=>t instanceof h)),this.dialog&&(this.dialog.animationEnabled=this.animationEnabled),this.reposition()}updated(t){super.updated(t),this.dialog&&t.has("animationEnabled")&&(this.dialog.animationEnabled=this.animationEnabled)}createKeyboardNavigationStrategy(){return new m(this.keyboardNavigator,this)}};S.DefaultArrowOffsetFromEdge=8,t([f("#root",!0)],S.prototype,"root",void 0),t([g({type:Number,attribute:"flyout-position"})],S.prototype,"flyoutPosition",void 0),t([g({type:Number,attribute:"flyout-distance"})],S.prototype,"flyoutDistance",void 0),t([g({type:Number,attribute:"flyout-offset"})],S.prototype,"flyoutOffset",void 0),S=y=t([b(P)],S);const E={DxFlyout:S,dxFlyoutTagName:P};export{w as CustomFlyoutPlacement,S as DxFlyout,j as FlyoutPosition,E as default,P as dxFlyoutTagName};
