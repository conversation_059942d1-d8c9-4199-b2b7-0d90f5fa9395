import{_ as e}from"./tslib.es6-d65164b3.js";import{d as t}from"./dom-554d0cc7.js";import{C as s}from"./css-classes-c63af734.js";import{n as o}from"./property-4ec0b52d.js";import{S as a}from"./single-slot-element-base-01d93921.js";import{C as r}from"./custom-events-helper-e7f279d3.js";class i{}class n extends CustomEvent{constructor(e){super(n.eventName,{detail:e,bubbles:!0,composed:!0,cancelable:!0})}static create(){return new n(new i)}}n.eventName="dxbl-grid-layout-root-element-base.contentchanged",r.register(n.eventName,(e=>e.detail));class l extends a{constructor(){super(...arguments),this.layoutInfo=""}willUpdate(e){e.has("layoutInfo")&&this.applyLayout()}applyLayout(){if(this.layoutInfo){const e=JSON.parse(this.layoutInfo);for(const t in e)this.style.setProperty(t,`${e[t]}`)}}}e([o({attribute:"layout-info"})],l.prototype,"layoutInfo",void 0);class c extends l{createRenderRoot(){return this.applyLayout(),super.createRenderRoot()}contentChanged(){this.makeChildrenVisible(),this.dispatchEvent(n.create())}restoreState(){super.restoreState(),this.makeChildrenVisible()}makeChildrenVisible(){t.DomUtils.removeClassName(this,s.Invisible)}}export{l as D,c as a};
