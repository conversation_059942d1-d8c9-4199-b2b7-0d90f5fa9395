import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{L as s}from"./layouthelper-67dd777a.js";import{d as n}from"./define-custom-element-7c2e65e2.js";import{C as i}from"./css-classes-c63af734.js";import{C as a}from"./custom-events-helper-e7f279d3.js";import{x as l}from"./lit-element-462e7ad3.js";import{n as r}from"./property-4ec0b52d.js";import{t as o}from"./state-c294470d.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./logicaltreehelper-67db40f1.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";class h{}class c extends CustomEvent{constructor(){super(c.eventName,{detail:new h,bubbles:!0})}}c.eventName="dxbl:loading-panel.click",a.register(c.eventName,(e=>e.detail));class d extends t{constructor(){super(...arguments),this.attachPanelHandler=this.attachPanel.bind(this),this.resizePanelHandler=this.resizePanel.bind(this),this.clickPanelHandler=this.clickPanel.bind(this),this.panel=null,this.targetElement=null,this.targetResizeObserver=null,this.panelItems=[],this.positionTarget=null,this.panelVisible=!1,this.blockedContent=!1,this.hasContent=!1,this.zIndex=null,this.width="unset",this.height="unset",this.translateState="translate(0px; 0px)",this.trackingInterval=null,this.targetCurrentRect=null}render(){return l`
            <style>
                :host {
                    display: ${this.getDisplayProp()};
                    position: ${this.hasContent?"relative":"fixed"};
                    pointer-events: ${this.blockedContent||this.hasContent?"unset":"none"};
                    width: ${this.hasContent?"var(--dxbl-loading-panel-width)":this.width};
                    height: ${this.hasContent?"var(--dxbl-loading-panel-height)":this.height};
                    transform: ${this.hasContent?"none":this.translateState};
                    z-index: ${this.panelVisible&&this.zIndex&&!this.hasContent?this.zIndex:"auto"};
                }
            </style>
            <slot></slot>
        `}connectedOrContentChanged(){super.connectedOrContentChanged(),this.setAttribute(i.Loaded,"")}connectedCallback(){super.connectedCallback(),this.hasContent||window.addEventListener("resize",this.resizePanelHandler),this.panel=document.querySelector(`#${this.id}`),this.addEventListener("click",this.clickPanelHandler)}disconnectedCallback(){var e;super.disconnectedCallback(),this.targetElement&&(null===(e=this.targetResizeObserver)||void 0===e||e.unobserve(this.targetElement),this.unsubscribeTargetScroll()),window.removeEventListener("resize",this.resizePanelHandler),this.removeEventListener("click",this.clickPanelHandler),this.disablePersistentAttaching()}updated(e){super.updated(e),e.has("positionTarget")&&(this.setTargetElement(),this.processPanelElements(),this.attachPanel(),this.resizePanel()),e.has("panelVisible")&&(!this.targetElement&&this.panelVisible&&this.setTargetElement(),this.attachPanel(),this.resizePanel(),this.checkBlockPanelTabIndexes()),e.has("blockedContent")&&this.checkBlockPanelTabIndexes()}setTargetElement(){var e;if(this.positionTarget){if(this.targetElement=document.querySelector(this.positionTarget),!this.targetElement)return;this.targetCurrentRect=this.targetElement.getBoundingClientRect(),this.subscribeTargetResize(),this.subscribeTargetScroll(),this.enablePersistentAttaching()}else this.targetElement&&(null===(e=this.targetResizeObserver)||void 0===e||e.unobserve(this.targetElement),this.targetElement=null)}attachPanel(){!this.hasContent&&this.targetElement&&this.panel&&requestAnimationFrame((()=>{const e=this.targetElement.getBoundingClientRect();this.translateState=`translate(${e.left+this.scrollLeft}px, ${e.top+this.scrollTop}px)`}))}resizePanel(){this.targetElement&&this.panel&&requestAnimationFrame((()=>{const e=this.targetElement.getBoundingClientRect();this.width=e.width+"px",this.height=e.height+"px"}))}clickPanel(){this.panelVisible&&this.dispatchEvent(new c)}processPanelElements(){this.targetElement&&(this.checkAndAddElementTabIndex(this.targetElement),this.processPanelChildElements(this.targetElement))}checkAndAddElementTabIndex(e){this.panelItems.map((e=>e.element)).indexOf(e)<0&&this.panelItems.push({element:e,tabIndex:e.hasAttribute("tabIndex")?e.tabIndex:null})}processPanelChildElements(e){const t=e.childNodes;for(let e=0;e<t.length;e++){const s=t[e];s instanceof HTMLElement&&(this.checkAndAddElementTabIndex(s),this.processPanelChildElements(s))}}checkBlockPanelTabIndexes(){this.panelItems.length&&this.panelItems.forEach((e=>{this.blockedContent&&this.panelVisible?e.element.tabIndex=-1:null!==e.tabIndex?e.element.tabIndex=e.tabIndex:e.element.removeAttribute("tabIndex")}))}subscribeTargetResize(){this.targetResizeObserver=new ResizeObserver((()=>this.resizePanel())),this.targetResizeObserver.observe(this.targetElement)}enablePersistentAttaching(){this.trackingInterval=setInterval((()=>{const e=this.targetElement.getBoundingClientRect();e.top===this.targetCurrentRect.top&&e.left===this.targetCurrentRect.left||(this.targetCurrentRect=e,this.attachPanel())}),0)}disablePersistentAttaching(){this.trackingInterval&&clearInterval(this.trackingInterval)}subscribeTargetScroll(){this.getTargetRootElements().forEach((e=>e.addEventListener("scroll",this.attachPanelHandler)))}unsubscribeTargetScroll(){this.getTargetRootElements().forEach((e=>e.removeEventListener("scroll",this.attachPanelHandler)))}getTargetRootElements(){return[...s.getRootPath(this.targetElement)].filter((e=>e instanceof HTMLElement)).map((e=>e))}getDisplayProp(){return this.hasContent||this.positionTarget?this.hasContent||this.panelVisible?"inline-block":"none":"block"}}e([r({type:String,attribute:"position-target",reflect:!0})],d.prototype,"positionTarget",void 0),e([r({type:Boolean,attribute:"panel-visible"})],d.prototype,"panelVisible",void 0),e([r({type:Boolean,attribute:"blocked-content"})],d.prototype,"blockedContent",void 0),e([r({type:Boolean,attribute:"has-content"})],d.prototype,"hasContent",void 0),e([r({type:Number,attribute:"z-index"})],d.prototype,"zIndex",void 0),e([o()],d.prototype,"width",void 0),e([o()],d.prototype,"height",void 0),e([o()],d.prototype,"translateState",void 0),n(customElements,"dxbl-loading-panel",d);export{c as LoadingPanelClickEvent,h as LoadingPanelClickEventContext,d as default};
