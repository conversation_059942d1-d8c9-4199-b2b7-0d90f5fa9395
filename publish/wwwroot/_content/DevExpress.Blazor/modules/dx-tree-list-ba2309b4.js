import{G as e,a as t,b as r,c as s,d as o,e as a,f as i,g as n,h as c,i as d,D as l,j as p}from"./dx-grid-444da71f.js";import{k as m}from"./key-ffa272aa.js";import"./dx-html-element-base-3262304e.js";import"./data-qa-utils-8be7c726.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./tslib.es6-d65164b3.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./eventhelper-8bcec49f.js";import"./constants-7c047c0d.js";import"./devices-17b9ba08.js";import"./dx-license-30fd02d1.js";import"./css-classes-c63af734.js";import"./dx-ui-handlers-bridge-c2148178.js";import"./dx-scroll-viewer-da0fb41c.js";import"./scroll-viewer-css-classes-e724f203.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./focushelper-2eea96ca.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./dom-utils-d057dcaa.js";import"./custom-events-helper-e7f279d3.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./dx-virtual-scroll-viewer-f4a3bc9e.js";import"./thumb-31d768d7.js";import"./dx-check-internal-a6f9ec37.js";import"./dragging-helper-863a69d5.js";import"./popup-355ecaa4.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./property-4ec0b52d.js";import"./custom-element-267f9a21.js";import"./lit-element-462e7ad3.js";import"./portal-b3727c25.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./capture-manager-2454adc2.js";import"./nameof-factory-64d95f5b.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./grid-scroll-utils-a8c65cf1.js";import"./svg-utils-3c1b4ead.js";class u{}u.TreeNodeExpandControlsCell=`${e.TableCell}.dxbl-grid-tree-node-expand-controls-cell`,u.TreeNodeExpandControlsContainer=`${u.TreeNodeExpandControlsCell} > .dxbl-grid-tree-node-expand-controls-container`,u.TreeNodeExpandButton=`${u.TreeNodeExpandControlsContainer} > .dxbl-grid-tree-node-expand-button`;class h extends t{getDropPosition(){if(!this.draggableRowContext)return r.Append;const{currentCursorPosition:e,mouseOverElementRect:s,mouseOverElement:o}=this.draggableRowContext;return o&&s?t.isEmptyRow(o)?r.Append:t.isDataRow(o)?this.isRestrictedArea(e.y,s)||h.isCursorInMiddle(e.y,s)?r.Inside:this.isPositionalDropAllowed()?t.getDropPositionByCursor(e.y,s):r.Append:r.Inside:r.Inside}isRestrictedArea(e,t){const{top:r,bottom:s}=h.getBounds(t);return this.isRestrictedTopArea()&&e>r||this.isRestrictedBottomArea()&&e<s}static isCursorInMiddle(e,t){const{top:r,bottom:s}=h.getBounds(t);return e>r&&e<s}static getBounds(e){const r=t.calculateVerticalCenter(e),s=e.height/4;return{top:r-s,bottom:r+s}}}class g{static isExpandCollapseHotkey(e,t){const r=m.KeyUtils.getEventKeyCode(e);return(r===m.KeyCode.Right||r===m.KeyCode.Left)&&(t?e.metaKey:e.ctrlKey)}}class j extends s{constructor(e,t,r){super(e,t,r)}handleArrowLeftKeyDown(e){return g.isExpandCollapseHotkey(e,this.isMacOSPlatform)?(this.performShortcutEvent(e),!0):super.handleArrowLeftKeyDown(e)}handleArrowRightKeyDown(e){return g.isExpandCollapseHotkey(e,this.isMacOSPlatform)?(this.performShortcutEvent(e),!0):super.handleArrowRightKeyDown(e)}}class f extends o{constructor(e,t,r){super(e,t,r)}handleArrowLeftKeyDown(e){return g.isExpandCollapseHotkey(e,this.isMacOSPlatform)?(this.performShortcutEvent(e),!0):super.handleArrowLeftKeyDown(e)}handleArrowRightKeyDown(e){return g.isExpandCollapseHotkey(e,this.isMacOSPlatform)?(this.performShortcutEvent(e),!0):super.handleArrowRightKeyDown(e)}}class b extends a{constructor(e,t){super(e,t)}createTableBodyItemStrategy(e){return this.isVirtualScrollingEnabled(e)?new x(this,this.grid,e):new w(this,this.grid,e)}}class w extends i{constructor(e,t,r){super(e,t,r)}createDataRowStrategy(e){return new j(this,this.grid,e)}createInplaceEditRowStrategy(e){return new f(this,this.grid,e)}}class x extends n{constructor(e,t,r){super(e,t,r)}createDataRowStrategy(e){return new j(this,this.grid,e)}createInplaceEditRowStrategy(e){return new f(this,this.grid,e)}}class y extends c{constructor(e){super(e)}createDataTableStrategy(e){return new b(this.grid,e)}}class C extends d{constructor(e){super(e)}isDataCellInteractiveElement(e){return!!e&&(e.matches(u.TreeNodeExpandControlsCell)||e.matches(u.TreeNodeExpandButton))}}class E extends l{constructor(){super()}getTagName(){return p}get bypassNonInlineHoverTitleElementChildSelector(){return u.TreeNodeExpandControlsContainer}allowInplaceEditingOnCellElementClick(e){return!e.matches(`${u.TreeNodeExpandButton} *`)}createRowDraggingHelper(){return new h(this)}createRootKeyboardNavigationStrategy(){return new y(this)}createEditorManager(){return new C(this)}}customElements.define(p,E);const v={loadModule:function(){}};export{E as DxTreeList,v as default};
