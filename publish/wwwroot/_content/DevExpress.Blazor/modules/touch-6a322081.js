import{c as e,g as o}from"./_commonjsHelpers-41cdd1e7.js";import{b as n,c as t}from"./common-48ec40e2.js";var r=e((function(e,o){Object.defineProperty(o,"__esModule",{value:!0}),o.TouchUtils=void 0;var r=function(){function e(){}return e.onEventAttachingToDocument=function(o,t){return!n.Browser.MacOSMobilePlatform||!e.isTouchEventName(o)||(e.documentTouchHandlers[o]||(e.documentTouchHandlers[o]=[]),e.documentTouchHandlers[o].push(t),e.documentEventAttachingAllowed)},e.isTouchEventName=function(e){return n.Browser.WebKitTouchUI&&(e.indexOf("touch")>-1||e.indexOf("gesture")>-1)},e.isTouchEvent=function(e){return n.Browser.WebKitTouchUI&&t.isDefined(e.changedTouches)},e.getEventX=function(e){return n.Browser.IE?e.pageX:e.changedTouches[0].pageX},e.getEventY=function(e){return n.Browser.IE?e.pageY:e.changedTouches[0].pageY},e.touchMouseDownEventName=n.Browser.WebKitTouchUI?"touchstart":n.Browser.Edge&&n.Browser.MSTouchUI&&window.PointerEvent?"pointerdown":"mousedown",e.touchMouseUpEventName=n.Browser.WebKitTouchUI?"touchend":n.Browser.Edge&&n.Browser.MSTouchUI&&window.PointerEvent?"pointerup":"mouseup",e.touchMouseMoveEventName=n.Browser.WebKitTouchUI?"touchmove":n.Browser.Edge&&n.Browser.MSTouchUI&&window.PointerEvent?"pointermove":"mousemove",e.msTouchDraggableClassName="dxMSTouchDraggable",e.documentTouchHandlers={},e.documentEventAttachingAllowed=!0,e}();o.TouchUtils=r}));o(r);export{r as t};
