class e extends Event{constructor(){super(e.eventName,{bubbles:!0,composed:!1,cancelable:!0})}}e.eventName="dxactivetabchanched";class t{constructor(e){this.id=e}}class s extends CustomEvent{constructor(e){super(s.eventName,{detail:new t(e),bubbles:!0})}}s.eventName="dxbl-ribbon-item.resized";class a{constructor(e,t){this.id=e,this.adaptivePriority=t}}class n extends CustomEvent{constructor(e,t){super(n.eventName,{detail:new a(e,t),bubbles:!0,composed:!0})}}n.eventName="dxbl-ribbon-item.updated";export{e as A,s as R,n as a};
