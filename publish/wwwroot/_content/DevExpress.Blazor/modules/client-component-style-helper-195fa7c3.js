class e{constructor(){}static getInstance(){return e.instance||(e.instance=new e),e.instance}get palette(){return this.getDefaultClientComponentPalette()}getDefaultClientComponentPalette(){const t=[e.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,e.<PERSON><PERSON><PERSON><PERSON>,e.<PERSON><PERSON><PERSON><PERSON><PERSON>,e.<PERSON><PERSON><PERSON><PERSON>,e.infoColor<PERSON>ey],o=window.getComputedStyle(document.body);return t.map((e=>this.getPaletteColor(e,o)))}getPaletteColor(t,o){return o.getPropertyValue(e.dxblClientComponentPrefix+t)}}e.dxblPrefix="--dxbl-",e.dxblClientComponentPrefix=e.dxblPrefix+"client-component-palette-",e.primaryColor<PERSON>ey="primary",e.successColorKey="success",e.danger<PERSON>olor<PERSON><PERSON>="danger",e.warningColorKey="warning",e.infoColorKey="info";export{e as C};
