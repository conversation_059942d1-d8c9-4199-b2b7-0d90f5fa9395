import{x as e,y as t,p as o,z as s}from"./popup-355ecaa4.js";import{d as r}from"./constants-a4904a3f.js";import{D as a}from"./popupportal-bbd2fea0.js";import{_ as i}from"./tslib.es6-d65164b3.js";import{e as p}from"./custom-element-267f9a21.js";import{s as n,x as m}from"./lit-element-462e7ad3.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./property-4ec0b52d.js";import"./eventhelper-8bcec49f.js";import"./logicaltreehelper-67db40f1.js";import"./portal-b3727c25.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./custom-events-helper-e7f279d3.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./key-ffa272aa.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./dom-utils-d057dcaa.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";const c="dxbl-modal-root";let d=class extends n{constructor(){super(...arguments),this.slotChangedHandler=this.handleSlotChange.bind(this)}render(){return m`
            <slot @slotchange=${this.slotChangedHandler}></slot>
        `}handleSlotChange(o){const s=o.target.assignedNodes().find((t=>t instanceof e)),r=this.closest(t);r&&(s?(r.notifyDialogConnected(s),r.notifyRootConnected()):(r.notifyDialogDisconnected(),r.notifyRootDisconnected()))}};d=i([p(c)],d);const l=[o,s,t,c,r,a];function j(e){if(!e)throw new Error("failed");return e}const f={getReference:j,registeredComponents:l};export{f as default,j as getReference,l as registeredComponents};
