import{E as e}from"./eventhelper-8bcec49f.js";import{F as t}from"./focushelper-2eea96ca.js";import{d as r}from"./constants-a4904a3f.js";class n{constructor(){this.lockCount=0}get IsLocked(){return this.lockCount>0}lock(){this.lockCount+=1}unlock(){this.IsLocked&&(this.lockCount-=1)}reset(){this.lockCount=0}lockOnce(){this.IsLocked||this.lock()}doLockedAction(e){this.lock();try{e()}finally{this.unlock()}}async doLockedAsyncAction(e){this.lock();try{await e()}finally{this.unlock()}}doIfNotLocked(e){this.IsLocked||e()}doLockedActionIfNotLocked(e){this.doIfNotLocked((()=>this.doLockedAction(e)))}async doLockedAsyncActionIfNotLocked(e){this.doIfNotLocked((async()=>await this.doLockedAsyncAction(e)))}}class i{constructor(){this.root=new o(null,!1)}get hasCapture(){return this.root.hasChildren}get captured(){var e,t;return null!==(t=null===(e=this.root.searchCaptured())||void 0===e?void 0:e.element)&&void 0!==t?t:null}get count(){return this.root.count()}add(e){var t;const r=this.root.searchCaptured();r&&r.element!==e&&(r.captured=!1,null===(t=r.element)||void 0===t||t.lostCapture());this.root.tryAdd(new o(e,!0))&&e.gotCapture()}remove(e){this.root.tryRemove(new o(e,!1))&&e.lostCapture()}isChild(e,t){const r=this.root.search(e);return null!==r&&null!==r.search(t)}nextCapture(e){var t,r;if(!this.root.captured)if(e.parentBranchId){const n=this.root.search(e.parentBranchId);if(n){const i=n.getNextChild(e);i?(i.captured=!0,null===(t=i.element)||void 0===t||t.gotCapture()):(n.captured=!0,null===(r=n.element)||void 0===r||r.gotCapture())}}else{const t=this.root.getNextChild(e);t&&(t.captured=!0)}}}class o{constructor(e,t){this.element=e,this.captured=t,this.children=[]}get isRoot(){return null===this.element}get hasChildren(){return this.children.length>0}get branchId(){var e,t;return null!==(t=null===(e=this.element)||void 0===e?void 0:e.branchId)&&void 0!==t?t:""}get parentBranchId(){var e,t;return null!==(t=null===(e=this.element)||void 0===e?void 0:e.parentBranchId)&&void 0!==t?t:null}tryAdd(e){if(!e.parentBranchId&&this.isRoot||e.parentBranchId===this.branchId){const t=this.children.find((t=>t.branchId===e.branchId));return t?t.captured=!0:this.children.push(e),!0}for(const t of this.children){if(t.tryAdd(e))return!0}return!1}tryRemove(e){const t=this.children.find((t=>t.branchId===e.branchId));if(t){const r=this.children.indexOf(e);if(this.children.splice(r,1),t.captured)if(null!=t.parentBranchId)this.captured=!0;else if(this.children.length>0){this.children[this.children.length-1].captured=!0}return!0}for(const t of this.children){if(t.tryRemove(e))return!0}return!1}count(){let e=0;if(this.children.length>0)for(const t of this.children)e+=t.count();return this.isRoot||(e+=1),e}search(e){if(this.branchId===e)return this;for(let t=0;t<this.children.length;t++){const r=this.children[t].search(e);if(r)return r}return null}searchCaptured(){if(this.captured)return this;for(let e=0;e<this.children.length;e++){const t=this.children[e].searchCaptured();if(t)return t}return null}getNextChild(e){if(0===this.children.length)return null;for(let t=this.children.length-1;t>=0;t--){const r=this.children[t];if(r.element!==e)return r}return null}}class s extends CustomEvent{constructor(){super(s.eventName)}}s.eventName="capture-interaction-prevent-ended";class c extends EventTarget{constructor(){super(...arguments),this.preventNextPointerInteraction=!1}reset(){this.preventNextPointerInteraction=!1,this.dispatchEvent(new s)}get isPreventing(){return this.preventNextPointerInteraction}prevent(){this.preventNextPointerInteraction=!0}}class a{constructor(){this._globalLocker=new n,this.preventNextPointerInteraction=new c}get isInteractionPreventing(){return this.preventNextPointerInteraction.isPreventing}subscribeInteractionPreventEnd(e){this.preventNextPointerInteraction.addEventListener(s.eventName,e)}unsubscribeInteractionPreventEnd(e){this.preventNextPointerInteraction.removeEventListener(s.eventName,e)}async processCapturedElementPointer(e,t){const r=this.getCapturedInteractionLocker(e),n=new h;await r.doLockedAsyncActionIfNotLocked((async()=>{this.preventNextPointerInteraction.reset(),this.capturedProcessPromise=t(n),await this.capturedProcessPromise,this.capturedProcessPromise=void 0})),n.handled&&n.nextInteractionHandled&&this.preventNextPointerInteraction.prevent()}async processCapturedElementKeyboard(e,t){const r=this.getCapturedInteractionLocker(e);await r.doLockedAsyncActionIfNotLocked((async()=>{const e=new h;await t(e)}))}checkInteractionsPreventing(e,t,r,n=!1){const i=this.getCapturedInteractionLocker(e),o=this.preventEventIfLocked(i,t);return i.doIfNotLocked((()=>{this.preventNextPointerInteraction.isPreventing&&(n&&this.preventNextPointerInteraction.reset(),r())})),o||this.preventNextPointerInteraction.isPreventing}async waitForCaptureProcessing(){this.capturedProcessPromise&&await this.capturedProcessPromise}endInteractionIfNotLocked(e){this.getCapturedInteractionLocker(e).doIfNotLocked((()=>this.preventNextPointerInteraction.reset()))}getCapturedInteractionLocker(e){return e?e.locker:this._globalLocker}preventEventIfLocked(t,r){return!!t.IsLocked&&(e.markHandled(r,!1),!0)}}class h{constructor(){this.nextInteractionHandled=!1,this.handled=!1}}class d{constructor(){this.captureTree=new i,this.captureProcessor=new a,this.restoreFocusOnPreventNextPointerInteractionElement=null,this.capturedInPointerUp=!1,this.capturedInPointerDown=!1,this.pointerDownHandler=this.handlePointerDown.bind(this),this.pointerUpHandler=this.handlePointerUp.bind(this),this.clickHandler=this.handleClick.bind(this),this.keyDownHandler=this.handleKeyDown.bind(this),this.disposeCheckHandler=this.checkDispose.bind(this)}get hasCapture(){return this.captureTree.hasCapture}get lastCaptured(){return this.captureTree.captured}get captureStackCount(){return this.captureTree.count}checkInitialize(){this.hasCapture||this.initialize()}checkDispose(){this.hasCapture||this.captureProcessor.isInteractionPreventing||this.dispose()}capture(e){this.checkInitialize(),this.captureTree.add(e)}releaseCapture(e){this.captureTree.remove(e),this.captureTree.nextCapture(e),this.checkDispose()}initialize(){document.addEventListener("pointerdown",this.pointerDownHandler,{capture:!0}),document.addEventListener("pointerup",this.pointerUpHandler,{capture:!0}),document.addEventListener("click",this.clickHandler,{capture:!0}),document.addEventListener("keydown",this.keyDownHandler,{capture:!0}),this.captureProcessor.subscribeInteractionPreventEnd(this.disposeCheckHandler)}dispose(){document.removeEventListener("pointerdown",this.pointerDownHandler,{capture:!0}),document.removeEventListener("pointerup",this.pointerUpHandler,{capture:!0}),document.removeEventListener("click",this.clickHandler,{capture:!0}),document.removeEventListener("keydown",this.keyDownHandler,{capture:!0}),this.captureProcessor.unsubscribeInteractionPreventEnd(this.disposeCheckHandler)}async handlePointerDown(e){const t=this.captureTree.captured;if(!t)return;if(this.captureProcessor.endInteractionIfNotLocked(t),this.capturedInPointerDown=t.isPointedCaptured(e),this.capturedInPointerDown||t.isCloseOnPointerUp)return;await this.captureProcessor.processCapturedElementPointer(t,(r=>t.processCapturedPointerAsync(e,r)))}async handlePointerUp(e){const t=this.captureTree.captured;if(this.capturedInPointerUp=(null==t?void 0:t.isPointedCaptured(e))||!0,!t||this.capturedInPointerUp&&this.capturedInPointerDown||!t.isCloseOnPointerUp)return;await this.captureProcessor.processCapturedElementPointer(t,(r=>t.processCapturedPointerAsync(e,r)))}async handleClick(r){await this.captureProcessor.waitForCaptureProcessing();this.captureProcessor.checkInteractionsPreventing(this.captureTree.captured,r,(()=>{e.markHandled(r,!1),this.restoreFocusOnPreventNextPointerInteractionElement&&t.isFocused(this.restoreFocusOnPreventNextPointerInteractionElement)||t.unfocus()}),!0)}async handleKeyDown(e){const t=this.captureTree.captured;if(!t)return;await this.captureProcessor.processCapturedElementKeyboard(t,(r=>t.processCapturedKeyDownAsync(e,r)))}}function u(){const e=document.querySelector(r);if(!e)throw new Error("Error when trying to get CaptureManager in DxPopupRoot");return e.captureManager}const l=Object.freeze({__proto__:null,CaptureInteractionOptions:h,CaptureManager:d,getCaptureManagerSingletonForTests:u,default:{LightCaptureManager:d,getCaptureManagerSingletonForTests:u}});export{d as C,n as L,l as c};
