import{b as n}from"./browser-3fc721b7.js";import{c as r,g as t}from"./_commonjsHelpers-41cdd1e7.js";var e=r((function(n,r){Object.defineProperty(r,"__esModule",{value:!0}),r.StringUtils=void 0;var t=function(){function n(){}return n.isAlpha=function(n){return n>="a"&&n<="z"||n>="A"&&n<="Z"},n.isDigit=function(n){return n>="0"&&n<="9"},n.stringHashCode=function(n){var r=0;if(0===n.length)return r;for(var t=n.length,e=0;e<t;e++)r=(r<<5)-r+n.charCodeAt(e),r|=0;return r},n.endsAt=function(n,r){var t=n.length-1,e=r.length-1,i=t-e;if(i<0)return!1;for(;t>=i;t--,e--)if(n[t]!==r[e])return!1;return!0},n.startsAt=function(n,r){return n.substr(0,r.length)===r},n.stringInLowerCase=function(n){return n.toLowerCase()===n},n.stringInUpperCase=function(n){return n.toUpperCase()===n},n.atLeastOneSymbolInUpperCase=function(r){for(var t=0,e=void 0;e=r[t];t++)if(n.stringInUpperCase(e)&&!n.stringInLowerCase(e))return!0;return!1},n.getSymbolFromEnd=function(n,r){return n[n.length-r]},n.trim=function(r,t){if(void 0===t)return n.trimInternal(r,!0,!0);var e=t.join("");return r.replace(new RegExp("(^["+e+"]*)|(["+e+"]*$)","g"),"")},n.trimStart=function(r,t){if(void 0===t)return n.trimInternal(r,!0,!1);var e=t.join("");return r.replace(new RegExp("^["+e+"]*","g"),"")},n.trimEnd=function(r,t){if(void 0===t)return n.trimInternal(r,!1,!0);var e=t.join("");return r.replace(new RegExp("["+e+"]*$","g"),"")},n.getDecimalSeparator=function(){return 1.1.toLocaleString().substr(1,1)},n.repeat=function(n,r){return new Array(r<=0?0:r+1).join(n)},n.isNullOrEmpty=function(n){return!n||!n.length},n.padLeft=function(r,t,e){return n.repeat(e,Math.max(0,t-r.length))+r},n.trimInternal=function(n,r,t){var e=n.length;if(!e)return n;if(e<764833){var i=n;return r&&(i=i.replace(/^\s+/,"")),t&&(i=i.replace(/\s+$/,"")),i}var o=0;if(t)for(;e>0&&/\s/.test(n[e-1]);)e--;if(r&&e>0)for(;o<e&&/\s/.test(n[o]);)o++;return n.substring(o,e)},n}();r.StringUtils=t}));t(e);const i=n,o=e;var u=r((function(n,r){Object.defineProperty(r,"__esModule",{value:!0}),r.numberToStringHex=r.numberToStringBin=r.isOdd=r.isEven=r.isNonNullString=r.isString=r.isNumber=r.boolToString=r.boolToInt=r.isDefined=void 0,r.isDefined=function(n){return null!=n},r.boolToInt=function(n){return n?1:0},r.boolToString=function(n){return n?"1":"0"},r.isNumber=function(n){return"number"==typeof n},r.isString=function(n){return"string"==typeof n},r.isNonNullString=function(n){return!!n},r.isEven=function(n){return n%2!=0},r.isOdd=function(n){return n%2==0},r.numberToStringBin=function(n,r){return void 0===r&&(r=0),o.StringUtils.padLeft(n.toString(2),r,"0")},r.numberToStringHex=function(n,r){return void 0===r&&(r=0),o.StringUtils.padLeft(n.toString(16),r,"0")}}));t(u);const s=u;export{i as b,s as c,o as s};
