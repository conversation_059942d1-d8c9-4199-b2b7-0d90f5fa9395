import{k as e}from"./key-ffa272aa.js";import{h as t,S as i,a as o,b as s,M as n}from"./menu-keyboard-strategy-7c257fd3.js";import{K as l}from"./keyboard-navigation-strategy-ea41c807.js";import{M as a,S as r}from"./popup-355ecaa4.js";import{D as d,F as h}from"./focushelper-2eea96ca.js";import{D as m}from"./dropdown-menu-keyboard-strategy-60d36909.js";import{B as c}from"./constants-3209ffde.js";const u=[...[".dxbl-btn:not([disabled]):not(.dxbl-toolbar-hidden-item)",".dxbl-btn-split:not(.dxbl-toolbar-hidden-item):has(.dxbl-btn:not([disabled]))"].map((e=>`.dxbl-toolbar-dropdown-item > .dxbl-toolbar-menu-item > ${e}`)),".dxbl-toolbar-dropdown-item.dxbl-toolbar-item-tmpl:not(.dxbl-toolbar-edit)",".dxbl-toolbar-dropdown-item.dxbl-toolbar-item-tmpl.dxbl-toolbar-edit:has(.dxbl-text-edit:not(.dxbl-disabled))"].join(",");class p extends m{constructor(e,t,i,o=u){super(e,t,o,i)}switchToNestedContent(){return!this.isSplitButtonMenuItem(this.selectedItemElement)&&super.switchToNestedContent()}doAction(e){if(this.isSplitButtonMenuItem(this.selectedItemElement)){const t=this.selectedItemElement.querySelector(".dxbl-dropdown-item.dxbl-btn");if(t)return void this.doClick(t,e)}super.doAction(e)}isSplitButtonMenuItem(e){return t(e,c.SplitButton)}}var S,v;!function(e){e[e.Header=0]="Header",e[e.Body=1]="Body"}(S||(S={})),function(e){e[e.Shown=0]="Shown",e[e.Closed=1]="Closed"}(v||(v={}));class b extends a{constructor(e,t,o,s){super(e,t),this._dispatcherAction=new d,this._activeSection=null,this._initialized=null,this._focusSemaphore=new i,this._parentStrategy=o,this._sectionElementMap={[S.Header]:null,[S.Body]:null},this._modalStatusSignal=new r,this._menuItemSelector=s}get headerElement(){return this._sectionElementMap[S.Header]}set headerElement(e){this._sectionElementMap[S.Header]=e}get bodyElement(){return this._sectionElementMap[S.Body]}set bodyElement(e){this._sectionElementMap[S.Body]=e}initialize(){this._initialized=this.initializeCore()}initializeCore(){return super.initialize(),this.headerElement&&this.bodyElement?Promise.resolve():this.shcheduleInitialization()}shcheduleInitialization(){return this._dispatcherAction.cancel(),new Promise((e=>{this._dispatcherAction.execute((()=>{super.initialize(),e()}))}))}queryItems(){return this.headerElement=this.targetElement.querySelector(".dxbl-modal-header"),this.bodyElement=this.targetElement.querySelector(".dxbl-modal-body"),[this.headerElement,this.bodyElement]}async activate(){var e;await this._initialized,h.cancelRestoreFocus();const t=null!==(e=this._activeSection)&&void 0!==e?e:S.Body;this.setActiveSection(t)}createItemStrategy(e){return e===this.headerElement?new y(this.navigator,this.headerElement):e===this.bodyElement?new _(this.navigator,this.bodyElement,this._parentStrategy,this._focusSemaphore,this._modalStatusSignal,this._menuItemSelector):null}handleKeyDown(t){return e.KeyUtils.getEventKeyCode(t)===e.KeyCode.Tab?(this.setActiveSection(this.toggleActiveSection()),!0):super.handleKeyDown(t)}toggleActiveSection(){return this._activeSection===S.Body?S.Header:S.Body}setActiveSection(e){const t=null!=e?e:S.Body,i=this.navigator.getStrategy(this._sectionElementMap[t]);this._focusSemaphore.doAllowedAction((()=>{null==i||i.activate()})),this._activeSection=t}onPopupShown(){super.onPopupShown(),this._modalStatusSignal.raise(this.targetElement,v.Shown)}onPopupClosed(){super.onPopupClosed(),this._modalStatusSignal.raise(this.targetElement,v.Closed)}}class y extends l{initialize(){super.initialize(),this.selectedItemIndex=0}queryItems(){return this.queryItemsBySelector(".dxbl-btn").filter((e=>e&&!t(e,"dxbl-invisible")))}handleKeyDown(t){switch(e.KeyUtils.getEventKeyCode(t)){case e.KeyCode.Enter:case e.KeyCode.Space:return this.raiseClickEvent(this.selectedItemElement,t.ctrlKey,t.metaKey,t.shiftKey),!0;case e.KeyCode.Left:return this.moveToPrevItem(!0),!0;case e.KeyCode.Right:return this.moveToNextItem(!0),!0;case e.KeyCode.Up:case e.KeyCode.Down:return!0;default:return super.handleKeyDown(t)}}}class _ extends p{constructor(e,t,i,s,n,l){super(e,t,i,l),this._boundModalStatusChanged=this.modalStatusChanged.bind(this),this._levelController=new o,this._focusSemaphore=s,this._modalStatusSignal=n,this._modalStatusSignal.subscribe(this._boundModalStatusChanged)}get menuLevel(){return this._levelController.currentLevel}modalStatusChanged(e,t){t===v.Shown?this.onPopupShown():this.onPopupClosed()}initialize(){super.initialize(),this._levelController.updateState(this.selectedItemIndex,this.itemCount),this.selectedItemIndex=this._levelController.selectedItemIndex}activate(){this._focusSemaphore.doIfAllowed((()=>{super.activate()})),this._focusSemaphore.disallow()}handleLeftKey(e){return this.menuLevel>0?(this.moveToPrevLevel(e),!0):!!s(this.selectedItemElement)&&(this.moveToNextLevel(e,n.Last),!0)}handleRightKey(e){return!!s(this.selectedItemElement)&&(this.moveToNextLevel(e,n.First),!0)}doAction(e){s(this.selectedItemElement)&&this.moveToNextLevel(e,n.First),super.doAction(e)}moveToNextLevel(e,t){this.performShortcutEvent(e),this._levelController.moveToNextLevel(t),this._focusSemaphore.allow()}moveToPrevLevel(e){this.performShortcutEvent(e),this._levelController.moveToPrevLevel(),this._focusSemaphore.allow()}getShortcutContext(){return{level:this.menuLevel,selectedItemIndex:this.selectedItemIndex}}handlePopupClosed(){super.handlePopupClosed(),this._levelController.clearState()}addEventSubscriptions(){}removeEventSubscriptions(){this._modalStatusSignal.unsubscribe(this._boundModalStatusChanged)}}export{p as D,b as a};
