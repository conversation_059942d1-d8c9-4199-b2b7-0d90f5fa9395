import{C as o}from"./css-classes-c63af734.js";class e{}e.Too<PERSON><PERSON>=o.Prefix+"-toolbar",e.Too<PERSON>barItem=e.<PERSON><PERSON>+"-item",e.Too<PERSON>barEdit=e.<PERSON>+"-edit",e.<PERSON><PERSON><PERSON><PERSON><PERSON>=e.<PERSON>+"-title",e.<PERSON>laceholder=e.<PERSON>+"-placeholder",e.ToolbarGroup=e.<PERSON>+"-group",e.ToolbarGroupSeparator=e.ToolbarGroup+"-separator",e.<PERSON><PERSON><PERSON>DefaultLayer="default",e.ToolbarHiddenItem=e.Tool<PERSON>+"-hidden-item",e.ToolbarItemCollapsed=e.<PERSON><PERSON>+"-collapsed",e.ToolbarNoItemText=e.<PERSON>+"-no-item-text",e.Too<PERSON><PERSON>asEllipsis=e.Tool<PERSON>+"-has-ellipsis",e.ButtonToolbar=o.Prefix+"-btn-toolbar",e.<PERSON><PERSON>lainToolbar=o.Prefix+"-btn-plain-toolbar",e.<PERSON><PERSON>=e.<PERSON>+"-btn-ellipsis",e.<PERSON><PERSON>pt<PERSON>=e.<PERSON><PERSON>bar+"-adaptive",e.AdaptiveItem=e.ToolbarAdaptive+"-item",e.AdaptiveItemText=e.AdaptiveItem+"-text",e.AdaptiveItemAllHidden=e.AdaptiveItem+"-all-hidden",e.AdaptiveItemHidden=e.AdaptiveItem+"-hidden",e.AdaptiveItemTextHidden=e.AdaptiveItemText+"-hidden",e.AdaptivePreviewImage=e.AdaptiveItem+"-preview-image",e.DropDownToggle=e.Toolbar+"-dropdown-toggle",e.Loading=o.Prefix+"-loading",e.Loaded=o.Prefix+"-loaded",e.VirtualToolbar=o.Prefix+"-virtual-toolbar",e.VirtualToolbarItem=e.VirtualToolbar+"-item";export{e as T};
