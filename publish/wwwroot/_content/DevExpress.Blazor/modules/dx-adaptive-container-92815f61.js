import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{T as s}from"./toolbar-css-classes-d303c118.js";import{C as i}from"./css-classes-c63af734.js";import{T as a,a as o}from"./events-5ceb0642.js";import{R as r,a as l}from"./events-f873e646.js";import{s as n}from"./lit-element-462e7ad3.js";import{n as d}from"./property-4ec0b52d.js";import{e as p}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./custom-events-helper-e7f279d3.js";var u,c,h;!function(e){e.Default="dxbl-adaptive-group-state-default",e.Text="dxbl-adaptive-group-state-text",e.NoText="dxbl-adaptive-group-state-no-text",e.Collapsed="dxbl-adaptive-group-state-collapsed"}(u||(u={})),function(e){e.Default="dxbl-adaptive-item-state-default",e.NoText="dxbl-adaptive-item-state-no-text",e.Collapsed="dxbl-adaptive-item-state-collapsed"}(c||(c={})),function(e){e.Default="dxbl-adaptive-ellipsis-state-default",e.Hidden="dxbl-toolbar-hidden-item"}(h||(h={}));const m=Object.values(u).map((e=>e));var f;Object.values(c).map((e=>e)),Object.values(h).map((e=>e)),function(e){e.Default="dxbl-adaptive-state-default",e.FullSequential="dxbl-adaptive-state-full-seq",e.NoText="dxbl-adaptive-state-no-text",e.NoTextSequential="dxbl-adaptive-state-no-text-seq",e.Group="dxbl-adaptive-state-group-text",e.GroupNoText="dxbl-adaptive-state-grouping-no-text"}(f||(f={}));const v=Object.values(f).map((e=>e));class b{}b.Adaptive=i.Prefix+"-adaptive",b.AdaptiveContainer=b.Adaptive+"-container",b.AdaptiveContainerContent=b.AdaptiveContainer+"-content",b.AdaptiveGroup=b.Adaptive+"-group",b.AdaptiveItem=b.Adaptive+"-item",b.EllipsisGroup=b.Adaptive+"-ellipsis-group",b.EllipsisGroupText=b.EllipsisGroup+"-text",b.EllipsisGroupNoText=b.EllipsisGroup+"-no-text",b.ItemCollapsed=b.AdaptiveItem+"-collapsed",b.ItemNoText=b.AdaptiveItem+"-no-text",b.VirtualContainer=b.Adaptive+"-virtual-container",b.VirtualTestContainer=b.VirtualContainer+"-test";const g=[b.EllipsisGroupNoText,b.EllipsisGroupText,h.Default,h.Hidden];class x{renderContainerDefaultState(e){var t,i,a;for(const t of e.groups)this.resetGroupToDefault(t),this.resetItems(t.items),this.expandGroup(t);e.root.classList.remove(...v),null===(t=e.ellipsis)||void 0===t||t.classList.remove(...g),null===(i=e.ellipsis)||void 0===i||i.classList.add(h.Hidden),null===(a=e.separator)||void 0===a||a.classList.add(s.ToolbarHiddenItem)}renderContainerNoTextState(e,t){t.forEach((t=>{const s=e.root.querySelector(`#${t}`);s&&s.classList.add(c.NoText)}))}renderGroupState(e,t){const s=e.element;s&&(s.classList.add(t),e.items.forEach((e=>this.hideElement(e))),this.showEllipsis(e))}renderContainerCollapsedState(e,t){if(t.forEach((t=>{const s=e.root.querySelector(`#${t}`);s&&s.classList.add(c.Collapsed)})),t.size>0){e.groups.filter((e=>e.items.some((e=>t.has(e.id))))).forEach((e=>this.showEllipsis(e)))}}showGlobalEllipsis(e){var t,i,a;null===(t=e.ellipsis)||void 0===t||t.classList.remove(...g),null===(i=e.ellipsis)||void 0===i||i.classList.remove(h.Hidden),null===(a=e.separator)||void 0===a||a.classList.remove(s.ToolbarHiddenItem)}hideGlobalEllipsis(e){var t,i,a;null===(t=e.ellipsis)||void 0===t||t.classList.remove(...g),null===(i=e.ellipsis)||void 0===i||i.classList.add(h.Hidden),null===(a=e.separator)||void 0===a||a.classList.add(s.ToolbarHiddenItem)}collapseGroup(e){e.items.forEach((e=>this.hideElement(e))),this.showEllipsis(e)}expandGroup(e){e.items.forEach((e=>this.showElement(e))),this.hideEllipsis(e)}resetGroupToDefault(e){e.element.classList.remove(...m),e.element.classList.remove(...v),e.ellipsis.classList.remove(...g)}resetItems(e){e.forEach((e=>{e.classList.remove(b.ItemNoText,b.ItemCollapsed,c.NoText.toString(),c.Collapsed.toString(),"dxbl-toolbar-item-collapsed","dxbl-toolbar-hidden-item"),e.classList.add(c.Default)}))}showEllipsis(e){e.ellipsis.classList.remove(s.ToolbarHiddenItem),e.ellipsis.classList.add(h.Default)}hideEllipsis(e){e.ellipsis.classList.remove(h.Default),e.ellipsis.classList.add(s.ToolbarHiddenItem)}showElement(e){e.classList.remove(b.ItemCollapsed)}hideElement(e){e.classList.add(b.ItemCollapsed)}}class S{createDefaultState(e){return{layout:f.Default,width:e.content.offsetWidth,groups:e.groups.map((e=>({id:e.element.id,state:u.Default,order:e.order,items:e.items.map((t=>({id:t.id,state:c.Default,groupId:e.element.id}))),ellipsis:{id:e.ellipsis.id,state:h.Hidden}}))),ellipsis:{id:e.ellipsis.id,state:h.Hidden}}}createNoTextState(e,t,s=new Set,i=u.Default){return{layout:f.NoText,width:e.content.offsetWidth,groups:e.groups.map((e=>({id:e.element.id,order:e.order,state:s.has(e.element.id)?u.Text:u.Default,items:e.items.map((i=>{return{id:i.id,groupId:e.element.id,state:(a=i.id,o=e.element.id,s.has(o)?c.Collapsed:t.has(a)?c.NoText:c.Default)};var a,o})),ellipsis:{id:e.ellipsis.id,state:s.has(e.element.id)?h.Default:h.Hidden}}))),ellipsis:{id:e.ellipsis.id,state:(a=s,e.groups.filter((e=>a.has(e.element.id))).flatMap((e=>e.blocks)).some((e=>e.collapseInGlobalGroup))?h.Default:h.Hidden)}};var a}createCollapseState(e,t,s,i){function a(e){const i=e.blocks.filter((e=>!e.collapseInGlobalGroup)).map((e=>e.id)).some((e=>t.has(e))),a=s.has(e.element.id);return i||a}return{layout:f.NoTextSequential,width:e.content.offsetWidth,groups:e.groups.map((e=>({id:e.element.id,order:e.order,state:s.has(e.element.id)?u.Text:u.Default,items:e.items.map((a=>{return{id:a.id,groupId:e.element.id,state:(o=a.id,r=e.element.id,s.has(r)||t.has(o)?c.Collapsed:i)};var o,r})),ellipsis:{id:e.ellipsis.id,state:a(e)?h.Default:h.Hidden}}))),ellipsis:{id:e.ellipsis.id,state:e.groups.flatMap((e=>e.blocks)).filter((e=>t.has(e.id)||s.has(e.groupId))).some((e=>e.collapseInGlobalGroup))?h.Default:h.Hidden}}}createGroupNoTextState(e,t){return{layout:f.GroupNoText,width:e.content.offsetWidth,groups:e.groups.map((e=>({id:e.element.id,order:e.order,state:t.has(e.element.id)?u.NoText:u.Text,items:e.items.map((t=>({id:t.id,groupId:e.element.id,state:c.Collapsed}))),ellipsis:{id:e.ellipsis.id,state:h.Default}}))),ellipsis:{id:e.ellipsis.id,state:e.groups.flatMap((e=>e.blocks)).some((e=>e.collapseInGlobalGroup))?h.Default:h.Hidden}}}}class I{constructor(){this.renderer=new x,this.factory=new S}getStates(e,t,s){const i=[],a=this.getBlocks(e),o=[];if(s.toGroups&&o.push(...this.getGroupLimits(e)),i.push(...this.getDefaultState(e,o)),s.toIcons&&i.push(...this.getNoTextStates(e,a,o)),s.hideItems){const t=i.filter((e=>e.layout===f.NoText)).sort(((e,t)=>e.width-t.width)).map((e=>e.width))[0];i.push(...this.getCollapsedStates(e,a,o,s.toIcons?c.NoText:c.Default,t))}return s.toGroups&&i.push(...this.getGroupNoTextStates(e,i)),i}getBlocks(e){return e.groups.flatMap((e=>e.blocks.reverse())).sort(((e,t)=>t.adaptivePriority-e.adaptivePriority))}getNoTextStates(e,t,s){const i=[],a=new Set,o=new Set;this.renderer.hideGlobalEllipsis(e);for(const r of t){a.add(r.id),this.renderer.renderContainerNoTextState(e,a);const t=e.groups.find((e=>e.element.id===r.groupId)),l=s.find((e=>e.groupId===t.element.id));if(l&&!this.compareGroupWidth(t,l)){if(o.has(l.groupId))continue;this.renderer.renderGroupState(t,u.Text),o.add(l.groupId)}i.push(this.factory.createNoTextState(e,a,o))}return i}getGroupLimits(e){const t=[];this.renderer.renderContainerDefaultState(e);for(const s of e.groups)this.renderer.renderGroupState(s,u.Text),t.push({groupId:s.element.id,width:s.element.offsetWidth});return t}getCollapsedStates(e,t,s,i,a){const o=[],r=new Set,l=new Set;let n=!1;for(const d of t){if(r.add(d.id),d.collapseInGlobalGroup&&!n&&(this.renderer.showGlobalEllipsis(e),n=!0),this.renderer.renderContainerCollapsedState(e,r),e.content.offsetWidth>a)continue;const t=e.groups.find((e=>e.element.id===d.groupId)),p=s.find((e=>e.groupId===t.element.id));if(p&&!this.compareGroupWidth(t,p)){if(l.has(p.groupId))continue;this.renderer.renderGroupState(t,u.Text),l.add(p.groupId)}o.push(this.factory.createCollapseState(e,r,l,i))}return o}getGroupNoTextStates(e,t){const s=[],i=t.filter((e=>e.groups.every((e=>e.state===u.Text)))),a=new Set,o=new Set(i.flatMap((e=>e.groups)).sort(((e,t)=>e.order-t.order)).map((e=>e.id)));for(const t of o){const i=e.groups.find((e=>e.element.id===t));i.hasIcon&&(a.add(t),this.renderer.renderGroupState(i,u.NoText),s.push(this.factory.createGroupNoTextState(e,a)))}return s}compareGroupWidth(e,t){return e.element.offsetWidth>t.width}getDefaultState(e,t){this.renderer.renderContainerDefaultState(e);for(const s of e.groups){const e=t.find((e=>e.groupId===s.element.id));e&&!this.compareGroupWidth(s,e)&&this.renderer.renderGroupState(s,u.Text)}return[this.factory.createDefaultState(e)]}}const C=Object.values(u).map((e=>e)),T=Object.values(c).map((e=>e)),y=Object.values(h).map((e=>e));class G{constructor(){this.appliedState=null,this.states=[],this.currentCheckpoint=0,this.calculator=new I,this.containerItemsUpdate=null}async configure(e,t,s){const i=this.copyContainer(e),a=this.createAdaptiveContainer(i,t);0!==a.content.offsetWidth&&(this.states=this.calculator.getStates(a,t,s),a.root.remove(),await this.adjust(e,t))}async adjust(e,t){const s=this.createAdaptiveContainer(e,t),i=s.root.offsetWidth,a=this.states.map((e=>e.width)).sort(((e,t)=>t-e)).find((e=>e<i));if(this.appliedState&&this.currentCheckpoint&&this.currentCheckpoint===a)return;e.querySelector(`.${b.AdaptiveContainerContent}`);const o=this.states.filter((e=>e.width===a))[0];o&&(this.currentCheckpoint=a,await this.waitForAnimationFrame((()=>{if(this.applyNewState(s,o),this.containerItemsUpdate&&o.groups.length>0){const e=o.groups.map((e=>e.items)).reduce(((e,t)=>e.concat(t))).map((e=>({Id:e.id,TextHidden:e.state===c.NoText,IsVisible:e.state!==c.Collapsed})));this.containerItemsUpdate(e)}})))}applyNewState(e,t){for(const s of t.groups)this.applyGroupState(e,s);t.ellipsis&&this.applyEllipsisState(e,t.ellipsis),this.appliedState=t}waitForAnimationFrame(e){return new Promise((t=>{requestAnimationFrame((s=>{e(),t(s)}))}))}applyGroupState(e,t){const s=e.root.querySelector(`#${t.id}`);if(s){s.classList.remove(...C),s.classList.add(t.state),s.dispatchEvent(new a(t.state.toString()));for(const s of t.items)this.applyItemState(e,s);t.ellipsis&&this.applyEllipsisState(e,t.ellipsis)}}applyItemState(e,t){const s=e.root.querySelector(`#${t.id}`);s&&(s.classList.remove(...T),s.classList.add(t.state))}applyEllipsisState(e,t){const s=e.root.querySelector(`#${t.id}`);s&&(s.classList.remove(...y),s.classList.add(t.state))}copyContainer(e){const t=e.cloneNode(!0);t.classList.add(b.VirtualContainer),t.style.visibility="hidden",t.style.position="absolute";return e.parentElement.appendChild(t),t}createAdaptiveContainer(e,t){const i=e.querySelector(`.${b.AdaptiveContainerContent}`),a=i.querySelectorAll(`.${b.AdaptiveGroup}`);let o=0;return{root:e,content:i,groups:[...a].reverse().map((e=>{o++;const i=e.querySelectorAll(t),a=e;return{element:e,order:o,hasText:a.hasAttribute("has-adaptive-text"),hasIcon:a.hasAttribute("has-adaptive-icon"),items:[...i],blocks:[...i].map((t=>({id:t.id,groupOrder:o,groupId:e.id,collapsed:!1,noText:!1,adaptivePriority:parseInt(t.getAttribute("adaptive-priority")),collapseInGlobalGroup:t.hasAttribute("collapse-in-global-group")}))),ellipsis:e.querySelector(`.${s.ButtonEllipsis}`)}})),separator:e.querySelector(".dxbl-ribbon-general-separator"),ellipsis:e.querySelector(".dxbl-toolbar-btn-ellipsis-general")}}}let w=class extends t{constructor(){super(...arguments),this.containerResizeObserver=null,this.contentResizeObserver=null,this.isAdaptivityEnabled=!1,this.virtualContainer=new G,this.itemResizedHandler=this.itemResized.bind(this),this.adaptiveItemUpdatedHandler=this.adaptiveItemUpdated.bind(this),this.initialized=!1,this.collapseToIcons=!1,this.canHideItems=!1,this.collapseToGroups=!1,this.minRootItemCount=0,this.itemSelector=".dxbl-adaptive-item:not(.dxbl-toolbar-btn-ellipsis)"}get settings(){return{toIcons:this.collapseToIcons,toGroups:this.collapseToGroups,hideItems:this.canHideItems}}firstUpdated(){this.tryAdapt()}updated(e){super.updated(e),(e.has("collapseToIcons")||e.has("canHideItems")||e.has("collapseToGroups"))&&this.configure()}connectedCallback(){super.connectedCallback(),this.addEventListener(r.eventName,this.itemResizedHandler),this.addEventListener(l.eventName,this.adaptiveItemUpdatedHandler),this.virtualContainer.containerItemsUpdate=e=>{this.dispatchEvent(new o(Array.from(e)))}}disconnectedCallback(){var e;super.disconnectedCallback(),this.removeEventListener(r.eventName,this.itemResizedHandler),this.removeEventListener(l.eventName,this.adaptiveItemUpdatedHandler),null===(e=this.containerResizeObserver)||void 0===e||e.unobserve(this)}tryAdapt(){this.classList.contains(b.VirtualContainer)||(this.containerResizeObserver=new ResizeObserver((()=>new Promise((()=>this.adjust())))),this.containerResizeObserver.observe(this),this.contentResizeObserver=new ResizeObserver((()=>{0!==this.offsetWidth&&this.configure()})),this.contentResizeObserver.observe(this.querySelector(`.${b.AdaptiveContainerContent}`)),this.initialized=!0,this.configure())}async configure(){this.initialized&&await this.virtualContainer.configure(this,this.itemSelector,this.settings)}async adjust(){await this.virtualContainer.adjust(this,this.itemSelector)}itemResized(e){super.getUpdateComplete().then((()=>this.virtualContainer.configure(this,this.itemSelector,this.settings)))}adaptiveItemUpdated(e){}};w.shadowRootOptions={...n.shadowRootOptions,mode:"open"},e([d({attribute:"collapse-to-icons",type:Boolean})],w.prototype,"collapseToIcons",void 0),e([d({attribute:"can-hide-items",type:Boolean})],w.prototype,"canHideItems",void 0),e([d({attribute:"collapse-to-groups",type:Boolean})],w.prototype,"collapseToGroups",void 0),e([d({attribute:"min-root-item-count",type:Number})],w.prototype,"minRootItemCount",void 0),w=e([p("dxbl-adaptive-container")],w);const E=w;export{E as default};
