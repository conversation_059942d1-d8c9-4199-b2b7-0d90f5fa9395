import{D as t}from"./layouthelper-67dd777a.js";import{T as e,I as a}from"./constants-7c047c0d.js";const n=["a[href]","audio[controls]","button",'[contenteditable]:not([contenteditable="false"])',"details>summary:first-of-type","details","input","select","textarea","[tabindex]","video[controls]"].join(",");class i{static getTabbables(t,e){const a=this.findTabbables(t,e),n=[],s=[];return a.forEach(((t,e)=>{const a=i.getTabIndex(t);0===a?n.push(t):s.push({index:e,tabIndex:a,item:t})})),s.sort(((t,e)=>t.tabIndex===e.tabIndex?t.index-e.index:t.tabIndex-e.tabIndex)).map((t=>t.item)).concat(n)}static findTabbables(t,e){const a=Array.from(t.querySelectorAll(n));return e&&(t.matches(n)||t.webkitMatchesSelector(n))&&a.unshift(t),a.filter(i.filterTabbable)}static filterTabbable(e){return!e.disabled&&(!i.isHiddenInput(e)&&(!t.isHidden(e)&&!(i.getTabIndex(e)<0)))}static getTabIndex(t){const a=t.getAttribute("tabindex");if(a){const t=parseInt(a,10);if(!isNaN(t))return t}return"true"===t.contentEditable||t.nodeName===e.audio||t.nodeName===e.video||t.nodeName===e.details?0:t.tabIndex}static isHiddenInput(t){return t.tagName===e.input&&t.getAttribute("type")===a.hidden}}export{i as T};
