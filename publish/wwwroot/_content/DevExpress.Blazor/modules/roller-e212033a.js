import{k as e}from"./key-ffa272aa.js";import{R as t,a as i}from"./dom-utils-d057dcaa.js";import{E as s,S as n,a as o}from"./observables-589a5615.js";import{C as l}from"./css-classes-c63af734.js";import{D as r,K as a}from"./keyboard-navigation-strategy-ea41c807.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./dom-554d0cc7.js";import"./tslib.es6-d65164b3.js";import"./focushelper-2eea96ca.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./devices-17b9ba08.js";function h(e){return e*(2-e)}function c(e){return e/Math.abs(e)}function m(e,t,i,s){let n=null;const o=e=>n?n(e,null):0,l=()=>{n=null},r=(e,s,o)=>{n=function(e,t,i,s,n){const o=t+i;let l=0;return(r,a)=>{if(a)return a(e-l,r,s(r/o));if(r<t)return null;r>=o&&n&&n();const h=e*s((Math.min(r,o)-t)/i)-l;return l+=h,h}}(s+e,o,t,i,l)},a=e=>{null===n?(r(e.value,0,e.timeStamp),s(o)):n(e.timeStamp,((t,i)=>r(e.value,t,i)))};return e.subscribe(a),()=>{n=null,e.unsubscribe(a)}}function u(e,i,n,o,l){const r=function(e,t,i){const n=new s;return e.addEventListener(t,(e=>{e.preventDefault();const t=i(e);0===t||isNaN(t)||n.emit({value:t,timeStamp:e.timeStamp})}),{capture:!1,passive:!1}),n}(e,"wheel",(e=>c(e.deltaY)*i));function a(e,i){t((t=>{const s=e(t);0!==s?(null!==s&&o(s),a(e,i)):i()}))}m(function(e,t){const i=new s;let n=0,o=1;return e.subscribe((e=>{(0===n||e.timeStamp>=n||c(e.value)!==o)&&(n=e.timeStamp+t,o=c(e.value),i.emit(e))})),i}(r,30),300,n,(function(e){l(new Promise(((t,i)=>{a(e,t)})))}))}class d{}d.Roller=l.Prefix+"-roller",d.RollerContainer=d.Roller+"-container",d.RollerTitle=d.Roller+"-title",d.RollerItem=d.Roller+"-item",d.RollerAfter=d.Roller+"-after",d.RollerExpander=d.Roller+"-expander",d.RollerFooter=l.Prefix+"-rollers-footer";class p extends Event{constructor(){super(p.eventName,{bubbles:!0,composed:!1,cancelable:!0})}}function g(e,t,i){const s=i(e);return s?t||s.visible.value?s:g(s,t,i):null}function v(e,t){const i=-1===t?"prevItem":"nextItem";return e[i]?e[i]:e[i]=(i=>{const s=e.value+t*i.delta;return-1===t&&-1!==i.min&&s<i.min?i.needLoop?new b(i,i.max,!1,null,e):null:1===t&&-1!==i.max&&s>i.max?i.needLoop?new b(i,i.min,!1,e,null):null:new b(i,s,!1,null,null)})(e.collection)}p.eventName="dxrollerinitialized";class f{constructor(e,t,i,s,o,l){this._collection=e,this.prevItem=o,this.nextItem=l,this.value=t,this.visible=i||new n(!0),this.selected=new n(!!s),o&&(o.nextItem=this),l&&(l.prevItem=this),this.displayText=new n(t);const r=this.collection.selectedItem;this.selected.subscribe((e=>{e&&(r.value&&r.value.selected.update(!1),r.update(this))})),this.visible.subscribe((e=>{if(!e&&this.selected.value){const e=this.getPrevious(!1);e&&e.selected.update(!0)}this.collection.visibleItemsChanged.emit(null)}),!0)}get collection(){return this._collection}getPrevious(e){return g(this,e,(e=>e.prevItem))}getNext(e){return g(this,e,(e=>e.nextItem))}getDisplayText(){return this.displayText.value||this.value}}class b extends f{constructor(e,t,i,s,n){super(e,t,null,i,s,n)}get collection(){return this._collection}getPrevious(e){return v(this,-1)}getNext(e){return v(this,1)}}class y extends b{constructor(e,t){super(e,t,!0,null,null)}}class C{constructor(e=!0){this.needLoop=e,this.items=[],this.selectedItem=new n(null),this.visibleItemsChanged=new s}static createCollection(e,t,i=!0){const s=new C(i);for(let i=0;i<t.length;i++)s.add(i+1,null,i+1===e).displayText.value=t[i];return s.initialize(e),s}static createGenerator(e,t,i,s,n=!0){const o=new I(t,i,s,n);return o.initialize(e),o}initialize(e){this.items.filter((t=>t.value===e))[0].selected.update(!0),this.needLoop&&(this.items[0].prevItem=this.items[this.items.length-1],this.items[this.items.length-1].nextItem=this.items[0])}add(e,t,i){const s=new f(this,e,t,i,this.items[this.items.length-1],null);return this.items.push(s),s}}class I extends C{constructor(e,t,i,s){super(s),this.min=e,this.max=t,this.delta=i,this.originItem=null}initialize(e){this.originItem=new y(this,e)}}class x{constructor(e,t,i,s,n){this.dataItem=null,this.displayTextSubscription=this.displayTextSubscription.bind(this),this.roller=i,this.prevItem=n,this.nextItem=null,this.index=t,this.elements=i.createRollerItemElements(),this.offset=t*s,this.height=s,this.position=0,this.visibleItemCount=i.visibleItemCount,n&&(n.nextItem=this),this.updateDataItem(e),this.selectItem=this.selectItem.bind(this)}initialize(e){const i=Math.floor(this.visibleItemCount/2);this.elements[1].style.top=(this.index-i)*this.height+"px",e.appendChild(this.elements[1]),this.move(this.offset),this.elements[0].addEventListener("click",(()=>{t(this.selectItem)}))}isSelected(){return Math.floor(this.visibleItemCount/2)*this.height===Math.round(this.position)}selectItem(e){if(!this.dataItem)return Promise.resolve();const i=Math.floor(this.visibleItemCount/2);return this.roller.afterMove(T(o.Empty,(e=>this.roller.move(e)),{divisor:this.roller.itemSize.height,startTimestamp:e,endTimestamp:e+300,easing:h,frameCallback:t,value:(i-this.index)*this.height-(this.position-this.offset)}))}move(e){this.updatePosition(e);const t=this.position-this.offset;this.elements.forEach((e=>e.style.transform="matrix(1, 0, 0, 1, 0, "+t+")"))}updatePosition(e){this.position+=e,this.position>this.visibleItemCount*this.height?(this.position-=(this.visibleItemCount+1)*this.height,this.updateDataItem(this.nextItem&&this.nextItem.dataItem?this.nextItem.dataItem.getPrevious(!1):null)):this.position<-1*this.height&&(this.position+=(this.visibleItemCount+1)*this.height,this.updateDataItem(this.prevItem.dataItem?this.prevItem.dataItem.getNext(!1):null))}updateDataItem(e){this.dataItem!==e&&(this.dataItem&&this.dataItem.displayText.unsubscribe(this.displayTextSubscription),this.dataItem=e,this.dataItem?this.dataItem.displayText.subscribe(this.displayTextSubscription):this.displayTextSubscription(""))}displayTextSubscription(e){this.elements.forEach((t=>D(t,e)))}raiseChanges(){this.isSelected()&&this.dataItem&&this.dataItem.selected.update(!0)}}function D(e,t){e.innerText=t}function W(e,t,i){const s=i(e);return s&&Math.abs(Math.round(s.position-e.position))===t?[s].concat(W(s,t,i)):[]}function w(e,t,i){0!==e.length&&(e.shift().updateDataItem(t),w(e,i(t),i))}class M{constructor(e,t,i,s,n,o){this.itemCollection=e,this.visibleItemCount=t,this.itemContainers=[],this.itemSize=s,this.caption=n,this.longestVisibleDisplayText=o,this.needLoop=e.needLoop,this.container=i,this.rollerElement=null,this.rollerContainer=null,this.move=this.move.bind(this),this.moveReversed=this.moveReversed.bind(this),this.afterMove=this.afterMove.bind(this)}initialize(){this.initializeRollerElements();const i=[this.itemCollection.selectedItem.value];for(;i.length<this.visibleItemCount;)i.splice(0,0,i[0]?i[0].getPrevious(!1):null),i.push(i[i.length-1]?i[i.length-1].getNext(!1):null);i.push(i[i.length-1]?i[i.length-1].getNext(!1):null);for(let e=0;e<i.length;e++)this.itemContainers.push(new x(i[e],e,this,this.itemSize.height,this.itemContainers[this.itemContainers.length-1]));this.longestVisibleDisplayText&&D(this.createRollerItemElement(d.RollerItem+" "+d.RollerExpander),this.longestVisibleDisplayText);const n=this.createRollerItemElement(d.RollerAfter);this.itemContainers[0].prevItem=this.itemContainers[this.itemContainers.length-1],this.itemContainers[this.itemContainers.length-1].nextItem=this.itemContainers[0];for(let e=0;e<this.itemContainers.length;e++)this.itemContainers[e].initialize(n);this.itemCollection.selectedItem.subscribe((()=>this.updateVisibleDataItems()),!0),this.itemCollection.visibleItemsChanged.subscribe((()=>this.updateVisibleDataItems())),u(this.rollerElement,this.itemSize.height,h,this.moveReversed,this.afterMove),function(e,i,n,o){const l=new s,r=new s;let a=0;e.addEventListener("touchstart",(e=>{a=e.touches[0].clientY}),{capture:!1,passive:!0}),e.addEventListener("touchend",(()=>{a=0,t((e=>r.emit(e)))}),{capture:!1,passive:!0}),e.addEventListener("touchmove",(e=>{e.preventDefault();const t=e.changedTouches[0].clientY;l.emit(t-a),a=t}),{capture:!1,passive:!1});const c={divisor:i,accelerationTimeFrame:0};!function(e,t,i,s,n,o){let l,r,a,c=1,m=!1;const u=i.accelerationTimeFrame||300;function d(e){const t=Math.abs(r%i.divisor);return{value:(i.divisor-t)*Math.sign(r),endTimestamp:e+300,startTimestamp:e,easing:h,frameCallback:o,divisor:0}}function p(e,t){return(t*=c)>=l&&e-a<=u&&(c=Math.min(2,1.2*c)),r+=t,l=t,a=e,t}function g(e){r=0,a=e,c=1}e.subscribe((i=>{o((o=>{m||(m=!0,g(o),n(new Promise(((i,n)=>{const l=n=>{t.unsubscribe(l),T(e,s,d(n)).then((()=>{m=!1,i()})).catch((()=>{t.subscribe(l)})).finally((()=>g(o)))};t.subscribe(l)})))),s(p(o,i))}))}))}(l,r,c,n,o,t)}(this.rollerElement,this.itemSize.height,this.move,this.afterMove),this.rollerContainer&&this.rollerContainer.addEventListener("keydown",(t=>{let i=null;t.keyCode===e.KeyCode.Up?i=this.itemCollection.selectedItem.value.getPrevious():t.keyCode===e.KeyCode.Down&&(i=this.itemCollection.selectedItem.value.getNext()),i&&(t.preventDefault(),i.selected.update(!0))}))}initializeRollerElements(){const e=this.rollerContainer=document.createElement("SPAN");e.className=d.RollerContainer,e.style.minWidth=this.itemSize.width.toString();const t=document.createElement("SPAN");t.innerText=this.caption,t.className=d.RollerTitle,e.appendChild(t);const i=this.rollerElement=document.createElement("DIV");i.className=d.Roller,i.tabIndex=-1,i.style.height=this.itemSize.height*this.visibleItemCount+"px",e.appendChild(i),this.container&&this.container.appendChild(e)}updateVisibleDataItems(){const e=this.itemContainers.filter((e=>e.isSelected()))[0];if(!e)return;const t=W(e,this.itemSize.height,(e=>e.prevItem)),i=W(e,this.itemSize.height,(e=>e.nextItem)),s=this.itemCollection.selectedItem.value;e.updateDataItem(s),w(t.concat([]),s.getPrevious(),(e=>e?e.getPrevious(!1):null)),w(i.concat([]),s.getNext(),(e=>e?e.getNext(!1):null))}createRollerItemElement(e){const t=document.createElement("SPAN");return t.className=e||d.RollerItem,this.rollerElement&&this.rollerElement.appendChild(t),t}createRollerItemElements(){const e=this.createRollerItemElement(null);return[e,e.cloneNode()]}moveReversed(e){this.move(0-e)}move(e){if(0===e)return;const t=Math.sign(e),i=this.itemContainers.length-1,s=-1===t?i:0,n=(-1===t?0:i)+t;if(!this.needLoop){if(1===t){const i=Math.floor(this.visibleItemCount/2)*this.itemSize.height;for(let o=s;o!==n;o+=t)!this.itemContainers[o].dataItem&&this.itemContainers[o].position+this.itemSize.height-i<=0&&this.itemContainers[o].position+this.itemSize.height+e-i>0&&(e=Math.min(i-(this.itemContainers[o].position+this.itemSize.height),e))}else{const i=(Math.floor(this.visibleItemCount/2)+1)*this.itemSize.height;for(let o=s;o!==n;o+=t)!this.itemContainers[o].dataItem&&this.itemContainers[o].position-i>=0&&this.itemContainers[o].position+e-i<0&&(e=Math.max(i-this.itemContainers[o].position,e))}if(0===e)return}for(let i=s;i!==n;i+=t)this.itemContainers[i].move(e)}afterMove(e){return e.then((()=>Promise.resolve(this.itemContainers.forEach((e=>e.raiseChanges())))))}}function T(e,t,i){return new Promise(((s,n)=>{function o(){e.unsubscribe(o),n()}e.subscribe(o);let l=0;const r=i.frameCallback,a=n=>{if(n<i.endTimestamp){const e=i.easing((n-i.startTimestamp)/(i.endTimestamp-i.startTimestamp)),s=i.value*e-l;t(s),l+=s,r(a)}else e.unsubscribe(o),t(i.value-l),s()};r(a)}))}function S(e){return e.value%4==0&&e.value%100!=0||e.value%400==0}function R(e){return 2!==e.value}function N(e){return-1!==[1,3,5,7,8,10,12].indexOf(e.value)}var z;function E(e,t,i,s,n,o,l){const r=e.add(t,i,s);o.subscribe((([e,i])=>{if(l){const s=n[L(i.value,e.value,t)];r.displayText.update(t+" "+s)}else r.displayText.value=n[t-1]}))}function L(e,t,i){return new Date(e,t-1,i).getDay()}function P(e){return e&&e.length?" "+e.concat([]).sort(((e,t)=>t.length-e.length))[0]:""}function Y(e){let t="initial";for(;e;){if(t=window.getComputedStyle(e).backgroundColor,t&&"rgba(0, 0, 0, 0)"!==t&&"transparent"!==t)return t;e=e.parentElement}return t}!function(e){e[e.DayWithoutLeadingZero=0]="DayWithoutLeadingZero",e[e.DayWithLeadingZero=1]="DayWithLeadingZero",e[e.DayWithShortName=2]="DayWithShortName",e[e.DayWithFullName=3]="DayWithFullName",e[e.MonthWithShortName=4]="MonthWithShortName",e[e.MonthWithFullName=5]="MonthWithFullName",e[e.MonthWithoutLeadingZero=6]="MonthWithoutLeadingZero",e[e.MonthWithLeadingZero=7]="MonthWithLeadingZero",e[e.YearWithFourDigit=8]="YearWithFourDigit",e[e.YearWithThreeDigit=9]="YearWithThreeDigit",e[e.YearWithTwoDigit=10]="YearWithTwoDigit",e[e.YearWithOneDigit=11]="YearWithOneDigit"}(z||(z={}));const j=1,k=2,F=0,Z=1,A=2,$=3;function K(e,t,i){if(!t)return Promise.reject();const s=t.twelveHourFormat;let n=s?t.time.hours%12:t.time.hours;s&&0===n&&(n=12);let o=t.time.minutes,l=t.time.seconds,a=t.time.hours>=12?k:j;const h=U(e),c=C.createGenerator(l,0,59,1),m=C.createGenerator(o,0,59,1),u=s?C.createGenerator(n,1,12,1):C.createGenerator(n,0,23,1);let g=null;t.ampmDesignators&&t.ampmDesignators[0]&&t.ampmDesignators[1]&&(g=C.createCollection(a,t.ampmDesignators,!1));const v=document.createDocumentFragment(),f=document.createElement("STYLE");function b(e){return t=>{e(t),i.invokeMethodAsync("UpdateDateTime",[n,o,l,a]).catch()}}f.type="text/css",f.innerText=`.${d.Roller} { height: ${5*h}px; } .${d.RollerItem}, .${d.RollerAfter} { height: ${h}px; } .${d.RollerAfter} { background-color: ${Y(e)}; }`,v.appendChild(f),t.items.forEach((e=>{switch(e.type){case F:new M(u,5,v,{width:-1,height:h},"Hour","15").initialize();break;case Z:new M(m,5,v,{width:-1,height:h},"Minute","55").initialize();break;case A:new M(c,5,v,{width:-1,height:h},"Second","55").initialize();break;case $:if(g){new M(g,5,v,{width:-1,height:h},"AM/PM","AM").initialize()}}})),u.selectedItem.subscribe(b((e=>{n=e.value}))),m.selectedItem.subscribe(b((e=>{o=e.value}))),c.selectedItem.subscribe(b((e=>{l=e.value}))),g&&g.selectedItem.subscribe(b((e=>{a=e.value}))),v.appendChild(document.createElement(r)),G.set(e,new O(u,m,c,g)),e.textContent="",e.appendChild(v);const y=e.querySelector(r);return y&&!y.initialized&&y.initialize(e,new V(y,e,i)),e.dispatchEvent(new p),Promise.resolve()}class V extends a{constructor(e,t,i){super(e,t),this.dotNetRef=i}queryItems(){return this.queryItemsBySelector(`.${d.RollerContainer} .${d.Roller} .${d.RollerAfter}`)}handleKeyDown(t){const i=e.KeyUtils.getEventKeyCode(t);return i!==e.KeyCode.Tab&&i!==e.KeyCode.Right&&i!==e.KeyCode.Space||t.shiftKey||this.lastItemSelected?i===e.KeyCode.Tab&&t.shiftKey||i===e.KeyCode.Left?(this.selectedItemIndex>0?this.moveToPrevItem():i!==e.KeyCode.Left&&this.leaveBackward(),!0):super.handleKeyDown(t):(this.moveToNextItem(),!0)}}const G=new WeakMap;class O{constructor(e,t,i,s){this.hoursCollection=e,this.minutesCollection=t,this.secondsCollection=i,this.AMPMCollection=s}update(e){if(this.AMPMCollection){let t=this.AMPMCollection?e.hours%12:e.hours;t=0===t?12:t,this.hoursCollection.initialize(t),this.AMPMCollection.initialize(e.hours>=12?k:j)}else this.hoursCollection.initialize(e.hours);this.minutesCollection.initialize(e.minutes),this.secondsCollection.initialize(e.seconds)}}class q{constructor(e,t,i){this.yearsCollection=e,this.monthsCollection=t,this.daysCollection=i}update(e){const t=e.filter((e=>e.type===z.DayWithShortName||e.type===z.DayWithFullName||e.type===z.DayWithoutLeadingZero||e.type===z.DayWithLeadingZero))[0],i=e.filter((e=>e.type===z.MonthWithShortName||e.type===z.MonthWithFullName||e.type===z.MonthWithLeadingZero||e.type===z.MonthWithoutLeadingZero))[0],s=e.filter((e=>e.type===z.YearWithFourDigit||e.type===z.YearWithThreeDigit||e.type===z.YearWithTwoDigit||e.type===z.YearWithOneDigit))[0];this.yearsCollection.initialize(s.value),this.monthsCollection.initialize(i.value),this.daysCollection.initialize(t.value)}}function H(e,t){if(!G.has(e))return;const i=G.get(e);if(i)return void i.update(t);const s=G.get(e);s&&s.update(t)}function U(e){const t=document.createElement("span");t.className=d.RollerItem,t.textContent="01234567890",e.appendChild(t);let s=t.offsetHeight;return s||(s=i(e,"dxbl-lg")?42:i(e,"dxbl-sm")?30:36),e.removeChild(t),s}const _={initializeDateRoller:function(e,t,i,s,n){const o=t.items.filter((e=>e.type===z.DayWithShortName||e.type===z.DayWithFullName||e.type===z.DayWithoutLeadingZero||e.type===z.DayWithLeadingZero))[0],l=t.items.filter((e=>e.type===z.MonthWithShortName||e.type===z.MonthWithFullName||e.type===z.MonthWithLeadingZero||e.type===z.MonthWithoutLeadingZero))[0],a=t.items.filter((e=>e.type===z.YearWithFourDigit||e.type===z.YearWithThreeDigit||e.type===z.YearWithTwoDigit||e.type===z.YearWithOneDigit))[0],h=t.yearNames||[];let c=t.monthNames||[];const m=t.dayNames||[],u=U(e);let g=o?o.value:1,v=null;l?v=l.value:(c=["","","","","","","","","","","",""],v=i);let f=a?a.value:s;const b=a?C.createCollection(f,h):C.createGenerator(f,1,9999,1),y=C.createCollection(v,c),I=y.selectedItem.asTrigger(R),x={29:b.selectedItem.asTrigger(S).or(I),30:I,31:y.selectedItem.asTrigger(N)},D=y.selectedItem.join(b.selectedItem),W=o&&(o.type===z.DayWithFullName||o.type===z.DayWithShortName),w=new C;for(let e=1;e<=31;e++)E(w,e,x[e],e===g,m,D,W);w.initialize(g);const T=["25"+P(m),P(c),"0000"].reduce(((e,t)=>e.length>t.length?e:t)),L=document.createDocumentFragment(),j=document.createElement("STYLE");function k(e){return t=>{e(t),n.invokeMethodAsync("UpdateDateTime",[g,v,f]).catch((e=>console.error(e)))}}j.type="text/css",j.innerText=`.${d.Roller} { height: ${5*u}px; } .${d.RollerItem}, .${d.RollerAfter} { height: ${u}px; } .${d.RollerAfter} { background-color: ${Y(e)}; } `,L.appendChild(j),t.items.forEach((e=>{switch(e.type){case z.DayWithoutLeadingZero:case z.DayWithLeadingZero:case z.DayWithFullName:case z.DayWithShortName:new M(w,5,L,{width:-1,height:u},"Day",T).initialize();break;case z.MonthWithFullName:case z.MonthWithShortName:case z.MonthWithLeadingZero:case z.MonthWithoutLeadingZero:new M(y,5,L,{width:-1,height:u},"Month",T).initialize();break;case z.YearWithFourDigit:case z.YearWithThreeDigit:case z.YearWithTwoDigit:case z.YearWithOneDigit:new M(b,5,L,{width:-1,height:u},"Year",T).initialize();break}})),L.appendChild(document.createElement(r)),e.textContent="",e.appendChild(L),w.selectedItem.subscribe(k((e=>{g=e.value}))),y.selectedItem.subscribe(k((e=>{v=e.value}))),b.selectedItem.subscribe(k((e=>{f=e.value}))),G.set(e,new q(b,y,w));const F=e.querySelector(r);return F&&!F.initialized&&F.initialize(e,new V(F,e,n)),e.dispatchEvent(new p),Promise.resolve()},initializeTimeRoller:K,updateRoller:H};export{M as Roller,d as RollerCssClasses,p as RollerInitializedEvent,f as RollerItem,C as RollerItemCollection,V as RollerKbdStrategy,_ as default,L as getDayOfWeek,K as initializeTimeRoller,H as updateRoller};
