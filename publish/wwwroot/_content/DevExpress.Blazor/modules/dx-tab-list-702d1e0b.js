import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{K as s,F as i,D as o}from"./keyboard-navigation-strategy-ea41c807.js";import{T as r}from"./constants-ed9663d1.js";import{tabIdSelector as a}from"./ribbon-tabs-1043ab2f.js";import{k as d}from"./key-ffa272aa.js";import{n}from"./property-4ec0b52d.js";import{e as c}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./focushelper-2eea96ca.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./devices-17b9ba08.js";import"./scroll-viewer-css-classes-e724f203.js";import"./toolbar-css-classes-d303c118.js";import"./ribbon-utils-2b6a64cb.js";import"./events-f873e646.js";class m extends s{constructor(e,t,s){super(t,s),this.ribbonTabs=e}queryItems(){return this.queryItemsBySelector(`:scope.${r.Item}`)}}class l extends s{constructor(e,t,s){super(t,s),this.ribbonTabs=e}queryItems(){return this.queryItemsBySelector(`:scope .${r.Item}`)}createItemStrategy(e){return new m(this.ribbonTabs,this.navigator,e)}handleKeyDown(e){const t=d.KeyUtils.getEventKeyCode(e);if(this.ribbonTabs.isSideTabs){if(t===d.KeyCode.Down)return this.moveForward(e),!0;if(t===d.KeyCode.Up)return this.moveBackward(e),!0}else{if(t===d.KeyCode.Right)return this.moveForward(e),!0;if(t===d.KeyCode.Left)return this.moveBackward(e),!0}return t===d.KeyCode.Tab?(e.shiftKey?this.leaveBackward():this.leaveForward(),!0):(t===d.KeyCode.Delete&&this.performShortcutEvent(e),!1)}moveForward(e){this.moveToNextItem(!0),this.performShortcutEvent(e)}moveBackward(e){this.moveToPrevItem(!0),this.performShortcutEvent(e)}getShortcutContext(){return{TabId:this.getSelectedTabId()}}focusSelectedItem(){var e;((null===(e=document.activeElement)||void 0===e?void 0:e.classList.contains(r.Prefix))||this.isTabActive())&&i.focusElement(this.selectedItemElement)}isTabActive(){var e;return null===(e=document.activeElement)||void 0===e?void 0:e.classList.contains(r.Item)}getSelectedTabId(){const e=this.getItem(this.selectedItemIndex);return null==e?void 0:e.getAttribute(a)}updateSelectedItemByChildElement(e,t=null){if(e===this.targetElement){const e=this.ribbonTabs.getActiveTab(),t=this.items.findIndex((t=>t===e));this.updateSelectedItemByIndex(this.validateItemIndex(t))}else super.updateSelectedItemByChildElement(e,t)}}class b extends t{constructor(){super(...arguments),this._ribbonTabs=null,this.tabId=""}connectedOrContentChanged(){if(this.keyboardNavigator=this.querySelector(`:scope > ${o}`),this.keyboardNavigator&&"initialized"in this.keyboardNavigator&&!this.keyboardNavigator.initialized){this._ribbonTabs=document.querySelector(`#${this.tabId}`);const e=new l(this._ribbonTabs,this.keyboardNavigator,this);this.keyboardNavigator.initialize(this,e)}}initializeKeyboardNavigator(){if(this.keyboardNavigator=this.querySelector(`:scope > ${o}`),this.keyboardNavigator&&"initialized"in this.keyboardNavigator&&!this.keyboardNavigator.initialized){const e=new l(this._ribbonTabs,this.keyboardNavigator,this);this.keyboardNavigator.initialize(this,e)}}}e([n({attribute:"data-dxtabs-id"})],b.prototype,"tabId",void 0);let h=class extends b{};h=e([c("dxbl-tab-list")],h);export{h as DxTabList};
