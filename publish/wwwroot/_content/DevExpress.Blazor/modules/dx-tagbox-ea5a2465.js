import{_ as e}from"./tslib.es6-d65164b3.js";import{DxComboBox as t}from"./dx-combobox-5b8c7dc7.js";import{k as s}from"./key-ffa272aa.js";import{g as o}from"./constants-da6cacac.js";import{n as r}from"./property-4ec0b52d.js";import{e as i}from"./custom-element-267f9a21.js";import"./browser-3fc721b7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./adaptivedropdowncomponents-7cb91d74.js";import"./dropdowncomponents-3d8f06da.js";import"./dropdown-f5b2318c.js";import"./popup-355ecaa4.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./lit-element-462e7ad3.js";import"./eventhelper-8bcec49f.js";import"./logicaltreehelper-67db40f1.js";import"./portal-b3727c25.js";import"./data-qa-utils-8be7c726.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./common-48ec40e2.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./custom-events-helper-e7f279d3.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./dom-utils-d057dcaa.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./thumb-31d768d7.js";import"./query-44b9267f.js";import"./popupportal-bbd2fea0.js";import"./events-interseptor-a522582a.js";import"./modalcomponents-951e20e2.js";import"./dx-dropdown-base3-726be7be.js";import"./masked-input-0c0a9541.js";import"./text-editor-733d5e56.js";import"./single-slot-element-base-01d93921.js";import"./input-66769c52.js";import"./constants-3209ffde.js";import"./dx-listbox-9345a0b2.js";let p=class extends t{constructor(){super(),this.customTagsEnable=!1,this.inputValue=""}connectedCallback(){super.connectedCallback()}disconnectedCallback(){super.disconnectedCallback()}onTextInput(e){this.inputValue=e.target.value,this.fitInputWidth(),super.onTextInput(e)}processFocusOut(e){var t;if(!this.inputElement)return;const s=null===(t=this.inputElement)||void 0===t?void 0:t.value,o=s&&""!==s;super.processFocusOut(e),!this.focused&&o&&this.setInputValue("")}fitInputWidth(){if(!this.inputElement)return;const e=this.inputValue.length;this.inputElement.setAttribute("size",0===e?"1":e.toString())}forceInputValue(e,t){this.setInputValue(e),t&&this.highlightInputText()}highlightInputText(){this.inputElement&&this.inputElement.select()}applyTextPropertyCore(){}processKeyDownServerCommand(e){return this.isKeyDownPreventingDefaultNeeded(e)&&e.preventDefault(),super.processKeyDownServerCommand(e)}isKeyDownPreventingDefaultNeeded(e){var t;if(e.keyCode!==s.KeyCode.Enter)return!1;const o=null===(t=this.inputElement)||void 0===t?void 0:t.value;return!(!o||0===o.length)}requireSendKeyCommandToServer(e){if(super.requireSendKeyCommandToServer(e))return!0;switch(e.keyCode){case s.KeyCode.Esc:return!0;case s.KeyCode.Backspace:return!this.inputElement||0===this.inputElement.value.length}return!1}setInputValue(e){this.inputElement&&(this.inputElement.value=e,this.inputValue=e&&""!==e?e:this.inputElement.placeholder),this.fitInputWidth()}};function n(e,t,s){e.forceInputValue(t,s)}e([r({type:Boolean,attribute:"custom-tags-enable"})],p.prototype,"customTagsEnable",void 0),p=e([i(o)],p);const a={loadModule:function(){},forceInputValue:n};export{a as default,n as forceInputValue};
