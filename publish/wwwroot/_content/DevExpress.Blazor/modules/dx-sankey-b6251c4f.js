import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t}from"./devextreme-widget-wrapper-33881f73.js";import{c as s}from"./create-after-timeout-fn-executor-38b3d79d.js";import{C as n}from"./custom-events-helper-e7f279d3.js";import{C as r}from"./events-a8fe5872.js";import{C as o}from"./client-component-style-helper-195fa7c3.js";import{e as i}from"./custom-element-267f9a21.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./utils-b5b2c8a9.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";const a="dxbl-sankey";class l extends r{constructor(){super(l.eventName)}}l.eventName=a+".drawn";class c extends r{constructor(){super(c.eventName)}}c.eventName=a+".exported";class d{constructor({label:e,linksIn:t,linksOut:s}){this.Node={label:e,linksIn:t,linksOut:s}}}class p{constructor(e){this.Connection=e}}class m extends CustomEvent{constructor(e){super(m.eventName,{detail:new d(e),bubbles:!0,composed:!0,cancelable:!0})}}m.eventName=a+".nodeClick";class u extends CustomEvent{constructor(e){super(u.eventName,{detail:new p(e),bubbles:!0,composed:!0,cancelable:!0})}}u.eventName=a+".linkClick",n.register(l.eventName,(e=>e.detail)),n.register(c.eventName,(e=>e.detail)),n.register(m.eventName,(e=>e.detail)),n.register(u.eventName,(e=>e.detail));const h="dxbl-sankey",b="exportTo",g="print",k="svg",j="getAllLinks",x="getAllNodes",v="color",C="font",$="subtitle",f="title",w="label",N=`${C}.${v}`,O=`${f}.${N}`,D=`${f}.${$}.${N}`,E=`${w}.${N}`,y=`--${h}`,_=`${C}-${v}`,S=`${f}-${_}`,I=`${$}-${_}`,H=`${w}-${_}`;let L=class extends t{constructor(){super(),this._styleHelper=o.getInstance(),this._drawnExecutor=s(this.onDrawn.bind(this))}getWidgetTypeName(){return"dxSankey"}createWidgetDefaultOptions(){return{tooltip:{enabled:!1},palette:this._styleHelper.palette}}createWidgetHandlers(){return{...super.createWidgetHandlers(),onDrawn:()=>this.onDrawn(),onOptionChanged:()=>this.onOptionChanged(),onNodeClick:e=>this.onNodeClick(e.target),onLinkClick:e=>this.onLinkClick(e.target),onExported:()=>this.onExported()}}processSpecialOptions(e){super.processSpecialOptions(e),e.dataSource&&(e.dataSource=this._prepareDataSource(e.dataSource))}_prepareDataSource(e){const t=e.sources||[],s=e.targets||[],n=e.weights||[],r=[];for(let e=0;e<t.length;e++)r.push({source:t[e],target:s[e],weight:n[e]});return r}onDrawn(){this._drawnExecutor.reset(),this.changeLoadingPanelVisibility(!1),this.dispatchEvent(new l)}onOptionChanged(){this._drawnExecutor.execute()}onNodeClick({label:e,linksIn:t,linksOut:s}){this.dispatchEvent(new m({label:e,linksIn:t,linksOut:s}))}onLinkClick({connection:e}){this.dispatchEvent(new u(e))}onlyContainerSizeChanged(){var e;null===(e=this._widgetPromise)||void 0===e||e.then((e=>{e.render()})),this.onDrawn()}onExported(){this.dispatchEvent(new c)}getThemeDependentOptionsDict(){const e={[O]:`${y}-${S}`,[D]:`${y}-${I}`,[E]:`${y}-${H}`};return{...super.getThemeDependentOptionsDict(),...e}}[b](...e){return this.executeClientMethod(b,...e)}[g](...e){return this.executeClientMethod(g,...e)}[k](...e){return this.executeClientMethod(k,...e)}[j](...e){return this.executeClientMethod(j,...e).then((e=>e.map((e=>e.connection))))}[x](...e){return this.executeClientMethod(x,...e).then((e=>e.map((e=>({label:e.label,linksIn:e.linksIn,linksOut:e.linksOut})))))}};L=e([i(h)],L);export{L as DxSankey};
