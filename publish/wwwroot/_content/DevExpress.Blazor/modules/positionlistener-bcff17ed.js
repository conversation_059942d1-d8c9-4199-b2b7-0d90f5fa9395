import{PositionChangingEvent as e}from"./positiontracker-754c1e75.js";import{E as t}from"./eventhelper-8bcec49f.js";import{R as n}from"./rafaction-bba7928b.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";const i=document.createElement("template");i.innerHTML='\n <div class="inner popover fade show bs-popover-right" role="tooltip" style="position: absolute; left: 0px; top: 0px">\n    <div class="arrow" style="top: 34px;"></div>\n    <h3 class="popover-header">test</h3>\n    <div class="popover-body">test</div>\n  </div>\n  <slot/>';class s extends HTMLElement{constructor(){super(...arguments),this.listenedIdField="",this.positionChangingHandler=this.handlePositionChanging.bind(this),this.rafAction=new n,this.innerElement=null}get listenerId(){return this.listenedIdField}set listenerId(e){this.listenedIdField=e}connectedCallback(){this.attachShadow({mode:"open"}).appendChild(i.content.cloneNode(!0)),this.addEventListener(e.eventName,this.positionChangingHandler),window.addEventListener(e.eventName,this.positionChangingHandler)}disconnectedCallback(){}static get observedAttributes(){return["listener-id"]}attributeChangedCallback(e,t,n){if("listener-id"===e)this.listenerIdChanged(n)}listenerIdChanged(e){this.listenerId=e}handlePositionChanging(e){if(!this.listenerId)return;if(e.detail.Tracker.listenerId!==this.listenerId)return;if(t.markHandled(e),!this.innerElement){if(this.innerElement=this.shadowRoot.querySelector(".inner"),!this.innerElement)return;this.closest("body").appendChild(this.innerElement)}const n=e.detail.Tracker.getTargetBoundingClientRect();this.innerElement.style.transform=["translate(",Math.round(n.x+n.width),"px, ",Math.round(n.y),"px)"].join("")}}function r(){}customElements.define("dxbl-position-listener",s);const o={init:r,PositionListener:s};export{s as PositionListener,o as default,r as init};
