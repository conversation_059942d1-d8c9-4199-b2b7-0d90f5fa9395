import{d as t}from"./dom-554d0cc7.js";import{D as e}from"./dx-html-element-base-3262304e.js";import{DxUIHandlersBridgeTagName as n}from"./dx-ui-handlers-bridge-c2148178.js";import{a as i,H as s,b as o,u as r}from"./dom-utils-d057dcaa.js";import{H as a,d as l,e as p,P as c,a as d,c as u}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{S as m}from"./dx-scroll-viewer-da0fb41c.js";import{t as h}from"./touch-6a322081.js";import{_ as g}from"./tslib.es6-d65164b3.js";import{S as f}from"./scroll-viewer-css-classes-e724f203.js";import{S as A}from"./single-slot-element-base-01d93921.js";import{F as C,K as v,L as y,D as T}from"./keyboard-navigation-strategy-ea41c807.js";import{n as b}from"./property-4ec0b52d.js";import{k as I}from"./key-ffa272aa.js";import{b as S}from"./constants-da6cacac.js";import{T as w}from"./tabbable-a2ae89a6.js";import{DxCheckBoxTagName as N}from"./dx-check-internal-a6f9ec37.js";import{d as E}from"./constants-a4904a3f.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./data-qa-utils-8be7c726.js";import"./dx-license-30fd02d1.js";import"./css-classes-c63af734.js";import"./eventhelper-8bcec49f.js";import"./constants-7c047c0d.js";import"./devices-17b9ba08.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./focushelper-2eea96ca.js";import"./custom-events-helper-e7f279d3.js";import"./focus-utils-ae044224.js";import"./disposable-d2c2d283.js";class D{static throttle(t,e){let n=!1,i=null,s=this;return function o(r){if(n)return i=r,void(s=this);t.apply(this,[r]),n=!0,setTimeout((()=>{n=!1,i&&(o.apply(s,[i]),i=null)}),e)}}}class x{}x.ContainerIndex="data-container-index",x.FirstContainerIndex="data-first-container-index",x.LastContainerIndex="data-last-container-index",x.FirstCellIndex="data-first-cell-index",x.LastCellIndex="data-last-cell-index",x.ColumnsCount="data-columns-count",x.Column="data-column",x.Row="data-row",x.RowSpan="data-row-span",x.Key="data-key",x.Start="data-start",x.End="data-end",x.Duration="data-duration",x.AllDay="data-allday",x.Resource="data-resource",x.SnapToCellsMode="snap-to-cells-mode",x.AppointmentTopPosition="data-appointment-top-position",x.UtcOffset="data-uct-offset";class H{}var V;H.TimeCellsContainerClassName="dxbl-sc-timecells-container",H.VerticalAppointmentsClassName="dxbl-sc-vertical-apts",H.HorizontalAppointmentsClassName="dxbl-sc-horizontal-apts",H.VerticalAppointmentClassName="dxbl-sc-vertical-apt",H.HorizontalAppointmentClassName="dxbl-sc-horizontal-apt",H.CompactAppointmentClassName="dxbl-sc-apt-compact",H.EditableAppointmentClassName="dxbl-sc-apt-edited",H.DateCellHeadClassName="dxbl-date-cell-head",H.MonthViewClassName="dxbl-sc-month",H.DateHeaderClassName="dxbl-sc-date-hr",H.TimeCellClassName="dxbl-sc-time-cell",H.TodayClassName="dxbl-sc-today",H.HorizontalViewClassName="dxbl-sc-horizontal-view",H.TimeMarkerWrapperClassName="dxbl-sc-time-marker-wrapper",H.TimeMarkerWrapperInvisibleClassName="dxbl-sc-time-marker-wrapper-invisible",H.TimeMarkerClassName="dxbl-sc-time-marker",H.TimeMarkerImageClassName="dxbl-sc-time-marker-image",H.TimeMarkerLineClassName="dxbl-sc-time-marker-line",H.TimeIndicatorClassName="dxbl-sc-time-indicator",H.AppointmentClassName="dxbl-sc-apt",H.SelectedAppointmentClassName="dxbl-sc-apt-selected",H.TargetAppointmentClassName="dxbl-sc-apt-target",H.DisableAppointmentClassName="dxbl-sc-apt-disable",H.VerticalResourceHeaderClassName="dxbl-v-resource-header",H.ResourcesContainerClassName="dxbl-sc-resources-container",H.HeadersContainerClassName="dxbl-sc-headers-container",H.AllDayAreaClassName="dxbl-sc-all-day-area",H.TimelineViewClassName="dxbl-sc-timeline",H.TopHandleClassName="dxbl-top-handle",H.BottomHandleClassName="dxbl-bottom-handle",H.LeftHandleClassName="dxbl-left-handle",H.RightHandleClassName="dxbl-right-handle",H.VerticalViewClassName="dxbl-sc-vertical-view",H.View="dxbl-view",H.ToolbarWrapper="dxbl-sc-tb-wrapper",H.ResourceNavigatorFooter="dxbl-rn-footer",H.ResourceNavigatorSelectAllContainer="dxbl-select-all-container",H.ResourceNavigatorSearchBox="dxbl-resource-navigator-search-box",H.ResourceNavigatorListBoxContainer="dxbl-resource-navigator-list-box-container",function(t){t[t.None=0]="None",t[t.Auto=1]="Auto",t[t.Always=2]="Always"}(V||(V={}));const z=V;class K{getContainerIndex(t){return K.getAttributeIntValue(t,x.ContainerIndex,-1)}getFirstContainerIndex(t){return K.getAttributeIntValue(t,x.FirstContainerIndex,-1)}getLastContainerIndex(t){return K.getAttributeIntValue(t,x.LastContainerIndex,-1)}getAppointmentFirstCellIndex(t){return K.getAttributeIntValue(t,x.FirstCellIndex,-1)}getAppointmentLastCellIndex(t){return K.getAttributeIntValue(t,x.LastCellIndex,-1)}getAppointmentColumnsCount(t){return K.getAttributeIntValue(t,x.ColumnsCount,0)}setAppointmentColumnsCount(t,e){t.setAttribute(x.ColumnsCount,e)}getAppointmentColumn(t){return K.getAttributeIntValue(t,x.Column,0)}setAppointmentColumn(t,e){t.setAttribute(x.Column,e)}getAppointmentRow(t){return K.getAttributeFloatValue(t,x.Row,-1)}getAppointmentRowSpan(t){return K.getAttributeFloatValue(t,x.RowSpan,0)}getAppointmentKey(t){return t.getAttribute(x.Key)||""}getAppointmentTopPosition(t){return K.getAttributeIntValue(t,x.AppointmentTopPosition,0)}setAppointmentTopPosition(t,e){t.setAttribute(x.AppointmentTopPosition,e)}removeAppointmentTopPosition(t){t.removeAttribute(x.AppointmentTopPosition)}getStart(t){const e=new Date(parseInt(t.getAttribute(x.Start)||""));return O.dateIncreaseWithUtcOffset(e,e.getTimezoneOffset()*O.HourSpan)}getEnd(t){const e=parseInt(t.getAttribute(x.Start)||""),n=parseInt(t.getAttribute(x.End)||"");return O.dateIncreaseWithUtcOffset(this.getStart(t),n-e)}getDuration(t){return parseInt(t.getAttribute(x.Duration)||"")}getAppointmentEnd(t){const e=this.getStart(t),n=this.getDuration(t);return new Date(e.getTime()+n)}getAllDay(t){return""===t.getAttribute(x.AllDay)}getResourceKey(t){return t.getAttribute(x.Resource)||""}getSnapToCellsMode(t){const e=t.getAttribute(x.SnapToCellsMode);return null===e?null:z[e]}getUtcOffset(t){return K.getAttributeIntValue(t,x.UtcOffset,0)}static getAttributeIntValue(t,e,n){return K.getAttributeParsedNumberValue(t,e,parseInt,n)}static getAttributeFloatValue(t,e,n){return K.getAttributeParsedNumberValue(t,e,parseFloat,n)}static getAttributeParsedNumberValue(t,e,n,i){const s=t.getAttribute(e);return s?n(s):i}}class k{static getVerticalAppointmentsContainer(t){return t.querySelectorAll(`.${H.VerticalAppointmentsClassName}`)[0]}static getHorizontalAppointmentsContainer(t){return t.querySelectorAll(`.${H.HorizontalAppointmentsClassName}`)[0]}static getHorizontalAppointments(t){const e=t.querySelectorAll(`.${H.HorizontalAppointmentClassName}`);return Array.from(e)}static getHorizontalEditableAppointments(t){const e=t.querySelectorAll(`.${H.HorizontalAppointmentClassName}.${H.EditableAppointmentClassName}`);return Array.from(e)}static getVerticalAppointments(t){const e=t.querySelectorAll(`.${H.VerticalAppointmentClassName}`);return Array.from(e)}static getVerticalEditableAppointments(t){const e=t.querySelectorAll(`.${H.VerticalAppointmentClassName}.${H.EditableAppointmentClassName}`);return Array.from(e)}static getHorizontalView(t){return t.querySelectorAll(`.${H.HorizontalViewClassName}`)[0]}static getTimeMarkerWrappers(t){return t.querySelectorAll(`.${H.TimeMarkerWrapperClassName}`)}static getTimeMarkerContainer(t){return t.querySelectorAll(`.${H.TimeMarkerClassName}`)[0]}static getTimeMarkerImages(t){const e=t.querySelectorAll(`.${H.TimeMarkerImageClassName}`);return Array.from(e)}static getTimeMarkerLine(t){return t.querySelectorAll(`.${H.TimeMarkerLineClassName}`)[0]}static getTimeIndicatorContainer(t){return t.querySelectorAll(`.${H.TimeIndicatorClassName}`)[0]}static getAppointmentContainer(t){return i(t,H.AppointmentClassName)}static getTimeCellContainer(t){return s(t,"",(function(t,e){return k.isTimeCellElement(t)}))}static getSelectedAppointment(t){return t.querySelectorAll(`.${H.SelectedAppointmentClassName}:not(.${H.DisableAppointmentClassName})`)[0]}static getEditableAppointments(t){const e=t.querySelectorAll(`.${H.EditableAppointmentClassName}`);return Array.from(e)}static isEditableAppointment(e){return t.DomUtils.hasClassName(e,H.EditableAppointmentClassName)}static getVerticalResourceHeaders(t){return t.querySelectorAll(`.${H.VerticalResourceHeaderClassName}`)}static getDateHeaders(t){const e=k.isMonthView(t)?H.TimeCellClassName:H.DateHeaderClassName;return t.querySelectorAll(`.${e}`)}static getResourcesViewport(t){return t.querySelectorAll(`.${H.ResourcesContainerClassName}`)[0]}static getTimescaleViewport(t){return t.querySelectorAll(`.${H.HeadersContainerClassName}`)[0]}static getTimeCellsViewport(t){return t.querySelectorAll(`.${H.TimeCellsContainerClassName}`)[0]}static getParentTimeCellsViewport(t){return i(t,H.TimeCellsContainerClassName)}static isVerticalAppointment(e){return t.DomUtils.hasClassName(e,H.VerticalAppointmentClassName)}static isHorizontalAppointment(e){return t.DomUtils.hasClassName(e,H.HorizontalAppointmentClassName)}static isTimeCellElement(e){return t.DomUtils.hasClassName(e,H.AllDayAreaClassName)||t.DomUtils.hasClassName(e,H.TimeCellClassName)}static isTimelineView(e){return t.DomUtils.hasClassName(e,H.TimelineViewClassName)}static isMonthView(e){return t.DomUtils.hasClassName(e,H.MonthViewClassName)}static isTopHandleElement(e){return t.DomUtils.hasClassName(e,H.TopHandleClassName)}static isBottomHandleElement(e){return t.DomUtils.hasClassName(e,H.BottomHandleClassName)}static isLeftHandleElement(e){return t.DomUtils.hasClassName(e,H.LeftHandleClassName)}static isRightHandleElement(e){return t.DomUtils.hasClassName(e,H.RightHandleClassName)}static isAppointmentSelected(e){return t.DomUtils.hasClassName(e,H.SelectedAppointmentClassName)}static isTargetAppointment(e){return t.DomUtils.hasClassName(e,H.TargetAppointmentClassName)}static isDateCellHeader(e){return t.DomUtils.hasClassName(e,H.DateCellHeadClassName)}static getToolbarWrapper(t){return t.querySelectorAll(`.${H.ToolbarWrapper}`)[0]}}k.Attr=new K;class O{static dateSubsWithTimezone(t,e){return t.valueOf()-e.valueOf()+(e.getTimezoneOffset()-t.getTimezoneOffset())*O.HourSpan}static truncToDate(t){return new Date(t.getFullYear(),t.getMonth(),t.getDate())}static calculateDaysDifference(t,e){const n=O.truncToDate(t),i=O.truncToDate(e);return O.dateSubsWithTimezone(i,n)/O.DaySpan}static dateIncreaseWithUtcOffset(t,e){const n=O.dateIncrease(t,e),i=(n.getTimezoneOffset()-t.getTimezoneOffset())*O.HourSpan;return O.dateIncrease(n,i)}static dateIncrease(t,e){return new Date(t.valueOf()+e)}static addTimeSpan(t,e){return new Date(t.valueOf()+e)}static toDayTime(t){return t.valueOf()-O.truncToDate(t).valueOf()}static getCellInterval(t){const e=k.Attr.getStart(t),n=k.Attr.getEnd(t);return new B(e,this.dateSubsWithTimezone(n,e))}static dateTimeComparer(t,e){const n=e.getTime()-t.getTime();return 0===n?0:n<0?1:-1}static dateTimeIndexComparer(t,e,n){return O.dateTimeComparer(t[e],n)}static getUtcTimezoneOffset(t){return t.getTimezoneOffset()*O.HourSpan*-1}static getCurrentLocalTime(){const t=(new Date).getTime();return new Date(t)}}O.HourSpan=6e4,O.HalfHourSpan=18e5,O.DaySpan=864e5;class B{constructor(t,e){this.start=t,this.duration=e,this.isLongerOrEqualDay=e>=O.DaySpan}getStart(){return this.start}getDuration(){return this.duration}getEnd(){return O.dateIncreaseWithUtcOffset(this.start,this.duration)}intersectsWith(t){return t.getEnd().valueOf()-this.getStart().valueOf()>=0&&t.getStart().valueOf()-this.getEnd().valueOf()<=0}}class M{constructor(t,e,n){this.id=t,this.views=e,this.interval=n,this.allDay=k.Attr.getAllDay(e[0]),this.resourceKey=k.Attr.getResourceKey(e[0]),this.sourceView=this.views[0].cloneNode(!0),this.aptCont=this.views[0].parentElement}getStart(){return this.interval.start}getDuration(){return this.interval.duration}getEnd(){return this.interval.getEnd()}clearViews(){this.views.forEach((t=>{t.parentElement&&t.parentElement.removeChild(t)})),this.views=[]}static createItem(t,e){return new M(t,e,M.getAppointmentInterval(e))}static getAppointmentInterval(t){const e=k.Attr.getStart(t[0]),n=k.Attr.getDuration(t[0]);return new B(e,n)}static createItems(t){const e={};for(let n,i=0;n=t[i];i++){const t=k.Attr.getAppointmentKey(n);e[t]||(e[t]=[]),e[t].push(n)}const n=[];for(const t in e)Object.prototype.hasOwnProperty.call(e,t)&&n.push(M.createItem(t,e[t]));return n}}var R;!function(t){t[t.Horizontal=0]="Horizontal",t[t.Vertical=1]="Vertical"}(R||(R={}));const U=R;class F{}var P,L;F.CreateAppointment="createAppointment",F.SelectAppointment="selectAppointment",F.DragAppointment="dragAppointment",F.DropNewAppointment="dropNewAppointment",F.DropAppointment="dropAppointment",F.StartDragAppointment="startDragAppointment",F.StartDragNewAppointment="startDragNewAppointment",F.CancelDragAppointment="cancelDragAppointment",F.ShowAppointmentToolTip="showAppointmentToolTip",F.HideAppointmentToolTip="hideAppointmentToolTip",function(t){t[t.None=0]="None",t[t.DragAppointment=1]="DragAppointment",t[t.DragAppointmentStart=2]="DragAppointmentStart",t[t.DragAppointmentEnd=3]="DragAppointmentEnd",t[t.DragNewAppointment=4]="DragNewAppointment"}(P||(P={})),function(t){t[t.Top=0]="Top",t[t.Bottom=1]="Bottom"}(L||(L={}));class j{constructor(t){this.onMouseMoveHandler=this.onMouseMove.bind(this),this.onMouseMoveInternalHandler=this.onMouseMoveInternal.bind(this),this.scheduler=t,this.schedulerView=null,this.appointmentInfo=null,this.sourceAppointmentInfo=null,this.startCellInfo=null,this.currentCellInfo=null,this.dateDiff=0,this.horizontalAppointmentDragged=!1,this.lastCellChanged=!1,this.initialPageY=0,this.initialTop=0,this.initialScrollTop=0,this.lastTop=0,this.lastCell=null,this.direction=L.Top,this.mode=P.None,this.throttledDrag=D.throttle(this.onMouseMoveInternalHandler,20)}dragAppointmentStart(t,e,n){this.mode=P.DragAppointmentStart,this.startDrag(t,e,n)}dragAppointmentEnd(t,e,n){this.mode=P.DragAppointmentEnd,this.startDrag(t,e,n)}dragAppointment(t,e,n){this.mode=P.DragAppointment,this.startDrag(t,e,n)}dragNewAppointment(t){this.mode=P.DragNewAppointment,this.startDragNewAppointment(t)}dropAppointment(){var t,e;this.mode!==P.None&&(this.scheduler.removeEventListener("pointermove",this.onMouseMoveHandler),this.stopAutoScrolling(),this.mode===P.DragNewAppointment?null===(t=this.getUIHandlersBridge())||void 0===t||t.send(F.DropNewAppointment,""):(this.removeAppointmentTopPositionInfo(),null===(e=this.getUIHandlersBridge())||void 0===e||e.send(F.DropAppointment,"")),this.schedulerView=null,this.mode=P.None)}cancelDragAppointment(){var t;this.mode!==P.None&&(this.scheduler.removeEventListener("pointermove",this.onMouseMoveHandler),this.stopAutoScrolling(),this.mode!==P.DragNewAppointment&&this.removeAppointmentTopPositionInfo(),null===(t=this.getUIHandlersBridge())||void 0===t||t.send(F.CancelDragAppointment,""),this.schedulerView=null,this.mode=P.None)}startDrag(t,e,n){var i;this.beforeStart(e),this.appointmentInfo=new M(t.id,t.views,t.interval),this.sourceAppointmentInfo=t,this.currentCellInfo=e,this.dateDiff=O.dateSubsWithTimezone(t.getStart(),e.interval.start),this.horizontalAppointmentDragged=e.layoutType===U.Horizontal,this.initialTop=this.findAppointmentViewByInterval(this.appointmentInfo.views,e.interval).offsetTop,this.initialPageY=n.pageY,this.lastTop=this.initialTop,this.lastCell=e.cell,this.initialScrollTop=this.getViewportScrollTop(),this.saveAppointmentTopPositionInfo(),null===(i=this.getUIHandlersBridge())||void 0===i||i.send(F.StartDragAppointment,{key:t.id,resize:this.mode===P.DragAppointmentStart||this.mode===P.DragAppointmentEnd}),this.startAutoScrolling()}startDragNewAppointment(t){this.beforeStart(t);const e=t.interval.start,n=O.dateIncreaseWithUtcOffset(e,t.interval.duration),i=this.scheduler.getUtcOffset(e,t.utcOffset),s=this.scheduler.getUtcOffset(n,t.utcOffset);this.dispatchEvent(F.StartDragNewAppointment,O.addTimeSpan(e,i),O.addTimeSpan(n,s),t.allDay,k.Attr.getResourceKey(t.cell)),this.startAutoScrolling()}beforeStart(t){this.startCellInfo=t,this.scheduler.addEventListener("pointermove",this.onMouseMoveHandler)}onMouseMove(t){this.mode!==P.None&&this.throttledDrag(t)}onMouseMoveInternal(t){var e;const n=null===(e=this.getSchedulerView())||void 0===e?void 0:e.findCellByCoordinates(t.clientX,t.clientY);if(!n)return;this.scheduler.getSelectedAppointmentInfo()&&(this.mode===P.DragNewAppointment?this.resizeNewAppointment(n):this.mode===P.DragAppointment?this.drag(n,t):this.resize(n,this.mode===P.DragAppointmentStart))}drag(t,e){var n;if(!(this.startCellInfo&&this.currentCellInfo&&this.appointmentInfo&&this.sourceAppointmentInfo))return;const i=this.currentCellInfo.interval;this.lastCellChanged=!1,this.updateEditedAppointmentPosition(e);const s=t.cell;if(this.lastCellChanged=this.lastCell!==s,this.lastCell=s,t.layoutType===U.Horizontal&&!this.horizontalAppointmentDragged){const t=(null===(n=s.offsetParent)||void 0===n?void 0:n.getBoundingClientRect().top)||0;this.initialTop=e.clientY-t,this.initialPageY=e.pageY,this.initialScrollTop=this.getViewportScrollTop(s),this.lastTop=this.initialTop}const o=t.interval;if(0===O.dateSubsWithTimezone(i.start,o.start)&&i.duration===o.duration){const t=k.Attr.getResourceKey(s);if(!this.appointmentInfo.resourceKey||this.appointmentInfo.resourceKey===t)return}if(this.currentCellInfo.allDay!==t.allDay)this.dateDiff=0,this.appointmentInfo.interval=t.allDay?this.sourceAppointmentInfo.allDay?new B(o.start,this.sourceAppointmentInfo.interval.duration):o:this.startCellInfo.allDay?o:new B(o.start,this.sourceAppointmentInfo.interval.duration),this.appointmentInfo.allDay=t.allDay;else{let t=O.addTimeSpan(o.start,this.dateDiff||0);this.appointmentInfo.allDay&&(t=O.truncToDate(t)),this.appointmentInfo.interval=new B(t,this.appointmentInfo.interval.duration)}this.updateResource(s),this.saveAppointmentTopPositionInfo();const r=this.scheduler.getUtcOffset(o.getStart(),t.utcOffset),a=this.scheduler.getUtcOffset(o.getEnd(),t.utcOffset);this.dispatchEvent(F.DragAppointment,O.addTimeSpan(this.appointmentInfo.getStart(),r),O.addTimeSpan(this.appointmentInfo.getEnd(),a),this.appointmentInfo.allDay,this.appointmentInfo.resourceKey),this.currentCellInfo=t}resize(t,e){if(!this.appointmentInfo||!this.currentCellInfo||!this.sourceAppointmentInfo)return;const n=t.interval,i=this.currentCellInfo.interval;if(0===O.dateSubsWithTimezone(i.start,n.start)&&i.duration===n.duration||i.isLongerOrEqualDay!==n.isLongerOrEqualDay)return;if(e){const t=this.sourceAppointmentInfo.interval.duration+O.dateSubsWithTimezone(this.sourceAppointmentInfo.interval.start,n.start);t>=0&&(this.appointmentInfo.interval=new B(n.start,t))}else this.appointmentInfo.interval.duration=O.dateSubsWithTimezone(n.getEnd(),this.sourceAppointmentInfo.interval.start);this.updateResource(this.currentCellInfo.cell);const s=this.scheduler.getUtcOffset(this.appointmentInfo.getStart(),t.utcOffset),o=this.scheduler.getUtcOffset(this.appointmentInfo.getEnd(),t.utcOffset);this.dispatchEvent(F.DragAppointment,O.addTimeSpan(this.appointmentInfo.getStart(),s),O.addTimeSpan(this.appointmentInfo.getEnd(),o),this.appointmentInfo.allDay,this.appointmentInfo.resourceKey),this.currentCellInfo=t}resizeNewAppointment(t){if(!this.startCellInfo)return;const e=t.interval,n=O.dateSubsWithTimezone(e.start,this.startCellInfo.interval.start);if(0===n&&this.startCellInfo.interval.duration===e.duration)return;n<0&&this.startCellInfo.interval.duration===e.duration?this.direction=L.Top:n>0&&this.startCellInfo.interval.duration===e.duration&&(this.direction=L.Bottom),this.direction===L.Bottom?this.startCellInfo.interval.duration=n+e.duration:this.direction===L.Top&&(this.startCellInfo.interval.start=e.start,this.startCellInfo.interval.duration+=-1*n);const i=this.startCellInfo.interval.start,s=O.dateIncreaseWithUtcOffset(i,this.startCellInfo.interval.duration),o=k.Attr.getResourceKey(t.cell),r=this.scheduler.getUtcOffset(i,t.utcOffset),a=this.scheduler.getUtcOffset(s,t.utcOffset);this.dispatchEvent(F.DragAppointment,O.addTimeSpan(i,r),O.addTimeSpan(s,a),t.allDay,o)}dispatchEvent(t,e,n,i,s){var o;null===(o=this.getUIHandlersBridge())||void 0===o||o.send(t,{start:e,end:n,isAllDay:i,resourceKey:s})}getUIHandlersBridge(){return this.scheduler.uiHandlersBridge}getSchedulerView(){return null===this.schedulerView&&(this.schedulerView=this.scheduler.getView()),this.schedulerView}findAppointmentViewByInterval(t,e){return t.find((t=>{if(e&&t.firstCell&&t.lastCell){const n=O.getCellInterval(t.firstCell).getStart(),i=O.getCellInterval(t.lastCell).getEnd();if(e.intersectsWith(new B(n,O.dateSubsWithTimezone(i,n))))return t}}))}updateEditedAppointmentPosition(t){const e=this.scheduler.getEditableAppointments();if(!this.lastCell||0===e.length||!this.currentCellInfo)return;const n=this.findAppointmentViewByInterval(e,this.currentCellInfo.interval);if(!n)return;if(!k.isHorizontalAppointment(n))return void(this.horizontalAppointmentDragged=!1);let i=!1;if(this.horizontalAppointmentDragged||(this.initialTop-=n.offsetHeight/2,this.lastTop=this.initialTop,this.horizontalAppointmentDragged=!0,i=!0),t){const e=this.getViewportScrollTop(),n=t.pageY-this.initialPageY+(e-this.initialScrollTop);this.lastTop=this.initialTop+n,i=i||0!==n}if(i||this.lastCellChanged){const t=this.lastCell.offsetTop+1;this.lastTop=Math.min(this.lastTop,t+this.lastCell.offsetHeight-n.offsetHeight-2),this.lastTop=Math.max(t,this.lastTop)}const s=this.lastTop-this.lastCell.offsetTop;e.forEach((t=>{const e=t,n=e.firstCell,i=n.offsetTop+n.offsetHeight-e.offsetHeight-2;let o=n.offsetTop+s;o>=i&&(o=i),e.style.top=o+"px",e.style.zIndex="1000"}))}saveAppointmentTopPositionInfo(){if(!this.lastCell)return;const t=this.lastTop-this.lastCell.offsetTop;k.Attr.setAppointmentTopPosition(this.getSchedulerView(),t.toString())}removeAppointmentTopPositionInfo(){k.Attr.removeAppointmentTopPosition(this.getSchedulerView())}updateResource(t){this.appointmentInfo&&this.appointmentInfo.resourceKey&&(this.appointmentInfo.resourceKey=k.Attr.getResourceKey(t))}getViewportScrollTop(t){const e=t||this.lastCell;if(!e)return 0;const n=k.getParentTimeCellsViewport(e);return(null==n?void 0:n.scrollTop)||0}startAutoScrolling(){var t,e;null===(e=null===(t=this.getSchedulerView())||void 0===t?void 0:t.getScrollViewer())||void 0===e||e.startAutoScrolling(m.Horizontal)}stopAutoScrolling(){var t,e;null===(e=null===(t=this.getSchedulerView())||void 0===t?void 0:t.getScrollViewer())||void 0===e||e.stopAutoScrolling()}}const $=E.toUpperCase();class q extends A{constructor(){super(),this.scheduler=null}connectedCallback(){super.connectedCallback(),this.scheduler=t.DomUtils.getParentByTagName(this,At.TagName),this.scheduler&&this.initEvents()}disconnectedCallback(){super.disconnectedCallback(),document.removeEventListener(h.TouchUtils.touchMouseDownEventName,this.toolTipLostFocusHandler)}initEvents(){this.toolTipLostFocusHandler=t=>{var e,n;const i=k.getAppointmentContainer(t.target);return!(i&&k.isTargetAppointment(i)||!(null===(e=this.scheduler)||void 0===e?void 0:e.uiHandlersBridge))&&function(t,e,n,i){return function(t,e,n,i){let s=t.target;for(;s;){if(s===e||s.tagName===$||s===n)return!1;s=s.parentElement}i&&setTimeout((()=>{i()}),0);return!0}(t,e,i,(function(){e&&"string"!=typeof e&&"none"!==e.style.display&&(null==n||n.send(F.HideAppointmentToolTip,{outsideClick:!0}))}))}(t,this,null===(n=this.scheduler)||void 0===n?void 0:n.uiHandlersBridge,this.scheduler)},document.addEventListener(h.TouchUtils.touchMouseDownEventName,this.toolTipLostFocusHandler)}}var W;q.TagName="dxbl-appointment-tooltip",function(t){t[t.None=0]="None",t[t.AppointmentEditForm=1]="AppointmentEditForm",t[t.AppointmentCompactEditForm=2]="AppointmentCompactEditForm",t[t.AppointmentToolTip=3]="AppointmentToolTip",t[t.RecurrentAppointmentEditForm=4]="RecurrentAppointmentEditForm",t[t.RecurrentAppointmentDeleteForm=5]="RecurrentAppointmentDeleteForm",t[t.GotoDateForm=6]="GotoDateForm"}(W||(W={}));const _=W;class Y{constructor(){this.TIMEOUT=6e4,this.subscriptions=[],this.timerId=-1}subscribe(t,e){if(-1===this.getSubscriptionIndex(t)){this.subscriptions.push({scheduler:t,updateAction:e}),-1===this.timerId&&this.startTimer()}}unsubscribe(t){const e=this.getSubscriptionIndex(t);-1!==e&&this.unsubscribeByIndex(e),0===this.subscriptions.length&&this.stopTimer()}unsubscribeByIndex(t){this.subscriptions.splice(t,1)}getSubscriptionIndex(t){for(let e,n=0;e=this.subscriptions[n];n++)if(e.scheduler===t)return n;return-1}startTimer(){this.timerId=setTimeout(this.onTimeout.bind(this),this.TIMEOUT)}stopTimer(){-1!==this.timerId&&(clearTimeout(this.timerId),this.timerId=-1)}onTimeout(){for(let t,e=0;t=this.subscriptions[e];e++)if(Y.isElementValid(t.scheduler))try{t.updateAction()}catch(t){}else this.unsubscribeByIndex(e),e--;this.startTimer()}static isElementValid(t){for(;null!=t;){if("BODY"===t.tagName)return!0;t=t.parentNode}return!1}}class G{constructor(t,e){this._element=t,this._cellSelector=e,this.containers=null,this.timeCells=null}get element(){return this._element}get cellSelector(){return this._cellSelector}getContainers(){if(!this.containers){const t=this.getTimeCells();this.containers={};for(let e,n=0;e=t[n];n++){const t=k.Attr.getContainerIndex(e);this.containers[t]||(this.containers[t]={cells:[]}),this.containers[t].cells.push(e)}}return this.containers}clearObjects(){this.timeCells=null,this.containers=null}getTimeCells(){return this.timeCells||(this.timeCells=this.element.querySelectorAll(this.cellSelector)),this.timeCells}getFirstContainer(){const t=this.getContainers();for(const e in t)if(Object.prototype.hasOwnProperty.call(t,e))return t[e]}findCell(t){const e=this.getContainers();for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n))for(let i,s=0;i=e[n].cells[s];s++){const e=k.Attr.getStart(i),n=k.Attr.getEnd(i);if(e.valueOf()-t.valueOf()<=0&&t.valueOf()-n.valueOf()<0)return i}return null}findStartCell(t){const e=this.getContainers();for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n))for(let i,s=0;i=e[n].cells[s];s++){const e=k.Attr.getStart(i);if(t.valueOf()-e.valueOf()<=0)return i}return null}findEndCell(t){const e=this.getContainers();for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n))for(let i,s=0;i=e[n].cells[s];s++){const e=k.Attr.getEnd(i);if(t.valueOf()-e.valueOf()<=0)return i}return null}findCellByPos(t,e){const n=this.getTimeCells();for(let i,s=0;i=n[s];s++){const n=i.getBoundingClientRect();if(n.top<=e&&e<n.bottom&&n.left<=t&&t<n.right)return i}return null}}class X{constructor(t,e){this.start=t,this.end=e}containsExcludeEndBound(t){return this.start<=t&&this.end>t}}function J(t,e,n){const i=t[e];return i===n?0:i<n?-1:1}function Q(t,e,n,i,s){n||(n=J);let o=null==i?0:i;null==s&&(s=t.length-o);let r=o+s-1;for(;o<=r;){const i=o+(r-o>>1),s=n(t,i,e);if(0===s)return i;s<0?o=i+1:r=i-1}return-(o+1)}class Z{}Z.state="state",Z.snapToCellsMode="snap-to-cells-mode",Z.editableAppointmentKey="editable-appointment-key";class tt{constructor(t,e,n){this.date=t,this.recurrenceIndex=e,this.resource=n}static from(t){const[e,n,i]=t.split(tt.ClientKeyPartSeparator);return new tt(e,n,i)}static to(t){let e=t.date+tt.ClientKeyPartSeparator+t.recurrenceIndex;return t.resource&&(e=e+tt.ClientKeyPartSeparator+t.resource),e}static IsSame(t,e,n=!1){const i=t.date===e.date&&t.recurrenceIndex===e.recurrenceIndex;return n?i:i&&t.resource===e.resource}}tt.ClientKeyPartSeparator="_";class et{static selectSameAppointments(e,n){n.filter((t=>{const n=k.Attr.getAppointmentKey(t),i=tt.from(n);return tt.IsSame(e,i,!0)})).forEach((e=>{t.DomUtils.hasClassName(e,H.SelectedAppointmentClassName)||t.DomUtils.addClassName(e,H.SelectedAppointmentClassName)}))}}class nt extends A{constructor(){super(),this.editableAppointmentKey="",this.state="",this.snapToCellsMode=z.Auto,this.horizontalAppointments=[],this.appointmentInfos=[],this.DEBUG_calculateHorizontalAppointmentsCount=0}contentChanged(){super.contentChanged(),this.timeMarkerUpdater=new Y,this.scrollViewer=this.querySelector(`.${f.ClassName}`),this.addEventSubscriptions()}disconnectedCallback(){this.removeEventSubscriptions(),super.disconnectedCallback()}recalculateLayout(){this.initializeDateHeaders(),this.initializeTimeCells(),this.initializeAppointments(!1),this.adjust()}adjust(){this.calculateAppointments(!0),this.updateTimeMarkersEventHandler()}recalculateAppointments(t,e){this.initializeAppointments(e),e?this.calculateEditableAppointments():this.calculateAppointments(t)}findCellByCoordinates(t,e){var n;const i=null===(n=this.horizontalView)||void 0===n?void 0:n.findCellByPos(t,e);return i?this.createCellInfo(i,U.Horizontal):null}getEditableAppointmentKey(){return this.editableAppointmentKey}isEditableAppointment(t){return k.Attr.getAppointmentKey(t)===this.getEditableAppointmentKey()&&!k.isTargetAppointment(t)}getScrollViewer(){return this.scrollViewer}restoreFocusedAppointment(t){if(!t)return;const e=this.closest(At.TagName);if(!(null==e?void 0:e.contains(document.activeElement)))return;const n=this.querySelector(`[${x.Key}='${t}']`);if(n)document.activeElement!==n&&(C.makeElementFocusable(n),C.focusElement(n));else{const e=tt.from(t);if(null===e.resource)return;const n=new tt(e.date,e.recurrenceIndex,null),i=tt.to(n),s=this.querySelector(`[${x.Key}^='${i}']`);s&&(C.makeElementFocusable(s),C.focusElement(s))}}createCellInfo(t,e){return{cell:t,layoutType:e,allDay:k.Attr.getAllDay(t),interval:O.getCellInterval(t),utcOffset:k.Attr.getUtcOffset(t)}}calculateEditableAppointments(){var t;if(null===(t=this.horizontalView)||void 0===t?void 0:t.getContainers()){this.resetCalculationInfo();for(let t,e=0;t=this.horizontalAppointments[e];e++){this.calculateHorizontalAppointmentCore(t);const e=k.Attr.getAppointmentTopPosition(this);t.style.top=t.firstCell.offsetTop+e+"px"}}}calculateAppointments(t){var e;const n=null===(e=this.horizontalView)||void 0===e?void 0:e.getContainers();n&&(this.resetCalculationInfo(),t?this.calculateHorizontalAppointmentsAndCells(this,n):this.calculateHorizontalAppointments(n)),this.DEBUG_calculateHorizontalAppointmentsCount++}initializeTimeCells(){var t;null===(t=this.getHorizontalViewPart())||void 0===t||t.clearObjects()}initializeAppointments(t){var e;this.horizontalAppointments=t?k.getHorizontalEditableAppointments(this):k.getHorizontalAppointments(this),this.prepareAppointments(null===(e=this.horizontalView)||void 0===e?void 0:e.getTimeCells(),this.horizontalAppointments),this.appointmentInfos=M.createItems(this.horizontalAppointments)}prepareAppointments(t,e){if(!t)return;const n={};for(let e,i=0;e=t[i];i++){e.intersects=[];const t=k.Attr.getContainerIndex(e);n[t]||(n[t]=[]),n[t].push(e)}for(let t,i=0;t=e[i];i++){const e=k.Attr.getContainerIndex(t),i=k.Attr.getAppointmentFirstCellIndex(t),s=k.Attr.getAppointmentLastCellIndex(t);if(n[e]){t.firstCell=n[e][i],t.lastCell=n[e][s],t.info={cellCount:s-i+1};for(let o=i;o<=s;o++){this.addIntersect(n[e][o],t)}}}}addIntersect(t,e){const n=t.intersects;n.findIndex((t=>k.Attr.getAppointmentKey(t)===k.Attr.getAppointmentKey(e)))<0&&n.push(e)}findTimelineResourceHeader(t,e){if(""===e)return null;const n=k.getVerticalResourceHeaders(t);for(let t,i=0;t=n[i];i++)if(this.getElementResourceKey(t)===e)return t;return null}getElementResourceKey(t){return k.Attr.getResourceKey(t)}prepareResourceHeaders(t,e){const n={};for(const i in e){if(!Object.prototype.hasOwnProperty.call(e,i))continue;const s=this.getElementResourceKey(e[i].cells[0]),o=this.findTimelineResourceHeader(t,s);o&&(o.style.height="",null==n[s]?n[s]={element:o,height:0,minHeight:0,lastContainerIndex:i}:n[s].lastContainerIndex=i)}for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(n[t].minHeight=n[t].element.offsetHeight);return n}calculateHorizontalAppointments(t){for(const e in t){if(!Object.prototype.hasOwnProperty.call(t,e))continue;const n=t[e].cells;for(let t,e=0;t=n[e];e++)this.calculateHorizontalAppointmentsInCell(t)}}calculateHorizontalAppointmentsInCell(t){const e=this.getCalculatedHorizontalAppointmentsInCell(t),n=this.getNotCalculatedHorizontalAppointmentsInCell(t);e.forEach((t=>t.calculated=!0)),n.forEach((t=>t.calculated=!1)),this.calculateHorizontalAptsInCellCore(t,e.concat(n),e.length)}calculateHorizontalAptsInCellCore(t,e,n){this.calculateAppointmentRelativeTopOffset(e,n),this.calculateAppointmentsPosition(t,e)}calculateAppointmentRelativeTopOffset(t,e){this.adjustAppointmentCellIndexes(t),this.calculateAppointmentRelativePositionsCore(t,e)}calculateAppointmentRelativePositionsCore(t,e){const n=t.length,i=this.createBusyIntervals(2*n);let s=0;for(;s<e;)this.isEditableAppointment(t[s])||this.makeIntervalBusy(t[s],i),s++;for(;s<n;){const e=t[s];if(this.calculateHorizontalAppointmentCore(e),!e.calculated){const t=this.findAvailableRelativePosition(e,i);e.relativePosition=t}this.isEditableAppointment(e)||this.makeIntervalBusy(e,i),s++}}findAvailableRelativePosition(t,e){t.relativePosition=0;let n=0;const i=t.info.busyIntervalFirstCellIndex,s=t.info.busyIntervalLastCellIndex;let o=i;for(;o<=s;){const s=this.findPossibleIntersectionInterval(e[o],n);null===s||s.start>=n+t.offsetHeight?o++:(n=s.end,o=i)}return n}findPossibleIntersectionInterval(t,e){for(let n=0;n<t.length;n++){const i=t[n];if(i.containsExcludeEndBound(e)||i.start>e)return new X(i.start,i.end)}return null}calculateHorizontalAppointmentCore(t){const e=t.firstCell,n=e.getBoundingClientRect().width,i=k.Attr.getAppointmentRow(t)-k.Attr.getAppointmentFirstCellIndex(t),s=e.offsetLeft+Math.floor(n*i);let o=n-Math.floor(n*i),r=e.nextElementSibling;const a=k.Attr.getAppointmentRowSpan(t);for(let t=1;t<Math.trunc(a)&&null!=r;t++)o+=r.getBoundingClientRect().width,r=r.nextElementSibling;const l=a-Math.trunc(a);l>0&&(a<1?o-=n-e.getBoundingClientRect().width*l:null!=r&&(o+=r.getBoundingClientRect().width*l)),this.updateAppointmentCompactStyle(t,o),t.style.height="",t.style.width=`${o}px`,t.style.left=`${s}px`,t.style.top="-10000px",t.style.display=""}updateAppointmentCompactStyle(e,n){t.DomUtils.toggleClassName(e.children[0],H.CompactAppointmentClassName,n<50)}createBusyIntervals(t){const e=[];for(let n=0;n<t;n++)e.push([]);return e}makeIntervalBusy(t,e){const n=t.info.busyIntervalLastCellIndex;for(let i=t.info.busyIntervalFirstCellIndex;i<=n;i++)this.addBusyInterval(e[i],new X(t.relativePosition,t.relativePosition+t.offsetHeight))}addBusyInterval(t,e){if(!this.checkBusyIntervalExists(t,e)){let n=t.findIndex((t=>t.start>e.start));-1===n&&(n=t.length),t.splice(n,0,e)}}checkBusyIntervalExists(t,e){return t.some((t=>t.start===e.start&&t.end===e.end))}calculateAppointmentsPosition(t,e){let n=t.offsetTop;if(t.firstElementChild&&k.isDateCellHeader(t.firstElementChild)){const e=window.getComputedStyle(t).paddingTop;n+=t.firstElementChild.offsetHeight+parseInt(e)}for(let t=0;t<e.length;t++){const i=e[t];let s=i.relativePosition+n;if(this.isEditableAppointment(i)){const t=e.find((t=>t.classList.contains(H.DisableAppointmentClassName)));if(!t)continue;s=t.relativePosition+n}i.info.calculatedTop=s,i.style.top=s+"px"}}adjustAppointmentCellIndexes(t){const e=this.createAptsDateTimeCollection(t);this.calculateAdjustedCellIndexes(t,e)}createAptsDateTimeCollection(t){const e=[];for(let n=0;n<t.length;n++){const i=t[n],{startTime:s,endTime:o}=this.getAptAdjustedStartEndTime(i);this.addDateTime(e,s),this.addDateTime(e,o)}return e.sort(O.dateTimeComparer),e}addDateTime(t,e){this.isAlreadyAdded(t,e)||t.push(e)}isAlreadyAdded(t,e){const n=t.length;for(let i=0;i<n;i++)if(t[i].valueOf()===e.valueOf())return!0;return!1}calculateAdjustedCellIndexes(t,e){for(let n=0;n<t.length;n++)this.calculateAdjustedCellIndexesCore(t[n],e)}calculateAdjustedCellIndexesCore(t,e){const{startTime:n,endTime:i}=this.getAptAdjustedStartEndTime(t),s=Q(e,n,O.dateTimeIndexComparer),o=Q(e,i,O.dateTimeIndexComparer)-1;t.info.busyIntervalFirstCellIndex=s,t.info.busyIntervalLastCellIndex=o}getAptAdjustedStartEndTime(t){const e=k.Attr.getSnapToCellsMode(this),n=k.Attr.getAppointmentFirstCellIndex(t),i=k.Attr.getAppointmentLastCellIndex(t),s=e===z.Always||e===z.Auto&&n-i<=1;return{startTime:k.Attr.getStart(s?t.firstCell:t),endTime:s?k.Attr.getEnd(t.lastCell):k.Attr.getAppointmentEnd(t)}}getCalculatedHorizontalAppointmentsInCell(t){return t.intersects.filter((t=>void 0!==t.info.calculatedTop)).sort(((t,e)=>t.info.calculatedTop-e.info.calculatedTop))}getNotCalculatedHorizontalAppointmentsInCell(t){return t.intersects.filter((t=>void 0===t.info.calculatedTop)).sort(((t,e)=>{const n=e.info.cellCount-t.info.cellCount;return n||k.Attr.getStart(t).valueOf()-k.Attr.getStart(e).valueOf()}))}getHorizontalCellHeight(t){let e=-1,n=null;return t.intersects.forEach((t=>{t.info.calculatedTop>e&&(e=t.info.calculatedTop,n=t)})),n?e-t.offsetTop+n.offsetHeight:0}calculateHorizontalAppointmentsAndCells(t,e){const n=this.prepareResourceHeaders(t,e);this.style.height=this.offsetHeight+"px";for(const t in e){if(!Object.prototype.hasOwnProperty.call(e,t))continue;let i=0;const s=e[t].cells;for(let t,e=0;t=s[e];e++){this.calculateHorizontalAppointmentsInCell(t);const e=this.getHorizontalCellHeight(t);e>i&&(i=e)}const o=s[0];let r=i?i+15:0;o.style.height="",r=Math.max(o.offsetHeight,r);const a=n[this.getElementResourceKey(o)];a&&(a.lastContainerIndex===t&&(r=Math.max(a.minHeight-a.height,r)),a.height+=r),o.style.height=r+"px"}this.style.height="";for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(n[t].element.style.height=n[t].height+"px")}initializeDateHeaders(){const t=k.getDateHeaders(this),e=O.getCurrentLocalTime();t.forEach((t=>{const n=k.Attr.getStart(t),i=k.Attr.getEnd(t);i.valueOf()-n.valueOf()>O.DaySpan||(e.valueOf()-n.valueOf()>=0&&i.valueOf()-e.valueOf()>0?t.classList.add(H.TodayClassName):t.classList.remove(H.TodayClassName))}))}resetCalculationInfo(){for(let t,e=0;t=this.horizontalAppointments[e];e++)t.info={cellCount:t.info.cellCount}}addEventSubscriptions(){var t,e;null===(t=this.timeMarkerUpdater)||void 0===t||t.subscribe(this,this.updateTimeMarkersEventHandler.bind(this)),null===(e=this.scrollViewer)||void 0===e||e.subscribeToScroll(this.viewportScrollViewerEventHandler.bind(this))}removeEventSubscriptions(){var t,e;null===(t=this.timeMarkerUpdater)||void 0===t||t.unsubscribe(this),null===(e=this.scrollViewer)||void 0===e||e.unsubscribeFromScroll()}updateTimeMarkersEventHandler(){var t;this.calculateHorizontalTimeMarker(this,null===(t=this.horizontalView)||void 0===t?void 0:t.getFirstContainer())}calculateHorizontalTimeMarker(t,e){const n=k.getTimeMarkerImages(t),i=k.getTimeMarkerLine(t);if(!n.length||!i||!e)return;const s=O.getCurrentLocalTime(),o=e.cells[e.cells.length-1],r=k.Attr.getStart(e.cells[0]),a=k.Attr.getEnd(o).valueOf()-r.valueOf(),l=O.dateSubsWithTimezone(s,r)/a;if(l>0&&l<1){n.forEach((t=>{t.style.display="unset"})),i.style.display="unset";const t=Math.round(1e4*l)/100,e=i.offsetWidth/2,s=`calc(${t}% - ${n[0].offsetWidth/2}px)`;n.forEach((t=>{t.style.left=s})),i.style.left=`calc(${t}% - ${e}px)`}else n.forEach((t=>{t.style.display="none"})),i.style.display="none"}viewportScrollViewerEventHandler(t,e){const n=k.getResourcesViewport(this);n&&(n.scrollTop=t);const i=k.getTimescaleViewport(this);i&&(i.scrollLeft=e)}getHorizontalViewPart(){return this.horizontalView||(this.horizontalView=new G(this,`.${H.AllDayAreaClassName}, .${H.HorizontalViewClassName} .${H.TimeCellClassName}`)),this.horizontalView}updated(t){(t.has("state")||t.has("snapToCellsMode"))&&(this.editableAppointmentKey?(this.recalculateAppointments(!this.editableAppointmentKey,!0),this.recalculateAppointments(!this.editableAppointmentKey,!1)):this.recalculateLayout(),this.restoreFocusedAppointment(t.get("editableAppointmentKey"))),super.updated(t)}selectSameAppointments(t){const e=tt.from(t);null!==e.resource&&et.selectSameAppointments(e,this.horizontalAppointments)}syncronizeScrollViewWithHeader(){}}g([b({attribute:Z.editableAppointmentKey})],nt.prototype,"editableAppointmentKey",void 0),g([b()],nt.prototype,"state",void 0),g([b({attribute:Z.snapToCellsMode})],nt.prototype,"snapToCellsMode",void 0);class it extends nt{constructor(){super()}}it.TagName="dxbl-timeline-view";class st extends nt{constructor(){super(),this.verticalAppointments=[],this.DEBUG_calculateVerticalAppointmentsCount=0}updateTimeMarkersEventHandler(){var t;this.calculateVerticalTimeMarkers(this,null===(t=this.verticalView)||void 0===t?void 0:t.getTimeCells())}findCellByCoordinates(t,e){var n;const i=null===(n=this.verticalView)||void 0===n?void 0:n.findCellByPos(t,e);return i?this.createCellInfo(i,U.Vertical):super.findCellByCoordinates(t,e)}calculateEditableAppointments(){super.calculateEditableAppointments(),this.calculateVerticalAppointments(this.verticalAppointments)}initializeTimeCells(){var t;super.initializeTimeCells(),null===(t=this.getVerticalViewPart())||void 0===t||t.clearObjects()}calculateAppointments(t){super.calculateAppointments(t),this.calculateVerticalAppointments(this.verticalAppointments)}initializeAppointments(t){var e;super.initializeAppointments(t),this.verticalAppointments=t?k.getVerticalEditableAppointments(this):k.getVerticalAppointments(this),this.prepareAppointments(null===(e=this.verticalView)||void 0===e?void 0:e.getTimeCells(),this.verticalAppointments),this.appointmentInfos=this.appointmentInfos.concat(M.createItems(this.verticalAppointments))}calculateVerticalAppointments(t){for(let e,n=0;e=t[n];n++)this.calculateVerticalAppointment(e);this.DEBUG_calculateVerticalAppointmentsCount++}calculateVerticalAppointment(t){const e=t.firstCell,n=(e.offsetWidth-10)/k.Attr.getAppointmentColumnsCount(t),i=e.getBoundingClientRect().height,s=k.Attr.getAppointmentRow(t)-k.Attr.getAppointmentFirstCellIndex(t),o=Math.floor(i*s),r=e.offsetTop+o,a=k.Attr.getAppointmentColumn(t),l=e.offsetLeft+n*a,p=Math.floor(i*k.Attr.getAppointmentRowSpan(t))-o;this.updateAppointmentCompactStyle(t,n),t.style.top=`${r}px`,t.style.left=`${l}px`,t.style.width=`${n}px`,t.style.height=`${p}px`,t.style.display=""}calculateVerticalTimeMarkers(t,e){if(!e)return;const n=O.getCurrentLocalTime(),i=O.truncToDate(n),s=this.getTimeMarkerInfos(t);if(0!==s.length)for(let o,r=0;o=s[r];r++)this.calculateVerticalTimeMarker(o,t,e,i,n)}getTimeMarkerInfos(t){const e=[],n=k.getTimeMarkerWrappers(t);for(let t,i=0;t=n[i];i++){const n={wrapper:t,timeMarker:k.getTimeMarkerContainer(t),timeIndicator:k.getTimeIndicatorContainer(t),firstContainerIndex:k.Attr.getFirstContainerIndex(t),lastContainerIndex:k.Attr.getLastContainerIndex(t),startFromContainer:i>0};e.push(n)}return e}calculateVerticalTimeMarker(t,e,n,i,s){const o=t.wrapper,r=this.findCellsByTime(n,s,t.firstContainerIndex,t.lastContainerIndex);if(!r.length)return void o.classList.add(H.TimeMarkerWrapperInvisibleClassName);o.classList.remove(H.TimeMarkerWrapperInvisibleClassName);const a=t.timeMarker,l=t.timeIndicator,p=a.offsetHeight/2,c=r[0].offsetLeft;let d=0;if(t.startFromContainer){const e=this.findFirstCellInContainer(n,t.firstContainerIndex);e&&(d=e.offsetLeft===c?c-p:e.offsetLeft)}const u=Math.floor(this.calculateTop(s,r[0],i));if(a.style.top=u-p+"px",a.style.left=d+"px",a.style.width=c-d+p+1+"px",this.isElementDisplayed(o)){let t=0;for(let e,n=0;e=r[n];n++)t+=e.offsetWidth;l.style.top=u-1+"px",l.style.width=t+"px",l.style.left=c+"px"}}findCellsByTime(t,e,n,i){const s=[];for(let o,r=0;o=t[r];r++)k.Attr.getContainerIndex(o)<n||k.Attr.getContainerIndex(o)>i||e.valueOf()-k.Attr.getStart(o).valueOf()>=0&&k.Attr.getEnd(o).valueOf()-e.valueOf()>0&&s.push(o);return s}getVerticalViewPart(){return this.verticalView||(this.verticalView=new G(this,`.${H.VerticalViewClassName} .${H.TimeCellClassName}`)),this.verticalView}findFirstCellInContainer(t,e){for(let n,i=0;n=t[i];i++)if(k.Attr.getContainerIndex(n)===e)return n;return null}isElementDisplayed(t){return t&&"none"!==t.style.display}calculateTop(t,e,n){const i=this.getAdaptedContainerTime(n,t),s=k.Attr.getStart(e),o=k.Attr.getEnd(e),r=O.dateSubsWithTimezone(i,s);return e.offsetTop+e.offsetHeight*r/(o.valueOf()-s.valueOf())}getAdaptedContainerTime(t,e){const n=t,i=O.dateSubsWithTimezone(e,n);let s=Math.abs(i)%O.DaySpan;return i<0&&(s=O.DaySpan-s),O.dateIncreaseWithUtcOffset(n,s)}selectSameAppointments(t){const e=tt.from(t);null!==e.resource&&(super.selectSameAppointments(t),et.selectSameAppointments(e,this.verticalAppointments))}syncronizeScrollViewWithHeader(){const t=this.getScrollViewer();if(t){const e=t.querySelector(`.${f.ContentContainerClassName}`);if(e){const t=k.getTimescaleViewport(this),n=t?t.scrollLeft:-1;n>-1&&e.scrollTo&&e.scrollTo(n,0)}}}}class ot extends st{constructor(){super()}}ot.TagName="dxbl-day-view";class rt extends nt{constructor(){super()}}rt.TagName="dxbl-month-view";class at extends v{constructor(t,e,n=!1){super(t.getKeyboardNavigator(),e,n),this.scheduler=t}canSwitchToNestedContentMode(){return!0}getToolbarWrapper(){return k.getToolbarWrapper(this.scheduler)}getVerticalAppointmentsContainer(){const t=k.getVerticalAppointmentsContainer(this.scheduler);return(null==t?void 0:t.childElementCount)>0?t:null}getHorizontalAppointmentsContainer(){const t=k.getHorizontalAppointmentsContainer(this.scheduler);return(null==t?void 0:t.childElementCount)>0?t:null}handleTabKeyDown(t,e=!1){if(!e&&t.altKey)return!1;if(t.shiftKey){if(this.firstItemSelected)return!1;this.moveToPrevItem()}else{if(this.lastItemSelected)return!1;this.moveToNextItem()}return!0}}class lt extends at{constructor(t,e){super(t,e),this.itemSelector=`:scope > .${H.HorizontalAppointmentClassName}`}queryItems(){return this.queryItemsBySelector(this.itemSelector)}handleKeyDown(t){const e=I.KeyUtils.getEventKeyCode(t);if(e===I.KeyCode.Tab)return this.handleTabKeyDown(t);switch(e){case I.KeyCode.Enter:case I.KeyCode.Delete:case I.KeyCode.Key_n:case I.KeyCode.Key_r:return this.performShortcutEvent(t),!0}return!1}handleTabKeyDown(t){return!!super.handleTabKeyDown(t,t.altKey)}getShortcutContext(){var t;return{AppointmentKey:null===(t=this.selectedItemElement.getAttributeNode("data-key"))||void 0===t?void 0:t.value}}focusSelectedItem(){var t;super.focusSelectedItem(),null===(t=this.scheduler.getView())||void 0===t||t.syncronizeScrollViewWithHeader()}}class pt extends at{constructor(t,e,n){super(t,e,!0),this.rootStrategy=n}leaveTransitContainer(t){super.leaveTransitContainer(t),this.rootStrategy.moveFromToolbar(t)}handleKeyDown(t){return I.KeyUtils.getEventKeyCode(t)===I.KeyCode.Key_n&&(this.performShortcutEvent(t),!0)}}class ct extends at{constructor(t,e){super(t,e),this.itemSelector=`:scope > .${H.VerticalAppointmentClassName}`}queryItems(){return this.queryItemsBySelector(this.itemSelector)}handleKeyDown(t){const e=I.KeyUtils.getEventKeyCode(t);if(e===I.KeyCode.Tab)return this.handleTabKeyDown(t);switch(e){case I.KeyCode.Enter:case I.KeyCode.Delete:case I.KeyCode.Key_n:case I.KeyCode.Key_r:return this.performShortcutEvent(t),!0}return!1}handleTabKeyDown(t){return!!super.handleTabKeyDown(t,t.altKey)}getShortcutContext(){var t;return{AppointmentKey:null===(t=this.selectedItemElement.getAttributeNode("data-key"))||void 0===t?void 0:t.value}}}class dt extends at{constructor(t){super(t,t)}queryItems(){return new Array(this.getToolbarWrapper(),this.getHorizontalAppointmentsContainer(),this.getVerticalAppointmentsContainer())}createItemStrategy(t){if(t===this.getToolbarWrapper())return new pt(this.scheduler,t,this);if(t===this.getVerticalAppointmentsContainer())return new ct(this.scheduler,t);if(t===this.getHorizontalAppointmentsContainer())return new lt(this.scheduler,t);throw new Error("Not implemented")}handleKeyDown(t){return I.KeyUtils.getEventKeyCode(t)===I.KeyCode.Tab&&this.handleTabKeyDown(t)}handleTabKeyDown(t){return super.handleTabKeyDown(t,t.altKey)||(t.shiftKey?this.leaveBackward():this.leaveForward()),!0}moveFromToolbar(t){t===y.Backward?(this.leaveBackward(),this.navigator.leaveFromNavigator()):t===y.Forward&&(this.lastItemSelected?(this.leaveForward(),this.navigator.leaveFromNavigator()):this.moveToNextItem())}}class ut extends v{constructor(t,e){super(t.getKeyboardNavigator(),e,!0),this._dateNavigator=t}canSwitchToNestedContentMode(){return!0}leaveTransitContainer(t){const e=this._dateNavigator.getCalendar();if(e){const n=w.getTabbables(e,!1);if(!n.length||t===y.None)return;(t===y.Forward?n[0]:n[n.length-1]).focus()}}}class mt extends A{getKeyboardNavigator(){return this._keyboardNavigator}getCalendar(){return this.querySelector(S)}contentChanged(){super.contentChanged(),this.initializeKeyboardNavigator()}initializeKeyboardNavigator(){this._keyboardNavigator=this.querySelector(T),this._keyboardNavigator&&!this._keyboardNavigator.initialized&&this._keyboardNavigator.initialize(this,new ut(this,this))}}mt.TagName="dxbl-date-navigator";class ht extends v{constructor(t,e){super(t.getKeyboardNavigator(),e),this._resourceNavigator=t}queryItems(){return new Array(this._resourceNavigator.getSearchBox(),this._resourceNavigator.getSelectAllCheckBox(),this._resourceNavigator.getResourceListBoxContainer(),this._resourceNavigator.getOkButton(),this._resourceNavigator.getCancelButton())}createItemStrategy(t){return t.matches(this._resourceNavigator.getResourceListBoxContainerSelector())?new gt(this._resourceNavigator.getKeyboardNavigator(),t,this):null}handleKeyDown(t){return I.KeyUtils.getEventKeyCode(t)===I.KeyCode.Tab?(t.shiftKey?this.moveToPrevItem(!0):this.moveToNextItem(!0),!0):super.handleKeyDown(t)}canSwitchToNestedContentMode(){return!0}moveForward(){this.moveToNextItem(!0)}moveBackward(){super.moveToPrevItem(!0)}}class gt extends v{constructor(t,e,n){super(t,e,!0),this._rootStrategy=n}leaveTransitContainer(t){super.leaveTransitContainer(t),t===y.Forward?this._rootStrategy.moveForward():t===y.Backward&&this._rootStrategy.moveBackward()}canSwitchToNestedContentMode(){return!0}}class ft extends A{getKeyboardNavigator(){return this._keyboardNavigator}getSearchBox(){return this.querySelector(`.${H.ResourceNavigatorSearchBox} > input`)}getSelectAllCheckBox(){return this.querySelector(`.${H.ResourceNavigatorSelectAllContainer} > ${N} input`)}getResourceListBoxContainerSelector(){return`.${H.ResourceNavigatorListBoxContainer}`}getResourceListBoxContainer(){return this.querySelector(this.getResourceListBoxContainerSelector())}getOkButton(){return this.querySelector(`.${H.ResourceNavigatorFooter} button:first-child`)}getCancelButton(){return this.querySelector(`.${H.ResourceNavigatorFooter} button:last-child`)}contentChanged(){super.contentChanged(),this.initializeKeyboardNavigator()}initializeKeyboardNavigator(){this._keyboardNavigator=this.querySelector(T),this._keyboardNavigator&&!this._keyboardNavigator.initialized&&this._keyboardNavigator.initialize(this,new ht(this,this))}}ft.TagName="dxbl-resource-navigator";class At extends e{constructor(){super(),this.onClickEventHandler=this.onDelayedClickEvent.bind(this),this.onDoubleClickEventHandler=this.onDoubleClickEvent.bind(this),this.onDragStartEventHandler=this.onDragStartEvent.bind(this),this.onDragStopEventHandler=this.onDragStopEvent.bind(this),this.onDragCancelEventHandler=this.onDragCancelEvent.bind(this),this.unforcedFunctions={},this.appointmentDragController=new j(this),this.allowCreateAppointment=!1,this.allowEditAppointment=!1,this.allowDragAppointment=!1,this.allowResizeAppointment=!1,this.enableAppointmentTooltip=!1,this.activeFormType=_.None,this.clientUtcOffset=0,this.handlePointerEventsMode=a.Click|a.DblClick|a.Dragging}initializeComponent(){super.initializeComponent(),this.uiHandlersBridge=this.querySelector(n),this.addEventSubscriptions(),this.initializeKeyboardNavigator()}disposeComponent(){delete this.uiHandlersBridge,this.removeEventSubscriptions(),super.disposeComponent()}getView(){return this.querySelector(`.${H.View}`)}getKeyboardNavigator(){return this.keyboardNavigator}adjustControl(t,e){var n;e.isInitial()||null===(n=this.getView())||void 0===n||n.adjust()}addEventSubscriptions(){this.addEventListener(l.eventName,this.onClickEventHandler),this.addEventListener(p.eventName,this.onDoubleClickEventHandler),this.addEventListener(c.eventName,this.onDragStartEventHandler),this.addEventListener(d.eventName,this.onDragStopEventHandler),this.addEventListener(u.eventName,this.onDragCancelEventHandler),this.elementContentSizeSubscription=o(this,this.adjustControl.bind(this))}removeEventSubscriptions(){this.removeEventListener(l.eventName,this.onClickEventHandler),this.removeEventListener(p.eventName,this.onDoubleClickEventHandler),this.removeEventListener(c.eventName,this.onDragStartEventHandler),this.removeEventListener(d.eventName,this.onDragStopEventHandler),this.removeEventListener(u.eventName,this.onDragCancelEventHandler),this.elementContentSizeSubscription&&r(this.elementContentSizeSubscription)}initializeKeyboardNavigator(){this.keyboardNavigator=this.querySelector(`:scope > ${T}`),this.keyboardNavigator&&!this.keyboardNavigator.initialized&&this.keyboardNavigator.initialize(this,new dt(this))}onClickEvent(t){var e,n,i,s;const o=t.detail.source.target,r=k.getAppointmentContainer(o),a=null===(e=this.getView())||void 0===e?void 0:e.getEditableAppointmentKey(),l=r?k.Attr.getAppointmentKey(r):null;if(!a||r&&a===l)if(r)k.isTargetAppointment(r)||this.selectAppointment(r),this.enableAppointmentTooltip&&a!==l&&(null===(i=this.uiHandlersBridge)||void 0===i||i.send(F.ShowAppointmentToolTip,"")),t.stopPropagation();else if(this.activeFormType===_.None){const e=k.getTimeCellContainer(o);this.allowCreateAppointment&&e&&(this.createAppointmentByTimeCell(e),t.stopPropagation())}else null===(s=this.uiHandlersBridge)||void 0===s||s.send(F.HideAppointmentToolTip,{outsideClick:!1});else null===(n=this.uiHandlersBridge)||void 0===n||n.send(F.HideAppointmentToolTip,{outsideClick:!1})}onDelayedClickEvent(t){this.unforcedFunctionCall(this.onClickEvent.bind(this,t),"ClickEvent",200,!0)}onDoubleClickEvent(t){const e=k.getAppointmentContainer(t.detail.source.target),n=this.getView();e&&n&&n.getEditableAppointmentKey()!==k.Attr.getAppointmentKey(e)&&(k.isTargetAppointment(e)||this.selectAppointment(e),this.clearUnforcedFunctionByKey("ClickEvent"))}onDragStartEvent(t){var e,n;const i=t.detail.source.target;if(!i||(null===(e=this.getView())||void 0===e?void 0:e.getEditableAppointmentKey()))return;const s=null===(n=this.getView())||void 0===n?void 0:n.findCellByCoordinates(t.detail.source.clientX,t.detail.source.clientY);if(!s)return;const o=k.getAppointmentContainer(i);if(o){const e=this.getAppointmentInfo(k.Attr.getAppointmentKey(o));this.allowEditAppointment&&e&&(this.allowResizeAppointment&&(k.isTopHandleElement(i)||k.isLeftHandleElement(i))?this.appointmentDragController.dragAppointmentStart(e,s,t.detail.source):this.allowResizeAppointment&&(k.isBottomHandleElement(i)||k.isRightHandleElement(i))?this.appointmentDragController.dragAppointmentEnd(e,s,t.detail.source):this.allowDragAppointment&&this.appointmentDragController.dragAppointment(e,s,t.detail.source))}else this.allowCreateAppointment&&this.allowResizeAppointment&&this.appointmentDragController.dragNewAppointment(s);t.stopPropagation()}onDragStopEvent(t){this.appointmentDragController.dropAppointment(),t.stopPropagation()}onDragCancelEvent(t){this.appointmentDragController.cancelDragAppointment(),t.stopPropagation()}selectAppointment(e){var n,i;if(!e)return;t.DomUtils.addClassName(e,H.TargetAppointmentClassName),t.DomUtils.addClassName(e,H.SelectedAppointmentClassName);const s=k.Attr.getAppointmentKey(e);null===(n=this.getView())||void 0===n||n.selectSameAppointments(s),null===(i=this.uiHandlersBridge)||void 0===i||i.send(F.SelectAppointment,{key:k.Attr.getAppointmentKey(e)})}createAppointmentByTimeCell(t){var e;const n=k.Attr.getStart(t),i=k.Attr.getEnd(t),s=this.getUtcOffset(n,k.Attr.getUtcOffset(t)),o=this.getUtcOffset(i,k.Attr.getUtcOffset(t));null===(e=this.uiHandlersBridge)||void 0===e||e.send(F.CreateAppointment,{appointmentInfo:{start:O.addTimeSpan(n,s),end:O.addTimeSpan(i,o),isAllDay:k.Attr.getAllDay(t),resourceKey:k.Attr.getResourceKey(t)}})}getSelectedAppointmentInfo(){const t=k.getSelectedAppointment(this);return t?this.getAppointmentInfo(k.Attr.getAppointmentKey(t)):null}getAppointmentInfo(t){const e=this.getView();return e?e.appointmentInfos.filter((e=>e.id===t))[0]:null}getEditableAppointments(){return k.getEditableAppointments(this)}getUtcOffset(t,e){return O.getUtcTimezoneOffset(t)+-1*e}static get observedAttributes(){return["allow-create-appointment","allow-edit-appointment","allow-drag-appointment","allow-resize-appointment","enable-appointment-tooltip","active-form-type","client-utc-offset"]}attributeChangedCallback(t,e,n){switch(t){case"allow-create-appointment":this.allowCreateAppointment=null!==n;break;case"allow-edit-appointment":this.allowEditAppointment=null!==n;break;case"allow-drag-appointment":this.allowDragAppointment=null!==n;break;case"allow-resize-appointment":this.allowResizeAppointment=null!==n;break;case"enable-appointment-tooltip":this.enableAppointmentTooltip=null!==n;break;case"active-form-type":this.activeFormType=parseInt(n);break;case"client-utc-offset":this.clientUtcOffset=parseInt(n)}}unforcedFunctionCall(t,e,n,i){i&&this.hasUnforcedFunction(e)&&this.clearUnforcedFunctionByKey(e),void 0===this.unforcedFunctions[e]&&(this.unforcedFunctions[e]=setTimeout((()=>{t(),delete this.unforcedFunctions[e]}),n))}hasUnforcedFunction(t){return!!this.unforcedFunctions[t]}clearUnforcedFunctionByKey(t){clearTimeout(this.unforcedFunctions[t]),delete this.unforcedFunctions[t]}}At.TagName="dxbl-scheduler",customElements.define(At.TagName,At),customElements.define(q.TagName,q),customElements.define(ot.TagName,ot),customElements.define(rt.TagName,rt),customElements.define(it.TagName,it),customElements.define(mt.TagName,mt),customElements.define(ft.TagName,ft);const Ct={loadModule:function(){}};export{At as DxScheduler,Ct as default};
