import{k as t}from"./key-ffa272aa.js";import{t as e}from"./touch-6a322081.js";import{F as n}from"./constants-7c047c0d.js";import{d as o,r as s}from"./disposable-d2c2d283.js";import{L as r}from"./logicaltreehelper-67db40f1.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";const u="data-dx-focus-hidden";function i(t){t.setAttribute(u,"")}function c(t){t.removeAttribute(u)}function a(t){return null!==t.getAttribute(u)}function m(n){let o=n;function s(t){!("mousedown"===t.type&&0===t.detail)&&o&&r.containsElement(o,t.target)&&i(o)}function u(t){if(!o)return;const e=t.relatedTarget;e&&!r.containsElement(o,e)&&document.hasFocus()&&c(o)}function a(e){o&&t.KeyUtils.getEventKeyCode(e)===t.KeyCode.Tab&&c(o)}const m=document.documentElement;return m.addEventListener(e.TouchUtils.touchMouseDownEventName,s),o.addEventListener("keydown",a),o.addEventListener("focusout",u),()=>{m.removeEventListener(e.TouchUtils.touchMouseDownEventName,s),null==o||o.removeEventListener("keydown",a),null==o||o.removeEventListener("focusout",u),o=null}}function d(t){if(!t)return;o(t);const e=m(t);s(t,e)}function l(t){return null!==t&&t.matches(n)}function f(t){return null!==t&&null!==t.closest(n)}const p={initFocusHidingEvents:d};export{u as FocusHiddenAttributeName,i as addFocusHiddenAttribute,m as attachEventsForFocusHiding,a as containsFocusHiddenAttribute,p as default,f as hasClosestFocusableElement,d as initFocusHidingEvents,l as isFocusableElement,c as removeFocusHiddenAttribute};
