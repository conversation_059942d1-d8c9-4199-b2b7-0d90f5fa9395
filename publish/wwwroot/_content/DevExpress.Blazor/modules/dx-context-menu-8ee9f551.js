import{_ as e}from"./tslib.es6-d65164b3.js";import{F as t,K as s,a as i,D as a}from"./keyboard-navigation-strategy-ea41c807.js";import{S as o}from"./single-slot-element-base-01d93921.js";import{k as r}from"./key-ffa272aa.js";import{b as n,M as d,P as l,K as c,d as m,e as p,c as h}from"./menu-keyboard-strategy-7c257fd3.js";import{r as u}from"./popup-355ecaa4.js";import{e as b}from"./custom-element-267f9a21.js";import"./focushelper-2eea96ca.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./devices-17b9ba08.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./property-4ec0b52d.js";import"./constants-ed56e953.js";import"./constants-3209ffde.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./portal-b3727c25.js";import"./constants-a4904a3f.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./capture-manager-2454adc2.js";import"./nameof-factory-64d95f5b.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";class y extends l{constructor(e,t,s){super(e,t),this._parentStrategy=s,this.selectedItemIndex=-1}get parentStrategy(){return this._parentStrategy}initialize(){super.initialize(),this.targetElement.setAttribute(c,"")}activate(){this.selectedItemIndex>=0&&super.activate()}updateSelectedItemByIndex(e){e>=0&&super.updateSelectedItemByIndex(e)}queryItems(){return this.queryItemsBySelector(".dxbl-context-menu-item, .dxbl-context-menu-template")}onActiveStateChanged(e){e||(this.selectedItemElement&&t.removeTabIndex(this.selectedItemElement),t.removeTabIndex(this.targetElement))}doAction(e){this.selectedItemElement&&(this.doClick(this.selectedItemElement,e),n(this.selectedItemElement)&&this.openSubMenu(d.First))}async openSubMenu(e){this.requestSubMenuOpen();const t=await this.waitSubMenuShown();e===d.Last?t.focusLastItem():t.focusFirstItem()}handlePopupShown(){this.parentStrategy.onSubMenuShown(this),this.focusContainer()}handlePopupClosed(){this.parentStrategy.onSubMenuClosed(this)}onSubMenuClosed(e){super.onSubMenuClosed(e),this.focusContainer()}focusContainer(){const e=t.findFocusableElements(this.targetElement);e.length>0&&t.focusElement(e[0])}createSubMenuStrategy(e,t){return new v(e,t,this)}}class g extends y{closeSelf(){return!!n(this.selectedItemElement)&&(this.openSubMenu(d.Last),!0)}}class v extends y{closeSelf(){const e=this.targetElement.placementTargetElement;return!!e&&(e.dispatchEvent(new m(p.Collapse)),this.parentStrategy.updateSelectedItemByChildElement(e),this.parentStrategy.focusSelectedItem(),!0)}}class f extends s{constructor(e,t){super(e,t),this._prevActiveElement=null,this.boundHandlePopupKeyboardStrategyCreating=this.handlePopupKeyboardStrategyCreating.bind(this),this.targetElement.addEventListener(u.eventName,this.boundHandlePopupKeyboardStrategyCreating)}queryItems(){return this.queryItemsBySelector(".dxbl-dropdown-dialog")}onSubMenuShown(e){this._prevActiveElement||(this._prevActiveElement=document.activeElement)}onSubMenuClosed(e){e.targetElement.isConnected&&!h(e.targetElement)||this.tryRestoreFocusedElement()}handlePopupKeyboardStrategyCreating(e){e.detail.factory={createPopup:(e,t)=>new g(e,t,this)},e.stopPropagation()}handleKeyDown(e){return r.KeyUtils.getEventKeyCode(e)!==r.KeyCode.Tab||this.nestedContentSelected?super.handleKeyDown(e):(this.performShortcutEvent(e),e.shiftKey?this.leaveBackward():this.leaveForward(),!0)}tryRestoreFocusedElement(){this._prevActiveElement&&this._prevActiveElement instanceof HTMLElement&&(this._prevActiveElement.focus(),this._prevActiveElement=null)}}let j=class extends o{constructor(){super(),this._keyboardNavigator=null,this.bindedOnChanged=this.onChanged.bind(this),this._observer=new MutationObserver(this.bindedOnChanged)}onChanged(e,t){null==e||e.forEach((e=>{e.addedNodes.forEach((e=>{e instanceof i&&(this._keyboardNavigator=e,this.initializeKeyboardNavigation())}))}))}connectedCallback(){this._keyboardNavigator=this.querySelector(a),this._keyboardNavigator?this.initializeKeyboardNavigation():this._observer.observe(this,{childList:!0})}initializeKeyboardNavigation(){this._keyboardNavigator&&"initialized"in this._keyboardNavigator&&!this._keyboardNavigator.initialized&&this._keyboardNavigator.initialize(this,new f(this._keyboardNavigator,this))}disconnectedCallback(){var e;this._observer.disconnect(),null===(e=this._keyboardNavigator)||void 0===e||e.disposeComponent()}};j=e([b("dxbl-context-menu")],j);const S={loadModule:function(){}};export{j as DxContextMenu,S as default};
