import{a as e,R as t,L as s,D as i}from"./layouthelper-67dd777a.js";import{R as r}from"./rafaction-bba7928b.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";class n extends CustomEvent{constructor(e){super(n.eventName,{detail:e,bubbles:!0,cancelable:!0})}}n.eventName="dxbl-position-changing";class o extends CustomEvent{constructor(e){super(o.eventName,{detail:e,bubbles:!0,cancelable:!0})}}o.eventName="dxbl-position-changed";class a extends HTMLElement{constructor(){super(...arguments),this.observer=null,this.targetField="",this.listenedIdField="",this.elementScrollHandler=this.handleElementScroll.bind(this),this.resizeHandler=this.handleResize.bind(this),this.mutationObserver=new MutationObserver(this.handleMutations.bind(this)),this.overflows=[],this.resizing=[],this.targetElement=null,this.rafAction=new r}get target(){return this.targetField}set target(e){this.targetField=e}get listenerId(){return this.listenedIdField}set listenerId(e){this.listenedIdField=e}connectedCallback(){this.initializeObserver(),this.subscribeToOverflows()}getTargetBoundingClientRect(){return this.targetElement?t.fromDomRect(this.targetElement.getBoundingClientRect()):e.Empty}initializeObserver(){this.destroyObserver();const e=this.querySelector(this.target);if(!e)return void this.mutationObserver.observe(this,{childList:!0,subtree:!0});this.observer=new IntersectionObserver((function(e,t){}),{root:e,rootMargin:"0px",threshold:0}),this.observer.observe(this),this.targetElement=e}disconnectedCallback(){this.destroyObserver()}destroyObserver(){var e;this.mutationObserver.disconnect(),null===(e=this.observer)||void 0===e||e.disconnect(),this.observer=null}static get observedAttributes(){return["target","listener-id"]}attributeChangedCallback(e,t,s){switch(e){case"target":this.targetChanged(s);break;case"listener-id":this.listenerIdChanged(s)}}handleMutations(e){const t=this.querySelector(this.target);t&&(this.targetElement=t,this.mutationObserver.disconnect(),this.initializeObserver(),this.subscribeToOverflows())}handleResize(e,t){this.raisePositionChanging(),this.rafAction.execute((()=>this.raisePositionChanged()))}targetChanged(e){this.target=e,this.initializeObserver()}listenerIdChanged(e){this.listenerId=e}raisePositionChanging(){const e=new n({Tracker:this});this.dispatchEvent(e)}raisePositionChanged(){const e=new o({Tracker:this});this.dispatchEvent(e)}subscribeToOverflows(){if(!this.targetElement)return;const e=[],t=[];for(const r of s.getRootPath(this.targetElement)){const s=r;if(!s)return;new ResizeObserver(this.resizeHandler).observe(s,{box:"border-box"}),t.push(),i.isScrollable(s)&&(s.addEventListener("scroll",this.elementScrollHandler,{capture:!0}),e.push(s))}this.overflows=e,this.resizing=t}unsubscribeFromOverflows(){this.overflows.forEach((e=>{e.removeEventListener("scroll",this.elementScrollHandler)})),this.resizing.forEach((e=>{e.disconnect()}))}handleElementScroll(e){this.raisePositionChanging(),this.rafAction.execute((()=>this.raisePositionChanged()))}}function l(){}customElements.define("dxbl-position-tracker",a);const h={init:l,PositionTracker:a};export{o as PositionChangedEvent,n as PositionChangingEvent,a as PositionTracker,h as default,l as init};
