class e{constructor(e){this.eventTokens=[],this.context=e}attachEventWithContext(e,t,n,s){if(!this.eventTokens||!s||!e)return{item:null,name:null,delegate:null,dispose:()=>{}};const i=n.bind(s);e.addEventListener(t,i);const o={item:e,name:t,delegate:i,dispose:()=>{this.detachEvent(o)}};return this.eventTokens.push(o),o}attachEvent(e,t,n){return this.attachEventWithContext(e,t,n,this.context)}detachEvent(e){if(!this.eventTokens)return null;if(!(e&&e.item&&e.name&&e.delegate))return!1;const t=this.eventTokens.indexOf(e);return t>=0&&(e.item.removeEventListener(e.name,e.delegate),this.eventTokens.splice(t,1),e.item=null,e.delegate=null,e.dispose=()=>{},!0)}dispose(){return!!this.eventTokens&&(this.eventTokens.forEach((function(e){e.item&&e.name&&e.delegate&&(e.item.removeEventListener(e.name,e.delegate),e.item=null,e.delegate=null,e.dispose=()=>{})})),this.eventTokens=[],!0)}}export{e as E};
