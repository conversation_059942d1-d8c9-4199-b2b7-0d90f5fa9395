const i=[],e={width:0,height:0};function n(e){return function(e){if(i.indexOf(e)>-1)throw new Error("already subscribed");i.push(e)}((i=>{o(e,i.width)})),o(e,window.innerWidth),Promise.resolve("ok")}function o(i,e){i.invokeMethodAsync("OnWindowResize",e).catch((i=>console.error(i)))}function t(){return Promise.resolve("ok")}window.addEventListener("resize",(n=>{const o=window.innerWidth,t=window.innerHeight;e.height===t&&e.width===o||(e.height=t,e.width=o,i.forEach((i=>i(e))))}),{passive:!0});const r={init:n,dispose:t};export{r as default,t as dispose,n as init};
