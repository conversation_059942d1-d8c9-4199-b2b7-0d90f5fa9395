import{P as t,a as e}from"./point-e4ec110e.js";import{D as i}from"./data-qa-utils-8be7c726.js";import{E as r}from"./eventhelper-8bcec49f.js";import{b as n}from"./browser-3fc721b7.js";import"./_commonjsHelpers-41cdd1e7.js";const s="dxbl-thumb";class a{get x(){return this._x}get y(){return this._y}get source(){return this._source}constructor(t,e,i){this._source=i,this._y=e,this._x=t}}class o extends a{get deltaX(){return this.deltaXField}get deltaY(){return this.deltaYField}constructor(t,e,i,r,n){super(i,r,n),this.deltaXField=t,this.deltaYField=e}}class d extends CustomEvent{constructor(t,e){super(t,{detail:e,bubbles:!0,composed:!0,cancelable:!0})}}class h extends d{constructor(t){super(h.eventName,t)}}h.eventName="dxthumbdragstarted";class p extends d{constructor(t){super(p.eventName,t)}}p.eventName="dxthumbdragdelta";class l extends d{constructor(t){super(l.eventName,t)}}l.eventName="dxthumbdragcompleted";class u{constructor(t){this.outsideClickHandler=this.handleOutsideClick.bind(this),this.thumbElement=t}preventOutsideClick(t){n.Browser.Firefox&&0===t.button&&document.addEventListener("click",this.outsideClickHandler,{capture:!0,once:!0})}cancelOutsideClickPrevention(){document.removeEventListener("click",this.outsideClickHandler,{capture:!0})}handleOutsideClick(t){document.elementFromPoint(t.clientX,t.clientY)!==this.thumbElement&&r.markHandled(t)}}class c extends HTMLElement{constructor(){super(...arguments),this.pointerDownHandler=this.handlePointerDown.bind(this),this.pointerUpHandler=this.handlePointerUp.bind(this),this.pointerLostCaptureHandler=this.lostPointerCapture.bind(this),this.pointerGotCaptureHandler=this.pointerGotCapture.bind(this),this.pointerMoveHandler=this.pointerMove.bind(this),this.pointerCancelHandler=this.pointerCancel.bind(this),this.touchStartHandler=this.touchStart.bind(this),this.visibilityChangeHandler=this.handleVisibilityChange.bind(this),this._isDragging=!1,this._isCaptured=!1,this.capturedPointer=null,this.previousPoint=new t(0,0),this.firefoxOutsideClickHandler=new u(this)}connectedCallback(){i.setLoaded(this),this.addEventListener("pointerdown",this.pointerDownHandler,{capture:!0}),this.addEventListener("pointerup",this.pointerUpHandler,{capture:!0}),this.addEventListener("lostpointercapture",this.pointerLostCaptureHandler,{capture:!0}),this.addEventListener("gotpointercapture",this.pointerGotCaptureHandler,{capture:!0}),this.addEventListener("pointermove",this.pointerMoveHandler,{capture:!0}),this.addEventListener("pointercancel",this.pointerCancelHandler,{capture:!0}),this.addEventListener("touchstart",this.touchStartHandler,{capture:!0,passive:!1}),document.addEventListener("visibilitychange",this.visibilityChangeHandler)}disconnectedCallback(){i.removeLoaded(this),this.removeEventListener("pointerdown",this.pointerDownHandler),this.removeEventListener("pointerup",this.pointerUpHandler),this.removeEventListener("lostpointercapture",this.pointerLostCaptureHandler),this.removeEventListener("gotpointercapture",this.pointerGotCaptureHandler),this.removeEventListener("pointermove",this.pointerMoveHandler),this.removeEventListener("pointercancel",this.pointerCancelHandler),this.removeEventListener("touchstart",this.touchStartHandler),document.removeEventListener("visibilitychange",this.visibilityChangeHandler),this.firefoxOutsideClickHandler.cancelOutsideClickPrevention()}get isDragging(){return this._isDragging}get isCaptured(){return this._isCaptured}cancelDrag(e=null){if(this.isDragging){this._isDragging=!1,e?this.releaseCapture(e.pointerId):this.capturedPointer&&this.releaseCapture(this.capturedPointer),i.removeAttribute(this,c.dragging);const r=e?new t(e.pageX,e.pageY):this.previousPoint,n=new l(new a(r.x,r.y,e));this.dispatchEvent(n)}}markHandled(t){t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation()}handleVisibilityChange(t){this.isDragging&&"visible"!==document.visibilityState&&this.cancelDrag()}touchStart(t){this.isDragging&&t.cancelable&&t.preventDefault()}pointerGotCapture(t){if(t.target===this)return;if(!this.isDragging)return;const e=t.target;null==e||e.releasePointerCapture(t.pointerId),this._isCaptured=!1,this.pointerCapture(t)}pointerCancel(t){t.target===this&&(this.hasPointerCapture(t.pointerId)||this.cancelDrag(t))}pointerCapture(t){this._isCaptured||(this.setPointerCapture(t.pointerId),this._isCaptured=!0,this.capturedPointer=t.pointerId)}releaseCapture(t){this._isCaptured&&(this.releasePointerCapture(t),this._isCaptured=!1,this.capturedPointer=null)}handlePointerDown(e){if(this.isDragging)return;const n=e.target;(null==n?void 0:n.hasPointerCapture(e.pointerId))&&n.releasePointerCapture(e.pointerId),i.setAttribute(this,c.dragging),r.markHandled(e),this.focus(),this.pointerCapture(e),this._isDragging=!0,this.previousPoint=new t(e.pageX,e.pageY);const s=new h(new a(e.pageX,e.pageY,e));this.dispatchEvent(s),this.firefoxOutsideClickHandler.preventOutsideClick(e)}handlePointerUp(t){this.isDragging&&this.isCaptured&&(r.markHandled(t),this.cancelDrag(t))}lostPointerCapture(t){t.target===this&&(this.hasPointerCapture(t.pointerId)||this.cancelDrag(t))}pointerMove(t){this.isDragging&&t.isPrimary&&this.processMove(t)}processMove(i){const n=new t(i.pageX,i.pageY);if(!e.areClose(n,this.previousPoint)){r.markHandled(i);const t=new p(new o(n.x-this.previousPoint.x,n.y-this.previousPoint.y,i.pageX,i.pageY,i));this.dispatchEvent(t),this.previousPoint=n}}}function g(){}c.dragging="dragging",customElements.define(s,c);const v={init:g,dxThumbTagName:s,ThumbDragStartedEvent:h,ThumbDragDeltaEvent:p,ThumbDragCompletedEvent:l,DragContext:a};export{a as DragContext,o as DragDeltaContext,c as DxThumb,l as ThumbDragCompletedEvent,p as ThumbDragDeltaEvent,d as ThumbDragEvent,h as ThumbDragStartedEvent,v as default,s as dxThumbTagName,g as init};
