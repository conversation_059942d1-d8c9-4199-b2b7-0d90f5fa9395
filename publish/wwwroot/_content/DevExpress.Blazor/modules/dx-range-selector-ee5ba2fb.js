import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t}from"./devextreme-widget-wrapper-33881f73.js";import{C as r}from"./custom-events-helper-e7f279d3.js";import{C as s}from"./events-a8fe5872.js";import{c as n}from"./create-after-timeout-fn-executor-38b3d79d.js";import{R as o,C as a}from"./settings-c0192d16.js";import{C as i}from"./client-component-style-helper-195fa7c3.js";import{e as l}from"./custom-element-267f9a21.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./utils-b5b2c8a9.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";const c="dxbl-range-selector";class p extends s{constructor(){super(p.eventName)}}p.eventName=c+".drawn";class d extends CustomEvent{constructor(e,t){super(d.eventName,{detail:new u(e,t),bubbles:!0,composed:!0,cancelable:!0})}}d.eventName=c+".value-changed";class m extends s{constructor(){super(m.eventName)}}m.eventName=c+".exported";class u{constructor(e,t){this.Value=e,this.PreviousValue=t}}r.register(p.eventName,(e=>e.detail)),r.register(d.eventName,(e=>e.detail)),r.register(m.eventName,(e=>e.detail));const g="dxbl-range-selector",h="title",b="color",v="scale",f="sliderMarker",j=`font.${b}`,C=`${h}.${j}`,S=`${h}.subtitle.${j}`,$=`${v}.tick.${b}`,x=`${v}.minorTick.${b}`,w=`${f}.${b}`,O=`${f}.${j}`,E=`sliderHandle.${b}`,k=`shutter.${b}`,y=`${v}.label.${j}`,V=`--${g}`,D=`${V}-scale-tick-color`,N=`${V}-title-font-color`,R=`${V}-selected-range-color`,_=`${V}-slider-marker-color`,T=`${V}-slider-marker-font-color`,H=`${V}-slider-handle-color`,M=`${V}-shutter-color`,W=`${V}-container-background-color`,B=`${V}-scale-label-font-color`,I="exportTo",P="print",L="svg";let q=class extends t{get styleHelper(){return this._styleHelper}constructor(){super(),this._styleHelper=i.getInstance(),this._drawnExecutor=n(this.onDrawn.bind(this)),this._widgetSettingsConverter=this.createWidgetSettingsConverter()}createWidgetHandlers(){return{...super.createWidgetHandlers(),onDrawn:()=>this.onDrawn(),onOptionChanged:()=>this.onOptionChanged(),onValueChanged:e=>this.onValueChanged(e),onExported:()=>this.onExported()}}getWidgetTypeName(){return"dxRangeSelector"}createWidgetSettingsConverter(){return new o}createInitOptions(){const e=super.createInitOptions(),t=this._widgetSettingsConverter.createDefaultSettings();return e.dataPrepareSettings=t.dataPrepareSettings,e.scale={label:{font:{opacity:a.defaultAxisLabelFontOpacity}}},e.chart={commonSeriesSettings:t.commonSeriesSettings,palette:this.styleHelper.palette},e}onDrawn(){this._drawnExecutor.reset(),this.changeLoadingPanelVisibility(!1),this.dispatchEvent(new p)}onlyContainerSizeChanged(){this.onDrawn()}onOptionChanged(){this._drawnExecutor.execute()}onValueChanged(e){this.dispatchEvent(new d(e.value,e.previousValue))}onExported(){this.dispatchEvent(new m)}createWidgetDefaultOptions(){return{height:"100%",width:"100%"}}processSpecialOptions(e){super.processSpecialOptions(e),this.prepareValueOptions(e),this.prepareImageOptions(e),this.prepareBehaviorOptions(e),e.chart&&this._widgetSettingsConverter.processSpecialChartSettings(e,e.chart)}prepareOptions(e,t,r){e[t]=e[t]||{};for(const[s,n]of Object.entries(r))s in e&&(e[t][n]=e[s],delete e[s])}prepareValueOptions(e){this.prepareOptions(e,"value",{selectedRangeStartValue:"startValue",selectedRangeEndValue:"endValue",selectedRangeLength:"length"})}prepareImageOptions(e){"background"in e&&this.prepareOptions(e.background,"image",{imagePosition:"location",imageUrl:"url"})}prepareBehaviorOptions(e){this.prepareOptions(e,"behavior",{allowSlidersSwap:"allowSlidersSwap",animationEnabled:"animationEnabled",manualRangeSelectionEnabled:"manualRangeSelectionEnabled",moveSelectedRangeByClick:"moveSelectedRangeByClick",snapToTicks:"snapToTicks",valueChangeMode:"valueChangeMode"})}[I](...e){return this.executeClientMethod(I,...e)}[P](...e){return this.executeClientMethod(P,...e)}[L](...e){return this.executeClientMethod(L,...e)}getThemeDependentOptionsDict(){const e={[C]:N,[S]:N,[$]:D,[x]:D,selectedRangeColor:R,[w]:_,[O]:T,[E]:H,[k]:M,containerBackgroundColor:W,[y]:B};return{...super.getThemeDependentOptionsDict(),...e}}};q=e([l("dxbl-range-selector")],q);export{q as DxRangeSelector,g as RangeSelector};
