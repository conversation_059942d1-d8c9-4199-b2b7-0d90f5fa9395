import{_ as e}from"./tslib.es6-d65164b3.js";import{S as t}from"./single-slot-element-base-01d93921.js";import{K as s,D as r}from"./keyboard-navigation-strategy-ea41c807.js";import{k as o}from"./key-ffa272aa.js";import{b as i}from"./constants-7c047c0d.js";import{N as a,a as n}from"./events-70d90aad.js";import{e as c}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./property-4ec0b52d.js";import"./focushelper-2eea96ca.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./devices-17b9ba08.js";var d;class m{}d=m,m.prefix="dxbl",m.root=`${d.prefix}-accordion`,m.group=`${d.root}-group`,m.header=`${d.group}-header`,m.body=`${d.group}-body`,m.item=`${d.root}-item`,m.itemContent=`${d.item}-content`,m.itemsContainer=`${d.root}-items-container`,m.itemTemplate=`${d.root}-tmpl`;class l extends s{get selector(){return this.isTemplatedItem(this.targetElement)?`:scope.${m.itemTemplate}`:`:scope.${m.itemContent}`}constructor(e,t){super(e.getKeyBoardNavigator(),t),this._accordion=e}canSwitchToNestedContentMode(){return!0}isTemplatedItem(e){return e.classList.contains(m.itemTemplate)}queryItems(){return this.queryItemsBySelector(this.selector)}isExpanded(){return"true"===this.selectedItemElement.getAttribute(i.ariaExpanded)}handleKeyDown(e){return!!super.handleKeyDown(e)||!this.nestedContentSelected&&this.handleKeyDownCore(e)}handleKeyDownCore(e){switch(o.KeyUtils.getEventKeyCode(e)){case o.KeyCode.Enter:return this.raiseClickEvent(this.targetElement,e.ctrlKey,e.metaKey,e.shiftKey),!0;case o.KeyCode.Left:return!!this.isExpanded()&&(this.performAction(a.Collapse),!0);case o.KeyCode.Right:return!this.isExpanded()&&(this.performAction(a.Expand),!0);default:return!1}}performAction(e){this.targetElement.dispatchEvent(new n(e))}}class h extends l{get selector(){return`:scope.${m.header}`}}class u extends s{get activateFirstItem(){return this._activateFirstItem}set activateFirstItem(e){this._activateFirstItem=e}constructor(e,t){super(e.getKeyBoardNavigator(),t),this._activateFirstItem=!0,this._accordion=e}activate(){this.focusedFromOutsideBody()&&(this.selectedItemIndex=this.activateFirstItem?0:this.itemCount-1),super.activate()}canSwitchToNestedContentMode(){return!0}focusedFromOutsideBody(){return!this.targetElement.contains(document.activeElement)}isTemplatedBody(){return null==this.targetElement.querySelector(`:scope .${m.item}`)}isItem(e){return e.classList.contains(m.itemContent)}queryItems(){return this.queryItemsBySelector(this.getBodyElementsSelector())}getBodyElementsSelector(){return this.isTemplatedBody()?`:scope.${m.body}`:`:scope .${m.itemContent}`}createItemStrategy(e){return this.isItem(e)?new l(this._accordion,e):null}handleKeyDown(e){const t=super.handleKeyDown(e);return t||this.nestedContentSelected?t:this.handleKeyDownCore(e)}handleKeyDownCore(e){switch(o.KeyUtils.getEventKeyCode(e)){case o.KeyCode.Down:return!!this.canProcessUpDownKey()&&(this.moveToNextItem(!0),!0);case o.KeyCode.Up:return!!this.canProcessUpDownKey()&&(this.moveToPrevItem(!0),!0);default:return!1}}canProcessUpDownKey(){return this.isItem(document.activeElement)}}const p=`:scope .${m.header},\n:scope .${m.body}[expanded-state=True], :scope .${m.itemTemplate}`;class y extends s{constructor(e){super(e.getKeyBoardNavigator(),e),this._accordion=e}isHeader(e){return e.classList.contains(m.header)}isBody(e){return e.classList.contains(m.body)}queryItems(){return this.queryItemsBySelector(p)}createItemStrategy(e){return this.isHeader(e)?new h(this._accordion,e):this.isBody(e)?new u(this._accordion,e):new l(this._accordion,e)}handleKeyDown(e){return super.handleKeyDown(e),!this.nestedContentSelected&&this.handleKeyDownCore(e)}activate(){var e,t;(null===(e=this.getSelectedItemStrategy())||void 0===e?void 0:e.targetElement.contains(document.activeElement))?null===(t=this.getSelectedItemStrategy())||void 0===t||t.activate():super.activate()}handleKeyDownCore(e){var t;switch(o.KeyUtils.getEventKeyCode(e)){case o.KeyCode.Enter:return this.isBody(this.selectedItemElement)&&(null===(t=this.getSelectedItemStrategy())||void 0===t||t.activate()),!0;case o.KeyCode.Esc:return document.activeElement!==this.selectedItemElement&&this.selectItem(this.selectedItemIndex),!0;case o.KeyCode.Down:return this.moveToNextItem(!1),!0;case o.KeyCode.Up:return this.moveToPrevItem(!1),!0;case o.KeyCode.Tab:return e.shiftKey?this.leaveBackward():this.leaveForward(),!0}return!1}canFocusSelectedItem(){return super.canFocusSelectedItem()||this.isBody(this.selectedItemElement)}updateSelectedItemByIndex(e){const t=this.selectedItemIndex;super.updateSelectedItemByIndex(e),this.isBody(this.selectedItemElement)&&(this.getSelectedItemStrategy().activateFirstItem=t<this.selectedItemIndex)}}class v extends t{constructor(){super(...arguments),this._keyboardNavigator=null}connectedOrContentChanged(){this._keyboardNavigator=this.querySelector(`:scope > ${r}`),this._keyboardNavigator&&this.initializeKeyboardNavigator(),super.connectedOrContentChanged()}initializeKeyboardNavigator(){this._keyboardNavigator&&"initialized"in this._keyboardNavigator&&!this._keyboardNavigator.initialized&&this._keyboardNavigator.initialize(this,new y(this))}disconnectedCallback(){var e;null===(e=this._keyboardNavigator)||void 0===e||e.disposeComponent()}getKeyBoardNavigator(){return this._keyboardNavigator}}const g="dxbl-accordion";let I=class extends v{};I=e([c(g)],I);export{g as AccordionTagName,I as DxAccordion};
