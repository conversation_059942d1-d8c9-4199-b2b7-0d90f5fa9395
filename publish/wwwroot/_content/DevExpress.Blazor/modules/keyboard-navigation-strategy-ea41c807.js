import{k as e}from"./key-ffa272aa.js";import{D as t,F as n}from"./focushelper-2eea96ca.js";import{L as s,D as i}from"./layouthelper-67dd777a.js";import{F as r}from"./constants-7c047c0d.js";import{s as o}from"./dom-utils-d057dcaa.js";import{C as a}from"./custom-events-helper-e7f279d3.js";import{E as l}from"./eventhelper-8bcec49f.js";import{hasClosestFocusableElement as c,attachEventsForFocusHiding as d,addFocusHiddenAttribute as h,removeFocusHiddenAttribute as u}from"./focus-utils-ae044224.js";import{g as m}from"./devices-17b9ba08.js";class g{static isFocusedElement(e){return e===document.activeElement}static scheduleResetTabIndex(e){g.resetTabIndexAction.cancel(),g.elementsToResetTabIndex.push(e),g.resetTabIndexAction.execute((()=>{g.elementsToResetTabIndex.forEach((e=>g.removeTabIndex(e))),g.elementsToResetTabIndex.splice(0)}))}static focusElement(e,t=!1){e&&e.focus({preventScroll:t})}static makeElementFocusable(e){e.tabIndex<0&&!e.hasAttribute("tabIndex")&&o(e,"tabIndex","0")}static removeTabIndex(e){e.tabIndex>=0&&o(e,"tabIndex")}static findPrevFocusableElement(e){const t=g.findFocusableElements(document),n=t.findIndex((t=>t===e))-1;return n>=0?t[n]:null}static findNextFocusableNotChildElement(e){const t=g.findFocusableElements(document),n=g.findFocusableElements(e),s=t.findIndex((t=>t===e))+n.length+1;return s<t.length?t[s]:null}static findFocusableElementInRootPath(e){var t;return null!==(t=[...s.getRootPathAndSelf(e)].find((e=>e.tabIndex>-1||this.isRootWidget(e))))&&void 0!==t?t:null}static isRootWidget(e){return"string"==typeof e.className&&e.className.includes("dxbl-kbn-root-widget")}static findFocusableElements(e){return Array.from(e.querySelectorAll(r)).filter(((e,t,n)=>g.isElementFocusable(e,n)))}static isElementFocusable(e,t){if(e.tabIndex>-1&&g.isElementVisible(e)&&!i.isHidden(e)){const n=e;return"radio"!==(null==n?void 0:n.type)||g.isRadioButtonFocusable(n,t)}return!1}static isElementVisible(e){return!!(e.offsetWidth||e.offsetHeight||e.getClientRects().length)}static isRadioButtonFocusable(e,t){return!!e.checked||!((t=t.filter((t=>(null==t?void 0:t.name)===e.name))).findIndex((t=>e!==t&&t.checked))>0)&&e===t[0]}static findItemElementIndexByChild(e,t){return e.findIndex((e=>s.containsElement(e,t)))}}function v(e,t){return function(e,t){const n=[];for(let s=0;s<e.length;s++){const i=e[s];if(t.has(i)){const e=t.get(i);e&&n.push(e)}}return n}([...s.getRootPathAndSelf(e)],t)}g.elementsToResetTabIndex=[],g.resetTabIndexAction=new t;class I extends CustomEvent{constructor(e,t){super(I.eventName,{detail:new E(e,t),bubbles:!0,composed:!0,cancelable:!0})}}I.eventName="dxbl-keyboard-navigator.shortcut";class E{constructor(e,t){this.Event=e,this.Info=JSON.stringify(t)}}a.register(I.eventName,(e=>{const t=e.detail;return{Key:t.Event.key,Code:t.Event.code,CtrlKey:t.Event.ctrlKey,AltKey:t.Event.altKey,ShiftKey:t.Event.shiftKey,MetaKey:t.Event.metaKey,Info:t.Info}}));function S(e){return"getPortal"in e}const f=new class{get keyboardEventProcessed(){return this._keyboardEventProcessed}get processingKeyCode(){return this._processingKeyCode}constructor(){this.boundOnKeyDownHandler=this.handleKeyDown.bind(this),this.boundOnKeyUpHandler=this.handleKeyUp.bind(this),this.boundOnMouseDownHandler=this.handleMouseDown.bind(this),this.boundOnFocusInHandler=this.handleFocusIn.bind(this),this.navigators=new Map,this.lockTreeLineActivation=!1,this.leavingToDocument=!1,this._keyboardEventProcessed=!1,this._processingKeyCode=null,setTimeout(this.addEventSubscriptions.bind(this))}register(e){const t=e.targetElement;this.navigators.has(t)||this.navigators.set(t,e)}remove(e){const t=e.targetElement;this.navigators.has(t)&&this.navigators.delete(t)}addFocusHiddenAttributes(e){this.forEachNavigatorInTreeLine(e,(e=>e.addFocusHiddenAttribute()))}removeFocusHiddenAttributes(e){this.forEachNavigatorInTreeLine(e,(e=>!e.removeFocusHiddenAttribute()))}addEventSubscriptions(){document.addEventListener("keydown",this.boundOnKeyDownHandler,{capture:!0}),document.addEventListener("keyup",this.boundOnKeyUpHandler,{capture:!0}),document.addEventListener("mousedown",this.boundOnMouseDownHandler,{capture:!0}),document.addEventListener("focusin",this.boundOnFocusInHandler,{capture:!0})}handleKeyDown(t){this._processingKeyCode=e.KeyUtils.getEventKeyCode(t),this.handleKeyboardEvent(t,((e,n)=>e.onKeyDown(t,n)))}handleKeyUp(e){this.handleKeyboardEvent(e,((t,n)=>t.onKeyUp(e,n))),this._processingKeyCode=null}handleKeyboardEvent(e,t){this._keyboardEventProcessed=!0,this.forEachNavigatorInTreeLine(e.target,((n,s,i,r)=>{if(t(n,s)){let t=!0;if(n.leaveDirection!==b.None){t=this.leaveFromNavigator(n,i<r.length-1?r[i+1]:null)}return t&&l.markHandled(e,!1),this.removeFocusHiddenAttributes(s),!0}return n.getIsNestedContentSelected(s)}))}handleMouseDown(e){this.activateTreeLine(e,!1),this.updateActiveState(e.target)}handleFocusIn(e){this.leavingToDocument||(this.updateActiveState(e.target),this.activateTreeLine(e,!0),this._keyboardEventProcessed=!1)}getNavigatorsTreeLine(e){return function(e,t){let n=[];do{const s=v(e,t),i=s[s.length-1];n=n.concat(...s),e=i&&S(i)?i.getPortal():null}while(e);return n}(e,this.navigators)}activateTreeLine(e,t){if(this.lockTreeLineActivation)return;this.lockTreeLineActivation=!0;this.activateTreeLineCore(e,e.target,t),this.lockTreeLineActivation=!1}activateTreeLineCore(e,t,n){this.forEachNavigatorInTreeLine(t,((s,i,r)=>{if(s.updateSelectedItems(i,t,e),0===r){s.activateStrategyTreeLine(e,t,n);const i=s.findLastSelectedStrategy();if(!i||!i.isTransitContainer||S(s))return!1;const r=i.getNestedContentElement();if(r&&n)return this.updateActiveState(r),this.activateTreeLineCore(e,r,n),!0}return!1}))}forEachNavigatorInTreeLine(e,t){var n;const s=this.getNavigatorsTreeLine(e);for(let i=0;i<s.length;i++){const r=s[i];if(t(r,e,i,s))break;const o=S(r)?r:null;o&&(e=null!==(n=o.getPortal())&&void 0!==n?n:e)}}updateActiveState(e){let t=null;if(c(e)){const n=v(e,this.navigators);t=n.length>0?n[0]:null}this.navigators.forEach((e=>{e.isActive=e===t}))}leaveFromNavigator(e,t){const n=e.leaveDirection===b.Backward,s=(n?g.findPrevFocusableElement:g.findNextFocusableNotChildElement)(e.targetElement),i=e.getSelectedItemElement(),r=null==t?void 0:t.getSelectedItemElement(),o=!i||r&&r.contains(i)?r:null;if(s&&(!o||o.contains(s)&&o!==s))g.focusElement(s);else{if(!t)return this.leaveToDocument(e,n),!1;t.captureFocus(e.leaveDirection)}return!0}leaveToDocument(e,t){const n=g.findFocusableElements(e.targetElement),s=t||0===n.length?e.targetElement:n[n.length-1];g.isFocusedElement(s)||(this.leavingToDocument=!0,g.focusElement(s,!0),this.leavingToDocument=!1)}},y="dxbl-keyboard-navigator";var b,C;!function(e){e[e.None=0]="None",e[e.Backward=1]="Backward",e[e.Forward=2]="Forward"}(b||(b={})),function(e){e.NearestItem="NearestItem",e.LastActiveItem="LastActiveItem"}(C||(C={}));class p extends HTMLElement{constructor(){super(),this._isActive=!1,this._initialized=!1,this._leaveDirection=b.None,this._owner=null,this._rootStrategy=null,this.unsubscribeFocusHiding=null,this.strategies=new Map,this._componentReactivateMode=C.LastActiveItem,this._focusedFromForwardElement=!1,this.contentChangedObserver=new MutationObserver(this.onContentChanged.bind(this))}get rootStrategy(){return this._rootStrategy}get isActive(){return this._isActive}set isActive(e){this.updateActiveState(e)}get leaveDirection(){return this._leaveDirection}get initialized(){return this._initialized&&!!this._rootStrategy}get targetElement(){return this.owner}get owner(){return this._owner}get focusedFromForwardElement(){return this._focusedFromForwardElement}get preventScrollOnFocus(){return!f.keyboardEventProcessed}attributeChangedCallback(e,t,n){"reactivate-mode"===e&&(this._componentReactivateMode=C[n])}initialize(e,t,s=null){this._initialized=!1,this._owner=e,this._rootStrategy=t,g.makeElementFocusable(this.targetElement),this.register(s),this.unsubscribeFocusHiding=d(this.targetElement),this.attachStrategy(this._rootStrategy),this._rootStrategy.initialize(),n.isFocusWithin(this.targetElement)&&this.updateSelectedItems(document.activeElement),this._initialized=!0}reinitialize(){this._initialized=!1,this._rootStrategy&&(this._rootStrategy.initialize(),this.isActive&&this._rootStrategy.activate()),this._initialized=!0}disconnectedCallback(){this.disposeComponent()}disposeComponent(){var e;this.disposeStrategies(),this._rootStrategy=null,this.contentChangedObserver.disconnect(),null===(e=this.unsubscribeFocusHiding)||void 0===e||e.call(this),this.removeFocusHiddenAttribute(),f.remove(this)}attachStrategy(e){e&&this.strategies.set(e.targetElement,e)}getStrategy(e){var t;return e&&this.strategies.has(e)&&null!==(t=this.strategies.get(e))&&void 0!==t?t:null}getIsNestedContentSelected(e){return this.getStrategiesPath(e).findIndex((e=>e.nestedContentSelected))>=0}onKeyDown(e,t){return this.handleKeyboardEvent(t,(t=>t.handleKeyDown(e)))}onKeyUp(e,t){return this.handleKeyboardEvent(t,(t=>t.handleKeyUp(e)))}dispatchShortcutEvent(e){const t=this.collectShortcutInfo(e);this.dispatchEvent(new I(e,t))}captureFocus(e=b.None){var t;null===(t=this.findLastSelectedStrategy())||void 0===t||t.captureFocus(e)}leaveFocus(e){this._leaveDirection=e?b.Backward:b.Forward}leaveFromNavigator(){f.leaveFromNavigator(this,null)}getSelectedItemElement(){var e;return null===(e=this.findLastSelectedStrategy())||void 0===e?void 0:e.selectedItemElement}addFocusHiddenAttribute(){return h(this.targetElement),!0}removeFocusHiddenAttribute(){return this.targetElement&&u(this.targetElement),!0}passFocusHiddenAttribute(e){f.addFocusHiddenAttributes(e)}updateSelectedItems(e,t=e,n=null){const s=this.getStrategiesPath(e);for(let i=0;i<s.length;i++){const r=0===i,o=s[i];o.updateSelectedItemByChildElement(e,n),e===t&&o.canSwitchToNestedContentMode()&&(o.nestedContentSelected=r)}}activateStrategyTreeLine(e,t,n){if(n&&this.tryActivateFromPreviousFocusedSibling(e))return;const s=this.getStrategiesPath(t);if(0===s.length)return;const i=s[0],r=i===this._rootStrategy;r&&i.activate(),r&&1!==s.length||this.handleSelectedItemFocus(i,t)}tryActivateFromPreviousFocusedSibling(e){const t=e.relatedTarget;if(!t||this.targetElement.contains(t))return!1;const n=e.target,s=g.findPrevFocusableElement(n),i=g.findNextFocusableNotChildElement(n);if(t!==s&&t!==i)return!1;const r=this.getStrategiesPath(n);return 0!==r.length&&(this.updateAndActivateRootStrategy(r[0],t,i),!0)}handleSelectedItemFocus(e,t){var n;let s=e.selectedItemElement===t;if(!s){const i=g.findFocusableElementInRootPath(t);e.selectedItemElement!==i&&(null===(n=e.selectedItemElement)||void 0===n?void 0:n.contains(i))||(s=!0)}s&&(e.resetNestedContentSelectedRecursive(),e.focusSelectedItem())}updateAndActivateRootStrategy(e,t,n){if(e.resetNestedContentSelectedRecursive(),this._rootStrategy){if(this._focusedFromForwardElement=t===n,this._componentReactivateMode===C.NearestItem){this._rootStrategy.updateSelectedItemByIndex(t===n?this._rootStrategy.itemCount-1:0)}this._rootStrategy.activate()}}updateActiveState(e){this._isActive!==e&&(this._isActive=e,this.strategies.forEach((t=>t.onActiveStateChanged(e))))}register(e){const t=["disabled"];e&&t.push(e),this.contentChangedObserver.observe(this.targetElement,{childList:!0,subtree:!0,attributeFilter:t}),f.register(this)}onContentChanged(){this.detachDisconnectedStrategies(),this.reinitialize()}getStrategiesPath(e){return v(e,this.strategies)}findLastSelectedStrategy(){var e,t;return null!==(t=null===(e=this._rootStrategy)||void 0===e?void 0:e.findLastSelectedStrategy())&&void 0!==t?t:null}handleKeyboardEvent(e,t){this._leaveDirection=b.None;const n=this.getStrategiesPath(e);for(let e=0;e<n.length;e++){const s=n[e];if(t(s))return!0;if(s.nestedContentSelected)return!1}return!1}detachDisconnectedStrategies(){var e;const t=Array.from(this.strategies.keys()).filter((e=>!e.isConnected));for(const n of t)null===(e=this.strategies.get(n))||void 0===e||e.onDispose(),this.strategies.delete(n)}disposeStrategies(){this.strategies.forEach((e=>e.onDispose()))}collectShortcutInfo(e){const t=this.getStrategiesPath(e.target).map((e=>e.getShortcutContext()));return Object.assign({},...t)}}customElements.define(y,p);class F{constructor(e,t,n=!1){this._isTransitContainer=!1,this._nestedContentSelected=!1,this._selectedItemIndex=0,this._savedSelectedItemElement=null,this._leaveDirection=b.None,this._navigator=e,this._targetElement=t,this._items=[],this._isTransitContainer=n,this._isMacOSPlatform=m().isMac}get navigator(){return this._navigator}get targetElement(){return this._targetElement}get itemCount(){return this.items.length}get selectedItemElement(){return this.items[this.selectedItemIndex]}get nestedContentSelected(){return this._nestedContentSelected}set nestedContentSelected(e){this._nestedContentSelected=e&&this.canFocusSelectedItem()}get preventScrollOnFocus(){return this._navigator.preventScrollOnFocus}get selectedItemIndex(){return this._selectedItemIndex}set selectedItemIndex(e){this._selectedItemIndex=e}get isTransitContainer(){return this._isTransitContainer}get leaveDirection(){return this._leaveDirection}get items(){return this._items}get firstItemSelected(){return 0===this.selectedItemIndex}get lastItemSelected(){return this.selectedItemIndex===this.itemCount-1}get isObsolete(){return!1}get isMacOSPlatform(){return this._isMacOSPlatform}getShortcutContext(){return{}}initialize(){this.storeSelection(),this.items.splice(0);const e=this.queryItems().filter((e=>!!e));0===e.length&&e.push(this.targetElement),e.forEach((e=>this.initializeItemStrategy(e))),this.tryRestoreSelection()}activate(){this.selectItem(this.selectedItemIndex)}onDispose(){var e;(null===(e=this.selectedItemElement)||void 0===e?void 0:e.isConnected)&&g.removeTabIndex(this.selectedItemElement)}getItem(e){return this.items[e]}getSelectedItemStrategy(){return this.navigator.getStrategy(this.selectedItemElement)}focusSelectedItem(){this.canFocusSelectedItem()&&g.focusElement(this.selectedItemElement,this.preventScrollOnFocus)}isIndexWithinBoundaries(e){return e>=0&&e<this.itemCount}handleKeyDown(t){const n=e.KeyUtils.getEventKeyCode(t);if(this.nestedContentSelected){if(n===e.KeyCode.Esc)return this.processEscapeKeyDown();if(n===e.KeyCode.Tab)return this.processTabKeyDown(t.shiftKey);this.isImmediatelyClickEnabled()&&this.hasSingleFocusableElement()&&this.leaveFromNestedContent()}return!this.nestedContentSelected&&n===e.KeyCode.Enter&&this.processEnter(t)}handleKeyUp(e){return!1}switchToNestedContent(){if(this.nestedContentSelected&&this.isNestedElementFocused())return!0;if(this.selectedItemElement){const e=this.getNestedContentElement();if(e)return this.nestedContentSelected=!0,g.focusElement(e,this.preventScrollOnFocus),!0}return!1}leaveFromNestedContent(e=b.None){this.nestedContentSelected=!1,this.isTransitContainer?(this._leaveDirection=e,this.navigator.isActive=!0,this.leaveDirection!==b.None&&this.leaveTransitContainer(this.leaveDirection),this._leaveDirection=b.None):this.focusSelectedItem()}resetNestedContentSelectedRecursive(){this.nestedContentSelected=!1;if(!(this.targetElement===this.selectedItemElement)){const e=this.getSelectedItemStrategy();null==e||e.resetNestedContentSelectedRecursive()}}captureFocus(e=b.None){this.nestedContentSelected&&this.leaveFromNestedContent(e)}findLastSelectedStrategy(){const e=this.getSelectedItemStrategy();return e&&e!==this?e.findLastSelectedStrategy():this}updateSelectedItemByIndex(e){const t=this.selectedItemElement;this.selectedItemIndex=this.validateItemIndex(e),t&&this.selectedItemElement!==t&&(g.scheduleResetTabIndex(t),this.nestedContentSelected=!1),this.selectedItemElement&&this.canFocusSelectedItem()&&g.makeElementFocusable(this.selectedItemElement)}updateSelectedItemByChildElement(e,t=null){const n=this.findItemElementIndexByChild(e);(n>-1||e===this.targetElement)&&this.updateSelectedItemByIndex(n)}canSwitchToNestedContentMode(){return!1}onActiveStateChanged(e){}leaveTransitContainer(e){}createItemStrategy(e){return null}getItemStrategy(e){const t=this.validateItemIndex(e);return this.navigator.getStrategy(this.getItem(t))}queryItems(){return new Array}queryItemsBySelector(e){return Array.from(this.targetElement.querySelectorAll(e))}selectItem(e){var t;this.updateSelectedItemByIndex(e),this.canFocusSelectedItem()?this.navigator.isActive&&!this.isNestedElementFocused()&&this.focusSelectedItem():null===(t=this.getSelectedItemStrategy())||void 0===t||t.activate()}moveToPrevItem(e=!1){this.selectedItemIndex>0?this.selectItem(this.selectedItemIndex-1):e&&this.selectItem(this.itemCount-1)}moveToNextItem(e=!1){this.selectedItemIndex<this.itemCount-1?this.selectItem(this.selectedItemIndex+1):e&&this.selectItem(0)}moveToFirstItem(){this.selectItem(0)}moveToLastItem(){this.selectItem(this.itemCount-1)}isImmediatelyClickEnabled(){return!1}raiseClickEvent(e,t,n,s){const i=new MouseEvent("click",{bubbles:!0,cancelable:!0,ctrlKey:t,metaKey:n,shiftKey:s});e.dispatchEvent(i)}performShortcutEvent(e){this.navigator.dispatchShortcutEvent(e)}validateItemIndex(e){return e=Math.max(e,0),Math.min(e,this.itemCount-1)}leaveBackward(){this.navigator.leaveFocus(!0)}leaveForward(){this.navigator.leaveFocus(!1)}getNestedContentContainer(){return this.selectedItemElement}getNestedContentElement(){const e=this.findFocusableElements();return e.length>0?e[0]:null}findItemElementIndexByChild(e){return g.findItemElementIndexByChild(this.items,e)}tryRestoreSelectedItem(e){if(e.isConnected){const t=this.items.indexOf(e);t>-1&&(this.selectedItemIndex=t)}}initializeItemStrategy(e){this.items.push(e);let t=this.navigator.getStrategy(e);t&&!t.isObsolete||(null==t||t.onDispose(),t=this.createItemStrategy(e),this.navigator.attachStrategy(t)),t&&t!==this&&t.initialize()}canFocusSelectedItem(){return this.targetElement===this.selectedItemElement||!this.getSelectedItemStrategy()}processEnter(e){return!(!this.isImmediatelyClickEnabled()||!this.processImmediatellyClick(e))||this.switchToNestedContent()}processEscapeKeyDown(){return this.leaveFromNestedContent(b.None),!0}processTabKeyDown(e){if(this.selectedItemElement){const t=this.findFocusableElements();if(t.length>0){const n=g.isFocusedElement(t[0]),s=g.isFocusedElement(t[t.length-1]);let i=null;if(e&&n&&(i=b.Backward),!e&&s&&(i=b.Forward),i)return this.leaveFromNestedContent(i),!0}}return!1}findFocusableElements(){return g.findFocusableElements(this.getNestedContentContainer())}processImmediatellyClick(e){const t=this.getNestedContentElement();return!(!t||!this.hasSingleFocusableElement())&&(this.raiseClickEvent(t,e.ctrlKey,e.metaKey,e.shiftKey),!0)}hasSingleFocusableElement(){return 1===this.findFocusableElements().length}isNestedElementFocused(){if(!document.activeElement||!this.nestedContentSelected)return!1;return g.findItemElementIndexByChild(this.items,document.activeElement)===this.selectedItemIndex}storeSelection(){this._savedSelectedItemElement=this.selectedItemElement}tryRestoreSelection(){return!!this._savedSelectedItemElement&&(this.tryRestoreSelectedItem(this._savedSelectedItemElement),this._savedSelectedItemElement=null,!0)}}export{y as D,g as F,F as K,b as L,p as a,f as n};
