import{_ as t}from"./tslib.es6-d65164b3.js";import{d as e}from"./dom-554d0cc7.js";import{s as i}from"./enumConverter-6047c3ff.js";import{S as o}from"./single-slot-element-base-01d93921.js";import{C as s}from"./css-classes-c63af734.js";import{T as n}from"./text-editor-733d5e56.js";import{n as a}from"./property-4ec0b52d.js";import{g as r,r as l,a as c}from"./dom-utils-d057dcaa.js";import{e as d}from"./custom-element-267f9a21.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./custom-events-helper-e7f279d3.js";import"./constants-da6cacac.js";import"./eventhelper-8bcec49f.js";import"./devices-17b9ba08.js";import"./focus-utils-ae044224.js";import"./key-ffa272aa.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";class p{}p.Root=s.Prefix+"-fl",p.Loading=p.Root+"-loading",p.RowBreak=p.Root+"-row-break",p.Group=p.Root+"-group",p.GroupBody=p.Group+"-body",p.GroupDecoration=p.Root+"-gd",p.GroupTab=p.Root+"-gt",p.GroupHeaderTemplate=p.Root+"-group-header-tmpl",p.Item=p.Root+"-item",p.TabItem=p.Root+"-tab-item",p.Caption=p.Root+"-cpt",p.EmptyCaption=p.Root+"-empty-caption",p.BeginRow=p.Root+"-begin-row",p.TabContent=p.Root+"-tab-content",p.ItemContentWithCaption=p.Root+"-ctrl",p.ItemContentWithoutCaption=p.Root+"-ctrl-nc",p.ItemHorizontal=p.Root+"-item-horizontal",p.HeaderContentTemplate=p.Root+"-header-content-tmpl",p.CaptionTemplate=p.Root+"-caption-tmpl",p.ItemCaptionWidthCalculation=p.Root+"-calc",p.Row=s.Prefix+"-row";const u="dxbl-form-layout-item";class m extends o{constructor(){super(),this._currentCaption=null,this.captionFor=null}get useShadowDom(){return!1}_findFormLayout(){let t=this.parentNode;for(;t&&!(t instanceof A);)t=null==t?void 0:t.parentNode;return t}contentChanged(){var t;super.contentChanged();const e=this.initAndGetCaptionForInput();if((null===(t=this._currentCaption)||void 0===t?void 0:t.innerHTML)!==(null==e?void 0:e.innerHTML)){const t=this._findFormLayout();null==t||t.calculateAdaptivityCssRules()}this._currentCaption=e}initAndGetCaptionForInput(){var t;const e=this.getCaption();if(e&&""!==this.captionFor){const i=this.captionFor||(null===(t=this.getEditorInput())||void 0===t?void 0:t.id);i&&(e.htmlFor=i)}return e}getCaption(){return this.querySelector(`:scope > .${p.Caption}`)}getEditorInput(){return this.querySelector(`.${n.TextEdit} > input, .${n.TextEdit} > textarea, .dxbl-checkbox-check-element > input`)}}var h;t([a({attribute:"caption-for"})],m.prototype,"captionFor",void 0),function(t){t.v4="v4",t.v5="v5"}(h||(h={}));const C={};function v(t){!function(t,i,o){const s=C[t]||(C[t]=e.DomUtils.getCurrentStyle(document.body).getPropertyValue(t)||i);if(s){const t=r();t&&t.insertRule("@media (min-width: "+s+") {\n"+o+"\n}\n",t.cssRules.length)}}("--breakpoint-lg","992px",t)}var y,g;!function(t){t[t.None=0]="None",t[t.InGroups=1]="InGroups",t[t.All=2]="All"}(y||(y={})),function(t){t[t.Small=0]="Small",t[t.Medium=1]="Medium",t[t.Large=2]="Large"}(g||(g={}));const f=` > .${p.Caption}`,b=t=>`${u}[fl-id=${t}].${p.Item}.${p.ItemHorizontal}`,R=/\./g;class I extends o{constructor(){super(...arguments),this.captionAlignment=y.InGroups}get itemSelector(){return` > .${p.Row} > ${b(this.mainElementId)}`}get mainElementId(){return this.id}getMainElement(){return document.getElementById(this.mainElementId)}calculateAdaptivityCssRules(){}startCalculationItems(t){const e=this.querySelectorAll(`:scope${` .${t}`}[fl-id=${this.mainElementId}]`);for(let t=0;t<e.length;t++)e[t].calculateAdaptivityCssRules()}updateGroups(){this.startCalculationItems(p.Group)}updateTabs(){this.startCalculationItems(p.TabContent)}createAdaptivityCssRules(){var t;let i=0;e.DomUtils.addClassName(this,p.ItemCaptionWidthCalculation);const o=` .${p.Group}[fl-id=${this.mainElementId}] .${p.GroupBody}[expanded-state=False]`,s=null===(t=this.getMainElement())||void 0===t?void 0:t.querySelectorAll(`:scope${o}`),n=t=>{for(let e=0;e<(null==s?void 0:s.length);e++)s[e].style.display=t?"":"none",s[e].style.visibility=t?"hidden":""};n(!0);const a=this.findCaptions();a&&a.length>0&&(i=this.getMaxCaptionWidth(a),this.createAdaptivityCssRulesForElements(i)),n(!1),e.DomUtils.removeClassName(this,p.ItemCaptionWidthCalculation)}findCaptions(){return[...this.querySelectorAll(`:scope${this.itemSelector}${f}`)]}createAdaptivityCssRulesForElements(t){this.createCssRulesForElementsInternal(t,!1,!1)}getMaxCaptionWidth(t){let e=0;if(t.length>0){const i=t.find((t=>t.classList.contains(p.EmptyCaption)));let o="0 px";i&&(o=window.getComputedStyle(i,null).getPropertyValue("height"));for(let i=0;i<t.length;i++){const s=t[i].offsetWidth;this.isEmptyCaption(t[i])&&(t[i].style.height=o);const n=t[i].parentNode;n&&s>e&&s<n.offsetWidth&&(e=s)}}return e>0?e+1:0}isEmptyCaption(t){return t.classList.contains(p.EmptyCaption)}getFilteredElements(t){return l(t,(t=>c(t,p.Root)===this))}encodeDotsForMediaQuiery(t){return t.replace(R,"\\$&")}getAdaptivityCssRulesPrefix(){const t=this.getMainElement();if(!t)return null;let e=`.${p.Root}`,i=t;for(;i;)e=" #"+this.encodeDotsForMediaQuiery(i.id)+e,i=c(i.parentNode,p.Root);e.slice(1);let o="",s=this;for(;s&&s!==t&&t.contains(s);)s.id&&(o=" #"+this.encodeDotsForMediaQuiery(s.id)+o),s=c(s.parentNode,p.Group);return e+=o,e}createCssRulesForElementsInternal(t,e,i){if(0===t)return;this.itemCaptionWidth=t;const o=this.getAdaptivityCssRulesPrefix(),s=this.captionAlignment===y.InGroups?this.itemSelector:` ${b(this.mainElementId)}`;let n=o+s+`${f} {\n width:`+t+"px;\n}\n";n+=o+s+` > .${p.ItemContentWithCaption}:not(img):not(.${p.EmptyCaption} + .${p.ItemContentWithCaption}):not(.${p.ItemContentWithoutCaption}) {\n width: calc(100% - `+t+"px);\n}\n",v(n)}}t([a({attribute:"caption-alignment",type:y,converter:i(y)})],I.prototype,"captionAlignment",void 0);class A extends I{constructor(){super(),this._enchancedCallback=()=>{},this._calculationProcess=null,this.sizeMode=g.Medium,this._isRendered=!1,this._mutationObserver=new MutationObserver((t=>{this.itemVisibilityChangeHandler(t)})),this._enchancedCallback=()=>this.calculateAdaptivityCssRules()}needReasignAttributes(){return!1}connectedCallback(){var t;super.connectedCallback(),(null===(t=window.Blazor)||void 0===t?void 0:t.addEventListener)&&window.Blazor.addEventListener("enhancedload",this._enchancedCallback),this._mutationObserver.observe(this,{childList:!0,subtree:!0})}disconnectedCallback(){var t;super.disconnectedCallback(),this._isRendered=!1,this._mutationObserver.disconnect(),(null===(t=window.Blazor)||void 0===t?void 0:t.removeEventListener)&&window.Blazor.removeEventListener("enhancedload",this._enchancedCallback)}itemVisibilityChangeHandler(t){if(this._isRendered){t.some((t=>(t.addedNodes.length||t.removedNodes.length)&&t.target.classList.contains("dxbl-row")))&&this.calculateAdaptivityCssRules()}}willUpdate(t){(t.has("sizeMode")||t.has("captionAlignment"))&&this.calculateAdaptivityCssRules()}findCaptions(){return this.captionAlignment===y.All?this.getFilteredElements(this.querySelectorAll(`${b(this.mainElementId)}:not(.${s.Invisible})`+f)):super.findCaptions()}calculateAdaptivityCssRules(){this._calculationProcess&&clearTimeout(this._calculationProcess),this._calculationProcess=setTimeout((()=>{this.captionAlignment!==y.None&&this.createAdaptivityCssRules(),this.captionAlignment===y.InGroups&&(this.updateGroups(),this.updateTabs()),e.DomUtils.removeClassName(this,p.Loading),this._isRendered=!0}))}}t([a({attribute:"size-mode",type:g,converter:i(g)})],A.prototype,"sizeMode",void 0);class E extends I{constructor(t){super(),this._isMainTabElement=t,this.flId=""}get mainElementId(){return this.flId}calculateAdaptivityCssRules(){this.captionAlignment!==y.None&&this.createAdaptivityCssRules()}}t([a({attribute:"fl-id",type:String})],E.prototype,"flId",void 0);class ${}$.Group=s.Prefix+"-group",$.Header=$.Group+"-header",$.Body=$.Group+"-body",$.BodyContent=$.Body+"-content";let x=class extends E{constructor(){super(!0),this.isActiveTab=!1}get itemSelector(){return` > .${p.Row} > .${p.TabItem}.${p.ItemHorizontal}`}willUpdate(t){t.has("isActiveTab")&&this.isActiveTab&&(this.captionAlignment===y.InGroups&&(this.calculateAdaptivityCssRules(),this.updateTabs(),this.updateGroups()),this.captionAlignment===y.All&&this.getMainElement().calculateAdaptivityCssRules())}createAdaptivityCssRulesForElements(t){this.createCssRulesForElementsInternal(t,!0,!1)}};t([a({attribute:"is-active-tab",type:Boolean})],x.prototype,"isActiveTab",void 0),x=t([d("dxbl-form-layout-tab")],x);let j=class extends E{constructor(){super(!1)}get itemSelector(){const t=` > .${p.Row} > ${b(this.flId)}`,e=` > .${$.Group} > .${$.Body} > .${$.BodyContent}${t}`;return this.isMainElementCardGroup()?e:t}isMainElementCardGroup(){return this.classList.contains(p.GroupDecoration)}createAdaptivityCssRulesForElements(t){this.createCssRulesForElementsInternal(t,!1,this.isMainElementCardGroup())}};j=t([d("dxbl-form-layout-group")],j);let G=class extends m{};G=t([d(u)],G);let w=class extends A{};w=t([d("dxbl-form-layout")],w);export{w as DxFormLayout,G as DxFormLayoutItem,j as dxFormLayoutGroup,x as dxFormLayoutTab};
