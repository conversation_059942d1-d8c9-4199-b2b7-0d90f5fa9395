import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t,b as s,c as i}from"./dx-dropdown-base3-726be7be.js";import{V as n,H as o,a as r,D as l,b as d}from"./dx-scroll-viewer-da0fb41c.js";import{S as a}from"./scroll-viewer-css-classes-e724f203.js";import{a as h,b as c,L as m,c as u,d as p,e as b}from"./dx-list-box-events-6c145567.js";import{b as I}from"./browser-3fc721b7.js";import{d as C}from"./dom-554d0cc7.js";import{t as w,u as y}from"./popup-355ecaa4.js";import{k as v}from"./key-ffa272aa.js";import{n as E}from"./keyboard-navigation-strategy-ea41c807.js";import{D as g}from"./layouthelper-67dd777a.js";import{a as _}from"./dx-virtual-scroll-viewer-f4a3bc9e.js";import{containsFocusHiddenAttribute as D,addFocusHiddenAttribute as f,removeFocusHiddenAttribute as V}from"./focus-utils-ae044224.js";import{C as S}from"./custom-events-helper-e7f279d3.js";import{p as x}from"./constants-da6cacac.js";import{D as K,H as A,L as N}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{n as T}from"./property-4ec0b52d.js";class R{constructor(e){this._selectedItemIndex=-1,this._selectedVisibleIndex=-1,this._firstVisibleIndex=-1,this._lastVisibleIndex=-1,this._firstAvailableVisibleIndex=0,this._lastAvailableVisibleIndex=-1,this._tabKeyPressed=!1,this._isActive=!1,this._renderContainer=null,this._requireUpdateSelectedItemState=-1,this.boundOnRenderContainerMutationHandler=this.onRenderContainerMutation.bind(this),this.boundOnVisibleElementChangedHandler=this.onVisibleElementChanged.bind(this),this._component=e,this._items=[],this._rows=[],this._renderContainerMutationObserver=new MutationObserver(this.boundOnRenderContainerMutationHandler)}get tabKeyPressed(){return this._tabKeyPressed||E.keyboardEventProcessed&&E.processingKeyCode===v.KeyCode.Tab}get items(){return this._items}get itemCount(){return this.items.length}get selectedItemElement(){return this.selectedItemIndex>=0&&this.selectedItemIndex<this.itemCount?this.items[this.selectedItemIndex]:null}get selectedItemIndex(){return this._selectedItemIndex}get isFirstItemSelected(){return this._selectedVisibleIndex<=this._firstAvailableVisibleIndex}get isLastItemSelected(){return this._selectedVisibleIndex>=this._lastAvailableVisibleIndex}get isVirtualRenderModeEnabled(){return this._component.enableVirtualRenderMode}get totalItemCount(){const e=this._component.getScrollViewer();return e&&e instanceof _?e.totalItemCount:this._rows.length}get searchIsAppliedAndReturnsNoneEmptyData(){return this.itemCount>0&&this._component.searchIsApplied}isIndexWithinBoundaries(e){return e>=0&&e<this.itemCount}onPopupShown(){this._isActive=!0,this.initialize(),this.addEventSubscriptions()}onPopupClosed(){this.removeEventSubscriptions(),this.dispose(),this._isActive=!1}processKeyDown(e){switch(v.KeyUtils.getEventKeyCode(e)){case v.KeyCode.Tab:this._tabKeyPressed=!0;break;case v.KeyCode.Enter:case v.KeyCode.Esc:return!this._isActive&&(this._component.dispatchKeyDownEvent(e),!0);case v.KeyCode.Up:return this._component.handleCloseDropDown(e);case v.KeyCode.Down:return this._component.handleOpenDropDown(e);case v.KeyCode.Delete:return this._component.handleTryClearEditorValue(e)}return!1}processCapturedKeyDown(e){switch(v.KeyUtils.getEventKeyCode(e)){case v.KeyCode.Tab:this._tabKeyPressed=!0;break;case v.KeyCode.Enter:case v.KeyCode.Esc:return this._component.dispatchKeyDownEvent(e),!0;case v.KeyCode.Up:return!(e.altKey||e.metaKey)&&this.handleArrowUpKeyDown();case v.KeyCode.Down:return!(e.altKey||e.metaKey)&&this.handleArrowDownKeyDown();case v.KeyCode.PageUp:return this.handlePageUp();case v.KeyCode.PageDown:return this.handlePageDown();case v.KeyCode.Home:return e.ctrlKey&&e.shiftKey&&this.handleHomeKeyDown();case v.KeyCode.End:return e.ctrlKey&&e.shiftKey&&this.handleEndKeyDown()}return!1}processKeyUp(e){switch(this._tabKeyPressed=!1,v.KeyUtils.getEventKeyCode(e)){case v.KeyCode.Down:case v.KeyCode.Up:case v.KeyCode.PageUp:case v.KeyCode.PageDown:this.notifyFocusedRowChanged();break;case v.KeyCode.Home:case v.KeyCode.End:e.shiftKey&&e.ctrlKey&&this.notifyFocusedRowChanged()}return!1}updateState(){this._isActive?(this.udpdateFocusState(),this.updateSelectedItemState()):this._requireUpdateSelectedItemState++}shouldHandleKeyDown(e){switch(v.KeyUtils.getEventKeyCode(e)){case v.KeyCode.Enter:case v.KeyCode.Esc:return this._isActive;default:return!1}}getItem(e){return this.items[e]}addFocusHiddenAttribute(){this._renderContainer&&!D(this._renderContainer)&&f(this._renderContainer)}removeFocusHiddenAttribute(){this._renderContainer&&D(this._renderContainer)&&V(this._renderContainer)}containsFocusHiddenAttribute(){return!!this._renderContainer&&D(this._renderContainer)}dispose(){this.removeFocusHiddenAttribute(),this._renderContainerMutationObserver.disconnect(),this.setSelectedItem(-1,!0),this.notifyFocusedRowChanged(),this._firstAvailableVisibleIndex=-1,this._lastAvailableVisibleIndex=-1,this._firstVisibleIndex=-1,this._lastVisibleIndex=-1,this._items=[],this._rows=[]}initialize(){if(this._items=this.queryItems(),this._rows=this.queryRows(),this.updateBoundaries(),this._renderContainer=this._component.renderContainer,this._renderContainer&&this._renderContainerMutationObserver.observe(this._renderContainer,{childList:!0,subtree:!0}),this.addFocusHiddenAttribute(),this.updateViewportBoundaries(),this._component.useCustomInput){const e=this.findSelectedDataItemIndex();e>=0&&(this.setSelectedItem(e),this.notifyFocusedRowChanged())}else this.restoreFocusedState()}restoreFocusedState(){if(this.searchIsAppliedAndReturnsNoneEmptyData&&this._requireUpdateSelectedItemState>=0)this.removeFocusHiddenAttribute(),this._requireUpdateSelectedItemState=-1,this.setSelectedItem(0,!0);else{const e=this.findSelectedDataItemIndex();e>=0&&this.setSelectedItem(e,!0)}this.notifyFocusedRowChanged()}handleArrowUpKeyDown(){return this.removeFocusHiddenAttribute(),this.isFirstItemSelected?(this.addFocusMarkerClass(this.selectedItemElement),!1):(this.isVirtualRenderModeEnabled?this.moveToPrevVirtualItem():this.moveToPrevItem(),!0)}handleArrowDownKeyDown(){return this.removeFocusHiddenAttribute(),this.isLastItemSelected?(this.addFocusMarkerClass(this.selectedItemElement),!1):(this.isVirtualRenderModeEnabled?this.moveToNextVirtualItem():this.moveToNextItem(),!0)}handlePageUp(){return this.removeFocusHiddenAttribute(),this.isFirstItemSelected?(this.addFocusMarkerClass(this.selectedItemElement),!1):(this.isVirtualRenderModeEnabled?this.moveToPrevVirtualPageItem():this.moveToPrevPageItem(),!0)}handlePageDown(){return this.removeFocusHiddenAttribute(),this.isLastItemSelected?(this.addFocusMarkerClass(this.selectedItemElement),!1):(this.isVirtualRenderModeEnabled?this.moveToNextVirtualPageItem():this.moveToNextPageItem(),!0)}moveToNextPageItem(){const e=this._lastAvailableVisibleIndex,t=this._component.getScrollViewer();if(t){const s=h.calculateBoundaryItemVisibleIndex(this,t,!0),i=s>e?e:s,r=h.isGroupRow(this,i),l=h.findItemIndex(r?i-1:i,this.items);this.setSelectedItem(l);const d=r?h.getRow(this,i):this.selectedItemElement;this.scrollToElement(n.Bottom,o.None,d)}}moveToPrevPageItem(){const e=this._component.getScrollViewer();if(e){const t=h.calculateBoundaryItemVisibleIndex(this,e,!1),s=t<0?0:t,i=h.isGroupRow(this,s),r=h.findItemIndex(i?s+1:s,this.items);this.setSelectedItem(r);const l=i?h.getRow(this,s):this.selectedItemElement;this.scrollToElement(n.Top,o.None,l)}}moveToPrevVirtualPageItem(){const e=this._component.getScrollViewer();if(e){const t=h.calculateBoundaryItemVisibleIndex(this,e,!1);if(this.isElementInsideViewport(t)){const e=h.isGroupRow(this,t),s=h.findItemIndex(e?t+1:t,this.items);this.setSelectedItem(s);const i=e?h.getRow(this,t):this.selectedItemElement;this.scrollToElement(n.Top,o.None,i)}else this.notifyMakeElementVisible(t,n.Top)}}moveToNextVirtualPageItem(){const e=this._component.getScrollViewer();if(e){const t=h.calculateBoundaryItemVisibleIndex(this,e,!0);if(this.isElementInsideViewport(t)){const e=h.isGroupRow(this,t),s=h.findItemIndex(e?t-1:t,this.items);this.setSelectedItem(s);const i=e?h.getRow(this,t):this.selectedItemElement;this.scrollToElement(n.Bottom,o.None,i)}else this.notifyMakeElementVisible(t,n.Bottom)}}handleHomeKeyDown(){return this.removeFocusHiddenAttribute(),this.isFirstItemSelected?(this.addFocusMarkerClass(this.selectedItemElement),!1):(this.isVirtualRenderModeEnabled?this.moveToFirstVirtualItem():this.moveToFirstItem(),!0)}handleEndKeyDown(){return this.removeFocusHiddenAttribute(),this.isLastItemSelected?(this.addFocusMarkerClass(this.selectedItemElement),!1):(this.isVirtualRenderModeEnabled?this.moveToLastVirtualItem():this.moveToLastItem(),!0)}moveToPrevItem(){this.selectedItemIndex>0&&this.setSelectedItem(this.selectedItemIndex-1),this.scrollToElement(null,o.None)}moveToNextItem(){this.selectedItemIndex<this.itemCount-1&&this.setSelectedItem(this.selectedItemIndex+1),this.scrollToElement(null,o.None)}moveToNextVirtualItem(){let e=this._selectedVisibleIndex+1;this.isElementInsideViewport(e)&&h.isGroupRow(this,e)&&e++,this.isElementInsideViewport(e)&&this.selectedItemIndex<this.totalItemCount-1?(this.setSelectedItem(this.selectedItemIndex+1),this.scrollToElement(null,o.None)):this.notifyMakeElementVisible(e,n.Bottom)}moveToPrevVirtualItem(){let e=this._selectedVisibleIndex-1;0===e&&0===this.selectedItemIndex||(this.isElementInsideViewport(e)&&h.isGroupRow(this,e)&&e--,this.isElementInsideViewport(e)&&this.selectedItemIndex>0?(this.setSelectedItem(this.selectedItemIndex-1),this.scrollToElement(null,o.None)):this.notifyMakeElementVisible(e,n.Top))}moveToFirstItem(){if(this._component.getScrollViewer()){const e=h.isGroupRow(this,0);this.setSelectedItem(0);const t=e?h.getRow(this,0):this.selectedItemElement;this.scrollToElement(n.Top,o.None,t)}}moveToFirstVirtualItem(){this.isElementInsideViewport(0)&&this.selectedItemIndex>0?(this.setSelectedItem(0),this.scrollToElement(n.Top,o.None)):this.notifyMakeElementVisible(0,n.Top)}moveToLastItem(){this._component.getScrollViewer()&&(this.setSelectedItem(this.itemCount-1),this.scrollToElement(n.Bottom,o.None))}moveToLastVirtualItem(){const e=this.totalItemCount-1;this.isElementInsideViewport(e)&&this.selectedItemIndex<this.itemCount-1?(this.setSelectedItem(this.itemCount-1),this.scrollToElement(n.Bottom,o.None)):this.notifyMakeElementVisible(e,n.Bottom)}updateViewportBoundaries(){if(0===this._rows.length||0===this.itemCount)return;this._firstVisibleIndex=this.getElementVisibleIndex(this._rows[0]);this._lastVisibleIndex=this.getElementVisibleIndex(this._rows[this._rows.length-1])}queryRows(){return this.itemsContainer?Array.from(this.itemsContainer.querySelectorAll(c.VisibleItemSelector)):[]}get itemsContainer(){const e=this._component.getTable();return e?e.querySelector(c.TableBodySelector):this._component.getList()}updateBoundaries(){let e=0;0!==this._firstAvailableVisibleIndex&&(e++,this._firstAvailableVisibleIndex=0);const t=this.totalItemCount-1;return this._lastAvailableVisibleIndex!==t&&(e++,this._lastAvailableVisibleIndex=t),e>0}isElementInsideViewport(e){return e>=this._firstVisibleIndex&&e<=this._lastVisibleIndex}findItemIndex(e){for(let t=0;t<this.itemCount;t++){const s=this.items[t];if(s&&this.getElementVisibleIndex(s)===e)return t}return-1}getElementVisibleIndex(e){return g.getAttributeIntValue(e,m.VisibleIndexAttributeName,-1)}findSelectedDataItemIndex(){for(let e=0;e<this.itemCount;e++){const t=this.items[e];if(t&&t.matches(c.SelectedItemSelector))return e}return-1}notifyFocusedRowChanged(){this._component.notifyFocusedRowChanged(this._selectedVisibleIndex)}notifyMakeElementVisible(e,t=null){e<0||this._component.notifyMakeElementVisible(e,h.getVerticalEdge(t))}setSelectedItem(e,t=!1){(this.selectedItemIndex!==e||t)&&(this.removeFocusMarkerClass(this.selectedItemElement),this._selectedItemIndex=e,this.selectedItemElement?(this.addFocusMarkerClass(this.selectedItemElement),this.updateActiveDescendantAttribute(this.selectedItemElement),this._selectedVisibleIndex=this.getElementVisibleIndex(this.selectedItemElement)):(this.removeFocusMarkerClass(this.selectedItemElement),this.updateActiveDescendantAttribute(this.selectedItemElement),this._selectedVisibleIndex=-1))}updateActiveDescendantAttribute(e){if(this.containsFocusHiddenAttribute())return;const t=e&&e.getAttribute(m.ItemIdAttributeName);this._component.updateActiveDescendantAttributeValue(null!=t?t:"")}removeFocusMarkerClass(e){e&&C.DomUtils.hasClassName(e,u.FocusedItemClassName)&&C.DomUtils.removeClassName(e,u.FocusedItemClassName)}addFocusMarkerClass(e){this.containsFocusHiddenAttribute()||!e||C.DomUtils.hasClassName(e,u.FocusedItemClassName)||C.DomUtils.hasClassName(e,u.EmptyDataItemClassName)||C.DomUtils.addClassName(e,u.FocusedItemClassName)}queryItems(){const e=this._component.getTable();if(e){const t=e.querySelector(c.TableBodySelector);if(t)return h.queryItemsBySelector(t,c.TableDataRowSelector)}else{const e=this._component.getList();if(e)return h.queryItemsBySelector(e,c.ItemSelector)}return[]}addEventSubscriptions(){const e=this._component.getScrollViewer();e&&e.addEventListener(r.eventName,this.boundOnVisibleElementChangedHandler)}removeEventSubscriptions(){const e=this._component.getScrollViewer();e&&e.removeEventListener(r.eventName,this.boundOnVisibleElementChangedHandler)}udpdateFocusState(){this._component.useCustomInput?this.addFocusHiddenAttribute():this.removeFocusHiddenAttribute()}updateSelectedItemState(){this.setSelectedItem(this.searchIsAppliedAndReturnsNoneEmptyData&&!this._component.useCustomInput?0:-1,!0),this.notifyFocusedRowChanged()}onRenderContainerMutation(e,t){if(this._isActive)if(this.removeFocusMarkerClass(this.selectedItemElement),this._items=this.queryItems(),this._rows=this.queryRows(),this.updateViewportBoundaries(),this.updateBoundaries())this.udpdateFocusState(),this.updateSelectedItemState();else if(this.isElementInsideViewport(this._selectedVisibleIndex)){const e=this.findItemIndex(this._selectedVisibleIndex);e>-1&&(this.setSelectedItem(e,!0),this.notifyFocusedRowChanged())}}onVisibleElementChanged(e){if(e.detail.isFocusRequired){const t=h.getElementVisibleIndex(e.detail.element),s=h.isGroupRow(this,t),i=h.findItemIndex(s?t+1:t,this.items);this.setSelectedItem(i),this.notifyFocusedRowChanged()}}scrollToElement(e,t,s=this.selectedItemElement){const i=this._component.getScrollViewer();i&&s&&l.scrollToElementRelyOnStickyDescendants(s,e,t,i)}getLastAvailableVisibleIndex(){return this._lastAvailableVisibleIndex}getSelectedVisibleIndex(){return this._selectedVisibleIndex}getItemsContainer(){return this.itemsContainer}getRows(){return this._rows}}class H extends CustomEvent{constructor(e,t,s,i){super(H.eventName,{detail:new P(e,t,s,i),bubbles:!0,composed:!0,cancelable:!0})}}H.eventName=x+".keydown";class P{constructor(e,t,s,i){this.Key=e,this.AltKey=t,this.CtrlKey=s,this.ShiftKey=i}}class F extends CustomEvent{constructor(){super(F.eventName,{bubbles:!0,composed:!0,cancelable:!0})}}F.eventName=x+".dropdownclosed",S.register(H.eventName,(e=>e.detail)),S.register(F.eventName,(e=>e.detail));class M{}var O;M.AriaActiveDescendant="aria-activedescendant",function(e){e.Default="Default",e.None="None",e.AutoSearch="AutoSearch"}(O||(O={}));class k extends t{constructor(){super(),this._columnsInfo=[],this._focusedRowIndex=-1,this._elementHasCalculatedWidthAttribute="has-calculated-width",this.boundOnScrollViewerUpdateHandler=this.onScrollViewerUpdate.bind(this),this.boundOnMouseDownHandler=this.onRenderContainerMouseDown.bind(this),this.boundOnVisibleElementChangedHandler=this.onVisibleElementChanged.bind(this),this.boundOnPopupShownHandler=this.onPopupShown.bind(this),this.boundOnPopupClosedHandler=this.onPopupClosed.bind(this),this._tableHeaderCellSelector=`.${a.ContentContainerClassName} > table > thead > ${c.HeaderRowSelector} > th`,this._tableBodyCellSelector=`.${a.ContentContainerClassName} > table > tbody > ${c.TableVisibleRowGeneralSelector} > td`,this._hoverTitleElementsSelector=[this._tableBodyCellSelector,this._tableHeaderCellSelector].join(", "),this._kbdNavigationHelper=new R(this),this.dropDownWidthMode=s.ContentOrEditorWidth,this.searchMode=O.Default,this.useCustomInput=!1,this._pointerEventsHelper=new K(this)}connectedCallback(){super.connectedCallback(),this.prepareRenderContainer()}disconnectedCallback(){super.disconnectedCallback(),this.releaseRenderContainer()}ensurePopupElement(){super.ensurePopupElement(),this.popupElement&&(this.popupElement.addEventListener(w.eventName,this.boundOnPopupShownHandler),this.popupElement.addEventListener(y.eventName,this.boundOnPopupClosedHandler))}disposePopupElement(){this.popupElement&&(this.popupElement.removeEventListener(w.eventName,this.boundOnPopupShownHandler),this.popupElement.removeEventListener(y.eventName,this.boundOnPopupClosedHandler)),super.disposePopupElement()}createKeyboardStrategy(){return new i(this)}get renderContainer(){return this.popupDOMElement?this.popupDOMElement.querySelector(`.${u.RenderContainer}`):null}getScrollViewer(){return this.renderContainer?this.renderContainer.querySelector(`.${a.ClassName}`):null}getScrollViewerContent(){return this.renderContainer?this.renderContainer.querySelector(`.${a.ContentContainerClassName}`):null}getTable(){return this.renderContainer?this.renderContainer.querySelector(`.${a.ContentContainerClassName} > table`):null}getList(){return this.renderContainer?this.renderContainer.querySelector(c.ListSelector):null}notifyFocusedRowChanged(e){e!==this._focusedRowIndex&&(this.dispatchEvent(new p(e)),this._focusedRowIndex=e)}notifyMakeElementVisible(e,t){e<0||this.dispatchEvent(new b(e,t))}notifyColumnsChanged(e){this._columnsInfo=e}handleOpenDropDown(e){return!(!this.enabled||this.isReadOnly||this.isDropDownOpen||!e.altKey&&!e.metaKey)&&(this.openDropDown(),!0)}handleCloseDropDown(e){return!(!this.enabled||this.isReadOnly||!this.isDropDownOpen||!e.altKey&&!e.metaKey)&&(this.closeDropDown(),!0)}handleTryClearEditorValue(e){return!(!this.enabled||this.isReadOnly||!e.altKey)&&(this.dispatchClearEditorValue(),!0)}dispatchKeyDownEvent(e){const t=new H(e.key,e.altKey,e.ctrlKey||e.metaKey,e.shiftKey);this.dispatchEvent(t)}updateActiveDescendantAttributeValue(e){this.inputElement&&this.inputElement.hasAttribute(M.AriaActiveDescendant)&&this.inputElement.setAttribute(M.AriaActiveDescendant,e)}get isTableRender(){return!!this.getTable()}get enableVirtualRenderMode(){const e=this.getScrollViewer();return null!==e&&e instanceof _}get isSearchActive(){switch(this.searchMode){case O.None:case O.Default:return!1;default:return!0}}get searchIsApplied(){const e=this.fieldElement?this.fieldElementValue:this.fieldText;return this.isSearchActive&&(e?e.length:-1)>0}onTextInput(e){this.inputElement&&(this.ensureDropDownOpened(e),this._kbdNavigationHelper.updateState(),this.raiseFieldText())}raiseFocusOut(e){super.raiseFocusOut(e,this._kbdNavigationHelper.tabKeyPressed)}ensureDropDownOpened(e){!this.isDropDownOpen&&e.data&&e.data.length>0&&this.tryOpenDropDown()}processKeyDown(e){return!!this.tryProcessKeyDown(e)||super.processKeyDown(e)}processKeyUp(e){return!!this.tryProcessKeyUp(e)||super.processKeyUp(e)}tryProcessKeyDown(e){return this._kbdNavigationHelper.processKeyDown(e)}tryProcessKeyUp(e){return this._kbdNavigationHelper.processKeyUp(e)}processCapturedKeyDownAsync(e,t){return this._kbdNavigationHelper.processCapturedKeyDown(e)?(e.preventDefault(),t.handled=!0,Promise.resolve()):super.processCapturedKeyDownAsync(e,t)}onPopupShown(e){setTimeout((()=>{this.prepareRenderContainer(),this._kbdNavigationHelper.onPopupShown()}))}onPopupClosed(e){setTimeout((()=>{this._kbdNavigationHelper.onPopupClosed(),this.releaseRenderContainer()}))}prepareRenderContainer(){this.renderContainer&&(this.renderContainer.addEventListener(d.eventName,this.boundOnScrollViewerUpdateHandler),I.Browser.Firefox&&this.isTableRender&&this.renderContainer.addEventListener("mousedown",this.boundOnMouseDownHandler),this.renderContainer.addEventListener(r.eventName,this.boundOnVisibleElementChangedHandler),this.resizeDropDownElement(),this._pointerEventsHelper.initialize())}onVisibleElementChanged(e){this.resizeDropDownElement()}resizeDropDownElementCore(){const e=this.getTable();if(e){const t=Array.from(e.querySelectorAll(":scope > colgroup > col")),s=[],i=[];if(t.forEach((e=>{(e.style.width.indexOf("%")>0||e.hasAttribute(this._elementHasCalculatedWidthAttribute))&&(e.style.removeProperty("width"),e.removeAttribute(this._elementHasCalculatedWidthAttribute)),e.style.width?s.push(e):i.push(e)})),i.length<1)return;const n=s.reduce(((e,t)=>e+t.getBoundingClientRect().width),0),o=e.style.width;e.style.width="auto";const r=i.reduce(((e,t)=>e+t.getBoundingClientRect().width),0),l=this.dropDownElement.minDesiredWidth?C.DomUtils.pxToInt(this.dropDownElement.minDesiredWidth):0;if(this.needAdjustNarrowContent(n,r,l)){if(i.length>1){const e=(l-n)/r;for(let t=0;t<i.length-1;t++){const s=i[t];s.style.width=g.toPx(Math.ceil(s.getBoundingClientRect().width*e)),s.setAttribute(this._elementHasCalculatedWidthAttribute,"")}}}else i.forEach((e=>{e.style.width=g.toPx(Math.ceil(e.getBoundingClientRect().width)),e.setAttribute(this._elementHasCalculatedWidthAttribute,"")}));e.style.width=o}else{const e=this.getList(),t=window.innerWidth,s=this.getScrollViewerContent(),i=this.popupDOMElement&&s?this.popupDOMElement.getBoundingClientRect().width-s.getBoundingClientRect().width:0,n=Math.ceil(e.getBoundingClientRect().width+i);this.dropDownElement.desiredWidth=g.toPx(Math.min(n,t))}}needAdjustNarrowContent(e,t,i){if(i){return this.dropDownWidthMode===s.ContentOrEditorWidth&&e+t<i}return!1}resizeDropDownElement(){this.dropDownElement&&this.isDropDownWidthDependsOnContent&&this.resizeDropDownElementCore()}releaseRenderContainer(){this.renderContainer&&(this.renderContainer.removeEventListener(d.eventName,this.boundOnScrollViewerUpdateHandler),this.renderContainer.removeEventListener(r.eventName,this.boundOnVisibleElementChangedHandler),I.Browser.Firefox&&this.isTableRender&&this.renderContainer.removeEventListener("mousedown",this.boundOnMouseDownHandler),this._pointerEventsHelper.dispose()),this.isDropDownWidthDependsOnContent&&this.dropDownElement&&(this.dropDownElement.desiredWidth=null)}onSizeChanged(e,t){if(!this.dropDownElement)return;const s=this.getScrollViewer();s&&s.refreshUI&&s.refreshUI(),super.onSizeChanged(e,t)}onRenderContainerMouseDown(e){e.ctrlKey&&e.preventDefault()}onScrollViewerUpdate(e){const t=this.getScrollViewerContent(),s=this.getTable();t&&s&&(s.offsetHeight<t.clientHeight?C.DomUtils.addClassName(s,u.TableNoScrollClassName):C.DomUtils.removeClassName(s,u.TableNoScrollClassName)),e.stopPropagation()}shouldHandleKeyDown(e){return this._kbdNavigationHelper.shouldHandleKeyDown(e)}get isDropDownWidthDependsOnContent(){return this.dropDownWidthMode===s.ContentWidth||this.dropDownWidthMode===s.ContentOrEditorWidth}get handlePointerEventsMode(){return A.None}get handlePointerEventsTarget(){var e;return null!==(e=this.getTable())&&void 0!==e?e:this}get handlePointerEventsDelay(){return N}get hoverTitleElementsSelector(){return this._hoverTitleElementsSelector}get bypassNonInlineHoverTitleElementChildSelector(){return null}shouldProcessPointerEvent(e){return!0}}e([T({type:O,attribute:"search-mode"})],k.prototype,"searchMode",void 0),e([T({type:Boolean,attribute:"use-custom-input"})],k.prototype,"useCustomInput",void 0);export{k as D,O as L};
