import{C as e}from"./custom-events-helper-e7f279d3.js";const s="dxbl-client-component-wrapper";class t extends CustomEvent{constructor(e){super(e,{detail:new o,bubbles:!0,composed:!0,cancelable:!0})}}class n extends CustomEvent{constructor(e){super(n.eventName,{detail:new c(e),bubbles:!0,composed:!0,cancelable:!0})}}n.eventName=`${s}.options-loaded`;class a extends CustomEvent{constructor(e){super(a.eventName,{detail:new l(e),bubbles:!0,composed:!0,cancelable:!0})}}a.eventName=`${s}.loading-panel-visibility-changed`;class o{}class c{constructor(e){this.OptionChunkKeys=e}}class l{constructor(e){this.Visible=e}}e.register(n.eventName,(e=>e.detail)),e.register(a.eventName,(e=>e.detail));export{t as C,a,n as b};
