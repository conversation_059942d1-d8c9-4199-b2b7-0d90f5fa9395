import{_ as t}from"./tslib.es6-d65164b3.js";import{d as e}from"./dom-554d0cc7.js";import{I as i,b as s,u as o,w as r,J as a,j as l,i as n,R as d,e as h}from"./dom-utils-d057dcaa.js";import{d as c}from"./disposable-d2c2d283.js";import{b as u}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{D as m}from"./dx-dropdown-owner-8d389864.js";import{T as p}from"./toolbar-css-classes-d303c118.js";import{S as y}from"./observables-589a5615.js";import{B as b}from"./constants-3209ffde.js";import{G as g}from"./const-90026e45.js";import{i as f,j as v}from"./constants-da6cacac.js";import{S as k}from"./single-slot-element-base-01d93921.js";import{b as I,c as L,d as x,e as C,a as T}from"./events-5ceb0642.js";import{D as w}from"./keyboard-navigation-strategy-ea41c807.js";import{D as S}from"./root-keyboard-strategy-d1e519a3.js";import{R as M}from"./constants-ed9663d1.js";import{t as B}from"./state-c294470d.js";import{n as R}from"./property-4ec0b52d.js";import{e as W}from"./custom-element-267f9a21.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./eventhelper-8bcec49f.js";import"./constants-7c047c0d.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./data-qa-utils-8be7c726.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./portal-b3727c25.js";import"./constants-a4904a3f.js";import"./popup-355ecaa4.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./custom-events-helper-e7f279d3.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./key-ffa272aa.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./menu-keyboard-strategy-7c257fd3.js";import"./constants-ed56e953.js";import"./modal-keyboard-strategy-05da34d9.js";import"./dropdown-menu-keyboard-strategy-60d36909.js";class E{constructor(t,e){this.toolbarModel=t,this.id=e,this.state="",this._isWidthCalculationLocked=!1,this.index=-1,this.adaptivePriority=0,this._width=0}getWidth(){return this.isVisible()?Et(this.getElement(),!1,Ct):0}getNoTextWidth(){return this.getWidth()}isVisible(){return!0}get isWidthCalculationLocked(){return this._isWidthCalculationLocked}updateState(t){this.state=t,this.updateStateCore(t)}updateStateCore(t){}lockWidthCalculation(){this._isWidthCalculationLocked=!0}unlockWidthCalculation(){this._isWidthCalculationLocked=!1}getGroupElement(){return null}reset(){}}class N extends E{constructor(t,e){var i,s,o,r,a;super(t,null!==(o=null!==(s=null===(i=e.element)||void 0===i?void 0:i.id)&&void 0!==s?s:e.id)&&void 0!==o?o:"-1"),this.item=e,this.adaptivePriority=null!==(a=null===(r=null==e?void 0:e.adaptivePriority)||void 0===r?void 0:r.value)&&void 0!==a?a:0}getElement(){return this.item.getElement()}updateStateCore(t){this.item.isDisplayed.update(t.indexOf("has-"+this.getName())>-1)}updateVisible(t){t||this.lockWidthCalculation(),this.item.updateVisible(t),t&&this.unlockWidthCalculation()}reset(){const t=this.getElement();t&&t.classList.remove(p.ToolbarHiddenItem)}}class P extends N{constructor(t,e){super(t,e)}getNoTextWidth(){return this.item.isDisplayed.value?function(t){let e=Et(t,!1,Ct);if(0===e)return e;if(t){if(t.querySelector(Bt))return e;const i=t.querySelector(St);if(i){const s=t.querySelector(Mt);e-=Pt(i);let o=i;for(;o=o.nextElementSibling;)!Vt(o)&&At(o)&&o!==s&&(e-=Et(o))}}return e}(this.getElement()):0}getName(){return"item"}isVisible(){return this.item.isDisplayed.value}updateStateCore(t){const e=this.item.toolbar,i=-1===t.indexOf(p.ToolbarHasEllipsis);this.updateVisible(e.minRootItems>0?i||this.index<e.minRootItems:i)}getGroupElement(){return this.item.getGroupElement()}}class V extends N{constructor(t,e){super(t,e)}getName(){return"ellipsis"}updateStateCore(t){super.updateStateCore(t),this.updateVisible(this.item.isDisplayed.value)}reset(){this.updateVisible(!0)}}class A extends P{constructor(t,e){super(t,e),this.itemBlocks=[],this.addItem(e)}addItem(t){this.itemBlocks.push(new P(this.toolbarModel,t))}isVisible(){return this.itemBlocks.reduce((function(t,e){return t&&e.isVisible()}),!0)}getWidth(){return this.itemBlocks.reduce((function(t,e){return t+e.getWidth()}),0)}getNoTextWidth(){return this.itemBlocks.reduce((function(t,e){return t+e.getNoTextWidth()}),0)}updateVisible(t){this.itemBlocks.forEach((function(e){e.updateVisible(t)}))}tryAddItem(t){const e=this.itemBlocks[this.itemBlocks.length-1].item,i=e.groupName===t.groupName&&e.group===t.group&&e.adaptivePriority.value===t.adaptivePriority.value;return i&&this.addItem(t),i}}class H extends E{constructor(t,e,i){super(t,i),this.item=e}getName(){return"title"}getWidth(){return Et(this.getElement(),!1,Ct)}getElement(){return this.item.getElement()}}class j{constructor(t){this.items=[],this.isLocked=!1,this.subscriptions=[],t&&this.addRange(t)}subscribe(t){this.isLocked||t(this.getChanges(this.items,[])),this.subscriptions.push(t)}getChanges(t,e){return{addedItems:t||[],removedItems:e||[]}}forEach(t,e){this.items.forEach(t,e)}selectMany(t,e){return this.map(t,e).reduce((function(t,e){return t.concat(e)}),[])}reduce(t,e){return this.items.reduce(t,e)}map(t,e){return this.items.map(t,e)}filter(t,e){return this.items.filter(t,e)}any(){return this.count()>0}count(){return this.items.length}get(t){return this.items[t]}add(t){this.addItemCore(t)&&this.raiseChanges([t],[])}remove(t){this.removeItemCore(t)&&this.raiseChanges([],[t])}addRange(t){this.raiseChanges(t.map(this.addItemCore.bind(this)).filter((function(t){return!!t})),[])}removeRange(t){this.raiseChanges([],t.map(this.removeItemCore.bind(this)).filter((function(t){return!!t})))}addItemCore(t){return this.items.indexOf(t)>-1?null:this.items[this.items.length]=t}removeItemCore(t){const e=this.items.indexOf(t);return-1===e?null:this.items.splice(e,1)[0]}raiseChanges(t,e){const i=this.getChanges(t,e);(i.addedItems.length>0||i.removedItems.length>0)&&this.subscriptions.forEach((function(t){t(i)}))}lock(){this.isLocked=!0}unlock(){this.isLocked=!1}}class D{constructor(t){this.id=t,this._margins=void 0,this.blocks=[]}push(t){this.blocks.includes(t)||this.blocks.push(t),this.update()}toArray(){return Array.from(this.blocks)}get margins(){return this._margins||0}update(){if(!this._margins&&this.blocks.length){const t=this.blocks[0].getGroupElement();if(t){const i=e.DomUtils.getCurrentStyle(t);this._margins=e.DomUtils.pxToInt(i.marginRight)+e.DomUtils.pxToInt(i.marginLeft)}}}}class U{constructor(t,e,i){var s;this.layer=t,this.block=e,this.width=0,this._blockCollection=void 0;const o=e.getGroupElement();o&&(this._blockCollection=this.layer.getBlockCollection(o),null===(s=this._blockCollection)||void 0===s||s.push(e)),i.subscribe(e,((t,i=!1)=>{var s;null===t&&(t=null===(s=this.layer.getPrevLayer())||void 0===s?void 0:s.getActualBlocks().filter((function(t){return t.block===e}))[0].width),t!==this.width&&(this.width=null!=t?t:0,i||this.layer.requestUpdateLayoutModel())}))}get blockCollection(){return this._blockCollection||new D(null)}getMinWidth(){return this.width}getMaxWidth(t){return this.getMinWidth()}}class q extends U{getPrevLayerMinWidth(){const t=this.layer.getPrevLayer();return t?t.getActualBlocks().filter((t=>t.block===this.block))[0].getMinWidth():0}getMaxWidth(t){return t===this.layer?this.getPrevLayerMinWidth():this.getMinWidth()}}class z extends U{}class O extends U{}class _{constructor(t,e){this.widthCalculator=t,this.triggersResolver=e,this.widthSubscriber=null,this.widthCalculator=t,this.triggersResolver=e}subscribe(t,e){this.widthSubscriber=e;const s=this.widthCalculator.bind(this);this.triggersResolver(t).forEach((function(o){o.subscribe((function(){t.isWidthCalculationLocked||i((function(){e(s(t),!1)}))}),!0)})),i((function(){e(s(t),!0)}))}updateWidth(t){const e=this.widthSubscriber,s=this.widthCalculator.bind(this);i((function(){e(s(t),!0)}))}}let F;F={fullWidthItem:new _((function(t){return t.getWidth()}),Nt),fullWidthSystemItem:new _((function(t){return t.getWidth()}),(function(t){return[]})),titleItem:new _((function(t){return t.getWidth()}),(function(t){var e,i;return[null===(e=t.toolbarModel.title)||void 0===e?void 0:e.hasTitle,null===(i=t.toolbarModel.title)||void 0===i?void 0:i.text]})),noTextItem:new _((function(t){return t.getNoTextWidth()}),Nt),contextItem:function(t){return new _((function(e){return t.items.filter((function(t){return"item"===t.getName()&&t.index<e.index&&t.isVisible()})).length>=e.toolbarModel.minRootItems||!e.isVisible()?0:null}),Nt)},hiddenItem:new _((function(){return 0}),(function(){return[]}))};class G{getMin(t){return t.map((t=>t.minSize)).reduce(((t,e)=>t+e))}getMax(t){return t.map((t=>t.fullSize)).reduce(((t,e)=>t+e))}}class $ extends G{getRange(t,e,i){return{min:this.getMin(t)+e,max:this.getMax(t)+e}}}class K extends G{getRange(t,e,i){return{min:this.getMin(t),max:this.getMax(t)+e}}}class J extends G{constructor(){super(...arguments),this.prevLayerRangeCalculator=new K}getRange(t,e,i=[]){return{min:this.getMin(t)+e,max:this.getMax(i)+e-1}}}class X{constructor(t,e,i,s){this.name=t,this.canApply=e,this.blocks=i,this.margin=s}orderBlock(t,e){const i=this.blocks.find((e=>e.id===t));if(!i)throw new Error(`No block with id ${t} in layer with type: ${this.type}`);i.index=e,this.blocks.sort(((t,e)=>it(t,e)))}getBlock(t){const e=this.blocks.find((e=>e.id===t));if(!e)throw new Error(`No block with id ${t} in layer with type: ${this.type}`);return e}}class Q extends X{constructor(){super(...arguments),this.type=ot.Default}updateBlock(t,e){const i=this.getBlock(t.id);if(0===t.width)return!1;const s=i.minWidth!==t.width||i.maxWidth!==t.width;return i.minWidth=t.width,i.maxWidth=t.width,s}}class Y extends X{constructor(){super(...arguments),this.type=ot.FullSequential}updateBlock(t,e){const i=this.getBlock(t.id);let s=i.maxWidth!==t.width;return i.maxWidth=t.width,i.minWidth&&(s=s||i.minWidth!==t.width,i.minWidth=t.width),e||(s=s||0!==i.minWidth,i.minWidth=0),s}}class Z extends X{constructor(){super(...arguments),this.type=ot.NoText}updateBlock(t,e){const i=this.getBlock(t.id);if(0===t.width)return!1;const s=i.minWidth!==t.width||i.maxWidth!==t.width;return i.minWidth=t.width,i.maxWidth=t.width,s}}class tt extends X{constructor(){super(...arguments),this.type=ot.NoTextSequential}updateBlock(t,e){const i=this.getBlock(t.id);let s=i.maxWidth!==t.width;return i.maxWidth=t.width,i.minWidth&&(s=s||i.minWidth!==t.width,i.minWidth=t.width),e||(s=s||0!==i.minWidth,i.minWidth=0),s}}class et{constructor(){this.layers=[],this.fullLayerTypes=[ot.Default,ot.FullSequential],this.noTextLayerTypes=[ot.NoText,ot.NoTextSequential]}get isEmpty(){if(0===this.layers.length)return!0;return 0===this.layers.find((t=>t.type===ot.Default)).blocks.filter((t=>!t.isEllipse&&!t.isTitle&&0!==t.maxWidth)).length}get blockIds(){const t=this.layers.find((t=>t.type===ot.Default));return t&&0!==t.blocks.length?t.blocks.filter((t=>!t.isEllipse)).map((t=>t.id)):[]}resetLayers(t){this.layers=this.convertLayers(t).map(this.toLayer)}initLayers(t){return!!this.isEmpty&&(this.resetLayers(t),!0)}convertLayers(t){return t.map((t=>{const e=t.getActualBlocks();return{name:t.stateName,canApply:t.canApply,type:t.type,blocks:jt(e,t).sort(((t,e)=>it(t,e))),margin:(i=e,[...new Set(i.map((t=>t.blockCollection)))].map((t=>t.margins)).reduce(((t,e)=>t+e),0))};var i}))}updateFullSizeBlock(t,e){return this.updateBlock(t,e,this.fullLayerTypes)}updateNoTextBlock(t,e){return this.updateBlock(t,e,this.noTextLayerTypes)}updateBlock(t,e,i){let s=!1;for(const o of this.layers.filter((t=>i.includes(t.type))))s=o.updateBlock(t,e)||s;return s}orderBlock(t,e){for(const i of this.layers)i.orderBlock(t,e)}updateBlockByLayer(t,e,i){return this.layers.find((t=>t.type===i)).updateBlock(t,e)}getItemsByName(t){const e=this.layers.find((e=>e.name===t));return this.getBlocks(e)}getItemsByType(t){const e=this.layers.find((e=>e.type===t));return this.getBlocks(e)}getBlocks(t){return t.blocks.filter((t=>-1!==t.index))}getBlock(t,e){const i=this.layers.find((e=>e.name===t));if(i)return i.blocks.find((t=>t.index===e))}getBlockInEveryLayer(t){return this.layers.map((t=>t.blocks)).flat().filter((e=>e.id===t))}toLayer(t){const{name:e,canApply:i,type:s,blocks:o,margin:r}={...t};switch(s){case ot.Default:return new Q(e,i,o,r);case ot.FullSequential:return new Y(e,i,o,r);case ot.NoText:return new Z(e,i,o,r);case ot.NoTextSequential:return new tt(e,i,o,r)}}}function it(t,e){return t.isTitle||e.isTitle?t.index-e.index:t.isEllipse||e.isEllipse?e.index-t.index:t.adaptivePriority===e.adaptivePriority||e.isEllipse?t.index>e.index?1:-1:t.adaptivePriority-e.adaptivePriority}class st{constructor(){this._layers=[],this.checkpointsByLayers=new Map}get layers(){return this._layers.filter((t=>t.canApply))}initialize(t){var e;this._layers=t,this.checkpointsByLayers.clear();let i=0;for(const t of this.layers){const s=this.getPoints(t,i).filter((t=>0!==t)),o=s.filter((t=>this.getCheckpoints().indexOf(t)<0));this.checkpointsByLayers.set(t.type,o),i=null!==(e=s[s.length-1])&&void 0!==e?e:0}}getLayerTypeByWidth(t){for(const e of this.layers.map((t=>t.type))){if(this.checkpointsByLayers.get(e).indexOf(t)>-1)return e}return ot.Default}getCheckpointPair(t){const e=this.getCheckpoints();let i=0,s=0;for(const o of e){if(o<t){s=o;break}i=o}return[i,s]}getCheckpoints(){const t=[...this.checkpointsByLayers.values()].flat();return[...new Set(t.sort(((t,e)=>t<e?1:-1)))]}getPoints(t,e=0){switch(t.type){case ot.Default:return this.getConstantLayerPoints(t);case ot.FullSequential:return this.getSeqPoints(t,!0,e);case ot.NoText:return this.getConstantLayerPoints(t);case ot.NoTextSequential:return this.getSeqPoints(t,!0,e)}return[]}getConstantLayerPoints(t){return[t.blocks.map((t=>t.fullSize)).reduce(((t,e)=>t+e),0)]}getSeqPoints(t,e,i){const s=[];let o=t.blocks.map((t=>t.fullSize)).reduce(((t,e)=>t+e),0);for(const e of t.blocks.slice().reverse()){const t=o-(e.fullSize-e.minWidth);t!==o&&e.index>-1&&(!i||t<i)&&s.push(t),o=t}return s}}var ot,rt,at;!function(t){t[t.Default=0]="Default",t[t.FullSequential=1]="FullSequential",t[t.NoText=2]="NoText",t[t.NoTextSequential=3]="NoTextSequential"}(ot||(ot={}));class lt{constructor(t){this.onLayerApplied=t,this.layers=[],this.blockCollection=null,this.currentLayer=null,this.currentWidth=null,this.currentHeight=null,this._defaultLayer=null,this._sequentialLayer=null,this.minWidth=null,this.virtualLayout=new et,this.layerCheckpointService=new st,this.layersCheckpoints=[],this.prevPoint=0,this.nextPoint=0,this.toolbarWidthUpdate=()=>{},this.toolbarHeightUpdate=()=>{},this.toolbarIsVisible=()=>!0}initialize(t,e,i){this.blockCollection=t,this.elementContentWidthSubscription=s(e,(t=>{var e;this.currentWidth===t.width&&this.currentHeight===t.height||(null===this.currentWidth&&(null===(e=this.blockCollection)||void 0===e||e.subscribe((t=>{t.addedItems.forEach(this.addItemBlock.bind(this)),t.removedItems.forEach(this.removeItemBlock.bind(this))}))),this.getCheckpoints(),this.currentWidth=t.width,this.currentHeight&&this.currentHeight===t.height||(this.currentHeight=t.height,this.toolbarHeightUpdate(this.currentHeight)),this.minWidth||this.setMinWidth(),this.currentLayer&&!this.checkForLayoutUpdate()||(this.virtualLayout.isEmpty&&(this.virtualLayout.resetLayers(this.layers),this.getCheckpoints()),this.setCheckpoints(),this.setLayout()),i&&i(t))}))}addBlockToCollection(t){var e;null===(e=this.blockCollection)||void 0===e||e.add(t)}removeBlockToCollection(t){var e;null===(e=this.blockCollection)||void 0===e||e.remove(t)}dispose(){this.elementContentWidthSubscription&&o(this.elementContentWidthSubscription)}getLastLayer(){return this.layers[this.layers.length-1]||null}setDefaultLayer(t,e){const i=this._defaultLayer=new dt("default",t,this,e);this.layers.push(i)}setSimultaneousTransitionLayer(t,e,i){this.layers.push(new ct(t,ot.NoText,e,this.getLastLayer(),i))}setSequentialTransitionLayer(t,e,i,s){const o=this._sequentialLayer=new ht(t,e,i,this.getLastLayer(),s);this.layers.push(o)}addItemBlock(t){this.layers.forEach((function(e){e.addBlock(t)}))}removeItemBlock(t){this.layers.forEach((function(e){e.removeBlock(t)}))}removeItemBlocks(t){for(const e of t)this.removeItemBlock(e)}checkForLayoutUpdate(){const t=this.currentWidth;return this.nextPoint>0&&t<this.nextPoint||this.prevPoint>0&&t>this.prevPoint}updateFromVirtualLayer(t){const e=this.layers.find((e=>e.type===t.type));e.canApply=t.canApply;for(const i of e.layoutBlocks){const e=t.blocks.find((t=>t.id===i.block.id));e&&(i.width=e.minWidth)}}orderFromVirtualLayer(t){const e=this.layers.find((e=>e.type===t.type)),i=e.layoutBlocks,s=t.blocks;for(const i of e.layoutBlocks){const e=t.blocks.find((t=>t.id===i.block.id));e&&(i.block.index=e.index)}e.layoutBlocks=[...i].sort(((t,e)=>s.indexOf(s.find((e=>e.id===t.block.id)))-s.indexOf(s.find((t=>t.id===e.block.id)))))}recalculateLayout(){this.setLayout()}setCheckpoints(){[this.prevPoint,this.nextPoint]=this.layerCheckpointService.getCheckpointPair(this.currentWidth)}updateCheckpoints(t){const[e,i]=[this.prevPoint,this.nextPoint];this.resetCheckpoints();const s=this.currentWidth;t&&(e!==this.prevPoint&&s<this.prevPoint||i!==this.nextPoint&&s>this.nextPoint)&&this.setLayout()}resetCheckpoints(){this.virtualLayout.resetLayers(this.layers),this.layerCheckpointService.initialize(this.virtualLayout.layers),this.layersCheckpoints=this.layerCheckpointService.getCheckpoints(),this.setCheckpoints(),this.setMinWidth()}setLayout(){const t=this.layerCheckpointService.getLayerTypeByWidth(this.nextPoint>0&&this.currentWidth>this.nextPoint?this.nextPoint:this.prevPoint),e=this.layers.find((e=>e.type===t));this.virtualLayout.resetLayers(this.layers),this.applyLayer(e)}setMinWidth(){const t=this.getCheckpoints();this.minWidth=t[t.length-1],this.toolbarWidthUpdate()}updateLayout(){const t=this.findLayersForWidth(this.currentWidth)[0];t&&(this.applyLayer(t),this.minWidth=Math.min(...this.layers.filter((t=>t.latestRange)).map((t=>t.latestRange.min))))}getCheckpoints(){return this.layersCheckpoints.length||this.virtualLayout.isEmpty||!this.toolbarIsVisible()||this.resetCheckpoints(),this.layersCheckpoints}resetToDefault(){this.applyLayer(this.layers[0],!0)}applyLayer(t,e=!1){if(this.currentLayer=t,e)t.activateAndReset(this.currentWidth);else{this.virtualLayout.initLayers(this.layers);const e=this.virtualLayout.layers.find((t=>t.type===this.currentLayer.type));t.activate(this.currentWidth,e.blocks)}this.onLayerApplied&&this.onLayerApplied(t)}findLayersForWidth(t){return this.layers.filter((function(e){return e.isValidWidth(t)}))}forceUpdate(){null!==this.currentWidth&&this.updateLayout()}getMinWidth(){return this.minWidth?this.minWidth:this._sequentialLayer?this._sequentialLayer.getActualBlocks().map((t=>t.getMinWidth())).reduce(((t,e)=>t+e),0):null}}class nt{constructor(t,e,i,s,o){this.stateName=t,this.type=e,this.blockUpdaterGetter=i,this.prevLayer=s,this.canApply=o,this.nextLayer=null,this.layoutBlocks=[],this.nextLayer=null,s&&(s.nextLayer=this)}getNextLayer(){return null!=this.nextLayer?this.nextLayer.canApply?this.nextLayer:this.nextLayer.getNextLayer():null}getPrevLayer(){return null!=this.prevLayer?this.prevLayer.canApply?this.prevLayer:this.prevLayer.getPrevLayer():null}requestUpdateLayoutModel(){var t;null===(t=this.getPrevLayer())||void 0===t||t.requestUpdateLayoutModel()}getBlockCollection(t){var e;return null===(e=this.getPrevLayer())||void 0===e?void 0:e.getBlockCollection(t)}isValidWidth(t){if(!this.canApply||!t)return!1;const e=this.getRangeNew();return!this.getNextLayer()&&e.min>t||!this.getPrevLayer()&&e.max<t||e.min<=t&&e.max>=t}getRange(){const t=new Set,e=this.latestRange=this.getActualBlocks().reduce(((e,i)=>(t.add(i.blockCollection),{min:e.min+i.getMinWidth(),max:e.max+i.getMaxWidth(this)})),{min:0,max:0});return t.forEach((t=>{e.min+=t.margins,e.max+=t.margins})),e}getRangeNew(){const t=jt(this.getActualBlocks(),this),e=this.type===ot.NoText&&this.prevLayer?jt(this.prevLayer.getActualBlocks(),this.prevLayer):[];return this.latestRange=this.rangeCalculator.getRange(t,0,e)}getActualBlocks(){const t=this.getPrevLayer();return t?t.getActualBlocks().map((t=>this.layoutBlocks.filter((function(e){return e.block===t.block}))[0]||t)):this.layoutBlocks}activate(t,e){t&&this.layoutBlocks.forEach((t=>{t.block.updateState(this.stateName)}))}addBlock(t){const e=this.blockUpdaterGetter(t);if(e){const i=this.createBlock(t,e);this.layoutBlocks.push(i)}}removeBlock(t){const e=this.layoutBlocks.find((e=>e.block.id===t));if(e){const t=this.layoutBlocks.indexOf(e);this.layoutBlocks.splice(t,1)}}activateAndReset(t){}}class dt extends nt{constructor(t,e,i,s){super(t,ot.Default,e,null,s),this.layoutModel=i,this.rangeCalculator=new $,this.blockCollection=new Map}requestUpdateLayoutModel(){this.layoutModel.updateLayout()}createBlock(t,e){return new O(this,t,e)}activateAndReset(t){this.activate(t,[]),this.layoutBlocks.forEach((t=>{t.block.reset()}))}getBlockCollection(t){return this.getBlockCollectionCore(t)}getBlockCollectionCore(t){let e=this.blockCollection.get(t);return e||this.blockCollection.set(t,e=new D(t)),e}}class ht extends nt{constructor(){super(...arguments),this.rangeCalculator=new K}activate(t,e){const i=this.getActualBlocks();this.getRangeNew();let s=this.latestRange.max,o=this.stateName;const r=new Map;for(let a=e.length-1;a>=0;a--){const l=e[a],n=i.find((t=>t.block.id===l.id));if(s>t){s-=l.fullSize-l.minSize;const e=n.blockCollection.id,i=(r.has(e)?r:r.set(e,n.blockCollection.toArray())).get(e);if(i&&i.splice(i.indexOf(n.block),1),s<=t){n.block.updateState(o);const t=this.getPrevLayer();o=t?t.stateName:"";continue}}n.block.updateState(o)}}getRange(){const t=new Set;return this.latestRange=this.getActualBlocks().reduce(((e,i)=>(t.add(i.blockCollection),{min:e.min+i.getMinWidth(),max:e.max+i.getMaxWidth(this)})),{min:0,max:0}),t.forEach((t=>{this.latestRange.max+=t.margins})),this.latestRange}createBlock(t,e){return new q(this,t,e)}}class ct extends nt{constructor(){super(...arguments),this.rangeCalculator=new J}createBlock(t,e){return new z(this,t,e)}getRange(){const t=new Set;let e=this.getActualBlocks().reduce((function(e,i){return t.add(i.blockCollection),e+i.getMinWidth()}),0);t.forEach((t=>{e+=t.margins}));const i=this.getPrevLayerRangeMax();return this.latestRange={min:e,max:i}}getPrevLayerRangeMax(){const t=this.getPrevLayer();return t?t.getRangeNew().max-1:0}}!function(t){t[t.Contained=0]="Contained",t[t.Plain=1]="Plain"}(rt||(rt={})),function(t){t[t.Default=0]="Default",t[t.Large=1]="Large"}(at||(at={}));class ut{constructor(t,e){this.element=t,this.toolbar=e,this.index=new y(-1),this.isVisible=new y(this.defaultIsVisible()),this.isVisible.subscribe((t=>{this.onIsVisibleChanged(t)}),!0)}defaultIsVisible(){return!0}getElement(){return this.element}updateVisible(t){this.isVisible.update(t)}onIsVisibleChanged(t){const e=this.getElement();e&&(t?this.show(e):this.hide(e))}show(t){t.classList.remove(p.ToolbarHiddenItem)}hide(t){t.classList.add(p.ToolbarHiddenItem)}}class mt extends ut{constructor(){super(...arguments),this.isDisplayed=new y(!0),this.text=new y(""),this.iconCssClass=new y("")}show(t){t.style.display=""}hide(t){t.style.display="none"}getGroupElement(){return null}}class pt extends mt{constructor(t,e,i){super(t,e),this.group=i,this.groupName=new y(""),this.adaptivePriority=new y(-1)}onIsVisibleChanged(t){super.onIsVisibleChanged(t),this.group.onItemVisibleChanged()}getElement(){return document.querySelector(`#${this.id}`)}getParent(){const t=this.getElement();return t&&t.parentElement?t.parentElement:null}getGroupElement(){return this.group.getElement()}}class yt extends mt{constructor(t,e){super(t,e)}defaultIsVisible(){return null}}class bt extends ut{constructor(t,e){super(t,e),this.items=[]}addItem(t){this.items.push(t)}onItemVisibleChanged(){if(0===this.items.length)return;const t=this.items.some((t=>t.isVisible.value));this.updateVisible(t)}getElement(){if(0===this.items.length)return null;const t=r(this.items[0].getElement(),Ht(Tt,this.toolbar.id));return t||this.items[0].getParent()}}class gt extends ut{constructor(t,e){super(null,t),this.elementSelector=e,this.hasTitle=new y(!1),this.text=new y(""),this.text.subscribe((t=>{this.hasTitle.update(null!=t&&""!==t)}))}getElement(){return document.querySelector(this.elementSelector)}}class ft{constructor(t,e){this.element=t,this.id=e,this.items=[],this.itemMap=new Map,this.groupMap=new Map,this.canHideRootItems=!1,this.canCollapseToIcons=!1,this.minRootItems=0}}const vt=[f,v].map((t=>t.toUpperCase())),kt=["DXBL-POPUP-PORTAL"];class It{constructor(t){this.dataToolbarId=t,this.virtualElementCreator={[p.ToolbarGroup]:this.createVirtualGroup,[p.ToolbarItem]:this.createVirtualRootItem,[p.ToolbarPlaceholder]:this.createVirtualPlaceholder,[p.ButtonEllipsis]:this.createVirtualRootItem},this.toolbar=document.querySelector(`[${Rt}='${this.dataToolbarId}']`),this.buttonToolbar=this.toolbar.querySelector(`.${p.ButtonToolbar}`),this.virtualButtonToolbar=document.createElement("div"),this.copyToolbar(),this.toolbar.appendChild(this.virtualButtonToolbar)}copyToolbar(){this.virtualButtonToolbar.classList.remove(...this.virtualButtonToolbar.classList),this.virtualButtonToolbar.classList.add(p.VirtualToolbar),this.virtualButtonToolbar.classList.add(...this.buttonToolbar.classList);const t=[...this.buttonToolbar.children],e=[];for(const i of t){const t=[...i.classList].find((t=>null!=this.virtualElementCreator[t]));e.push(this.virtualElementCreator[t].bind(this,i)())}this.numberToolbarGroups(e.filter((t=>t.classList.contains(p.ToolbarGroup)))),this.virtualButtonToolbar.innerHTML="",e.forEach((t=>this.virtualButtonToolbar.appendChild(t)))}numberToolbarGroups(t){for(let e=0;e<t.length;e++)0===e&&t[e].classList.add(b.ButtonGroupFirst)}getElementWidth(t){this.buttonToolbar.style.width=this.buttonToolbar.offsetWidth+"px";const i=this.virtualButtonToolbar.querySelector(`[virtual-id=${t}]`),s=i.parentElement;let o=0,r=0;const a=[...s.children].find((t=>t.classList.contains(p.ToolbarGroupSeparator)));let l=0;if(null!=i.parentElement&&i.parentElement.classList.contains(p.ToolbarGroup)){const t=e.DomUtils.getCurrentStyle(i.parentElement);if(i.parentElement.lastElementChild===i&&(r=e.DomUtils.pxToInt(t.marginRight)),i.parentElement.firstElementChild===i&&(o=e.DomUtils.pxToInt(t.marginLeft)),s.classList.contains(b.ButtonGroupFirst)&&s.children[1]===i)o=e.DomUtils.pxToInt(t.marginLeft);else if(s.firstElementChild===a&&a.nextElementSibling===i){const t=a;if(t){const i=e.DomUtils.getCurrentStyle(s),o=e.DomUtils.getCurrentStyle(t);l+=e.DomUtils.pxToInt(i.marginLeft)+t.offsetWidth+e.DomUtils.pxToInt(o.marginRight)}}}return this.buttonToolbar.style.width="",0===i.offsetWidth?0:i.offsetWidth+o+r+l}setRenderMode(t){t==rt.Plain?this.virtualButtonToolbar.classList.add(p.ButtonPlainToolbar):this.virtualButtonToolbar.classList.remove(p.ButtonPlainToolbar)}applyFullLayer(){this.applyAdaptivityState(p.ToolbarDefaultLayer)}applyNoTextLayer(){this.applyAdaptivityState(p.ToolbarNoItemText)}applyAdaptivityState(t){t.indexOf(p.ToolbarNoItemText)>-1?this.virtualButtonToolbar.classList.add(p.ToolbarNoItemText):this.virtualButtonToolbar.classList.remove(p.ToolbarNoItemText)}createVirtualGroup(t){const e=document.createElement("div"),i=e.querySelectorAll(`.${p.ToolbarItem} > .${b.Button}`),s=i.length>0?i[0].id:"",o=i.length>0?i[i.length-1].id:"";for(const i of t.attributes)e.setAttribute(i.name,i.value);for(let i=0;i<t.children.length;i++)e.appendChild(this.createVirtualItem(t.children[i],s,o));return e.classList.remove(p.ToolbarHiddenItem),e}createVirtualRootItem(t){return this.createVirtualItem(t,"","")}createVirtualItem(t,e,i){const s=document.createElement("div");for(const e of[...t.attributes].filter((t=>"id"!==t.name)))s.setAttribute(e.name,e.value);if(s.setAttribute("virtual-id",t.id),s.style.display="block",t.children.length>0)for(const o of t.children){const t=o.tagName===p.ToolbarItem?this.createVirtualItem(o,e,i):this.createVirtualChild(o,e,i);s.appendChild(t)}else s.innerHTML=t.innerHTML;return s}createVirtualPlaceholder(t){const e=document.createElement("DIV");for(const i of[...t.attributes].filter((t=>"id"!==t.name)))e.setAttribute(i.name,i.value);return e.style.width=t.clientWidth+"px",e}createVirtualChild(t,e,i){const s=t.cloneNode(!0),o=(e,i=null)=>{if(!kt.some((t=>e.tagName===t))){if(i&&vt.indexOf(e.tagName)>-1){const s=document.createElement("DIV");return s.style.width=t.getBoundingClientRect().width+"px",void i.replaceChild(s,e)}for(const t of e.attributes)t.name.toLowerCase().endsWith("id")&&e.setAttribute(t.name,t.value+"__virtual");e.setAttribute(g.virtualElementAttributeName,"true");for(const t of e.children)o(t,e)}};return o(s),s.setAttribute("virtual-id",t.id),s.classList.remove(p.ToolbarHiddenItem),t.classList.contains(b.Button)&&(e===s.id&&s.classList.add(b.ButtonFirst),i===s.id&&s.classList.add(b.ButtonLast)),s}}class Lt{constructor(t,e,i){this.layoutModel=t,this.layoutOptions=e,this.minRootItemIds=[],this.virtualLayout=new et,this.virtualToolbar=new It(i),this.setRenderMode(this.layoutOptions.renderMode),this.setMinRootItems()}updateMinRootItems(t){this.virtualLayout.resetLayers(this.layoutModel.layers);const e=this.virtualLayout.layers.find((t=>t.type===ot.FullSequential)),i=this.virtualLayout.layers.find((t=>t.type===ot.NoTextSequential));e.canApply=t>0;const s=this.virtualLayout.getItemsByName(this.layoutModel.currentLayer.stateName),o=this.getAffectedItemIds(s.map((t=>t.id)),this.layoutOptions.minRootItems,t);t-this.layoutOptions.minRootItems>0?(e.blocks.filter((t=>o.includes(t.id))).forEach((t=>t.minWidth=t.maxWidth)),i.blocks.filter((t=>o.includes(t.id))).forEach((t=>t.minWidth=t.maxWidth))):(e.blocks.filter((t=>o.includes(t.id))).forEach((t=>t.minWidth=0)),i.blocks.filter((t=>o.includes(t.id))).forEach((t=>t.minWidth=0))),this.layoutOptions.minRootItems=t,this.setMinRootItems(),this.layoutModel.updateFromVirtualLayer(e),this.layoutModel.updateFromVirtualLayer(i),this.layoutModel.updateCheckpoints(!0)}setMinRootItems(){if(0===this.virtualLayout.layers.length)return;const t=this.virtualLayout.layers.find((t=>t.type===ot.FullSequential)).blocks.filter((t=>!t.isTitle&&!t.isEllipse));this.minRootItemIds=this.getAffectedItemIds(t.map((t=>t.id)),0,this.layoutOptions.minRootItems)}setRenderMode(t){this.layoutOptions.renderMode=t,this.virtualToolbar.setRenderMode(this.layoutOptions.renderMode)}updateRenderMode(t){this.virtualLayout.resetLayers(this.layoutModel.layers),this.virtualToolbar.copyToolbar(),this.setRenderMode(t);for(const t of this.virtualLayout.blockIds){const e={id:t,width:0};for(const t of this.virtualLayout.layers){t.type===ot.NoText?this.virtualToolbar.applyNoTextLayer():this.virtualToolbar.applyFullLayer();const i=this.renderVirtualResizeModel(e);t.updateBlock(i,this.checkIfIsInMinRootItems(e.id))}}this.updateAllRealLayers(!0)}updateCanHideRootItems(t){if(this.refreshVirtualLayout(),this.layoutOptions.canHideRootItems===t)return;this.layoutOptions.canHideRootItems=t;const e=this.virtualLayout.layers.find((t=>t.type===ot.FullSequential)),i=this.virtualLayout.layers.find((t=>t.type===ot.NoTextSequential));e.canApply=this.layoutOptions.canHideRootItems&&(this.layoutOptions.minRootItems>0||!this.layoutOptions.canCollapseToIcons),i.canApply=this.layoutOptions.canHideRootItems,this.updateAllRealLayers(!0)}updateCanCollapseToIcons(t){if(this.refreshVirtualLayout(),this.layoutOptions.canCollapseToIcons===t)return;this.layoutOptions.canCollapseToIcons=t;const e=this.virtualLayout.layers.find((t=>t.type===ot.NoText)),i=this.virtualLayout.layers.find((t=>t.type===ot.NoTextSequential));e.canApply=this.layoutOptions.canCollapseToIcons,i.canApply=this.layoutOptions.canCollapseToIcons&&this.layoutOptions.canHideRootItems,this.updateAllRealLayers(!0)}addBlock(t){this.virtualLayout.initLayers(this.layoutModel.layers)}refreshVirtualLayout(){this.virtualLayout.resetLayers(this.layoutModel.layers),this.setMinRootItems()}removeBlocks(t){this.virtualLayout.initLayers(this.layoutModel.layers),this.refreshVirtualToolbar(),this.layoutModel.updateCheckpoints(!0)}orderBlock(t){this.virtualLayout.orderBlock(t.id,t.index),this.setMinRootItems(),this.updateAllRealLayers(!0),this.orderAllRealLayers()}updateBlockSize(t,e){this.refreshVirtualLayout(),e&&this.virtualToolbar.copyToolbar();let i=!1;var s;i=t.isTitle?this.virtualLayout.updateBlock(t,this.checkIfIsInMinRootItems(t.id),(s=ot,Object.keys(s).filter((t=>Number.isNaN(Number(t)))).map((t=>s[t]))))||i:this.renderVirtualInLayers(t)||i,this.updateAllRealLayers(i)}renderVirtualInLayers(t){let e=!1;for(const i of this.virtualLayout.layers){i.type===ot.NoText?this.virtualToolbar.applyNoTextLayer():this.virtualToolbar.applyFullLayer();const s=this.renderVirtualResizeModel(t);e=i.updateBlock(s,this.checkIfIsInMinRootItems(t.id))||e}return e}renderVirtualResizeModel(t){const e=this.virtualToolbar.getElementWidth(t.id);return{id:t.id,width:e}}getAffectedItemIds(t,e,i){const s=t.slice(0,e),o=t.slice(0,i);return i>e?o.filter((t=>!s.includes(t))):s.filter((t=>!o.includes(t)))}updateAllRealLayers(t){for(const t of this.virtualLayout.layers)this.layoutModel.updateFromVirtualLayer(t);this.layoutModel.updateCheckpoints(t)}orderAllRealLayers(){for(const t of this.virtualLayout.layers)this.layoutModel.orderFromVirtualLayer(t);this.layoutModel.updateCheckpoints(!0)}refreshVirtualToolbar(){this.virtualToolbar.copyToolbar()}checkIfIsInMinRootItems(t){return this.minRootItemIds.includes(t)}}class xt{constructor(t,e,i,s,o,r,a,l){this.id=t,this.index=e,this.adaptivePriority=i,this.minWidth=s,this.maxWidth=o,this.margin=r,this.isTitle=a,this.isEllipse=l}get fullSize(){return this.maxWidth}get minSize(){return this.minWidth}}const Ct=!1,Tt="data-dxtoolbar-group-id",wt=".dxbl-btn-toolbar",St=".dxbl-image:not(.dxbl-toolbar-dropdown-toggle)",Mt=".dxbl-image.dxbl-toolbar-dropdown-toggle",Bt=".dxbl-toolbar-item-tmpl",Rt="data-dxtoolbar-id",Wt=1;function Et(t,i=!1,s=!1){if(!t)return 0;let o=Math.ceil(a(t)+(i?0:Pt(t)));if(t.parentElement&&!s){const i=[...t.parentElement.children].find((t=>t.classList.contains(p.ToolbarGroupSeparator)));if(t.parentElement.lastElementChild===t){const i=e.DomUtils.getCurrentStyle(t.parentElement);o+=e.DomUtils.pxToInt(i.marginRight)}if(t.parentElement.firstElementChild===i&&i.nextElementSibling===t){const s=i;if(s){const i=e.DomUtils.getCurrentStyle(t.parentElement),r=e.DomUtils.getCurrentStyle(s);o+=0!==s.offsetWidth?s.offsetWidth:Wt+e.DomUtils.pxToInt(i.marginLeft)+e.DomUtils.pxToInt(r.marginRight)}}if(t.parentElement.firstElementChild===t){const i=e.DomUtils.getCurrentStyle(t.parentElement);o+=e.DomUtils.pxToInt(i.marginLeft)}}return o}function Nt(t){return[t.item.text,t.item.isDisplayed,t.item.iconCssClass]}function Pt(t){return l(t)}function Vt(t){return e.DomUtils.hasClassName(t,"popout")}function At(t){return"absolute"!==e.DomUtils.getCurrentStyle(t).position}function Ht(t,e){return`[${t}=${e}]`}function jt(t,e){return[...new Set(t)].map((t=>new xt(t.block.id,t.block.index,t.block.adaptivePriority,t.getMinWidth(),t.getMaxWidth(e),t.blockCollection.margins,t.block instanceof H,t.block instanceof V)))}const Dt=new class{constructor(){this.toolbars=new Map}addToolbar(t){this.toolbars.set(t.id,t)}removeToolbar(t){this.toolbars.delete(t.id)}addToolbarItem(t){const e=this.getToolbar(t.id);e&&e.addItem(t)}removeToolbarItem(t){for(const e of this.toolbars.values())e.removeItem(t)}getToolbar(t){const e=document.querySelector(`#${t}`);if(!e)return;const i=e.closest(`.${p.Toolbar}`);return i?this.toolbars.get(i.id):void 0}};let Ut=class extends k{constructor(){super(...arguments),this.isLoading=!0,this.isLayoutCalculated=!1,this.model=null,this._keyboardNavigator=null,this.layoutModel=null,this.minWidth=null,this.reinitRequested=!1,this.items=[],this.toolbarItemResizedHandler=this.resizeToolbarItem.bind(this),this.toolbarItemUpdatedHandler=this.updateToolbarItem.bind(this),this.toolbarItemVisibilityChangedHandler=this.toolbarItemVisibilityChanged.bind(this),this.toolbarItemOrderHandler=this.orderToolbarItem.bind(this),this.toolbarTitle="",this.minRootItemCount=0,this.collapseItemsToIcons=!1,this.hideRootItems=!1,this.itemSize=0,this.renderMode=rt.Contained,this.itemSpacingMode=at.Default,this.ellipsisId=null,this.layoutUpdateService=null}connectedCallback(){super.connectedCallback(),Dt.addToolbar(this),this.addEventListener(I.eventName,this.toolbarItemResizedHandler),this.addEventListener(L.eventName,this.toolbarItemUpdatedHandler),this.addEventListener(x.eventName,this.toolbarItemVisibilityChangedHandler),this.addEventListener(C.eventName,this.toolbarItemOrderHandler)}disconnectedCallback(){this.removeEventListener(I.eventName,this.toolbarItemResizedHandler),this.removeEventListener(L.eventName,this.toolbarItemUpdatedHandler),this.removeEventListener(x.eventName,this.toolbarItemVisibilityChangedHandler),this.removeEventListener(C.eventName,this.toolbarItemOrderHandler),Dt.removeToolbar(this),super.disconnectedCallback()}updated(t){var e,i,s,o;super.updated(t),t.has("ellipsisId")&&this.initializeToolbar(null!=this.ellipsisId),t.has("collapseItemsToIcons")&&(null===(e=this.layoutUpdateService)||void 0===e||e.updateCanCollapseToIcons(this.collapseItemsToIcons)),t.has("hideRootItems")&&(null===(i=this.layoutUpdateService)||void 0===i||i.updateCanHideRootItems(this.hideRootItems)),t.has("minRootItemCount")&&(this.model&&(this.model.minRootItems=this.minRootItemCount),null===(s=this.layoutUpdateService)||void 0===s||s.updateMinRootItems(this.minRootItemCount)),t.has("renderMode")&&(null===(o=this.layoutUpdateService)||void 0===o||o.updateRenderMode(this.renderMode))}updateLayout(){var t;null===(t=this.layoutModel)||void 0===t||t.updateLayout()}addItem(t){var e,i,s,o;if(t.isEllipse)return;if(!(null===(i=null===(e=this.model)||void 0===e?void 0:e.items)||void 0===i?void 0:i.find((e=>e.id===t.id)))&&this.model){const e=this.initItem(t,!0),i=this.getItemsBlocks(this.model,[e]);null===(s=this.layoutModel)||void 0===s||s.addItemBlock(i[0])}t&&!this.items.some((e=>e.id===t.id))&&(this.items.push(t),null===(o=this.layoutUpdateService)||void 0===o||o.addBlock(t))}removeItem(t){var e,i,s,o;const r=null===(e=this.model)||void 0===e?void 0:e.items.find((e=>e.id===t.id));if(r&&this.model){const t=this.model.items.indexOf(r);null===(i=this.model)||void 0===i||i.items.splice(t,1),null===(s=this.layoutModel)||void 0===s||s.removeItemBlock(r.id),null===(o=this.layoutUpdateService)||void 0===o||o.removeBlocks([r.id])}}resizeToolbarItem(t){this.processToolbarItem(t.detail.size,t.detail.force)}updateToolbarItem(t){this.processToolbarItem(t.detail.item)}toolbarItemVisibilityChanged(t){this.initializeKeyboardNavigator()}initializeKeyboardNavigator(){this._keyboardNavigator&&(this._keyboardNavigator.initialized?this._keyboardNavigator.reinitialize():this._keyboardNavigator.initialize(this,new S(this._keyboardNavigator,this)))}orderToolbarItem(t){var e;const i=t.detail.item,s=this.items.find((t=>t.id===i.id));s&&(s.index=i.index,null===(e=this.layoutUpdateService)||void 0===e||e.orderBlock(i))}processToolbarItem(t,e=!1){var i;if(0===this.offsetHeight)return;const s=this.items.find((e=>e.id===t.id));s&&(s.width!==t.width||e)&&(s.width=t.width,"none"!==window.getComputedStyle(this).display&&(t.isEllipse||null===(i=this.layoutUpdateService)||void 0===i||i.updateBlockSize(t,!0)))}initializeToolbar(t){const e={mainElement:this,canCollapseToIcons:this.collapseItemsToIcons,canHideRootItems:this.hideRootItems,minRootItems:this.minRootItemCount,title:this.toolbarTitle,items:this.items,itemSize:this.itemSize,renderMode:this.renderMode,itemSpacingMode:this.itemSpacingMode,adaptiveMenuModel:{id:this.ellipsisId}};t?this.reinit(e):this.init(e)}initItem(t,e=!1){const i=document.querySelector(`#${t.id}[dxbl-toolbar-adaptive-item]`);const s=this.model.groupMap;i&&i.classList.remove(p.ToolbarHiddenItem);const o=r(i,Ht(Tt,this.dataset.dxtoolbarId));let a=null;s.has(o)&&(a=s.get(o)),a||(a=new bt(o,this.model),s.set(o,a));const l=new pt(i,this.model,a);return l.text.update(t.text),l.groupName.update(t.groupName),l.adaptivePriority.update(t.adaptivePriority),l.iconCssClass.update(t.iconCssClass),l.isDisplayed.update(t.visible),l.index.update(t.index),l.id=t.id,this.model.itemMap.set(l.id,l),this.model.items.every((t=>t.id!==l.id))&&(a.addItem(l),this.model.items.push(l)),l}init(t){var s;const o=t.mainElement;if(!o)return;this.isLayoutCalculated=!1;const r=o.dataset.dxtoolbarId,a=Ht("data-dxtoolbar-title-id",r),l=Ht("data-dxtoolbar-ellipsis-id",r),d=document.querySelector(l),h=this.model=new ft(o,r),c=h.groupMap;Array.from(t.items).forEach((t=>this.initItem(t))),h.canHideRootItems=t.canHideRootItems,h.canCollapseToIcons=t.canCollapseToIcons,h.minRootItems=t.minRootItems,h.itemSize=t.itemSize,h.renderMode=t.renderMode,h.itemSpacing=t.itemSpacingMode;const u=new j([]),m=new gt(h,a);m.text.update(t.title),h.title=m;const y=document.querySelector(a);u.add(new H(h,m,null!==(s=null==y?void 0:y.id)&&void 0!==s?s:"-1"));const b=Array.from(c.values()),g=b.some((t=>t))?b.map((t=>t.items)).reduce(((t,e)=>t.concat(e))):[];u.addRange(this.getItemsBlocks(h,g.filter((t=>t.element))));const f=new yt(d,h);f.id=t.adaptiveMenuModel.id,u.add(new V(h,f)),h.ellipsisViewModel=f,this._keyboardNavigator=[...this.children].filter((t=>t.tagName.toLowerCase()===w))[0],this.initializeKeyboardNavigator();const v=F.contextItem(u),k=this.layoutModel=new lt((t=>{this.isLayoutCalculated||(this.isLayoutCalculated=!0,i((()=>{this.updateToolbarHeight(),this.isLoading=!1})));const s=h.element.querySelector(wt)||h.element;if(t.stateName.indexOf(p.ToolbarNoItemText)>-1?e.DomUtils.addClassName(s,p.ToolbarNoItemText):e.DomUtils.removeClassName(s,p.ToolbarNoItemText),e.DomUtils.removeClassName(o,p.Loading),n(o))return;const r=s.classList.contains(p.ToolbarNoItemText),a=h.items.concat(h.ellipsisViewModel).map((t=>({Id:t.id,IsVisible:t.isVisible.value,TextHidden:r})));this.dispatchEvent(new T(Array.from(a)))}));k.setDefaultLayer((function(t){switch(t.getName()){case"item":return F.fullWidthItem;case"title":return F.titleItem}return F.hiddenItem}),!0),k.setSequentialTransitionLayer(p.ToolbarHasEllipsis,ot.FullSequential,(function(t){switch(t.getName()){case"ellipsis":return F.fullWidthSystemItem;case"item":return v}}),h.canHideRootItems&&(h.minRootItems>0||!h.canCollapseToIcons)),k.setSimultaneousTransitionLayer(p.ToolbarNoItemText,(function(t){switch(t.getName()){case"item":return F.noTextItem;case"ellipsis":return F.hiddenItem}}),h.canCollapseToIcons),k.setSequentialTransitionLayer(h.canCollapseToIcons?`${p.ToolbarNoItemText} ${p.ToolbarHasEllipsis}`:p.ToolbarHasEllipsis,ot.NoTextSequential,(function(t){switch(t.getName()){case"item":return v;case"ellipsis":return F.fullWidthSystemItem}}),h.canHideRootItems&&h.canCollapseToIcons),u.forEach((t=>t._width=t.getWidth())),k.initialize(u,h.element.querySelector(wt)||h.element,(t=>{this.height!==t.height&&(this.isLayoutCalculated=!1,this.height=t.height),this.checkUpdateMinWidth(h)})),k.toolbarWidthUpdate=()=>this.checkUpdateMinWidth(h),k.toolbarHeightUpdate=t=>this.updateToolbarHeight(),k.toolbarIsVisible=()=>"none"!==window.getComputedStyle(this).display&&0!==this.offsetWidth,setTimeout((()=>{document.querySelector(`#${this.id}`)&&(this.layoutUpdateService=new Lt(k,{canCollapseToIcons:this.collapseItemsToIcons,canHideRootItems:this.hideRootItems,minRootItems:this.minRootItemCount,renderMode:this.renderMode},this.getAttribute(Rt)))}))}reinit(t){this.reinitRequested||(this.reinitRequested=!0,d((()=>{this.resetToDefault(),this.disposeModel(),d((()=>{this.reinitRequested=!1,this.init(t)}))})))}resetToDefault(){var t;null===(t=this.layoutModel)||void 0===t||t.resetToDefault()}updateSettings(){}oldUpdateSettings(t){var e,i;if(!this.model)return;let s=!1,o=!1;if(this.model.canHideRootItems!==t.canHideRootItems&&(this.model.canHideRootItems=t.canHideRootItems,s=!0),this.model.canCollapseToIcons!==t.canCollapseToIcons&&(this.model.canCollapseToIcons=t.canCollapseToIcons,s=!0),this.model.minRootItems!==t.minRootItems&&(this.model.minRootItems=t.minRootItems,o=!0),this.model.itemSize!==t.itemSize&&(this.model.itemSize=t.itemSize,o=!0),this.model.renderMode!==t.renderMode&&(this.model.renderMode=t.renderMode,o=!0),this.model.itemSpacing!==t.itemSpacingMode&&(this.model.itemSpacing=t.itemSpacingMode,o=!0),null===(e=this.model.title)||void 0===e||e.text.update(t.title),!o){const e=Array.from(t.items).map((t=>t.id)),i=Array.from(this.model.itemMap.values()).map((t=>t.id));o=!((r=e).length===(a=i).length&&r.every(((t,e)=>t===a[e])))}var r,a;o?this.reinit(t):(Array.from(t.items).forEach((t=>{if(!this.model)return;const e=this.model.itemMap.get(t.id);e&&(e.text.update(t.text),e.groupName.update(t.groupName),e.adaptivePriority.update(t.adaptivePriority),e.index.update(t.index),e.iconCssClass.update(t.iconCssClass),e.isDisplayed.update(t.visible))})),s&&(null===(i=this.layoutModel)||void 0===i||i.forceUpdate()))}disposeModel(){var t;null===(t=this.layoutModel)||void 0===t||t.dispose()}dispose(){this.disposeModel()}getItemsBlocks(t,e){return e.reduce(((e,i)=>e.group&&e.group.tryAddItem(i)?e:i.groupName.value?{group:e.blocks[e.blocks.length]=new A(t,i),blocks:e.blocks}:{group:null,blocks:e.blocks.concat([new P(t,i)])}),{blocks:[],group:null}).blocks.sort((function(t,e){return t.item.adaptivePriority.value-e.item.adaptivePriority.value})).map((function(t,e){return t.index=e,t}))}updateToolbarHeight(){const t=this.querySelector(wt);this.style.height=(!this.isLoading&&t?t.offsetHeight:this.offsetHeight)+"px"}checkUpdateMinWidth(t){var e;const i=null===(e=this.layoutModel)||void 0===e?void 0:e.getMinWidth();i&&this.minWidth!==i&&(this.minWidth=i,t.element.style.minWidth=i+"px")}};t([B()],Ut.prototype,"layoutModel",void 0),t([B()],Ut.prototype,"items",void 0),t([R({type:String,attribute:"toolbar-title"})],Ut.prototype,"toolbarTitle",void 0),t([R({type:Number,attribute:"min-root-item-count"})],Ut.prototype,"minRootItemCount",void 0),t([R({type:Boolean,attribute:"auto-collapse-items-to-icons"})],Ut.prototype,"collapseItemsToIcons",void 0),t([R({type:Boolean,attribute:"auto-hide-root-items"})],Ut.prototype,"hideRootItems",void 0),t([R({type:Number,attribute:"item-size"})],Ut.prototype,"itemSize",void 0),t([R({type:rt,attribute:"render-mode"})],Ut.prototype,"renderMode",void 0),t([R({type:at,attribute:"item-spacing-mode"})],Ut.prototype,"itemSpacingMode",void 0),t([R({type:String,attribute:"ellipsis-id"})],Ut.prototype,"ellipsisId",void 0),Ut=t([W(p.Toolbar)],Ut);const qt=new Map,zt=new ResizeObserver((t=>{for(const e of t){const t=e.target;t&&t.updateSize()}}));let Ot=class extends k{constructor(){super(...arguments),this.text="",this.groupName="",this.adaptivePriority=0,this.iconCssClass="",this.visible=!1,this.index=0,this.justHidden=!1}get isEllipse(){return this.classList.contains(p.ButtonEllipsis)}get isTitle(){return this.classList.contains(p.ToolbarTitle)}get isPlaceholder(){return this.classList.contains(p.ToolbarPlaceholder)}get isHidden(){return"none"===this.style.display}update(t){this.isInRibbon||(t.has("text")&&this.updateSize(!0),t.has("visible")&&this.dispatchEvent(new x(this.visible)),t.has("index")&&this.itemOrdered(),super.update(t))}updateSize(t=!1){(!this.isHidden||this.justHidden||(this.justHidden=!0,t))&&(this.isHidden||!this.justHidden?this.dispatchEvent(new I({id:this.id,width:this.offsetWidth,isTitle:this.isTitle,isEllipse:this.isEllipse,isHidden:this.isHidden},t)):this.justHidden=!1)}connectedCallback(){super.connectedCallback(),this.isInRibbon||(Dt.addToolbarItem(this.toolbarItem),this.isEllipse||this.isPlaceholder||zt.observe(this))}disconnectedCallback(){super.disconnectedCallback(),this.isInRibbon||(Dt.removeToolbarItem(this.toolbarItem),this.isEllipse||this.isPlaceholder||zt.unobserve(this))}itemOrdered(){this.index>-1&&this.dispatchEvent(new C(this.toolbarItem))}get toolbarItem(){return{id:this.id,groupName:this.groupName,text:this.text,adaptivePriority:this.adaptivePriority,iconCssClass:this.iconCssClass,visible:this.visible,index:this.index,width:this.offsetWidth,isEllipse:this.isEllipse,isTitle:this.isTitle}}get isInRibbon(){return null!==this.closest(M.Ribbon)}};function _t(t,e){return Promise.resolve("ok")}function Ft(t){return e(t=h(t),qt),Array.from(qt.keys()).forEach((t=>{n(t)&&e(t,qt)})),Promise.resolve("ok");function e(t,e){if(!t)return;const i=t.querySelector(wt);i&&c(i);const s=e.get(t);null==s||s.dispose(),e.delete(t)}}t([R({type:String,attribute:"text"})],Ot.prototype,"text",void 0),t([R({type:String,attribute:"group-name"})],Ot.prototype,"groupName",void 0),t([R({type:Number,attribute:"adaptive-priority"})],Ot.prototype,"adaptivePriority",void 0),t([R({type:String,attribute:"icon-css-class"})],Ot.prototype,"iconCssClass",void 0),t([R({type:Boolean,attribute:"visible"})],Ot.prototype,"visible",void 0),t([R({type:Number,attribute:"index"})],Ot.prototype,"index",void 0),Ot=t([W(p.ToolbarItem)],Ot),customElements.define("dxbl-toolbar-menu-item",class extends m{constructor(){super(...arguments),this.boundOnButtonClick=this.onButtonClick.bind(this)}connectedCallback(){super.connectedCallback(),this.addEventListener("click",this.boundOnButtonClick)}disconnectedCallback(){this.removeEventListener("click",this.boundOnButtonClick),super.disconnectedCallback()}canHandlePointerDown(t){return u.containsInComposedPath(t,this)}onButtonClick(t){if(null===this.getAttribute("submit-form-on-click"))return;const e=this.getAttribute("data-dxtoolbar-container-id");if(e){const t=document.querySelector(`[${Rt}=${e}]`);if(t){const e=document.createElement("input");e.type="submit",e.hidden=!0,t.appendChild(e),e.click(),t.removeChild(e)}}}});const Gt={init:_t,dispose:Ft};export{Ut as ToolbarComponent,Ot as ToolbarItem,Gt as default,Ft as dispose,_t as init};
