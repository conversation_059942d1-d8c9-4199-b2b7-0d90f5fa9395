import{V as e}from"./dx-scroll-viewer-da0fb41c.js";import{D as t}from"./layouthelper-67dd777a.js";import{D as s}from"./keyboard-navigation-strategy-ea41c807.js";import{T as a,B as l}from"./dx-virtual-scroll-viewer-f4a3bc9e.js";import{S as i}from"./scroll-viewer-css-classes-e724f203.js";import{T as o}from"./text-editor-733d5e56.js";import{C as r}from"./css-classes-c63af734.js";import{G as c}from"./grid-scroll-utils-a8c65cf1.js";import{C as n}from"./custom-events-helper-e7f279d3.js";import{o as m}from"./constants-da6cacac.js";class d{}d.ClassName=r.Prefix+"-list-box",d.TableNoScrollClassName=d.ClassName+"-table-no-scroll",d.TableHeaderRowClassName=d.ClassName+"-table-header-row",d.TableSelectionCellClassName=d.ClassName+"-selection-cell",d.MultiSelectClassName=d.ClassName+"-multi-select",d.SelectedItemClassName=d.ClassName+"-item-selected",d.FocusedItemClassName=d.ClassName+"-item-focused",d.EmptyDataItemClassName=d.ClassName+"-empty-data-item",d.GroupRowClassName=d.ClassName+"-group-item",d.ItemDisplayTemplateContainer=d.ClassName+"-item-display-template-container",d.RenderContainer=r.Prefix+"-list-box-render-container";class u{}u.VisibleIndexAttributeName="data-visible-index",u.ItemIdAttributeName="id";class g{}g.TableSelector=`.${i.ContentContainerClassName} > table`,g.ListSelector=`.${i.ContentContainerClassName} > ul`,g.MultiSelectClassSelector=`.${d.MultiSelectClassName}`,g.TablePartsSelector=":scope > *:not(colgroup):not(thead)",g.TableBodySelector=":scope > tbody",g.KeyboardNavigatorSelector=`:scope > ${s}`,g.HeaderRowSelector=`.${d.TableHeaderRowClassName}`,g.TableDataRowSelector=`:scope > tr:not([${a}]):not([${l}]):not(.${d.GroupRowClassName})`,g.TableVisibleRowGeneralSelector=`tr[${u.VisibleIndexAttributeName}]`,g.TableVisibleRowSelector=`:scope > ${g.TableVisibleRowGeneralSelector}`,g.ItemSelector=`:scope > li:not([${a}]):not([${l}]):not(.${d.GroupRowClassName})`,g.ListVisibleItemGeneralSelector=`li[${u.VisibleIndexAttributeName}]`,g.ListVisibleItemSelector=`:scope > ${g.ListVisibleItemGeneralSelector}`,g.VisibleItemSelector=`:scope > [${u.VisibleIndexAttributeName}]`,g.ListItemDisplayTemplateConrainerSelector=`.${d.ItemDisplayTemplateContainer}`,g.SelectedItemSelector=`.${d.SelectedItemClassName}`,g.FocusedItemSelector=`.${d.FocusedItemClassName}`,g.EmptyDataItemSelector=`.${d.EmptyDataItemClassName}`,g.SearchBoxSelector=`:scope > .${o.TextEdit}`,g.GroupRowSelector=`.${d.GroupRowClassName}`;class b{static getVerticalEdge(t){switch(t){case e.Top:return 0;case e.Bottom:return 1;default:return-1}}static getElementVisibleIndex(e){return t.getAttributeIntValue(e,u.VisibleIndexAttributeName,-1)}static findItemIndex(e,t){for(let s=0;s<t.length;s++){const a=t[s];if(a&&this.getElementVisibleIndex(a)===e)return s}return-1}static queryItemsBySelector(e,t){return Array.from(e.querySelectorAll(t))}static isGroupRowElement(e){return!!e&&(e.matches&&e.matches(g.GroupRowSelector))}static getRow(e,t){const s=e.getItemsContainer();return s?s.querySelector(`:scope > [${u.VisibleIndexAttributeName}="${t}"]`):null}static isGroupRow(e,t){const s=b.getRow(e,t);return!!s&&b.isGroupRowElement(s)}static getElementHeight(e,t,s=0){const a=b.getRow(e,t);return a?a.getBoundingClientRect().height:s}static calculateItemAverageHeight(e){const t=e[0];return(e[e.length-1].getBoundingClientRect().bottom-t.getBoundingClientRect().top)/e.length}static isOutsideViewportItemRequired(e,t,s,a){const l=a?s+1:s-1,i=b.getRow(e,l);if(i){const s=i.getBoundingClientRect(),o=t.getElementsRectangle(t.getHeaderElements());if(!a&&o)return!c.getIntersectionRectangle(s,o).isEmpty;return c.getIntersectionRectangle(s,t.getBoundingClientRect()).height<=b.getElementHeight(e,l)}return!0}static calculateRowHeight(e,t,s,a){const l=b.getRow(e,s);let i=b.getElementHeight(e,s);return l&&i&&!b.isOutsideViewportItemRequired(e,t,s,a)&&(i+=c.calculateHeightOffsetValue(l,t,a)),i}static calculateBoundaryItemVisibleIndex(e,t,s){const a=Math.ceil(c.getDataAreaViewportHeight(t)),l=s?0:e.getLastAvailableVisibleIndex();let i=s&&e.getSelectedVisibleIndex()<=l||!s&&e.getSelectedVisibleIndex()>=l?l:e.getSelectedVisibleIndex(),o=b.calculateRowHeight(e,t,i,s);const r=c.isVirtualScrollingEnabled(t)?b.calculateItemAverageHeight(e.getRows()):void 0,n=s?1:-1;let m=i+n;for(;e.isElementInsideViewport(m)&&(o+=b.getElementHeight(e,m,r),!(o>a&&i!==e.getSelectedVisibleIndex()));)i=m,m+=n;return i}}class p extends CustomEvent{constructor(e){super(p.eventName,{detail:new C(e),bubbles:!0,composed:!0,cancelable:!0})}}p.eventName=m+".focusedrowchanged";class C{constructor(e){this.VisibleIndex=e}}class I extends CustomEvent{constructor(e,t){super(I.eventName,{detail:new N(e,t),bubbles:!0,composed:!0,cancelable:!0})}}I.eventName=m+".makeelementvisible";class N{constructor(e,t){this.VisibleIndex=e,this.VerticalEdge=t}}n.register(p.eventName,(e=>e.detail)),n.register(I.eventName,(e=>e.detail));export{u as L,b as a,g as b,d as c,p as d,I as e};
