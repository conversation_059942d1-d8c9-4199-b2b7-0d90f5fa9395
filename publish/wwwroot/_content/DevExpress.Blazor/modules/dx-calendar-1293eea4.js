import{d as e}from"./dom-554d0cc7.js";import{D as t}from"./dx-html-element-base-3262304e.js";import{C as n}from"./css-classes-c63af734.js";import{_ as s}from"./tslib.es6-d65164b3.js";import{H as a,D as i,L as r,P as o,a as l,c as d,d as c}from"./dx-html-element-pointer-events-helper-c1007ce3.js";import{S as h}from"./single-slot-element-base-01d93921.js";import{b as u}from"./constants-da6cacac.js";import{n as m}from"./property-4ec0b52d.js";import{R as D}from"./layouthelper-67dd777a.js";import{C as y}from"./custom-events-helper-e7f279d3.js";import{K as g,F as p,D as C}from"./keyboard-navigation-strategy-ea41c807.js";import{k as f}from"./key-ffa272aa.js";import{f as v}from"./focustrap-d11cfef9.js";const S="dxbl-calendar-table-container";class I{}I.MainElement=n.Prefix+"-calendar",I.SelectedItem=I.MainElement+"-selected-item",I.SelectedRange=I.MainElement+"-selected-range",I.Header=I.MainElement+"-header",I.Content=I.MainElement+"-content",I.DataTableContainerWrapperClassName=I.MainElement+"-data-table-container-wrapper",I.Footer=I.MainElement+"-footer",I.WeekRow=I.MainElement+"-week-row",I.YearRow=I.MainElement+"-year-row",I.DecadeRow=I.MainElement+"-decade-row",I.CenturyRow=I.MainElement+"-century-row",I.Day=I.MainElement+"-day",I.NotCurrentView=I.MainElement+"-not-current-view",I.HeaderHelper=I.MainElement+"-header",I.HeaderTitleButton=I.HeaderHelper+"-title-btn",I.HeaderPreviousYearButton=I.HeaderHelper+"-previous-year-btn",I.HeaderPreviousMonthButton=I.HeaderHelper+"-previous-month-btn",I.HeaderPreviousPeriodButton=I.HeaderHelper+"-previous-period-btn",I.HeaderNextYearButton=I.HeaderHelper+"-next-year-btn",I.HeaderNextMonthButton=I.HeaderHelper+"-next-month-btn",I.HeaderNextPeriodButton=I.HeaderHelper+"-next-period-btn";class b{}var T,w;b.MainElement=`.${I.MainElement}`,b.SelectedItem=`td.${I.SelectedItem}`,b.Header=`.${I.Header}`,b.FirstDataTableContainer=`${S}:nth-of-type(1)`,b.DateTableContainer=S,b.DataTable=`.${I.Content} > table`,b.DataTableContainerWrapperClassName=`.${I.DataTableContainerWrapperClassName}`,b.Footer=`.${I.Footer}`,b.Day=`.${I.Day}`,function(e){e.class="class",e.view="view",e.viewDate="view-date",e.enableMultiSelect="enable-multi-select",e.minDate="min-date",e.maxDate="max-date",e.visiblePeriodsCount="visible-periods-count",e.enableRangeSelect="enable-range-select",e.periodIndex="period-index",e.hasNullStartXorEndDate="has-null-start-xor-end-date",e.selectDateOnFirstFocus="select-date-on-first-focus"}(T||(T={})),function(e){e.dataDate="data-date",e.dataMonth="data-month",e.dataYear="data-year",e.dataDecade="data-decade"}(w||(w={}));const E=12,x=new Date(new Date(Date.UTC(1,0,1)).setUTCFullYear(1)),M=new Date(Date.UTC(9999,11,31)),F=864e5;class R extends CustomEvent{constructor(e,t,n){super(R.eventName,{detail:new _(e,t,n),bubbles:!0,composed:!0,cancelable:!0})}}R.eventName=u+".daycellpointerup";class P extends Event{constructor(){super(P.eventName,{bubbles:!0,composed:!1,cancelable:!0})}}P.eventName=u+".keyboardnavigationleaved";class _{constructor(e,t,n){this.PointerDownDate=e,this.PointerUpDate=t,this.ClearSelection=n}}y.register(R.eventName,(e=>e.detail));class K{constructor(e,t){this._pointerDownDate=e.pointerDownDate,this._pointerUpDate=null,this._clearSelection=e.clearSelection,this.dayCellElements=e.dayCellElements,this._callback=t,this.refreshUI(!1)}get clearSelection(){return this._clearSelection}get pointerDownDate(){return this._pointerDownDate}get pointerUpDate(){return this._pointerUpDate}set pointerUpDate(e){this._pointerUpDate!==e&&e&&(this._pointerUpDate=e,this.refreshUI(!0))}get isValid(){return!!this._pointerUpDate}stop(){this.refreshUI(!1),this.isValid&&null!==this._callback&&this._callback(this._pointerDownDate,this._pointerUpDate,this._clearSelection)}cancel(){for(let e=0;e<this.dayCellElements.length;e++){const t=this.dayCellElements[e];K.removeDayCellSelectedClasses(t),t.hasAttribute("data-selected")&&K.addSelectedClassesToCell(t)}}refreshUI(t){const n=this.getStartPointedDateRange(),s=this.getEndPointedDateRange();for(let a=0;a<this.dayCellElements.length;a++){const i=this.dayCellElements[a];if(se.isDateCellDisabled(i))continue;const r=Number(i.dataset.date);K.removeDayCellSelectedClasses(i),t&&this.isDateRangeBoundary(r,n,s)&&e.DomUtils.addClassName(i,I.SelectedRange),this.needApplySelectedStyle(r,n,s,t)&&K.addSelectedClassesToCell(i),i.hasAttribute("data-selected")&&!this.clearSelection&&K.addSelectedClassesToCell(i)}}getStartPointedDateRange(){return this.pointerUpDate?Math.min(this.pointerUpDate.getTime(),this.pointerDownDate.getTime()):this.pointerDownDate.getTime()}getEndPointedDateRange(){return this.pointerUpDate?Math.max(this.pointerUpDate.getTime(),this.pointerDownDate.getTime()):this.pointerDownDate.getTime()}needApplySelectedStyle(e,t,n,s){return!(e<t||e>n)&&(!s||!this.isDateRangeBoundary(e,t,n))}isDateRangeBoundary(e,t,n){if(!this.isDateDisabled(e)&&(e===t||e===n))return!0;return!(!(e>t&&e<n)||e!==this.getBoundaryDate(t,e)&&e!==this.getBoundaryDate(n,e))}getBoundaryDate(e,t){const n=e<t,s=n?1:-1;let a=e;for(;(n?a<t:a>t)&&this.isDateDisabled(a);)a+=s*F;return a}isDateDisabled(e){const t=this.getDayCellByDay(e);return null!=t&&se.isDateCellDisabled(t)}getDayCellByDay(e){for(let t=0;t<this.dayCellElements.length;t++)if(this.dayCellElements[t].getAttribute(w.dataDate)===e.toString())return this.dayCellElements[t];return null}static addSelectedClassesToCell(t){e.DomUtils.addClassName(t,I.SelectedItem)}static removeDayCellSelectedClasses(t){e.DomUtils.removeClassName(t,I.SelectedItem),e.DomUtils.removeClassName(t,I.SelectedRange),e.DomUtils.removeClassName(t,"text-white")}}class H{constructor(e,t,n){this._pointerDownDate=e,this._pointerUpDate=null,this._clearSelection=t,this._callback=n}get clearSelection(){return this._clearSelection}get pointerDownDate(){return this._pointerDownDate}get pointerUpDate(){return this._pointerUpDate}set pointerUpDate(e){this._pointerUpDate=e}get isValid(){var e;return this.pointerDownDate.getTime()===(null===(e=this.pointerUpDate)||void 0===e?void 0:e.getTime())}stop(){null!==this._callback&&this._pointerUpDate&&this._callback(this._pointerDownDate,this._pointerUpDate,this._clearSelection)}cancel(){}}class N{constructor(e,t){this._pointerDownDate=e.pointerDownDate,this._pointerUpDate=null,this._clearSelection=e.clearSelection,this.dayCellElements=e.dayCellElements,this._callback=t,this.refreshUI()}get clearSelection(){return this._clearSelection}get pointerDownDate(){return this._pointerDownDate}get pointerUpDate(){return this._pointerUpDate}set pointerUpDate(e){this._pointerUpDate!==e&&e&&(this._pointerUpDate=e,this.refreshUI())}get isValid(){return!!this._pointerUpDate}stop(){this.refreshUI(),this.isValid&&null!==this._callback&&this._callback(this._pointerDownDate,this._pointerUpDate,this._clearSelection)}cancel(){for(let e=0;e<this.dayCellElements.length;e++){const t=this.dayCellElements[e];N.removeDayCellSelectedClasses(t),t.hasAttribute("data-selected")&&N.addSelectedClassesToCell(t)}}refreshUI(){const t=this.getStartPointedDateRange(),n=this.getEndPointedDateRange();for(let s=0;s<this.dayCellElements.length;s++){const a=this.dayCellElements[s];if(se.isDateCellDisabled(a))continue;const i=Number(a.dataset.date);N.removeDayCellSelectedClasses(a),this.isDateRangeBoundary(i,t,n)&&e.DomUtils.addClassName(a,I.SelectedRange),this.needApplySelectedStyle(i,t,n)&&N.addSelectedClassesToCell(a),a.hasAttribute("data-selected")&&!this.clearSelection&&N.addSelectedClassesToCell(a)}}getStartPointedDateRange(){return this.pointerUpDate?Math.min(this.pointerUpDate.getTime(),this.pointerDownDate.getTime()):this.pointerDownDate.getTime()}getEndPointedDateRange(){return this.pointerUpDate?Math.max(this.pointerUpDate.getTime(),this.pointerDownDate.getTime()):this.pointerDownDate.getTime()}needApplySelectedStyle(e,t,n){return!(e<t||e>n)&&!this.isDateRangeBoundary(e,t,n)}isDateRangeBoundary(e,t,n){if(!this.isDateDisabled(e)&&(e===t||e===n))return!0;return e>t&&e<n&&(e===this.getBoundaryDate(t,e)||e===this.getBoundaryDate(n,e))}getBoundaryDate(e,t){const n=e<t,s=n?1:-1;let a=e;for(;(n?a<t:a>t)&&this.isDateDisabled(a);)a+=s*F;return a}isDateDisabled(e){const t=this.getDayCellByDay(e);return null!=t&&se.isDateCellDisabled(t)}getDayCellByDay(e){for(let t=0;t<this.dayCellElements.length;t++)if(this.dayCellElements[t].getAttribute(w.dataDate)===e.toString())return this.dayCellElements[t];return null}static addSelectedClassesToCell(t){e.DomUtils.addClassName(t,I.SelectedItem)}static removeDayCellSelectedClasses(t){e.DomUtils.removeClassName(t,I.SelectedItem),e.DomUtils.removeClassName(t,I.SelectedRange),e.DomUtils.removeClassName(t,"text-white")}}var U,B,k,Y;!function(e){e[e.PreviousYear=0]="PreviousYear",e[e.PreviousMonth=1]="PreviousMonth",e[e.Title=2]="Title",e[e.NextMonth=3]="NextMonth",e[e.NextYear=4]="NextYear",e[e.PreviousPeriod=5]="PreviousPeriod",e[e.NextPeriod=6]="NextPeriod"}(U||(U={})),function(e){e[e.Header=0]="Header",e[e.DataTable=1]="DataTable",e[e.Footer=2]="Footer"}(B||(B={})),function(e){e[e.Month=0]="Month",e[e.Year=1]="Year",e[e.Decade=2]="Decade",e[e.Century=3]="Century"}(k||(k={})),function(e){e[e.January=0]="January",e[e.February=1]="February",e[e.March=2]="March",e[e.April=3]="April",e[e.May=4]="May",e[e.June=5]="June",e[e.July=6]="July",e[e.August=7]="August",e[e.September=8]="September",e[e.October=9]="October",e[e.November=10]="November",e[e.December=11]="December"}(Y||(Y={}));class V extends h{constructor(){super(),this._handlePointerEventsMode=a.None,this.viewDate=new Date,this.viewType=k.Month,this.periodIndex=0,this._pointerEventsHelper=new i(this)}get calendar(){return this._calendar||(this._calendar=e.DomUtils.getParentByTagName(this,u)),this._calendar}connectedCallback(){super.connectedCallback(),this._pointerEventsHelper.initialize()}disconnectedCallback(){super.disconnectedCallback(),this._pointerEventsHelper.dispose()}updated(e){e.has("viewDate")&&this.calendar.restoreFocus(),e.has("viewType")&&(this._handlePointerEventsMode=a.None,this.viewType===k.Month&&(this.calendar.fixCalendarTableSize(),this._handlePointerEventsMode=a.Click|a.Dragging),this.calendar.restoreFocus()),super.updated(e)}get handlePointerEventsMode(){return this._handlePointerEventsMode}get handlePointerEventsTarget(){return this}get handlePointerEventsDelay(){return r}get hoverTitleElementsSelector(){return null}get bypassNonInlineHoverTitleElementChildSelector(){return null}shouldProcessPointerEvent(e){return!0}}s([m({attribute:T.viewDate,converter:e=>new Date(Number(e))})],V.prototype,"viewDate",void 0),s([m({type:Number,attribute:T.view})],V.prototype,"viewType",void 0),s([m({type:Number,attribute:T.periodIndex})],V.prototype,"periodIndex",void 0);class A extends g{constructor(e,t){super(e.getKeyboardNavigator(),t),this.calendar=e}isFocusedFromOutside(){return this.calendar.isSameNode(document.activeElement)}isFocusedFromDataTable(){var e,t,n;return null!==(n=null===(t=null!==(e=this.getDataTableContainerWrapper())&&void 0!==e?e:this.getCurrentDataTableContainer())||void 0===t?void 0:t.contains(document.activeElement))&&void 0!==n&&n}getHeader(){return this.calendar.getHeaderElement()}getDataTableContainerWrapper(){return this.calendar.tableContainerWrapper}getCurrentDataTableContainer(){return this.calendar.currentTableContainer}getAllDataTableContainers(){return this.calendar.enumerateTableContainers}getFooter(){return this.calendar.getFooterElement()}}class O extends A{constructor(e,t){super(e,t),this.itemSelector=`:scope > .${n.Button}:not(.${n.Disabled}), :scope > div.${I.HeaderTitleButton}:not(.${n.Disabled})`,this.buttonClassToButtonType=new Map([[I.HeaderPreviousYearButton,U.PreviousYear],[I.HeaderPreviousMonthButton,U.PreviousMonth],[I.HeaderPreviousPeriodButton,U.PreviousPeriod],[I.HeaderTitleButton,U.Title],[I.HeaderNextYearButton,U.NextYear],[I.HeaderNextMonthButton,U.NextMonth],[I.HeaderNextPeriodButton,U.NextPeriod]]),this.buttonTypeToButtonClass=new Map([[U.PreviousYear,I.HeaderPreviousYearButton],[U.PreviousMonth,I.HeaderPreviousMonthButton],[U.PreviousPeriod,I.HeaderPreviousPeriodButton],[U.Title,I.HeaderTitleButton],[U.NextYear,I.HeaderNextYearButton],[U.NextMonth,I.HeaderNextMonthButton],[U.NextPeriod,I.HeaderNextPeriodButton]])}getShortcutContext(){return{AreaType:B.Header,ButtonType:this.getSelectedButtonType()}}queryItems(){return this.queryItemsBySelector(this.itemSelector)}activate(){const e=this.getButtonIndexByType(this.lastFocusedButtonType);this.selectedItemIndex=this.isFocusedFromOutside()?0:this.isFocusedFromDataTable()?this.itemCount-1:null!=e?e:0,super.activate()}leaveBackward(){this.lastFocusedButtonType=void 0,super.leaveBackward()}leaveForward(){this.lastFocusedButtonType=void 0,super.leaveForward()}handleKeyDown(e){switch(f.KeyUtils.getEventKeyCode(e)){case f.KeyCode.Tab:return e.shiftKey?this.moveToPrevItem():this.moveToNextItem();case f.KeyCode.Space:case f.KeyCode.Enter:return this.calendar.scheduleFocusRestoration(!1),this.performShortcutEvent(e),!0;case f.KeyCode.Left:case f.KeyCode.Up:case f.KeyCode.Home:case f.KeyCode.PageUp:case f.KeyCode.Right:case f.KeyCode.Down:case f.KeyCode.End:case f.KeyCode.PageDown:return!0}return super.handleKeyDown(e)}moveToPrevItem(){return this.selectedItemIndex>0&&(this.selectItem(this.selectedItemIndex-1),!0)}moveToNextItem(){return this.selectedItemIndex<this.itemCount-1&&(this.selectItem(this.selectedItemIndex+1),!0)}updateSelectedItemByIndex(e){super.updateSelectedItemByIndex(e),this.lastFocusedButtonType=this.getSelectedButtonType()}getSelectedButtonType(){for(const e of this.selectedItemElement.classList)if(this.buttonClassToButtonType.has(e))return this.buttonClassToButtonType.get(e)}getButtonIndexByType(e){return e?this.items.findIndex((t=>t.classList.contains(this.buttonTypeToButtonClass.get(e)))):void 0}}class z extends A{get isObsolete(){return this.calendar.currentView!==this.currentCalendarView}get targetElement(){return super.targetElement}constructor(e,t){super(e,t),this.boundCalendarFocusedDayChangedHandler=this.onCalendarFocusedDayChanged.bind(this),this.calendar.onFocusedDayChanged(this.boundCalendarFocusedDayChangedHandler)}activate(){this.applyCalendarFocus(),super.activate()}onDispose(){this.calendar.offFocusedDayChanged(this.boundCalendarFocusedDayChangedHandler),super.onDispose()}get daySelectionRenderer(){return this.calendar.daySelectionRenderer}getShortcutContext(){const e=this.lastShortcutArgs;return delete this.lastShortcutArgs,{AreaType:B.DataTable,Offset:null==e?void 0:e.offset,Row:null==e?void 0:e.row,Column:null==e?void 0:e.column,PeriodIndex:null==e?void 0:e.periodIndex,Date:null==e?void 0:e.date}}queryItems(){return this.queryItemsBySelector(this.itemSelector)}handleKeyDown(e){switch(f.KeyUtils.getEventKeyCode(e)){case f.KeyCode.Left:return!((e.ctrlKey||e.metaKey||e.altKey)&&!e.shiftKey)&&this.moveToPrevDay(e);case f.KeyCode.Right:return!((e.ctrlKey||e.metaKey||e.altKey)&&!e.shiftKey)&&this.moveToNextDay(e);case f.KeyCode.Up:return!((e.ctrlKey||e.metaKey||e.altKey)&&!e.shiftKey)&&this.moveToPrevRow(e);case f.KeyCode.Down:return!((e.ctrlKey||e.metaKey||e.altKey)&&!e.shiftKey)&&this.moveToNextRow(e);case f.KeyCode.Home:return this.handleHomeKey(e);case f.KeyCode.End:return this.handleEndKey(e);case f.KeyCode.PageUp:return this.calendar.scheduleFocusRestoration(),this.handlePageUpKey(e);case f.KeyCode.PageDown:return this.calendar.scheduleFocusRestoration(),this.handlePageDownKey(e);case f.KeyCode.Enter:case f.KeyCode.Space:return this.handleItemClick(e),!0}return super.handleKeyDown(e)}handleKeyUp(e){return this.isShiftKeyCode(e)&&this.stopSelectingDays(),super.handleKeyUp(e)}isShiftKeyCode(e){return 16===f.KeyUtils.getEventKeyCode(e)}getSelectedItemRowIndex(){return Math.floor(this.selectedItemIndex/this.rowLength)}getSelectedItemColumnIndex(){return Math.floor(this.selectedItemIndex%this.rowLength)}handlePageUpKey(e,t){return this.prepareAndPerformShortcutEvent(e,{offset:null!=t?t:e.shiftKey?-12:-1,date:e.shiftKey?se.getDateFromCell(this.selectedItemElement):void 0}),!0}handlePageDownKey(e,t){return this.prepareAndPerformShortcutEvent(e,{offset:null!=t?t:e.shiftKey?E:1,date:e.shiftKey?se.getDateFromCell(this.selectedItemElement):void 0}),!0}prepareAndPerformShortcutEvent(e,t){t.periodIndex=this.targetElement.periodIndex,this.lastShortcutArgs=t,this.performShortcutEvent(e)}tryRemoveTabindexFromSelectedElement(){this.selectedItemElement&&p.removeTabIndex(this.selectedItemElement)}onCalendarFocusedDayChanged(e,t=!0){this.tryRemoveTabindexFromSelectedElement(),this.isActive()&&e&&(this.tryRestoreSelectedItem(e),this.selectedItemElement&&p.makeElementFocusable(this.selectedItemElement),t&&this.selectItem(this.selectedItemIndex))}applyCalendarFocus(){var e;this.tryRemoveTabindexFromSelectedElement();const t=null!==(e=this.calendar.getFocusedCellElementInView(this.currentCalendarView))&&void 0!==e?e:this.targetElement.querySelector(b.SelectedItem);t&&this.tryRestoreSelectedItem(t)}tryRestoreSelectedItem(e){if(!e.isConnected)return;const t=this.navigator.getStrategy(this.getCurrentDataTableContainer());if(!t)return;const n=t.items.indexOf(e);n>-1&&(t.selectedItemIndex=n)}initStartDaySelection(e){null===this.daySelectionRenderer&&e.shiftKey&&this.calendar.createDaySelectionRenderer(se.getDateFromCell(this.selectedItemElement),!e.ctrlKey&&!e.metaKey)}stopSelectingDays(){null!==this.daySelectionRenderer&&(this.daySelectionRenderer.pointerUpDate=se.getDateFromCell(this.selectedItemElement),this.calendar.stopSelectingDays())}updateSelectedItemFocusableState(){this.selectedItemElement&&(this.isActive()?p.makeElementFocusable(this.selectedItemElement):p.removeTabIndex(this.selectedItemElement))}isActive(){return this.targetElement===this.calendar.currentTableContainer}}class L extends z{get rowLength(){return 7}get currentCalendarView(){return k.Month}get itemSelector(){return`:scope .${I.WeekRow} > .${I.Day}`}constructor(e,t){super(e,t)}activate(){super.activate(),this.extendSelection()}moveToPrevDay(e){var t,n;this.initStartDaySelection(e);const s=this.calendar.tryMoveFocus({value:-1,isHorizontal:!0},this.selectedItemElement);if(s.needChangePeriod){if(s.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,null!==(n=s.periodsToSkip)&&void 0!==n?n:-1)}else{const e=null!==(t=s.unitsToSkip)&&void 0!==t?t:-1;this.selectedItemIndex>=e&&this.selectItem(this.selectedItemIndex+e),this.extendSelection()}return!0}moveToNextDay(e){var t,n;this.initStartDaySelection(e);const s=this.calendar.tryMoveFocus({value:1,isHorizontal:!0},this.selectedItemElement);if(s.needChangePeriod){if(s.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,null!==(n=s.periodsToSkip)&&void 0!==n?n:1)}else{const e=null!==(t=s.unitsToSkip)&&void 0!==t?t:1;this.selectedItemIndex<this.itemCount-e&&this.selectItem(this.selectedItemIndex+e),this.extendSelection()}return!0}moveToPrevRow(e){var t,n;this.initStartDaySelection(e);const s=this.calendar.tryMoveFocus({value:-1,isHorizontal:!1},this.selectedItemElement);if(s.needChangePeriod){if(s.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,null!==(n=s.periodsToSkip)&&void 0!==n?n:-1)}else{const e=null!==(t=s.unitsToSkip)&&void 0!==t?t:-this.rowLength;this.selectedItemIndex>=e&&this.selectItem(this.selectedItemIndex+e),this.extendSelection()}return!0}moveToNextRow(e){var t,n;this.initStartDaySelection(e);const s=this.calendar.tryMoveFocus({value:1,isHorizontal:!1},this.selectedItemElement);if(s.needChangePeriod){if(s.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,null!==(n=s.periodsToSkip)&&void 0!==n?n:1)}else{const e=null!==(t=s.unitsToSkip)&&void 0!==t?t:this.rowLength;this.selectedItemIndex<this.itemCount-e&&this.selectItem(this.selectedItemIndex+e),this.extendSelection()}return!0}handleHomeKey(e){var t;const n=this.getSelectedItemRowIndex()*this.rowLength,s={value:n-this.selectedItemIndex,isHorizontal:!0};this.initStartDaySelection(e);const a=this.calendar.tryMoveFocus(s,this.selectedItemElement);if(a.needChangePeriod){if(a.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,null!==(t=a.periodsToSkip)&&void 0!==t?t:-1)}else{this.selectItem(void 0!==a.unitsToSkip?this.selectedItemIndex+a.unitsToSkip:n),this.extendSelection()}return!0}handleEndKey(e){var t;const n=(this.getSelectedItemRowIndex()+1)*this.rowLength-1,s={value:n-this.selectedItemIndex,isHorizontal:!0};this.initStartDaySelection(e);const a=this.calendar.tryMoveFocus(s,this.selectedItemElement);if(a.needChangePeriod){if(a.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,null!==(t=a.periodsToSkip)&&void 0!==t?t:1)}else{this.selectItem(void 0!==a.unitsToSkip?this.selectedItemIndex+a.unitsToSkip:n),this.extendSelection()}return!0}handlePageUpKey(e){const t={value:-(e.shiftKey?se.getYearOffsetInDaysFrom(-1,this.selectedItemElement):se.getMonthOffsetInDaysFrom(-1,this.selectedItemElement)),isHorizontal:!0};this.initStartDaySelection(e);const n=this.calendar.tryMoveFocus(t,this.selectedItemElement);if(n.needChangePeriod){if(n.newPeriodFitsIntoVisibleRange)return this.applyCalendarFocus(),!1;super.handlePageUpKey(e,n.periodsToSkip)}else this.applyCalendarFocus(),this.selectItem(this.selectedItemIndex);return!0}handlePageDownKey(e){const t={value:e.shiftKey?se.getYearOffsetInDaysFrom(1,this.selectedItemElement):se.getMonthOffsetInDaysFrom(1,this.selectedItemElement),isHorizontal:!0};this.initStartDaySelection(e);const n=this.calendar.tryMoveFocus(t,this.selectedItemElement);if(n.needChangePeriod){if(n.newPeriodFitsIntoVisibleRange)return this.applyCalendarFocus(),!1;super.handlePageDownKey(e,n.periodsToSkip)}else this.applyCalendarFocus(),this.selectItem(this.selectedItemIndex);return!0}extendSelection(){null!==this.daySelectionRenderer&&(this.daySelectionRenderer.pointerUpDate=se.getDateFromCell(this.selectedItemElement))}handleItemClick(e){const t=se.getDateFromCell(this.selectedItemElement);this.calendar.tryCreateRangeSelectionRenderer(t),this.prepareAndPerformShortcutEvent(e,{date:t})}switchCurrentMonth(e,t){this.prepareAndPerformShortcutEvent(e,{offset:t,date:this.calendar.focusedDate})}}class $ extends z{get rowLength(){return 4}moveToPrevDay(e){var t;const n=this.calendar.tryMoveFocus({value:-1,isHorizontal:!0},this.selectedItemElement);if(n.needChangePeriod){if(n.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,-1)}else{const e=null!==(t=n.unitsToSkip)&&void 0!==t?t:-1;this.selectedItemIndex>=e&&this.selectItem(this.selectedItemIndex+e)}return!0}moveToNextDay(e){var t;const n=this.calendar.tryMoveFocus({value:1,isHorizontal:!0},this.selectedItemElement);if(n.needChangePeriod){if(n.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,1)}else{const e=null!==(t=n.unitsToSkip)&&void 0!==t?t:1;this.selectedItemIndex<this.itemCount-e&&this.selectItem(this.selectedItemIndex+e)}return!0}moveToPrevRow(e){var t;const n=this.calendar.tryMoveFocus({value:-1,isHorizontal:!1},this.selectedItemElement);if(n.needChangePeriod){if(n.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,-1)}else{const e=null!==(t=n.unitsToSkip)&&void 0!==t?t:-this.rowLength;this.selectedItemIndex>=e&&this.selectItem(this.selectedItemIndex+e)}return!0}moveToNextRow(e){var t;const n=this.calendar.tryMoveFocus({value:1,isHorizontal:!1},this.selectedItemElement);if(n.needChangePeriod){if(n.newPeriodFitsIntoVisibleRange)return!1;this.switchCurrentMonth(e,1)}else{const e=null!==(t=n.unitsToSkip)&&void 0!==t?t:this.rowLength;this.selectedItemIndex<this.itemCount-e&&this.selectItem(this.selectedItemIndex+e)}return!0}handleHomeKey(e){const t=this.getIndexOfFirstAccessibleItemInCurrentView(),n=this.calendar.tryMoveFocus({value:t-this.selectedItemIndex,isHorizontal:!0},this.selectedItemElement);if(n.needChangePeriod){if(n.newPeriodFitsIntoVisibleRange)return!1}else{this.selectItem(void 0!==n.unitsToSkip?this.selectedItemIndex+n.unitsToSkip:t)}return!0}handleEndKey(e){const t=this.getIndexOfLastAccessibleItemInCurrentView(),n=this.calendar.tryMoveFocus({value:t-this.selectedItemIndex,isHorizontal:!0},this.selectedItemElement);if(n.needChangePeriod){if(n.newPeriodFitsIntoVisibleRange)return!1}else{this.selectItem(void 0!==n.unitsToSkip?this.selectedItemIndex+n.unitsToSkip:t)}return!0}handlePageUpKey(e){const t=this.calendar.tryMoveFocus({value:-(this.calendar.currentView===k.Year?E:10),isHorizontal:!0},this.selectedItemElement);if(t.needChangePeriod){if(t.newPeriodFitsIntoVisibleRange)return!1;super.handlePageUpKey(e,t.periodsToSkip)}return!0}handlePageDownKey(e){const t=this.calendar.tryMoveFocus({value:this.calendar.currentView===k.Year?E:10,isHorizontal:!0},this.selectedItemElement);if(t.needChangePeriod){if(t.newPeriodFitsIntoVisibleRange)return!1;super.handlePageDownKey(e,t.periodsToSkip)}return!0}handleItemClick(e){this.calendar.scheduleFocusRestoration(),this.prepareAndPerformShortcutEvent(e,{row:this.getSelectedItemRowIndex(),column:this.getSelectedItemColumnIndex()})}switchCurrentMonth(e,t){this.prepareAndPerformShortcutEvent(e,{offset:t})}getIndexOfFirstAccessibleItemInCurrentView(){let e=0;for(;e<=this.selectedItemIndex&&!this.itemBelongsToCurrentView(e);)e++;return e}getIndexOfLastAccessibleItemInCurrentView(){let e=this.itemCount-1;for(;e>=this.selectedItemIndex&&!this.itemBelongsToCurrentView(e);)e--;return e}itemBelongsToCurrentView(e){var t;return!(null===(t=this.getItem(e))||void 0===t?void 0:t.classList.contains(I.NotCurrentView))}}class q extends ${get currentCalendarView(){return k.Year}get itemSelector(){return`:scope .${I.YearRow} > td`}}class j extends ${get currentCalendarView(){return k.Decade}get itemSelector(){return`:scope .${I.DecadeRow} > td:not(.${n.Invisible})`}}class W extends ${get currentCalendarView(){return k.Century}get itemSelector(){return`:scope .${I.CenturyRow} > td:not(.${n.Invisible})`}}class X extends A{constructor(e,t){super(e,t),this.itemSelector=`:scope > .${n.Button}`}getShortcutContext(){return{AreaType:B.Footer,ButtonType:this.selectedItemIndex}}queryItems(){return this.queryItemsBySelector(this.itemSelector)}activate(){this.selectedItemIndex=this.isFocusedFromOutside()?this.itemCount-1:0,super.activate()}handleKeyDown(e){switch(f.KeyUtils.getEventKeyCode(e)){case f.KeyCode.Tab:return e.shiftKey?this.moveToPrevItem():this.moveToNextItem();case f.KeyCode.Space:case f.KeyCode.Enter:return this.performShortcutEvent(e),!0;case f.KeyCode.Left:case f.KeyCode.Up:case f.KeyCode.Home:case f.KeyCode.PageUp:case f.KeyCode.Right:case f.KeyCode.Down:case f.KeyCode.End:case f.KeyCode.PageDown:return!0}return super.handleKeyDown(e)}moveToPrevItem(){return this.selectedItemIndex>0&&(this.selectItem(this.selectedItemIndex-1),!0)}moveToNextItem(){return this.selectedItemIndex<this.itemCount-1&&(this.selectItem(this.selectedItemIndex+1),!0)}}class J extends A{constructor(e){super(e,e),this._isFirstFocus=!0}updateSelectedItemByChildElement(e,t=null){super.updateSelectedItemByChildElement(e,t),this.calendar.hasAttribute(T.selectDateOnFirstFocus)&&this._isFirstFocus&&(this._isFirstFocus=!1,this.selectedItemIndex=1)}queryItems(){return new Array(this.getHeader(),...this.getAllDataTableContainers(),this.getFooter())}activate(){this.isDateTableStrategy(this.getItemStrategy(this.selectedItemIndex))&&(this.selectedItemIndex=this.findActiveDataTableStrategyIndex()),super.activate()}createItemStrategy(e){if(e.matches(b.Header))return new O(this.calendar,e);if(e.matches(b.DateTableContainer))return this.createDataTableStrategy(e);if(e.matches(b.Footer))return new X(this.calendar,e);throw new Error("Not implemented")}createDataTableStrategy(e){switch(e.viewType){case k.Month:return new L(this.calendar,e);case k.Year:return new q(this.calendar,e);case k.Decade:return new j(this.calendar,e);case k.Century:return new W(this.calendar,e)}}handleKeyDown(e){switch(f.KeyUtils.getEventKeyCode(e)){case f.KeyCode.Tab:return this.handleTab(e);case f.KeyCode.Up:return this.handleArrowUp(e);case f.KeyCode.Down:return this.handleArrowDown(e);case f.KeyCode.Left:case f.KeyCode.Home:case f.KeyCode.PageUp:return this.handleMove(e,!1);case f.KeyCode.Right:case f.KeyCode.End:case f.KeyCode.PageDown:return this.handleMove(e,!0)}return super.handleKeyDown(e)}handleTab(e){if(this.isDateTableStrategy(this.getSelectedItemStrategy())){const t=this.findNotDataTableStrategyIndex(!e.shiftKey);if(-1!==t)return this.selectItem(t),!0;this.updateDataTableStrategiesFocusableElements(),this.selectedItemIndex=e.shiftKey?0:this.itemCount-1}else{if(this.isDateTableStrategy(this.getItemStrategy(this.selectedItemIndex+(e.shiftKey?-1:1))))return this.updateDataTableStrategiesFocusableElements(),this.selectItem(this.findActiveDataTableStrategyIndex()),!0}return e.shiftKey?(this.selectedItemIndex>0?this.moveToPrevItem():(this.leaveBackward(),this.dispatchKeyboardNavigationLeavedEvent()),!0):this.selectedItemIndex<this.itemCount-1?(this.moveToNextItem(),!0):super.handleKeyDown(e)}handleArrowUp(e){return e.ctrlKey?(this.calendar.scheduleFocusRestoration(),this.performShortcutEvent(e),!0):this.handleMove(e,!1)}handleArrowDown(e){return e.ctrlKey?(this.calendar.scheduleFocusRestoration(),this.performShortcutEvent(e),!0):this.handleMove(e,!0)}handleMove(e,t){return this.isAnyModifierKeyPressed(e)?super.handleKeyDown(e):(t?this.moveToNextItem():this.moveToPrevItem(),!0)}dispatchKeyboardNavigationLeavedEvent(){setTimeout((()=>this.targetElement.dispatchEvent(new P)),0)}isAnyModifierKeyPressed(e){return e.shiftKey||e.ctrlKey||e.metaKey||e.altKey}findNotDataTableStrategyIndex(e){if(e){for(let e=this.selectedItemIndex+1;e<this.itemCount;e++)if(!this.isDateTableStrategy(this.getItemStrategy(e)))return e}else for(let e=this.selectedItemIndex-1;e>=0;e--)if(!this.isDateTableStrategy(this.getItemStrategy(e)))return e;return-1}findActiveDataTableStrategyIndex(){for(let e=0;e<this.itemCount;e++){const t=this.getItemStrategy(e);if(this.isDateTableStrategy(t)&&t.isActive())return e}return 0}updateDataTableStrategiesFocusableElements(){for(let e=0;e<this.itemCount;e++){const t=this.getItemStrategy(e);this.isDateTableStrategy(t)&&t.updateSelectedItemFocusableState()}v.isActivated&&v.updateSubscriptions()}isDateTableStrategy(e){return e instanceof z}}class G{get focusedItem(){return this._focusedItem}set focusedItem(e){this._focusedItem=e}get focusedElement(){return this._focusedElement}set focusedElement(e){this._focusedElement=e}get minDate(){return this._minDate}set minDate(e){this._minDate=e}get maxDate(){return this._maxDate}set maxDate(e){this._maxDate=e}get calendar(){return this._calendar}constructor(e){this.boundCalendarMinMaxDatesChangedHandler=this.updateMinMaxDates.bind(this),this._calendar=e,this._focusedElement=null,this._minDate=this._calendar.minDate,this._maxDate=this._calendar.maxDate,this.calendar.onMinMaxDatesChanged(this.boundCalendarMinMaxDatesChangedHandler)}get calendarCurrentViewDate(){return this.calendar.currentViewDate}getUpdatedElementToFocus(){return this.focusedElement=this.findCellElementByItem(this.getUpdatedItemToFocus()),this.focusedElement}updateMinMaxDates(e,t){e&&(this._minDate=e),t&&(this._maxDate=t)}}class Q extends G{tryMoveFocus(e,t){const n=se.getMonthFromCell(t);this.focusedItem=this.getMonthRelativeTo(e,n),void 0!==this.focusedItem&&(this.focusedItem=this.applyMinMaxDates(this.focusedItem)),this.focusedElement=this.findCellElementByItem(this.focusedItem);const s=this.areMonthsDifferByYear(e,n);return{needChangePeriod:s,periodsToSkip:s?e.value:void 0,unitsToSkip:this.focusedItem&&n?this.focusedItem-n:void 0}}getUpdatedItemToFocus(){if(void 0===this.focusedItem){const e=this.calendar.findSelectedCellElement();this.focusedItem=e?se.getMonthFromCell(e):this.calendarCurrentViewDate.getMonth()}return this.focusedItem&&(this.focusedItem=this.applyMinMaxDates(this.focusedItem)),this.focusedItem}findCellElementByItem(e){return void 0===e?null:this.calendar.currentTableContainer.querySelector(`td[${w.dataMonth}="${Y[e]}"]`)}applyMinMaxDates(e){return this.calendarCurrentViewDate.getFullYear()===this.minDate.getFullYear()&&e<this.minDate.getMonth()?this.minDate.getMonth():this.calendarCurrentViewDate.getFullYear()===this.maxDate.getFullYear()&&e>this.maxDate.getMonth()?this.maxDate.getMonth():e}getMonthRelativeTo(e,t){if(void 0===t)return;const n=(this.getRawIndex(e,t)%E+E)%E;return Y[Y[n%E]]}areMonthsDifferByYear(e,t){if(void 0===t)return!1;const n=this.getRawIndex(e,t);return n<0||n>=E}getEffectiveOffset(e){return e.value*(e.isHorizontal?1:4)}getRawIndex(e,t){return t+this.getEffectiveOffset(e)}}class Z extends G{tryMoveFocus(e,t){const n=se.getYearFromCell(t);this.focusedItem=this.getYearRelativeTo(e,n),void 0!==this.focusedItem&&(this.focusedItem=this.applyMinMaxDates(this.focusedItem)),this.focusedElement=this.findCellElementByItem(this.focusedItem);const s=this.areYearsDifferByDecade(this.calendarCurrentViewDate.getFullYear(),this.focusedItem);return{needChangePeriod:s,periodsToSkip:s?e.value:void 0,unitsToSkip:this.focusedItem&&n?this.focusedItem-n:void 0}}getUpdatedItemToFocus(){if(void 0===this.focusedItem){const e=this.calendar.findSelectedCellElement();this.focusedItem=e?se.getYearFromCell(e):this.calendarCurrentViewDate.getFullYear()}return this.focusedItem&&(this.focusedItem=this.applyMinMaxDates(this.focusedItem)),this.focusedItem}findCellElementByItem(e){return void 0===e?null:this.calendar.currentTableContainer.querySelector(`td[${w.dataYear}="${e}"]`)}applyMinMaxDates(e){return e<this.minDate.getFullYear()?this.minDate.getFullYear():e>this.maxDate.getFullYear()?this.maxDate.getFullYear():e}getYearRelativeTo(e,t){if(void 0!==t)return t+this.getEffectiveOffset(e)}getEffectiveOffset(e){return e.value*(e.isHorizontal?1:4)}areYearsDifferByDecade(e,t){return void 0!==e&&void 0!==t&&Math.floor(e/10)!==Math.floor(t/10)}}class ee extends G{tryMoveFocus(e,t){const n=se.getDateFromCell(t);this.focusedItem=this.applyMinMaxDates(this.getDateRelativeTo(e,t)),this.focusedElement=this.findCellElementByItem(this.focusedItem);const s=this.areDatesDifferByMonthOrYear(this.calendarCurrentViewDate,this.focusedItem);return{needChangePeriod:s,periodsToSkip:s?this.getShortcutOffsetInMonths(this.focusedItem,this.calendarCurrentViewDate):void 0,unitsToSkip:this.focusedItem?Math.round((this.focusedItem.getTime()-n.getTime())/F):void 0}}getUpdatedItemToFocus(){if(this.focusedItem=this.getFindCachedFocusedItem(),this.areDatesDifferByMonthOrYear(this.focusedItem,this.calendarCurrentViewDate)){const e=this.focusedItem.getDate();this.focusedItem=new Date(this.calendarCurrentViewDate);let t=this.trySetDate(this.focusedItem,e);null===t&&(t=this.trySetDate(this.focusedItem,this.calendarCurrentViewDate.getDate())),null===t&&(t=this.trySetDate(this.focusedItem,1)),this.focusedItem=t}return this.focusedItem=this.applyMinMaxDates(this.focusedItem),this.focusedItem}getFindCachedFocusedItem(){let e=this.focusedItem;if(void 0===e){const t=this.calendar.findSelectedCellElement();e=t?se.getDateFromCell(t):this.calendarCurrentViewDate}return e}findCellElementByItem(e){return void 0===e?null:this.calendar.currentTableContainer.querySelector(`.${I.Day}[${w.dataDate}="${e.getTime()}"]`)}applyMinMaxDates(e){return e<this.minDate?new Date(this.minDate):e>this.maxDate?new Date(this.maxDate):new Date(e)}areDatesDifferByMonthOrYear(e,t){return e.getMonth()!==t.getMonth()||this.areDatesDifferByYear(e,t)}areDatesDifferByYear(e,t){return e.getUTCFullYear()!==t.getUTCFullYear()}trySetDate(e,t){const n=new Date(e);return n.setDate(t),this.areDatesDifferByMonthOrYear(n,e)?null:n}getShortcutOffsetInMonths(e,t){return e.getMonth()-t.getMonth()+(e.getUTCFullYear()-t.getUTCFullYear())*E}getDateRelativeTo(e,t){const n=se.getDateFromCell(t),s=new Date(n);return new Date(s.setDate(s.getDate()+e.value*(e.isHorizontal?1:7)))}}class te extends G{tryMoveFocus(e,t){const n=se.getDecadeFromCell(t);this.focusedItem=this.getDecadeRelativeTo(e,n),void 0!==this.focusedItem&&(this.focusedItem=this.applyMinMaxDates(this.focusedItem)),this.focusedElement=this.findCellElementByItem(this.focusedItem);const s=this.areDecadesDifferByCentury(this.getCurrentDecade(),this.focusedItem),a=this.focusedItem&&n?this.subtractDecade(this.focusedItem,n):void 0;return{needChangePeriod:s,periodsToSkip:s?e.value:void 0,unitsToSkip:a}}getUpdatedItemToFocus(){if(void 0===this.focusedItem){const e=this.calendar.findSelectedCellElement();this.focusedItem=e?se.getDecadeFromCell(e):this.getCurrentDecade()}return this.focusedItem&&(this.focusedItem=this.applyMinMaxDates(this.focusedItem)),this.focusedItem}findCellElementByItem(e){return void 0===e?null:this.calendar.currentTableContainer.querySelector(`td[${w.dataDecade}="${e.startYear}-${e.endYear}"]`)}applyMinMaxDates(e){return-1===this.compareDecades(e,this.getDecadeOfDate(this.minDate))?this.getDecadeOfDate(this.minDate):1===this.compareDecades(e,this.getDecadeOfDate(this.maxDate))?this.getDecadeOfDate(this.maxDate):e}getDecadeRelativeTo(e,t){if(void 0===t)return;const n=t.startYear+this.getEffectiveOffset(e);return{startYear:n,endYear:n+9}}getEffectiveOffset(e){return e.value*(e.isHorizontal?1:4)*10}areDecadesDifferByCentury(e,t){if(void 0===(null==e?void 0:e.startYear)||void 0===(null==t?void 0:t.startYear))return!1;return Math.floor(e.startYear/100)!==Math.floor(t.startYear/100)}getCurrentDecade(){return this.getDecadeOfDate(this.calendarCurrentViewDate)}getDecadeOfDate(e){const t=e.getFullYear(),n=10*Math.floor(t/10);return{startYear:n,endYear:n+9}}compareDecades(e,t){return e.startYear<t.startYear?-1:e.startYear===t.startYear?0:1}subtractDecade(e,t){return(e.startYear-t.startYear)/10}}const ne=document.createElement("template");ne.innerHTML="\n<style></style>\n<slot />";class se extends t{constructor(){super(),this.boundOnStartDaySelectionHandler=this.onStartDaySelection.bind(this),this.boundOnStopDaySelectionHandler=this.onStopDaySelection.bind(this),this.boundOnCancelDaySelectionHandler=this.onCancelDaySelection.bind(this),this.boundOnDayCellPointerEnterHandler=this.onDayCellPointerEnter.bind(this),this._enableMultiSelect=!1,this._enableRangeSelect=!1,this._hasNullStartXorEndDate=!1,this._visiblePeriodsCount=1,this._currentViewIndex=0,this._focusedCellElement=null,this._focusRestorationScheduled=!0,this._needImmediateSelectionAfterFocusRestored=!0,this._tableContainers=new Array,this._minDate=x,this._maxDate=M,this._daySelectionRenderer=null,this.calendarTableResizeObservers=new Map,this.focusedDayChangedHandlers=new Set,this.minMaxDatesChangedHandlers=new Set}get enableMultiSelect(){return this._enableMultiSelect}set enableMultiSelect(e){this._enableMultiSelect=e}get enableRangeSelect(){return this._enableRangeSelect}set enableRangeSelect(e){this._enableRangeSelect=e}get hasNullStartXorEndDate(){return this._hasNullStartXorEndDate}set hasNullStartXorEndDate(e){this._hasNullStartXorEndDate=e}get visiblePeriodsCount(){return this._visiblePeriodsCount}set visiblePeriodsCount(e){this._visiblePeriodsCount=e}get currentViewDate(){return this.currentTableContainer.viewDate}get baseViewDate(){return this.baseTableContainer.viewDate}get currentView(){var e,t;return null!==(t=null===(e=this.baseTableContainer)||void 0===e?void 0:e.viewType)&&void 0!==t?t:k.Month}get focusedDate(){return this._focusStrategies[k.Month].getFindCachedFocusedItem()}get enumerateTableContainers(){return this._tableContainers||this.registerTableContainers(),this._tableContainers}get currentTableContainer(){return this._tableContainers[this._currentViewIndex]}get baseTableContainer(){return this._baseTableContainer||(this._baseTableContainer=this.querySelector(b.FirstDataTableContainer)),this._baseTableContainer}get tableContainerWrapper(){return this._tableContainerWrapper||(this._tableContainerWrapper=this.querySelector(b.DataTableContainerWrapperClassName)),this._tableContainerWrapper}get minDate(){return this._minDate}set minDate(e){this._minDate=e}get maxDate(){return this._maxDate}set maxDate(e){this._maxDate=e}get daySelectionRenderer(){return this._daySelectionRenderer}connectedCallback(){super.connectedCallback(),this.addEventSubscriptions()}initializeComponent(){super.initializeComponent(),this._focusStrategies={[k.Month]:new ee(this),[k.Year]:new Q(this),[k.Decade]:new Z(this),[k.Century]:new te(this)},this.fixCalendarTableSize(),this.registerTableContainers(),this.initializeKeyboardNavigator(),this.tryInitializeRangeSelectionSubscriptions()}disposeComponent(){this.removeEventSubscriptions(),this.focusedDayChangedHandlers.clear(),this.minMaxDatesChangedHandlers.clear(),super.disposeComponent()}disconnectedCallback(){this.calendarTableResizeObservers.forEach((e=>e.disconnect())),super.disconnectedCallback()}registerTableContainers(){this._tableContainers=Array.from(this.querySelectorAll(b.DateTableContainer))}fixCalendarTableSize(){if(this.currentView!==k.Month)return;const e=this.getTableElements();e.length&&e.forEach((e=>{var t;if(!e)return;e.style.width="",e.style.height="";const n=e.getBoundingClientRect();D.fromDomRect(n).isEmpty?(this.calendarTableResizeObservers.set(e,new ResizeObserver(this.onCalendarTableSizeChanged.bind(this))),null===(t=this.calendarTableResizeObservers.get(e))||void 0===t||t.observe(e)):this.applyTableSize(e,n.width,n.height)}))}applyTableSize(e,t,n){e.style.width=t+"px",e.style.height=n+"px"}onCalendarTableSizeChanged(e){var t;if(e.length<1)return;const n=e[0].target,s=e[0].contentRect;D.fromDomRect(s).isEmpty||(this.applyTableSize(n,s.width,s.height),null===(t=this.calendarTableResizeObservers.get(n))||void 0===t||t.disconnect())}addEventSubscriptions(){this.addEventListener(o.eventName,this.boundOnStartDaySelectionHandler),this.addEventListener(l.eventName,this.boundOnStopDaySelectionHandler),this.addEventListener(d.eventName,this.boundOnCancelDaySelectionHandler),this.enableRangeSelect&&this.addEventListener(c.eventName,this.boundOnStartDaySelectionHandler)}removeEventSubscriptions(){this.removeEventListener(o.eventName,this.boundOnStartDaySelectionHandler),this.removeEventListener(l.eventName,this.boundOnStopDaySelectionHandler),this.removeEventListener(d.eventName,this.boundOnCancelDaySelectionHandler),this.removeEventListener(c.eventName,this.boundOnStartDaySelectionHandler)}addDayCellEventSubscriptions(){const e=this.findDayCellElements();for(let t=0;t<e.length;t++){e[t].addEventListener("pointerenter",this.boundOnDayCellPointerEnterHandler)}}removeDayCellEventSubscriptions(){const e=this.findDayCellElements();for(let t=0;t<e.length;t++){e[t].removeEventListener("pointerenter",this.boundOnDayCellPointerEnterHandler)}}tryInitializeRangeSelectionSubscriptions(){if(!this.hasNullStartXorEndDate)return;const e=this.findSelectedCellElement();if(!e)return;const t=se.getDateFromCell(e);t&&(this.createDaySelectionRenderer(t,!0),this._daySelectionRenderer&&!se.isDateCellDisabled(e)&&(this._daySelectionRenderer.pointerUpDate=t),this.addDayCellEventSubscriptions())}onStartDaySelection(t){if(e.DomUtils.hasClassName(this,n.Disabled)||e.DomUtils.hasClassName(this,n.ReadOnly))return;const s=se.getDayCellByTarget(t.detail.source.target),a=se.getDateFromTarget(t.detail.source.target);if(!s||!a)return;this.createDaySelectionRenderer(a,!t.detail.isLongTap&&(!(t.detail.source.ctrlKey||t.detail.source.metaKey)||t.detail.isTouch)),this._daySelectionRenderer&&!se.isDateCellDisabled(s)&&(this._daySelectionRenderer.pointerUpDate=se.getDateFromCell(s)),this.addDayCellEventSubscriptions(),t.stopPropagation()}onStopDaySelection(e){if(!this._daySelectionRenderer)return;const t=se.getDayCellByTarget(e.detail.source.target);t&&!se.isDateCellDisabled(t)&&(this._daySelectionRenderer.pointerUpDate=se.getDateFromCell(t)),this.stopSelectingDays(),this.removeDayCellEventSubscriptions(),e.stopPropagation()}onCancelDaySelection(e){this._daySelectionRenderer&&(this._daySelectionRenderer.cancel(),this.removeDayCellEventSubscriptions(),this._daySelectionRenderer=null,e.stopPropagation())}stopSelectingDays(){this._daySelectionRenderer&&(this._daySelectionRenderer.stop(),this._daySelectionRenderer=null)}invokeDayCellPointerUp(e,t,n){this.dispatchEvent(new R(e,t,n))}onDayCellPointerEnter(e){const t=se.getDayCellByTarget(e.target);this._daySelectionRenderer&&t&&!se.isDateCellDisabled(t)&&(this._daySelectionRenderer.pointerUpDate=se.getDateFromCell(t))}tryCreateRangeSelectionRenderer(e){this.enableRangeSelect&&(this._daySelectionRenderer=this.createDateRangeSelectionRenderer(e,!1))}createDaySelectionRenderer(e,t){this._daySelectionRenderer=this.enableRangeSelect?this.createDateRangeSelectionRenderer(e,t):this.enableMultiSelect?this.createMultipleDaySelectionRenderer(e,t):this.createSingleDaySelectionRenderer(e,t)}createMultipleDaySelectionRenderer(e,t){return new K({pointerDownDate:e,clearSelection:t,dayCellElements:this.findDayCellElements()},this.invokeDayCellPointerUp.bind(this))}createSingleDaySelectionRenderer(e,t){return new H(e,t,this.invokeDayCellPointerUp.bind(this))}createDateRangeSelectionRenderer(e,t){return new N({pointerDownDate:e,clearSelection:t,dayCellElements:this.findDayCellElements()},this.invokeDayCellPointerUp.bind(this))}findDayCellElements(){return this.querySelectorAll(b.Day)}findSelectedCellElement(){return this.querySelector(b.SelectedItem)}getMainElement(){return this.querySelector(b.MainElement)}getHeaderElement(){return this.querySelector(b.Header)}getTableElements(){return Array.from(this.querySelectorAll(b.DataTable))}getFooterElement(){return this.querySelector(b.Footer)}initializeKeyboardNavigator(){this.keyboardNavigator=this.querySelector(C),this.keyboardNavigator&&!this.keyboardNavigator.initialized&&this.keyboardNavigator.initialize(this,new J(this))}getKeyboardNavigator(){return this.keyboardNavigator}getFocusedCellElementInView(e){return this._focusStrategies[e].getUpdatedElementToFocus()}tryMoveFocus(e,t){const n=this._focusStrategies[this.currentView].tryMoveFocus(e,t);if(n.needChangePeriod&&n.periodsToSkip){this.scheduleFocusRestoration();const e=this._currentViewIndex+(this.currentView===k.Month?n.periodsToSkip:n.periodsToSkip>0?1:-1);e>=0&&e<this._visiblePeriodsCount&&(this._currentViewIndex=e,n.newPeriodFitsIntoVisibleRange=!0)}return n}scheduleFocusRestoration(e=!0){this._focusRestorationScheduled=!0,this._needImmediateSelectionAfterFocusRestored=e}restoreFocus(){const e=this._focusStrategies[this.currentView].getUpdatedElementToFocus();this._focusRestorationScheduled&&(this.tryFireFocusedDayChanged(e),this.daySelectionRenderer&&e&&(this.daySelectionRenderer.pointerUpDate=se.getDateFromCell(e)),this._focusRestorationScheduled=!1,this._needImmediateSelectionAfterFocusRestored=!1)}onFocusedDayChanged(e){this.focusedDayChangedHandlers.add(e)}offFocusedDayChanged(e){this.focusedDayChangedHandlers.delete(e)}onMinMaxDatesChanged(e){this.minMaxDatesChangedHandlers.add(e)}tryFireFocusedDayChanged(e){if(null!==e)for(const t of this.focusedDayChangedHandlers)t(e,this._needImmediateSelectionAfterFocusRestored)}fireMinMaxDatesChanged(e,t){for(const n of this.minMaxDatesChangedHandlers)n(e,t)}static getDateFromTarget(e){const t=se.getDayCellByTarget(e);return t?se.getDateFromCell(t):null}static getDayCellByTarget(t){const n=t;return n?e.DomUtils.hasClassName(n,I.Day)?n:n.closest(b.Day):null}static getDateFromCell(e){return new Date(Number(e.getAttribute(w.dataDate)))}static getMonthFromCell(e){const t=e.getAttribute(w.dataMonth);if(t)return Y[t]}static getYearFromCell(e){const t=e.getAttribute(w.dataYear);if(t)return Number(t)}static getDecadeFromCell(e){const t=e.getAttribute(w.dataDecade);if(!t)return;const[n,s]=t.split("-").map(Number);return{startYear:n,endYear:s}}static getMonthOffsetInDaysFrom(e,t){const n=se.getDateFromCell(t),s=n.getDate(),a=new Date(n);a.setDate(1),a.setMonth(a.getMonth()+e);const i=this.getLastDayOfTheMonth(a);return a.setDate(s>i?i:s),Math.round(Math.abs(a.getTime()-n.getTime())/F)}static getYearOffsetInDaysFrom(e,t){const n=se.getDateFromCell(t),s=n.getDate(),a=new Date(n);a.setDate(1),a.setFullYear(a.getFullYear()+e);const i=this.getLastDayOfTheMonth(a);return a.setDate(s>i?i:s),Math.round(Math.abs(a.getTime()-n.getTime())/F)}static getLastDayOfTheMonth(e){return new Date(e.getFullYear(),e.getMonth()+1,0).getDate()}static isDateCellDisabled(e){return e.hasAttribute("data-disabled")}getContentTemplate(){return ne}static get observedAttributes(){return[T.class,T.enableMultiSelect,T.minDate,T.maxDate,T.enableRangeSelect,T.visiblePeriodsCount,T.hasNullStartXorEndDate]}attributeChangedCallback(e,t,n){switch(e){case T.class:this.fixCalendarTableSize();break;case T.enableMultiSelect:this.enableMultiSelect=null!==n;break;case T.minDate:{const e=new Date(Number(n));this.minDate=e>=x?e:x,this.fireMinMaxDatesChanged(this.minDate);break}case T.maxDate:{const e=new Date(Number(n));this.maxDate=e<=M?e:M,this.fireMinMaxDatesChanged(void 0,this.maxDate);break}case T.visiblePeriodsCount:this.visiblePeriodsCount=Number(n);break;case T.enableRangeSelect:this.enableRangeSelect=null!==n,this.removeEventSubscriptions(),this.addEventSubscriptions();break;case T.hasNullStartXorEndDate:this.hasNullStartXorEndDate=null!==n,this.removeDayCellEventSubscriptions(),this.tryInitializeRangeSelectionSubscriptions(),this.removeEventSubscriptions(),this.addEventSubscriptions()}}}customElements.define(u,se),customElements.define(S,V);const ae={loadModule:function(){}},ie=Object.freeze({__proto__:null,DxCalendar:se,default:ae});export{P as C,I as a,ae as c,ie as d};
