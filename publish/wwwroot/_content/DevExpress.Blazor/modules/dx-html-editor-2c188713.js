import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t}from"./devextreme-widget-wrapper-33881f73.js";import{C as i}from"./custom-events-helper-e7f279d3.js";import{C as s}from"./events-a8fe5872.js";import{L as n}from"./logicaltreehelper-67db40f1.js";import{k as a}from"./key-ffa272aa.js";import{F as l}from"./constants-7c047c0d.js";import{D as o}from"./layouthelper-67dd777a.js";import{n as r}from"./property-4ec0b52d.js";import{e as d}from"./custom-element-267f9a21.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./utils-b5b2c8a9.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./point-e4ec110e.js";const c="dxbl-html-editor";class u extends CustomEvent{constructor(e,t,i){super(u.eventName,{detail:new p(e,t,i),bubbles:!0,composed:!0,cancelable:!0})}}u.eventName=c+".state-changed";class h extends CustomEvent{constructor(e){super(h.eventName,{detail:new v(e),bubbles:!0,composed:!0,cancelable:!0})}}h.eventName=c+".show-hyperlink-dialog";class g extends CustomEvent{constructor(e){super(g.eventName,{detail:new w(e),bubbles:!0,composed:!0,cancelable:!0})}}g.eventName=c+".show-image-dialog";class m extends s{constructor(){super(m.eventName)}}m.eventName=c+".show-table-dialog";class b extends CustomEvent{constructor(e){super(b.eventName,{detail:new f(e),bubbles:!0,composed:!0,cancelable:!0})}}b.eventName=c+".value-changed";class p{constructor(e,t,i){this.Format=e,this.ButtonsVisibility=t,this.ReadOnly=i}}class v{constructor(e){this.Data=e}}class w{constructor(e){this.Data=e}}class f{constructor(e){this.Value=e}}i.register(u.eventName,(e=>e.detail)),i.register(b.eventName,(e=>e.detail)),i.register(h.eventName,(e=>e.detail)),i.register(g.eventName,(e=>e.detail)),i.register(m.eventName,(e=>e.detail));class _{static moveFocus(e,t,i){const s=e.querySelectorAll(t);if(s.length<=1)return;const n=Array.from(s).filter((e=>!o.isHidden(e)));if(n.length<=1)return;const a=document.activeElement,l=n.reduce(((e,t,i)=>t.contains(a)?i:e),-1);if(l<0)return;let r,d=l;if(i)if(d++,d>=n.length){const t=this.findFocusableElements(document),i=this.findFocusableElements(e),s=t.indexOf(i[i.length-1]);r=s>=0&&s<t.length-1?t[s+1]:t[0]}else r=n[d];else if(d--,d<0){const t=this.findFocusableElements(document),i=this.findFocusableElements(e),s=t.indexOf(i[0]);r=s>0?t[s-1]:t[t.length-1]}else r=n[d];null==r||r.focus()}static findFocusableElements(e){return Array.from(e.querySelectorAll(l)).filter((e=>e.tabIndex>-1&&e.offsetWidth&&e.offsetHeight&&!o.isHidden(e)))}}const y="silent",I="focusout",C="keydown",k="Normal text";var x;!function(e){e.OnLostFocus="onLostFocus",e.OnDelayedInput="onDelayedInput"}(x||(x={}));let S=class extends t{constructor(){super(...arguments),this._readOnly=!1,this._bindValueMode=x.OnLostFocus,this._inputDelay=500,this._timer=0,this._lastQuillFormat={},this._lastButtonsVisibility="",this._valueChangesQueueTimer=0,this._valueChangesQueue=[],this.isBindingUse=!1}getWidgetModuleName(){return"ui"}getWidgetTypeName(){return"dxHtmlEditor"}createInitOptions(){const e=super.createInitOptions();return e.toolbar=void 0,e.mediaResizing={enabled:!0},e}updateWidgetOptions(e,t){if(1===Object.keys(t).length&&"value"in t){if(e.option("value")===t.value&&this.hideLoadingPanel(),this._valueChangesQueue.length>0){if(this._valueChangesQueue.shift()===t.value)return;this._valueChangesQueue=[]}}super.updateWidgetOptions(e,t),this.updateStates(t)}processSpecialOptions(e){super.processSpecialOptions(e),this.prepareValidationOptions(e),this.prepareResizingOptions(e),this.setBindValueOptions(e)}setBindValueOptions(e){e.bindMarkupMode&&(this._bindValueMode=e.bindMarkupMode,delete e.bindMarkupMode),e.inputDelay&&(this._inputDelay=e.inputDelay,delete e.inputDelay)}prepareValidationOptions(e){"showValidationMessageOnFocus"in e&&(e.validationMessageMode="always",delete e.showValidationMessageOnFocus),!1===e.isValid&&(e.validationStatus="invalid",delete e.isValid),e.validationMessage&&(e.validationError={message:e.validationMessage},delete e.validationMessage)}prepareResizingOptions(e){var t,i;"mediaResizeEnabled"in e&&(e.mediaResizing={enabled:e.mediaResizeEnabled},delete e.mediaResizeEnabled),"tableResizeEnabled"in e&&(e.tableResizing={enabled:e.tableResizeEnabled,minColumnWidth:null!==(t=e.tableColumnMinWidth)&&void 0!==t?t:40,minRowHeight:null!==(i=e.tableRowMinHeight)&&void 0!==i?i:24},delete e.tableResizeEnabled),delete e.tableColumnMinWidth,delete e.tableRowMinHeight}createComponent(e){var t;super.createComponent(e),null===(t=this._widgetPromise)||void 0===t||t.then((t=>{this._widgetInstance=t,this._widgetInstance.getQuillInstance().on("editor-change",this.onEditorChange.bind(this)),this.updateStates(e),this.getWidgetElement().removeAttribute("role")}));const i=this.getContainerToSetStyle();i.addEventListener(I,this.onFocusOut),i.addEventListener(C,this.onKeyDown)}updateStates(e){this.applyHtmlEditorState(e.readOnly)}disposeComponent(){super.disposeComponent();const e=this.getContainerToSetStyle();e.removeEventListener(I,this.onFocusOut),e.removeEventListener(C,this.onKeyDown)}createWidgetHandlers(){return{...super.createWidgetHandlers(),onContentReady:e=>this.hideLoadingPanel(),onValueChanged:e=>this.onValueChanged(e.value),onOptionChanged:e=>this.hideLoadingPanel()}}hideLoadingPanel(){this.changeLoadingPanelVisibility(!1)}setValueInQueue(e){this._valueChangesQueue.push(e),clearTimeout(this._valueChangesQueueTimer),this._valueChangesQueueTimer=setTimeout((()=>{this._valueChangesQueue=[]}),500)}onValueChanged(e){this._bindValueMode===x.OnDelayedInput?(clearTimeout(this._timer),this._timer=setTimeout((()=>{this.isBindingUse&&this.setValueInQueue(e),this.sendValue(e)}),this._inputDelay)):this._value=e}onFocusOut(e){this._bindValueMode!==x.OnLostFocus||void 0===this._value||n.containsElement(this.getContainerToSetStyle(),e.relatedTarget)||(this.sendValue(this._value),this._value=void 0)}onKeyDown(e){if(e.shiftKey&&e.altKey){const t=a.KeyUtils.getEventKeyCode(e);t===a.KeyCode.Down?(this.moveFocus(!0),e.stopPropagation()):t===a.KeyCode.Up&&(this.moveFocus(!1),e.stopPropagation())}}moveFocus(e){_.moveFocus(this.getContainerToSetStyle(),"dxbl-ribbon-internal, .ql-editor",e)}sendValue(e){let t;""!==e&&(t=this.getJsObjectReference(e)),this.dispatchEvent(new b(t))}getJsObjectReference(e){const t=(new TextEncoder).encode(e);return DotNet.createJSStreamReference(t)}onEditorChange(e,...t){"selection-change"===e&&null===t[0]?this._selection=t[1]:!this._readOnly&&this.applyHtmlEditorState()}applyHtmlEditorState(e){const t=!!e,i=this.getQuillFormat(),s=this.modifyFormat(i),n=this.getButtonsVisibility(i);this._readOnly===t&&JSON.stringify(this._lastQuillFormat)===JSON.stringify(i)&&this._lastButtonsVisibility===n||this.dispatchEvent(new u(s,n,t)),this._readOnly=t,this._lastQuillFormat=i,this._lastButtonsVisibility=n}getQuillFormat(){var e,t;const i=null===(e=this._widgetInstance)||void 0===e?void 0:e.getSelection();return i?null===(t=this._widgetInstance)||void 0===t?void 0:t.getFormat(i.index,i.length):{}}modifyFormat(e){const t=this.prepareFormatParameters(e),i=this.getFullFormat(t);return JSON.stringify(i)}getFullFormat(e){return e.header||(e.header=k),e}getButtonsVisibility(e){if(!this._widgetInstance)return"{}";const t=this._widgetInstance.getQuillInstance().history,{undo:i,redo:s}=t.stack,n=this.isTableElement(e);return JSON.stringify({undo:i.length>0,redo:s.length>0,insertTable:!n,insertRowAbove:n,insertRowBelow:n,deleteRow:n,insertColumnLeft:n,insertColumnRight:n,deleteColumn:n,insertHeaderRow:n,deleteTable:n})}isTableElement(e){return!(!e.tableCellLine&&!e.tableHeaderCellLine)}prepareFormatParameters(e){const t={};for(const[i,s]of Object.entries(e))switch(i){case"align":"left"===s?t.alignLeft=!0:"center"===s?t.alignCenter=!0:"right"===s?t.alignRight=!0:t.alignJustify=!0;break;case"list":"ordered"===s?t.orderedList=!0:"bullet"===s&&(t.bulletList=!0);break;case"script":"sub"===s?t.subscript=!0:"super"===s&&(t.superscript=!0);break;case"indent":case"imageSrc":case"background":case"color":case"target":case"link":break;case"code-block":t["code-block"]=!0;break;case"header":t.header=this.getHeaderValue(s);break;default:t[i]=s}return t}parseFormatParameters(e,t){let i,s;switch(e){case"alignLeft":i="align",s=!!t&&"left";break;case"alignCenter":i="align",s=!!t&&"center";break;case"alignRight":i="align",s=!!t&&"right";break;case"alignJustify":i="align",s=!!t&&"justify";break;case"orderedList":i="list",s=!!t&&"ordered";break;case"bulletList":i="list",s=!!t&&"bullet";break;case"subscript":i="script",s=!!t&&"sub";break;case"superscript":i="script",s=!!t&&"super";break;case"increaseIndent":i="indent",s=!!t&&"+1";break;case"decreaseIndent":i="indent",s=!!t&&"-1";break;case"header":s=this.parseHeaderValue(t)}return{formatName:null!=i?i:e,formatValue:null!=s?s:t}}toggleUndoRedo(e){var t,i;"undo"===e?null===(t=this._widgetInstance)||void 0===t||t.undo():null===(i=this._widgetInstance)||void 0===i||i.redo(),this.applyHtmlEditorState()}clearFormat(){var e,t;const i=null===(e=this._widgetInstance)||void 0===e?void 0:e.getSelection();i&&(null===(t=this._widgetInstance)||void 0===t||t.removeFormat(i.index,i.length),this.applyHtmlEditorState())}toggleFontChanges(e,t){var i,s;this.focusEditor(),this._selection&&(null===(i=this._widgetInstance)||void 0===i||i.setSelection(this._selection.index,this._selection.length)),null===(s=this._widgetInstance)||void 0===s||s.format(e,t),this.applyHtmlEditorState()}parseHeaderValue(e){return e!==k&&e.split(" ")[1]}getHeaderValue(e){return e?`Heading ${e}`:k}getLeafSelection(e,t){const i=e.getLeaf(t),s=i[0].text;return{index:t-i[1],length:s?s.length:0}}getTableModule(){var e;const t=null===(e=this._widgetInstance)||void 0===e?void 0:e.getQuillInstance();return t.focus(),t.getModule("table")}insertRowAbove(){this.getTableModule().insertRowAbove()}insertRowBelow(){this.getTableModule().insertRowBelow()}deleteRow(){this.getTableModule().deleteRow()}insertColumnLeft(){this.getTableModule().insertColumnLeft()}insertColumnRight(){this.getTableModule().insertColumnRight()}deleteColumn(){this.getTableModule().deleteColumn()}insertHeaderRow(){this.getTableModule().insertHeaderRow()}deleteTable(){this.getTableModule().deleteTable()}insertVariable(e,t){var i,s;const n=null===(i=this._widgetInstance)||void 0===i?void 0:i.getSelection(),a=n?n.index:0,l=null===(s=this._widgetInstance)||void 0===s?void 0:s.getQuillInstance();n&&l.deleteText(a,n.length,y),this._widgetInstance.insertEmbed(a,"variable",{value:e,escapeChar:t||["{","}"]})}executeCommand(e,t,i){var s;const{formatName:n,formatValue:a}=this.parseFormatParameters(e,t);switch(n){case"undo":case"redo":this.toggleUndoRedo(n);break;case"clear":this.clearFormat();break;case"header":case"size":case"font":this.toggleFontChanges(n,a);break;case"insertRowAbove":this.insertRowAbove();break;case"insertRowBelow":this.insertRowBelow();break;case"deleteRow":this.deleteRow();break;case"insertColumnLeft":this.insertColumnLeft();break;case"insertColumnRight":this.insertColumnRight();break;case"deleteColumn":this.deleteColumn();break;case"insertHeaderRow":this.insertHeaderRow();break;case"deleteTable":this.deleteTable();break;case"variable":this.insertVariable(t,i);break;default:null===(s=this._widgetInstance)||void 0===s||s.format(n,a),this.applyHtmlEditorState()}}async applyPicture(e,t,i,s){var n,a,l,o;const r=null===(n=this._widgetInstance)||void 0===n?void 0:n.getFormat(),d=null===(a=this._widgetInstance)||void 0===a?void 0:a.getSelection(),c=null===(l=this._widgetInstance)||void 0===l?void 0:l.getQuillInstance(),u=await this.decodeStream(e);let h=null!==(o=d.index)&&void 0!==o?o:0;r.imageSrc&&0===d.length?(h=this.getLeafSelection(c,d.index).index,c.deleteText(h,1,y)):d.length&&c.deleteText(h,d.length,y),this._widgetInstance.insertEmbed(h,"extendedImage",{src:u,alt:t,width:i,height:s})}applyHyperlink(e,t,i){var s,n,a,l,o;const r=null===(s=this._widgetInstance)||void 0===s?void 0:s.getFormat(),d=null===(n=this._widgetInstance)||void 0===n?void 0:n.getSelection(),c=null===(a=this._widgetInstance)||void 0===a?void 0:a.getQuillInstance();if(d.length>0&&this.hasEmbeddedElement(d))null===(l=this._widgetInstance)||void 0===l||l.format("link",{href:e,target:i});else{const s=0===t.length?e:t;let n=null!==(o=d.index)&&void 0!==o?o:0;if(void 0!==r.link&&0===d.length){const{index:e,length:t}=this.getLeafSelection(c,d.index);n=e,c.deleteText(e,t,y)}else d.length&&c.deleteText(d.index,d.length,y);c.insertText(n,s,"link",{href:e,target:i},"user"),c.setSelection(n+s.length,0)}}insertTable(e,t){this.getTableModule().insertTable(e,t)}showDialog(e){switch(e){case"ShowHyperlinkDialog":this.passHyperlinkDialogData();break;case"ShowInsertPictureDialog":this.passImageDialogData();break;case"ShowInsertTableDialog":this.passTableDialogData()}}focusEditor(){var e;null===(e=this._widgetInstance)||void 0===e||e.focus()}getSelectedContent(){if(!this._widgetInstance)return;const{start:e,length:t}=this.getCurrentPosition();return this._widgetInstance.getText(e,t)}insertText(e,t){if(!this._widgetInstance)return;const{start:i,length:s}=this.getCurrentPosition();switch(t){case"after":this._widgetInstance.insertText(i+s,e,y),this._widgetInstance.setSelection(i,s);break;case"before":this._widgetInstance.insertText(i,e,y);break;case"replace":this._widgetInstance.delete(i,s),this._widgetInstance.insertText(i,e,y),this._widgetInstance.setSelection(i,e.length)}}getCurrentPosition(){const e=this._widgetInstance.getSelection(!0);return e&&e.length?{start:e.index,length:e.length}:{start:0,length:this._widgetInstance.getLength()}}hasEmbeddedElement(e){var t;const i=(null===(t=this._widgetInstance)||void 0===t?void 0:t.getQuillInstance()).getContents(e.index,e.length).ops;for(const e of i)if(e.insert.extendedImage||e.insert.variable||e.insert.mention)return!0;return!1}async decodeStream(e){const t=await e.arrayBuffer();return new TextDecoder("utf-8").decode(t)}passHyperlinkDialogData(){var e,t,i;let s={};const n=null===(e=this._widgetInstance)||void 0===e?void 0:e.getFormat(),a=null===(t=this._widgetInstance)||void 0===t?void 0:t.getSelection(),l=null===(i=this._widgetInstance)||void 0===i?void 0:i.getQuillInstance();if(a.length>0&&this.hasEmbeddedElement(a))s={url:n.link,text:null,blank:n.link?!!n.target:void 0};else if(void 0===n.link){s={text:this._widgetInstance.getText(a.index,a.length)}}else{const e=l.getLeaf(a.index),t=this._widgetInstance.getText(a.index,a.length);s={url:n.link,text:0===a.length?e[0].text:t,blank:!!n.target}}this.dispatchEvent(new h(s))}passImageDialogData(){var e;let t={};const i=null===(e=this._widgetInstance)||void 0===e?void 0:e.getFormat();t={stream:i.imageSrc&&this.getJsObjectReference(i.imageSrc),alt:i.alt,width:i.width,height:i.height},this.dispatchEvent(new g(t))}passTableDialogData(){this.dispatchEvent(new m)}};e([r({attribute:"is-binding-use",type:Boolean})],S.prototype,"isBindingUse",void 0),S=e([d("dxbl-html-editor")],S);export{S as DxHtmlEditor};
