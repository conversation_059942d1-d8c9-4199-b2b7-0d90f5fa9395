import{S as t}from"./screenhelper-e9ec6e3e.js";import{a as e,R as i}from"./layouthelper-67dd777a.js";import{D as n,V as c}from"./dx-scroll-viewer-da0fb41c.js";class g{static calculateHorizontalScrollCorrection(t,e,i){let n=0;const c=t.getBoundingClientRect();for(const t of e){const e=t.getBoundingClientRect(),g=this.getIntersectionRectangle(c,e);if(!g.isEmpty){const t=i?g.left-e.right:g.right-e.left;c.x-=t,n+=t}}return n}static calculateBoundaryItemIndex(t,e,i){const n=Math.ceil(this.getDataAreaViewportHeight(e)),c=i?0:t.itemCount-1;let g=i&&t.selectedItemIndex<=c||!i&&t.selectedItemIndex>=c?c:t.selectedItemIndex,l=this.calculateItemHeight(t,e,g,i);const s=this.isVirtualScrollingEnabled(e)?this.calculateItemAverageHeight(t):void 0,o=i?1:-1;let r=g+o;for(;t.isIndexWithinBoundaries(r)&&(l+=this.getElementHeight(t.getItem(r),s),!(l>n&&g!==t.selectedItemIndex));)g=r,r+=o;return g}static isVirtualScrollingEnabled(t){return void 0!==t.totalItemCount}static isElementInView(t,e){const i=e.getBoundingClientRect(),n=t.getBoundingClientRect();return n.bottom>i.top+n.height&&n.top<i.bottom}static bottomAlignmentRequired(t,e){const i=t.getBoundingClientRect();return e.getBoundingClientRect().bottom-i.bottom<i.height}static calculateItemAverageHeight(t){const e=t.getItem(0);return(t.getItem(t.itemCount-1).getBoundingClientRect().bottom-e.getBoundingClientRect().top)/t.itemCount}static calculateItemHeight(t,e,i,n){const c=t.getItem(i);let g=this.getElementHeight(c);return c&&!this.isOutsideViewportItemRequired(t,e,i,n)&&(g+=this.calculateHeightOffsetValue(c,e,n)),g}static isOutsideViewportItemRequired(t,e,i,n){const c=t.getItem(n?i+1:i-1);if(c){const t=c.getBoundingClientRect(),i=e.getElementsRectangle(e.getHeaderElements()),g=e.getElementsRectangle(e.getFooterElements());if(n&&g)return!this.getIntersectionRectangle(t,g).isEmpty;if(!n&&i)return!this.getIntersectionRectangle(t,i).isEmpty;return this.getIntersectionRectangle(t,e.getBoundingClientRect()).height<this.getElementHeight(c)}return!0}static calculateHeightOffsetValue(t,e,i){const g=e.getDataAreaRectangle(),l=g.y,s=g.y+g.height,o=t.getBoundingClientRect();return n.calcScrollOffset(l,s,o.top,o.bottom,i?c.Top:c.Bottom)}static getDataAreaViewportHeight(e){const i=t.viewport(),n=null==e?void 0:e.getRectangle();return n&&n.height<i.height?n.height-this.calcStickyElementsHeight(e):i.height}static calcStickyElementsHeight(t){let e=0;for(const i of this.getStickyElementsRectangles(t))e+=this.getElementRectangleHeight(i);return e}static getStickyElementsRectangles(t){return t?[t.getElementsRectangle(t.getHeaderElements()),t.getElementsRectangle(t.getFooterElements())]:[]}static getElementHeight(t,e=0){return t?t.getBoundingClientRect().height:e}static getElementRectangleHeight(t,e=0){return t?t.height:e}static getIntersectionRectangle(t,n){const c=new e(t.x,t.y,t.width,t.height),g=new e(n.x,n.y,n.width,n.height);return i.intersect(c,g)}}export{g as G};
