class s{constructor(){this.subscribers=[]}subscribe(s,e=!1){-1===this.subscribers.indexOf(s)&&this.subscribers.push(s)}unsubscribe(s){const e=this.subscribers.indexOf(s);-1!==e&&this.subscribers.splice(e,1)}}s.Empty=new s;class e extends s{emit(s){this.subscribers.forEach((e=>e(s)))}}class i extends s{constructor(s){super(),this.isInitialized=1===arguments.length,this.value=s}update(s){this.isInitialized&&s===this.value||(this.isInitialized=!0,this.value=s,this.subscribers.forEach((e=>e(s))))}subscribe(s,e=!1){this.isInitialized&&!e&&s(this.value),super.subscribe(s,e)}asTrigger(s){const e=new i(null);return this.subscribe("function"!=typeof s?i=>e.update(-1!==s.indexOf(i)):i=>e.update(s(i))),e}or(s){const e=new i(null);return this.subscribe((i=>e.update(i||s.value))),s.subscribe((s=>e.update(this.value||s))),e}and(s){const e=new i(null);return this.subscribe((i=>e.update(i&&s.value))),s.subscribe((s=>e.update(this.value&&s))),e}join(s){const e=new i(null);return this.subscribe((i=>e.update([i,s.value]))),s.subscribe((s=>e.update([this.value,s]))),e}}export{e as E,i as S,s as a};
