import{_ as e}from"./tslib.es6-d65164b3.js";import t from"./adaptivedropdowncomponents-7cb91d74.js";import{j as o}from"./constants-da6cacac.js";import{D as s}from"./dx-dropdown-list-box-3ed16714.js";import{E as r}from"./eventhelper-8bcec49f.js";import{n as i}from"./property-4ec0b52d.js";import"./dropdowncomponents-3d8f06da.js";import"./dropdown-f5b2318c.js";import"./popup-355ecaa4.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./rafaction-bba7928b.js";import"./screenhelper-e9ec6e3e.js";import"./transformhelper-ebad0156.js";import"./positiontracker-754c1e75.js";import"./branch-aebd078a.js";import"./custom-element-267f9a21.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./portal-b3727c25.js";import"./data-qa-utils-8be7c726.js";import"./constants-a4904a3f.js";import"./const-90026e45.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./devices-17b9ba08.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./capture-manager-2454adc2.js";import"./focushelper-2eea96ca.js";import"./nameof-factory-64d95f5b.js";import"./custom-events-helper-e7f279d3.js";import"./focustrap-d11cfef9.js";import"./tabbable-a2ae89a6.js";import"./key-ffa272aa.js";import"./keyboard-navigation-strategy-ea41c807.js";import"./dom-utils-d057dcaa.js";import"./css-classes-c63af734.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./thumb-31d768d7.js";import"./query-44b9267f.js";import"./popupportal-bbd2fea0.js";import"./events-interseptor-a522582a.js";import"./modalcomponents-951e20e2.js";import"./dx-dropdown-base3-726be7be.js";import"./masked-input-0c0a9541.js";import"./text-editor-733d5e56.js";import"./single-slot-element-base-01d93921.js";import"./input-66769c52.js";import"./constants-3209ffde.js";import"./dx-scroll-viewer-da0fb41c.js";import"./dx-html-element-base-3262304e.js";import"./scroll-viewer-css-classes-e724f203.js";import"./dx-list-box-events-6c145567.js";import"./dx-virtual-scroll-viewer-f4a3bc9e.js";import"./grid-scroll-utils-a8c65cf1.js";var p;!function(e){e.Input="Input",e.Click="Click"}(p||(p={}));class a extends s{constructor(){super(),this.dropDownTriggerMode=p.Input}connectedCallback(){super.connectedCallback()}disconnectedCallback(){super.disconnectedCallback()}applyTextPropertyCore(){super.applyTextPropertyCore(),this.allowInput||this.fieldElement&&this.fieldText&&""!==this.fieldText&&this.fieldElement.setSelectionRange(0,0)}ensureDropDownOpened(e){if(!this.isDropDownOpen){(e.data&&e.data.length>0||(this.fieldElement?this.fieldElementValue:this.fieldText))&&super.tryOpenDropDown()}}processPointerDown(e){const t=r.containsInComposedPath(e,this.isFieldElement.bind(this));let o=!1;return r.containsInComposedPath(e,this.isEditBoxTemplateElement.bind(this))?o=!0:t&&(o=this.dropDownTriggerMode===p.Click||this.isFieldElementReadonly),o&&this.toggleDropDownVisibility(),super.processPointerDown(e)}}e([i({type:p,attribute:"drop-down-trigger-mode"})],a.prototype,"dropDownTriggerMode",void 0),customElements.define(o,a);const n={loadModule:function(){},adaptiveDropdownComponents:t};export{p as DropDownTriggerMode,a as DxComboBox2,n as default};
