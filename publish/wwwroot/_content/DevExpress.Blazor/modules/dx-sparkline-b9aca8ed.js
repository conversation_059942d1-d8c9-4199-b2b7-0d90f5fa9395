import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t}from"./devextreme-widget-wrapper-33881f73.js";import{C as r}from"./custom-events-helper-e7f279d3.js";import{C as s}from"./events-a8fe5872.js";import{c as o}from"./create-after-timeout-fn-executor-38b3d79d.js";import{e as n}from"./custom-element-267f9a21.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";import"./utils-b5b2c8a9.js";import"./single-slot-element-base-01d93921.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";const i="dxbl-sparkline";class a extends s{constructor(){super(a.eventName)}}a.eventName=i+".drawn";class p extends s{constructor(){super(p.eventName)}}p.eventName=i+".exported",r.register(a.eventName,(e=>e.detail)),r.register(p.eventName,(e=>e.detail));const d="exportTo",c="print",m="svg";let l=class extends t{constructor(){super(),this._drawnExecutor=o(this.onDrawn.bind(this))}getWidgetTypeName(){return"dxSparkline"}createWidgetHandlers(){return{...super.createWidgetHandlers(),onDrawn:()=>this.onDrawn(),onOptionChanged:()=>this.onOptionChanged(),onExported:()=>this.onExported()}}createWidgetDefaultOptions(){return{ignoreEmptyPoints:!0,tooltip:{enabled:!1}}}processSpecialOptions(e){super.processSpecialOptions(e),e.dataSource&&(e.dataSource=this._prepareDataSource(e.dataSource))}getContainerToSetStyle(){return this.getWidgetElement()}_prepareDataSource(e){const t=e.arg||[],r=e.val||[],s=[];for(let e=0;e<t.length;e++)s.push({arg:t[e],val:r[e]});return s}onDrawn(){this._drawnExecutor.reset(),this.changeLoadingPanelVisibility(!1),this.dispatchEvent(new a)}onExported(){this.dispatchEvent(new p)}onOptionChanged(){this._drawnExecutor.execute()}onlyContainerSizeChanged(){var e;null===(e=this._widgetPromise)||void 0===e||e.then((e=>{e.render()})),this.onDrawn()}getSkippedEmptyOptions(){return["dataSource"]}[d](...e){return this.executeClientMethod(d,...e)}[c](...e){return this.executeClientMethod(c,...e)}[m](...e){return this.executeClientMethod(m,...e)}};l=e([n("dxbl-sparkline")],l);export{l as DxSparkline};
