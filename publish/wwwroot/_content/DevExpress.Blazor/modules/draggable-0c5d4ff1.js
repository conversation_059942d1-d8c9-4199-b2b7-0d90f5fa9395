import{ThumbDragStartedEvent as t,ThumbDragDeltaEvent as e,ThumbDragCompletedEvent as s}from"./thumb-31d768d7.js";import{E as a}from"./eventhelper-8bcec49f.js";import{D as i}from"./data-qa-utils-8be7c726.js";import{L as r,D as d}from"./layouthelper-67dd777a.js";import{R as l}from"./rafaction-bba7928b.js";import{T as n}from"./transformhelper-ebad0156.js";import"./point-e4ec110e.js";import"./browser-3fc721b7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./constants-7c047c0d.js";const o="dxbl-draggable";class h extends HTMLElement{get left(){return this.leftField}set left(t){this.leftField=t}get top(){return this.topField}set top(t){this.topField=t}constructor(){super(),this.dragX=0,this.dragY=0,this.leftField=0,this.topField=0,this.rafAction=new l,this.dragStartedHandler=this.dragStarted.bind(this),this.dragDeltaHandler=this.dragDelta.bind(this),this.dragCompletedHandler=this.dragCompleted.bind(this);this.attachShadow({mode:"closed"}).innerHTML="\n            <style>\n              :host {\n                  position: absolute;\n                  display: block;\n              }\n            </style>\n            <slot/>"}connectedCallback(){i.setLoaded(this),this.addEventListener(t.eventName,this.dragStartedHandler),this.addEventListener(e.eventName,this.dragDeltaHandler),this.addEventListener(s.eventName,this.dragCompletedHandler)}disconnectedCallback(){i.removeLoaded(this)}dragStarted(t){a.markHandled(t);const e=r.findParent(this,(t=>d.isAbsolutePositioning(t))),s=r.getRelativeElementRect(this,e);this.left=s.x,this.top=s.y,this.style.transform=n.translate(this.left,this.top)}dragDelta(t){a.markHandled(t),this.left+=t.detail.deltaX,this.top+=t.detail.deltaY,this.rafAction.execute((()=>this.style.transform=n.translate(this.left,this.top)))}dragCompleted(t){a.markHandled(t),this.rafAction.cancel(),this.style.transform=n.translate(this.left,this.top)}}customElements.define(o,h);const m={init:function(){},DxDraggable:h,dxDraggableTagName:o};export{m as default};
