import{D as e}from"./dx-ui-element-0c1e122f.js";import{P as n}from"./portal-b3727c25.js";import{t as o,u as t}from"./popup-355ecaa4.js";import{E as r}from"./eventhelper-8bcec49f.js";import{e as s,d as l,f as d}from"./logicaltreehelper-67db40f1.js";import{x as i}from"./lit-element-462e7ad3.js";class a extends e{constructor(){super(),this.boundOnDropDownElementChangedHandler=this.onDropDownElementChanged.bind(this),this.boundOnDropDownShownHandler=this.onDropDownShown.bind(this),this.boundOnDropDownClosedHandler=this.onDropDownClosed.bind(this),this.boundOnDropDownPortalElementsChangedHandler=this.onDropDownPortalElementsChanged.bind(this),this.boundSlotChangedHandler=this.onSlotChanged.bind(this),this.dropDownPortal=null,this._dropDownElement=null,this._adaptiveDropDownElement=null}get dropDownElement(){return this._dropDownElement}get useMobileFocusBehaviour(){var e;return(null===(e=this._adaptiveDropDownElement)||void 0===e?void 0:e.adaptivityEnabled)||!1}connectedCallback(){super.connectedCallback(),this.ensureDropDownInfrastructure(),this.addEventListener(n.eventName,this.boundOnDropDownElementChangedHandler)}disconnectedCallback(){this.removeEventListener(n.eventName,this.boundOnDropDownElementChangedHandler),this.disposeDropDownInfrastructure(),super.disconnectedCallback()}render(){return i`
            <slot @slotchange="${this.boundSlotChangedHandler}">
            </slot>
        `}processCapturedKeyDownAsync(e,n){return this.invokeKeyDownServerCommand(e)?(e.preventDefault(),n.handled=!0,Promise.resolve()):super.processCapturedKeyDownAsync(e,n)}processCapturedPointerAsync(e,n){return this.canHandlePointerDown(e)?(n.handled=!0,Promise.resolve()):super.processCapturedPointerAsync(e,n)}canHandlePointerDown(e){return!1}invokeKeyDownServerCommand(e){return!1}onSlotChanged(e){this.disposeDropDownInfrastructure(),this.ensureDropDownInfrastructure()}getDropDownPortal(){if(!this.shadowRoot)return null;const e=this.shadowRoot.querySelector("slot");if(!e)return null;return e.assignedNodes().find((e=>s(e)))}onDropDownElementChanged(e){this.dropDownPortal||this.ensureDropDownInfrastructure(),r.getOriginalSource(e)===this.dropDownPortal&&(this.disposeDropDownElement(),this.ensureDropDownElement())}onDropDownShown(e){this.dropDownElement&&l(this.dropDownElement)&&this.addLogicalChild(this.dropDownElement)}onDropDownClosed(e){this.dropDownElement&&l(this.dropDownElement)&&this.removeLogicalChild(this.dropDownElement)}onDropDownPortalElementsChanged(e){this.ensureDropDownElement()}ensureDropDownInfrastructure(){var e;this.dropDownPortal=this.getDropDownPortal(),null===(e=this.dropDownPortal)||void 0===e||e.addEventListener(n.eventName,this.boundOnDropDownPortalElementsChangedHandler),this.ensureDropDownElement()}ensureDropDownElement(){var e,n,r;this.dropDownPortal&&(this._dropDownElement=null===(e=this.dropDownPortal)||void 0===e?void 0:e.popup,null===(n=this._dropDownElement)||void 0===n||n.addEventListener(o.eventName,this.boundOnDropDownShownHandler),null===(r=this._dropDownElement)||void 0===r||r.addEventListener(t.eventName,this.boundOnDropDownClosedHandler),this._dropDownElement&&d(this._dropDownElement)&&(this._adaptiveDropDownElement=this._dropDownElement))}disposeDropDownInfrastructure(){var e;null===(e=this.dropDownPortal)||void 0===e||e.removeEventListener(n.eventName,this.boundOnDropDownPortalElementsChangedHandler),this.dropDownPortal=null,this._adaptiveDropDownElement=null,this.disposeDropDownElement()}disposeDropDownElement(){var e,n;null===(e=this._dropDownElement)||void 0===e||e.removeEventListener(o.eventName,this.boundOnDropDownShownHandler),null===(n=this._dropDownElement)||void 0===n||n.removeEventListener(t.eventName,this.boundOnDropDownClosedHandler),this._dropDownElement=null}}export{a as D};
