import{T as e}from"./tabbable-a2ae89a6.js";import{L as t}from"./logicaltreehelper-67db40f1.js";import{F as n}from"./focushelper-2eea96ca.js";class s{constructor(e){this.action=null,this.postponeAction=e}executePostpone(e){this.action=null,this.postponeAction()?e():this.action=e}execute(){this.action&&this.postponeAction()&&this.executeForce()}executeForce(){this.action&&this.action(),this.clear()}clear(){this.action=null}}const i={subtree:!0,childList:!0,attributeFilter:["disabled","tabindex"]};class o{constructor(e,t,n,s,i){this.BlockOutsideClick=!1,this.BlockOutsideNavigation=!1,this.NestedOnly=!1,this.BlockOutsideClick=e,this.BlockOutsideNavigation=t,this.Element=n,this.LastFocusedElement=s,this.NestedOnly=i}}const l=new class{constructor(){this.documentFocusInHandler=this.documentFocusIn.bind(this),this.documentKeyDownHandler=this.documentKeyDown.bind(this),this.firstInnerElementKeyDownHandler=this.firstInnerElementKeyDown.bind(this),this.lastInnerElementKeyDownHandler=this.lastInnerElementKeyDown.bind(this),this.handleFocusOutHandler=this.handleFocusOut.bind(this),this.documentKeyboardTrackerHandler=this.documentKeyboardTracker.bind(this),this.documentMouseTrackerHandler=this.documentMouseTracker.bind(this),this.tabbables=[],this.mutationObserver=new MutationObserver(this.handleMutations.bind(this)),this.focusPostponedAction=new s((()=>{var e,t;return null!==(t=(null===(e=this.tabbables)||void 0===e?void 0:e.length)>0)&&void 0!==t&&t})),this.firstInnerElement=null,this.lastInnerElement=null,this._stack=[],this._lastInputFromPointer=!1,document.addEventListener("keydown",this.documentKeyboardTrackerHandler,!0),document.addEventListener("pointerdown",this.documentMouseTrackerHandler,!0)}documentKeyboardTracker(e){this._lastInputFromPointer=!1}documentMouseTracker(e){this._lastInputFromPointer=!0}get isActivated(){return this._stack.length>0}get info(){return this.isActivated?this._stack[this._stack.length-1]:null}get element(){var e,t;return null!==(t=null===(e=this.info)||void 0===e?void 0:e.Element)&&void 0!==t?t:null}get blockOutsideClick(){var e,t;return null!==(t=null===(e=this.info)||void 0===e?void 0:e.BlockOutsideClick)&&void 0!==t?t:null}get blockOutsideNavigation(){var e,t;return null!==(t=null===(e=this.info)||void 0===e?void 0:e.BlockOutsideNavigation)&&void 0!==t?t:null}get lastFocusedElement(){var e,t;return null!==(t=null===(e=this.info)||void 0===e?void 0:e.LastFocusedElement)&&void 0!==t?t:null}get nestedOnly(){var e,t;return null!==(t=null===(e=this.info)||void 0===e?void 0:e.NestedOnly)&&void 0!==t?t:null}filterStack(e){e&&(this._stack=this._stack.filter((t=>t.Element!==e)))}activate(e,t,n=!1,s=t){if("string"==typeof e&&(e=document.querySelector(e)),null==e)throw"element is null";this.element&&this.deactivateCore();const i=document.activeElement;this.filterStack(e),this._stack.push(new o(s,t,e,i,n)),this.activateCore()}activateCore(){this.element&&((this.blockOutsideClick||this.blockOutsideNavigation)&&this.subscribe(this.element),this.tabbables=e.getTabbables(this.element,!this.nestedOnly),this.updateInnerElements(),this.subscribeInnerElementsKeyDown(),this.focusPostponedAction.executePostpone(this.forceFocusFirstElement.bind(this)),this.mutationObserver.observe(this.element,i))}deactivate(e,s){var i;if(!this.isActivated||!e)return;if(this.element!==e)return void this.filterStack(e);this.deactivateCore();const o=null===(i=this._stack.pop())||void 0===i?void 0:i.LastFocusedElement;s&&!t.containsElement(e,document.activeElement)&&o&&n.restoreFocus(o),this.element&&this.activateCore()}deactivateCore(){this.element&&(this.focusPostponedAction.clear(),this.mutationObserver.disconnect(),this.unsubscribeDocument(),this.unsubscribe(this.element),this.unsubscribeInnerElementsKeyDown(),clearTimeout(this.updateTabbablesTimerID))}forceFocusFirstElement(){const e=this.tabbables[0];e&&(this.lastFocusedElement&&this.element&&t.containsElement(this.element,document.activeElement)||n.restoreFocus(e))}handleMutations(e){clearTimeout(this.updateTabbablesTimerID),this.updateTabbablesTimerID=setTimeout(this.updateSubscriptions.bind(this),50)}updateSubscriptions(){this.tabbables=e.getTabbables(this.element,!this.nestedOnly),this.unsubscribeInnerElementsKeyDown(),this.updateInnerElements(),this.subscribeInnerElementsKeyDown(),this.focusPostponedAction.execute()}subscribe(e){this.element.addEventListener("focusout",this.handleFocusOutHandler,!0)}unsubscribe(e){this.element.removeEventListener("focusout",this.handleFocusOutHandler,!0)}handleFocusOut(e){if(this._lastInputFromPointer){if(!this.blockOutsideClick)return}else if(!this.blockOutsideNavigation)return;const t=e.relatedTarget;t?this.element.contains(t)||this.focusPostponedAction.executePostpone(this.forceFocusFirstElement.bind(this)):this.subscribeDocument()}subscribeDocument(){document.addEventListener("focusin",this.documentFocusInHandler),document.addEventListener("keydown",this.documentKeyDownHandler)}unsubscribeDocument(){document.removeEventListener("keydown",this.documentKeyDownHandler),document.removeEventListener("focusin",this.documentFocusInHandler)}documentKeyDown(e){var t;const n=e.target;if(n&&(this.element===n||(null===(t=this.element)||void 0===t?void 0:t.contains(n))))return;const s=this.element;s&&s.processKeyDown(e)}documentFocusIn(e){const t=e.target;if(!t)return;this.unsubscribeDocument();const n=e.relatedTarget;(n||this.element.contains(t))&&(!n||this.element.contains(n))||this.focusPostponedAction.executePostpone(this.forceFocusFirstElement.bind(this))}firstInnerElementKeyDown(e){var t;this.firstInnerElement===document.activeElement&&"Tab"===e.key&&e.shiftKey&&(e.preventDefault(),null===(t=this.lastInnerElement)||void 0===t||t.focus({preventScroll:!1}))}lastInnerElementKeyDown(e){var t;this.lastInnerElement!==document.activeElement||"Tab"!==e.key||e.shiftKey||(e.preventDefault(),null===(t=this.firstInnerElement)||void 0===t||t.focus({preventScroll:!1}))}unsubscribeInnerElementsKeyDown(){var e,t;null===(e=this.firstInnerElement)||void 0===e||e.removeEventListener("keydown",this.firstInnerElementKeyDownHandler),null===(t=this.lastInnerElement)||void 0===t||t.removeEventListener("keydown",this.lastInnerElementKeyDownHandler)}subscribeInnerElementsKeyDown(){var e,t;null===(e=this.firstInnerElement)||void 0===e||e.addEventListener("keydown",this.firstInnerElementKeyDownHandler),null===(t=this.lastInnerElement)||void 0===t||t.addEventListener("keydown",this.lastInnerElementKeyDownHandler)}updateInnerElements(){this.tabbables&&(this.firstInnerElement=this.tabbables[0],this.lastInnerElement=this.tabbables[this.tabbables.length-1])}};export{l as f};
