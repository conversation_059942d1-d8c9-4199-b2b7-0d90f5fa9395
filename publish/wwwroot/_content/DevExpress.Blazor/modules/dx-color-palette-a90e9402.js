import{u as e}from"./constants-da6cacac.js";import{K as t,F as s,D as r}from"./keyboard-navigation-strategy-ea41c807.js";import{S as o}from"./single-slot-element-base-01d93921.js";import{k as n}from"./key-ffa272aa.js";import{C as i}from"./css-classes-c63af734.js";import"./focushelper-2eea96ca.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./dom-utils-d057dcaa.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./tslib.es6-d65164b3.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./custom-events-helper-e7f279d3.js";import"./eventhelper-8bcec49f.js";import"./focus-utils-ae044224.js";import"./touch-6a322081.js";import"./disposable-d2c2d283.js";import"./devices-17b9ba08.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./property-4ec0b52d.js";var a,l,d;class c{}a=c,c.DefaultCssClass=i.Prefix+"-color-palette",c.SelectedTileCssClass=a.DefaultCssClass+"-selected-tile",c.NoColorContainerCssClass=a.DefaultCssClass+"-no-color-container",c.ColorValueAttribute="color-value",function(e){e[e.Undefined=0]="Undefined",e[e.ColorTile=1]="ColorTile",e[e.NoColorTile=2]="NoColorTile"}(l||(l={})),function(e){e[e.First=0]="First",e[e.LastSelected=1]="LastSelected",e[e.Last=2]="Last"}(d||(d={}));class u extends t{constructor(e,t,s){super(e,s),this.rootStrategy=t}getItemStrategy(e){return super.getItemStrategy(e)}getSelectedItemStrategy(){return super.getSelectedItemStrategy()}getShortcutContext(){return{ElementType:this.getElementType(),Value:this.getElementValue()}}selectItem(e){this.resetFocusableState(),super.selectItem(e)}resetFocusableState(){const e=this.findLastSelectedStrategy().selectedItemElement;e&&s.removeTabIndex(e)}getElementType(){return l.Undefined}getElementValue(){return null}move(e,t,s=!0){const r=this.selectedItemIndex+(e?1:-1);if(!this.isIndexWithinBoundaries(r))return!1;if(s){const s=this.getItemStrategy(r);if(!s||!this.canMove(s,e,t))return!1;this.setSelectedItemIndex(s,e,t)}return e?this.moveToNextItem():this.moveToPrevItem(),!0}canMove(e,t,s){return!0}}class m extends u{constructor(e,t,s){super(e,t,s)}handleKeyDown(e){switch(n.KeyUtils.getEventKeyCode(e)){case n.KeyCode.Left:case n.KeyCode.Right:return!0;case n.KeyCode.Enter:case n.KeyCode.Space:return this.performShortcutEvent(e),!0}return super.handleKeyDown(e)}setSelectedItemIndex(e,t,s){}getElementType(){return l.NoColorTile}}class h extends u{constructor(e,t,s){super(e,t,s)}handleKeyDown(e){switch(n.KeyUtils.getEventKeyCode(e)){case n.KeyCode.Left:return this.move(!1,!1,!1);case n.KeyCode.Right:return this.move(!0,!1,!1);case n.KeyCode.Home:return this.moveToFirstItem(),!0;case n.KeyCode.End:return this.moveToLastItem(),!0;case n.KeyCode.Enter:case n.KeyCode.Space:return this.performShortcutEvent(e),!0}return super.handleKeyDown(e)}focusSelectedItem(){var e;super.focusSelectedItem(),null===(e=this.rootStrategy)||void 0===e||e.setLastColumnIndex(this.selectedItemIndex)}setRowSelectedItemIndex(e){var t,s;switch(e){case d.First:this.selectedItemIndex=0;break;case d.LastSelected:this.selectedItemIndex=null!==(s=null===(t=this.rootStrategy)||void 0===t?void 0:t.getLastColumnIndex())&&void 0!==s?s:0;break;case d.Last:this.selectedItemIndex=this.itemCount-1}}findAndSetRowSelectedTile(){for(let e=0;e<this.itemCount;e++){const t=this.getItem(e);if(t&&t.classList.contains(c.SelectedTileCssClass))return this.selectedItemIndex=e,!0}return!1}queryItems(){return this.queryItemsBySelector("td > div")}getElementType(){return l.ColorTile}getElementValue(){return this.selectedItemElement.getAttribute(c.ColorValueAttribute)}setSelectedItemIndex(e,t,s){}}class y{static isGroupStrategy(e){return e instanceof p}static isRowStrategy(e){return e instanceof h}static isButtonStrategy(e){return e instanceof m}}class p extends u{constructor(e,t,s){super(e,t,s)}queryItems(){return this.queryItemsBySelector("tbody > tr")}createItemStrategy(e){if(e.matches("tr"))return new h(this.navigator,this.rootStrategy,e);throw new Error("Color palete kbd strategy not implemented ")}handleKeyDown(e){switch(n.KeyUtils.getEventKeyCode(e)){case n.KeyCode.Left:return this.move(!1,!1);case n.KeyCode.Up:return this.move(!1,!0);case n.KeyCode.Right:return this.move(!0,!1);case n.KeyCode.Down:return this.move(!0,!0)}return super.handleKeyDown(e)}getShortcutContext(){return{}}setGroupSelectedItemIndex(e,t){this.selectedItemIndex=t===d.First?0:this.itemCount-1;const s=this.getItemStrategy(this.selectedItemIndex);y.isRowStrategy(s)&&s.setRowSelectedItemIndex(e)}findAndSetGroupSelectedTile(){for(let e=0;e<this.itemCount;e++){const t=this.getItemStrategy(e);if(y.isRowStrategy(t)&&t.findAndSetRowSelectedTile())return void(this.selectedItemIndex=e)}}setSelectedItemIndex(e,t,s){y.isRowStrategy(e)&&e.setRowSelectedItemIndex(s?d.LastSelected:t?d.First:d.Last)}}class I extends u{constructor(e,t){super(e,null,t),this.lastColumnIndex=0}queryItems(){return new Array(...this.queryItemsBySelector("& > table"),...this.queryItemsBySelector(`& > div.${c.NoColorContainerCssClass}`))}createItemStrategy(e){if(e.matches("table"))return new p(this.navigator,this,e);if(e.matches(`div.${c.NoColorContainerCssClass}`))return new m(this.navigator,this,e);throw new Error("Color palete kbd strategy not implemented ")}handleKeyDown(e){switch(n.KeyUtils.getEventKeyCode(e)){case n.KeyCode.Left:return this.move(!1,!1);case n.KeyCode.Up:return this.move(!1,!0);case n.KeyCode.Right:return this.move(!0,!1);case n.KeyCode.Down:return this.move(!0,!0);case n.KeyCode.Tab:return this.handleTab(e)}return super.handleKeyDown(e)}getShortcutContext(){return{}}setLastColumnIndex(e){this.lastColumnIndex=e}getLastColumnIndex(){return this.lastColumnIndex}setSelectedItemIndex(e,t,s){y.isGroupStrategy(e)&&(t?e.setGroupSelectedItemIndex(s?d.LastSelected:d.First,d.First):e.setGroupSelectedItemIndex(s?d.LastSelected:d.Last,d.Last))}canMove(e,t,s){return!t||s||!y.isButtonStrategy(e)}handleTab(e){const t=this.selectedItemIndex+(e.shiftKey?-1:1);if(this.isIndexWithinBoundaries(t)){const e=this.getItemStrategy(t);return y.isGroupStrategy(e)&&e.findAndSetGroupSelectedTile(),this.selectItem(t),!0}return e.shiftKey?(this.leaveBackward(),!0):super.handleKeyDown(e)}}class g extends o{constructor(){super()}contentChanged(){super.contentChanged(),this.initializeKeyboardNavigator()}disconnectedCallback(){delete this.keyboardNavigator,super.disconnectedCallback()}initializeKeyboardNavigator(){this.keyboardNavigator=this.querySelector(r),this.keyboardNavigator&&!this.keyboardNavigator.initialized&&this.keyboardNavigator.initialize(this,new I(this.getKeyboardNavigator(),this))}getKeyboardNavigator(){return this.keyboardNavigator}}customElements.define(e,g);const C={loadModule:function(){}};export{g as DxColorPalette,C as default};
