import{S as e}from"./single-slot-element-base-01d93921.js";import{b as t}from"./dx-scroll-viewer-da0fb41c.js";import{S as s}from"./scroll-viewer-css-classes-e724f203.js";import{b as o}from"./browser-3fc721b7.js";import{C as r}from"./css-classes-c63af734.js";import{D as i}from"./dx-html-element-base-3262304e.js";import{d as l}from"./dom-554d0cc7.js";import"./tslib.es6-d65164b3.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./property-4ec0b52d.js";import"./_commonjsHelpers-41cdd1e7.js";import"./dx-html-element-pointer-events-helper-c1007ce3.js";import"./eventhelper-8bcec49f.js";import"./devices-17b9ba08.js";import"./common-48ec40e2.js";class a{}a.ClassName=r.Prefix+"-pivot-table",a.TableDataClassName=a.ClassName+"-data",a.RowFieldItemClassName=a.ClassName+"-row-field-item",a.AreaDataClassName=a.ClassName+"-area-data",a.AreaRowClassName=a.ClassName+"-area-row",a.AreaRowFieldClassName=a.ClassName+"-area-row-field",a.AreaColumnInnerContainerClassName=a.ClassName+"-area-column-inner-container",a.FixedCellClassName=a.ClassName+"-fixed-cell",a.NoScrollClassName=a.ClassName+"-no-scroll",a.LastFixedLeftCellClassName=a.ClassName+"-last-fixed-left-cell";class n{constructor(){}}n.Table=`.${a.TableDataClassName}`,n.BodyRows="tbody tr",n.RowFieldItem=`.${a.RowFieldItemClassName}`,n.AreaData=`.${a.AreaDataClassName}`,n.AreaRow=`.${a.AreaRowClassName}`,n.AreaRowField=`.${a.AreaRowFieldClassName}`,n.AreaColumnInnerContainer=`.${a.AreaColumnInnerContainerClassName}`,n.ColElementSelector=`:scope > .${s.ClassName} > .${s.ContentContainerClassName} > .${a.TableDataClassName} > colgroup > col`;const d="dxbl-pivot-table";class c{constructor(){}}var h;c.DataColumnIndex="data-column-index",c.TableState="table-state",c.Uid="data-dx-pivot-uid",function(e){e[e.Disabled=0]="Disabled",e[e.LeftBorder=1]="LeftBorder",e[e.BetweenBorders=2]="BetweenBorders",e[e.RightBorder=3]="RightBorder"}(h||(h={}));class m{constructor(e){this._pivotUid="",this._lastGeneratedStyle="",this._scrollStatus=h.Disabled,this._pivot=e}get highlightLeftBorder(){switch(this._scrollStatus){case h.BetweenBorders:case h.RightBorder:return!0;default:return!1}}onScrollPositionUpdated(e){this._scrollStatus=this.calcScrollStatus(e),requestAnimationFrame((()=>this.updateFixedCellsStyle()))}hasAnyFixedColumn(){return this._pivot.columnsInfo.some((e=>!!e.fixedPosition))}updateFixedCellsStyle(){this._pivotUid=this._pivot.getAttribute(c.Uid);const e=this._pivot.querySelector("style");if(!e)return;let t="";this.hasAnyFixedColumn()&&(t+=this.createPositionStyles(),t+=this.createBorderStyles()),this._lastGeneratedStyle!==t&&(e.innerText=t,this._lastGeneratedStyle=t)}createPositionStyles(){const e=this._pivot.getColElements(),t=this.getOffsetWidths(e),s=new Array(t.length);s[0]=0;for(let e=1;e<t.length;++e)s[e]=s[e-1]+t[e-1];let o="";for(let e=0;e<this._pivot.columnsInfo.length;++e){const t=this._pivot.columnsInfo[e].fixedPosition;t&&"left"===t&&(o+=this.createPositionStyleForColumn(e,t,s[e]))}return o}createPositionStyleForColumn(e,t,s){return this.getTableRowsSelector()+` > .${a.FixedCellClassName}[data-column-index="${e}"]`+` { ${t}: ${s}px; }`}createBorderStyles(){const e=[];return this.highlightLeftBorder&&(e.push(this.createLeftBorderStyle("th")),e.push(this.createLeftBorderStyle("td"))),e.join("")}createLeftBorderStyle(e){return this.getTableRowsSelector()+` > ${e}.${a.LastFixedLeftCellClassName} { border-right-color: var(--dxbl-pivot-table-fixed-column-border-color); border-right-width: var(--dxbl-pivot-table-border-width); }`}getTableRowsSelector(){return[`${d}[${c.Uid}="${this._pivotUid}"]`,`.${s.ClassName}`,`.${s.ContentContainerClassName}`,`.${a.TableDataClassName}`,"*","tr"].join(" > ")}getOffsetWidths(e){const t=[];for(let s=0;s<e.length;++s)t.push(this._pivot.getColElementOffsetWidth(s));return t}calcScrollStatus(e){if(!(this._pivot&&this._pivot.getTable()))return h.Disabled;const t=this._pivot.getTable().clientWidth;return t===e.clientWidth?h.Disabled:0===e.scrollLeft?h.LeftBorder:Math.abs(t-e.clientWidth-e.scrollLeft)<1?h.RightBorder:h.BetweenBorders}}class u{get uID(){return this._uID}get width(){return this._width}get minWidth(){return this._minWidth}get hasWidth(){return!!this._width}get isPercentWidth(){return this.hasWidth&&-1!==this.width.indexOf("%")}get isAbsoluteWidth(){return this.hasWidth&&!this.isPercentWidth}get colElementWidth(){return this._colElementWidth}set colElementWidth(e){this._colElementWidth=e}get fixedPosition(){return this._fixedPosition}constructor(e,t,s,o){this._uID=e,this._width=t,this._colElementWidth=t,this._minWidth=s,this._fixedPosition=o}updateWidth(e){this._width=e,this._colElementWidth=e}}class C extends e{constructor(){super(),this.boundOnMouseDownHandler=this.onMouseDown.bind(this),this.boundOnScrollViewerScrollHandler=this.onScrollViewerScroll.bind(this),this.boundOnScrollViewerUpdateHandler=this.onScrollViewerUpdate.bind(this),this._columnsInfo=[],this._styleGenerator=null}get styleGenerator(){return this._styleGenerator}get columnsInfo(){return this._columnsInfo}get uId(){return this.getAttribute(c.Uid)}getScrollViewer(){return this.querySelector(`.${s.ClassName}`)}getScrollViewerContent(){return this.querySelector(`.${s.ContentContainerClassName}`)}getTable(){return this.querySelector(n.Table)}getColElements(){return this.querySelectorAll(n.ColElementSelector)}getTableContainerWidth(){const e=this.getScrollViewerContent();return e?null==e?void 0:e.getBoundingClientRect().width:0}getColElementOffsetWidth(e){return this.getColElements()[e].getBoundingClientRect().width}connectedCallback(){super.connectedCallback(),this.addEventSubscriptions(),this._styleGenerator=new m(this),this.setupBottomBorderForLastDataRow()}disconnectedCallback(){this.removeEventSubscriptions(),super.disconnectedCallback()}notifyColumnsChanged(e){this._columnsInfo=e}addEventSubscriptions(){o.Browser.Firefox&&this.addEventListener("mousedown",this.boundOnMouseDownHandler),setTimeout((()=>{const e=this.getScrollViewer();e&&e.subscribeToScroll&&e.subscribeToScroll(this.boundOnScrollViewerScrollHandler),this.addEventListener(t.eventName,this.boundOnScrollViewerUpdateHandler)}))}removeEventSubscriptions(){o.Browser.Firefox&&this.removeEventListener("mousedown",this.boundOnMouseDownHandler),setTimeout((()=>{const e=this.getScrollViewer();e&&e.unsubscribeFromScroll&&e.unsubscribeFromScroll()}))}onScrollViewerScroll(){this.styleGenerator.onScrollPositionUpdated(this.getScrollViewerContent())}setupBottomBorderForLastDataRow(){const e=this.getScrollViewerContent(),t=this.getTable();e&&t&&(t.offsetHeight<e.clientHeight?l.DomUtils.addClassName(t,a.NoScrollClassName):l.DomUtils.removeClassName(t,a.NoScrollClassName))}onScrollViewerUpdate(e){this.setupBottomBorderForLastDataRow(),e.stopPropagation()}onMouseDown(e){e.ctrlKey&&e.preventDefault()}}customElements.define(d,C),customElements.define("dxbl-pivot-table-columns-info",class extends i{get data(){return this.getAttribute("data")}get columns(){return null!==this.data?this.parseColumns(this.data):[]}connectedCallback(){var e;super.connectedCallback(),this.pivot=this.closest(`.${a.ClassName}`),null===(e=this.pivot)||void 0===e||e.notifyColumnsChanged(this.columns)}parseColumns(e){const t=JSON.parse(e),s=[];for(let e=0;e<t.length;e++){const o=t[e],r=new u(o.UID,o.Width,o.MinWidth,o.FixedPosition);s.push(r)}return s}static get observedAttributes(){return["data"]}attributeChangedCallback(e,t,s){var o;if("data"===e)null===(o=this.pivot)||void 0===o||o.notifyColumnsChanged(this.parseColumns(s))}});const p={loadModule:function(){}};export{C as DxPivotTable,p as default};
