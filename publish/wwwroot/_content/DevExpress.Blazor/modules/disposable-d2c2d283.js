function t(){const t=(621355968e5+(new Date).getTime()).toString()+(e=1,s=1e3,Math.floor(Math.random()*(s-e))+e).toString();var e,s;return parseInt(t)}function e(t){const e=t.dataset.disposeId?parseInt(t.dataset.disposeId):-1;e>-1&&(d[e](),delete d[e],delete t.dataset.disposeId),n()}function s(e,s){e.dataset.disposeId||(e.dataset.disposeId=function(){let e;do{e=t()}while(d[e]);return e.toString()}()),d[parseInt(e.dataset.disposeId)]=s,n()}const d=[];function n(){d.filter((t=>t))}export{e as d,s as r};
