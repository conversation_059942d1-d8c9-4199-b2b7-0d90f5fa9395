class t{static isZero(t){return Math.abs(t)<Number.EPSILON}static areClose(t,e){if(t===e)return!0;const r=(Math.abs(t)+Math.abs(e)+10)*Number.EPSILON,a=t-e;return-r<a&&r>a}static greaterThan(e,r){return e>r&&!t.areClose(e,r)}static lessThan(e,r){return e<r&&!t.areClose(e,r)}static lessThanOrClose(e,r){return!(e>=r)||t.areClose(e,r)}static greaterThanOrClose(e,r){return!(e<=r)||t.areClose(e,r)}static toRange(t,e,r){const a=Math.min(e,r),s=Math.max(e,r);return Math.min(Math.max(t,a),s)}static findNumber(t){if(null==t)return null;const e=t.match(/\d+/);return e&&(null==e?void 0:e.length)>0?parseInt(e[0]):null}}class e{get x(){return this._x}get y(){return this._y}constructor(t,e){this._x=t,this._y=e}}e.zero=new e(0,0);class r{static areClose(e,r){return t.areClose(e.x,r.x)&&t.areClose(e.y,r.y)}static equals(t,e){return t.x===e.x&&t.y===e.y}static add(t,r){return new e(t.x+r.x,t.y+r.y)}static sub(t,r){return new e(t.x-r.x,t.y-r.y)}}export{t as N,e as P,r as a};
