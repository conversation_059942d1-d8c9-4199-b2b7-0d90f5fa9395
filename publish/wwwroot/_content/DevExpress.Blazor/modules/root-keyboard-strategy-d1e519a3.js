import{k as e}from"./key-ffa272aa.js";import{D as t,M as s,c as n,d as r,e as o}from"./menu-keyboard-strategy-7c257fd3.js";import{D as i,a}from"./modal-keyboard-strategy-05da34d9.js";const l=":scope > .dxbl-btn-toolbar:not(.dxbl-virtual-toolbar)",u=[...[".dxbl-btn:not([disabled]):not(.dxbl-toolbar-hidden-item)",".dxbl-btn-split:not(.dxbl-toolbar-hidden-item) > .dxbl-btn:not([disabled])"].map((e=>`${l} .dxbl-toolbar-item[visible]:not(.dxbl-toolbar-edit) > ${e}`)),`${l} .dxbl-toolbar-btn-ellipsis[visible]:not([disabled]):not(.dxbl-toolbar-hidden-item) > .dxbl-btn`,`${l} .dxbl-toolbar-item[visible].dxbl-toolbar-edit:has(.dxbl-text-edit:not(.dxbl-disabled))`,`${l} .dxbl-toolbar-item[visible].dxbl-toolbar-item-tmpl:not(.dxbl-toolbar-edit)`].join(",");class d extends t{queryItems(){return this.queryItemsBySelector(u)}getShortcutContext(){return{itemIndex:this.selectedItemIndex}}handleKeyDownCore(t){switch(e.KeyUtils.getEventKeyCode(t)){case e.KeyCode.Right:return this.subMenuStrategy||this.moveToNextItem(!0),!0;case e.KeyCode.Left:return this.subMenuStrategy||this.moveToPrevItem(!0),!0;case e.KeyCode.Home:return this.selectItem(0),!0;case e.KeyCode.End:return this.selectItem(this.itemCount-1),!0;case e.KeyCode.Up:if(t.altKey&&!t.shiftKey){if(this.isMenuExpanded())return this.requestSubMenuClose(),!0}else if(this.subMenuStrategy)return this.subMenuStrategy.focusLastItem(),!0;return!1;case e.KeyCode.Down:if(t.altKey&&!t.shiftKey){if(this.hasMenu())return this.requestSubMenuOpen(),this.focusSubMenuItemAsync(s.First),!0}else if(this.subMenuStrategy)return this.subMenuStrategy.focusFirstItem(),!0;return!1;case e.KeyCode.Tab:return!this.nestedContentSelected&&(this.requestSubMenuClose(),super.handleTabKeyDown(t));case e.KeyCode.Enter:case e.KeyCode.Space:return this.hasMenu()&&this.focusSubMenuItemAsync(s.First),!1;default:return!1}}focusSubMenuItemAsync(e){return this.shouldRestoreFocus=n(this.targetElement),super.focusSubMenuItemAsync(e)}requestSubMenuOpen(){var e;null===(e=this.selectedItemElement)||void 0===e||e.dispatchEvent(new r(o.Expand))}requestSubMenuClose(){var e;null===(e=this.selectedItemElement)||void 0===e||e.dispatchEvent(new r(o.Collapse))}createPopupStrategy(e,t){return new i(e,t,this)}createModalStrategy(e,t){return new a(e,t,this)}}export{d as D};
