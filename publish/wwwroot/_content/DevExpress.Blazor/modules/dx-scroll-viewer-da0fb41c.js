import{d as e}from"./dom-554d0cc7.js";import{D as t}from"./dx-html-element-base-3262304e.js";import{S as l}from"./scroll-viewer-css-classes-e724f203.js";import{D as n}from"./data-qa-utils-8be7c726.js";class r{constructor(){this._visible=!1,this._contentElementInfo={scrollStart:0,clientSize:0,scrollSize:0},this._scrollBarStart=0,this._scrollBarEnd=0,this._scrollBarOffset=0,this._scrollBarThickness=0,this._thumbStart=0,this._thumbEnd=0}get visible(){return this._visible}set visible(e){this._visible=e}get contentElementInfo(){return this._contentElementInfo}set contentElementInfo(e){this._contentElementInfo=e}get scrollBarStart(){return this._scrollBarStart}set scrollBarStart(e){this._scrollBarStart=e}get scrollBarEnd(){return this._scrollBarEnd}set scrollBarEnd(e){this._scrollBarEnd=e}get scrollBarOffset(){return this._scrollBarOffset}set scrollBarOffset(e){this._scrollBarOffset=e}get scrollBarThickness(){return this._scrollBarThickness}set scrollBarThickness(e){this._scrollBarThickness=e}get thumbStart(){return this._thumbStart}set thumbStart(e){this._thumbStart=e}get thumbEnd(){return this._thumbEnd}set thumbEnd(e){this._thumbEnd=e}}class o{get scrollBarInfo(){return this._scrollBarInfo}get contentElementInfo(){return this.scrollBarInfo.contentElementInfo}constructor(e){this._scrollBarInfo=e,this.thumbSize=20,this._scrollBarInfo.scrollBarEnd=this._scrollBarInfo.contentElementInfo.clientSize,this.recalculate()}scrollByPointerClick(e){const t=this.contentElementInfo.scrollSize-this.contentElementInfo.clientSize;this.contentElementInfo.scrollStart=t*e/this.getScrollBarSize(),this.contentElementInfo.scrollStart=Math.max(0,this.contentElementInfo.scrollStart),this.contentElementInfo.scrollStart=Math.min(t,this.contentElementInfo.scrollStart),this.recalculate()}scrollByPointerMove(e){const t=this.contentElementInfo.scrollSize-this.contentElementInfo.clientSize,l=t/(this.getScrollBarSize()-this.thumbSize);this.contentElementInfo.scrollStart=this.contentElementInfo.scrollStart+e*l,this.contentElementInfo.scrollStart=Math.max(0,this.contentElementInfo.scrollStart),this.contentElementInfo.scrollStart=Math.min(t,this.contentElementInfo.scrollStart),this.recalculate()}recalculate(){this.recalculateThumb()}recalculateThumb(){this.thumbSize=this.calculateThumbSize(),this._scrollBarInfo.visible=this.scrollBarInfo.contentElementInfo.scrollSize>this.scrollBarInfo.contentElementInfo.clientSize,this._scrollBarInfo.thumbStart=this.calculateThumbStart(),this._scrollBarInfo.thumbEnd=this._scrollBarInfo.thumbStart+this.thumbSize}calculateThumbSize(){const e=this.getScrollBarSize(),t=e*this.scrollBarInfo.contentElementInfo.clientSize/this.scrollBarInfo.contentElementInfo.scrollSize;return e>20?Math.max(t,20):t}calculateThumbStart(){const e=(this.getScrollBarSize()-this.thumbSize)/(this.scrollBarInfo.contentElementInfo.scrollSize-this.scrollBarInfo.contentElementInfo.clientSize);return this.scrollBarInfo.contentElementInfo.scrollStart*e}getScrollBarSize(){return this._scrollBarInfo.scrollBarEnd-this._scrollBarInfo.scrollBarStart}}class i{constructor(e){this._offsets={left:0,right:0,top:0,bottom:0},this._verticalScrollBarInfo=new r,this._horizontalScrollBarInfo=new r,this._verticalThumbCalculator=new o(this._verticalScrollBarInfo),this._horizontalThumbCalculator=new o(this._horizontalScrollBarInfo),this.updateScrollableElementInfo(e)}get verticalScrollBarInfo(){return this._verticalScrollBarInfo}get horizontalScrollBarInfo(){return this._horizontalScrollBarInfo}updateScrollableElementInfo(e){this._verticalScrollBarInfo.contentElementInfo.scrollStart=e.scrollTop||0,this._verticalScrollBarInfo.contentElementInfo.scrollSize=e.scrollHeight||0;const t=e.getBoundingClientRect();this._verticalScrollBarInfo.contentElementInfo.clientSize=Math.ceil(t.height),this._horizontalScrollBarInfo.contentElementInfo.scrollStart=e.scrollLeft||0,this._horizontalScrollBarInfo.contentElementInfo.scrollSize=e.scrollWidth||0,this._horizontalScrollBarInfo.contentElementInfo.clientSize=e.clientWidth||0,this.recalculate()}initScrollBarsThickness(e,t){this._verticalScrollBarInfo.scrollBarThickness=e,this._horizontalScrollBarInfo.scrollBarThickness=t}updateTopPanelHeight(e){this._offsets.top=e,this.recalculate()}updateBottomPanelHeight(e){this._offsets.bottom=e,this.recalculate()}updateLeftPanelWidth(e){this._offsets.left=e,this.recalculate()}updateRightPanelWidth(e){this._offsets.right=e,this.recalculate()}vertScrollByPointerMove(e){this._verticalThumbCalculator.scrollByPointerMove(e),this.recalculate()}vertScrollByPointerClick(e){this._verticalThumbCalculator.scrollByPointerClick(e),this.recalculate()}horScrollByPointerMove(e){this._horizontalThumbCalculator.scrollByPointerMove(e),this.recalculate()}horScrollByPointerClick(e){this._horizontalThumbCalculator.scrollByPointerClick(e),this.recalculate()}recalculate(){this._verticalScrollBarInfo.scrollBarStart=this._offsets.top,this._verticalScrollBarInfo.scrollBarEnd=this._verticalScrollBarInfo.contentElementInfo.clientSize-this._offsets.bottom,this._horizontalScrollBarInfo.scrollBarStart=this._offsets.left,this._horizontalScrollBarInfo.scrollBarEnd=this._horizontalScrollBarInfo.contentElementInfo.clientSize-this._offsets.right,this._verticalScrollBarInfo.scrollBarOffset=this._offsets.right,this._horizontalScrollBarInfo.scrollBarOffset=this._offsets.bottom,this._verticalThumbCalculator.recalculate(),this._horizontalThumbCalculator.recalculate(),this._horizontalScrollBarInfo.visible&&(this._verticalScrollBarInfo.thumbEnd-=this._horizontalScrollBarInfo.scrollBarThickness)}}var s;!function(e){e[e.Vertical=0]="Vertical",e[e.Horizontal=1]="Horizontal"}(s||(s={}));class c extends t{constructor(){super(),this.thumbElement=null,this._mode=s.Vertical,this._info=null}initializeComponent(){super.initializeComponent(),this.thumbElement=this.getThumbElement(),this.thumbElement.addEventListener("click",c.onThumbElementClick)}disposeComponent(){super.disposeComponent(),this.thumbElement&&this.thumbElement.removeEventListener("click",c.onThumbElementClick)}get mode(){return this._mode}set mode(e){this._mode=e}get info(){return this._info}set info(e){this._info=e,this.applyUIChanges()}getThumbThickness(){if(!this.thumbElement)return 0;const t=e.DomUtils.getCurrentStyle(this.thumbElement);return parseInt(this.mode===s.Vertical?t.width:t.height)}applyUIChanges(){if(!this.isInitialized)return;if(!this.info||!this.info.visible)return void e.DomUtils.removeClassName(this,l.ScrollBarActiveClassName);const t=this.info.thumbEnd-this.info.thumbStart+"px";this.mode===s.Vertical?this.applyVerticalScrollUIChanges(t):this.applyHorizontalScrollUIChanges(t),e.DomUtils.addClassName(this,l.ScrollBarActiveClassName)}applyVerticalScrollUIChanges(e){this.thumbElement&&this.info&&(this.thumbElement.style.height=e,this.thumbElement.style.transform=`translateY(${this.info.thumbStart}px)`,this.style.top=this.info.scrollBarStart+"px",this.style.bottom=this.info.contentElementInfo.clientSize-this.info.scrollBarEnd+"px",this.style.right=this.info.scrollBarOffset+"px")}applyHorizontalScrollUIChanges(e){this.thumbElement&&this.info&&(this.thumbElement.style.width=e,this.thumbElement.style.transform=`translateX(${this.info.thumbStart}px)`,this.style.left=this.info.scrollBarStart+"px",this.style.right=this.info.contentElementInfo.clientSize-this.info.scrollBarEnd+"px",this.style.bottom=this.info.scrollBarOffset+"px")}getThumbElement(){return this.querySelector(`.${l.ScrollBarThumbClassName}`)}static onThumbElementClick(e){e.stopPropagation()}}const h="dxbl-scroll-viewer",a="data-required-visible-element",m=40;var u,f,S,d;!function(e){e[e.None=0]="None",e[e.Top=1]="Top",e[e.Bottom=2]="Bottom"}(u||(u={})),function(e){e[e.None=0]="None",e[e.Left=1]="Left",e[e.Right=2]="Right"}(f||(f={})),function(e){e[e.All=0]="All",e[e.ScrollBars=1]="ScrollBars",e[e.ScrollPosition=2]="ScrollPosition"}(S||(S={})),function(e){e[e.Horizontal=0]="Horizontal",e[e.Vertical=1]="Vertical",e[e.Both=2]="Both"}(d||(d={}));class b extends CustomEvent{constructor(){super(b.eventName,{bubbles:!0,composed:!1,cancelable:!0})}}b.eventName="dxscrollviewerupdate";class g extends CustomEvent{constructor(e,t){super(g.eventName,{detail:{element:e,isFocusRequired:t},bubbles:!0,composed:!1})}}g.eventName="dxscrollviewervisibleelementchanged";class E extends t{constructor(){super(),this.scrollAttributeLifespanTimeoutId=null,this.pendingRefreshUi=null,this.currentMakeElementVisibleTimeoutId=-1,this.autoScrollingMode=null,this.autoScrollingRect=null,this.autoScrollingRafID=-1,this.autoScrollingCallback=null,this.contentContainerScrollingCallback=null,this.boundOnContentScrollHandler=this.onContentScroll.bind(this),this.boundOnVerticalScrollBarClickHandler=this.onVerticalScrollBarClick.bind(this),this.boundOnVerticalScrollThumbMoveHandler=this.onVerticalScrollThumbMove.bind(this),this.boundOnHorizontalScrollBarClickHandler=this.onHorizontalScrollBarClick.bind(this),this.boundOnHorizontalScrollThumbMoveHandler=this.onHorizontalScrollThumbMove.bind(this),this.boundOnDocumentMouseMoveHandler=this.onDocumentMouseMove.bind(this),this.preventScrollEvent=!1,this.contentContainerElement=null,this.verticalScrollBarElement=null,this.horizontalScrollBarElement=null,this.scrollController=null,this.contentResizeObserver=new ResizeObserver(this.onContentSizeChanged.bind(this)),this.headerResizeObserver=new ResizeObserver(this.onHeaderSizeChanged.bind(this)),this.footerResizeObserver=new ResizeObserver(this.onFooterSizeChanged.bind(this)),this.leftResizeObserver=new ResizeObserver(this.onLeftSizeChanged.bind(this)),this.rightResizeObserver=new ResizeObserver(this.onRightSizeChanged.bind(this))}get useShadowDom(){return!1}get headerSelector(){return this.getAttribute("header-selector")}get footerSelector(){return this.getAttribute("footer-selector")}get leftSelector(){return this.getAttribute("left-selector")}get rightSelector(){return this.getAttribute("right-selector")}get isRefreshUIRequested(){return!!this.pendingRefreshUi}get canMakeElementVisible(){if(this.isRefreshUIRequested)return!1;if(this.contentContainerElement){return this.contentContainerElement.isConnected&&this.contentContainerElement.offsetHeight>0&&this.contentContainerElement.offsetWidth>0}return!1}getContentContainerElement(){return this.contentContainerElement}getVerticalScrollBarElement(){return this.verticalScrollBarElement}getHorizontalScrollBarElement(){return this.horizontalScrollBarElement}get hasHorizontalScrollBar(){var e,t;return!!(null===(t=null===(e=this.getHorizontalScrollBarElement())||void 0===e?void 0:e.info)||void 0===t?void 0:t.visible)}initializeComponent(){super.initializeComponent(),this.initializeInnerHTMLElements(),this.initializeScrollController(),this.connectResizeObservers()}afterInitialize(){super.afterInitialize(),this.refreshScrollPosition()}disposeComponent(){super.disposeComponent(),this.releaseInnerHTMLElements(),this.scrollController=null,this.contentResizeObserver.disconnect(),this.headerResizeObserver.disconnect(),this.footerResizeObserver.disconnect(),this.leftResizeObserver.disconnect(),this.rightResizeObserver.disconnect()}getRectangle(){const{left:e,top:t,width:l,height:n}=this.getBoundingClientRect();return{x:e,y:t,width:l,height:n}}getDataAreaRectangle(){const e=this.getRectangle(),t=this.getElementsRectangle(this.getHeaderElements());if(t){const l=t.height;e.height-=l,e.y+=l}const l=this.getElementsRectangle(this.getLeftElements());if(l){const t=l.width;e.width-=t,e.x+=t}const n=this.getElementsRectangle(this.getRightElements());n&&(e.width-=n.width);const r=this.getElementsRectangle(this.getFooterElements());return r&&(e.height-=r.height),e}static calculateBoundingRectangle(e){if(1===e.length)return e[0];let t=-1,l=-1,n=-1,r=-1;return e.forEach((function(e){(e.x<t||-1===t)&&(t=e.x),(e.y<l||-1===l)&&(l=e.y),(e.x+e.width>n||-1===n)&&(n=e.x+e.width),(e.y+e.height>r||-1===r)&&(r=e.y+e.height)})),{x:t,y:l,width:n-t,height:r-l}}onDocumentMouseMove(e){this.performAutoScrolling(e)}startAutoScrolling(e,t=null,l=null){this.autoScrollingMode=e,this.autoScrollingCallback=t;const n=this.getRectangle();l&&l(n),this.autoScrollingRect=n,document.addEventListener("pointermove",this.boundOnDocumentMouseMoveHandler)}stopAutoScrolling(){document.removeEventListener("pointermove",this.boundOnDocumentMouseMoveHandler),this.stopAutoScrollingTimer(),this.autoScrollingCallback=null,this.autoScrollingRect=null,this.autoScrollingMode=null}subscribeToScroll(e=null){this.contentContainerScrollingCallback=e}unsubscribeFromScroll(){this.contentContainerScrollingCallback=null}get isHorizontalAutoScrollingEnabled(){return this.autoScrollingMode===d.Horizontal||this.autoScrollingMode===d.Both}get isVerticalAutoScrollingEnabled(){return this.autoScrollingMode===d.Vertical||this.autoScrollingMode===d.Both}performAutoScrolling(e){if(this.stopAutoScrollingTimer(),void 0!==this.autoScrollingMode&&this.autoScrollingRect){const t=this.autoScrollingRect,l=t.x<=e.pageX&&e.pageX<=t.x+t.width,n=t.y<=e.pageY&&e.pageY<=t.y+t.height;let r=0,o=0;if(this.isHorizontalAutoScrollingEnabled&&l&&n){const l=e.pageX-t.x;0<=l&&l<=m&&(r=-(m-l)/2);const n=t.x+t.width-e.pageX;0<=n&&n<=m&&(r=(m-n)/2)}if(this.isVerticalAutoScrollingEnabled&&l&&n){const l=e.pageY-t.y;0<=l&&l<=m&&(o=-(m-l)/2);const n=t.y+t.height-e.pageY;0<=n&&n<=m&&(o=(m-n)/2)}0===r&&0===o||this.startAutoScrollingTimer(e,r,o)}}startAutoScrollingTimer(e,t,l){this.autoScrollingRafID=requestAnimationFrame((()=>{this.contentContainerElement&&(this.contentContainerElement.scrollBy(t,l),this.refresh(S.ScrollBars)),this.autoScrollingCallback&&this.autoScrollingCallback(e),this.performAutoScrolling(e)}))}stopAutoScrollingTimer(){this.autoScrollingRafID>-1&&(cancelAnimationFrame(this.autoScrollingRafID),this.autoScrollingRafID=-1)}releaseInnerHTMLElements(){this.contentContainerElement&&this.contentContainerElement.removeEventListener("scroll",this.boundOnContentScrollHandler),this.verticalScrollBarElement&&this.unsubscribeEvents(this.verticalScrollBarElement,this.boundOnVerticalScrollBarClickHandler,this.boundOnVerticalScrollThumbMoveHandler),this.horizontalScrollBarElement&&this.unsubscribeEvents(this.horizontalScrollBarElement,this.boundOnHorizontalScrollBarClickHandler,this.boundOnHorizontalScrollThumbMoveHandler),this.contentContainerElement=null,this.verticalScrollBarElement=null,this.horizontalScrollBarElement=null,-1!==this.currentMakeElementVisibleTimeoutId&&clearTimeout(this.currentMakeElementVisibleTimeoutId)}getHeaderElements(){return this.getElements(this.headerSelector)}getFooterElements(){return this.getElements(this.footerSelector)}getLeftElements(){return this.getElements(this.leftSelector)}getRightElements(){return this.getElements(this.rightSelector)}getElementsRectangle(e){if(null===e||0===e.length)return null;const t=Array.from(e);return E.calculateBoundingRectangle(t.map((e=>e.getBoundingClientRect())))}getElements(e){return e&&this.contentContainerElement?this.contentContainerElement.querySelectorAll(e):null}subscribeEvents(e,t,l){e.addEventListener("click",t),e.addEventListener("dxthumbdragdelta",l)}unsubscribeEvents(e,t,l){e.removeEventListener("click",t),e.removeEventListener("dxthumbdragdelta",l)}refreshUI(){this.refresh(S.All),this.dispatchEvent(new b)}refreshScrollPosition(){this.refresh(S.ScrollPosition),this.dispatchEvent(new b)}refresh(e){if(this.pendingRefreshUi){e===S.ScrollBars&&e!==this.pendingRefreshUi.refreshType&&this.contentContainerElement&&(this.pendingRefreshUi.scrollTop=this.contentContainerElement.scrollTop,this.pendingRefreshUi.scrollLeft=this.contentContainerElement.scrollLeft,this.pendingRefreshUi.refreshType===S.ScrollPosition&&(this.pendingRefreshUi.refreshType=S.All))}else this.pendingRefreshUi={refreshType:e},requestAnimationFrame((()=>{const{refreshType:e,scrollTop:t,scrollLeft:l}=this.pendingRefreshUi;if(this.pendingRefreshUi=null,!this.scrollController)return;const n=this.scrollController.verticalScrollBarInfo,r=this.scrollController.horizontalScrollBarInfo;this.contentContainerElement&&(e!==S.All&&e!==S.ScrollPosition||(this.contentContainerElement.scrollTop=null!=t?t:n.contentElementInfo.scrollStart,this.contentContainerElement.scrollLeft=null!=l?l:r.contentElementInfo.scrollStart),e!==S.All&&e!==S.ScrollBars||this.updateScrollableElementInfo(this.contentContainerElement)),this.verticalScrollBarElement&&(this.verticalScrollBarElement.info=n),this.horizontalScrollBarElement&&(this.horizontalScrollBarElement.info=r),this.contentContainerElement&&this.onRefresh(this.contentContainerElement.scrollTop,this.contentContainerElement.scrollLeft),this.preventScrollEvent=!1}))}onRefresh(e,t){this.contentContainerScrollingCallback&&this.contentContainerScrollingCallback(e,t)}initializeInnerHTMLElements(){this.contentContainerElement=this.querySelector(`.${l.ContentContainerClassName}`),this.contentContainerElement.addEventListener("scroll",this.boundOnContentScrollHandler),this.verticalScrollBarElement=this.getScrollElement(l.VerticalScrollBarClassName),this.verticalScrollBarElement.mode=s.Vertical,this.subscribeEvents(this.verticalScrollBarElement,this.boundOnVerticalScrollBarClickHandler,this.boundOnVerticalScrollThumbMoveHandler),this.horizontalScrollBarElement=this.getScrollElement(l.HorizontalScrollBarClassName),this.horizontalScrollBarElement.mode=s.Horizontal,this.subscribeEvents(this.horizontalScrollBarElement,this.boundOnHorizontalScrollBarClickHandler,this.boundOnHorizontalScrollThumbMoveHandler)}initializeScrollController(){if(this.contentContainerElement&&this.verticalScrollBarElement&&this.horizontalScrollBarElement&&(this.scrollController=new i(this.contentContainerElement),this.verticalScrollBarElement instanceof c&&this.horizontalScrollBarElement instanceof c)){const e=this.verticalScrollBarElement.getThumbThickness(),t=this.horizontalScrollBarElement.getThumbThickness();this.scrollController.initScrollBarsThickness(e,t)}}updateScrollableElementInfo(e){this.scrollController&&e&&this.scrollController.updateScrollableElementInfo(e)}connectResizeObservers(){if(this.contentContainerElement){this.contentResizeObserver.observe(this);for(let e=0;e<this.contentContainerElement.children.length;e++)this.contentResizeObserver.observe(this.contentContainerElement.children[e]);this.connectHeaderResizeObserver(),this.connectFooterResizeObserver(),this.connectLeftResizeObserver(),this.connectRightResizeObserver()}}connectHeaderResizeObserver(){const e=this.getHeaderElements();e&&e.forEach((e=>{this.headerResizeObserver.observe(e)}))}connectFooterResizeObserver(){const e=this.getFooterElements();e&&e.forEach((e=>{this.footerResizeObserver.observe(e)}))}connectLeftResizeObserver(){const e=this.getLeftElements();e&&e.forEach((e=>{this.leftResizeObserver.observe(e)}))}connectRightResizeObserver(){const e=this.getRightElements();e&&e.forEach((e=>{this.rightResizeObserver.observe(e)}))}reconnectHeaderResizeObserver(){this.headerResizeObserver.disconnect(),this.connectHeaderResizeObserver()}reconnectFooterResizeObserver(){this.footerResizeObserver.disconnect(),this.connectFooterResizeObserver()}reconnectLeftResizeObserver(){this.leftResizeObserver.disconnect(),this.connectLeftResizeObserver()}reconnectRightResizeObserver(){this.rightResizeObserver.disconnect(),this.connectRightResizeObserver()}onContentScroll(e){this.preventScrollEvent||(this.refresh(S.ScrollBars),this.setScrollAttribute())}setScrollAttribute(){const e="scrolling";n.setAttribute(this,e),this.scrollAttributeLifespanTimeoutId&&clearTimeout(this.scrollAttributeLifespanTimeoutId),this.scrollAttributeLifespanTimeoutId=setTimeout((()=>{n.removeAttribute(this,e)}),2e3)}onVerticalScrollBarClick(e){this.scrollController&&(this.scrollController.vertScrollByPointerClick(e.offsetY),this.refreshScrollPosition())}onHorizontalScrollBarClick(e){this.scrollController&&(this.scrollController.horScrollByPointerClick(e.offsetX),this.refreshScrollPosition())}onVerticalScrollThumbMove(e){this.preventScrollEvent=!0,this.scrollController&&(this.scrollController.vertScrollByPointerMove(e.detail.deltaY),this.refreshScrollPosition())}onHorizontalScrollThumbMove(e){this.preventScrollEvent=!0,this.scrollController&&(this.scrollController.horScrollByPointerMove(e.detail.deltaX),this.refreshScrollPosition())}getScrollElement(e){return this.querySelector(`:scope > .${e}`)}onContentSizeChanged(e,t){this.refreshUI()}onHeaderSizeChanged(e,t){if(e.length<1||!this.scrollController)return;if(!e[0].target.isConnected)return void this.reconnectHeaderResizeObserver();const l=this.getElementsRectangle(this.getHeaderElements());l&&this.scrollController.updateTopPanelHeight(l.height),this.refreshScrollPosition()}onFooterSizeChanged(e,t){if(e.length<1||!this.scrollController)return;if(!e[0].target.isConnected)return void this.reconnectFooterResizeObserver();const l=this.getElementsRectangle(this.getFooterElements());l&&this.scrollController.updateBottomPanelHeight(l.height),this.refreshScrollPosition()}onLeftSizeChanged(e,t){if(e.length<1||!this.scrollController)return;if(!e[0].target.isConnected)return void this.reconnectLeftResizeObserver();const l=this.getElementsRectangle(this.getLeftElements());l&&this.scrollController.updateLeftPanelWidth(l.width),this.refreshScrollPosition()}onRightSizeChanged(e,t){if(e.length<1||!this.scrollController)return;if(!e[0].target.isConnected)return void this.reconnectRightResizeObserver();const l=this.getElementsRectangle(this.getRightElements());l&&this.scrollController.updateRightPanelWidth(l.width),this.refreshScrollPosition()}processRequestMakeElementVisible(e){if(!e)return;const t=JSON.parse(e);if(3===t.length){const e=t[0];let l=null,n=null;null!==t[1]&&(l=t[1]),null!==t[2]&&(n=t[2]),e?this.execRequestMakeElementVisible(e,l,n):this.cancelRequestMakeElementVisible()}}execRequestMakeElementVisible(e,t,l){this.scheduleMakeElementVisible(e,t,l)}cancelRequestMakeElementVisible(){-1!==this.currentMakeElementVisibleTimeoutId&&clearTimeout(this.currentMakeElementVisibleTimeoutId)}scheduleMakeElementVisible(e,t,l){-1!==this.currentMakeElementVisibleTimeoutId&&clearTimeout(this.currentMakeElementVisibleTimeoutId),this.currentMakeElementVisibleTimeoutId=setTimeout((()=>{this.canMakeElementVisible?this.makeElementVisible(e,t,l):this.scheduleMakeElementVisible(e,t,l)}))}makeElementVisible(e,t,l){if(!this.contentContainerElement)return;const n=this.contentContainerElement.querySelector(e);n?(E.scrollToElementRelyOnStickyDescendants(n,t,l,this),this.dispatchEvent(new g(n,!0))):this.makeUnrenderedElementVisible(e,t,l)}makeUnrenderedElementVisible(e,t,l){}static scrollToElementRelyOnStickyDescendants(e,t,l,n){if(n.contentContainerElement){const r=n.getDataAreaRectangle(),o=r.y,i=r.x,s=r.y+r.height,c=r.x+r.width,h=e.getBoundingClientRect();let a=this.calcScrollOffset(o,s,h.top,h.bottom,t),m=this.calcScrollOffset(i,c,h.left,h.right,l);const u=Math.round(n.contentContainerElement.scrollTop+a),f=Math.round(n.contentContainerElement.scrollLeft+m);a=u-n.contentContainerElement.scrollTop,m=f-n.contentContainerElement.scrollLeft,0===a&&0===m||(n.contentContainerElement.scrollBy(m,a),n.refresh(S.ScrollBars))}const r=E.findClosestViewer(n);r&&E.scrollToElementRelyOnStickyDescendants(e,null,null,r)}static calcScrollOffset(e,t,l,n,r){switch(r){case u.Top||f.Left:return l-e;case u.Bottom||f.Right:return n-t;case u.None||f.None:return 0}return e>l?l-e:t<n?Math.min(n-t,l-e):0}static findClosestViewer(e){return e.parentElement?e.parentElement.closest(`.${l.ClassName}`):null}static get observedAttributes(){return["reset-v-scroll-guid","reset-h-scroll-guid","request-make-element-visible","header-selector","footer-selector","left-selector","right-selector"]}attributeChangedCallback(e,t,l){switch(e){case"reset-v-scroll-guid":this.contentContainerElement&&l&&(this.contentContainerElement.scrollTop=0,this.refresh(S.ScrollBars));break;case"reset-h-scroll-guid":this.contentContainerElement&&l&&(this.contentContainerElement.scrollLeft=0,this.refresh(S.ScrollBars));break;case"header-selector":this.contentContainerElement&&setTimeout((()=>this.reconnectHeaderResizeObserver()));break;case"footer-selector":this.contentContainerElement&&setTimeout((()=>this.reconnectFooterResizeObserver()));break;case"left-selector":this.contentContainerElement&&setTimeout((()=>this.reconnectLeftResizeObserver()));break;case"right-selector":this.contentContainerElement&&setTimeout((()=>this.reconnectRightResizeObserver()));break;case"request-make-element-visible":this.processRequestMakeElementVisible(l)}}}customElements.define("dxbl-scroll-bar",c),customElements.define(h,E);export{E as D,f as H,a as R,d as S,u as V,g as a,b,h as c};
