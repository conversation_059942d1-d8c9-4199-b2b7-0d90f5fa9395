import{c as e}from"./dom-utils-d057dcaa.js";import{d as t}from"./define-custom-element-7c2e65e2.js";import"./dom-554d0cc7.js";import"./_commonjsHelpers-41cdd1e7.js";import"./tslib.es6-d65164b3.js";import"./common-48ec40e2.js";import"./browser-3fc721b7.js";import"./css-classes-c63af734.js";const s=document.createElement("style");s.type="text/css";class c extends HTMLElement{constructor(){super(),this.resourceStyleElement=s.cloneNode()}static get observedAttributes(){return["css"]}attributeChangedCallback(t,s,c){"css"===t&&async function(t,s){t&&await e((()=>s.innerHTML=t))}(c,this.resourceStyleElement)}connectedCallback(){this.resourceStyleElement.isConnected||document.head.appendChild(this.resourceStyleElement)}disconnectedCallback(){this.resourceStyleElement.isConnected&&this.resourceStyleElement.remove()}}t(customElements,"dxbl-dynamic-stylesheet",c);export{c as default};
