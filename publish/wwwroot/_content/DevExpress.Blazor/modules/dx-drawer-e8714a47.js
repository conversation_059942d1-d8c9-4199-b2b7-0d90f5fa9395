import{_ as t}from"./tslib.es6-d65164b3.js";import{S as e}from"./single-slot-element-base-01d93921.js";import{f as s}from"./focustrap-d11cfef9.js";import{n as o}from"./property-4ec0b52d.js";import{e as a}from"./custom-element-267f9a21.js";import"./data-qa-utils-8be7c726.js";import"./const-90026e45.js";import"./dx-ui-element-0c1e122f.js";import"./lit-element-base-9288748e.js";import"./dx-license-30fd02d1.js";import"./lit-element-462e7ad3.js";import"./logicaltreehelper-67db40f1.js";import"./layouthelper-67dd777a.js";import"./point-e4ec110e.js";import"./constants-7c047c0d.js";import"./tabbable-a2ae89a6.js";import"./focushelper-2eea96ca.js";var i;let c=i=class extends e{constructor(){super(...arguments),this._focusInHandler=this.focusIn.bind(this),this._focusOutHandler=this.focusOut.bind(this),this.focusLoop=!1}disconnectedCallback(){super.disconnectedCallback(),this.focusLoop&&this.removeFocusTrapEvents()}updated(t){super.updated(t),t.has("focusLoop")&&this.onFocusLoopUpdated()}onFocusLoopUpdated(){this.focusLoop?(this.addFocusTrapEvents(),this.contains(document.activeElement)&&this.activateFocusTrap()):(this.removeFocusTrapEvents(),this.deactivateFocusTrap())}addFocusTrapEvents(){this.addEventListener("focusin",this._focusInHandler),this.addEventListener("focusout",this._focusOutHandler)}removeFocusTrapEvents(){this.removeEventListener("focusin",this._focusInHandler),this.removeEventListener("focusout",this._focusOutHandler)}focusIn(t){this.contains(t.relatedTarget)||this.activateFocusTrap()}focusOut(t){this.contains(t.relatedTarget)||this.contains(document.activeElement)||this.deactivateFocusTrap()}activateFocusTrap(){s.activate(this,!0,!0,!1)}deactivateFocusTrap(){s.deactivate(this,!1)}};c.TagName="dxbl-drawer-panel",t([o({type:Boolean,attribute:"focus-loop"})],c.prototype,"focusLoop",void 0),c=i=t([a(i.TagName)],c);export{c as DxDrawerPanel};
