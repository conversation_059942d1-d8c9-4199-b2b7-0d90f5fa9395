import{C as s}from"./css-classes-c63af734.js";class e{static getSvgHtml(e){return`\n<svg class="${s.Image}" role="img" aria-hidden="true">\n    <use class="dxbl-icon-set-default" href="_content/DevExpress.Blazor/dx-blazor.svg#${e}"></use>\n    <use class="dxbl-icon-set-fluent" href="_content/DevExpress.Blazor/dx-blazor.svg#${e}-fluent"></use>\n</svg>`}}e.ArrowUpIconName="dx-arrow-up",e.ArrowDownIconName="dx-arrow-down";export{e as S};
