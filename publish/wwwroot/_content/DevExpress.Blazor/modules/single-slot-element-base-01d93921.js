import{_ as e}from"./tslib.es6-d65164b3.js";import{D as t}from"./data-qa-utils-8be7c726.js";import{G as n}from"./const-90026e45.js";import{D as r}from"./dx-ui-element-0c1e122f.js";import{n as o}from"./property-4ec0b52d.js";let a=!1;class i extends r{constructor(){super(...arguments),this.boundSlotChangedHandler=this.onSlotChanged.bind(this),this.renderGuid="",this._removeEnchancedNavigationSubscription=()=>{}}contentChanged(){}connectedOrContentChanged(){}needReasignAttributes(){return!0}connectedCallback(){super.connectedCallback(),this.connectedOrContentChanged()}restoreState(){this.needReasignAttributes()&&this.constructor.observedAttributes.forEach((e=>{if(e!==n.renderGuidAttributeName&&this.hasAttribute(e)){const t=this.getAttribute(e);this.setAttribute(e,""),this.setAttribute(e,t)}}))}attributeChangedCallback(e,t,r){var o;e===n.renderGuidAttributeName&&t&&(null===(o=window.Blazor)||void 0===o?void 0:o.addEventListener)?this._removeEnchancedNavigationSubscription=(e=>{var t;const n=()=>{var t;a=!0,e(),null===(t=window.Blazor)||void 0===t||t.removeEventListener("enhancedload",n),a=!1};return null===(t=window.Blazor)||void 0===t||t.addEventListener("enhancedload",n),()=>{var e;a||null===(e=window.Blazor)||void 0===e||e.removeEventListener("enhancedload",n)}})((()=>this.restoreState())):super.attributeChangedCallback(e,t,r)}disconnectedCallback(){super.disconnectedCallback(),this._removeEnchancedNavigationSubscription()}onSlotChanged(e){this.connectedOrContentChanged(),this.contentChanged(),this.readyOnConnectedCallback||t.setLoaded(this)}createRenderRoot(){const e=super.createRenderRoot(),t=document.createElement("slot");return e.appendChild(t),t.addEventListener("slotchange",this.boundSlotChangedHandler),e}}e([o({type:String,attribute:n.renderGuidAttributeName})],i.prototype,"renderGuid",void 0);export{i as S};
