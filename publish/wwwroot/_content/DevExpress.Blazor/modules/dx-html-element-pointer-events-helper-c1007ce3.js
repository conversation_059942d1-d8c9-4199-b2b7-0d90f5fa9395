import{d as e}from"./dom-554d0cc7.js";import{E as t}from"./eventhelper-8bcec49f.js";import{A as n,a as i,D as o}from"./constants-7c047c0d.js";import{D as r}from"./devices-17b9ba08.js";const s=100,a=500;var d,c;!function(e){e[e.None=0]="None",e[e.Click=1]="Click",e[e.DblClick=2]="DblClick",e[e.Dragging=4]="Dragging"}(d||(d={})),function(e){e[e.Stopped=0]="Stopped",e[e.WaitingForDragging=1]="WaitingForDragging",e[e.Dragging=2]="Dragging"}(c||(c={}));class l extends CustomEvent{constructor(e){super(l.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!0})}}l.eventName="dxpointerdragstart";class h extends CustomEvent{constructor(e){super(h.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!0})}}h.eventName="dxpointerdragstop";class u extends Event{constructor(){super(u.eventName,{bubbles:!0,composed:!1,cancelable:!0})}}u.eventName="dxpointerdragcancel";class p extends CustomEvent{constructor(e){super(p.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!0})}}p.eventName="dxclick";class v extends CustomEvent{constructor(e){super(v.eventName,{detail:e,bubbles:!0,composed:!1,cancelable:!0})}}v.eventName="dxdblclick";class g{static containsInComposedPath(e,t){const n=e.composedPath();for(const e in n)if(n[e]===t)return!0;return!1}}const m=function(e,t,o=null){return!(!e||!e.matches(t))&&(getComputedStyle(e).textOverflow===i.ellipsis&&(!!b(e,o)&&!(!e.hasAttribute(n.textTrimmed)&&e.getAttribute(n.title))))},b=function(e,t=null){for(let n=0;n<e.children.length;n++)if(!(getComputedStyle(e.children[n]).display===o.inline||t&&e.children[n].matches(t)))return!1;return!0};class D{constructor(e){this.boundOnPointerDownHandler=this.onPointerDown.bind(this),this.boundOnPointerMoveHandler=this.onPointerMove.bind(this),this.boundOnPointerUpHandler=this.onPointerUp.bind(this),this.boundOnPointerCancelHandler=this.onPointerCancel.bind(this),this.boundOnDraggingKeyDownHandler=this.onDraggingKeyDown.bind(this),this.boundOnDraggingWindowBlurHandler=this.onDraggingWindowBlur.bind(this),this.boundOnDraggingContextMenuHandler=this.onDraggingContextMenu.bind(this),this.boundOnGotPointerCaptureHandler=this.onGotPointerCapture.bind(this),this.boundOnPointerClickHandler=this.onPointerClick.bind(this),this.boundOnPointerDblClickHandler=this.onPointerDblClick.bind(this),this.element=e,this.state=c.Stopped;const t=e.hoverTitleElementsSelector,i=e.bypassNonInlineHoverTitleElementChildSelector;t&&r.isHoverableDevice()&&(this.boundOnPointerOverHandler=e=>function(e,t,i=null){const o=e.target;m(o,t,i)&&(o.offsetWidth<o.scrollWidth?(o.setAttribute(n.title,o.innerText),o.setAttribute(n.textTrimmed,"")):(o.removeAttribute(n.title),o.removeAttribute(n.textTrimmed)))}(e,t,i))}initialize(){this.addPointerEventsSubscriptions()}dispose(){this.removePointerEventsSubscriptions()}reinitialize(e){this.removePointerEventsSubscriptions(),this.element=e,this.addPointerEventsSubscriptions()}static preventDefaultBrowserBehaviour(e){e.cancelable&&e.preventDefault()}onGotPointerCapture(e){if(!this.shouldProcessPointerEvent(e))return;if(!this.pointerDownContext||!this.pointerDownContext.isLongTap&&this.state!==c.WaitingForDragging)return;const t=e.target;t&&t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId)}get handlePointerEventsMode(){return this.element.handlePointerEventsMode}get handlePointerEventsTarget(){var e;return null===(e=this.element)||void 0===e?void 0:e.handlePointerEventsTarget}get handlePointerEventsDelay(){return this.element.handlePointerEventsDelay}shouldProcessPointerEvent(e){return t.containsInComposedPath(e,(e=>e===this.handlePointerEventsTarget))&&this.element.shouldProcessPointerEvent(e)}addPointerEventsSubscriptions(){const e=this.handlePointerEventsTarget;if(e){if(this.element!==e&&this.element.contains(e))throw new Error("Specify the handlePointerEventsTarget property as web component or it parent.");e.addEventListener("click",this.boundOnPointerClickHandler),e.addEventListener("dblclick",this.boundOnPointerDblClickHandler),e.addEventListener("pointerdown",this.boundOnPointerDownHandler),e.addEventListener("gotpointercapture",this.boundOnGotPointerCaptureHandler),this.boundOnPointerOverHandler&&e.addEventListener("pointerover",this.boundOnPointerOverHandler)}}removePointerEventsSubscriptions(){const e=this.handlePointerEventsTarget;e&&(e.removeEventListener("click",this.boundOnPointerClickHandler),e.removeEventListener("dblclick",this.boundOnPointerDblClickHandler),e.removeEventListener("pointerdown",this.boundOnPointerDownHandler),e.removeEventListener("gotpointercapture",this.boundOnGotPointerCaptureHandler),this.boundOnPointerOverHandler&&e.removeEventListener("pointerover",this.boundOnPointerOverHandler))}onPointerDown(t){var n;if(D.preventClickEvent=!1,!this.shouldProcessPointerEvent(t))return;const i="touch"===t.pointerType;i&&this.state===c.Dragging||(this.pointerDownContext={source:t,isTouch:i,isLongTap:!1},0!=(this.handlePointerEventsMode&d.Dragging)?0===t.button&&(this.state=c.WaitingForDragging,i&&(this.touchDeviceInteractionTimer=setTimeout(this.onTouchDeviceInteractionTimerTimeout.bind(this),this.handlePointerEventsDelay)),document.addEventListener("pointermove",this.boundOnPointerMoveHandler),document.addEventListener("pointerup",this.boundOnPointerUpHandler),document.addEventListener("pointercancel",this.boundOnPointerCancelHandler),document.addEventListener("keydown",this.boundOnDraggingKeyDownHandler),document.addEventListener("contextmenu",this.boundOnDraggingContextMenuHandler),window.addEventListener("blur",this.boundOnDraggingWindowBlurHandler),e.DomUtils.addClassName(document.body,"dx-prevent-selection")):t.shiftKey&&(null===(n=null===document||void 0===document?void 0:document.getSelection())||void 0===n||n.removeAllRanges()))}onPointerMove(e){var t;this.state!==c.Stopped&&this.state===c.WaitingForDragging&&((null===(t=this.pointerDownContext)||void 0===t?void 0:t.isTouch)?this.isDistanceExceedLimit(e)&&this.stopPointerInteraction():this.isDistanceExceedLimit(e)&&this.tryStartPointerInteraction())}onPointerCancel(e){if(this.state===c.Stopped)return;this.stopPointerInteraction();const t=new u;this.element.dispatchEvent(t)}onDraggingKeyDown(e){if(this.state!==c.Stopped&&"Escape"===e.key){this.stopPointerInteraction();const e=new u;this.element.dispatchEvent(e)}}onDraggingWindowBlur(e){if(this.state===c.Stopped)return;this.stopPointerInteraction();const t=new u;this.element.dispatchEvent(t)}onDraggingContextMenu(e){this.state!==c.Stopped&&(e.preventDefault(),e.stopPropagation())}onPointerUp(e){if(this.pointerDownContext&&this.state===c.Dragging){const t=new h({source:e,isTouch:this.pointerDownContext.isTouch,isLongTap:this.pointerDownContext.isLongTap});this.element.dispatchEvent(t),this.pointerDownContext.isTouch||(D.preventClickEvent=!0)}this.stopPointerInteraction(),this.element.releasePointerCapture(e.pointerId)}onPointerClick(e){0!=(this.handlePointerEventsMode&d.Click)&&this.shouldProcessPointerEvent(e)&&(D.preventClickEvent||0!==e.button||this.isDistanceExceedLimit(e)?e.stopPropagation():(this.raiseDxPointerEvent(e,new p({source:e,isTouch:!!this.pointerDownContext&&this.pointerDownContext.isTouch}),p.eventName),delete this.pointerDownContext))}onPointerDblClick(e){0!=(this.handlePointerEventsMode&d.DblClick)&&this.shouldProcessPointerEvent(e)&&(0!==e.button||this.isDistanceExceedLimit(e)?e.stopPropagation():this.raiseDxPointerEvent(e,new v({source:e,isTouch:!!this.pointerDownContext&&this.pointerDownContext.isTouch}),v.eventName))}raiseDxPointerEvent(e,t,n){e[n]||(this.element.dispatchEvent(t),e[n]=!0)}tryStartPointerInteraction(){D.pointerInteractionElement?this.state=c.Stopped:this.startPointerInteraction()}startPointerInteraction(){this.state=c.Dragging,D.pointerInteractionElement=this.element,document.addEventListener("touchmove",D.preventDefaultBrowserBehaviour,{passive:!1}),this.raisePointerDragStartEvent()}stopPointerInteraction(){this.clearTouchDeviceInteractionTimer(),this.state===c.Dragging&&(delete D.pointerInteractionElement,this.state=c.Stopped),document.removeEventListener("touchmove",D.preventDefaultBrowserBehaviour),document.removeEventListener("pointermove",this.boundOnPointerMoveHandler),document.removeEventListener("pointerup",this.boundOnPointerUpHandler),document.removeEventListener("pointercancel",this.boundOnPointerCancelHandler),document.removeEventListener("keydown",this.boundOnDraggingKeyDownHandler),document.removeEventListener("contextmenu",this.boundOnDraggingContextMenuHandler),window.removeEventListener("blur",this.boundOnDraggingWindowBlurHandler),e.DomUtils.removeClassName(document.body,"dx-prevent-selection"),delete this.pointerDownContext}clearTouchDeviceInteractionTimer(){this.touchDeviceInteractionTimer&&(clearTimeout(this.touchDeviceInteractionTimer),delete this.touchDeviceInteractionTimer)}onTouchDeviceInteractionTimerTimeout(){this.state===c.WaitingForDragging&&this.pointerDownContext&&this.pointerDownContext.isTouch&&(this.pointerDownContext.isLongTap=!0,this.tryStartPointerInteraction(),this.clearTouchDeviceInteractionTimer())}isDistanceExceedLimit(e){return!!this.pointerDownContext&&(Math.abs(this.pointerDownContext.source.clientX-e.clientX)>4||Math.abs(this.pointerDownContext.source.clientY-e.clientY)>4)}raisePointerDragStartEvent(){if(!this.pointerDownContext)return;const e=new l({source:this.pointerDownContext.source,isTouch:this.pointerDownContext.isTouch,isLongTap:this.pointerDownContext.isLongTap});this.element.dispatchEvent(e)}}D.preventClickEvent=!1;export{D,d as H,a as L,l as P,s as T,h as a,g as b,u as c,p as d,v as e};
