import{_ as e}from"./tslib.es6-d65164b3.js";import{d as t}from"./dom-554d0cc7.js";import{C as s}from"./custom-events-helper-e7f279d3.js";import{S as i}from"./single-slot-element-base-01d93921.js";import{C as o}from"./css-classes-c63af734.js";import{n as r}from"./property-4ec0b52d.js";const a="dxbl-upload";class n{constructor(e){this.progress=e}}class l{constructor(e){this.requestOptions=e}}class d{constructor(e){this.lastErrorCode=e}}class u extends CustomEvent{constructor(e){super(u.eventName,{detail:new n(e),bubbles:!0,composed:!0})}}u.eventName=a+".uploadprogress",s.register(u.eventName,(e=>e.detail));class c extends CustomEvent{constructor(e){super(c.eventName,{detail:new l(e),bubbles:!0,composed:!0})}}c.eventName=a+".customizeformdata",s.register(c.eventName,(e=>e.detail));class h extends CustomEvent{constructor(e){super(h.eventName,{detail:new d(e),bubbles:!0,composed:!0})}}h.eventName=a+".erroroccurred",s.register(h.eventName,(e=>e.detail));const p=10;var m,f,g,S,F;!function(e){e.acceptedFileTypes="accepted-file-types",e.allowedFileExtensions="allowed-file-extensions",e.externalSelectButtonCssSelector="external-select-button-css-selector",e.showEmbeddedSelectButton="show-embedded-select-button",e.chunkSize="chunk-size",e.maxFileSize="max-file-size",e.minFileSize="min-file-size",e.maxFileCount="max-file-count",e.multiple="multiple",e.uploadMode="upload-mode",e.uploadTechnology="upload-technology",e.dragOverClassName="drag-over-class-name",e.dropZoneCssSelector="drop-zone-css-selector",e.customizeFormData="customize-form-data"}(m||(m={})),function(e){e[e.Instant=0]="Instant",e[e.OnButtonClick=1]="OnButtonClick"}(f||(f={})),function(e){e[e.Http=0]="Http",e[e.JsInterop=1]="JsInterop"}(g||(g={})),function(e){e[e.WaitingStart=0]="WaitingStart",e[e.PendingUpload=1]="PendingUpload",e[e.Uploading=2]="Uploading",e[e.Paused=3]="Paused",e[e.Complete=4]="Complete",e[e.Canceled=5]="Canceled",e[e.Error=6]="Error",e[e.Removing=7]="Removing"}(S||(S={})),function(e){e[e.MaxFileCountExceeded=0]="MaxFileCountExceeded",e[e.DragAndDropMultipleFiles=1]="DragAndDropMultipleFiles"}(F||(F={}));class v{constructor(){this.guids=[],this.actions=[],this.reloadedFileGuids=[]}}class C{constructor(e){this.FileName=e.fileInfo.name,this.FileSize=e.fileInfo.size,this.FileType=e.fileInfo.type,this.LastModified=e.fileInfo.lastModified,this.FileGuid=e.fileInfo.guid,this.Index=e.chunkIndex,this.TotalCount=e.totalChunkCount}}class E{constructor(e,t,s){this._value=e,this._status=s.uploadMode===f.Instant?S.PendingUpload:S.WaitingStart,this._chunkIndex=0,this._loadedBytes=0,this._totalChunkCount=0,this._isFileExtensionValid=E.validateFileExtension(e,s),this._isMinSizeValid=E.validateMinFileSize(e,s),this._isMaxSizeValid=E.validateMaxFileSize(e,s),this._fileInfo={name:e.name,size:e.size,type:e.type,lastModified:1e4*e.lastModified+621355968e9,guid:t},this._request=null,this._onLoadStart=null,this._onProgress=null,this._onAbort=null,this._onPause=null,this._onError=null,this._onLoadEnd=null,this.isValid()||(this._status=S.Error)}get value(){return this._value}get status(){return this._status}set status(e){this._status=e}get chunkIndex(){return this._chunkIndex}set chunkIndex(e){this._chunkIndex=e}get loadedBytes(){return this._loadedBytes}set loadedBytes(e){this._loadedBytes=e}get totalChunkCount(){return this._totalChunkCount}set totalChunkCount(e){this._totalChunkCount=e}get isFileExtensionValid(){return this._isFileExtensionValid}get isMinSizeValid(){return this._isMinSizeValid}get isMaxSizeValid(){return this._isMaxSizeValid}get fileInfo(){return this._fileInfo}get request(){return this._request}set request(e){this._request=e}get onLoadStart(){return this._onLoadStart}set onLoadStart(e){this._onLoadStart=e}get onProgress(){return this._onProgress}set onProgress(e){this._onProgress=e}get onAbort(){return this._onAbort}set onAbort(e){this._onAbort=e}get onError(){return this._onError}set onError(e){this._onError=e}get onLoadEnd(){return this._onLoadEnd}set onLoadEnd(e){this._onLoadEnd=e}isValid(){return this.isFileExtensionValid&&this.isMaxSizeValid&&this.isMinSizeValid}isUploadComplete(){return this.loadedBytes>=this.fileInfo.size}loadStart(){this.status!==S.Uploading&&(this.status=S.Uploading,this.onLoadStart&&this.onLoadStart.call(this))}progress(){this.onProgress&&this.onProgress.call(this)}loadEnd(){this.isUploadComplete()&&(this.status=S.Complete,this.onLoadEnd&&this.onLoadEnd.call(this))}abort(e=!0){this.status!==S.Canceled&&(this.status=S.Canceled,this.request&&this.request.abort(),e&&this.onAbort&&this.onAbort.call(this),this.chunkIndex=0,this.loadedBytes=0)}error(e){this.status=S.Error,this.onError&&this.onError.call(this,e)}static validateFileExtension(e,t){const s=t.allowedFileExtensions,i=e.name.substring(e.name.lastIndexOf(".")).toLowerCase();if(0===s.length)return!0;for(let e=0;e<s.length;e++)if(i===s[e].toLowerCase())return!0;return!1}static validateMinFileSize(e,t){const s=t.minFileSize;return!(s>0)||e.size>=s}static validateMaxFileSize(e,t){const s=t.maxFileSize;return!(s>0)||e.size<=s}updateStatus(e,t){var s,i,o,r,a,n,l;switch(this.status=e,this.status){case S.WaitingStart:case S.PendingUpload:break;case S.Uploading:null===(s=this.onLoadStart)||void 0===s||s.call(this);break;case S.Paused:break;case S.Complete:this.loadedBytes=this.value.size,null===(i=this.onLoadEnd)||void 0===i||i.call(this);break;case S.Canceled:null===(o=this.onAbort)||void 0===o||o.call(this);break;case S.Error:{const e=new EventTarget;e.status=null!==(r=null==t?void 0:t.status)&&void 0!==r?r:-1,e.statusText=null!==(a=null==t?void 0:t.statusText)&&void 0!==a?a:"Unexpected error",e.responseText=null!==(n=null==t?void 0:t.responseText)&&void 0!==n?n:"Unexpected error",null===(l=this.onError)||void 0===l||l.call(this,e);break}}}}class y{constructor(e){this.control=e,this.requestMetadata=null}customizeFormData(e){return this.control.dispatchEvent(new c(e)),Promise.resolve()}}class b extends y{constructor(e){super(e)}upload(e,t,s,i){if(!e)return Promise.reject();e.totalChunkCount||(e.chunkIndex=0,e.totalChunkCount=b.calculateTotalChunkCount(e.fileInfo.size,t));const o=t.chunkSize*e.chunkIndex,r=e.value.slice(o,o+t.chunkSize);return this.requestMetadata={formData:new FormData,request:t,file:e,loadEnd:i},this.createFormData(t.name,r,e,s)}createFormData(e,t,s,i){var o,r;const a=new C(s);return null===(o=this.requestMetadata)||void 0===o||o.formData.append(e,t),null===(r=this.requestMetadata)||void 0===r||r.formData.append("chunkMetadata",JSON.stringify(a)),this.control.customizeFormData||i||(i={}),i?this.onCustomizeChunkMetadataResponse(i):this.customizeFormData(a)}onCustomizeChunkMetadataResponse(e){return new Promise(((t,s)=>{if(null==this.requestMetadata)return void s();const{formData:i,request:o,file:r,loadEnd:a}=this.requestMetadata;if(r.status!==S.Paused&&r.status!==S.Canceled){for(const t in e)if(Object.prototype.hasOwnProperty.call(e,t)){const s=e[t];i.append(t,s instanceof Object?JSON.stringify(s):s)}for(const e in o.requestData)Object.prototype.hasOwnProperty.call(o.requestData,e)&&i.append(e,o.requestData[e]);r.request=x.sendRequest(i,{crossDomain:!1,url:o.uploadUrl,method:"POST",headers:o.requestHeaders,onAbort:()=>{r.abort(),s()},onProgress:()=>{},onError:e=>{e.target&&r.error(e.target),s()},onLoadStart:()=>{r.loadStart()},onLoad:e=>{if(e.target)if(200===e.target.status){const n=i.get(o.name);r.chunkIndex++,r.loadedBytes+=n.size,r.progress(),a(r).then(t,s).catch((()=>{s(),r.error(e.target)}))}else r.error(e.target),s()},onLoadEnd:null})}else s()}))}static calculateTotalChunkCount(e,t){let s=Math.trunc(e/t.chunkSize);return e%t.chunkSize>0&&s++,s}}class I extends y{constructor(e){super(e)}upload(e,t,s,i){return e?(this.requestMetadata={formData:new FormData,request:t,file:e,loadEnd:i},this.createFormData(t.name,e,s)):Promise.reject()}createFormData(e,t,s){var i;return null===(i=this.requestMetadata)||void 0===i||i.formData.append(e,t.value),this.onCustomizeChunkMetadataResponse(s||{})}onCustomizeChunkMetadataResponse(e){return new Promise(((e,t)=>{if(null==this.requestMetadata)return void t();const{formData:s,request:i,file:o,loadEnd:r}=this.requestMetadata;for(const e in i.requestData)Object.prototype.hasOwnProperty.call(i.requestData,e)&&s.append(e,i.requestData[e]);let a=!1;o.request=x.sendRequest(s,{url:i.uploadUrl,method:"POST",headers:i.requestHeaders,crossDomain:!1,onProgress:e=>{a=!0,o.loadedBytes=e.loaded>o.fileInfo.size?o.fileInfo.size:e.loaded,o.progress()},onAbort:e=>{o.abort(),t()},onError:e=>{e.target&&o.error(e.target),t()},onLoadStart:e=>{o.loadStart()},onLoad:s=>{s.target&&(200===s.target.status?(a||(o.loadedBytes=o.fileInfo.size,o.progress()),r(o,s).then(e,t).catch((()=>{t(),o.error(s.target)}))):(o.error(s.target),t()))},onLoadEnd:null})}))}}class x{static sendRequest(e,t){const s=new XMLHttpRequest;t.crossDomain=x.isCrossDomain(t.url);const i=x.getRequestHeaders(t);s.open(t.method,t.url,!0),t.onLoadStart&&(s.upload.onloadstart=t.onLoadStart),t.onLoad&&(s.onload=t.onLoad),t.onLoadEnd&&(s.upload.onloadend=t.onLoadEnd),t.onProgress&&(s.upload.onprogress=t.onProgress),t.onError&&(s.upload.onerror=t.onError),t.onAbort&&(s.upload.onabort=t.onAbort);for(const e in i)Object.prototype.hasOwnProperty.call(i,e)&&i[e]&&s.setRequestHeader(e,i[e]);return s.send(e),s}static getRequestHeaders(e){const t=e.headers||{};return t.Accept=t.Accept||x.getAcceptHeader(),e.crossDomain||t["X-Requested-With"]||(t["X-Requested-With"]="XMLHttpRequest"),t}static getAcceptHeader(){return"*/*"}static isCrossDomain(e){let t=!1;const s=document.createElement("a"),i=document.createElement("a");s.href=window.location.href;try{i.href=e,i.href=i.href,t=s.protocol+"//"+s.host!=i.protocol+"//"+i.host}catch(e){t=!0}return t}}class D{constructor(e){this.control=e,this.state=new z,this.indexMap=new Map,this.dispatchTimerId=-1,this.idleTimerId=-1}dispatch(e,t,s=null){const i=this.getFileIndex(t),o=new k(i,e,t.loadedBytes,t.status);this.state.progressInfos.push(o),s&&this.state.errors.push(s),this.tryPerformDispatch()}forceDelayedDispatch(){-1!==this.dispatchTimerId&&clearTimeout(this.dispatchTimerId),this.performDispatch()}tryPerformDispatch(){if(-1===this.dispatchTimerId){let e=50;-1!==this.idleTimerId&&(clearTimeout(this.idleTimerId),this.idleTimerId=-1,e=200),this.dispatchTimerId=setTimeout((()=>this.performDispatch()),e)}}performDispatch(){this.dispatchTimerId=-1,this.control.dispatchEvent(new u(this.state)),this.state=new z,this.indexMap.clear(),this.idleTimerId=setTimeout((()=>{this.idleTimerId=-1}),1e3)}cancelSendingOfLastEvent(){clearTimeout(this.dispatchTimerId),this.dispatchTimerId=-1}getFileIndex(e){let t=this.indexMap.get(e.fileInfo.guid);return void 0!==t||(t=this.state.fileGuids.length,this.state.fileGuids.push(e.fileInfo.guid),this.indexMap.set(e.fileInfo.guid,t)),t}}class z{constructor(){this.fileGuids=[],this.progressInfos=[],this.errors=[]}}class k{constructor(e,t,s,i){this.index=e,this.type=t,this.loaded=s,this.status=i}}var M,w;!function(e){e[e.Started=0]="Started",e[e.Progress=1]="Progress",e[e.Uploaded=2]="Uploaded",e[e.Aborted=3]="Aborted",e[e.Error=4]="Error"}(M||(M={}));class T{}T.MainElement=o.Prefix+"-upload",T.SelectButton=T.MainElement+"-select-btn",function(e){e.accept="accept",e.multiple="multiple"}(w||(w={}));class B extends i{constructor(){super(),this.onInputChangeHandler=this.onFileInputChange.bind(this),this.onSelectButtonClickHandler=this.onSelectButtonClick.bind(this),this.onFilesDropHandler=this.onFilesDrop.bind(this),this.onFilesDragOverHandler=this.onFilesDragOver.bind(this),this.onFilesDragLeaveHandler=this.onFilesDragLeave.bind(this),this.acceptedFileTypes=null,this.allowedFileExtensions=[],this.externalSelectButtonCssSelector="",this.showEmbeddedSelectButton=!0,this.chunkSize=0,this.maxFileSize=0,this.minFileSize=0,this.maxFileCount=1e3,this.multiple=!1,this.uploadMode=f.Instant,this.uploadTechnology=g.Http,this.dragOverClassName="",this.dropZoneCssSelector="",this.customizeFormData=!1,this.files=new Map,this.progressDispatcher=new D(this),this.recentlyAddedFilesStream=null}firstUpdated(e){super.firstUpdated(e),this.initEvents(),this.prepareInputElement()}initDotNetReference(e){this.dotnetHelper=e,this.dotnetHelper&&this.attachSelectButtonClickHandler()}updateFileStatus(e,t,s){var i,o;null===(o=null===(i=this.files.get(e))||void 0===i?void 0:i.updateStatus)||void 0===o||o.call(i,t,s)}getFileBytes(e){var t;return null===(t=this.files.get(e))||void 0===t?void 0:t.value}getFileInput(){return this.querySelector("input")}getSelectButton(e){return null!=e||(e=this.externalSelectButtonCssSelector),e?document.querySelector(e):this.querySelector(`.${T.SelectButton}`)}getDropZoneContainer(){return this.dropZoneCssSelector?document.querySelector(this.dropZoneCssSelector):null}initEvents(){this.getFileInput().addEventListener("change",this.onInputChangeHandler),this.dotnetHelper&&this.attachSelectButtonClickHandler();const e=this.getDropZoneContainer();e&&(e.addEventListener("drop",this.onFilesDropHandler),e.addEventListener("dragover",this.onFilesDragOverHandler),e.addEventListener("dragleave",this.onFilesDragLeaveHandler))}attachSelectButtonClickHandler(){var e;null===(e=this.getSelectButton())||void 0===e||e.addEventListener("click",this.onSelectButtonClickHandler)}detachSelectButtonClickHandler(e){var t;null===(t=this.getSelectButton(e))||void 0===t||t.removeEventListener("click",this.onSelectButtonClickHandler)}prepareInputElement(){const e=this.getFileInput();this.multiple?e.setAttribute(w.multiple,""):e.removeAttribute(w.multiple),this.acceptedFileTypes&&e.setAttribute(w.accept,this.acceptedFileTypes.join(","))}onFileInputChange(e){this.addFiles(this.createFileItems(this.getFileInput().files)),this.getFileInput().value=""}onSelectButtonClick(e){this.getFileInput().click()}onFilesDrop(e){e.preventDefault(),this.addFiles(this.createFileItems(this.getFileFromDataTransfer(e.dataTransfer))),this.onFilesDragLeave(e)}onFilesDragOver(e){this.dragOverClassName&&e.srcElement&&t.DomUtils.addClassName(e.srcElement,this.dragOverClassName),e.preventDefault()}onFilesDragLeave(e){this.dragOverClassName&&t.DomUtils.removeClassName(e.srcElement,this.dragOverClassName)}raiseErrorOccurred(e){this.dispatchEvent(new h(e))}createFileItems(e){if(!e)return[];const t=[],s=this.getAllowedFileTypes();for(let i=0,o=e[i];o;i++,o=e[i]){const r=new E(o,this.getUUID4(),this);r.isValid()&&s&&!this.isFileTypeAllowed(e[i],s)||t.push(r)}return t}isFileTypeAllowed(e,t){return t.some((function(t){if("."===t[0]){if(t=t.replace(".","\\."),e.name.match(new RegExp(t+"$","i")))return!0}else if(t=t.replace("*",""),e.type.match(new RegExp(t,"i")))return!0}))}addFiles(e){this.dotnetHelper&&0!==e.length&&(this.maxFileCount>0&&this.files.size+e.length>this.maxFileCount?this.raiseErrorOccurred(F.MaxFileCountExceeded):(e.forEach((e=>this.files.set(e.fileInfo.guid,e))),this.registerFileInfos(e)))}registerFileInfos(e){var t;this.recentlyAddedFilesStream=(new TextEncoder).encode(JSON.stringify(this.getFileInfosCollection(e))),null===(t=this.dotnetHelper)||void 0===t||t.invokeMethodAsync("RegisterAddedFiles",this.recentlyAddedFilesStream.length)}getRecentlyAddedFileInfosStream(){return this.recentlyAddedFilesStream}getFileFromDataTransfer(e){const t=[];if(e)if(e.items){if(!this.multiple&&this.files.size+e.items.length>1)return this.raiseErrorOccurred(F.DragAndDropMultipleFiles),null;for(let s=0,i=e.items[s];i;s++,i=e.items[s])if("file"===i.kind){const e=i.getAsFile();e&&t.push(e)}}else for(let s=0,i=e.files[s];i;s++,i=e.files[s])t.push(i);return t}getFileInfosCollection(e){return e.map(this.createFileViewInfo)}createFileViewInfo(e){return{name:e.fileInfo.name,size:e.fileInfo.size,type:e.fileInfo.type,lastModified:e.fileInfo.lastModified,guid:e.fileInfo.guid,loadedBytes:e.loadedBytes,status:e.status,isFileExtensionValid:e.isFileExtensionValid,isMinSizeValid:e.isMinSizeValid,isMaxSizeValid:e.isMaxSizeValid}}getAllowedFileTypes(){return this.acceptedFileTypes&&this.acceptedFileTypes.length>0?this.acceptedFileTypes.map((e=>e.trim())):null}getUUID4(){const e=new Uint8Array(16);crypto.getRandomValues(e),e[8]&=63,e[8]|=128,e[6]&=15,e[6]|=64;let t=0;return"XXXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX".replace(/XX/g,(()=>e[t++].toString(16).padStart(2,"0")))}reloadFile(e,t){if(e.status===S.Canceled&&t){const s=this.createFileItems([e.value])[0];s.status=S.PendingUpload,s.fileInfo.guid=t,this.files=B.replaceFileInFilesMap(this.files,e.fileInfo.guid,s)}}static replaceFileInFilesMap(e,t,s){const i=new Map;for(const o of e.values()){const e=o.fileInfo.guid===t?s:o;i.set(e.fileInfo.guid,e)}return i}attachEventsToFileItem(e){e.onLoadStart||(e.onLoadStart=()=>{this.fileUploadStarted(e)}),e.onProgress||(e.onProgress=()=>{this.fileProgress(e)}),e.onAbort||(e.onAbort=()=>{this.fileUploadAborted(e)}),e.onError||(e.onError=t=>{this.fileUploadError(e,t.status,t.statusText,t.responseText)}),e.onLoadEnd||(e.onLoadEnd=()=>{this.fileUploaded(e)})}fileUploadStarted(e){this.progressDispatcher.dispatch(M.Started,e)}fileUploaded(e){this.progressDispatcher.dispatch(M.Uploaded,e)}fileUploadAborted(e){this.progressDispatcher.dispatch(M.Aborted,e)}fileProgress(e){e.status!==S.WaitingStart&&e.status!==S.PendingUpload&&e.status!==S.Uploading||this.progressDispatcher.dispatch(M.Progress,e)}fileUploadError(e,t,s,i){this.progressDispatcher.dispatch(M.Error,e,{status:t,statusText:s,responseText:i})}processUploadedFilesOptions(e){this.updateFileStates(e),this.processUploadedFilesOptionsCore(e)}updateFileStates(e){var t;for(const s of e){const e=this.files.get(null!==(t=s.beforeReloadGuid)&&void 0!==t?t:s.fileGuid);e&&(e.status!==s.status&&this.changeStatus(e,s.status,s.fileGuid))}}createActionRequest(e){const t=new v,s=new Map;return e.forEach((e=>{const i=e.file.fileInfo.guid;let o=s.get(i);void 0===o&&(o=t.guids.length,t.guids.push(i),s.set(i,o)),t.actions.push(e.action,o),e.action===S.PendingUpload&&e.newReloadedFile&&t.reloadedFileGuids.push(e.newReloadedFile.fileInfo.guid)})),t}changeStatus(e,t,s){switch(t){case S.PendingUpload:this.reloadFile(e,s);break;case S.Paused:e.status=S.Paused;break;case S.Canceled:this.progressDispatcher.cancelSendingOfLastEvent(),e.abort(!1);break;case S.Removing:this.progressDispatcher.cancelSendingOfLastEvent(),e.abort(!1),this.files.delete(e.fileInfo.guid);break;default:throw new Error("Status not supported.")}}update(e){super.update(e);const t=e.has("showEmbeddedSelectButton"),s=e.has("externalSelectButtonCssSelector");this.dotnetHelper&&(t||s)&&this.detachSelectButtonClickHandler(e.get("externalSelectButtonCssSelector"))}updated(e){(e.has("acceptedFileTypes")||e.has("multiple"))&&this.prepareInputElement();const t=e.has("showEmbeddedSelectButton")&&this.showEmbeddedSelectButton,s=e.has("externalSelectButtonCssSelector")&&this.externalSelectButtonCssSelector;this.dotnetHelper&&(t||s)&&this.attachSelectButtonClickHandler()}disconnectedCallback(){super.disconnectedCallback(),this.detachSelectButtonClickHandler()}showFileSelectorDialog(){var e;null===(e=this.getFileInput())||void 0===e||e.click()}}function L(e,t){var s,i;null===(i=null===(s=P(e))||void 0===s?void 0:s.initDotNetReference)||void 0===i||i.call(s,t)}function q(e){var t;return(null===(t=P(e))||void 0===t?void 0:t.getRecentlyAddedFileInfosStream())||null}function P(e){return e.isConnected?e:null}e([r({attribute:m.acceptedFileTypes,type:Array})],B.prototype,"acceptedFileTypes",void 0),e([r({attribute:m.allowedFileExtensions,type:Array})],B.prototype,"allowedFileExtensions",void 0),e([r({attribute:m.externalSelectButtonCssSelector})],B.prototype,"externalSelectButtonCssSelector",void 0),e([r({attribute:m.showEmbeddedSelectButton,type:Boolean})],B.prototype,"showEmbeddedSelectButton",void 0),e([r({attribute:m.chunkSize,type:Number})],B.prototype,"chunkSize",void 0),e([r({attribute:m.maxFileSize,type:Number})],B.prototype,"maxFileSize",void 0),e([r({attribute:m.minFileSize,type:Number})],B.prototype,"minFileSize",void 0),e([r({attribute:m.maxFileCount,type:Number})],B.prototype,"maxFileCount",void 0),e([r({attribute:m.multiple,type:Boolean})],B.prototype,"multiple",void 0),e([r({attribute:m.uploadMode,type:Number})],B.prototype,"uploadMode",void 0),e([r({attribute:m.uploadTechnology,type:Number})],B.prototype,"uploadTechnology",void 0),e([r({attribute:m.dragOverClassName})],B.prototype,"dragOverClassName",void 0),e([r({attribute:m.dropZoneCssSelector})],B.prototype,"dropZoneCssSelector",void 0),e([r({attribute:m.customizeFormData,type:Boolean})],B.prototype,"customizeFormData",void 0);export{b as C,S as F,p as M,B as U,I as W,g as a,q as g,L as i,P as t};
