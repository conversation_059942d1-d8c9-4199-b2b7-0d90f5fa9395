const s="dxbl-ui-handlers-bridge";class t{constructor(s,t){this.id=s,this.result=t}processedBy(s){return this.id===s}}class e extends HTMLElement{constructor(){super(),this.pendingCommands=new Map,this._value=null}disposeComponent(){this.pendingCommands.clear()}get value(){return this._value}send(s,t,e=null){try{const n=this.nextCommandId();return this.pendingCommands.set(n,e),this._value=JSON.stringify([n.toString(),s,t]),this.dispatchEvent(new Event("change",{bubbles:!0})),n}finally{this._value=null}}static get observedAttributes(){return["command-results"]}attributeChangedCallback(s,t,e){if("command-results"===s)setTimeout((()=>this.processCommandResults(e)),0)}processCommandResults(s){const t=this.parseCommandResults(s);for(const s of t){const t=this.pendingCommands.get(s.id);this.pendingCommands.delete(s.id),t&&t(s)}}parseCommandResults(s){const e=new Array,n=JSON.parse(s);for(const s of n.CommandResults)e.push(new t(parseInt(s.Id),s.Result));return e.sort(((s,t)=>s.id===t.id?0:s.id>t.id?1:-1))}nextCommandId(){if(0===this.pendingCommands.size)return 0;let s=0;for(const t of this.pendingCommands.keys())t>s&&(s=t);return s+1}}customElements.define(s,e);const n={loadModule:function(){}};export{e as DxUIHandlersBridge,t as DxUIHandlersBridgeCommandResult,s as DxUIHandlersBridgeTagName,n as default};
