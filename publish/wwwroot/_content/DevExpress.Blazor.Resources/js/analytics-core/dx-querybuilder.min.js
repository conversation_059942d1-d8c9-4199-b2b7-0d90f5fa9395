﻿/**
* DevExpress Analytics (dist\js\dx-querybuilder.min.js)
* Version:  24.2.5
* Build date: Feb 17, 2025
* Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
* License: https://www.devexpress.com/Support/EULAs/universal.xml
*/
(()=>{"use strict";var e={n:t=>{var a=t&&t.__esModule?()=>t.default:()=>t;return e.d(a,{a}),a},d:(t,a)=>{for(var i in a)e.o(a,i)&&!e.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:a[i]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{ConnectingPointDragHandler:()=>M,ConnectingPointSurface:()=>w,ConnectingPointViewModel:()=>O,ConnectionPointDragHandler:()=>L,ConnectionPointSurface:()=>q,ConnectionPointViewModel:()=>J,ConnectorSurface:()=>Y,ConnectorViewModel:()=>X,DiagramElementBaseSurface:()=>N,DiagramElementBaseViewModel:()=>z,DiagramElementSurface:()=>Q,DiagramElementViewModel:()=>R,DiagramSurface:()=>H,DiagramViewModel:()=>V,GRID_SIZE:()=>E,PointSide:()=>D,RoutedConnectorSurface:()=>Z,RoutedConnectorViewModel:()=>K,connectingPointSerializationInfo:()=>B,connectionPointSerializationInfo:()=>G,createDiagramDesigner:()=>ae,determineConnectingPoints:()=>I,diagramControlsFactory:()=>k,diagramElementSerializationInfo:()=>$,diagramSerializationsInfo:()=>U,groups:()=>te,location:()=>v,margins:()=>W,name:()=>y,pageHeight:()=>j,pageWidth:()=>F,registerControls:()=>ee,size:()=>_,sizeLocation:()=>C,text:()=>S,unknownSerializationsInfo:()=>T});var a={};e.r(a),e.d(a,{ConnectionOptions:()=>We,CustomSqlQuery:()=>pt,DBColumn:()=>se,DBColumnType:()=>ie,DBForeignKey:()=>oe,DBSchema:()=>ue,DBSchemaProvider:()=>be,DBStoredProcedure:()=>le,DBStoredProcedureArgument:()=>ce,DBStoredProcedureArgumentDirection:()=>re,DBTable:()=>de,DataSourceParameter:()=>ct,FederatedQueriesContainer:()=>da,FederatedQueryExpression:()=>Wt,FederatedQueryExpressionType:()=>Vt,FederationDataSource:()=>fa,FederationMasterDetailRelation:()=>ma,FederationSource:()=>Ut,FederationTransformationRule:()=>na,JsonAuthenticationInfo:()=>Ee,JsonDataSource:()=>Le,JsonNode:()=>Te,JsonNodeType:()=>Ce,JsonParameter:()=>Ae,JsonParameterType:()=>Ie,JsonSchemaNode:()=>Pe,JsonSchemaProvider:()=>qe,JsonSchemaRootNode:()=>Ne,JsonSource:()=>ze,MasterDetailRelation:()=>mt,ObjectCtor:()=>Dt,ObjectDataMember:()=>wt,ObjectDataMemberBase:()=>Tt,ObjectDataSource:()=>Ct,ObjectDataSourceMethodBase:()=>Nt,ObjectParameter:()=>Pt,ObjectType:()=>Et,ResultSet:()=>ve,ResultTable:()=>Se,SelectQuery:()=>ia,SerializableDataFederationDataSource:()=>ga,SourceQuery:()=>Lt,SqlDataConnection:()=>gt,SqlDataSource:()=>vt,StoredProcQuery:()=>St,SubNode:()=>ta,TableQuery:()=>bt,TransformQuery:()=>oa,UnionQuery:()=>ua,UnionTypes:()=>ca,expressionSerializationInfo:()=>Qt,selectQuerySerializationsInfo:()=>aa,sourceQuerySerializationsInfo:()=>qt,subNodeSerializationInfo:()=>ea,transformQuerySerializationsInfo:()=>ra,transformationRuleSerializationsInfo:()=>sa,unionQuerySerializationsInfo:()=>la});var i={};e.r(i),e.d(i,{DBSchemaFederationDataSourceProvider:()=>xa,_resetGetJsonSchemaCallback:()=>Me,_setGetJsonSchemaCallback:()=>Re,deserializeToCollection:()=>ne,generateQueryUniqueName:()=>ba,getDBSchemaCallback:()=>pe,getDBStoredProceduresCallback:()=>he,getJsonSchemaCallback:()=>Be});var r={};e.r(r),e.d(r,{FederationQueryType:()=>Ue,JsonSourceType:()=>je,SqlQueryType:()=>Fe});var s={};e.r(s),e.d(s,{customQuerySerializationsInfo:()=>ut,dsParameterNameValidationRules:()=>Je,dsParameterSerializationInfo:()=>et,federationDataSourceSerializationInfo:()=>Ft,masterDetailRelationSerializationsInfo:()=>ht,parameterValueSerializationsInfo:()=>Ge,sourceSerializationInfo:()=>jt,storedProcParameterSerializationsInfo:()=>at,storedProcQuerySerializationsInfo:()=>yt,tableQuerySerializationsInfo:()=>ft,validateName:()=>He});var o={};e.r(o),e.d(o,{AutoQueryPreload:()=>Na,BaseWizard:()=>Nr,ChooseAvailableDataSourcePage:()=>Ni,ChooseAvailableItemPage:()=>Pi,ChooseDataSourceTypePage:()=>ka,ChooseJsonConnectionPage:()=>Di,ChooseJsonSchemaPage:()=>Ki,ChooseJsonSourcePage:()=>Bi,ChooseObjectDataSourceDataMembersPage:()=>ui,ChooseObjectDataSourceTypesPage:()=>di,ChooseSqlConnectionPage:()=>Ha,ConfigureMasterDetailRelationshipsPage:()=>jr,ConfigureObjectDataSourceParametersPage:()=>ti,ConfigureQueryPage:()=>yr,ConfigureQueryParametersPage:()=>_r,DataSourceType:()=>Ia,DataSourceWizard:()=>Js,DataSourceWizardPageId:()=>ya,DataSourceWizardPageIterator:()=>Hs,DataSourceWizardSettings:()=>Us,FederatedMasterDetailRelationshipsPage:()=>Qs,FederatedQueryConfigurePage:()=>qs,FederationDataSourceWizardPageId:()=>_a,FullscreenDataSourceWizard:()=>vo,FullscreenDataSourceWizardPageId:()=>Ta,FullscreenDataSourceWizardPageIterator:()=>Co,FullscreenDataSourceWizardSectionId:()=>Pa,FullscreenWizard:()=>_o,FullscreenWizardPage:()=>uo,FullscreenWizardPageFactory:()=>ro,JsonDataSourceWizardPageId:()=>va,MasterDetailRelationshipsPageBase:()=>Fr,MultiQueryConfigurePage:()=>Er,MultiQueryConfigureParametersPage:()=>Rr,MultiQueryDataSourceWizard:()=>eo,MultiQueryDataSourceWizardPageIterator:()=>to,ObjectDataSourceWizardPageId:()=>Ca,PageFactory:()=>Ws,PageIterator:()=>Pr,PopupWizard:()=>wr,SelectDataSourcePage:()=>po,SpecifyFederationDataSourceSettingsPage:()=>bo,SpecifyJsonConnectionPage:()=>Mi,SpecifyJsonDataSourceSettingsPage:()=>mo,SpecifyObjectDataSourceSettingsPage:()=>Ys,SpecifySqlDataSourceSettingsPage:()=>xo,SqlDataSourceWizardPageId:()=>Sa,StateManager:()=>Tr,TypeItem:()=>Aa,WizardNavigationPanel:()=>So,WizardPageBase:()=>Ea,_DataSourceWizardOptions:()=>js,_DataSourceWizardOptionsBase:()=>Fs,_MultiQueryDataSourceWizardOptions:()=>Zs,_SqlDataSourceWrapper:()=>$a,_WrappedWizardPage:()=>Ua,__loadingStateFunctionName:()=>wa,__nextActionFunctionName:()=>Da,_canEditQueryParameters:()=>Br,_createDataSourceFullscreenWizard:()=>To,_createDataSourceWizard:()=>Xs,_createDefaultDataSourceWizardState:()=>ja,_createMultiQueryDataSourceWizard:()=>io,_registerChooseAvailableDataSourcePage:()=>wi,_registerChooseDataSourceTypePage:()=>za,_registerChooseJsonConnectionPage:()=>Ei,_registerChooseJsonSchemaPage:()=>Zi,_registerChooseJsonSourcePage:()=>Ri,_registerChooseObjectDataSourceDataMembersPage:()=>pi,_registerChooseObjectDataSourceTypesPage:()=>li,_registerChooseSqlConnectionPage:()=>Ja,_registerConfigureMasterDetailRelationshipsPage:()=>Ur,_registerConfigureObjectDataSourceParametersPage:()=>ai,_registerConfigureParametersPage:()=>vr,_registerConfigureQueryPage:()=>Sr,_registerDataSourceWizardPages:()=>Gs,_registerFederatedMasterDetailRelationshipsPage:()=>Vs,_registerFederatedQueryConfigurePage:()=>Ls,_registerMultiQueryConfigurePage:()=>Ir,_registerMultiQueryConfigureParametersPage:()=>Mr,_registerMultiQueryDataSourcePages:()=>ao,_registerSelectDataSourcePage:()=>ho,_registerSpecifyFederationDataSourceSettingsPage:()=>yo,_registerSpecifyJsonConnectionPage:()=>qi,_registerSpecifyJsonDataSourceSettingsPage:()=>go,_registerSpecifyObjectDataSourceSettingsPage:()=>Ks,_registerSpecifySqlDataSourceSettingsPage:()=>fo,_resetRestoreJsonDataSourceFromState:()=>Wa,_resetRestoreSqlDataSourceFromState:()=>Ma,_restoreFederationDataSourceFromState:()=>qa,_restoreJsonDataSourceFromState:()=>Qa,_restoreObjectDataSourceFromState:()=>Fa,_restoreSqlDataSourceFromState:()=>Ba,_setRestoreJsonDataSourceFromState:()=>Va,_setRestoreSqlDataSourceFromState:()=>Ra,parameterTypeToPropertyMap:()=>Ii});var n={};e.r(n),e.d(n,{ChooseObjectDataMembers:()=>ci,ChooseObjectMemberParameters:()=>Za,ChooseObjectParameters:()=>ei,ChooseObjectTypes:()=>ni,ChooseObjectTypesTreelistController:()=>oi,CustomQueryTreeListItem:()=>xi,DBSchemaItemsProvider:()=>Cr,DBSchemaTreeListController:()=>Dr,DataMemberTreeNode:()=>Ji,FederatedQueriesTreeNode:()=>Bs,FederationDataSourceItemsExtender:()=>Os,FederationQueryBuilderPopupBase:()=>Ds,FederationSelectQueryBuilderPopup:()=>Is,FederationTablesExpressionFieldListProvider:()=>Es,FederationTransformQueryBuilderPopup:()=>ks,FederationTreeNodeProvider:()=>Rs,FederationUnionQueryBuilderPopup:()=>zs,FieldTreeNode:()=>Hi,JsonDataSourceJsonSourcePageSettingsBase:()=>zi,JsonDataSourceJsonSourcePageStringSettings:()=>$i,JsonDataSourceJsonSourcePageUriSettings:()=>Oi,JsonStringEditor:()=>ki,JsonTreeNodeItemsProvider:()=>Yi,MultiQueryTreeListItemFactory:()=>fi,ObjectDataSourceParameterProperty:()=>Ga,ObjectDataSourceParametersModel:()=>Ka,ObjectSchemaProvider:()=>si,ObjectTypeDescriptions:()=>ii,ParameterTreeNode:()=>Fi,ParametersTreeListController:()=>Or,ParametersTreeListItem:()=>kr,ParametersTreeListItemBase:()=>Ar,ParametersTreeListRootItem:()=>$r,ParametersTreeListRootItemBase:()=>zr,QueriesTreeNode:()=>ji,QueryBuilderPopup:()=>cr,QueryBuilderPopupBase:()=>lr,SelectQuerySqlTextProvider:()=>ur,SelectStatementQueryControl:()=>fr,SingleCheckedDataMemberTreeNode:()=>Gi,StoredProceduresQueryControl:()=>br,TransformResultSchemaProvider:()=>As,TreeLeafNode:()=>Vi,TreeNode:()=>Wi,TreeNodeBase:()=>Qi,TreeNodeItemsProvider:()=>Xi,TreeQueryNode:()=>Ui,WizardAction:()=>Po,WizardPageProcessor:()=>co,WizardPageSection:()=>no,WizardPageSectionFactory:()=>so,WizardPageSectionIterator:()=>lo,WizardSectionPosition:()=>mi,WrappedWizardPageSection:()=>oo,_createBeforeInitializePageEventArgs:()=>vi,_createPageEventArgs:()=>Ci,_isMoreThanOneDataSourceTypeAvailable:()=>Ti,defaultObjectDataSourceItemSpecifics:()=>Li,getLocalizedValidationErrorMessage:()=>Ai,getObjectTypeDescriptionsCallback:()=>ri,getSectionStyle:()=>bi,subscribeArray:()=>yi,subscribeObject:()=>_i,subscribeProperties:()=>Si});var d={};e.r(d),e.d(d,{expressionFunctions:()=>rt});var l={};e.r(l),e.d(l,{FederatedQueriesHelper:()=>$s,GroupFilterEditorSerializer:()=>Kr,KeyColumnSurface:()=>Lr,ManageFederatedQueriesEditor:()=>wo,MasterDetailEditor:()=>Wr,MasterDetailEditorPopupManager:()=>qr,MasterDetailRelationSurface:()=>Qr,MasterQuerySurface:()=>Vr,OperandParameterQBSurface:()=>ts,OperandPropertyQBSurface:()=>as,QBFilterEditorHelper:()=>is,QBFilterEditorHelperDefault:()=>rs,QBFilterStringOptions:()=>os,QueryBuilderObjectsProvider:()=>Yr,RightPanelSwitcher:()=>Do,UndoEditor:()=>No,_setQBFilterEditorHelperDefault:()=>ss,createDefaultSQLAceOptions:()=>mr,createDefaultSQLAdditionalOptions:()=>gr,createDefaultSQLLanguageHelper:()=>xr,editorTemplates:()=>It,expressionFunctions:()=>rt,isAggregatedExpression:()=>Xr});var c={};e.r(c),e.d(c,{alias:()=>kt,location:()=>Bt,name:()=>At,selected:()=>$t,size:()=>Ot,sizeLocation:()=>Rt,text:()=>zt,unknownSerializationsInfo:()=>Mt});var u={};e.r(u),e.d(u,{ActionId:()=>me,FederationQueryType:()=>Ue,HandlerUri:()=>ge,JsonSourceType:()=>je,RequestWrapper:()=>fe,SqlQueryType:()=>Fe,controlsFactory:()=>Gt});var p={};e.r(p),e.d(p,{AccordionTabInfo:()=>Vo,ColumnDragHandler:()=>Eo,ColumnExpressionCollectionHelper:()=>dr,DbObjectDragDropHandler:()=>Io,FederatedTransformQueryBuilderTreeListController:()=>ws,FederatedUnionQueryBuilderTreeListController:()=>Ns,FederationAllColumnsViewModel:()=>Ss,FederationColumnViewModel:()=>ys,FederationQuerySurface:()=>Cs,FederationQueryViewModel:()=>vs,FederationTableSurface:()=>Ps,FederationTableViewModel:()=>Ts,QueryBuilderTreeListController:()=>Ro,SelectedTabInfo:()=>Wo,createIsLoadingFlag:()=>Jo,createQueryBuilder:()=>Ko,createQueryBuilderSurface:()=>Zo,federationQuerySerializationsInfo:()=>_s,registerControls:()=>Bo,serializeDataConnection:()=>xe,updateQueryBuilderSurfaceContentSize:()=>Ho,wrapGetFederationdResultSchema:()=>ar,wrapGetSelectStatement:()=>er,wrapRebuildResultSchema:()=>tr});var h={};e.r(h),e.d(h,{AllColumnsSurface:()=>fs,AllColumnsViewModel:()=>Jr,ColumnExpression:()=>nr,ColumnSurface:()=>xs,ColumnViewModel:()=>ds,JoinConditionSurface:()=>Oo,JoinConditionViewModel:()=>Yt,ParameterViewModel:()=>es,QueryElementBaseSurface:()=>gs,QueryElementBaseViewModel:()=>Xt,QuerySurface:()=>hs,QueryViewModel:()=>ps,QueryViewModelBase:()=>us,RelationSurface:()=>Ao,RelationViewModel:()=>Zt,TableSurface:()=>bs,TableViewModel:()=>cs});var m={};e.r(m),e.d(m,{AggregationType:()=>ir,ColumnType:()=>sr,ConditionType:()=>zo,ParametersMode:()=>Gr,allColumnsSerializationInfo:()=>Hr,columnExpressionSerializationsInfo:()=>or,columnSerializationInfo:()=>rr,joinConditionSerializationInfo:()=>$o,querySerializationsInfo:()=>ns,relationSerializationInfo:()=>Kt,tableSerializationInfo:()=>ls});var g={};e.r(g),e.d(g,{DxQueryBuilder:()=>nn,EventGenerator:()=>rn,JSQueryBuilder:()=>en,JSQueryBuilderBinding:()=>sn});const x=DevExpress.Analytics.Widgets,f=DevExpress.Analytics.Internal,b=DevExpress.Analytics.Elements,y={propertyName:"name",modelName:"@Name",displayName:"Name",editor:x.editorTemplates.getEditor("text"),validationRules:f.nameValidationRules},S={propertyName:"text",modelName:"@Text",displayName:"Text",editor:x.editorTemplates.getEditor("text")},_={propertyName:"size",modelName:"@Size",defaultVal:"100,50",from:b.Size.fromString,displayName:"Size",editor:x.editorTemplates.getEditor("objecteditor")},v={propertyName:"location",modelName:"@Location",from:b.Point.fromString,displayName:"Location",editor:x.editorTemplates.getEditor("objecteditor")},C=[_,v],T=[y].concat(C),P=ko;class N extends b.SurfaceElementBase{constructor(e,t,a){super(e,t,(0,f.extend)({},N._unitProperties,a)),this.template="dx-diagram-element",this.selectiontemplate="dx-diagram-element-selection",this.contenttemplate="dx-diagram-element-content",this.margin=P.observable(0),this._disposables.push(this.positionWidthWithoutMargins=P.pureComputed((()=>this.position.width()-2*this.margin()))),this._disposables.push(this.positionLineHeightWithoutMargins=P.pureComputed((()=>this.position.lineHeight()-2*this.margin())))}}N._unitProperties={_height:e=>e.size.height,_width:e=>e.size.width,_x:e=>e.location.x,_y:e=>e.location.y};class w extends N{constructor(e,t){super(e,t,w._unitProperties),this.template="dxdd-connecting-point",this.selectiontemplate="dxdd-connection-point-selection",this.contenttemplate=""}}var D;w._unitProperties={_x:e=>P.pureComputed((()=>e.location.x()-e.parentModel().location.x())),_y:e=>P.pureComputed((()=>e.location.y()-e.parentModel().location.y()))},function(e){e[e.East=0]="East",e[e.South=1]="South",e[e.North=2]="North",e[e.West=3]="West"}(D||(D={}));const E=10;function I(e,t){const a={start:null,end:null};if(t.leftConnectionPoint.location.x()>e.rightConnectionPoint.location.x()+2*E)a.start=e.rightConnectionPoint,a.end=t.leftConnectionPoint;else if(e.leftConnectionPoint.location.x()>t.rightConnectionPoint.location.x()+2*E)a.start=e.leftConnectionPoint,a.end=t.rightConnectionPoint;else{(e.rightConnectionPoint.location.x()+e.rightConnectionPoint.location.x())/2>(t.rightConnectionPoint.location.x()+t.rightConnectionPoint.location.x())/2?(a.start=e.rightConnectionPoint,a.end=t.rightConnectionPoint):(a.start=e.leftConnectionPoint,a.end=t.leftConnectionPoint)}return a}const A=DevExpress.Analytics.Utils,k=new A.ControlsFactory;class z extends b.ElementViewModel{getControlFactory(){return k}constructor(e,t,a){super(e,t,a)}}const $=[_,v,y,S,{propertyName:"type",modelName:"@Type"}];class O extends z{constructor(e,t,a){super((0,f.extend)({"@ControlType":"ConnectingPoint"},e),t,a),this.side=P.pureComputed((()=>this.percentOffsetY()>=this.percentOffsetX()?this.percentOffsetY()>1-this.percentOffsetX()?D.South:D.West:this.percentOffsetY()>1-this.percentOffsetX()?D.East:D.North)),this.size=new b.Size(7,7),this.location=new b.Point(0,0),this.location.x=P.pureComputed((()=>{const e=this.parentModel();return e.location.x()+e.size.width()*this.percentOffsetX()})),this.location.y=P.pureComputed((()=>{const e=this.parentModel();return e.location.y()+e.size.height()*this.percentOffsetY()}))}}const B=[{propertyName:"percentOffsetX",modelName:"@PercentOffsetX",defaultVal:.5,from:A.floatFromModel},{propertyName:"percentOffsetY",modelName:"@PercentOffsetY",defaultVal:.5,from:A.floatFromModel}];class R extends z{constructor(e,t,a){super((0,f.extend)({"@ControlType":"DiagramElement"},e),t,a),this.connectingPoints=(0,A.deserializeArray)(e&&e.ConnectingPoints||[],(e=>new O(e,this,a))),void 0===this.text()&&this.text(this.name())}}class M extends f.DragDropHandler{constructor(e,t,a,i,r){super(e,t,a,i,r),this.startConnectingPoint=null,this.newConnector=null,this.cursor="arrow",this.containment=".dxrd-ghost-container",this.helper=e=>{r.update(e)}}startDrag(e){if(!(e instanceof w))throw new Error("ConnectingPointDragHandler can be applied to the ConnectingPoint only.");this.startConnectingPoint=e;const t=this.startConnectingPoint.parent.getControlModel();this.newConnector=t.parentModel().createChild({"@ControlType":"RoutedConnector"}),this.newConnector.startPoint().connectingPoint(this.startConnectingPoint.getControlModel())}drag(e,t){t.dataset.leftPosition=(0,f.convertToCssPixelUnits)((0,f.convertFromCssPixelUnits)(t.dataset.leftPosition)+t.scroll.left),t.dataset.topPosition=(0,f.convertToCssPixelUnits)((0,f.convertFromCssPixelUnits)(t.dataset.topPosition)+t.scroll.top);const a=this._getAbsoluteSurfacePosition(t);this.newConnectorSurface.endPoint().rect({top:a.top,left:a.left})}doStopDrag(){if(this.dragHelperContent.reset(),this.selection.dropTarget){const e=this.selection.dropTarget.getControlModel();if(e instanceof O)this.newConnector.endPoint().connectingPoint(e);else if(e instanceof R){const t=e.connectingPoints();this.newConnector.endPoint().connectingPoint(t[0])}this.selection.initialize(this.newConnectorSurface)}}get newConnectorSurface(){return this.newConnector&&(0,f.findSurface)(this.newConnector)}}class q extends b.SurfaceElementBase{constructor(e,t){super(e,t,q._unitProperties),this.template="dx-diagram-connection-point",this.selectiontemplate="dx-diagram-connection-point",this.relativeX=P.pureComputed((()=>this.rect().left-this.parent.rect().left)),this.relativeY=P.pureComputed((()=>this.rect().top-this.parent.rect().top))}container(){return this.getRoot()}}q._unitProperties={_x:e=>e.location.x,_y:e=>e.location.y};class L extends f.DragDropHandler{constructor(e,t,a,i,r){super(e,t,a,i,r),this.currentConnectionPoint=null,this.cursor="arrow",this.containment=".dxrd-ghost-container",this.helper=e=>{r.update(e)}}startDrag(e){if(!(e instanceof q))throw new Error("ConnectionPointDragHandler can be applied to the ConnectionPoint only.");this.currentConnectionPoint=e}drag(e,t){t.dataset.leftPosition=(0,f.convertToCssPixelUnits)((0,f.convertFromCssPixelUnits)(t.dataset.leftPosition)+t.scroll.left),t.dataset.topPosition=(0,f.convertToCssPixelUnits)((0,f.convertFromCssPixelUnits)(t.dataset.topPosition)+t.scroll.top);const a=this._getAbsoluteSurfacePosition(t);this.currentConnectionPoint.rect({top:a.top,left:a.left})}doStopDrag(){if(this.dragHelperContent.reset(),this.selection.dropTarget){const e=this.selection.dropTarget.getControlModel();if(e instanceof O){const t=this.currentConnectionPoint.parent.getControlModel();this.currentConnectionPoint.getControlModel()===t.startPoint()?t.startPoint().connectingPoint(e):t.endPoint().connectingPoint(e)}else if(e instanceof R){const t=this.currentConnectionPoint.parent.getControlModel(),a=e.connectingPoints();this.currentConnectionPoint.getControlModel()===t.startPoint()?t.startPoint().connectingPoint(a[0]):t.endPoint().connectingPoint(a[0])}}}}class Q extends N{constructor(e,t){super(e,t,null),this.contenttemplate="dxdd-element-content-with-connecting-points"}_getChildrenHolderName(){return"connectingPoints"}}class V extends z{getInfo(){return U}constructor(e){super(e,null,new A.ModelSerializer),this.controlType="Diagram",this.controls=P.observableArray(),this.name("Diagram")}}const W={propertyName:"margins",modelName:"@Margins",from:b.Margins.fromString,displayName:"Margins"},F={propertyName:"pageWidth",modelName:"@PageWidth",defaultVal:850,from:A.floatFromModel,displayName:"Page Width",editor:x.editorTemplates.getEditor("numeric")},j={propertyName:"pageHeight",modelName:"@PageHeight",defaultVal:1250,from:A.floatFromModel,displayName:"Page Height",editor:x.editorTemplates.getEditor("numeric")},U=[y,F,j,W];class H extends b.SurfaceElementBase{constructor(e,t=P.observable(1)){super(e,{measureUnit:P.observable("Pixels"),zoom:t,dpi:P.observable(100)},H._unitProperties),this.measureUnit=P.observable("Pixels"),this.dpi=P.observable(100),this.controls=P.observableArray(),this.allowMultiselect=!1,this.focused=P.observable(!1),this.selected=P.observable(!1),this.underCursor=P.observable(new f.HoverInfo),this.templateName="dx-diagram-surface",this.margins={bottom:this._bottom,left:this._left,right:this._right,top:this._top},this.zoom=t,this._context=this,(0,f.createObservableArrayMapCollection)(e.controls,this.controls,this._createSurface)}checkParent(e){return!1}get parent(){return this._parent}set parent(e){this._parent=e}getChildrenCollection(){return P.observableArray([])}}H._unitProperties={_width:e=>e.pageWidth,_height:e=>e.pageWidth,pageWidth:e=>e.pageWidth,pageHeight:e=>e.pageHeight,_bottom:e=>e.margins.bottom,_left:e=>e.margins.left,_right:e=>e.margins.right,_top:e=>e.margins.top};class J extends z{constructor(e,t,a){super((0,f.extend)(e,{"@ControlType":"ConnectionPoint"}),t,a);const i=this.location.x,r=this.location.y;this.location.x=P.pureComputed({read:()=>this.connectingPoint()&&this.connectingPoint().location.x()||i(),write:e=>{this.connectingPoint(null),i(e)}}),this.location.y=P.pureComputed({read:()=>this.connectingPoint()&&this.connectingPoint().location.y()||r(),write:e=>{this.connectingPoint(null),r(e)}})}}const G=[v,{propertyName:"connectingPoint",modelName:"@ConnectingPoint",link:!0}];class X extends z{getX(){return this.startPoint().location.x()<this.endPoint().location.x()?this.startPoint().location.x():this.endPoint().location.x()}getY(){return this.startPoint().location.y()<this.endPoint().location.y()?this.startPoint().location.y():this.endPoint().location.y()}getWidth(){return Math.abs(this.startPoint().location.x()-this.endPoint().location.x())||X.MIN_LINE_THICKNESS}getHeight(){return Math.abs(this.startPoint().location.y()-this.endPoint().location.y())||X.MIN_LINE_THICKNESS}constructor(e,t,a){super((0,f.extend)({"@ControlType":"Connector"},e),t,a),this.startPoint(this.startPoint()||new J({"@Location":"0, 0"},this,a)),this.endPoint(this.endPoint()||new J({"@Location":"150, 75"},this,a)),this.location=new b.Point(0,0),this._disposables.push(this.location.x=P.pureComputed({read:()=>this.getX(),write:e=>{const t=e-(this.startPoint().location.x()<this.endPoint().location.x()?this.startPoint().location.x():this.endPoint().location.x());this.startPoint().location.x(this.startPoint().location.x()+t),this.endPoint().location.x(this.endPoint().location.x()+t)}})),this._disposables.push(this.location.y=P.pureComputed({read:()=>this.getY(),write:e=>{const t=e-(this.startPoint().location.y()<this.endPoint().location.y()?this.startPoint().location.y():this.endPoint().location.y());this.startPoint().location.y(this.startPoint().location.y()+t),this.endPoint().location.y(this.endPoint().location.y()+t)}})),this.size=new b.Size(0,0),this._disposables.push(this.size.width=P.pureComputed({read:()=>this.getWidth(),write:e=>{this.startPoint().location.x()<this.endPoint().location.x()?this.endPoint().location.x(this.startPoint().location.x()+e):this.startPoint().location.x(this.endPoint().location.x()+e)}})),this._disposables.push(this.size.height=P.pureComputed({read:()=>this.getHeight(),write:e=>{this.startPoint().location.y()<this.endPoint().location.y()?this.endPoint().location.y(this.startPoint().location.y()+e):this.startPoint().location.y(this.endPoint().location.y()+e)}}))}}X.MIN_LINE_THICKNESS=3;class Y extends N{constructor(e,t){super(e,t,null),this.template="dxdd-connector",this.selectiontemplate="dxdd-connector-selection",this.startPoint=P.pureComputed((()=>new q(e.startPoint(),t))),this.endPoint=P.pureComputed((()=>new q(e.endPoint(),t)))}}class K extends X{getX(){let e=super.getX();return this.routePoints&&this.routePoints().forEach((t=>{t.x()<e&&(e=t.x())})),e}getY(){let e=super.getY();return this.routePoints&&this.routePoints().forEach((t=>{t.y()<e&&(e=t.y())})),e}getWidth(){let e=super.getWidth();const t=this.getX();return this.routePoints&&[this.startPoint().location,this.endPoint().location].concat(this.routePoints()).forEach((a=>{a.x()-t>e&&(e=a.x()-t)})),e}getHeight(){let e=super.getHeight();const t=this.getY();return this.routePoints&&[this.startPoint().location,this.endPoint().location].concat(this.routePoints()).forEach((a=>{a.y()-t>e&&(e=a.y()-t)})),Math.round(e)}_fixPoint(e,t){switch(t){case D.North:e.y(e.y()-E);break;case D.East:e.x(e.x()+E);break;case D.West:e.x(e.x()-E);break;case D.South:e.y(e.y()+E)}}_getStartPointSide(){return this.startPoint().connectingPoint()?this.startPoint().connectingPoint().side():this.startPoint().location.y()!==this.endPoint().location.y()?this.startPoint().location.y()>this.endPoint().location.y()?D.North:D.South:this.startPoint().location.x()>this.endPoint().location.x()?D.West:D.East}_getEndPointSide(){return this.endPoint().connectingPoint()?this.endPoint().connectingPoint().side():this.startPoint().location.y()!==this.endPoint().location.y()?this.startPoint().location.y()>this.endPoint().location.y()?D.South:D.North:this.startPoint().location.x()>this.endPoint().location.x()?D.East:D.West}_getRatio(e){const t=this._getPower(e),a=Math.pow(2,t),i=Math.pow(2,t-1),r=[];for(let e=1;e<i;e++)e%2!=0&&(r.push(a-e),r.push(e));r.reverse();return(r[e-i]||1)/a}constructor(e,t,a){super((0,f.extend)({"@ControlType":"RoutedConnector"},e),t,a),this._isUpdating=!1,this._getPower=function(e){for(let t=0;t<10;t++)if(e>=Math.pow(2,t)&&e<Math.pow(2,t+1))return t+1;return 1},this.seriesNumber=P.observable(1),this.routePoints=P.observable([]),this.freezeRoute=P.observable(!1),this._disposables.push(P.computed((()=>{const e=!(1+this.startPoint().location.x()+this.startPoint().location.y()+this.endPoint().location.x()+this.endPoint().location.y());this._isUpdating||this.freezeRoute(e)}))),this._disposables.push(P.computed((()=>{if(!this.freezeRoute()){const e=[],t=this._getStartPointSide(),a=this._getEndPointSide(),i=new b.Point(this.startPoint().location.x(),this.startPoint().location.y()),r=new b.Point(this.endPoint().location.x(),this.endPoint().location.y());this.startPoint().connectingPoint()&&(this._fixPoint(i,this.startPoint().connectingPoint().side()),e.push(i)),this.endPoint().connectingPoint()&&this._fixPoint(r,this.endPoint().connectingPoint().side());let s=Math.min(i.x(),r.x()),o=Math.abs(i.x()-r.x());const n=Math.min(i.y(),r.y()),d=Math.abs(i.y()-r.y()),l=this.seriesNumber(),c=this._getRatio(l),u=(l-1)*E;i.y()-r.y()>0?i.x()-r.x()>0?t===D.North||t===D.East?a===D.North||a===D.East?(1!==l&&(o+=u,e.push(new b.Point(s+o,n+d))),e.push(new b.Point(s+o,n))):(e.push(new b.Point(s+o,n+d*c)),e.push(new b.Point(s,n+d*c))):a===D.South||a===D.West?e.push(new b.Point(s,n+d)):(e.push(new b.Point(s+o*c,n+d)),e.push(new b.Point(s+o*c,n))):t===D.North||t===D.West?a===D.North||a===D.West?(1!==l&&(s-=u,e.push(new b.Point(s,n+d))),e.push(new b.Point(s,n))):(e.push(new b.Point(s,n+d*c)),e.push(new b.Point(s+o,n+d*c))):a===D.South||a===D.East?e.push(new b.Point(s+o,n+d)):(e.push(new b.Point(s+o*c,n+d)),e.push(new b.Point(s+o*c,n))):i.x()-r.x()>0?t===D.South||t===D.East?a===D.South||a===D.East?(1!==l&&(o+=u,e.push(new b.Point(s+o,n))),e.push(new b.Point(s+o,n+d))):(e.push(new b.Point(s+o,n+d*c)),e.push(new b.Point(s,n+d*c))):a===D.North||a===D.West?e.push(new b.Point(s,n)):(e.push(new b.Point(s+o*c,n)),e.push(new b.Point(s+o*c,n+d))):t===D.South||t===D.West?a===D.South||a===D.West?(1!==l&&(s-=u,e.push(new b.Point(s,n))),e.push(new b.Point(s,n+d))):(e.push(new b.Point(s,n+d*c)),e.push(new b.Point(s+o,n+d*c))):a===D.North||a===D.East?e.push(new b.Point(s+o,n)):(e.push(new b.Point(s+o*c,n)),e.push(new b.Point(s+o*c,n+d))),this.endPoint().connectingPoint()&&e.push(r),this.routePoints(e)}})))}beginUpdate(){this._isUpdating=!0}endUpdate(){this._isUpdating=!1}}class Z extends N{_createRoutePoint(e,t){return{x:P.pureComputed((()=>this._context.rtl()?Math.round(this.getControlModel().size.width()-(e.x()-t.x())):Math.round(e.x()-t.x()))),y:P.pureComputed((()=>Math.round(e.y()-t.y()))),modelPoint:e}}_createRouteLineWrapper(e,t,a=!1){const i=this,r=Math.abs(e.x.peek()-t.x.peek())<1,s=e.modelPoint.y.peek(),o=e.modelPoint.x.peek();return{position:{top:Math.min(e.y.peek(),t.y.peek())-2,left:Math.min(e.x.peek(),t.x.peek())-2,width:Math.abs(e.x.peek()-t.x.peek())+6,height:Math.abs(e.y.peek()-t.y.peek())+6},isVerticalLine:r,resizeHandler:a=>{i._control.freezeRoute(!0);try{if(i._control.beginUpdate(),r){let i=o+a.delta.dx;this._context.rtl()&&(i=o-a.delta.dx),e.modelPoint.x(i),t.modelPoint.x(i)}else{const i=s+a.delta.dy;e.modelPoint.y(i),t.modelPoint.y(i)}}finally{i._control.endUpdate()}},resizeStopped:()=>{i._control.routePoints.notifySubscribers(i._control.routePoints())},isLocked:P.observable(a)}}_updateRoutePoints(){const e=[],t=this.getControlModel(),a=t.location;e.push(this._createRoutePoint(t.startPoint().location,a)),t.routePoints().forEach((t=>{e.push(this._createRoutePoint(t,a))})),e.push(this._createRoutePoint(t.endPoint().location,a)),this.routePoints(e)}constructor(e,t){super(e,t,null),this.template="dxdd-routed-connector",this.selectiontemplate="dxdd-routed-connector-selection",this.showArrow=P.observable(!1),this.showRightArrow=P.observable(!1),this.isVisible=P.observable(!0),this.routePoints=P.observableArray(),this.routePointsSet=P.pureComputed((()=>{const e=[];return this.routePoints().forEach((t=>{e.push(t.x()+" "+t.y())})),e.join(", ")})),this.routeLineWrappers=P.pureComputed((()=>{const e=[];for(let t=1;t<this.routePoints().length;t++)e.push(this._createRouteLineWrapper(this.routePoints()[t-1],this.routePoints()[t],1===t||t===this.routePoints().length-1));return e})),this.connectorID=()=>this._connectorID,this._disposables.push(e),this._connectorID=Z._connectorsCount++,this.startPoint=P.pureComputed((()=>new q(e.startPoint(),t))),this.endPoint=P.pureComputed((()=>new q(e.endPoint(),t))),this._disposables.push(e.routePoints.subscribe((e=>{this._updateRoutePoints()}))),this._updateRoutePoints()}}function ee(){k.registerControl("Unknown",{info:T,type:b.ElementViewModel,nonToolboxItem:!0,surfaceType:b.SurfaceElementBase}),k.registerControl("Connector",{info:[y,{propertyName:"location",displayName:"Location",editor:x.editorTemplates.getEditor("objecteditor")},{propertyName:"startPoint",modelName:"@StartPoint",link:!0},{propertyName:"endPoint",modelName:"@EndPoint",link:!0}],surfaceType:Y,type:X,elementActionsTypes:[],nonToolboxItem:!1}),k.registerControl("RoutedConnector",{info:[y,{propertyName:"location",displayName:"Location",editor:x.editorTemplates.getEditor("objecteditor")},{propertyName:"startPoint",modelName:"@StartPoint",link:!0},{propertyName:"endPoint",modelName:"@EndPoint",link:!0}],surfaceType:Z,type:K,elementActionsTypes:[],nonToolboxItem:!1}),k.registerControl("ConnectionPoint",{info:G,surfaceType:q,type:J,elementActionsTypes:[],nonToolboxItem:!0}),k.registerControl("Diagram",{info:U,surfaceType:H,popularProperties:["name"],type:V,elementActionsTypes:[],isContainer:!0,nonToolboxItem:!0}),k.registerControl("DiagramElement",{info:$,defaultVal:{"@SizeF":"150,50",ConnectingPoints:{Item1:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"1","@PercentOffsetY":"0.5"},Item2:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"0.5","@PercentOffsetY":"1"},Item3:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"0.5","@PercentOffsetY":"0"},Item4:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"0","@PercentOffsetY":"0.5"}}},surfaceType:Q,popularProperties:["text"],type:R,elementActionsTypes:[],nonToolboxItem:!1}),k.registerControl("Ellipse",{info:$,defaultVal:{"@SizeF":"150,50","@Type":"Ellipse",ConnectingPoints:{Item1:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"1","@PercentOffsetY":"0.5"},Item2:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"0.5","@PercentOffsetY":"1"},Item3:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"0.5","@PercentOffsetY":"0"},Item4:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"0","@PercentOffsetY":"0.5"}}},surfaceType:Q,popularProperties:["text"],type:R,elementActionsTypes:[],nonToolboxItem:!1}),k.registerControl("Condition",{info:$,defaultVal:{"@SizeF":"150,50","@Type":"Condition",ConnectingPoints:{Item1:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"1","@PercentOffsetY":"0.5"},Item2:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"0.5","@PercentOffsetY":"1"},Item3:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"0.5","@PercentOffsetY":"0"},Item4:{"@ControlType":"ConnectingPoint","@PercentOffsetX":"0","@PercentOffsetY":"0.5"}}},surfaceType:Q,popularProperties:["text"],type:R,elementActionsTypes:[],nonToolboxItem:!1}),k.registerControl("ConnectingPoint",{info:B,surfaceType:w,type:O,elementActionsTypes:[],nonToolboxItem:!0})}Z._connectorsCount=0;const te={Appearance:{info:[]},Behavior:{info:[]},Design:{info:[y]},Layout:{info:[v,_,F,j]}};function ae(e,t,a,i){a&&(0,A.addCultureInfo)({messages:a}),ee();const r=P.pureComputed((()=>new V(t()))),s=P.pureComputed((()=>new H(r()))),o=(0,f.createDesigner)(r,s,k,te,void 0,void 0,i);o.connectionPointDragHandler=new L(s,o.selection,o.undoEngine,o.snapHelper,o.dragHelperContent),o.connectingPointDragHandler=new M(s,o.selection,o.undoEngine,o.snapHelper,o.dragHelperContent),o.isLoading(!1),o.selection.focused(s()),(0,f.$dx)(e).empty(),P.applyBindings(o,e);const n=(0,f.updateSurfaceContentSize)(o.surfaceSize,e),d=()=>{n()};return window.addEventListener("resize",d),(0,f.addDisposeCallback)(e,(function(){window.removeEventListener("resize",d)})),o.tabPanel.width.subscribe((()=>{n()})),n(),o}var ie,re;P.bindingHandlers.routeLineDraggable={init:(e,t)=>{let a=null;const i=t(),r=(0,f.extend)({snap:".dxrd-drag-snap-line",snapTolerance:f.SnapLinesHelper.snapTolerance},P.unwrap(i),{start:function(e,t){i.starting()},stop:function(e,t){i.stopped()},drag:function(e,t){a=a||{left:(0,f.convertFromCssPixelUnits)(t.dataset.leftPosition),top:(0,f.convertFromCssPixelUnits)(t.dataset.topPosition)};const r=a&&e.pageX-a.left,s=a&&e.pageY-a.top;i.forceResize({delta:{dx:r||0,dy:s||0}})}}),s=new f.Draggable(e,r);(0,f.addDisposeCallback)(e,(()=>{s.dispose(),e=null}))}},function(e){e[e.Unknown=0]="Unknown",e[e.Boolean=1]="Boolean",e[e.Byte=2]="Byte",e[e.SByte=3]="SByte",e[e.Char=4]="Char",e[e.Decimal=5]="Decimal",e[e.Double=6]="Double",e[e.Single=7]="Single",e[e.Int32=8]="Int32",e[e.UInt32=9]="UInt32",e[e.Int16=10]="Int16",e[e.UInt16=11]="UInt16",e[e.Int64=12]="Int64",e[e.UInt64=13]="UInt64",e[e.String=14]="String",e[e.DateTime=15]="DateTime",e[e.Guid=16]="Guid",e[e.TimeSpan=17]="TimeSpan",e[e.ByteArray=18]="ByteArray"}(ie||(ie={}));class se{constructor(e){this.name=e.Name,this.type=e.ColumnType,this.size=e.Size}static GetType(e){switch(e){case ie.Boolean:return f.DotnetTypes.SystemBoolean;case ie.Byte:return f.DotnetTypes.SystemByte;case ie.SByte:return f.DotnetTypes.SystemSByte;case ie.Char:return f.DotnetTypes.SystemChar;case ie.Decimal:return f.DotnetTypes.SystemDecimal;case ie.Double:return f.DotnetTypes.SystemDouble;case ie.Single:return f.DotnetTypes.SystemSingle;case ie.Int32:return f.DotnetTypes.SystemInt32;case ie.UInt32:return f.DotnetTypes.SystemUInt32;case ie.Int16:return f.DotnetTypes.SystemInt16;case ie.UInt16:return f.DotnetTypes.SystemUInt16;case ie.Int64:return f.DotnetTypes.SystemInt64;case ie.UInt64:return f.DotnetTypes.SystemUInt64;case ie.String:return f.DotnetTypes.SystemString;case ie.DateTime:return f.DotnetTypes.SystemDateTime;case ie.Guid:return f.DotnetTypes.SystemGuid;case ie.TimeSpan:return f.DotnetTypes.SystemTimeSpan;case ie.ByteArray:return f.DotnetTypes.SystemBiteArray;default:return f.DotnetTypes.SystemObject}}static GetSpecific(e){switch(e){case f.DotnetTypes.SystemBoolean:return"Bool";case f.DotnetTypes.SystemByte:case f.DotnetTypes.SystemSByte:case f.DotnetTypes.SystemInt16:case f.DotnetTypes.SystemUInt16:case f.DotnetTypes.SystemInt32:case f.DotnetTypes.SystemUInt32:case f.DotnetTypes.SystemInt64:case f.DotnetTypes.SystemUInt64:return"Integer";case f.DotnetTypes.SystemChar:case f.DotnetTypes.SystemGuid:case"System.ByteArray":case f.DotnetTypes.SystemString:return"String";case f.DotnetTypes.SystemDouble:case f.DotnetTypes.SystemSingle:case f.DotnetTypes.SystemDecimal:return"Float";case f.DotnetTypes.SystemDateTime:case f.DotnetTypes.SystemTimeSpan:case f.DotnetTypes.SystemDateOnly:return"Date";case f.DotnetTypes.SystemTimeOnly:return"Time";default:return"String"}}}class oe{constructor(e){this.name=e.Name,this.primaryKeyTable=e.PrimaryKeyTable,this.columns=e.Columns,this.primaryKeyColumns=e.PrimaryKeyTableKeyColumns}}function ne(e,t,a){const i=a||[];return e&&e.forEach((e=>{i.push(t(e))})),i}class de{constructor(e){this.name=e.Name,this.isView="true"===e.IsView||!0===e.IsView,this.columns=ne(e.columns,(e=>new se(e))),this.foreignKeys=ne(e.foreignKeys,(e=>new oe(e)))}}class le{constructor(e){this.name=e.Name,this.arguments=ne(e.arguments,(e=>new ce(e)))}}!function(e){e[e.In=0]="In",e[e.Out=1]="Out",e[e.InOut=2]="InOut"}(re||(re={}));class ce{constructor(e){this.name=e.Name,this.type=e.Type,this.direction=e.Direction}}class ue{constructor(e){const t=ne(e.Tables,(e=>new de(e)));t.sort(((e,t)=>e.name.localeCompare(t.name)));const a=ne(e.Views,(e=>new de(e)));a.sort(((e,t)=>e.name.localeCompare(t.name))),this.tables=t.concat(a),this.procedures=ne(e.StoredProcedures,(e=>new le(e)))}assignTablesAndViews(e,t){this.tables=e.concat(t)}}function pe({requestWrapper:e,connection:t,tables:a,getTables:i,getViews:r}){const s=new f.DxDeferred;return e.getDbSchema({connection:t,tables:a,getTables:i,getViews:r}).done((e=>{s.resolve(new ue(JSON.parse(e.dbSchemaJSON)))})).fail((e=>{(0,f.ShowMessage)((0,f.formatUnicorn)((0,A.getLocalization)("Schema loading failed. {0}","DxDesignerStringId.Error_SchemaLoadingFailed"),(0,f.getErrorMessage)(e)),"error",5e3),s.reject()})),s.promise()}function he(e,t){const a=new f.DxDeferred;return e.getDbStoredProcedures(t).done((e=>{a.resolve(new ue(JSON.parse(e.dbSchemaJSON)).procedures)})).fail((e=>{(0,f.ShowMessage)((0,f.formatUnicorn)((0,A.getLocalization)("Stored procedures loading failed. {0}","DxDesignerStringId.Error_SchemaLoadingFailed"),(0,f.getErrorMessage)(e)),"error",5e3),a.reject()})),a.promise()}const me={Save:"dxqb-save",DataPreview:"dxqb-data-preview",SelectStatementPreview:"dxqb-select-statement-preview"},ge=(0,f.createGlobalModuleVariableFunc)("DXQB.axd");function xe(e){const t=new A.ModelSerializer,a={DataConnection:t.serialize(e)};return e.options&&(0,f.extend)(a,{ConnectionOptions:t.serialize(e.options)}),JSON.stringify(a)}class fe{sendRequest(e,t){return(0,f.sendRequest)(ge(),e,t)}_sendRequest(e){return(0,f.sendRequest)(e)}getDbSchema({connection:e,tables:t,getViews:a,getTables:i}){const r={connectionJSON:xe(e),tables:null,views:null,getTables:i,getViews:a};return t&&t.length>0&&(r.tables=(t||[]).filter((e=>!e.isView)).map((e=>e.name)),r.views=(t||[]).filter((e=>e.isView)).map((e=>e.name))),this.sendRequest("getDBSchema",(0,A.PrepareRequestArgs)(r))}getDbStoredProcedures(e){const t=(0,A.PrepareRequestArgs)({connectionJSON:xe(e)});return this.sendRequest("getDBStoredProcedures",t)}getSelectStatement(e,t){const a=(0,A.PrepareRequestArgs)({connectionJSON:xe(e),sqlQueryJSON:t});return this.sendRequest("getSelectStatement",a)}getDataPreview(e,t){const a=(0,A.PrepareRequestArgs)({connectionJSON:xe(e),sqlQueryJSON:t});return this.sendRequest("getDataPreview",a)}rebuildResultSchema(e,t,a=!1,i,r){const s=(0,A.PrepareRequestArgs)({sqlDataSourceJSON:JSON.stringify({SqlDataSource:(new A.ModelSerializer).serialize(e)}),queryName:t,relationsEditing:a,parameters:i,dataSerializationExtension:r});return this.sendRequest("rebuildResultSchema",s)}getFederationResultSchema(e){const t=e.getSerializableModel().getSerializableFederationDataSourceInfo(),a=(0,A.PrepareRequestArgs)({federationDataSourceJSON:JSON.stringify({FederationDataSource:(new A.ModelSerializer).serialize(e)}),dataSources:t.dataSources});return this.sendRequest("getFederationResultSchema",a)}validateJsonUri(e){const t=JSON.stringify(e.source.serialize(!0)),a=(0,A.PrepareRequestArgs)({uriJsonSourceJSON:t}),i={uri:ge(),action:"validateJsonEndPoint",arg:a,ignoreError:()=>!0};return this._sendRequest(i)}saveJsonSource(e,t){const a=t.source,i=JSON.stringify(a.serialize(!0)),r=(0,A.PrepareRequestArgs)({connectionName:e,customJson:a.json(),uriJsonSourceJSON:i});return this.sendRequest("saveJsonSource",r)}getJsonSchema(e,t){const a=e.source,i=JSON.stringify(a.serialize(!0)),r=(0,A.PrepareRequestArgs)({connectionName:e.connectionName(),customJson:a.json(),uriJsonSourceJSON:i,parameters:t});return this.sendRequest("getJsonSchema",r)}getObjectTypeDescriptions(e){return this.sendRequest("getObjectSchema",e)}}class be extends A.Disposable{_getDBSchema(e,t,a){return pe({requestWrapper:this._requestWrapper,connection:this.connection,tables:e,getTables:a,getViews:t})}_getDBStoredProcedures(e){return he(this._requestWrapper,e)}constructor(e,t=new fe){super(),this._requestWrapper=t,this._tables={},this._tableRequests=P.observableArray([]).extend({deferred:!0}),this.connection=e,this._disposables.push(this.connection.name.subscribe((()=>{this._tables={},this._dbSchema=null,this._dbStoredProceduresSchema=null}))),this._disposables.push(P.computed((()=>{const e=this._tableRequests();if(!e.length)return;this._tableRequests([]);const t=e.map((e=>e.table));this._getDBSchema(t).done((t=>{e.forEach((e=>{const a=t.tables.filter((t=>t.name===e.table.name))[0];a?(e.table.columns=a.columns,e.deferred.resolve(e.table)):e.deferred.reject()}))})).fail((()=>e.forEach((e=>e.deferred.reject()))))}))),this.getItems=e=>{const t=new f.DxDeferred;return e.fullPath?t.resolve([]):this.getDbSchema().done((e=>{t.resolve((e.tables||[]).map((e=>({name:e.name,displayName:e.name,isList:!1,specifics:e.isView?"view":"table",dragData:{noDragable:!1}}))))})),t.promise()}}getDbViews(){return this._dbViewsSchema&&"rejected"!==this._dbViewsSchema.state()||(this._dbViewsSchema=this._getDBSchema(void 0,!0,!1)),this._dbViewsSchema}getDbTables(){return this._dbTablesSchema&&"rejected"!==this._dbTablesSchema.state()||(this._dbTablesSchema=this._getDBSchema(void 0,!1,!0)),this._dbTablesSchema}getDbSchema(){if(!this._dbSchema||"rejected"===this._dbSchema.state()){const e=new f.DxDeferred;this._dbSchema=e,f.DxDeferred.when([this.getDbTables(),this.getDbViews()]).done((t=>{const a=new ue({});a.assignTablesAndViews(t[0].tables,t[1].tables),e.resolve(a)})).fail((()=>e.reject()))}return this._dbSchema.promise()}getDbStoredProcedures(){return this._dbStoredProceduresSchema&&"rejected"!==this._dbStoredProceduresSchema.state()||(this._dbStoredProceduresSchema=this._getDBStoredProcedures(this.connection)),this._dbStoredProceduresSchema}getDbTable(e,t=""){let a=this.getDbSchema;if(t.startsWith("tables")?a=this.getDbTables:t.startsWith("views")&&(a=this.getDbViews),!this._tables[e]){const t=new f.DxDeferred;this._tables[e]=t.promise(),a.call(this).done((a=>{const i=(0,f.findFirstItemMatchesCondition)(a.tables,(t=>t.name===e));i?i.columns.length>0?t.resolve(i):this._tableRequests.push({table:i,deferred:t}):(t.reject(),(0,f.isCustomizedWithUpdateLocalizationMethod)("The schema does not contain the specified table: ")?(0,A.getLocalization)("The schema does not contain the specified table: "):(0,f.formatUnicorn)((0,A.getLocalization)('The schema does not contain the specified table: "{0}".',"DataAccessStringId.TableNotInSchemaValidationException"),e))})).fail((()=>t.reject()))}return this._tables[e]}}const ye=[{propertyName:"tableName",modelName:"@Name"},{propertyName:"columns",modelName:"Fields",array:!0,info:[{propertyName:"name",modelName:"@Name"},{propertyName:"propertyType",modelName:"@Type"}]}];class Se{getInfo(){return ye}constructor(e,t){(t=t||new A.ModelSerializer).deserialize(this,e)}}const _e=[{propertyName:"name",modelName:"@Name"},{propertyName:"tables",modelName:"Views",array:!0}];class ve{getInfo(){return _e}static from(e,t){return e&&new ve(e.DataSet,t)||null}static toJson(e,t,a){return{DataSet:t.serialize(e,_e,a)}}constructor(e,t){(t=t||new A.ModelSerializer).deserialize(this,e),this.tables=(0,A.deserializeArray)(e&&e.Views||[],(e=>new Se(e,t)))}}var Ce;!function(e){e[e.Object=0]="Object",e[e.Array=1]="Array",e[e.Property=2]="Property"}(Ce||(Ce={}));class Te{static from(e,t){return new Te(e,t)}static toJsonNodes(e,t,a){return(e||[]).map((e=>Te.toJsonNode(e,t,a)))}static toJsonNode(e,t,a,i=!0){const r=t.serialize(e,we,a),s=i?Te.toJsonNodes(e.nodes,t,a):[];return s.length>0&&(r.Node=s),r}getInfo(){return we}constructor(e,t){if(this.nodes=[],!e)return;(t=t||new A.ModelSerializer).deserialize(this,e);const a=e.Node,i=a instanceof Array?ne(a,(e=>new Te(e))).sort(((e,t)=>e.name().localeCompare(t.name()))):a?[new Te(a)]:[];this.nodes=i}}class Pe extends Te{static from(e,t){return new Pe(e||{},t)}static toJson(e,t,a){if(!e)return{};const i={Node:Te.toJsonNode(e,t,a,!1)};return i.Node.Node=Te.toJsonNodes(e.nodes,t,a),i}getInfo(){return we}constructor(e,t){super(e.Node,t),this.nodeType=Ce[Ce.Object],this.valueType="Unknown",this.displayName=(0,A.getLocalization)("root"),this.selected=P.observable(!1)}}class Ne extends Te{static from(e,t){return new Ne(e,t)}static toJson(e,t,a){return Pe.toJson(e.nodes[0],t,a)}getInfo(){return De}constructor(e,t){super(e),this._rootElementList=null}getRootElementPartList(e=!0){if(this._rootElementList)return this._rootElementList;if(0===this.nodes.length)return[];this._rootElementList=[];const t=this.nodes[0],a={fullPath:P.unwrap(t.name),path:"",pathParts:[P.unwrap(t.name)]};return this._fillRootElementList(t,a,e),this._rootElementList.sort(((e,t)=>e.fullPath.localeCompare(t.fullPath))),this._rootElementList}_fillRootElementList(e=this.nodes[0],t={fullPath:"root",path:"",pathParts:["root"]},a){if(!e)return this._rootElementList;const i=P.unwrap(e.nodeType);if(i!=Ce[Ce.Property]){if(i!==Ce[Ce.Array])return a&&i===Ce[Ce.Object]&&this._rootElementList.push(t),(e.nodes||[]).forEach((e=>{const i=this._getNextPath(t,e.name());this._fillRootElementList(e,i,a)})),this._rootElementList;this._rootElementList.push(t)}}_getNextPath(e,t){const a=e.fullPath?[e.fullPath,t].join("."):t;return{pathParts:e.pathParts.concat(t),fullPath:a,path:t}}}const we=[{propertyName:"nodes",modelName:"Node",from:Te.from,toJsonObject:Te.toJsonNodes},{propertyName:"name",modelName:"@Name"},{propertyName:"selected",modelName:"@Selected",from:A.parseBool},{propertyName:"nodeType",modelName:"@NodeType"},{propertyName:"type",modelName:"@Type"}],De=[{propertyName:"nodes",modelName:"Node",from:Ne.from,toJsonObject:Ne.toJsonNodes}];class Ee{static from(e,t){return new Ee(e,t)}static toJson(e,t,a){return t.serialize(e,e.getInfo(),a)}getInfo(){return[{propertyName:"password",modelName:"@password",defaultVal:""},{propertyName:"userName",modelName:"@user",defaultVal:""}]}constructor(e,t){(t=t||new A.ModelSerializer).deserialize(this,e||{})}}var Ie;!function(e){e[e.PathParameter=0]="PathParameter",e[e.QueryParameter=1]="QueryParameter",e[e.Header=2]="Header"}(Ie||(Ie={}));class Ae extends A.Disposable{static from(e,t){return new Ae(e||{},t)}static toJson(e,t,a){return t.serialize(e,e.getInfo(),a)}_initEditingProperties(){const e=this.value()||"";0===e.indexOf(this.expression_Prefix)?(this.isExpression(!0),this._expression.value(e.substring(this.expression_Prefix.length))):this._editingValue(e)}switchEditors(){const e=!this.isExpression();this.isExpression(e),e?this._expression.value(this._editingValue()?"'"+this._editingValue()+"'":""):this._editingValue("")}getInfo(){return[{propertyName:"name",modelName:"@Name",displayName:"Name",editor:x.editorTemplates.getEditor("text")},{propertyName:"value",modelName:"@Value",displayName:"Value",editor:x.editorTemplates.getEditor("text")},{propertyName:"itemType",modelName:"@ItemType",defaultVal:Ie[Ie.PathParameter],alwaysSerialize:!0}]}constructor(e,t){super(),this.expression_Prefix="expression:",this.namePlaceholder=()=>(0,A.getLocalization)("Name","AnalyticsCoreStringId.CollectionEditor_Name_Placeholder"),this.valuePlaceholder=()=>(0,A.getLocalization)("Value","AnalyticsCoreStringId.CollectionEditor_Value_Placeholder"),this._editingValue=P.observable(""),this._expression={value:P.observable("")},this.isExpression=P.observable(!1),this.itemsProvider=null,this._parameterTypes=ke,this.nameValidationRules=void 0,(t=t||new A.ModelSerializer).deserialize(this,e),this._initEditingProperties(),this._disposables.push(this.value=P.computed((()=>{const e=this.expression_Prefix+this._expression.value(),t=this._editingValue();return this.isExpression.peek()?e:t})))}}const ke=[{value:Ie[Ie.PathParameter],displayValue:"Path Parameter",localizationId:"AnalyticsCoreStringId.JsonDSWizard_ChooseJsonSourcePage_PathParameter"},{value:Ie[Ie.QueryParameter],displayValue:"Query Parameter",localizationId:"AnalyticsCoreStringId.JsonDSWizard_ChooseJsonSourcePage_QueryParameter"},{value:Ie[Ie.Header],displayValue:"Header",localizationId:"AnalyticsCoreStringId.JsonDSWizard_ChooseJsonSourcePage_Header"}];class ze extends A.Disposable{static from(e,t){return new ze(e||{},t)}static toJson(e,t,a){return t.serialize(e,$e,a)}getInfo(){return $e}constructor(e={},t){function a(e,a){return e&&Array.isArray(e[Ie[a]])&&e[Ie[a]]&&(e=e[Ie[a]]),(0,A.deserializeArray)(e,(e=>(e["@ItemType"]||(e["@ItemType"]=Ie[a]),new Ae(e,t))))}super(),this.sourceType=P.observable(),this.uri=P.observable(),this.json=P.observable(),(t=t||new A.ModelSerializer).deserialize(this,e),this._disposables.push(this.uri.subscribe((e=>{e&&this.sourceType(ze._URIJSONSOURCE_TYPE)}))),this._disposables.push(this.json.subscribe((e=>{e&&this.sourceType(ze._CUSTOMJSONSOURCE_TYPE)}))),this.queryParameters=a(e.QueryParameters,Ie.QueryParameter),this.headers=a(e.Headers,Ie.Header),this.pathParameters=a(e.PathParameters,Ie.PathParameter)}serialize(e=!1){return e?{Source:this.serialize()}:(new A.ModelSerializer).serialize(this)}resetSource(){this.sourceType(""),this.json(""),this.uri("")}}ze._URIJSONSOURCE_TYPE="DevExpress.DataAccess.Json.UriJsonSource",ze._CUSTOMJSONSOURCE_TYPE="DevExpress.DataAccess.Json.CustomJsonSource";const $e=[{propertyName:"sourceType",modelName:"@SourceType",defaultVal:""},{propertyName:"json",modelName:"@Json",defaultVal:""},{propertyName:"uri",modelName:"@Uri",defaultVal:""},{propertyName:"authenticationInfo",modelName:"AuthenticationInfo",from:Ee.from,toJsonObject:Ee.toJson},{propertyName:"headers",modelName:"Headers",array:!0},{propertyName:"queryParameters",modelName:"QueryParameters",array:!0},{propertyName:"pathParameters",modelName:"PathParameters",array:!0}],Oe=(e,t,a=[])=>{const i=new f.DxDeferred;return e.getJsonSchema(t,a).done((e=>{try{const t=JSON.parse(e.jsonSchemaJSON),a=new Ne(t);i.resolve(a)}finally{"pending"===i.state()&&i.reject()}})).fail((e=>{(0,f.ShowMessage)((0,f.formatUnicorn)((0,A.getLocalization)("Schema loading failed. {0}","DxDesignerStringId.Error_SchemaLoadingFailed"),(0,f.getErrorMessage)(e))),i.reject()})),i.promise()};let Be=Oe;function Re(e){Be=e}function Me(){Be=Oe}class qe extends A.Disposable{constructor(e,t=new fe){super(),this._requestWrapper=t,this._jsonDataSource=e,this._disposables.push(this._jsonDataSource.source.sourceType.subscribe((()=>{this._jsonSchemaPromise=null}))),this.getItems=e=>{const t=new f.DxDeferred;return(this._jsonSchema?(new f.DxDeferred).resolve(this._jsonSchema).promise():this.getJsonSchema()).done((a=>{this._jsonSchema=a;const i=this.getSchemaByPath(e,a);t.resolve(i)})).fail(t.reject),t.promise()}}reset(){this._jsonSchemaPromise=null}mapToDataMemberContract(e){return(e||[]).map((e=>({name:e.name(),displayName:e.displayName||e.name(),isSelected:e.selected(),isList:e.nodes&&e.nodes.length>0,specifics:"table",dragData:{noDragable:!1}})))}getSchemaByPath(e,t){if(e.fullPath){let a=t.nodes;for(let t=0;t<e.pathParts.length;t++){const i=(a||[]).filter((a=>a.name()==e.pathParts[t]))[0];if(!i)return[];a=i.nodes}return this.mapToDataMemberContract(a)}return this.mapToDataMemberContract(t.nodes)}getJsonSchema(e=[]){return this._jsonSchemaPromise&&"rejected"!==this._jsonSchemaPromise.state()||(this._jsonSchemaPromise=Be(this._requestWrapper,this._jsonDataSource,e)),this._jsonSchemaPromise}}class Le extends A.Disposable{getInfo(){return Qe}clone(e){const t=(e||new A.ModelSerializer).serialize(this);return new Le(t)}static from(e,t){return new Le(e,t)}static toJson(e,t,a){return t.serialize(e,Qe,a)}constructor(e,t,a=new fe){super(),(t=t||new A.ModelSerializer).deserialize(this,e),this.jsonSchemaProvider=new qe(this,a),this.source&&this._disposables.push(this.source),this._disposables.push(this.connectionName.subscribe((()=>{this.source.resetSource()})))}getSchema(e=[]){const t=new f.DxDeferred;return this.jsonSchemaProvider.getJsonSchema(e).done((e=>{this.schema=e,t.resolve(e)})).fail((()=>{this.schema=null,t.reject()})),t.promise()}}const Qe=[{propertyName:"name",modelName:"@Name"},{propertyName:"connectionName",modelName:"@ConnectionName"},{propertyName:"rootElement",modelName:"@RootElement",defaultVal:"root"},{propertyName:"schema",modelName:"Schema",from:Ne.from,toJsonObject:Ne.toJson},{propertyName:"source",modelName:"Source",from:ze.from,toJsonObject:ze.toJson}],Ve=[{propertyName:"closeConnection",modelName:"@CloseConnection",from:A.parseBool},{propertyName:"commandTimeout",modelName:"@DbCommandTimeout",from:e=>{let t=parseInt(e);return isNaN(t)&&(t=null),P.observable(t)},defaultVal:null}];class We{getInfo(){return Ve}constructor(e,t){this.closeConnection=P.observable(!0),this.commandTimeout=P.observable(null),(t=t||new A.ModelSerializer).deserialize(this,e)}}const Fe={customSqlQuery:"CustomSqlQuery",tableQuery:"SelectQuery",storedProcQuery:"StoredProcQuery"},je={fileJsonSource:"FileJsonSource",customJsonSource:"CustomJsonSource",uriJsonSource:"UriJsonSource"};var Ue;function He(e){return e&&!e.match(/[~`!"№;%\^:\?*\(\)&\-\+={}\[\]\|\\\/,\.<>'\s]/)}!function(e){e[e.SelectNode=0]="SelectNode",e[e.UnionNode=1]="UnionNode",e[e.SourceNode=2]="SourceNode",e[e.TransformationNode=3]="TransformationNode"}(Ue||(Ue={}));const Je=[{type:"custom",validationCallback:e=>He(e.value),get message(){return(0,A.getLocalization)("Name is required and should be a valid identifier.","AnalyticsCoreStringId.NameIsRequired_Error")}}],Ge={propertyName:"value",displayName:"Value",localizationId:"DevExpress.DataAccess.Parameter.Value",editor:x.editorTemplates.getEditor("text")},Xe={propertyName:"name",displayName:"Name",localizationId:"DevExpress.DataAccess.Parameter.Name",validationRules:Je,editor:x.editorTemplates.getEditor("text")},Ye={propertyName:"type",displayName:"Type",localizationId:"DevExpress.DataAccess.Parameter.Type",modelName:"@Type",editor:x.editorTemplates.getEditor("combobox"),valuesArray:[{value:f.DotnetTypes.SystemString,displayValue:"String",localizationId:"AnalyticsCoreStringId.Parameter_Type_String"},{value:f.DotnetTypes.SystemDateTime,displayValue:"Date and Time",localizationId:"AnalyticsCoreStringId.Parameter_Type_DateTime"},{value:f.DotnetTypes.SystemDateOnly,displayValue:"Date",localizationId:"AnalyticsCoreStringId.Parameter_Type_DateOnly"},{value:f.DotnetTypes.SystemTimeOnly,displayValue:"Time",localizationId:"AnalyticsCoreStringId.Parameter_Type_TimeOnly"},{value:f.DotnetTypes.SystemInt16,displayValue:"Number (16 bit integer)",localizationId:"AnalyticsCoreStringId.Parameter_Type_Int16"},{value:f.DotnetTypes.SystemInt32,displayValue:"Number (32 bit integer)",localizationId:"AnalyticsCoreStringId.Parameter_Type_Int32"},{value:f.DotnetTypes.SystemInt64,displayValue:"Number (64 bit integer)",localizationId:"AnalyticsCoreStringId.Parameter_Type_Int64"},{value:f.DotnetTypes.SystemSingle,displayValue:"Number (floating-point)",localizationId:"AnalyticsCoreStringId.Parameter_Type_Float"},{value:f.DotnetTypes.SystemDouble,displayValue:"Number (double-precision floating-point)",localizationId:"AnalyticsCoreStringId.Parameter_Type_Double"},{value:f.DotnetTypes.SystemDecimal,displayValue:"Number (decimal)",localizationId:"AnalyticsCoreStringId.Parameter_Type_Decimal"},{value:f.DotnetTypes.SystemBoolean,displayValue:"Boolean",localizationId:"AnalyticsCoreStringId.Parameter_Type_Boolean"},{value:f.DotnetTypes.SystemGuid,displayValue:"Guid",localizationId:"AnalyticsCoreStringId.Parameter_Type_Guid"},{value:f.ExpressionType,displayValue:"Expression",localizationId:"DataAccessUIStringId.ParametersColumn_Expression"}]},Ke={propertyName:"resultType",displayName:"Result Type",localizationId:"DataAccessWebStringId.QueryBuilder_ResultType",modelName:"@ResultType",editor:x.editorTemplates.getEditor("combobox"),valuesArray:Ye.valuesArray.filter((e=>!(0,f.IsDataAccessExpression)(e.value)))},Ze=[{propertyName:"_name",modelName:"@Name"},{propertyName:"_value",modelName:"#text"},Ge,{propertyName:"itemType",modelName:"@ItemType"}],et=[Xe,Ye,Ke].concat(Ze),tt=[{value:f.DotnetTypes.SystemByte,displayValue:"Non-negative number (8 bit integer)",localizationId:"DataAccessStringId.Type_Byte"},{value:f.DotnetTypes.SystemSByte,displayValue:"Number (8 bit integer)",localizationId:"DataAccessStringId.Type_SByte"},{value:f.DotnetTypes.SystemUInt32,displayValue:"Non-negative number (32 bit integer)",localizationId:"DataAccessStringId.Type_UInt"},{value:f.DotnetTypes.SystemUInt16,displayValue:"Non-negative number (16 bit integer)",localizationId:"DataAccessStringId.Type_UShort"},{value:f.DotnetTypes.SystemUInt64,displayValue:"Non-negative number (64 bit integer)",localizationId:"DataAccessStringId.Type_ULong"},{value:f.DotnetTypes.SystemChar,displayValue:"Char",localizationId:"DataAccessStringId.Type_Char"},{value:f.DotnetTypes.SystemObject,displayValue:"Object",localizationId:"DataAccessStringId.Type_Object"},{value:f.DotnetTypes.SystemBiteArray,displayValue:"Byte array",localizationId:"DataAccessStringId.Type_ByteArray"},{value:f.DotnetTypes.SystemTimeSpan,displayValue:"Time interval",localizationId:"DataAccessStringId.Type_TimeSpan"}];function at(e){const t=(0,f.extend)(!0,{},Ye),a=[];a.push(Ye.valuesArray.filter((t=>t.value===e))[0]||tt.filter((t=>t.value===e))[0]),a.push(Ye.valuesArray.filter((e=>(0,f.IsDataAccessExpression)(e.value)))[0]),t.valuesArray=a;const i=(0,f.extend)(!0,{},Ke);return i.valuesArray=a.slice(0,0),i.disabled=!0,[(0,f.extend)({disabled:!0},Xe),t,i].concat(Ze)}const it=DevExpress.Analytics.Widgets.Internal,rt=(0,it.combineFunctionDisplay)({String:{CreateTable:[{paramCount:1,text:"CreateTable(, )",displayName:"CreateTable(Column1, ..., ColumnN)",descriptionStringId:"ExpressionEditorStringId.Function_CreateTable"}],FormatString:[{paramCount:1,text:"FormatString(, )",displayName:"FormatString(Format, Value1, ... , ValueN)",descriptionStringId:"ExpressionEditorStringId.Function_FormatString"}]}});function st(e,t,a){return(0,f.integerValueConverter)(e,t,a)}function ot(e,t,a){return(0,f.floatValueConverter)(e,t,a)}class nt{constructor(e,t,a,i){this.name=e,this.defaultValue=t,this.realTypeName=i,a&&(this.valueConverter=e=>a(e,t,this.name))}get specifics(){return se.GetSpecific(this.realTypeName||this.name)}}const dt=e=>{let t;try{t=(0,f.parseDate)(e)}catch(e){t=lt()}return t},lt=()=>{const e=new Date;return e.setHours(0,0,0,0),e};class ct extends A.Disposable{static _getTypeValue(e,t=null){const a=ct._typeValues.filter((t=>t.name===e));return a.length>0?(t&&(0,f.IsDataAccessExpression)(e)&&(a[0].realTypeName=t),a[0]):{name:e,defaultValue:null,specifics:"String",disableEditor:!0}}_getTypeValue(e){return ct._getTypeValue(e,this.resultType())}_tryConvertValue(e,t){if(!ct._isValueValid(e))return t.defaultValue;const a=(t.valueConverter||(e=>e))(e);return ct._isValueValid(a)?a:t.defaultValue}static _isValueValid(e){return null!=e&&!isNaN("string"==typeof e?"":e)}getEditorType(e){return(0,f.getEditorType)(e)}_updateValueInfo(e){const t=this._getTypeValue(e),a=this._tryConvertValue(this._value(),t),i=this._expressionValue.peek();this._expressionValue(null),this._value(null),this._valueInfo((0,f.extend)({},Ge,{editor:this.getEditorType(t.name),disabled:!0===t.disableEditor,editorOptions:{onFocusOut:e=>{this.isValid(e.component.option("isValid"))}}})),this._expressionValue(i),this._value(a)}constructor(e,t,a=et){super(),this._serializationsInfo=a,this._valueInfo=P.observable(Ge),this._parametersFunctions=rt,this.isValid=P.observable(!0),(t=t||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Parameter"})),this._disposables.push(this.name=P.pureComputed({read:()=>this._name(),write:e=>{He(e)&&this._name(e)}})),this._expressionValue=P.observable({value:this._value,functions:this._parametersFunctions}),this._disposables.push(this.type.subscribe((e=>{(0,f.IsDataAccessExpression)(e)?this.resultType(this._previousResultType):(this.resultType(null),this._previousResultType=e),this._updateValueInfo(e)}))),this._previousResultType=(0,f.IsDataAccessExpression)(this.type.peek())?this.resultType():this.type(),this.value=P.pureComputed({read:()=>(0,f.IsDataAccessExpression)(this.type())?this._expressionValue():this._value(),write:e=>{this._value(e)}}),this._updateValueInfo(this.type.peek())}get specifics(){const e=(0,f.IsDataAccessExpression)(this.type.peek())?this.resultType():this.type(),t=ct._typeValues.filter((t=>t.name===e));return t.length>0?t[0].specifics:"string"}getInfo(){if(this.type){const e=(0,f.extend)(!0,[],this._serializationsInfo);return e.splice(e.indexOf(e.filter((e=>"value"===e.propertyName))[0]),1,this._valueInfo()),e}return this._serializationsInfo}isPropertyVisible(e){return"resultType"!==e||(0,f.IsDataAccessExpression)(this.type())}}ct._typeValues=[new nt(f.DotnetTypes.SystemDateTime,lt(),dt),new nt(f.DotnetTypes.SystemDateOnly,lt(),dt),new nt(f.DotnetTypes.SystemTimeOnly,lt(),dt),new nt(f.DotnetTypes.SystemString,""),new nt(f.DotnetTypes.SystemSByte,"0",st),new nt(f.DotnetTypes.SystemInt16,"0",st),new nt(f.DotnetTypes.SystemInt32,"0",st),new nt(f.DotnetTypes.SystemInt64,"0",st),new nt(f.DotnetTypes.SystemByte,"0",st),new nt(f.DotnetTypes.SystemUInt16,"0",st),new nt(f.DotnetTypes.SystemUInt32,"0",st),new nt(f.DotnetTypes.SystemUInt64,"0",st),new nt(f.DotnetTypes.SystemDecimal,"0",ot),new nt(f.DotnetTypes.SystemDouble,"0",ot),new nt(f.DotnetTypes.SystemSingle,"0",ot),new nt(f.DotnetTypes.SystemBoolean,!1,(e=>void 0!==e?"true"===String(e).toLowerCase():e)),new nt(f.DotnetTypes.SystemGuid,"00000000-0000-0000-0000-000000000000"),new nt(f.ExpressionType,"",(function(e){if(e instanceof Date){const t=e=>(e<10?"0":"")+e;return(0,f.formatUnicorn)("#{0}/{1}/{2} {3}:{4}#",t(e.getMonth()+1),t(e.getDate()),e.getFullYear(),t(e.getHours()),t(e.getMinutes()))}return(e||"").toString()})),new nt(f.DotnetTypes.SystemChar,"")];const ut=[{propertyName:"type",modelName:"@Type"},{propertyName:"name",modelName:"@Name"},{propertyName:"sqlString",modelName:"Sql",defaultVal:""},{propertyName:"parameters",modelName:"Parameters",array:!0},{propertyName:"itemType",modelName:"@ItemType"}];class pt{constructor(e,t,a){this.parent=t,(a||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Query"})),this.type=P.pureComputed((()=>Fe.customSqlQuery)),this.parameters=(0,A.deserializeArray)(e.Parameters,(e=>new ct(e,a)))}getInfo(){return ut}generateName(){return"CustomSqlQuery"}}const ht=[{propertyName:"masterQuery",modelName:"@Master"},{propertyName:"detailQuery",modelName:"@Detail"},{propertyName:"_customName",modelName:"@Name"},{propertyName:"keyColumns",modelName:"KeyColumns",array:!0,info:[{propertyName:"masterColumn",modelName:"@Master"},{propertyName:"detailColumn",modelName:"@Detail"},{propertyName:"itemType",modelName:"@ItemType"}]},{propertyName:"itemType",modelName:"@ItemType"}];class mt extends A.Disposable{dispose(){super.dispose(),this.disposeObservableArray(this.keyColumns)}constructor(e,t){super(),this.name=P.pureComputed({read:()=>this._customName()||this.masterQuery()+this.detailQuery(),write:e=>{this._customName(e)},deferEvaluation:!0}),(t||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Relation"})),this._disposables.push(this.name)}createKeyColumn(){const e={masterColumn:P.observable(),detailColumn:P.observable(),itemType:"KeyColumn"};this.keyColumns.push(e)}getInfo(){return ht}}class gt{static from(e,t){return new gt(e,t)}static toJson(e,t,a){return t.serialize(e,xt,a)}getInfo(){return xt}constructor(e,t){this.name=P.observable(),this.parameteres=P.observable(),this.fromAppConfig=P.observable(!0),(t=t||new A.ModelSerializer).deserialize(this,e)}}const xt=[{propertyName:"name",modelName:"@Name"},{propertyName:"parameteres",modelName:"Parameters"},{propertyName:"fromAppConfig",modelName:"@FromAppConfig",defaultVal:!1,from:A.parseBool}],ft=[{propertyName:"type",modelName:"@Type"},{propertyName:"name",modelName:"@Name"},{propertyName:"parameters",modelName:"Parameters",array:!0},{propertyName:"_tablesObject",modelName:"Tables",info:[{propertyName:"tables",modelName:"SelectedTables",array:!0,info:[{propertyName:"name",modelName:"@Name"},{propertyName:"alias",modelName:"@Alias"}]}]},{propertyName:"filterString",modelName:"Filter",defaultVal:""},{propertyName:"itemType",modelName:"@ItemType"}];class bt{constructor(e,t,a){this.parent=t,(a||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Query"})),this.type=P.pureComputed((()=>Fe.tableQuery)),this.parameters=(0,A.deserializeArray)(e.Parameters,(e=>new ct(e,a)))}tables(){return this._tablesObject.tables()}getInfo(){return ft}generateName(){return this.tables().length>0?this.tables()[0].alias()||this.tables()[0].name():"SelectQuery"}}const yt=[{propertyName:"type",modelName:"@Type"},{propertyName:"name",modelName:"@Name"},{propertyName:"procName",modelName:"ProcName"},{propertyName:"parameters",modelName:"Parameters",array:!0},{propertyName:"itemType",modelName:"@ItemType"}];class St{constructor(e,t,a){this.parent=t,(a||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Query"})),this.type=P.pureComputed((()=>Fe.storedProcQuery)),this.parameters=(0,A.deserializeArray)(e.Parameters,(e=>{let t=e["@Type"];return(0,f.IsDataAccessExpression)(t)&&(t=e["@ResultType"]||t),new ct(e,a,at(t))}))}getInfo(){return yt}generateName(){return this.procName()||"Query"}}const _t=[{propertyName:"name",modelName:"@Name"},{propertyName:"connection",modelName:"Connection",from:gt.from,toJsonObject:gt.toJson},{propertyName:"queries",modelName:"Queries",array:!0},{propertyName:"relations",modelName:"Relations",array:!0},{propertyName:"resultSet",modelName:"ResultSchema",from:ve.from,toJsonObject:ve.toJson},{propertyName:"itemType",modelName:"@ItemType"}];class vt extends A.Disposable{getInfo(){return _t}createQuery(e,t){if(e["@Type"]===Fe.customSqlQuery)return new pt(e,this,t);if(e["@Type"]===Fe.tableQuery)return new bt(e,this,t);if(e["@Type"]===Fe.storedProcQuery)return new St(e,this,t);throw new Error("Unknown sql query type.")}constructor(e,t,a=new fe){super(),(t=t||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"SqlDataSource"}));const i=this._model.Name;i&&(this.name()||this.name(i),delete this._model.Name),this.queries=(0,A.deserializeArray)(e.Queries,(e=>this.createQuery(e,t))),this.relations=(0,A.deserializeArray)(e.Relations,(e=>new mt(e,t))),this.connection&&e.ConnectionOptions&&(this.connection.options=new We(e.ConnectionOptions,t)),this.dbSchemaProvider=new be(this.connection,a),this._disposables.push(this.dbSchemaProvider),this._disposables.push(this.connection.name.subscribe((()=>{this.queries([]),this.relations([]),this.resultSet=null})))}}class Ct extends A.Disposable{constructor(){super(...arguments),this.name=P.observable()}setState(e){this.ctor=e.ctor,this.dataMember=e.dataMember,this.selectedType=e.selectedType,this.id=(0,f.guid)().replace(/-/g,""),this.name(e.dataSourceName)}}class Tt{constructor(e){e&&(()=>{this.name=e.name,this.displayName=e.displayName,this.dataMember=e.dataMember})()}}class Pt extends Tt{constructor(e){super(e),e&&(()=>{this.value=e.value,this.type=e.type,this.resultType=e.resultType})()}}class Nt extends Tt{constructor(e){super(e),this.parameters=ne(e.parameters,(e=>new Pt(e)))}}class wt extends Nt{constructor(e){super(e),this.disabled=P.observable(!1),this.memberType=e&&e.memberType,this.isStatic=e.isStatic}static empty(){return new wt({displayName:wt.entireObject(),name:wt.entireObject(),parameters:[]})}isEntireObject(){return this.name==wt.entireObject()&&this.displayName==wt.entireObject()}isAvailable(){return!this.disabled()}}wt.entireObject=()=>(0,A.getLocalization)("Entire Object","AnalyticsCoreStringId.ObjectDSWizard_ConfigureParameters_EntireObject");class Dt extends Nt{}class Et extends Tt{constructor(e){super(e),this.ctors=ne(e.ctors,(e=>new Dt(e))),this.members=ne(e.members,(e=>new wt(e)))}updateMembers(e){this.members.forEach((t=>{e?t.disabled(!1):t.disabled(!!t.isEntireObject()||!t.isStatic)}))}}const It=new x.EditorTemplates,At={propertyName:"name",modelName:"@Name",displayName:"Name",localizationId:"DevExpress.DataAccess.Sql.SqlQuery.Name",disabled:!0,editor:It.getEditor("text")},kt={propertyName:"alias",modelName:"@Alias",displayName:"Alias",localizationId:"DataAccessUIStringId.QueryBuilderColumns_Alias",defaultVal:"",editor:It.getEditor("text")},zt={propertyName:"text",modelName:"@Text",displayName:"Text",editor:It.getEditor("text")},$t={propertyName:"selected",displayName:"Output",editor:It.getEditor("bool"),localizationId:"DataAccessUIStringId.QueryBuilderColumns_Output"},Ot={propertyName:"size",modelName:"@Size",defaultVal:"100,125",from:b.Size.fromString},Bt={propertyName:"location",modelName:"@Location",from:b.Point.fromString},Rt=[Ot,Bt],Mt=[At].concat(Rt),qt=[{propertyName:"queryType",modelName:"@QueryType"},At,kt,{propertyName:"sourceName",modelName:"@SourceName"}];class Lt extends A.Disposable{constructor(e,t,a,i){super(),this._sourcePath=i,e["@SourceName"]=e["@SourceName"]||a,(t||new A.ModelSerializer).deserialize(this,e),this._disposables.push(this.queryType=P.pureComputed((()=>Ue[Ue.SourceNode])))}getInfo(){return qt}getPath(){if(this._sourcePath)return this._sourcePath;if(this.name()){if(this.name()===this.sourceName())return this.name();return this.sourceName().slice(0,this.sourceName().indexOf(this.name())-1)+"."+this.name()}}}const Qt=[{propertyName:"table",modelName:"@NodeAlias"},At,kt,{propertyName:"expressionType",modelName:"@ExpressionType"},{propertyName:"columnExpression",modelName:"@ColumnExpression"}];var Vt;!function(e){e[e.SelectColumnExpression=0]="SelectColumnExpression",e[e.SelectExpression=1]="SelectExpression",e[e.SelectAllColumnsExpression=2]="SelectAllColumnsExpression",e[e.SelectAllNodeColumnsExpression=3]="SelectAllNodeColumnsExpression",e[e.SelectRowCountExpression=4]="SelectRowCountExpression"}(Vt||(Vt={}));class Wt{constructor(e,t){(t=t||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Expression"}))}getInfo(){return Qt}}const Ft=[{propertyName:"name",modelName:"@Name"},{propertyName:"queries",modelName:"Queries",array:!0},{propertyName:"relations",modelName:"Relations",array:!0},{propertyName:"sources",modelName:"Sources",array:!0},{modelName:"SerializableSourceMap",propertyName:"serializableSourceMap",array:!0,info:[{modelName:"@DataSource",propertyName:"dataSource",link:!0},{modelName:"@Name",propertyName:"name"}]}],jt=[{propertyName:"sourceName",modelName:"@SourceName"},{propertyName:"dataMember",modelName:"@DataMember"}];class Ut extends A.Disposable{constructor(e,t,a,i){if(super(),t=t||new A.ModelSerializer,a){const t=a.split(".");t.length>1&&(e["@DataMember"]=t.slice(1).join("_"))}i&&(e["@SourceName"]=i),t.deserialize(this,(0,f.extend)(e,{"@ItemType":"Source"}))}getInfo(){return jt}getDataSourceName(){return this.hasDataMember()?this.sourceName().slice(0,this.sourceName().lastIndexOf(this.dataMember())-1):this.sourceName()}getPath(){return this.getDataSourceName()+(this.hasDataMember()?"."+this.dataMember():"")}hasDataMember(){return this.dataMember()&&0!==this.sourceName().lastIndexOf(this.dataMember())}}const Ht=DevExpress.Analytics.Criteria,Jt=DevExpress.Analytics.Criteria.Utils,Gt=new A.ControlsFactory;class Xt extends b.ElementViewModel{getControlFactory(){return Gt}constructor(e,t,a){super(e,t,a)}}class Yt extends K{getControlFactory(){return Gt}preInitProperties(){this.startPoint=P.observable(),this.endPoint=P.observable()}constructor(e,t,a){super((0,f.extend)(e,{"@ControlType":"JoinCondition","@ItemType":"KeyColumn"}),t,a),this.parentColumn=P.pureComputed((()=>t.parentTable().getColumn(this.parentColumnName()))),this.nestedColumn=P.pureComputed((()=>t.nestedTable().getColumn(this.nestedColumnName()))),this.joinType=t.joinType,this.left=P.pureComputed((()=>t.parentTableName()+"."+this.parentColumnName())),this.right=P.pureComputed((()=>t.nestedTableName()+"."+this.nestedColumnName())),this._disposables.push(P.computed((()=>{if(this.parentColumn()&&this.nestedColumn()){const e=I(this.parentColumn(),this.nestedColumn());this.startPoint().connectingPoint(e.start),this.endPoint().connectingPoint(e.end)}})))}}const Kt=[{propertyName:"joinType",modelName:"@Type"},{propertyName:"parentTableName",modelName:"@Parent"},{propertyName:"nestedTableName",modelName:"@Nested"},{propertyName:"conditions",modelName:"KeyColumns",defaultVal:[],array:!0},{propertyName:"itemType",modelName:"@itemType"}];class Zt extends Xt{_getConditionNumber(){let e=this.conditions().length+1;const t=this.conditions().map((e=>e.seriesNumber()));for(let a=0;a<this.conditions().length;a++)if(-1===t.indexOf(a+1)){e=a+1;break}return e}constructor(e,t,a){super((0,f.extend)(e,{"@ControlType":"Relation","@ItemType":"Relation"}),t,a),this.parentTable=P.observable(t.getTable(this.parentTableName.peek())),this.nestedTable=P.observable(t.getTable(this.nestedTableName.peek())),this.parentTableName=P.pureComputed((()=>this.parentTable().actualName())),this.nestedTableName=P.pureComputed((()=>this.nestedTable().actualName())),this.conditions=(0,A.deserializeArray)(e.KeyColumns,(e=>new Yt(e,this,a)))}getInfo(){return Kt}addChild(e){const t=e;this.conditions&&-1===this.conditions.indexOf(t)&&(t.seriesNumber(this._getConditionNumber()),t.parentModel(this),this.conditions.push(t))}removeChild(e){const t=this.conditions().indexOf(e);t>-1&&this.conditions.splice(t,1),0===this.conditions().length&&this.parentModel().removeChild(this)}}const ea=[{propertyName:"condition",modelName:"@Condition",defaultVal:""},{propertyName:"joinType",modelName:"@JoinType",defaultVal:"Inner"},{propertyName:"query",modelName:"Query"}];class ta{constructor(e,t){(t=t||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"JoinElement"})),e.Query&&(this.query=P.observable(new Lt(e.Query)))}static deserializeRelationModel(e,t){const a=t.parentTableName(),i=t.nestedTableName(),r=[];t.conditions().forEach((e=>{const t="["+i+"."+e.nestedColumnName()+"]",s="["+a+"."+e.parentColumnName()+"]",o=t+" "+Jt.operatorTokens[e.operator()]+" "+s;r.push(o)}));const s=new ta({"@JoinType":t.joinType(),"@Condition":r.join(" And ")});return s.query(e),s}_parsePath(e){return"["===e[0]&&"]"===e[e.length-1]?Jt.CriteriaOperatorStateMachine.parse(e).propertyName:e}_createCondition(e,t,a){return{"@ControlType":"JoinCondition","@ItemType":"KeyColumn","@Operator":a,"@Nested":t,"@Parent":e}}_conditionBinary(e){const t=this._parsePath(e.leftPart.propertyName),a=this._parsePath(e.rightPart.propertyName),i=Ht.BinaryOperatorType[e.operatorType];return this._createCondition(a.split(".")[1],t.split(".")[1],i)}createRelationModel(e){const t=Jt.CriteriaOperatorStateMachine.parse(this.condition()),a=[];t instanceof Ht.GroupOperator?t.operands.forEach((e=>{e instanceof Ht.BinaryOperator&&a.push(e)})):t instanceof Ht.BinaryOperator&&a.push(t);const i=this._parsePath(a[0].rightPart.propertyName).split(".")[0],r=this._parsePath(a[0].leftPart.propertyName).split(".")[0],s={};for(let e=0;e<a.length;e++){const t=a[e];s["Item"+(e+1)]=this._conditionBinary(t)}return new Zt({"@Parent":i,"@Nested":r,"@Type":this.joinType(),KeyColumns:s},e,new A.ModelSerializer)}getInfo(){return ea}}const aa=[{propertyName:"queryType",modelName:"@QueryType"},{propertyName:"alias",modelName:"@Alias"},{propertyName:"expressions",modelName:"Expressions",array:!0},{propertyName:"subNodes",modelName:"SubNodes",array:!0},{propertyName:"root",modelName:"Root"},{propertyName:"itemType",modelName:"@ItemType"}];class ia extends A.Disposable{constructor(e,t,a){super(),this._path=a,this.init(e,t,a)}get sources(){return P.observableArray([this.root()].concat(this.subNodes().map((e=>e.query()))).map((e=>new Ut({},new A.ModelSerializer,e.getPath(),e.sourceName()))))}getInfo(){return aa}generateName(){return this.root()?this.root().alias():"SelectQuery"}init(e,t,a){(t||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Query"})),this._disposables.push(this.queryType=P.pureComputed((()=>Ue[Ue.SelectNode]))),this.expressions=(0,A.deserializeArray)(e.Expressions,(e=>new Wt(e,t))),this.subNodes=(0,A.deserializeArray)(e.SubNodes,(e=>new ta(e,t))),e.Root&&(this.root=P.observable(new Lt(e.Root,t,this.alias(),a)))}}const ra=[{propertyName:"queryType",modelName:"@QueryType"},{propertyName:"alias",modelName:"@Alias"},{propertyName:"transformationRules",modelName:"TransformationRules",array:!0},{propertyName:"root",modelName:"Root"},{propertyName:"itemType",modelName:"@ItemType"}],sa=[{propertyName:"name",modelName:"@Name"},{propertyName:"alias",modelName:"@Alias"},{propertyName:"unfold",modelName:"@Unfold"},{propertyName:"flatten",modelName:"@Flatten"},{propertyName:"itemType",modelName:"@ItemType"}];class oa extends A.Disposable{constructor(e,t){super(),(t||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Query"})),this._disposables.push(this.queryType=P.pureComputed((()=>Ue[Ue.TransformationNode]))),this.transformationRules=(0,A.deserializeArray)(e.TransformationRules,(e=>new na(e,t))),e.Root&&(this.root=P.observable(new Lt(e.Root,t)))}get sources(){return P.observableArray([new Ut({},new A.ModelSerializer,this.root().getPath(),this.root().sourceName())])}getInfo(){return ra}generateName(){return this.root()&&this.root().sourceName().split("_").pop()}}class na{constructor(e,t){(t||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"TransformationRule"}))}getInfo(){return sa}}class da extends A.Disposable{constructor(e,t,a){super(),this.dataSources=t,this._serializer=a,this.sources=P.observableArray(),this._serializer=a||new A.ModelSerializer}_dataSourceName(e){return e?.data?.name()||e?.name}getQueryNameFromPath(e){const t=e.split("."),a=this.dataSources().filter((e=>e.ref==t[0]||e.id==t[0]))[0];return t[0]=this._dataSourceName(a)||t[0],t.join("_")}getPathFromQueryName(e){for(const t of this.sources())if(t.sourceName()===e){const e=t.getPath().split(".");let a=e.shift();const i=this.dataSources().filter((e=>this._dataSourceName(e)==a))[0];return a=i&&(i.ref||i.id)||a,a+(e.length>0?"."+e.join("."):"")}}createQuery(e,t){return e["@QueryType"]===Ue[Ue.SelectNode]?new ia(e,this._serializer,(t||this).getPathFromQueryName(e.Root&&e.Root["@SourceName"])):null}addSource(e,t){if(e instanceof Ut){return void(!this.sources().filter((t=>t.sourceName()==e.sourceName()))[0]&&this.sources.push(e))}!this.sources().filter((t=>t.sourceName()==e))[0]&&this.sources.push(new Ut({},this._serializer,t,e))}removeSource(e){const t=(0,f.findFirstItemMatchesCondition)(this.sources(),(t=>t.sourceName()===e));this.queries().some((t=>t.expressions().some((t=>t.table()===e))))||this.sources.remove(t)}addSelectQuery(e,t){const a=this.getQueryNameFromPath(e);let i=this.queries().filter((e=>e.alias()===a))[0];if(i||(i=new ia({"@Alias":a,Root:{}},this._serializer,e),this.queries.push(i),this.addSource(a,e)),!t||i.expressions().filter((e=>e.name()==t))[0])return;const r=new Wt({"@NodeAlias":a,"@Name":t,"@ExpressionType":Vt[Vt.SelectColumnExpression]},this._serializer);i.expressions.push(r)}removeQuery(e){const t=this.queries().filter((t=>t.alias()===e))[0];this.queries.remove(t);const a=this.sources().filter((t=>t.sourceName()==e))[0];a&&this.sources.remove(a)}removeExpression(e,t){const a=this.getQueryNameFromPath(t),i=this.queries().filter((e=>e.alias()===a))[0],r=i.expressions().filter((t=>t.name()==e))[0];i.expressions.remove(r),0==i.expressions().length&&this.removeQuery(a)}dispose(){super.dispose(),this.disposeObservableArray(this.sources),this.disposeObservableArray(this.queries)}}const la=[{propertyName:"queryType",modelName:"@QueryType"},{propertyName:"unionType",modelName:"@UnionType"},{propertyName:"alias",modelName:"@Alias"},{propertyName:"queries",modelName:"UnionElements",array:!0}];var ca;!function(e){e[e.Union=0]="Union",e[e.UnionAll=1]="UnionAll"}(ca||(ca={}));class ua extends da{constructor(e,t,a){super(e,t,a),(a||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Query"})),this._disposables.push(this.queryType=P.pureComputed((()=>Ue[Ue.UnionNode]))),this.queries=(0,A.deserializeArray)(e.UnionElements,(e=>this.createQuery(e)))}getInfo(){return la}generateName(){return this.queries()[0]&&this.queries()[0].alias().split("_").pop()}}const pa=[{propertyName:"masterColumn",modelName:"@Parent"},{propertyName:"detailColumn",modelName:"@Nested"},{propertyName:"itemType",modelName:"@ItemType"}],ha=[{propertyName:"masterQuery",modelName:"@MasterQueryName"},{propertyName:"detailQuery",modelName:"@DetailQueryName"},{propertyName:"_customName",modelName:"@RelationName"},{propertyName:"keyColumns",modelName:"KeyColumns",array:!0,info:pa},{propertyName:"itemType",modelName:"@ItemType"}];class ma extends mt{static create(e){const t=new A.ModelSerializer,a=new ma(t.serialize(e),t);return a.keyColumns().forEach((e=>e.getInfo=()=>pa)),a}constructor(e,t){super(e,t),this.getInfo=()=>ha,e["@MasterQueryName"]&&e["@DetailQueryName"]&&(t||new A.ModelSerializer).deserialize(this,(0,f.extend)(e,{"@ItemType":"Relation"}))}}class ga extends A.Disposable{get _currentDataSources(){const e=[];return this.dataSource&&this.dataSource.serializableSourceMap().reduce(((e,t)=>{const a=t.dataSource();return a&&-1===e.indexOf(a)&&e.push(a),e}),e),e}_collectDependentDataSources(e,t){e.serializableSourceMap&&e.serializableSourceMap().forEach((e=>this._collectDependentDataSources(e.dataSource(),t)));const a=P.unwrap(e.name);a&&!t.every((e=>P.unwrap(e.name)!==a))||t.push(e)}constructor(e,t){super(),this.dataSources=P.observableArray([]),this.serializer=t||new A.ModelSerializer,e&&(this.dataSource=e,this.dataSources(this._currentDataSources))}dispose(){this.dataSource=null,this.dataSources=null}getInfo(){return[{propertyName:"dataSources",modelName:"dataSources",array:!0},{propertyName:"dataSource",modelName:"dataSource"}]}collectDependentDataSources(){const e=[];return this._currentDataSources.forEach((t=>this._collectDependentDataSources(t,e))),e}serialize(){const e=new ga(null);e.dataSource=this.dataSource,e.dataSources(this.collectDependentDataSources());const t=this.serializer.serialize(e);return e.dispose(),t}getSerializableFederationDataSourceInfo(){const e=this.serialize();return{dataSource:JSON.stringify(e.dataSource),dataSources:Object.keys(e.dataSources||[]).map((t=>JSON.stringify(e.dataSources[t])))}}}class xa extends A.Disposable{getDbSchema(){const e=new f.DxDeferred;return this._rootItems.length?e.resolve(this._rootItems):this.getItems(new A.PathRequest("")).done((t=>{e.resolve(t)})),e.promise()}getDbTable(e,t){const a=new f.DxDeferred;return this.getItems(new A.PathRequest(t)).done((t=>{const i=t.map((e=>({Name:e.name})));a.resolve(new de({Name:e,columns:i}))})),a.promise()}dispose(){super.dispose(),this.getItems=null}constructor(e){super(),this._rootItems=[],this.getItems=e.getItems}}class fa extends da{getInfo(){return Ft}getSerializableModel(){return this._serializableModel}createQuery(e){const t=super.createQuery(e);if(!t){if(e["@QueryType"]===Ue[Ue.UnionNode])return new ua(e,this.dataSources,this._serializer);if(e["@QueryType"]===Ue[Ue.TransformationNode])return new oa(e,this._serializer)}return t}updateSerializableModel(){this.serializableSourceMap=P.observableArray(),this.sources().forEach((e=>{const t=e.getDataSourceName();t&&this.serializableSourceMap().push({name:P.observable(e.sourceName()),dataSource:P.observable(this.dataSources().filter((e=>this._dataSourceName(e)===t))[0].data)})})),this._serializableModel=new ga(this,this._serializer)}constructor(e,t,a,i){super(e,t,i),this.dataSources=t,this.fielListProvider=a,this.serializableSourceMap=P.observableArray(),this._serializer.deserialize(this,(0,f.extend)(e,{"@ItemType":"FederationDataSource"})),this.sources=(0,A.deserializeArray)(e.Sources,(e=>new Ut(e,i))),this.queries=(0,A.deserializeArray)(e.Queries,(e=>this.createQuery(e))),this.relations=(0,A.deserializeArray)(e.Relations,(e=>new ma(e,this._serializer))),a&&this._disposables.push(this.dbSchemaProvider=new xa(this.fielListProvider)),this.updateSerializableModel()}dispose(){super.dispose(),this.disposeObservableArray(this.relations),this._serializableModel.dispose()}get dependentDataSources(){return this.getSerializableModel().getSerializableFederationDataSourceInfo().dataSources}}function ba(e,t,a="name"){const i=(t[a]()||t.generateName()).replace(/\./g,"_");return(0,f.findFirstItemMatchesCondition)(e,(e=>e[a]()===i))?(0,f.getUniqueNameForNamedObjectsArray)(e,i+"_"):i}const ya={ChoosePredefinedDataSourcePage:"choosePredefinedDataSourcePage",ChooseDataSourceTypePage:"chooseDataSourceTypePage",ConfigureMasterDetailRelationshipsPage:"configureMasterDetailRelationshipsPage"},Sa={ChooseConnectionPage:"chooseSqlConnectionPage",ConfigureQueryPage:"configureSqlQueryPage",ConfigureParametersPage:"configureSqlParametersPage",MultiQueryConfigurePage:"multiSqlQueryConfigurePage",MultiQueryConfigureParametersPage:"multiSqlQueryConfigureParametersPage",FederatedQueryConfigurePage:"federatedQueryConfigurePage"},_a={FederatedQueryConfigurePage:"federatedQueryConfigurePage",FederatedMasterDetailRelationshipsPage:"federatedMasterDetailRelationshipsPage"},va={ChooseJsonSourcePage:"chooseJsonSourcePage",ChooseJsonSchemaPage:"chooseJsonSchemaPage",ChooseConnectionPage:"chooseJsonConnectionPage",SpecifyJsonConnectionPage:"specifyJsonConnectionPage"},Ca={ChooseTypesPage:"chooseObjectDataSourceTypesPage",ChooseDataMembersPage:"chooseObjectDataSourceDataMembersPage",ConfigureParametersPage:"configureObjectDataSourceParametersPage"},Ta={ChooseDataSourceTypePage:ya.ChooseDataSourceTypePage,SpecifySqlDataSourceSettingsPage:"specifySqlDataSourceSettingsPage",SpecifyJsonDataSourceSettingsPage:"specifyJsonDataSourceSettingsPage",SpecifyObjectDataSourceSettingsPage:"specifyObjectDataSourceSettingsPage",SpecifyFederationDataSourceSettingsPage:"specifyFederationDataSourceSettingsPage",SelectDataSourcePage:"selectDataSourcePage"},Pa={SpecifyJsonConnectionPage:va.SpecifyJsonConnectionPage,ChooseJsonSchemaPage:va.ChooseJsonSchemaPage,ChooseJsonSourcePage:va.ChooseJsonSourcePage,ChooseSqlConnectionPage:Sa.ChooseConnectionPage,ConfigureFederatedQueriesPage:_a.FederatedQueryConfigurePage,ConfigureFederatedMasterDetailRelationshipsPage:_a.FederatedMasterDetailRelationshipsPage,ConfigureQueryPage:Sa.MultiQueryConfigurePage,ConfigureQueryParametersPage:Sa.MultiQueryConfigureParametersPage,ConfigureMasterDetailRelationshipsPage:ya.ConfigureMasterDetailRelationshipsPage},Na=(0,f.createGlobalModuleVariableFunc)(!0),wa="__loadingState",Da="__nextAction";class Ea extends A.Disposable{constructor(){super(...arguments),this.changeAlways=!1,this._onChange=()=>{}}dispose(){super.dispose(),this._onChange=()=>{}}commit(){return(0,f.createDeferred)().resolve().promise()}onChange(e){this._onChange=e}initialize(e,t){return(0,f.createDeferred)().resolve().promise()}canNext(){return!0}canFinish(){return!1}}var Ia;!function(e){e[e.NoData=0]="NoData",e[e.Sql=1]="Sql",e[e.Json=2]="Json",e[e.Object=3]="Object",e[e.Federation=4]="Federation"}(Ia||(Ia={}));class Aa{constructor(e,t,a,i,r){this.text=(0,A.getLocalization)(e,t),this.imageClassName=a,this.imageTemplateName=i,this.type=r}}class ka extends Ea{constructor(e){super(),this._dataSourceTypeOptions=e,this._itemClick=e=>{this.selectedItem(e)},this._IsSelected=e=>this.selectedItem().type===e.type,this.selectedItem=P.observable(),this.typeItems=this._createTypeItems(),this._disposables.push(this.selectedItem.subscribe((()=>this._onChange()))),this._extendCssClass=()=>{}}canNext(){return null!=this.selectedItem()}canFinish(){return!1}_goToNextPage(){this[Da]&&this[Da]()}commit(){return(0,f.createDeferred)().resolve({dataSourceType:this.selectedItem().type}).promise()}_createTypeItems(){const e=[];return this._dataSourceTypeOptions.sqlDataSourceAvailable&&e.push(new Aa("Database","DataAccessUIStringId.DSTypeSql","sqldatasource","dxrd-svg-wizard-SqlDataSource",Ia.Sql)),this._dataSourceTypeOptions.jsonDataSourceAvailable&&e.push(new Aa("JSON","DataAccessUIStringId.DSTypeJson","jsondatasource","dxrd-svg-wizard-JsonDataSource",Ia.Json)),this._dataSourceTypeOptions.objectDataSourceAvailable&&e.push(new Aa("Object","DataAccessUIStringId.DSTypeObject","objectdatasource","dxrd-svg-wizard-ObjectDataSource",Ia.Object)),this._dataSourceTypeOptions.federationDataSourceAvailable&&e.push(new Aa("Data Federation","DataAccessUIStringId.DSTypeFederation","federationdatasource","dxrd-svg-wizard-FederationDataSource",Ia.Federation)),e}initialize(e){if(this.typeItems&&0!==this.typeItems.length){const t=void 0!==e.dataSourceType?e.dataSourceType:this.typeItems[0].type;this.selectedItem((0,f.findFirstItemMatchesCondition)(this.typeItems,(e=>e.type===t)))}else this.selectedItem(null);return(0,f.createDeferred)().resolve().promise()}}function za(e,t){e.registerMetadata(ya.ChooseDataSourceTypePage,{setState:(e,t)=>{t.dataSourceType=e.dataSourceType},getState:e=>e,resetState:(e,t)=>{e.dataSourceType=t.dataSourceType},create:()=>new ka(t),description:(0,A.getLocalization)("Select the data source type.","DataAccessUIStringId.WizardPageChooseDSType"),template:"dxrd-page-choose-datasource-type"})}class $a extends A.Disposable{get sqlQuery(){return this.sqlDataSource.queries()[this._queryIndex]}set sqlQuery(e){e&&(e.parent=this.sqlDataSource),this.sqlDataSource.queries()[this._queryIndex]=e}saveCustomQueries(){const e=new A.ModelSerializer;return this.customQueries.length>0&&this.customQueries.map((t=>JSON.stringify(e.serialize(t))))}save(){return JSON.stringify((new A.ModelSerializer).serialize(this.sqlDataSource))}constructor(e,t,a){super(),this.sqlDataSourceJSON=e,this.customQueries=[],this.sqlDataSource=new vt(e?JSON.parse(e):{},void 0,a),this._disposables.push(this.sqlDataSource),t?this.sqlDataSource.queries().some(((e,a)=>e.name()===t&&(this._queryIndex=a,!0))):this._queryIndex=this.sqlDataSource.queries().length}}const Oa=(e,t,a)=>{const i=new $a(e.sqlDataSourceJSON,e.queryName,t);e.name&&i.sqlDataSource.connection.name(e.name);const r=new A.ModelSerializer;return e.customQueries&&e.customQueries.length>0&&(i.customQueries=e.customQueries.map((e=>i.sqlDataSource.createQuery(JSON.parse(e),r))),i.sqlDataSource.queries.push(...i.customQueries)),e.relations&&e.relations.length>0&&i.sqlDataSource.relations.push(...e.relations.map((e=>new mt(JSON.parse(e),r)))),i.sqlDataSource.id=a||(0,f.guid)().replace(/-/g,""),i};let Ba=Oa;const Ra=e=>{Ba=e},Ma=()=>{Ba=Oa},qa=(e,t,a)=>{const i=new fa(JSON.parse(e.federationDataSourceJSON),t);return a&&(i.id=a),e.relations&&e.relations.length>0&&i.relations.push(...e.relations.map((e=>new ma(JSON.parse(e),new A.ModelSerializer)))),i},La=(e,t,a)=>{const i=new Le({Source:e.jsonSource&&JSON.parse(e.jsonSource)||{},Schema:e.jsonScheme&&JSON.parse(e.jsonScheme)||{},"@RootElement":e.rootElement||""},void 0,t);return e.connectionName&&i.connectionName(e.connectionName),e.dataSourceName&&i.name(e.dataSourceName),i.id=a||(0,f.guid)().replace(/-/g,""),i};let Qa=La;function Va(e){Qa=e}function Wa(){Qa=La}function Fa(e,t,a){const i=new Ct;return i.setState(e),a&&(i.id=a),i}function ja(e={},t={jsonSource:""},a={},i={}){return{dataSourceType:Ia.Sql,jsonDataSourceWizard:t,sqlDataSourceWizard:e,objectDataSourceWizard:a,federationDataSourceWizard:i,dataSourceId:(0,f.guid)().replace(/-/g,"")}}class Ua extends A.Disposable{dispose(){this.onChange=null,this[wa]=null,this.page.dispose(),this._initDef&&this._initDef.reject(),this._initDef=null}resetCommitedState(){this._lastCommitedState=null}constructor(e,t,a,i){super(),this.pageId=e,this.page=t,this.template=a,this.description=i,this._isInitialized=!1,this._initDef=null,this.isChanged=!0,t.onChange&&(this.onChange=e=>t.onChange(e))}commit(){return this.page.commit().done((e=>{this.isChanged=JSON.stringify(this._lastCommitedState)!==JSON.stringify(e),this._lastCommitedState=e}))}initialize(e,t=!1,a=!1){return this._initDef&&this._initDef.reject(),this._initDef=new f.DxDeferred,!this._isInitialized||t?(this._isInitialized=!0,this.page.initialize(e,a).fail((()=>{this._isInitialized=!1,this._initDef&&this._initDef.reject()})).done((e=>{this._initDef&&this._initDef.resolve(e)}))):this._initDef.resolve(),this._initDef.promise()}}class Ha extends Ea{constructor(e,t){super(),this._getSqlConnectionsCallback=t,this._connectionStrings=P.observableArray([]),this._selectedConnectionString=P.observableArray([]),this._connectionStrings=e,this._disposables.push(this._selectedConnectionString.subscribe((()=>this._onChange())))}initialize(e){const t=t=>{if(1===t.length)this._selectedConnectionString([t[0]]);else{const a=(0,f.getFirstItemByPropertyValue)(t,"name",e.name)||t[0];this._selectedConnectionString(a?[a]:[])}};if(this._getSqlConnectionsCallback){const e=(0,f.createDeferred)();return this._getSqlConnectionsCallback().done((a=>{this._connectionStrings(a),t(a),e.resolve(a)})).fail((()=>{e.reject()})),e.promise()}const a=this._connectionStrings();return t(a),(0,f.createDeferred)().resolve().promise()}canNext(){return 0!==this._selectedConnectionString().length}commit(){const e=(0,f.createDeferred)();return this._selectedConnectionString()[0]?e.resolve({name:this._selectedConnectionString()[0].name}):e.resolve(),e.promise()}}function Ja(e,t,a){e.registerMetadata(Sa.ChooseConnectionPage,{create:()=>new Ha(t,a),setState:(e,t)=>{t.name=e.name},getState:e=>e.sqlDataSourceWizard,resetState:(e,t)=>{e.name=t.name},template:"dxrd-page-connectionstring",description:(0,f.localizeWithUpdateLocalizationMethod)("Choose a data connection")||(0,A.getLocalization)("Choose a data connection.","AnalyticsCoreStringId.SqlDSWizard_PageChooseConnection")})}class Ga extends A.Disposable{switchPropertyType(){this.isExpression()?(this.type(this._parameter.resultType),this._parameter.value=this.value()):(this.type(f.ExpressionType),this._parameter.value=this.expression.value())}isExpression(){return(0,f.IsDataAccessExpression)(this.type())}_generateInfo(e,t="value"){return(0,f.extend)({},{displayName:this._parameter.displayName,propertyName:t,modelName:t,editorOptions:{}},e)}_initGetInfo(e){const t=[this._generateInfo({editor:(0,f.getEditorType)(this._parameter.resultType),from:t=>P.observable((e.valueConverter||(e=>e))(t)),defaultVal:!this.isExpression()&&this._parameter.value||e.defaultValue}),this._generateInfo({editor:{header:"dx-objectdatasource-expression-popup"},from:e=>({value:P.observable(e),itemsProvider:this._itemsProvider}),defaultVal:this.isExpression()&&this._parameter.value||""},"expression")];this._disposables.push({dispose:()=>this.getInfo=void 0}),this.getInfo=()=>t}_patchOriginalParameterValue(e){const t=this._parameter.resultType;e&&(0,A.isDateType)(this._parameter.resultType)&&e instanceof Date?this._parameter.value=(0,A.serializeParameterDate)(e,t):this._parameter.value=e}_afterInitialize(){this.isExpression()||this._patchOriginalParameterValue(this.value())}_subscribeProperties(){this._disposables.push(this.type.subscribe((e=>{this._parameter.type=e})),this.expression.value.subscribe((e=>{this.isExpression()&&this._patchOriginalParameterValue(e)})),this.value.subscribe((e=>{!this.isExpression()&&this._patchOriginalParameterValue(e)})))}isPropertyVisible(e){return this.isExpression()?"expression"===e:"value"===e}constructor(e,t){super(),this._parameter=e,this._itemsProvider=t,this.type=P.observable(""),this.type(this._parameter.type);const a=ct._getTypeValue(this._parameter.resultType);this._initGetInfo(a),(new A.ModelSerializer).deserialize(this,{}),this._subscribeProperties(),this._afterInitialize()}}class Xa{_doWithModel(e){const t=this._editor._get("_model");if(t instanceof Ga)return e(t)}constructor(e){this._editor=e,this.imageTemplateName="dx-objectdatasource-expression"}switchEditors(){this._doWithModel((e=>e.switchPropertyType()))}isExpression(){return this._doWithModel((e=>e.isExpression()))}}class Ya extends x.ObjectProperties{constructor(e){super(P.observable(e)),this.createEditorAddOn=e=>{const t=(0,x.unwrapEditor)(e);return{data:new Xa(t),templateName:"dx-wizard-menu-box-editorswitch"}}}}class Ka extends A.Disposable{constructor(e,t){super(),this.displayName=e.displayName,this._grids=e.parameters.map((e=>{const a=new Ga(e,t),i=new Ya(a);return this._disposables.push(a),this._disposables.push(i),i}))}}class Za extends A.Disposable{_updateParameters(e,t){this[e]()&&this[e]().dispose(),t&&t.parameters.length>0?this[e](new Ka(t,this._itemsProvider)):this[e](null)}constructor(e){super(),this._itemsProvider=e,this._ctorParametersObject=P.observable(),this._dataMemberParametersObject=P.observable(),this.hasParameters=()=>this._dataMemberParametersObject()||this._ctorParametersObject()}updateCtorParameters(e){this._updateParameters("_ctorParametersObject",e)}updateMethodParameters(e){this._updateParameters("_dataMemberParametersObject",e)}}class ei extends Za{constructor(e,t,a){super(a),this._disposables.push(e.subscribe((e=>{this.updateCtorParameters(e)}))),this._disposables.push(t.subscribe((e=>{this.updateMethodParameters(e[0])})))}}class ti extends Ea{constructor(e){super(),this._objectDataSource=new Ct,this._disposables.push(this._chooseObjectParameters=new Za(e))}canNext(){return!1}canFinish(){return!0}commit(){return(0,f.createDeferred)().resolve({selectedType:this._objectDataSource.selectedType,ctor:this._objectDataSource.ctor,dataMember:this._objectDataSource.dataMember,dataSourceName:this._objectDataSource.name()}).promise()}initialize(e){return this._objectDataSource.setState(e),this._chooseObjectParameters.updateCtorParameters(this._objectDataSource.ctor),this._chooseObjectParameters.updateMethodParameters(this._objectDataSource.dataMember),(0,f.createDeferred)().resolve().promise()}}function ai(e,t){e.registerMetadata(Ca.ConfigureParametersPage,{setState:(e,t)=>{t.ctor=e.ctor,t.dataMember=e.dataMember,t.selectedType=e.selectedType},getState:e=>e.objectDataSourceWizard,resetState:(e,t)=>{e.dataMember=t.dataMember},create:()=>new ti(t&&t()),description:(0,A.getLocalization)("Configure constructor parameters and/or method parameters.","AnalyticsCoreStringId.ObjectDSWizard_ConfigureParameters_Description"),template:"dx-objectdatasource-configureparameters-page"})}class ii{constructor(e){const t=ne(e,(e=>new Et(e)));this.types=t.sort(((e,t)=>e.displayName.localeCompare(t.displayName)))}}function ri(e,t){const a=new f.DxDeferred;return e.getObjectTypeDescriptions(t).done((e=>{try{const t=new ii(e.objectDataSourceInfoJson);a.resolve(t)}finally{"pending"===a.state()&&a.reject()}})).fail((e=>{(0,f.ShowMessage)((0,f.formatUnicorn)((0,A.getLocalization)("Schema loading failed. {0}","DxDesignerStringId.Error_SchemaLoadingFailed"),(0,f.getErrorMessage)(e))),a.reject()})),a.promise()}class si extends A.Disposable{constructor(e=new fe){super(),this._requestWrapper=e,this.getItems=e=>{const t=new f.DxDeferred;return this.getObjectTypeDescriptions().done((a=>{t.resolve(this.getSchemaByPath(e,a))})).fail(t.reject),t.promise()}}dispose(){this._objectTypeDescriptionsPromise=null}getSchemaByPath(e,t){if(e.fullPath){let a=[];for(let i=0;i<e.pathParts.length;i++){const r=(t.types||[]).filter((t=>t.name==e.pathParts[i]))[0];if(!r)return[];a=r.ctors}return a.map((e=>(0,f.extend)({},e,{isList:!1,specifics:"default"})))}return t.types.map((e=>(0,f.extend)({},e,{isList:!0,specifics:"List"})))}getObjectTypeDescriptions(e=""){return this._objectTypeDescriptionsPromise&&"rejected"!==this._objectTypeDescriptionsPromise.state()||(this._objectTypeDescriptionsPromise=ri(this._requestWrapper,e)),this._objectTypeDescriptionsPromise}}class oi extends it.TreeListController{canSelect(e){return!0}}class ni extends A.Disposable{constructor(e,t){super(),this.types=e,this.selectedType=P.observable(),this.selectedCtor=P.observable(),this.selectedPath=P.observable(""),this._scrollViewHeight="calc(100% - 36px)",this.availableTypesTreelistModel={expandRootItems:!0,itemsProvider:t,selectedPath:this.selectedPath,treeListController:new oi},this._disposables.push(this.selectedPath.subscribe((t=>{e().forEach((e=>{e.name==t?(this.selectedType(e),null==this.selectedCtor()?this.selectedCtor.valueHasMutated():this.selectedCtor(null)):e.ctors.forEach((a=>{e.name.concat(".").concat(a.name)==t&&(this.selectedType(e),this.selectedCtor(a))}))}))})))}}class di extends Ea{constructor(e=new fe){super(),this._requestWrapper=e,this._objectDataSource=new Ct,this._types=P.observableArray([]),this._disposables.push(this._provider=new si(this._requestWrapper),this._chooseObjectType=new ni(this._types,this._provider))}canNext(){return!!this._selectedTypeName}canFinish(){return!1}commit(){return(0,f.createDeferred)().resolve({selectedType:this._selectedTypeName,selectedObjectType:this._chooseObjectType.selectedType(),ctor:this._chooseObjectType.selectedCtor()}).promise()}initialize(e){return this._objectDataSource.setState(e),this._provider.getObjectTypeDescriptions(e.context).done((e=>{e.types.forEach((e=>e.members.splice(0,0,wt.empty()))),this._types(e.types||[]),e.types.length>0&&this._chooseObjectType.selectedPath(e.types[0].name)})),(0,f.createDeferred)().resolve().promise()}get _selectedTypeName(){return this._chooseObjectType.selectedType()?this._chooseObjectType.selectedType().name:null}}function li(e,t){e.registerMetadata(Ca.ChooseTypesPage,{setState:(e,t)=>{t.selectedType=e.selectedType,t.selectedObjectType=e.selectedObjectType,t.ctor=e.ctor},getState:e=>e.objectDataSourceWizard,resetState:(e,t)=>{e.selectedType=t.selectedType,e.selectedObjectType=t.selectedObjectType,e.ctor=t.ctor},create:()=>new di(t.requestWrapper),description:(0,A.getLocalization)("Choose the type and its constructor.","AnalyticsCoreStringId.ObjectDSWizard_ChooseType_Description"),template:"dxrd-page-objectdatasource-types"})}class ci extends A.Disposable{constructor(e,t){super(),this.dataMembers=P.observableArray([]),this.selectedDataMembers=P.observableArray([]);let a=e()&&e().name;this._disposables.push(t.subscribe((t=>{e().updateMembers(t),a!=e().name&&(this.dataMembers(e().members),this.selectedDataMembers([]),a=e().name),this.coerceSelection()})))}coerceSelection(){const e=this.selectedDataMembers()[0],t=e&&e.isAvailable()?e:this.dataMembers().filter((e=>e.isAvailable()))[0];this.selectedDataMembers([t])}}class ui extends Ea{constructor(e=new fe){super(),this._requestWrapper=e,this._objectDataSource=new Ct,this._type=P.observable(),this._ctor=P.observable(),this._disposables.push(this._chooseObjectDataMember=new ci(this._type,this._ctor))}initialize(e){return this._objectDataSource.setState(e),this._type(e.selectedObjectType),this._ctor(e.ctor),(0,f.createDeferred)().resolve().promise()}canNext(){return!!this._needParametersPage}canFinish(){return!this._needParametersPage}commit(){return(0,f.createDeferred)().resolve({selectedType:this._type().name,ctor:this._ctor(),dataMember:!this._selectedDataMember||this._selectedDataMember.isEntireObject()?void 0:this._selectedDataMember}).promise()}get _selectedDataMember(){const e=this._chooseObjectDataMember.selectedDataMembers();return e?e[0]:null}get _needParametersPage(){return!!this._selectedDataMember&&this._selectedDataMember.parameters.length>0||!!this._ctor()&&this._ctor().parameters.length>0}}function pi(e,t){e.registerMetadata(Ca.ChooseDataMembersPage,{setState:(e,t)=>{t.selectedType=e.selectedType,t.ctor=e.ctor,t.dataMember=e.dataMember},getState:e=>e.objectDataSourceWizard,resetState:(e,t)=>{e.dataMember=t.dataMember},create:()=>new ui(t.requestWrapper),description:(0,A.getLocalization)("Choose the entire object or a data member to bind.","AnalyticsCoreStringId.ObjectDSWizard_ChooseDataMember_Description"),template:"dxrd-page-objectdatasource-datamembers"})}const hi=DevExpress.Analytics.Serializer.Native;var mi,gi=function(e,t,a,i){var r,s=arguments.length,o=s<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,a):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)o=Reflect.decorate(e,t,a,i);else for(var n=e.length-1;n>=0;n--)(r=e[n])&&(o=(s<3?r(o):s>3?r(t,a,o):r(t,a))||o);return s>3&&o&&Object.defineProperty(t,a,o),o};!function(e){e[e.Left=1]="Left",e[e.TopLeft=2]="TopLeft",e[e.BottomLeft=3]="BottomLeft",e[e.Right=4]="Right",e[e.TopRight=5]="TopRight",e[e.BottomRight=6]="BottomRight",e[e.Top=7]="Top",e[e.Bottom=8]="Bottom"}(mi||(mi={}));class xi extends it.TreeListItemViewModel{_getTemplateName(){return this._getCustomizedTemplateName(!0)}_getCustomizedTemplateName(e){return e?"dxd-custom-query-treelisitem":super._getTemplateName()}updateViewModel(e){super.updateViewModel(e);this.getViewModel().queryName=this.queryName}createViewModel(){return(0,hi.createViewModelGenerator)(super.createViewModel()).generateProperty("queryName",this.queryName).generateProperty("queryNameHasChanged",(e=>this.queryName=e.value)).getViewModel()}onPropertyChanged(e){if(super.onPropertyChanged(e),"data"===e.propertyName&&(this.queryName=this.data?.name||""),"queryName"===e.propertyName){const t=e,a=this.data;a&&a.name!==t.newValue&&(a.name=t.newValue,a._afterCheckToggled&&a._afterCheckToggled(a))}}}gi([(0,hi.mutable)("")],xi.prototype,"queryName",void 0);class fi extends it.KoTreeListItemFactory{createItem(e,t,a,i,r){return"queries"===t[0]?new xi((0,it.wrapTreeListOptionsWithKo)(e),t,a,i,r):super.createItem(e,t,a,i,r)}}function bi(e,t=30,a=!0){const i="inherit",r="-"+t/2+"px",s=e=>e+"%";return e?{top:e===mi.Left||e===mi.Right||e===mi.Top||e===mi.TopLeft||e===mi.TopRight?r:i,bottom:e===mi.Left||e===mi.Right||e===mi.Bottom||e===mi.BottomLeft||e===mi.BottomRight?r:i,left:e===mi.Top||e===mi.Bottom||e===mi.Left||e===mi.TopLeft||e===mi.BottomLeft?r:i,right:e===mi.Top||e===mi.Bottom||e===mi.Right||e===mi.TopRight||e===mi.BottomRight?r:i,width:e===mi.Top||e===mi.Bottom?i:s(50),height:e===mi.Left||e===mi.Right?i:s(50),display:a?"block":"none"}:{top:r,bottom:r,left:r,right:r,width:i,height:i,display:a?"block":"none"}}function yi(e,t,a){return e().forEach((e=>t(e,a))),e.subscribe((e=>{e.forEach((e=>{"added"===e.status?t(e.value,a):"deleted"===e.status&&e.value.dispose&&e.value.dispose()})),a()}),null,"arrayChange")}function Si(e,t){const a=[];return e.forEach((e=>{e&&e.subscribe&&a.push(e.subscribe((e=>t(e))))})),a}function _i(e,t,a){return t(e(),a),e.subscribe((e=>{t(e,a),a()}))}function vi(e,t){return{page:e.page,pageId:e.pageId,wizard:t,state:t.stateManager.getPageState(e.pageId)}}function Ci(e,t){return{page:e.page,pageId:e.pageId,wizard:t}}function Ti(e){return[e.jsonDataSourceAvailable,e.sqlDataSourceAvailable,e.objectDataSourceAvailable,e.federationDataSourceAvailable].filter((e=>!!e)).length>1}class Pi extends Ea{constructor(e,t=!0,a){super(),this.items=e,this._getJsonConnectionsCallback=a,this.selectedItems=P.observableArray([]),this.operations=[{text:this.existingOperationText,createNew:!1},{text:this.createNewOperationText,createNew:!0}],this.selectedOperation=P.observable(this.operations[0]),this._createNew=P.pureComputed((()=>this.selectedOperation().createNew)),this.canCreateNew=P.observable(t),this._disposables.push(...Si([this.selectedOperation,this.selectedItems],(()=>this._onChange())))}canNext(){return 0!==this.selectedItems().length||this.selectedOperation().createNew}initialize(e){if(this._getJsonConnectionsCallback){const t=(0,f.createDeferred)();return this._getJsonConnectionsCallback().done((a=>{this.items(a);const i=this._getSelectedItem(e);this.selectedItems(i?[i]:[]),t.resolve(a)})).fail((()=>{t.reject()})),t.promise()}const t=this._getSelectedItem(e);return this.selectedItems(t?[t]:[]),(0,f.createDeferred)().resolve(this).promise()}_displayExpr(e){return e.description||e.name}_getSelectedItem(e){return this.items()[0]}onDblClick(){this[Da]&&this[Da]()}get createNewOperationText(){return(0,A.getLocalization)("No, I'd like to create a new data source","AnalyticsCoreStringId.Wizard_CreateNewDataSource")}get existingOperationText(){return(0,A.getLocalization)("Yes, let me choose an existing data source from the list","AnalyticsCoreStringId.Wizard_ChooseDataSourceFromList")}}class Ni extends Pi{commit(){return(0,f.createDeferred)().resolve({predefinedDataSourceName:this.selectedOperation().createNew?null:this.selectedItems()[0].name}).promise()}_getSelectedItem(e){const t=this.items()||[];return e.predefinedDataSourceName?t.filter((t=>t.name===e.predefinedDataSourceName))[0]:0===t.length?null:t[0]}canNext(){return this.selectedOperation().createNew}canFinish(){return!this.selectedOperation().createNew}}function wi(e,t){e.registerMetadata(ya.ChoosePredefinedDataSourcePage,{setState:(e,t)=>{t.predefinedDataSourceName=e.predefinedDataSourceName},getState:e=>e,resetState:(e,t)=>{e.predefinedDataSourceName=null},create:()=>new Ni(t.predefinedDataSources,t.canCreateDataSource),alwaysShowTitle:!0,template:"dxrd-page-selectitems",description:(0,f.getLocalization)("Do you want to use an existing data source?","AnalyticsCoreStringId.Wizard_UseExisting_DataSource")+" "+(0,f.getLocalization)("The Wizard assigns the selected or a newly created data source to the report.","AnalyticsCoreStringId.Wizard_DataSourceAssignment_Description")})}class Di extends Pi{commit(){return(0,f.createDeferred)().resolve({connectionName:this.selectedOperation().createNew?null:this.selectedItems()[0].name}).promise()}_getSelectedItem(e){return(0,f.getFirstItemByPropertyValue)(this.items(),"name",e.connectionName)||super._getSelectedItem()}get createNewOperationText(){return(0,A.getLocalization)("No, I'd like to create a new data connection","AnalyticsCoreStringId.JsonDSWizard_CreateNewConnection")}get existingOperationText(){return(0,A.getLocalization)("Yes, let me choose an existing data connection from the list","AnalyticsCoreStringId.JsonDSWizard_UseExistingConnection")}}function Ei(e,t){e.registerMetadata(va.ChooseConnectionPage,{create:()=>new Di(t.connectionStrings&&t.connectionStrings.json,t.allowCreateNewJsonConnection,t.getJsonConnectionStrings),description:t.allowCreateNewJsonConnection?(0,A.getLocalization)("Do you want to use an existing data connection?","AnalyticsCoreStringId.JsonDSWizard_ChooseConnection_Description"):(0,A.getLocalization)("Choose a data connection.","AnalyticsCoreStringId.SqlDSWizard_PageChooseConnection"),getState:e=>e.jsonDataSourceWizard,setState:(e,t)=>t.connectionName=e.connectionName,resetState:(e,t)=>{e.connectionName=t.connectionName},template:"dxrd-page-selectitems"})}const Ii={PathParameter:"pathParameters",QueryParameter:"queryParameters",Header:"headers"};function Ai(e,t,a){const i=e||(0,A.getLocalization)("The value cannot be empty","AnalyticsCoreStringId.ParametersPanel_DateTimeValueValidationError");if(!t)return i;let r=a?(0,f.formatUnicorn)("{0}. {1}",t,a):t;return(0,A._stringEndsWith)(r,":")||(r+=":"),(0,f.formatUnicorn)("{0} {1}",r,i)}class ki extends x.Editor{constructor(e,t,a,i){super(e,t,a,i),this.aceEditorHasErrors=P.observable(!1),this.aceAvailable=(0,it.aceAvailable)(),this.editorContainer=P.observable(),this.languageHelper={getLanguageMode:()=>"ace/mode/json",createCompleters:()=>[]},this.aceOptions={showLineNumbers:!1,highlightActiveLine:!1,showPrintMargin:!1,enableBasicAutocompletion:!0,enableLiveAutocompletion:!0},this.isValid=P.computed((()=>this._model()&&this._model().isValid())),this.additionalOptions={onChangeAnnotation:e=>{const t=e&&e.getAnnotations()||[];this._model()&&this._model().aceEditorHasErrors&&this._model().aceEditorHasErrors(t.filter((e=>"error"===e.type||"warning"===e.type)).length>0)},onBlur:()=>{const e=this.editorContainer();e&&this.value(e.getValue())}},this.jsonStringValidationRules=[{type:"custom",reevaluate:!0,validationCallback:e=>this.isValid(),get message(){return Ai((0,A.getLocalization)("The value cannot be empty and should have a valid format.","AnalyticsCoreStringId.ValueIsRequiredOrInvalidFormat_Error"),(0,A.getLocalization)("JSON String:","DataAccessUIStringId.WizardPageChooseJsonSource_Custom"))}}]}uploadFile(e){e&&e.event&&(e.event.stopPropagation(),e.event.preventDefault()),(0,f.uploadFile)({accept:".json,.txt"}).done((e=>{const t=(0,f.base64DecodeUnicode)(e.content);this.value(t)}))}getUploadTitle(){return(0,A.getLocalization)("Upload JSON File","AnalyticsCoreStringId.UploadJsonFile_Title")}}class zi extends A.Disposable{dispose(){this._validationSummary&&this._validationSummary.dispose(),this._validationGroup&&this._validationGroup.dispose(),this._validationSummary=null,this._validationGroup=null,super.dispose()}_onValidationGroupInitialized(e){this._validationGroup=e.component}_onValidationGroupDisposing(e){this._validationGroup=null}_onValidationSummaryInitialized(e){this._validationSummary=e.component}_onValidationSummaryDisposing(e){this._validationSummary=null}_repaintSummary(){this._validationSummary&&this._validationSummary.repaint()}_validate(){this._validationSummary&&this._validationGroup&&this._validationGroup.validate()}constructor(){super(),this._validationGroup=null,this._validationSummary=null,this.validationGroup={onInitialized:e=>this._onValidationGroupInitialized(e),onDisposing:e=>this._onValidationGroupDisposing(e),validate:()=>this._validate()},this.validationSummary={onInitialized:e=>this._onValidationSummaryInitialized(e),onDisposing:e=>this._onValidationSummaryDisposing(e)},this._disposables.push(this.grid=new x.ObjectProperties(P.observable(this)))}}class $i extends zi{onChange(e){let t=null;const a=()=>{e(),clearTimeout(t),t=setTimeout((()=>this._validate()),1)};this._disposables.push(this.stringSource.subscribe((e=>a())))}_isJsonSourceValid(e){if(!e)return!1;let t=!0;try{JSON.parse(e)}catch(e){t=!1}return t}isEmpty(){return!this.stringSource()}reset(){this.stringSource("")}setValue(e){this.stringSource(e.source.json())}getInfo(){return[{propertyName:"stringSource",defaultVal:"",displayName:"JSON String",editor:{header:"dx-jsonwizard-jsonstring-editor",editorType:ki,custom:"dx-property-json-string-editor"}}]}applySettings(e){e.source.uri(void 0),e.source.json(this.stringSource()),e.source.authenticationInfo=new Ee({}),e.source.headers([]),e.source.queryParameters([]),e.source.pathParameters([])}constructor(){super(),this._validatorsReady=P.observable(!1),this.isValid=P.pureComputed((()=>{const e=this._isJsonSourceValid(this.stringSource()),t=this.aceEditorHasErrors();return e&&!t})),this.validationGroup=null,this.validationSummary=null,this.stringSource=P.observable(""),this.aceEditorHasErrors=P.observable(!1),this.cssClass={"dxrd-wizard-json-string-source-grid":!0},this._disposables.push(this.grid=new x.ObjectProperties(P.observable(this)))}}class Oi extends zi{_getPatchedParameter(e){return e.nameValidationRules=[{type:"required",get message(){return Ai(null,(0,A.getLocalization)("Parameters","AnalyticsCoreStringId.QueryBuilder_Parameters"),this._collectionItemNamePlaceholder)}}],e.itemsProvider=this._itemsProvider,e}_validateUriSource(){const e=(0,A.getLocalization)("Invalid URI.","AnalyticsCoreStringId.ReportDesigner_Wizard_JsonSource_UriValidationError"),t=(0,A.getLocalization)("Web Service Endpoint (URI):","DataAccessUIStringId.WizardPageChooseJsonSource_URI");this._isUriValid(!1),this._lastValidateDeferred&&this._lastValidateDeferred.reject();const a=new f.DxDeferred;this._lastValidateDeferred=a;try{const i=new Le({}),r=this._getSerializedUriSource(i);this._lastValidatedJsonSourceJSON=r;const s=i=>{if(a.state&&"rejected"===a.state())return;this._isUriValid(i.isUriValid);const r=Ai((i.isUriValid?"":i&&i.faultMessage)||e,t);a.resolve({isUriValid:i.isUriValid,faultMessage:r})};this._requestWrapper.validateJsonUri(i).done(s).fail(((e={})=>{e.isValid=!1,s(e)}))}catch(e){this._isUriValid(!1)}return a.promise()}_isCollectionValid(e){return!this[e]().length||this[e]().every((e=>e.name()))}_isParametersValid(){return this._isCollectionValid("parameters")}_isBasicHttpAuthValid(){return!this.basicHttpAuth.password()||!!this.basicHttpAuth.userName()}_getSourceUriInfo(){const e={propertyName:"sourceUri",displayName:"Web Service Endpoint (URI):",localizationId:"DataAccessUIStringId.WizardPageChooseJsonSource_URI",defaultVal:"",editor:x.editorTemplates.getEditor("text"),validatorOptions:null,isRequired:!0,editorOptions:{elementAttr:{title:this.sourceUri}}},t=this;return e.validatorOptions={onInitialized:e=>{this._sourceUriValidatorsReady(!0)},onDisposed:()=>{this._sourceUriValidatorsReady(!1)},validationRules:[{type:"custom",assignValueFirst:!0,isDeferred:P.pureComputed((()=>this._noEmptyProperties())),get message(){return t._lastValidationMessage()},validationCallback:this._sourceUriValidationCallback}]},e}_getBasicHttpAuthInfo(){const e={propertyName:"userName",displayName:"Username:",localizationId:"DataAccessUIStringId.WizardPageConfigureJsonConnection_UsernameText",editor:x.editorTemplates.getEditor("text"),validatorOptions:void 0},t={propertyName:"basicHttpAuth",displayName:"Basic HTTP Authentication",localizationId:"DataAccessUIStringId.WizardPageConfigureJsonConnection_BasicHttpAuthText",info:[e,(0,f.createPasswordSerializationInfo)({propertyName:"password",displayName:"Password:",localizationId:"DataAccessUIStringId.WizardPageConfigureJsonConnection_PasswordText"},!1)],editor:x.editorTemplates.getEditor("objecteditor")};return e.validatorOptions={onInitialized:e=>{const t=e&&e.component;this._authNameValidatorInstance&&this._authNameValidatorInstance!=t&&this._authNameValidatorInstance.dispose(),this._authNameValidatorInstance=t,this._basicAuthValidatorsReady(!0)},onDisposed:()=>{this._authNameValidatorInstance=null},validationRules:[{type:"custom",reevaluate:!0,assignValueFirst:!0,get message(){return Ai(null,(0,A.getLocalization)(t.displayName,t.localizationId),(0,A.getLocalization)(e.displayName,e.localizationId))},validationCallback:e=>this._isBasicHttpAuthValid()}]},t}_getParametersInfo(){return{propertyName:"parameters",displayName:"Parameters",localizationId:"AnalyticsCoreStringId.QueryBuilder_Parameters",array:!0,addHandler:()=>{const e=(0,f.getUniqueNameForNamedObjectsArray)(this.parameters(),"parameter");return this._getPatchedParameter(Ae.from({"@Name":e}))},editor:x.editorTemplates.getEditor("commonCollection"),editorOptions:null,template:"#dx-jsonwizard-parametercollection"}}_getResultUriInfo(){return{propertyName:"resultUri",displayName:"Resulting URI:",localizationId:"DataAccessUIStringId.WizardPageChooseJsonSource_ResultingUri",defaultVal:"",editor:x.editorTemplates.getEditor("text"),editorOptions:{readOnly:!0,elementAttr:{title:this.resultUri}}}}_getResultUri(){if(!this.sourceUri()||!this._isUriValid()||!this._isParametersValid())return null;if(this.sourceUri()&&0===this.parameters().length)return this.sourceUri();const e=this.sourceUri().split("?");let t=e[0];const a=e[1];return t=this._appendPathSegmentsToUri(e[0]),this._appendQuerySegmentsToUri(t,a)}_appendPathSegmentsToUri(e){return this.parameters().filter((e=>"PathParameter"===e.itemType())).map((e=>"{"+e.name()+"}")).forEach((t=>{e=e.replace(/[\/]+$/g,"")+"/"+t.replace(/^[\/]+/g,"")})),e}_appendQuerySegmentsToUri(e,t){let a=t||"";return this.parameters().filter((e=>"QueryParameter"===e.itemType())).map((e=>e.name()+"={?}")).forEach((e=>{a&&(a+="&"),a+=e})),a.length>0&&(e+="?"+a),e}constructor(e,t){super(),this._requestWrapper=e,this._itemsProvider=t,this._isUriValid=P.observable(!1),this._lastValidatedJsonSourceJSON="",this._authNameValidatorInstance=null,this._isInitUri=!0,this._collectionItemNamePlaceholder=(0,A.getLocalization)("Name","AnalyticsCoreStringId.CollectionEditor_Name_Placeholder"),this._lastValidateDeferred=null,this._sourceUriValidatorsReady=P.observable(!0),this._basicAuthValidatorsReady=P.observable(!1),this._validationRequested=P.observable(!1).extend({deferred:!0}),this._noEmptyProperties=P.pureComputed((()=>{const e=this._isBasicHttpAuthValid(),t=this._isParametersValid(),a=!!this.sourceUri();return e&&t&&a})),this._lastValidationMessage=P.observable(),this._getSerializedUriSource=(e=new Le({}))=>(this.applySettings(e),JSON.stringify(e.source.serialize(!0))),this._sourceUriValidationCallback=e=>{if(!this.sourceUri())return this._isInitUri?(this._isInitUri=!1,!0):(e.rule.message=Ai(null,(0,A.getLocalization)("Web Service Endpoint (URI):","DataAccessUIStringId.WizardPageChooseJsonSource_URI")),!1);let t=!1;this._isInitUri=!1;return this._getSerializedUriSource()==this._lastValidatedJsonSourceJSON?!this._lastValidationMessage():(this._lastValidationMessage()||this._lastValidationMessage((0,A.getLocalization)("Validation...","AnalyticsCoreStringId.Validation")),this._validateUriSource().done((a=>{t=e.rule.isValid=a.isUriValid,a.faultMessage&&(e.rule.message=a.faultMessage),this._lastValidationMessage(t?null:a.faultMessage),e.validator.validate(),setTimeout((()=>this._repaintSummary()),1)})),t)},this.isValid=P.pureComputed((()=>{const e=this._noEmptyProperties(),t=this._isUriValid();return e&&t})),this._validatorsReady=P.pureComputed({read:()=>{const e=this._sourceUriValidatorsReady(),t=!this.basicHttpAuth.password()&&!this.basicHttpAuth.userName(),a=this._basicAuthValidatorsReady();return e&&(a||t)},write:e=>{this._basicAuthValidatorsReady(e)}}),this.sourceUri=P.observable(""),this.basicHttpAuth={password:P.observable(""),userName:P.observable("")},this.parameters=P.observableArray([]),this._disposables.push(this.resultUri=P.pureComputed((()=>this._getResultUri()))),this._disposables.push(this.grid=new x.ObjectProperties(P.observable(this))),this._disposables.push(P.computed((()=>{const e=this._validatorsReady(),t=this._validationRequested();e&&t&&this._validate()}))),this._disposables.push(this._validatorsReady)}_onValidationSummaryInitialized(e){super._onValidationSummaryInitialized(e),this._isInitUri=!0,setTimeout((()=>{this._validate()}),1)}_applyParametersToSource(e){const t=this.parameters().reduce(((e,t)=>({...e,[t.itemType()]:[...e[t.itemType()]||[],t]})),{});["PathParameter","QueryParameter","Header"].forEach((a=>{t[a]&&e.source[Ii[a]](t[a])}))}applySettings(e){e.source.uri(this.sourceUri()),e.source.json(void 0),e.source.authenticationInfo.password(this.basicHttpAuth.password()),e.source.authenticationInfo.userName(this.basicHttpAuth.userName()),this._applyParametersToSource(e)}getInfo(){return[this._getSourceUriInfo(),this._getBasicHttpAuthInfo(),this._getParametersInfo(),this._getResultUriInfo()]}reset(){this.sourceUri(""),this.basicHttpAuth.password(""),this.basicHttpAuth.userName(""),this.parameters([])}setValue(e){this.sourceUri(e.source.uri()),this.basicHttpAuth.userName(e.source.authenticationInfo.userName()),this.basicHttpAuth.password(e.source.authenticationInfo.password());const t=[].concat.apply([],[e.source.pathParameters(),e.source.queryParameters(),e.source.headers()]);this.parameters(t.map((e=>this._getPatchedParameter(e))))}dispose(){this._authNameValidatorInstance&&this._authNameValidatorInstance.dispose(),this._lastValidateDeferred&&(this._lastValidateDeferred.reject(),this._lastValidateDeferred=null),super.dispose(),this.disposeObservableArray(this.parameters)}onChange(e){let t=null;const a=()=>{e(),clearTimeout(t),t=setTimeout((()=>this._validate()),1)};this._disposables.push(...Si([this.sourceUri,this.basicHttpAuth.password,this.basicHttpAuth.userName,this.resultUri],a)),this._disposables.push(yi(this.parameters,(e=>{e._disposables.push(...Si([e.name,e.itemType,e.value],a))}),a))}isEmpty(){return!this.sourceUri()}_validate(){this._validationSummary&&this._validationGroup&&(this._validatorsReady()?(this._validationGroup.validate(),this._validationRequested(!1)):this._validationRequested(!0))}}class Bi extends Ea{_onValidationGroupInitialized(e){this.__validationGroup=e.component}_onValidationGroupDisposing(e){this.__validationGroup=null}_onValidationSummaryInitialized(e){this.__validationSummary=e.component,this.__validationGroup&&this.__validationSummary&&this._connectionName&&this._connectionName()&&setTimeout((()=>{this.__validationGroup&&this.__validationGroup.validate()}),1)}_onValidationSummaryDisposing(e){this.__validationSummary=null}constructor(e=new fe,t){super(),this._requestWrapper=e,this.__validationGroup=null,this.__validationSummary=null,this._jsonSourceTitle=(0,A.getLocalization)("JSON Source:","DataAccessUIStringId.WizardPageChooseJsonSource_SourceType"),this._jsonConnectionTitle=(0,A.getLocalization)("Connection Name:","AnalyticsCoreStringId.ReportDesigner_Wizard_Json_ConnectionName"),this._connectionNameValidationRules=[{type:"required",get message(){return Ai(null,this._jsonConnectionTitle)}}],this._connectionName=P.observable(""),this._validationGroup={onInitialized:e=>this._onValidationGroupInitialized(e),onDisposing:e=>this._onValidationGroupDisposing(e)},this._validationSummary={onInitialized:e=>this._onValidationSummaryInitialized(e),onDisposing:e=>this._onValidationSummaryDisposing(e)},this._sources=[],this._disposables.push(this._jsonStringSettings=new $i),this._jsonStringSettings.onChange((()=>this._onChange())),this._disposables.push(this._jsonUriSetting=new Oi(this._requestWrapper,t)),this._jsonUriSetting.onChange((()=>this._onChange())),this._sources=[{value:this._jsonUriSetting,displayValue:"Web Service Endpoint (URI)",localizationId:"DataAccessUIStringId.WizardPageChooseJsonSource_SourceType_Uri"},{value:this._jsonStringSettings,displayValue:"JSON String",localizationId:"DataAccessUIStringId.WizardPageChooseJsonSource_SourceType_Custom"}];const a=P.observable();this._selectedSource=P.pureComputed({read:()=>a(),write:e=>{a()!==e&&(a(e),e._validatorsReady&&e._validatorsReady(!1),setTimeout((()=>{e._validate&&e._validate()}),1),this._onChange())}}),this._selectedSource(this._sources[0].value),this._disposables.push(this._selectedSource),this._disposables.push(this._connectionName.subscribe((()=>this._onChange())))}canNext(){const e=!!this._connectionName(),t=this._selectedSource().isValid(),a=this._selectedSource().isEmpty();return e&&t&&!a}commit(){const e=new Le({});this._selectedSource().applySettings(e);const t=(new A.ModelSerializer).serialize(e);return(0,f.createDeferred)().resolve({jsonSource:JSON.stringify(t.Source),newConnectionName:this._connectionName()}).promise()}initialize(e){this.__validationGroup=null,this.__validationSummary=null;const t=Qa(e);return t.source.uri()?this._selectedSource(this._jsonUriSetting):t.source.json()&&this._selectedSource(this._jsonStringSettings),this._selectedSource().setValue(t),(0,f.createDeferred)().resolve().promise()}}function Ri(e,t,a){e.registerMetadata(va.ChooseJsonSourcePage,{setState:(e,t)=>{t.jsonSource=e.jsonSource,t.newConnectionName=e.newConnectionName},getState:e=>e.jsonDataSourceWizard,resetState:(e,t)=>{e.jsonSource=t.jsonSource},create:()=>new Bi(t,a&&a()),description:(0,A.getLocalization)("Create a data connection.","AnalyticsCoreStringId.JsonDSWizard_CreateNewConnectionPage_Description"),template:"dxrd-page-jsonsource"})}class Mi extends Di{constructor(e,t,a,i=new fe,r){super(e,t,r),this._requestWrapper=i,this._disposables.push(this._specifySourceData=new Bi(i,a)),this._specifySourceData.onChange((()=>this._onChange()))}commit(){const e=(0,f.createDeferred)();let t;return t=this._createNew()?this._specifySourceData.commit():super.commit(),t.done((t=>{e.resolve(t)})),e.promise()}canNext(){return this._createNew()?this._specifySourceData.canNext():super.canNext()}initialize(e){const t=(0,f.createDeferred)();return super.initialize(e).done((()=>{this._specifySourceData.initialize(e).done((()=>{t.resolve(this)}))})),t.promise()}}function qi(e,t,a,i,r){e.registerMetadata(va.SpecifyJsonConnectionPage,{create:()=>new Mi(t,a,i&&i(),void 0,r),description:a?(0,A.getLocalization)("Do you want to use an existing data connection?","AnalyticsCoreStringId.JsonDSWizard_ChooseConnection_Description"):(0,A.getLocalization)("Choose a data connection.","AnalyticsCoreStringId.SqlDSWizard_PageChooseConnection"),getState:e=>e.jsonDataSourceWizard,setState:(e,t)=>{t.connectionName=e.connectionName,t.jsonSource=e.jsonSource,t.newConnectionName=e.newConnectionName},resetState:(e,t)=>{e.connectionName=t.connectionName,e.jsonSource=t.jsonSource,e.newConnectionName=t.newConnectionName},template:"dxrd-page-specify-connection"})}const Li="Default";class Qi extends A.Disposable{constructor(e,t,a,i=!1,r){super(),this.name=e,this.displayName=t,this.specifics=a,this.checked=P.pureComputed((()=>this._checked())),this.isList=!1,this._checked=P.observable(i),this._afterCheckToggled=r||(()=>{})}unChecked(){return!1===this.checked()}toggleChecked(){this.setChecked(!this.checked.peek()),this._afterCheckToggled(this)}setChecked(e){this._checked(e)}}class Vi extends Qi{constructor(e,t,a,i=!1,r=null,s){super(e,t,a,i,s),this.name=e,this.displayName=t,this.specifics=a,this.hasQuery=!1,this.arguments=r}}class Wi extends Qi{constructor(e,t,a,i,r){super(e,t,a,i,r),this.countChecked=P.pureComputed((()=>{let e=0;for(let t=0;t<this.children().length;t++)if(!this.children()[t].unChecked()){if(e>1)break;e++}return e})),this.isList=!0,this.children=P.observableArray([]),this.checked=P.pureComputed({read:()=>{if(this.initialized()){let e=0,t=0;return this.children().forEach((a=>{!0===a.checked()?e++:!1!==a.checked()&&t++})),(0!==e||0!==t)&&(e===this.children.peek().length||void 0)}return this._checked()}})}initialized(){return this.children().length>0}setChecked(e){this._checked(e),this.children.peek().forEach((t=>{t.setChecked(e)}))}initializeChildren(e){this.children(e||[])}}class Fi extends Wi{constructor(e,t,a,i,r){super(e,t,a,i,r),this.countChecked=P.pureComputed((()=>{let e=0;this.hasParamsToEdit(!1);for(let t=0;t<this.children().length;t++){const a=this.children()[t];if(!a.unChecked()){if(e>1)break;e++,a.arguments&&a.arguments.length>0&&this.hasParamsToEdit(!0),"query"===a.specifics&&this.hasParamsToEdit(!0)}}return e})),this.hasParamsToEdit=P.observable(!1)}}class ji extends Fi{constructor(e,t,a,i,r,s){super(e,t,a,i,s),this.addAction={clickAction:e=>this.disableCustomSql()?this.addQuery():this.showPopover(),imageClassName:"dxrd-image-add",imageTemplateName:"dxrd-svg-operations-add",templateName:"dx-treelist-action-with-popover",text:(0,A.getLocalization)("Add query","AnalyticsCoreStringId.SqlDSWizard_AddQuery")},this.itemClickAction=e=>{this.popoverVisible(!1),e.itemData.addAction()},this.className="dx-addqueries-popover",this.popoverVisible=P.observable(!1),this.path=e,this.addQuery=()=>{r().showQbCallBack()},this.addCustomQuery=()=>{r().showQbCallBack(null,!0)},this.disableCustomSql=()=>r&&r().disableCustomSql,this.target="."+this.addAction.templateName,this.selectionDisabled=P.pureComputed((()=>!this.children().length))}getActions(e){const t=[];return 0===e.path.indexOf("queries")&&t.push(this.addAction),t}popoverListItems(){return[{name:(0,A.getLocalization)("Run Query Builder","DataAccessUIStringId.Button_QueryBuilder"),addAction:()=>this.addQuery()},{name:(0,A.getLocalization)("Write Custom SQL","AnalyticsCoreStringId.SqlDSWizard_WriteCustomSQL"),addAction:()=>this.addCustomQuery()}]}showPopover(){this.popoverVisible(!0)}}class Ui extends Vi{constructor(e,t,a,i,r,s,o,n){super(e,t,a,i,null,o),this.query=n,this.editAction={clickAction:e=>this.editQuery(),imageClassName:"dx-image-edit",imageTemplateName:"dxrd-svg-operations-edit",text:(0,A.getLocalization)("Edit query","AnalyticsCoreStringId.SqlDSWizard_EditQuery")},this.removeAction={clickAction:e=>{this.removeQuery({model:e.data})},imageClassName:"dxrd-image-recycle-bin",imageTemplateName:"dxrd-svg-operations-recycle_bin",text:(0,A.getLocalization)("Remove query","AnalyticsCoreStringId.SqlDSWizard_RemoveQuery")},this.parameters=r,this.removeQuery=e=>{e.model.unChecked()||e.model.toggleChecked(),s().deleteAction(e.model.name)},this.editQuery=e=>{s().showQbCallBack(this.name)},this.hasQuery=!0}setObservableName(e,t){["name","displayName"].forEach((a=>Object.defineProperty(this,a,{get:()=>e(),set(e){t(e)},configurable:!0})))}getActions(e){const t=[];return t.push(this.removeAction),t.push(this.editAction),t}}class Hi extends Qi{constructor(e,t,a,i,r,s,o=!1){super(e,t,a,i,s),this.visible=P.observable(!0),this.disabled=P.observable(!1),this.path=r,this.isComplex=a===Li,o&&(this.dragData={noDragable:!1})}}class Ji extends Wi{constructor(e,t,a,i,r,s){super(e,t,a,i,s),this.visible=P.observable(!0),this.path=r,this.isComplex=this.isList&&a===Li,this.checked=P.pureComputed({read:()=>{if(this.initialized()){let e=0,t=0;const a=this.children().filter((e=>e.visible()));return a.forEach((a=>{!0===a.checked()?e++:!1!==a.checked()&&t++})),(0!==e||0!==t)&&(e===a.length||void 0)}return this._checked()}})}setChecked(e){super.setChecked(!!this.visible()&&e)}}class Gi extends Ji{constructor(e,t,a,i,r,s){super(e,t,a,i,r,s),this.checked=P.pureComputed({read:()=>this._checked()})}}class Xi extends A.Disposable{_createTree(){return this._fullTreeLoaded?(new f.DxDeferred).resolve().promise():f.DxDeferred.when(this._rootItems().map((e=>this.getItems(new A.PathRequest(e.path),!0)))).always((()=>this._fullTreeLoaded=!0))}_createTreePart(e,t=new f.DxDeferred,a){if(this._fullTreeLoaded)return t.resolve().promise();if(0===e.length)return t.resolve();if(!a){const t=new f.DxDeferred;return 1===e.length?this.getItems(new A.PathRequest(e[0],e)).done((()=>t.resolve())).fail((()=>t.reject())):this._createTreePart(e.slice(1),t,[e[0]]),t}{const i=[].concat([],a,e[0]),r=new A.PathRequest(i.join("."),i);this._getParentNode(r)?this._createTreePart(e.slice(1),t,i):this.getItems(new A.PathRequest(a.join("."),a)).done((a=>{this._createTreePart(e.slice(1),t,i)})).fail((()=>t.reject()))}}_setChecked(e){e.setChecked(!0),e instanceof Ji&&e.children().forEach((e=>this._setChecked(e)))}selectAllItems(e=!0){const t=new f.DxDeferred;return this._createTree().always((()=>{e?this._rootItems().forEach((e=>e.setChecked(!0))):this._rootItems().forEach((e=>this._setChecked(e))),t.resolve()})),t.promise()}selectItemsByPath(e){const t=new f.DxDeferred,a=e.split(".");return this._createTreePart(a).done((()=>{this.getItems(new A.PathRequest(a.join("."),a)).done((e=>{e.forEach((e=>{e instanceof Qi&&e.setChecked(!0)}))})).always((()=>t.resolve()))})),t.promise()}selectItemByPath(e){const t=new f.DxDeferred,a=e.split(".");return this._createTreePart(a).done((()=>{const e=a.pop();this.getItems(new A.PathRequest(a.join("."),a)).done((t=>{const a=t.filter((t=>t.name===e))[0];a instanceof Qi&&a.setChecked(!0)})).always((()=>t.resolve()))})),t.promise()}_getParentNode(e){let t=this._rootItems().filter((t=>t.path===(e.id||e.ref)))[0];if(!t)return;let a=t.path;for(let i=1;i<e.pathParts.length;i++){if(!t)return;a+="."+e.pathParts[i],t=t.children().filter((e=>(0,f.isList)(e)&&e.path==a))[0]}return t}_getDefaultTreeNodeCheckState(e){return!1}constructor(e,t,a,i){super(),this._fullTreeLoaded=!1,this._rootItems=P.observableArray([]),this._checkedRootNodesCount=P.computed((()=>{if(!this._rootItems||0===this._rootItems().length)return 0;let e=0;for(let t=0;t<this._rootItems().length&&e<1;t++)e+=this._rootItems()[t].unChecked()?0:1;return e})),this.hasCheckedItems=P.computed((()=>!(0===this._checkedRootNodesCount()))),this.getRootItems=()=>this._rootItems(),this.getItems=(t,r=!1)=>{const s=new f.DxDeferred;return t.fullPath||0!==t.pathParts.length?e.getItems(t).done((e=>{const o=this._getParentNode(t);if(!o||!o.children)return s.reject();if(0===o.children().length){const n=[],d=[];e.forEach((e=>{const r=this._getDefaultTreeNodeCheckState(e);this.isList(e,o)?t.pathParts.length<=5&&(d.push([].concat(t.fullPath.split("."),[e.name])),n.push(a(e,r,[t.fullPath,e.name].join(".")))):n.push(i(e,r,[t.fullPath,e.name].join(".")))})),o.initializeChildren(n),r?f.DxDeferred.when(d.map((e=>this.getItems(new A.PathRequest(e.join("."),e),r)))).always((()=>s.resolve(n))):s.resolve(n)}else s.resolve(o.children())})):s.resolve(this._rootItems()),s.promise()},this._disposables.push(t.subscribe((e=>{this._fullTreeLoaded=!1,this._rootItems(e.map((e=>{const t=this._getDefaultTreeNodeCheckState(e);return a(e,t,e.name)})))}))),this._disposables.push(this.hasCheckedItems)}isList(e,t){return(0,f.isList)(e)}}class Yi extends Xi{constructor(e,t,a,i){super(e,t,a,i)}_getDefaultTreeNodeCheckState(e){return e.isSelected}getNodeByPath(e){let t=this._rootItems().filter((t=>t.path===(e.pathParts||[])[0]))[0];if(!t)return;let a=t.path;for(let i=1;i<e.pathParts.length;i++){if(!t)return;a+="."+e.pathParts[i],t=t.children().filter((e=>e.path==a))[0]}return t}}class Ki extends Ea{_clear(){this._rootItems([]),this._fieldSelectedPath(""),this._rootElementList([]),this._selectedRootElement(null),this._dataSource&&this._dataSource.jsonSchemaProvider.reset(),this._cachedState={connectionName:null,jsonSource:null}}_createFieldListCallback(){return e=>{const t=new Te({});t.nodes=[this._rootItems()[0].data];const a=this._getInnerItemsByPath(e,t);return(0,f.createDeferred)().resolve(a).promise()}}_getSchemaToDataMemberInfo(e){let t=this._dataSource.schema;for(let a=0;a<e.pathParts.length;a++)if(t=t.nodes.filter((t=>t.name()===e.pathParts[a]))[0],!t)return null;return t}_mapJsonNodesToTreelistItems(e){return(e||[]).map((e=>({name:e.name(),displayName:e.displayName||e.name(),isSelected:e.selected(),isList:e.nodes&&e.nodes.length>0,specifics:"Default"})))}_getNodesByPath(e,t){if(e.fullPath){let a=t.nodes;for(let t=0;t<e.pathParts.length;t++){const i=(a||[]).filter((a=>a.name()==e.pathParts[t]))[0];if(!i)return[];a=i.nodes}return a}return t.nodes}_getInnerItemsByPath(e,t){const a=this._getNodesByPath(e,t);return this._mapJsonNodesToTreelistItems(a)}_beginInternal(e){if(e.connectionName&&this._cachedState.connectionName===e.connectionName||e.jsonSource&&this._cachedState.jsonSource===e.jsonSource)return(0,f.createDeferred)().resolve().promise();this._clear(),this._cachedState={connectionName:e.connectionName,jsonSource:e.jsonSource};const t=this._dataSource&&this._dataSource.id;return this._dataSource=Qa(e,this._requestWrapper),t&&t!=this._dataSource.id||!this._dataSource.schema.nodes.length?this._dataSource.getSchema(this._callbacks&&this._callbacks.getParameters&&this._callbacks.getParameters()).done((e=>this._updatePage(e))):(0,f.createDeferred)().done((e=>this._updatePage(e))).resolve(this._dataSource.schema).promise()}_updatePage(e){const t=e.getRootElementPartList(this._allowObjectRootElements);if(this._rootElementList()!==t&&this._rootElementList(this._filterRootElementList(t,e)),this._dataSource.rootElement()){const e=["root",this._dataSource.rootElement()].join("."),t=this._rootElementList().filter((t=>t.fullPath===e))[0]||this._rootElementList()[0];this._selectedRootElement(t)}this._onChange()}_resetSelectionRecursive(e,t){e!==t&&(e.selected&&e.selected(!1),(e.nodes||[]).forEach((e=>this._resetSelectionRecursive(e,t))))}_mapJsonSchema(e,t){const a=this._fieldListItemsProvider().getNodeByPath(t);if(a)return e.selected(!1!==a.checked()),(e.nodes||[]).forEach((e=>{const a=t.pathParts.concat(e.name()),i=a.join("."),r={fullPath:i,path:e.name(),id:i,pathParts:a};this._mapJsonSchema(e,r)})),e}_filterRootElementList(e,t){return e}canNext(){return!1}canFinish(){return this._fieldListItemsProvider().hasCheckedItems()}constructor(e=new fe,t=!0,a){super(),this._requestWrapper=e,this._allowObjectRootElements=t,this._callbacks=a,this._rootItems=P.observableArray([]),this._fieldListItemsProvider=P.observable(null),this._fieldSelectedPath=P.observable(null),this._cachedState={connectionName:null,jsonSource:null},this._createTreeNode=(e,t,a)=>{const i=new Ji(e.name,e.displayName,e.specifics,t,a);return this._disposables.push(i.checked.subscribe((()=>this._onChange()))),i},this._createLeafTreeNode=(e,t,a)=>{const i=new Hi(e.name,e.displayName,e.specifics,t,a);return this._disposables.push(i.checked.subscribe((()=>this._onChange()))),i},this._rootElementTitle=(0,A.getLocalization)("Root element:","DataAccessUIStringId.WizardPageChooseJsonSchema_RootElement"),this._rootElementList=P.observable([]),this._selectedRootElement=P.observable(null);let i=null;this._disposables.push(this._rootElementList.subscribe((e=>{i&&i.dispose(),i=this._selectedRootElement.subscribe((e=>{if(!e)return this._rootItems([]);const t=this._getSchemaToDataMemberInfo(e);t&&this._rootItems([{name:P.unwrap(t.name),isSelected:P.unwrap(t.selected),displayName:P.unwrap(t.displayName)||P.unwrap(t.name),data:t,specifics:t.nodes.length>0?"List":"Default"}])})),this._selectedRootElement(e[0])})));const r=new f.FieldListProvider(this._createFieldListCallback(),this._rootItems);this._fieldListItemsProvider(new Yi(r,this._rootItems,this._createTreeNode,this._createLeafTreeNode)),this._disposables.push(this._fieldListItemsProvider()),this._fieldListModel={expandRootItems:!0,itemsProvider:this._fieldListItemsProvider(),selectedPath:this._fieldSelectedPath,treeListController:null,templateName:"dxrd-treelist-with-checkbox"}}commit(){if(!this._rootItems()[0])return;const e=this._rootItems()[0].data;this._resetSelectionRecursive(this._dataSource.schema,e);const t=e.name(),a={fullPath:t,path:"",id:t,pathParts:[t]};this._mapJsonSchema(this._rootItems()[0].data,a);const i=this._selectedRootElement().pathParts.slice(1).join(".");this._dataSource.rootElement(i);const r=(new A.ModelSerializer).serialize(this._dataSource);return(0,f.createDeferred)().resolve({connectionName:this._dataSource.connectionName(),dataSourceName:r["@Name"],jsonScheme:JSON.stringify(r.Schema),rootElement:r["@RootElement"]}).promise()}initialize(e){return this._beginInternal(e)}reset(){this._clear()}}function Zi(e,t,a){e.registerMetadata(va.ChooseJsonSchemaPage,{setState:(e,t)=>{t.dataSourceName=e.dataSourceName,t.jsonScheme=e.jsonScheme,t.rootElement=e.rootElement},getState:e=>e.jsonDataSourceWizard,resetState:(e,t)=>{e.dataSourceName=t.dataSourceName,e.jsonScheme=t.jsonScheme,e.rootElement=t.rootElement},create:()=>new Ki(t,void 0,a),description:(0,A.getLocalization)("Select data fields.","DataAccessUIStringId.WizardPageChooseJsonSchema"),template:"dxrd-jsondatasource-fields-page"})}function er(e){return(t,a)=>e?e(t,a):(new fe).getSelectStatement(t,a)}function tr(e){return(t,a,i)=>e?e(t,a,i):(new fe).rebuildResultSchema(t,a,i)}function ar(e){return t=>e?e(t):(new fe).getFederationResultSchema(t)}const ir={None:"None",Count:"Count",Max:"Max",Min:"Min",Avg:"Avg",Sum:"Sum",CountDistinct:"CountDistinct",AvgDistinct:"AvgDistinct",SumDistinct:"SumDistinct"},rr=[At,{propertyName:"displayType",displayName:"Type",localizationId:"DataAccessUIStringId.ParametersColumn_Type",disabled:!0,editor:It.getEditor("text")},{propertyName:"alias",displayName:"Alias",localizationId:"DataAccessUIStringId.QueryBuilderColumns_Alias",editor:It.getEditor("text")},$t,{propertyName:"sortingType",displayName:"Sort Type",editor:It.getEditor("combobox"),defaultVal:"Unsorted",valuesArray:[{value:"Unsorted",displayValue:"Unsorted",localizationId:"DataAccessUIStringId.SortingTypeNone"},{value:"Ascending",displayValue:"Ascending",localizationId:"DataAccessUIStringId.SortingTypeAscending"},{value:"Descending",displayValue:"Descending",localizationId:"DataAccessUIStringId.SortingTypeDescending"}],localizationId:"AnalyticsCoreStringId.QueryBuilder_SortType"},{propertyName:"sortOrder",displayName:"Sort Order",editor:It.getEditor("numeric"),localizationId:"DataAccessUIStringId.QueryBuilderColumns_SortOrder"},{propertyName:"groupBy",displayName:"Group By",editor:It.getEditor("bool"),defaultVal:!1,localizationId:"DataAccessUIStringId.QueryBuilderColumns_GroupBy"},{propertyName:"aggregate",displayName:"Aggregate",editor:It.getEditor("comboboxUndo"),values:ir,defaultVal:ir.None,localizationId:"DataAccessUIStringId.QueryBuilderColumns_Aggregate"}],sr={RecordsCount:"RecordsCount",Column:"Column",Expression:"Expression",AllColumns:"AllColumns",AllColumnsQuery:"AllColumnsQuery"},or=[{propertyName:"expression",modelName:"#text"},{propertyName:"table",modelName:"@Table"},{propertyName:"column",modelName:"@Name"},{propertyName:"aggregate",modelName:"@Aggregate",defaultVal:ir.None},{propertyName:"alias",modelName:"@Alias"},{propertyName:"descending",modelName:"@Descending",defaultVal:!1},{propertyName:"itemType",modelName:"@ItemType"}];class nr{constructor(e,t,a){this.initialize(e,t,a)}actualName(){return this.alias()||this.column()}initialize(e,t,a){if((a=a||new A.ModelSerializer).deserialize(this,e),this._dependedTables=[],this.table()){const e=t.getTable(this.table());this.table=P.pureComputed((()=>e.actualName())),this._dependedTables.push(e)}else if(this.expression())try{this._criteria=Jt.CriteriaOperatorStateMachine.parse(this.expression()),(0,Jt.criteriaForEach)(this._criteria,(e=>{if(e instanceof Ht.OperandProperty){const a=(0,f.findFirstItemMatchesCondition)(t.tables(),(t=>0===e.propertyName.indexOf(t.actualName()+".")));a&&this._dependedTables.push(a)}}))}catch(e){}this.__table=this.table,this.__column=this.column,this.__expression=this.expression}toTable(){this.table=this.__table,this.column=this.__column,this.expression=P.observable(null),this.itemType(sr[sr.Column]),this.__column.peek()&&0===this.alias().indexOf("Expr")&&this.alias(null)}getInfo(){return or}isDepended(e){return!!(0,f.findFirstItemMatchesCondition)(this._dependedTables,(t=>t.actualName()===e))}}class dr{static addToColumnCache(e){e.forEach((e=>{this.columnCache[e.table()]??(this.columnCache[e.table()]={}),this.columnCache[e.table()][e.column()]=e}))}static clearCache(){this.columnCache={}}static find(e,t,a){return(0,f.findFirstItemMatchesCondition)(e(),(e=>e.column()===a&&e.table()===t))}static findByName(e,t){return(0,f.findFirstItemMatchesCondition)(e(),(e=>e.actualName()===t))}static removeDependend(e,t){e.remove((e=>e.isDepended(t)))}static toExpresson(e,t,a){e.table=P.observable(null),e.column=P.observable(null),e.expression=e.__expression,e.itemType(sr[sr.Expression]),e.alias()||e.alias(dr.setUniqueAlias(t,"Expr")),e.expression(a)}static setUniqueAlias(e,t){return dr.findByName(e,t)?(0,f.getUniqueName)(e().map((e=>e.actualName())),t+"_"):t}static createNew(e,t,a,i){let r=this.columnCache[a]&&this.columnCache[a][i];const s={"@Table":a,"@Name":i,"@ItemType":"Column"};return r?(s["@Alias"]=r.alias(),r.initialize(s,e)):r=new nr(s,e),e.columns===t&&!r.alias()&&dr.findByName(t,r.actualName())&&r.alias(this.setUniqueAlias(t,r.table()+"_"+r.column())),r}static addNew(e,t,a,i,r=!1){const s=this.createNew(e,t,a,i);return(r?t():t).push(s),s}static remove(e,t,a,i=!1){if(i){for(let i=0;i<e().length;i++)if(e()[i].column()===a&&e()[i].table()===t)return void this.addToColumnCache(e().splice(i,1))}else this.addToColumnCache(e.remove((e=>e.column()===a&&e.table()===t)))}static columnTypeToFederated(e){switch(e){case sr[sr.Expression]:return Vt[Vt.SelectExpression];case sr[sr.AllColumns]:return Vt[Vt.SelectAllNodeColumnsExpression];case sr[sr.AllColumnsQuery]:return Vt[Vt.SelectAllColumnsExpression];default:return Vt[Vt.SelectColumnExpression]}}static federatedTypeToColumn(e){switch(e){case Vt[Vt.SelectExpression]:return sr[sr.Expression];case Vt[Vt.SelectAllNodeColumnsExpression]:return sr[sr.AllColumns];case Vt[Vt.SelectAllColumnsExpression]:return sr[sr.AllColumnsQuery];default:return sr[sr.Column]}}}dr.columnCache={};class lr extends A.Disposable{constructor(e,t=!1,a=e=>e){super(),this.customizeQBInitializationData=a,this._querySource=P.observable(null),this._dbSchemaProvider=P.observable(null),this.designer=P.observable(),this.qbOptions=P.observable(null),this.okButtonDisabled=P.pureComputed((()=>this.designer()&&!this.designer().model().isValid())),this.isVisible=P.observable(!1),this.showLoadIndicator=P.observable(!1),this.localizationIdMap={title:{text:"Query Builder",localizationId:"DataAccessUIStringId.QueryBuilder"},loading:{text:"Loading...",localizationId:"AnalyticsCoreStringId.Loading"},previewResults:{text:"Preview Results...",localizationId:"DataAccessUIStringId.QueryBuilderButtons_PreviewResults"},cancel:{text:"Cancel",localizationId:"AnalyticsCoreStringId.SearchDialog_Cancel"},ok:{text:"OK",localizationId:"DataAccessUIStringId.Button_OK"}},this._rtl=t,this._applyQuery=e,this.qbOptions(this.qbOptions()||this.customizeQBInitializationData({queryBuilderModel:this.designer,dbSchemaProvider:this._dbSchemaProvider,querySource:this._querySource,callbacks:{CustomizeMenuActions:cr.customizeQueryBuilderActions},rtl:this._rtl}))}show(e,t){this._dataSource=t,this._dbSchemaProvider(t.dbSchemaProvider),this._querySource((new A.ModelSerializer).serialize(e)),this.isVisible(!0),this.designer().updateSurface()}cancelHandler(){dr.clearCache(),this.isVisible(!1)}previewHandler(){this.designer().showPreview()}okHandler(){this.designer().model().canSave()&&(dr.clearCache(),this._applyQuery(this.createQuery(),this.showLoadIndicator).done((()=>{this.isVisible(!1)})))}onHiddingHandler(){this.designer().dataPreview.isVisible(!1)}popupViewModel(e){const t=(0,f.getParentContainer)(e);return{visible:this.isVisible,title:this.getDisplayText("title"),showTitle:!0,shading:!0,fullScreen:!1,width:"95%",height:"95%",container:t,wrapperAttr:{class:"dxrd-querybuilder-popup"},position:{of:t},onHidding:this.onHiddingHandler}}getDisplayText(e){return(0,A.getLocalization)(this.localizationIdMap[e].text,this.localizationIdMap[e].localizationId)}}lr.customizeQueryBuilderActions=(e,t)=>{const a=t.Actions,i=(0,f.findFirstItemMatchesCondition)(a,(e=>"Delete"===e.text)),r=(0,f.findFirstItemMatchesCondition)(a,(e=>"Undo"===e.text)),s=(0,f.findFirstItemMatchesCondition)(a,(e=>"Redo"===e.text));a.splice(0,a.length,i,r,s)};class cr extends lr{constructor(e,t=!1,a=e=>e){super(e,t,a),this.customizeQBInitializationData=a}getDataSource(){return this._dataSource}createQuery(){return new bt(this.designer().model().serialize(),this.getDataSource())}}class ur{constructor(e,t){this._selectStatementCallback=e,this._connection=t}getQuerySqlText(e){const t=JSON.stringify({Query:(new A.ModelSerializer).serialize(e)});return this._selectStatementCallback(this._connection(),t).fail((e=>{const t=(0,f.getErrorMessage)(e);(0,f.ShowMessage)("Unable to build a SQL string"+(t?": "+t:"."))}))}}const pr=window.ace;var hr=e.n(pr);function mr(e=!1){return{showLineNumbers:!1,showPrintMargin:!1,enableBasicAutocompletion:!1,enableLiveAutocompletion:!1,readOnly:e,highlightSelectedWord:e,showGutter:!1,highlightActiveLine:!1}}function gr(e){return{onChange:t=>{e(t.getValue())},onValueChange:e=>{e.resize(!0)},changeTimeout:200,overrideEditorFocus:!0,setUseWrapMode:!0}}function xr(){return{getLanguageMode:()=>"ace/mode/sql",createCompleters:()=>[]}}hr()&&(hr().define("ace/mode/sql_highlight_rules",["require","exports","module","ace/lib/oop","ace/mode/text_highlight_rules","ace/mode/doc_comment_highlight_rules"],(function(e,t,a){const i=hr().require("ace/lib/oop"),r=hr().require("ace/mode/doc_comment_highlight_rules").DocCommentHighlightRules,s=hr().require("ace/mode/text_highlight_rules").TextHighlightRules,o=function(){let e="ALL|AND|ANY|BETWEEN|EXISTS|IN|LIKE|NOT|OR|SOME";e+="|NULL|IS|APPLY|INNER|OUTER|LEFT|RIGHT|JOIN|CROSS";const t="OPENDATASOURCE|OPENQUERY|OPENROWSET|OPENXML|AVG|CHECKSUM_AGG|COUNT|COUNT_BIG|GROUPING|GROUPING_ID|MAX|MIN|STDEV|STDEVP|SUM|VAR|VARP|DENSE_RANK|NTILE|RANK|ROW_NUMBER@@DATEFIRST|@@DBTS|@@LANGID|@@LANGUAGE|@@LOCK_TIMEOUT|@@MAX_CONNECTIONS|@@MAX_PRECISION|@@NESTLEVEL|@@OPTIONS|@@REMSERVER|@@SERVERNAME|@@SERVICENAME|@@SPID|@@TEXTSIZE|@@VERSION|CAST|CONVERT|PARSE|TRY_CAST|TRY_CONVERT|TRY_PARSE@@CURSOR_ROWS|@@FETCH_STATUS|CURSOR_STATUS|@@DATEFIRST|@@LANGUAGE|CURRENT_TIMESTAMP|DATEADD|DATEDIFF|DATEFROMPARTS|DATENAME|DATEPART|DATETIME2FROMPARTS|DATETIMEFROMPARTS|DATETIMEOFFSETFROMPARTS|DAY|EOMONTH|GETDATE|GETUTCDATE|ISDATE|MONTH|SET DATEFIRST|SET DATEFORMAT|SET LANGUAGE|SMALLDATETIMEFROMPARTS|SP_HELPLANGUAGE|SWITCHOFFSET|SYSDATETIME|SYSDATETIMEOFFSET|SYSUTCDATETIME|TIMEFROMPARTS|TODATETIMEOFFSET|YEAR|CHOOSE|IIF|ABS|ACOS|ASIN|ATAN|ATN2|CEILING|COS|COT|DEGREES|EXP|FLOOR|LOG|LOG10|PI|POWER|RADIANS|RAND|ROUND|SIGN|SIN|SQRT|SQUARE|TAN|@@PROCID|APPLOCK_MODE|APPLOCK_TEST|APP_NAME|ASSEMBLYPROPERTY|COLUMNPROPERTY|COL_LENGTH|COL_NAME|DATABASEPROPERTYEX|DATABASE_PRINCIPAL_ID|DB_ID|DB_NAME|FILEGROUPPROPERTY|FILEGROUP_ID|FILEGROUP_NAME|FILEPROPERTY|FILE_ID|FILE_IDEX|FILE_NAME|FULLTEXTCATALOGPROPERTY|FULLTEXTSERVICEPROPERTY|INDEXKEY_PROPERTY|INDEXPROPERTY|INDEX_COL|OBJECTPROPERTY|OBJECTPROPERTYEX|OBJECT_DEFINITION|OBJECT_ID|OBJECT_NAME|OBJECT_SCHEMA_NAME|ORIGINAL_DB_NAME|PARSENAME|SCHEMA_ID|SCHEMA_NAME|SCOPE_IDENTITY|SERVERPROPERTY|STATS_DATE|TYPEPROPERTY|TYPE_ID|TYPE_NAME|CERTENCODED|CERTPRIVATEKEY|CURRENT_USER|DATABASE_PRINCIPAL_ID|HAS_PERMS_BY_NAME|IS_MEMBER|IS_ROLEMEMBER|IS_SRVROLEMEMBER|ORIGINAL_LOGIN|PERMISSIONS|PWDCOMPARE|PWDENCRYPT|SCHEMA_ID|SCHEMA_NAME|SESSION_USER|SUSER_ID|SUSER_NAME|SUSER_SID|SUSER_SNAME|SYS.FN_BUILTIN_PERMISSIONS|SYS.FN_GET_AUDIT_FILE|SYS.FN_MY_PERMISSIONS|SYSTEM_USER|USER_ID|USER_NAME|ASCII|CHAR|CHARINDEX|CONCAT|DIFFERENCE|FORMAT|LEN|LOWER|LTRIM|NCHAR|PATINDEX|QUOTENAME|REPLACE|REPLICATE|REVERSE|RTRIM|SOUNDEX|SPACE|STR|STUFF|SUBSTRING|UNICODE|UPPER|$PARTITION|@@ERROR|@@IDENTITY|@@PACK_RECEIVED|@@ROWCOUNT|@@TRANCOUNT|BINARY_CHECKSUM|CHECKSUM|CONNECTIONPROPERTY|CONTEXT_INFO|CURRENT_REQUEST_ID|ERROR_LINE|ERROR_MESSAGE|ERROR_NUMBER|ERROR_PROCEDURE|ERROR_SEVERITY|ERROR_STATE|FORMATMESSAGE|GETANSINULL|GET_FILESTREAM_TRANSACTION_CONTEXT|HOST_ID|HOST_NAME|ISNULL|ISNUMERIC|MIN_ACTIVE_ROWVERSION|NEWID|NEWSEQUENTIALID|ROWCOUNT_BIG|XACT_STATE|@@CONNECTIONS|@@CPU_BUSY|@@IDLE|@@IO_BUSY|@@PACKET_ERRORS|@@PACK_RECEIVED|@@PACK_SENT|@@TIMETICKS|@@TOTAL_ERRORS|@@TOTAL_READ|@@TOTAL_WRITE|FN_VIRTUALFILESTATS|PATINDEX|TEXTPTR|TEXTVALID|COALESCE|NULLIF",a="BIGINT|BINARY|BIT|CHAR|CURSOR|DATE|DATETIME|DATETIME2|DATETIMEOFFSET|DECIMAL|FLOAT|HIERARCHYID|IMAGE|INTEGER|INT|MONEY|NCHAR|NTEXT|NUMERIC|NVARCHAR|REAL|SMALLDATETIME|SMALLINT|SMALLMONEY|SQL_VARIANT|TABLE|TEXT|TIME|TIMESTAMP|TINYINT|UNIQUEIDENTIFIER|VARBINARY|VARCHAR|XML",i="sp_addextendedproc|sp_addextendedproperty|sp_addmessage|sp_addtype|sp_addumpdevice|sp_add_data_file_recover_suspect_db|sp_add_log_file_recover_suspect_db|sp_altermessage|sp_attach_db|sp_attach_single_file_db|sp_autostats|sp_bindefault|sp_bindrule|sp_bindsession|sp_certify_removable|sp_clean_db_file_free_space|sp_clean_db_free_space|sp_configure|sp_control_plan_guide|sp_createstats|sp_create_plan_guide|sp_create_plan_guide_from_handle|sp_create_removable|sp_cycle_errorlog|sp_datatype_info|sp_dbcmptlevel|sp_dbmmonitoraddmonitoring|sp_dbmmonitorchangealert|sp_dbmmonitorchangemonitoring|sp_dbmmonitordropalert|sp_dbmmonitordropmonitoring|sp_dbmmonitorhelpalert|sp_dbmmonitorhelpmonitoring|sp_dbmmonitorresults|sp_db_increased_partitions|sp_delete_backuphistory|sp_depends|sp_describe_first_result_set|sp_describe_undeclared_parameters|sp_detach_db|sp_dropdevice|sp_dropextendedproc|sp_dropextendedproperty|sp_dropmessage|sp_droptype|sp_execute|sp_executesql|sp_getapplock|sp_getbindtoken|sp_help|sp_helpconstraint|sp_helpdb|sp_helpdevice|sp_helpextendedproc|sp_helpfile|sp_helpfilegroup|sp_helpindex|sp_helplanguage|sp_helpserver|sp_helpsort|sp_helpstats|sp_helptext|sp_helptrigger|sp_indexoption|sp_invalidate_textptr|sp_lock|sp_monitor|sp_prepare|sp_prepexec|sp_prepexecrpc|sp_procoption|sp_recompile|sp_refreshview|sp_releaseapplock|sp_rename|sp_renamedb|sp_resetstatus|sp_sequence_get_range|sp_serveroption|sp_setnetname|sp_settriggerorder|sp_spaceused|sp_tableoption|sp_unbindefault|sp_unbindrule|sp_unprepare|sp_updateextendedproperty|sp_updatestats|sp_validname|sp_who|sys.sp_merge_xtp_checkpoint_files|sys.sp_xtp_bind_db_resource_pool|sys.sp_xtp_checkpoint_force_garbage_collection|sys.sp_xtp_control_proc_exec_stats|sys.sp_xtp_control_query_exec_stats|sys.sp_xtp_unbind_db_resource_pool";let s="ABSOLUTE|ACTION|ADA|ADD|ADMIN|AFTER|AGGREGATE|ALIAS|ALL|ALLOCATE|ALTER|AND|ANY|ARE|ARRAY|AS|ASC|ASENSITIVE|ASSERTION|ASYMMETRIC|AT|ATOMIC|AUTHORIZATION|BACKUP|BEFORE|BEGIN|BETWEEN|BIT_LENGTH|BLOB|BOOLEAN|BOTH|BREADTH|BREAK|BROWSE|BULK|BY|CALL|CALLED|CARDINALITY|CASCADE|CASCADED|CASE|CATALOG|CHARACTER|CHARACTER_LENGTH|CHAR_LENGTH|CHECK|CHECKPOINT|CLASS|CLOB|CLOSE|CLUSTERED|COALESCE|COLLATE|COLLATION|COLLECT|COLUMN|COMMIT|COMPLETION|COMPUTE|CONDITION|CONNECT|CONNECTION|CONSTRAINT|CONSTRAINTS|CONSTRUCTOR|CONTAINS|CONTAINSTABLE|CONTINUE|CORR|CORRESPONDING|COVAR_POP|COVAR_SAMP|CREATE|CROSS|CUBE|CUME_DIST|CURRENT|CURRENT_CATALOG|CURRENT_DATE|CURRENT_DEFAULT_TRANSFORM_GROUP|CURRENT_PATH|CURRENT_ROLE|CURRENT_SCHEMA|CURRENT_TIME|CURRENT_TRANSFORM_GROUP_FOR_TYPE|CYCLE|DATA|DATABASE|DBCC|DEALLOCATE|DEC|DECLARE|DEFAULT|DEFERRABLE|DEFERRED|DELETE|DENY|DEPTH|DEREF|DESC|DESCRIBE|DESCRIPTOR|DESTROY|DESTRUCTOR|DETERMINISTIC|DIAGNOSTICS|DICTIONARY|DISCONNECT|DISK|DISTINCT|DISTRIBUTED|DOMAIN|DOUBLE|DROP|DUMP|DYNAMIC|EACH|ELEMENT|ELSE|END|END-EXEC|EQUALS|ERRLVL|ESCAPE|EVERY|EXCEPT|EXCEPTION|EXEC|EXECUTE|EXISTS|EXIT|EXTERNAL|EXTRACT|FETCH|FILE|FILLFACTOR|FILTER|FIRST|FOR|FOREIGN|FORTRAN|FOUND|FREE|FREETEXT|FREETEXTTABLE|FROM|FULL|FULLTEXTTABLE|FUNCTION|FUSION|GENERAL|GET|GLOBAL|GO|GOTO|GRANT|GROUP|HAVING|HOLD|HOLDLOCK|HOST|HOUR|IDENTITY|IDENTITYCOL|IDENTITY_INSERT|IF|IGNORE|IMMEDIATE|IN|INCLUDE|INDEX|INDICATOR|INITIALIZE|INITIALLY|INNER|INOUT|INPUT|INSENSITIVE|INSERT|INTEGER|INTERSECT|INTERSECTION|INTERVAL|INTO|IS|ISOLATION|ITERATE|JOIN|KEY|KILL|LANGUAGE|LARGE|LAST|LATERAL|LEADING|LESS|LEVEL|LIKE|LIKE_REGEX|LIMIT|LINENO|LN|LOAD|LOCAL|LOCALTIME|LOCALTIMESTAMP|LOCATOR|MAP|MATCH|MEMBER|MERGE|METHOD|MINUTE|MOD|MODIFIES|MODIFY|MODULE|MULTISET|NAMES|NATIONAL|NATURAL|NCLOB|NEW|NEXT|NO|NOCHECK|NONCLUSTERED|NONE|NORMALIZE|NOT|NULL|NULLIF|OBJECT|OCCURRENCES_REGEX|OCTET_LENGTH|OF|OFF|OFFSETS|OLD|ON|ONLY|OPEN|OPERATION|OPTION|OR|ORDER|ORDINALITY|OUT|OUTER|OUTPUT|OVER|OVERLAPS|OVERLAY|PAD|PARAMETER|PARAMETERS|PARTIAL|PARTITION|PASCAL|PATH|PERCENT|PERCENTILE_CONT|PERCENTILE_DISC|PERCENT_RANK|PIVOT|PLAN|POSITION|POSITION_REGEX|POSTFIX|PRECISION|PREFIX|PREORDER|PREPARE|PRESERVE|PRIMARY|PRINT|PRIOR|PRIVILEGES|PROC|PROCEDURE|PUBLIC|RAISERROR|RANGE|READ|READS|READTEXT|RECONFIGURE|RECURSIVE|REF|REFERENCES|REFERENCING|REGR_AVGX|REGR_AVGY|REGR_COUNT|REGR_INTERCEPT|REGR_R2|REGR_SLOPE|REGR_SXX|REGR_SXY|REGR_SYY|RELATIVE|RELEASE|REPLICATION|RESTORE|RESTRICT|RESULT|RETURN|RETURNS|REVERT|REVOKE|ROLE|ROLLBACK|ROLLUP|ROUTINE|ROW|ROWCOUNT|ROWGUIDCOL|ROWS|RULE|SAVE|SAVEPOINT|SCHEMA|SCOPE|SCROLL|SEARCH|SECOND|SECTION|SECURITYAUDIT|SELECT|SEMANTICKEYPHRASETABLE|SEMANTICSIMILARITYDETAILSTABLE|SEMANTICSIMILARITYTABLE|SENSITIVE|SEQUENCE|SESSION|SET|SETS|SETUSER|SHUTDOWN|SIMILAR|SIZE|SOME|SPECIFIC|SPECIFICTYPE|SQL|SQLCA|SQLCODE|SQLERROR|SQLEXCEPTION|SQLSTATE|SQLWARNING|START|STATE|STATEMENT|STATIC|STATISTICS|STDDEV_POP|STDDEV_SAMP|STRUCTURE|SUBMULTISET|SUBSTRING_REGEX|SYMMETRIC|SYSTEM|TABLESAMPLE|TEMPORARY|TERMINATE|TEXTSIZE|THAN|THEN|TIMEZONE_HOUR|TIMEZONE_MINUTE|TO|TOP|TRAILING|TRAN|TRANSACTION|TRANSLATE|TRANSLATE_REGEX|TRANSLATION|TREAT|TRIGGER|TRIM|TRUNCATE|TSEQUAL|UESCAPE|UNDER|UNION|UNIQUE|UNKNOWN|UNNEST|UNPIVOT|UPDATE|UPDATETEXT|USAGE|USE|USER|USING|VALUE|VALUES|VARIABLE|VARYING|VAR_POP|VAR_SAMP|VIEW|WAITFOR|WHEN|WHENEVER|WHERE|WHILE|WIDTH_BUCKET|WINDOW|WITH|WITHIN|WITHIN GROUP|WITHOUT|WORK|WRITE|WRITETEXT|XMLAGG|XMLATTRIBUTES|XMLBINARY|XMLCAST|XMLCOMMENT|XMLCONCAT|XMLDOCUMENT|XMLELEMENT|XMLEXISTS|XMLFOREST|XMLITERATE|XMLNAMESPACES|XMLPARSE|XMLPI|XMLQUERY|XMLSERIALIZE|XMLTABLE|XMLTEXT|XMLVALIDATE|ZONE";s+="|KEEPIDENTITY|KEEPDEFAULTS|IGNORE_CONSTRAINTS|IGNORE_TRIGGERS|XLOCK|FORCESCAN|FORCESEEK|HOLDLOCK|NOLOCK|NOWAIT|PAGLOCK|READCOMMITTED|READCOMMITTEDLOCK|READPAST|READUNCOMMITTED|REPEATABLEREAD|ROWLOCK|SERIALIZABLE|SNAPSHOT|SPATIAL_WINDOW_MAX_CELLS|TABLOCK|TABLOCKX|UPDLOCK|XLOCK|IGNORE_NONCLUSTERED_COLUMNSTORE_INDEX|EXPAND|VIEWS|FAST|FORCE|KEEP|KEEPFIXED|MAXDOP|MAXRECURSION|OPTIMIZE|PARAMETERIZATION|SIMPLE|FORCED|RECOMPILE|ROBUST|PLAN|SPATIAL_WINDOW_MAX_CELLS|NOEXPAND|HINT",s+="|LOOP|HASH|MERGE|REMOTE",s+="|TRY|CATCH|THROW",s+="|TYPE",s=s.split("|"),s=s.filter((function(i,r,s){return-1===e.split("|").indexOf(i)&&-1===t.split("|").indexOf(i)&&-1===a.split("|").indexOf(i)})),s=s.sort().join("|");const o=this.createKeywordMapper({"constant.language":e,"storage.type":a,"support.function":t,"support.storedprocedure":i,keyword:s},"identifier",!0),n="SET ANSI_DEFAULTS|SET ANSI_NULLS|SET ANSI_NULL_DFLT_OFF|SET ANSI_NULL_DFLT_ON|SET ANSI_PADDING|SET ANSI_WARNINGS|SET ARITHABORT|SET ARITHIGNORE|SET CONCAT_NULL_YIELDS_NULL|SET CURSOR_CLOSE_ON_COMMIT|SET DATEFIRST|SET DATEFORMAT|SET DEADLOCK_PRIORITY|SET FIPS_FLAGGER|SET FMTONLY|SET FORCEPLAN|SET IDENTITY_INSERT|SET IMPLICIT_TRANSACTIONS|SET LANGUAGE|SET LOCK_TIMEOUT|SET NOCOUNT|SET NOEXEC|SET NUMERIC_ROUNDABORT|SET OFFSETS|SET PARSEONLY|SET QUERY_GOVERNOR_COST_LIMIT|SET QUOTED_IDENTIFIER|SET REMOTE_PROC_TRANSACTIONS|SET ROWCOUNT|SET SHOWPLAN_ALL|SET SHOWPLAN_TEXT|SET SHOWPLAN_XML|SET STATISTICS IO|SET STATISTICS PROFILE|SET STATISTICS TIME|SET STATISTICS XML|SET TEXTSIZE|SET XACT_ABORT".split("|"),d="READ UNCOMMITTED|READ COMMITTED|REPEATABLE READ|SNAPSHOP|SERIALIZABLE".split("|");for(let e=0;e<d.length;e++)n.push("SET TRANSACTION ISOLATION LEVEL "+d[e]);this.$rules={start:[{token:"string.start",regex:"'",next:[{token:"constant.language.escape",regex:/''/},{token:"string.end",next:"start",regex:"'"},{defaultToken:"string"}]},r.getStartRule("doc-start"),{token:"comment",regex:"--.*$"},{token:"comment",start:"/\\*",end:"\\*/"},{token:"constant.numeric",regex:"[+-]?\\d+(?:(?:\\.\\d*)?(?:[eE][+-]?\\d+)?)?\\b"},{token:o,regex:"@{0,2}[a-zA-Z_$][a-zA-Z0-9_$]*\\b(?!])"},{token:"constant.class",regex:"@@?[a-zA-Z_$][a-zA-Z0-9_$]*\\b"},{token:"keyword.operator",regex:"\\+|\\-|\\/|\\/\\/|%|<@>|@>|<@|&|\\^|~|<|>|<=|=>|==|!=|<>|=|\\*"},{token:"paren.lparen",regex:"[\\(]"},{token:"paren.rparen",regex:"[\\)]"},{token:"punctuation",regex:",|;"},{token:"text",regex:"\\s+"}],comment:[r.getTagRule(),{token:"comment",regex:"\\*\\/",next:"no_regex"},{defaultToken:"comment",caseInsensitive:!0}]};for(let e=0;e<n.length;e++)this.$rules.start.unshift({token:"set.statement",regex:n[e]});this.embedRules(r,"doc-",[r.getEndRule("start")]),this.normalizeRules();const l=[],c=function(e,t){e.forEach((function(e){l.push({name:e,value:e,score:0,meta:t})}))};c(i.split("|"),"procedure"),c(e.split("|"),"operator"),c(t.split("|"),"function"),c(a.split("|"),"type"),c(n,"statement"),c(s.split("|"),"keyword"),this.completions=l};i.inherits(o,s),t.SqlHighlightRules=o})),hr().define("ace/mode/sql",["require","exports","module","ace/lib/oop","ace/mode/text","ace/mode/sql_highlight_rules","ace/range"],(function(e,t,a){const i=hr().require("ace/lib/oop"),r=hr().require("ace/mode/text").Mode,s=hr().require("ace/mode/sql_highlight_rules").SqlHighlightRules,o=(hr().require("ace/range").Range,function(){this.HighlightRules=s});i.inherits(o,r),function(){this.lineCommentStart="--",this.$id="ace/mode/sql"}.call(o.prototype),t.Mode=o})));class fr extends A.Disposable{constructor(e,t){super(),this._tableQueryString=P.observable(""),this._query=P.observable(),this._needToCustomizeParameters=P.pureComputed((()=>this._query()&&(this._query().type()===Fe.customSqlQuery||this._query().parameters().length>0))),this.template="dxrd-select-control",this.aceOptions=mr(),this.additionalOptions=gr((e=>{this.sqlString(e)})),this.aceAvailable=(0,it.aceAvailable)(),this.languageHelper={getLanguageMode:()=>"ace/mode/sql",createCompleters:()=>[]},this.caption=()=>(0,A.getLocalization)("SQL string:","DataAccessUIStringId.QueryControl_SqlString"),this.sqlString=P.pureComputed({read:()=>this._query()&&this._query().type()===Fe.customSqlQuery?this._query().sqlString():this._tableQueryString(),write:e=>{if(this._query().type()!==Fe.customSqlQuery){const t=new pt({"@Name":this._query().name()},this._query().parent);t.parameters(this._query().parameters()),t.sqlString(e),this._query(t)}else this._query().sqlString(e)}}),this.isNextDisabled=P.pureComputed((()=>!this.sqlString()||!this._needToCustomizeParameters())),this.isFinishDisabled=P.pureComputed((()=>!this.sqlString()||this._needToCustomizeParameters())),this._sqlTextProvider=e,this.disableCustomSql=()=>t,this.aceOptions.readOnly=this.disableCustomSql(),this._disposables.push(this._needToCustomizeParameters,this.sqlString)}setQuery(e,t){return this._query()!==e&&e.type()===Fe.tableQuery?(t&&t(!0),this._sqlTextProvider.getQuerySqlText(e).done((t=>{t.errorMessage&&(0,f.ShowMessage)(t.errorMessage),this._tableQueryString(t.sqlSelectStatement),this._query(e)})).always((()=>{t&&t(!1)}))):(this._query(e),(new f.DxDeferred).resolve().promise())}getQuery(){return this._query()}get runQueryBuilderDisabled(){return!1}}class br extends A.Disposable{_needToProcessParameters(e){return e.arguments.some(br._availableConvertToParameter)}static _availableConvertToParameter(e){return e.direction!==re.Out}get _selectedProcedure(){return this.selectedProcedure()[0]}set _selectedProcedure(e){this.selectedProcedure(e?[e]:[])}constructor(){super(),this.template="dxrd-procedures-control",this.storedProcedures=P.observableArray([]),this.selectedProcedure=P.observableArray([]),this.caption=()=>(0,A.getLocalization)("Select a stored procedure:","DataAccessUIStringId.StoredProcControl_Caption"),this.generateStoredProcedureDisplayName=e=>br.generateStoredProcedureDisplayName(e),this.isNextDisabled=P.pureComputed((()=>!this._selectedProcedure||!this._needToProcessParameters(this._selectedProcedure))),this.isFinishDisabled=P.pureComputed((()=>!this._selectedProcedure||this._needToProcessParameters(this._selectedProcedure))),this.storedProcedures.subscribe((e=>{e?this._selectedProcedure?this._selectedProcedure=(0,f.getFirstItemByPropertyValue)(this.storedProcedures(),"name",this._selectedProcedure.name):this._query&&this._query.procName()?this._selectedProcedure=(0,f.getFirstItemByPropertyValue)(this.storedProcedures(),"name",this._query.procName()):this._selectedProcedure=e[0]:this._selectedProcedure=null}))}scrollActiveItem(e){const t=e.model.selectedProcedure.peek();e.component.scrollToItem(t[0])}static generateStoredProcedureDisplayName(e){return 0===e.arguments.length?e.name:e.arguments.reduce(((e,t,a,i)=>e+(t.name+(a<i.length-1?", ":")"))),e.name+"(")}setQuery(e){return this._query=e,this._selectedProcedure=(0,f.getFirstItemByPropertyValue)(this.storedProcedures(),"name",e.procName()),(new f.DxDeferred).resolve().promise()}getQuery(){if(!this._selectedProcedure)return null;const e=new St({"@Name":this._query.name()||this._selectedProcedure.name,ProcName:this._selectedProcedure.name},this._query.parent);return this._selectedProcedure.arguments.forEach((t=>{br._availableConvertToParameter(t)&&e.parameters.push((0,f.getFirstItemByPropertyValue)(this._query.parameters(),"name",t.name)||new ct({"@Name":t.name,"@Type":se.GetType(t.type)},null,at(se.GetType(t.type))))})),e}get runQueryBuilderDisabled(){return!0}}class yr extends Ea{constructor(e){super(),this._options=e,this._connection=()=>this._dataSource().connection,this._dataSource=()=>this._dataSourceWrapper&&this._dataSourceWrapper.sqlDataSource,this.queryNameCaption=()=>(0,A.getLocalization)("Query Name","AnalyticsCoreStringId.SqlDSWizard_QueryName")+":",this.queryControl=P.observable(),this.runQueryBuilderBtnText=P.pureComputed((()=>this._selectStatementControl.sqlString()&&this._selectStatementControl.getQuery().type()!==Fe.tableQuery?(0,A.getLocalization)("Create New Query...","AnalyticsCoreStringId.SqlDSWizard_CreateNewQuery"):(0,A.getLocalization)("Run Query Builder...","DataAccessUIStringId.Button_QueryBuilder"))).extend({deferred:!0}),this.placeholder=()=>{const e=this.queryControl(),t=e&&e.getQuery(),a=this.initialName||t&&t.generateName();return a?(0,f.formatUnicorn)((0,A.getLocalization)("Type custom query name (current name: {0})","AnalyticsCoreStringId.SqlDSWizard_QueryNamePlaceholder"),a):a},this.queryName=P.observable(""),this.queryTypeItems=[yr.QUERY_TEXT,yr.SP_TEXT],this.selectedQueryType=P.observable(),this.initialName="",this._proceduresList=new br,this._disposables.push(this._selectStatementControl=new fr(new ur(er(this._options.callbacks.selectStatement),this._connection),this._options.disableCustomSql)),this._disposables.push(this.selectedQueryType.subscribe((e=>{e===yr.SP_TEXT?(this[wa]&&this[wa](!0),this._dataSource().dbSchemaProvider.getDbStoredProcedures().done((e=>{this._proceduresList.storedProcedures([]),this._proceduresList.storedProcedures(e)})).always((e=>this[wa]&&this[wa](!1))),this.queryControl(this._proceduresList)):this.queryControl(this._selectStatementControl)}))),this.selectedQueryType(yr.QUERY_TEXT),this.popupQueryBuilder=new cr(((e,t)=>(this.queryName(e.name()||e.generateName()),this._selectStatementControl.setQuery(e,t))),this._options.rtl,this._options.callbacks.customizeQBInitData)}canNext(){return!this.queryControl().isNextDisabled()}canFinish(){return!this.queryControl().isFinishDisabled()||!this.queryControl().isNextDisabled()}runQueryBuilder(){const e=this.queryControl().getQuery();e&&e.type()===Fe.tableQuery?(e.name(this.queryName()),this.popupQueryBuilder.show(e,this._dataSource())):this.popupQueryBuilder.show(new bt({"@Name":this.queryName()},this._dataSource()),this._dataSource())}localizeQueryType(e){return yr.QUERY_TEXT===e?(0,A.getLocalization)(yr.QUERY_TEXT,"DataAccessUIStringId.WizardPageConfigureQuery_Query"):(0,A.getLocalization)(yr.SP_TEXT,"DataAccessUIStringId.WizardPageConfigureQuery_StoredProcedure")}initialize(e){return this._dataSourceWrapper=Ba(e,this._options.requestWrapper),this._proceduresList.setQuery(new St({},this._dataSource())),this._selectStatementControl.setQuery(new pt({},this._dataSource())),this.popupQueryBuilder.isVisible(!1),this._dataSourceWrapper.sqlQuery?(this.initialName=this._dataSourceWrapper.sqlQuery.name(),this.queryName(this.initialName),this.selectedQueryType(this._dataSourceWrapper.sqlQuery.type()===Fe.storedProcQuery?yr.SP_TEXT:yr.QUERY_TEXT),this.queryControl().setQuery(this._dataSourceWrapper.sqlQuery)):(this.selectedQueryType(yr.QUERY_TEXT),(0,f.createDeferred)().resolve().promise())}commit(){const e=this.queryControl().getQuery();if(e){const t=this.queryName()||this.initialName;t&&e.name(t),e.name()&&this._dataSourceWrapper.sqlQuery&&this._dataSourceWrapper.sqlQuery.name()===e.name()||e.name(ba(this._dataSource().queries(),e)),this._dataSourceWrapper.sqlQuery=e}return(0,f.createDeferred)().resolve({queryName:this._dataSourceWrapper.sqlQuery.name(),sqlDataSourceJSON:this._dataSourceWrapper.save()}).promise()}}function Sr(e,t){e.registerMetadata(Sa.ConfigureQueryPage,{create:()=>new yr(t),setState:(e,t)=>{t.queryName=e.queryName,t.sqlDataSourceJSON=e.sqlDataSourceJSON},getState:e=>e.sqlDataSourceWizard,resetState:(e,t)=>{e.sqlDataSourceJSON=t.sqlDataSourceJSON,e.queryName=t.queryName},template:"dxrd-wizard-create-query-page",description:(0,f.localizeWithUpdateLocalizationMethod)("Create a query or select a stored procedure")||(0,A.getLocalization)("Create a query or select a stored procedure.",f.StringId.WizardPageConfigureQuery)})}yr.QUERY_TEXT="Query",yr.SP_TEXT="Stored Procedure";class _r extends Ea{_isParametersValid(){return this.getParameters().every((e=>e.isValid()))}constructor(e={createParameterViewModel:e=>e,getParameterFromViewModel:e=>e},t){super(),this.parametersConverter=e,this._requestWrapper=t,this._sqlDataSourceWrapper=new $a(void 0,void 0,this._requestWrapper),this.removeButtonTitle=(0,A.getLocalization)("Remove","DataAccessUIStringId.Button_Remove"),this.parametersEditorOptions={addHandler:()=>this.parametersConverter.createParameterViewModel(new ct({"@Name":(0,f.getUniqueNameForNamedObjectsArray)(this.parametersEditorOptions.values.peek().peek(),"parameter"),"@Type":f.DotnetTypes.SystemInt32})),values:P.observable(P.observableArray([])),displayName:"Parameters",level:0,info:P.observable({displayName:"Parameters",localizationId:"DevExpress.DataAccess.Sql.SqlQuery.Parameters",propertyName:"parameters",modelName:"Parameter",array:!0,editor:x.editorTemplates.getEditor("commonCollection"),template:"#dxrd-parameter-collection-item"}),editorTemplate:"#dxrd-wizard-datasource-parameters",hideButtons:P.observable(!1),collapsed:!1}}canNext(){return!1}canFinish(){return this._isParametersValid()}getParameters(){return this.parametersEditorOptions.values()()}initialize(e){return this._sqlDataSourceWrapper=Ba(e,this._requestWrapper),this.parametersEditorOptions.hideButtons(this._sqlDataSourceWrapper.sqlQuery.type()===Fe.storedProcQuery),setTimeout((()=>{this.parametersEditorOptions.values(P.observableArray(this._sqlDataSourceWrapper.sqlQuery.parameters().map((e=>this.parametersConverter.createParameterViewModel(e)))))}),100),(0,f.createDeferred)().resolve().promise()}commit(){return this._sqlDataSourceWrapper.sqlQuery.parameters(this.parametersEditorOptions.values()().map((e=>this.parametersConverter.getParameterFromViewModel(e)))),(0,f.createDeferred)().resolve({sqlDataSourceJSON:this._sqlDataSourceWrapper.save()}).promise()}}function vr(e,t,a){e.registerMetadata(Sa.ConfigureParametersPage,{create:()=>new _r(a,t),getState:e=>e.sqlDataSourceWizard,setState:(e,t)=>t.sqlDataSourceJSON=e.sqlDataSourceJSON,resetState:()=>{},template:"dxrd-page-configure-parameters",description:(0,A.getLocalization)("Configure query parameters.","AnalyticsCoreStringId.SqlDSWizard_PageConfigureParameters")})}class Cr extends A.Disposable{constructor(e,t,a,i,r){super(),this._callBack=P.observable({deleteAction:e=>{this._customQueries.remove((0,f.findFirstItemMatchesCondition)(this._customQueries(),(t=>(t.name()||t.generateName())===e)))},showQbCallBack:null,disableCustomSql:!1}),this._checkedRootNodesCount=P.pureComputed((()=>{let e=0;for(let t=0;t<this._rootItems.length&&e<2;t++)e+=this._rootItems[t].countChecked();return e})),this.hasCheckedItems=P.pureComputed((()=>!(0===this._checkedRootNodesCount()))),this.nextButtonDisabled=P.pureComputed((()=>!(this._checkedRootNodesCount()>1)&&!this.hasParametersToEdit())),this.hasParametersToEdit=P.pureComputed((()=>{for(let e=0;e<this._rootItems.length;e++)if(this._rootItems[e].hasParamsToEdit&&this._rootItems[e].hasParamsToEdit())return!0;return!1})),this.tables=()=>this._tables,this.views=()=>this._views,this.procedures=()=>this._procedures,this.queries=()=>this._queries,this.customQueries=()=>this._customQueries,this._callBack().showQbCallBack=a,this._callBack().disableCustomSql=i,this._tables=new Wi("tables",(0,A.getLocalization)("Tables","DataAccessStringId.ConfigureMultiQueryPage_TableCategory"),"list",!1,r),this._views=new Wi("views",(0,A.getLocalization)("Views","DataAccessStringId.ConfigureMultiQueryPage_ViewCategory"),"list",!1,r),this._procedures=new Fi("procedures",(0,A.getLocalization)("Stored Procedures","DataAccessStringId.ConfigureMultiQueryPage_SpCategory"),"list",!1,r),this._queries=new ji("queries",(0,A.getLocalization)("Queries","DataAccessStringId.ConfigureMultiQueryPage_QueryCategory"),"list",!1,this._callBack,r),this._disposables.push(this._tables,this._views,this._procedures,this._queries),this._rootItems=[this._tables,this._views,this._procedures,this._queries],this.itemsRequestDeferreds=P.observableArray([]),this.getItems=a=>{const i=new f.DxDeferred;if(a.fullPath)if("tables"===a.fullPath)e.getDbTables().done((e=>{if(0===this._tables.children().length){const t=[];e.tables.forEach((e=>{e.isView||t.push(new Wi(e.name,e.name,"table",this._tables.checked.peek(),r))})),this._tables.initializeChildren(t),i.resolve(t)}else i.resolve(this._tables.children())})).fail((e=>i.reject(e)));else if("views"===a.fullPath)e.getDbViews().done((e=>{if(0===this._views.children().length){const t=[];e.tables.forEach((e=>{e.isView&&t.push(new Wi(e.name,e.name,"view",this._views.checked.peek(),r))})),this._views.initializeChildren(t),i.resolve(t)}else i.resolve(this._views.children())})).fail((e=>i.reject(e)));else if("procedures"===a.fullPath)e.getDbStoredProcedures().done((e=>{if(0===this._procedures.children().length){const t=e.map((e=>{const t=new Vi(e.name,br.generateStoredProcedureDisplayName(e),"procedure",this._procedures.checked.peek(),e.arguments,r);return this._disposables.push(t),t}));this._procedures.initializeChildren(t),i.resolve(t)}else i.resolve(this._procedures.children())})).fail((e=>i.reject(e)));else if("queries"===a.fullPath){const e=t().map((e=>{const t=e.name()||e.generateName(),a=this._queries.children().filter((t=>t.query===e))[0];if(a)return a;const i=new Ui(t,t,"query",!!a&&a.checked(),e.parameters,this._callBack,r,e);return this._disposables.push(i),i.setObservableName((()=>e.name()||e.generateName()),(t=>e.name(t))),i}));this._queries.initializeChildren(e),i.resolve(e)}else e.getDbTable(a.path,a.fullPath).done((e=>{let t;if(t=e.isView?(0,f.findFirstItemMatchesCondition)(this._views.children(),(t=>t.name===e.name)):(0,f.findFirstItemMatchesCondition)(this._tables.children(),(t=>t.name===e.name)),0===t.children().length){const a=e.columns.map((e=>{const a=new Vi(e.name,e.name,"column",t.checked.peek(),null,r);return this._disposables.push(a),a}));t.initializeChildren(a),i.resolve(a)}else i.resolve(t.children())})).fail((e=>i.reject(e)));else i.resolve(this._rootItems);return this.addRequestDeferred(i),i.promise()},this._customQueries=t}addRequestDeferred(e){this.itemsRequestDeferreds.push(e),e.always((()=>{this.itemsRequestDeferreds.remove(e)}))}dispose(){super.dispose(),this.itemsRequestDeferreds().forEach((e=>e.reject())),this.itemsRequestDeferreds([])}}class Tr{_getPageState(e,t=this.globalState){return this.pageFactory.getMetadata(e).getState(t)}constructor(e,t){this.globalState=e,this.pageFactory=t,this.defaultState=(0,f.extend)(!0,{},e)}setPageState(e,t){this.pageFactory.getMetadata(e).setState(t,this.getPageState(e))}getPageState(e){return this._getPageState(e)}resetPageState(e){const t=(0,f.extend)(!0,{},this._getPageState(e,this.defaultState));this.pageFactory.getMetadata(e).resetState(this.getPageState(e),t)}getCurrentState(){return this.globalState}reset(){this.globalState.reset()}}class Pr extends A.Disposable{dispose(){this._pages.forEach((e=>e.dispose())),this._pages=[]}__resetPages(e){if(e<this._pages.length)for(let t=this._pages.length-1;t>=e;t--)this.stateManager.resetPageState(this._pages[t].pageId),this._onResetPage(this._pages[t]),this._pages[t].dispose(),this._pages.splice(t,1)}_nextPage(){return this._pages[this._currentIndex+1]}_getNextExistingPage(){this._currentIndex+=1;const e=new f.DxDeferred;return e.resolve(this._pages[this._currentIndex]),e.promise()}_resetPages(){this.__resetPages(this._currentIndex+1)}_getNextNewPage(e){this._currentIndex+=1;const t=new f.DxDeferred;this.__resetPages(this._currentIndex);const a=this.pageFactory.getMetadata(e),i=new Ua(e,a.create(),a.template,a.description);return this._pages.push(i),t.resolve(i),t.promise()}constructor(e,t,a=()=>{}){super(),this.pageFactory=e,this.stateManager=t,this._onResetPage=a,this._pages=[],this._currentIndex=0}_getStartPage(e){e=e||this.getNextPageId();const t=this.pageFactory.getMetadata(e),a=new Ua(e,t.create(),t.template,t.description);return this._pages.push(a),a}_getNextPage(){const e=this._getCurrentPage();if(e.isChanged||!this._nextPage()){const t=this.getNextPageId(this._getCurrentPage().pageId);return t?(e.isChanged=!1,this._getNextNewPage(t)):(new f.DxDeferred).reject().promise()}return this._getNextExistingPage()}_getPreviousPage(){const e=new f.DxDeferred;return this._currentIndex-1<0?e.reject(null):(this._currentIndex-=1,e.resolve(this._pages[this._currentIndex])),e.promise()}_goToPage(e){const t=new f.DxDeferred,a=this._pages.filter((t=>t.pageId===e))[0];return a?(this._currentIndex=this._pages.indexOf(a),t.resolve(a)):t.reject(null),t.promise()}_getCurrentPage(){return this._pages[this._currentIndex]}_getCurrentState(){return this.stateManager.getCurrentState()}getNextPageId(e){return""}}class Nr extends A.Disposable{_createLoadingState(e){e[wa]||(e[wa]=e=>this._loadingState(e))}_createNextAction(e){e[Da]||(e[Da]=()=>this.nextAction())}_loadingState(e){e?(this._currentActivateCount||(this._loadingTimeout&&clearTimeout(this._loadingTimeout),this._loadingTimeout=setTimeout((()=>{this._currentActivateCount&&this.isLoading(!0)}),100)),this._currentActivateCount++):(this._currentActivateCount--,this._currentActivateCount||(this._loadingTimeout&&clearTimeout(this._loadingTimeout),this.isLoading(!1)))}_callBeforeFinishHandler(e,t){this.events.call("beforeFinish",{state:e})}_callAfterFinishHandler(e,t){this.events.call("afterFinish",{state:e})}onFinish(){this._currentPage(null),this.iterator.dispose()}constructor(e,t){super(),this.pageFactory=e,this.events=new A.EventManager,this._loadingTimeout=null,this._currentActivateCount=0,this.isLoading=P.observable(!1),this._currentPage=P.observable(),this.isVisible=P.observable(!1),this._finishCallback=t,this._disposables.push(this.events)}initialize(e={},t=(e,t)=>new Pr(e,t)){this.events.call("beforeInitialize",{wizard:this,state:e}),this.stateManager=new Tr(e,this.pageFactory),this.iterator=t(this.pageFactory,this.stateManager),this.events.call("afterInitialize",{wizard:this})}isFirstPage(){return this._currentPage()&&this._currentPage().pageId==this.iterator.getNextPageId()}canNext(){return!this.isLoading()&&this._currentPage()&&this.pageFactory.getMetadata(this._currentPage().pageId).canNext(this._currentPage().page)}canFinish(){return!this.isLoading()&&this._currentPage()&&this.pageFactory.getMetadata(this._currentPage().pageId).canFinish(this._currentPage().page)}_initPage(e){return this.events.call("beforePageInitialize",vi(e,this)),this._createLoadingState(e.page),this._createNextAction(e.page),e.initialize(this.stateManager.getPageState(e.pageId))}start(){this.events.call("beforeStart",{wizard:this}),this._loadingState(!0);const e=this.iterator._getStartPage();this._initPage(e).done((()=>{this._currentPage(e),this.events.call("afterPageInitialize",Ci(e,this))})).always((()=>this._loadingState(!1))).fail((()=>{this.isVisible(!1)}))}canRunWizard(){return!0}nextAction(){if(!this.canNext())return;const e=this.iterator._getCurrentPage();this._loadingState(!0);const t=()=>this.iterator._getPreviousPage().always((()=>{this.iterator._resetPages(),this._loadingState(!1)}));e.commit().done((a=>{e.isChanged&&this.stateManager.setPageState(e.pageId,a),this.iterator._getNextPage().done((e=>{e?this._initPage(e).done((()=>{this._currentPage(e),this.events.call("afterPageInitialize",Ci(e,this)),this._loadingState(!1)})).fail((()=>t())):t()})).fail((()=>this._loadingState(!1)))})).fail((()=>this._loadingState(!1)))}previousAction(){this.isFirstPage()||(this._loadingState(!0),this.iterator._getPreviousPage().done((e=>{e&&this._currentPage(e)})).always((()=>this._loadingState(!1))))}goToPage(e){this._loadingState(!0),this.iterator._goToPage(e).done((e=>{e&&this._currentPage(e)})).always((()=>this._loadingState(!1)))}finishAction(){if(!this.canFinish())return;this._loadingState(!0);const e=this.iterator._getCurrentPage();e.commit().done((t=>{if(this.stateManager.setPageState(e.pageId,t),this.iterator._resetPages(),this._finishCallback){const e=this.stateManager.getCurrentState();this._callBeforeFinishHandler(e),this._finishCallback(e).done((t=>{this.onFinish(),this._callAfterFinishHandler(e,t),this.isVisible(!1)})).always((()=>{this._loadingState(!1)}))}else this._loadingState(!1),this.isVisible(!1)}))}}class wr extends Nr{static _getLoadPanelViewModel(e,t){const a=(0,f.$unwrap)(e).closest(".dxrd-wizard-content");return{animation:{show:{type:"fade",from:0,to:1,duration:700},hide:{type:"fade",from:1,to:0,duration:700}},deferRendering:!1,message:(0,A.getLocalization)("Loading...","AnalyticsCoreStringId.Loading"),visible:t,shading:!0,shadingColor:"transparent",position:{of:a},container:a}}constructor(e,t){super(e,t),this.height=P.observable(520),this.width=P.observable(690),this._extendCssClass="",this._container=f.getParentContainer,this.itemsProvider=P.observable(),this.nextButton={text:(0,A.getLocalization)("Next","AnalyticsCoreStringId.Wizard_Next"),disabled:P.computed((()=>!this.canNext())),onClick:()=>this.nextAction()},this.cancelButton={text:(0,A.getLocalization)("Cancel","AnalyticsCoreStringId.SearchDialog_Cancel"),onClick:()=>{this.iterator.dispose(),this.isVisible(!1)}},this.previousButton={text:(0,A.getLocalization)("Previous","AnalyticsCoreStringId.Wizard_Previous"),disabled:P.computed((()=>this.isFirstPage())),onClick:()=>this.previousAction()},this.finishButton={text:(0,A.getLocalization)("Finish","AnalyticsCoreStringId.Wizard_Finish"),type:"default",disabled:P.computed((()=>!this.canFinish())),onClick:()=>this.finishAction()},this._titleTemplate=(0,x.getTemplate)("dx-wizard-headerNew"),this.title="Popup Wizard"}start(){super.start(),this.isVisible(!0)}_wizardPopupPosition(e){return{of:(0,f.getParentContainer)(e)}}_loadPanelViewModel(e){return this._getLoadPanelViewModel(e,this.isLoading)}_getLoadPanelViewModel(e,t){return wr._getLoadPanelViewModel(e,t)}}class Dr extends it.TreeListController{constructor(e,t=new it.TreeListSearchOptions){super(),this._customizeDBSchemaTreeListActions=e,this.searchOptions=t}getActions(e){if(!e.data)return[];if(!(e.data instanceof ji||e.data instanceof Ui))return[];const t=e.data.getActions(e);return this._customizeDBSchemaTreeListActions&&this._customizeDBSchemaTreeListActions(e.data,t),t}canSelect(e){return!0}}class Er extends Ea{_addQueryAlgorithm(e,t,a,i){e.unChecked()&&"queries"!==t?e.children().forEach((e=>{Er._removeQuery(a.queries,e)})):(0===e.children().length&&this._itemsProvider().getItems(new A.PathRequest(t)),"tables"===t||"views"===t?this._addQueryFromTables(e,a):"procedures"===t?this._addQueryFromStoredProcedures(e,a):this._addQueryFromCustomQueries(e,i,a.queries))}_addQueryFromTables(e,t){for(let a=0;a<e.children().length;a++){const i=e.children()[a];if(i.unChecked())Er._removeQuery(t.queries,i);else{const e={},a={Columns:e,Tables:{SelectedTables:{Item1:{"@Name":i.name,"@ControlType":"Table","@ItemType":"Table"}}}};0===i.children().length&&this._itemsProvider().getItems(new A.PathRequest("tables."+i.name)).done((()=>{i.initializeChildren(i.children())}));for(let t=0;t<i.children().length;t++){const a=i.children()[t];a.checked()&&(e["Item"+(t+1)]={"@Table":i.name,"@Name":a.name,"@ItemType":"Column"})}Er._pushQuery(new bt(a,t),i,t.queries)}}}_addQueryFromStoredProcedures(e,t){for(let a=0;a<e.children().length;a++){const i=e.children()[a];if(i.checked()){const e=new St({"@Name":i.name,ProcName:i.name},t);i.arguments.forEach((t=>{e.parameters.push(new ct({"@Name":t.name,"@Type":se.GetType(t.type)},null,at(se.GetType(t.type))))})),Er._pushQuery(e,i,t.queries)}else Er._removeQuery(t.queries,i)}}_addQueryFromCustomQueries(e,t,a){for(let i=0;i<e.children().length;i++){const r=e.children()[i],s=(0,f.findFirstItemMatchesCondition)(t.peek(),(e=>r.name===(e.name()||e.generateName())));r.checked()&&(s.name(ba(a.peek(),s)),this._checkedQueries.push(s))}}_getItemsPromise(e){return this._itemsProvider().getItems(e)}_resetDataSourceResult(){this._customResetOptions(),this._dataSource().relations([]),this._dataSource().resultSet=null,this._onChange()}_setQueryCore(e){const t=this._fieldListModel().itemsProvider,a=this._queryEditIndex();if(a>=0)this._itemsProvider().queries().children()[a].name=e.name(),t.customQueries().splice(a,1,e);else{e.name(ba(t.customQueries().peek(),e)),t.customQueries().push(e),this._selectedPath("queries."+e.name());const a=this._itemsProvider().queries().children(),i=a[a.length-1];i.setChecked(!0),i._afterCheckToggled&&i._afterCheckToggled(i)}this._resetDataSourceResult()}static _pushQuery(e,t,a){(0,f.findFirstItemMatchesCondition)(a.peek(),(t=>t.name()===(e.name()||e.generateName())))||(e.name(ba(a.peek(),e)),a.push(e)),t.hasQuery=!0}static _removeQuery(e,t){if(t.hasQuery){let a=-1;e.peek().some(((e,i)=>(e.name()===t.name||e.generateName()===t.name)&&(a=i,!0)))&&e.splice(a,1),t.hasQuery=!1}}constructor(e){super(),this._options=e,this._selectedPath=P.observable(null),this._itemsProvider=P.observable(),this._customQueries=P.observableArray([]),this._checkedQueries=P.observableArray([]),this._sqlDataSourceWrapper=new $a(void 0,void 0,void 0),this._dataSource=()=>this._sqlDataSourceWrapper&&this._sqlDataSourceWrapper.sqlDataSource,this._dataConnection=()=>this._dataSource()&&this._dataSource().connection,this._showStatementPopup=e=>{this._popupSelectStatement.isVisible(!0),this._popupSelectStatement.query=e,this._popupSelectStatement.data(e.sqlString())},this._showQbCallBack=(e=null,t=!1)=>{if(null!==e){const t=(0,f.findFirstItemMatchesCondition)(this._customQueries(),(t=>e===(t.name()||t.generateName())));this._queryEditIndex(this._customQueries().indexOf(t)),t.type()===Fe.customSqlQuery?this._showStatementPopup(t):this._popupQueryBuilder.show(t,this._dataSource())}else if(this._queryEditIndex(-1),t)this._showStatementPopup(new pt({"@Name":null},this._dataSource()));else{const e=new bt({"@Name":null},this._dataSource());this._popupQueryBuilder.show(e,this._dataSource())}},this._popupSelectStatement={isVisible:P.observable(!1),title:()=>(0,A.getLocalization)("Custom SQL Editor","AnalyticsCoreStringId.SqlDSWizard_CustomSqlEditor"),query:null,data:P.observable(),okButtonText:()=>(0,A.getLocalization)("OK","DataAccessUIStringId.Button_OK"),okButtonHandler:e=>{this._popupSelectStatement.query.sqlString(e.model.data()),this._setCustomSqlQuery(this._popupSelectStatement.query),e.model.isVisible(!1)},aceOptions:mr(),aceAvailable:(0,it.aceAvailable)(),additionalOptions:gr((e=>{this._popupSelectStatement.data(e)})),languageHelper:{getLanguageMode:()=>"ace/mode/sql",createCompleters:()=>[]},closest:(e,t)=>(0,f.$dx)(e).closest(t)[0]},this._customResetOptions=()=>{},this._queryEditIndex=P.observable(-1),this.disableCustomSql=!0,this._scrollViewHeight="calc(100% - 37px)",this._customizeDBSchemaTreeListActions=null,this._isDataLoadingInProcess=P.observable(!1),this._callbacks=this._options.callbacks,this._sqlTextProvider=new ur(er(this._callbacks.selectStatement),this._dataConnection),this._popupQueryBuilder=new cr(((e,t)=>this._setTableQuery(e,t)),e.rtl,e.callbacks.customizeQBInitData),this._fieldListModel=P.observable(null),this._disposables.push(this._hasParametersToEdit=P.pureComputed((()=>this._itemsProvider().hasParametersToEdit()))),this._disposables.push(this._sqlDataSourceWrapper),this.disableCustomSql=this._options.disableCustomSql,this._getItemsAfterCheck=e=>{this._resetDataSourceResult(),e.checked.peek()&&e.isList?"tables"===e.name||"views"===e.name?this._itemsProvider().getItems(new A.PathRequest(e.name)).done((()=>{e.isList&&e.children.peek().length>0&&f.DxDeferred.when(e.children.peek().map((t=>this._getItemsPromise(new A.PathRequest(e.name+"."+t.name)))))})).always((()=>this._onChange())):"table"===e.specifics||"view"===e.specifics?this._itemsProvider().getItems(new A.PathRequest(e.specifics+"."+e.name)).always((()=>this._onChange())):"procedures"===e.name&&this._itemsProvider().getItems(new A.PathRequest(e.name)).always((()=>this._onChange())):this._onChange()}}canNext(){return!this._itemsProvider().nextButtonDisabled()&&this.canFinish()}canFinish(){return this._itemsProvider()&&this._itemsProvider().hasCheckedItems()&&!this._isDataLoadingInProcess()}_AddQueryWithBuilder(){}_runQueryBuilder(){}_loadPanelViewModel(e){return wr._getLoadPanelViewModel(e,this._isDataLoadingInProcess)}_setTableQuery(e,t){return t&&t(!0),this._sqlTextProvider.getQuerySqlText(e).done((()=>this._setQueryCore(e))).always((()=>{t&&t(!1)}))}_setCustomSqlQuery(e){this._setQueryCore(e)}_createTreeListFactory(){return new fi}commit(){this._dataSource().queries.removeAll(),this._checkedQueries.removeAll(),this._addQueryAlgorithm(this._itemsProvider().tables(),"tables",this._dataSource()),this._addQueryAlgorithm(this._itemsProvider().views(),"views",this._dataSource()),this._addQueryAlgorithm(this._itemsProvider().procedures(),"procedures",this._dataSource()),this._addQueryAlgorithm(this._itemsProvider().queries(),"queries",this._dataSource(),this._customQueries),P.utils.arrayPushAll(this._dataSource().queries(),this._checkedQueries()),this._dataSource().queries.valueHasMutated();const e=this._itemsProvider().queries().children().filter((e=>e.checked())).map((e=>e.name)),t=this._sqlDataSourceWrapper.sqlDataSource.queries().filter((t=>e.some((e=>e===t.name()))||t instanceof St));t.forEach((e=>this._sqlDataSourceWrapper.sqlDataSource.queries.remove(e)));const a=new A.ModelSerializer;return(0,f.createDeferred)().resolve({sqlDataSourceJSON:this._sqlDataSourceWrapper.save(),customQueries:t.map((e=>JSON.stringify(a.serialize(e))))}).promise()}initialize(e){this._sqlDataSourceWrapper=Ba(e,this._options.requestWrapper);const t=this._callbacks.customQueriesPreset?this._callbacks.customQueriesPreset(this._dataSource()):(new f.DxDeferred).resolve([]).promise(),a=(0,f.createDeferred)();return t.done((e=>{this._customQueries(e),this._selectedPath("");const t=new Cr(this._dataSource().dbSchemaProvider,this._customQueries,this._showQbCallBack,this.disableCustomSql,this._getItemsAfterCheck);this._disposables.push(t),this._itemsProvider(t),this._disposables.push(t.itemsRequestDeferreds.subscribe((e=>this._isDataLoadingInProcess(e.length>0)))),this._getItemsPromise(new A.PathRequest("queries"));const i=new it.TreeListSearchOptions;i.autoLoadItems=Na(),this._fieldListModel({factory:this._createTreeListFactory(),itemsProvider:this._itemsProvider(),loadChildItemsForCollapsedNodes:Na(),selectedPath:this._selectedPath,treeListController:new Dr(this._customizeDBSchemaTreeListActions,i),templateName:"dxrd-treelist-with-checkbox"}),this._popupQueryBuilder.isVisible(!1),a.resolve()})).fail(a.reject),a.promise()}}function Ir(e,t){e.registerMetadata(Sa.MultiQueryConfigurePage,{create:()=>new Er(t),setState:(e,t)=>{t.sqlDataSourceJSON=e.sqlDataSourceJSON,t.customQueries=e.customQueries},getState:e=>e.sqlDataSourceWizard,resetState:(e,t)=>{e.sqlDataSourceJSON=t.sqlDataSourceJSON,e.customQueries=t.customQueries},description:(0,A.getLocalization)("Columns selected from specific tables and/or views will be automatically included into a separate query.","AnalyticsCoreStringId.SqlDSWizard_PageConfigureMultiQuery"),template:"dxrd-wizard-add-queries-page"})}class Ar extends A.Disposable{constructor(e){super(),this.editor=x.editorTemplates.getEditor("commonCollection"),this.isList=!1,this.contenttemplate="dx-treelist-accordion-contenttemplate-custom-with-actions",this.actionsTemplate="dx-treelist-item-actions",this.dataSourceParameter=P.observable(e),this._name=e.name,this._displayName=e.displayName}get name(){return this._name()}get displayName(){return this._displayName||this.name}}class kr extends Ar{constructor(e,t){super(e),this.parent=t}query(){return this.parent.query()}}class zr{constructor(e){this.isList=!0,this.specifics="List",this.visible=P.observable(!0),this.name=this.displayName=e,this.parameters=P.observableArray([])}removeChild(e){this.parameters.remove(e)}}class $r extends zr{constructor(e){super(e.name()),this._query=e}query(){return this._query}}class Or extends it.TreeListController{constructor(e,t){super(),this._createNewParameter=t,this._rootItems=e}hasItems(e){return e.isList}getActions(e){const t=[];if(!e.data)return t;if(e.data.query().type()===Fe.storedProcQuery)return e.data.isList?[]:[it.treeListEditAction];if(e.data.isList){const a=e.data;t.push({clickAction:()=>a.parameters.push(new kr(this._createNewParameter(a.name,a.parameters()),a)),imageClassName:"dxrd-image-add",imageTemplateName:"dxrd-svg-operations-add",text:(0,A.getLocalization)("Add parameter","AnalyticsCoreStringId.FieldListActions_AddParameter")})}else{const a=e.data;t.push({clickAction:()=>{a.parent.removeChild(a)},imageClassName:"dxrd-image-recycle-bin",imageTemplateName:"dxrd-svg-operations-recycle_bin",text:(0,A.getLocalization)("Remove parameter","DataAccessUIStringId.Button_Remove")}),t.push(it.treeListEditAction)}return t}canSelect(e){return!0}}function Br(e,t){return e.type()===Fe.tableQuery||e.type()===Fe.customSqlQuery?t.indexOf(e)>-1:e.type()===Fe.storedProcQuery&&e.parameters().length>0}class Rr extends Ea{_isParametersValid(){return this._getParameters().every((e=>e.isValid()))}canNext(){return this._isParametersValid()&&this._sqlDataSourceWrapper.sqlDataSource.queries().length>1}canFinish(){return this._isParametersValid()&&this._sqlDataSourceWrapper.sqlDataSource.queries().length>=1}constructor(e={createParameterViewModel:e=>e,getParameterFromViewModel:e=>e},t){super(),this.parametersConverter=e,this._requestWrapper=t,this._sqlDataSourceWrapper=new $a(void 0,void 0,this._requestWrapper),this._selectedPath=P.observable(null),this._rootItems=P.observableArray(),this._createNewParameter=(e,t)=>{const a=new ct({"@Name":(0,f.getUniqueNameForNamedObjectsArray)(t,"parameter"),"@Type":f.DotnetTypes.SystemInt32});return this._selectedPath(e+"."+a.name()),this.parametersConverter.createParameterViewModel(a)},this._scrollViewHeight="100%",this._fieldListModel=P.observable(null),this._removeButtonTitle=(0,A.getLocalization)("Remove","DataAccessUIStringId.Button_Remove");const a=()=>this._onChange();this._disposables.push(yi(this._rootItems,(e=>{this._disposables.push(yi(e.parameters,(e=>{this._disposables.push(_i(e.dataSourceParameter,(e=>{this._disposables.push(...Si([e.name,e.value,e.type],a))}),a))}),a))}),a)),this._parametersEditorOptions={addHandler:()=>this.parametersConverter.createParameterViewModel(new ct({"@Name":(0,f.getUniqueNameForNamedObjectsArray)(this._parametersEditorOptions.values.peek().peek(),"parameter"),"@Type":f.DotnetTypes.SystemInt32})),values:P.observable(P.observableArray([])),displayName:"Parameters",level:0,info:P.observable({displayName:"Parameters",localizationId:"DevExpress.DataAccess.Sql.SqlQuery.Parameters",propertyName:"parameters",modelName:"Parameter",array:!0,editor:x.editorTemplates.getEditor("commonCollection"),template:"#dxrd-parameter-collection-item"}),editorTemplate:"#dxrd-wizard-datasource-parameters",hideButtons:P.observable(!1),collapsed:!1}}_getParameters(){return[].concat.apply([],(this._rootItems()||[]).map((e=>e.parameters().map((e=>e.dataSourceParameter())))))}initialize(e){const t=[],a=this._rootItems();return this._sqlDataSourceWrapper=Ba(e,this._requestWrapper),this._sqlDataSourceWrapper.sqlDataSource.queries().forEach((e=>{if(Br(e,this._sqlDataSourceWrapper.customQueries)){const a=new $r(e);a.parameters(e.parameters().map((e=>new kr(this.parametersConverter.createParameterViewModel(e),a)))),t.push(a)}})),a.filter((e=>t.every((t=>t.name!==e.name)))).forEach((e=>{a.splice(this._rootItems().indexOf(e),1)})),t.forEach((e=>{const t=a.filter((t=>t.name===e.name))[0];t?e.parameters().filter((e=>t.parameters().every((t=>t.name!==e.name)))).forEach((e=>{t.parameters.push(e)})):a.push(e)})),this._rootItems.valueHasMutated(),this._fieldListModel({itemsProvider:{getItems:e=>{const t=new f.DxDeferred;if(e.fullPath){const a=(0,f.findFirstItemMatchesCondition)(this._rootItems(),(t=>t.name===e.ref));t.resolve(a.parameters())}else t.resolve(this._rootItems());return t.promise()}},templateName:"dx-treelist-item-with-hover",selectedPath:this._selectedPath,treeListController:new Or(this._rootItems(),this._createNewParameter)}),(0,f.createDeferred)().resolve().promise()}commit(){return this._rootItems().forEach((e=>{(0,f.findFirstItemMatchesCondition)(this._sqlDataSourceWrapper.customQueries,(t=>t.name()===e.query().name())).parameters(e.parameters().map((e=>this.parametersConverter.getParameterFromViewModel(e.dataSourceParameter()))))})),(0,f.createDeferred)().resolve({sqlDataSourceJSON:this._sqlDataSourceWrapper.sqlDataSourceJSON,customQueries:this._sqlDataSourceWrapper.saveCustomQueries()}).promise()}}function Mr(e,t,a){e.registerMetadata(Sa.MultiQueryConfigureParametersPage,{create:()=>new Rr(a,t),getState:e=>e.sqlDataSourceWizard,setState:(e,t)=>t.customQueries=e.customQueries,resetState:()=>{},description:(0,A.getLocalization)("Configure query parameters.","AnalyticsCoreStringId.SqlDSWizard_PageConfigureParameters"),template:"dxrd-configure-query-parameters-page"})}class qr{_updateActions(e){this._popupService.subscription=this._popupService.visible.subscribe((e=>{this.target.isSelected&&this.target.isSelected(e)})),this._popupService.data({data:this._popupItems,template:"dx-filtereditor-popup-common",click:t=>{e[this._action](t),this._popupService.visible(!1)}})}constructor(e,t,a,i){this.showPopup=(e,t)=>{this._popupService.subscription&&this._popupService.subscription.dispose(),this._popupService.title(""),this._updateActions(this.target),this._popupService.target(t),this._popupService.visible(!0)},this.target=e,this._action=a,this._popupService=t,this._popupItems=i}}class Lr{constructor(e,t,a=!1){this._isMaster=a,this.getTitle=()=>this._isMaster?(0,A.getLocalization)("Master Query","AnalyticsCoreStringId.Wizard_MasterDetailRelationship_MasterQuery"):(0,A.getLocalization)("Detail Query","AnalyticsCoreStringId.Wizard_MasterDetailRelationship_DetailQuery"),this.isSelected=P.observable(!1),this.selectColumnText=()=>(0,A.getLocalization)("<Select a Column>","DataAccessUIStringId.JoinEditorEmptyColumnText"),this.column=e,this.queryName=t,this._setColumn=e=>{this.column(P.unwrap(e.name))}}}class Qr{constructor(e,t){this.isEditable=P.observable(!1),this.relationName=e.name,this.keyColumns=P.pureComputed((()=>e.keyColumns().map((t=>({master:new Lr(t.masterColumn,e.masterQuery(),!0),detail:new Lr(t.detailColumn,e.detailQuery(),!1)}))))),this.create=()=>{e.createKeyColumn()},this.remove=a=>{e.keyColumns.remove((e=>e.masterColumn===a.master.column&&e.detailColumn===a.detail.column)),0===e.keyColumns().length&&t.remove(this)}}}class Vr{constructor(e,t){this.relations=P.observableArray(),this.queryName=e,this.add=e=>{this.relations.push(new Qr(e,this))},this.create=e=>{const a=new mt({"@Master":this.queryName,"@Detail":e.name});(0,f.getFirstItemByPropertyValue)(this.relations(),"relationName",a.name())&&a.name((0,f.getUniqueName)(this.relations().map((e=>e.relationName())),a.name()+"_")),a.createKeyColumn(),this.add(a),t.push(a)},this.remove=e=>{this.relations.remove(e),t.remove((t=>t.name===e.relationName))}}}class Wr extends it.PopupEditorBase{constructor(e,t,a){super(),this.isValid=P.observable(!0),this.loadPanelVisible=P.observable(!1),this.masterQueries=P.observableArray(),this.popupService=new f.PopupService,this.save=()=>{e().some((e=>!e.detailQuery()||!e.masterQuery()||e.keyColumns().some((e=>!e.detailColumn()||!e.masterColumn()))))?(0,f.ShowMessage)((0,A.getLocalization)("Some fields are empty. Please fill all empty fields or remove the corresponding conditions to proceed.","DataAccessUIStringId.JoinEditorFillAllFieldsException")):a().done((()=>{this.popupVisible(!1)}))},this.createRelation=e=>{const a=t.tables().filter((t=>t.tableName()!==e.queryName)).map((e=>({name:e.tableName()})));return{data:new qr(e,this.popupService,"create",a),templateName:"dx-filtereditor-create"}},this.setColumn=e=>{const a=(0,f.getFirstItemByPropertyValue)(t.tables(),"tableName",e.queryName);return{data:new qr(e,this.popupService,"_setColumn",a?a.columns():[]),templateName:"dx-masterdetail-editor-setColumn"}},this._createMainPopupButtons();const i={};t.tables().forEach((t=>{i[t.tableName()]=new Vr(t.tableName(),e)})),e().forEach((t=>{i[t.masterQuery()]=i[t.masterQuery()]||new Vr(t.masterQuery(),e),i[t.masterQuery()].add(t)})),this.masterQueries(Object.keys(i).map((e=>i[e])))}title(){return(0,A.getLocalization)("Master-Detail Relation Editor",f.StringId.MasterDetailRelationsEditor)}}class Fr extends Ea{_getResultSet(e){const t=(0,f.createDeferred)();return e.resultSet?t.resolve(e.resultSet):this._getResultSchema(e).done((e=>{t.resolve(new ve(JSON.parse(e.resultSchemaJSON)))})).fail((e=>{t.reject(e)})),t.promise()}_dataSource(){return null}_restoreDataSource(e){}_updateRelations(){const e=this._relations();e.forEach(((t,a)=>{const i=this._resultSet.tables().filter((e=>e.tableName()===t.detailQuery()))[0],r=this._resultSet.tables().filter((e=>e.tableName()===t.masterQuery()))[0];if(!i||!r)return void e.splice(a,1);const s=t.keyColumns();s.forEach(((e,t)=>{(i.columns().every((t=>t.name()!==e.detailColumn()))||r.columns().every((t=>t.name()!==e.masterColumn())))&&s.splice(t,1)})),0===s.length&&e.splice(a,1)})),this._relations.valueHasMutated()}constructor(e){super(),this._getResultSchema=e,this._relations=P.observableArray([]),this._customResetOptions=()=>{},this._relationsEditor=P.observable(null);const t=()=>this._onChange();this._disposables.push(yi(this._relations,(e=>{e._disposables.push(...Si([e.detailQuery,e.name,e.masterQuery],t)),e._disposables.push(yi(e.keyColumns,(a=>{e._disposables.push(...Si([a.detailColumn,a.masterColumn],t))}),t))}),t))}canNext(){return!1}canFinish(){return this._relations().every((e=>e.keyColumns().every((e=>!!e.detailColumn()&&!!e.masterColumn()))))}initialize(e){return this.relationsSubscription&&this.relationsSubscription.dispose(),this._restoreDataSource(e),this._disposables.push(this.relationsSubscription=this._relations.subscribe((e=>{e.some((e=>!e.moved&&0!==e.moved))&&this._customResetOptions()}),null,"arrayChange")),this._getResultSet(this._dataSource()).done((e=>{this._resultSet=e,this._updateRelations(),this._relationsEditor(new Wr(this._relations,this._resultSet,(()=>{})))})).fail((e=>{(0,f.getErrorMessage)(e)&&(0,f.ShowMessage)((0,f.getErrorMessage)(e))}))}dispose(){super.dispose(),this.disposeObservableArray(this._relations)}}class jr extends Fr{constructor(){super(...arguments),this._customResetOptions=()=>{},this._relationsEditor=P.observable(null)}_restoreDataSource(e){this._sqlDataSourceWrapper=Ba(e)}_dataSource(){return this._sqlDataSourceWrapper.sqlDataSource}commit(){this.relationsSubscription.dispose(),this._sqlDataSourceWrapper.sqlDataSource.relations(this._relations()),this._sqlDataSourceWrapper.sqlDataSource.resultSet=this._resultSet;const e=new A.ModelSerializer;return(0,f.createDeferred)().resolve({sqlDataSourceJSON:this._sqlDataSourceWrapper.sqlDataSourceJSON,customQueries:this._sqlDataSourceWrapper.saveCustomQueries(),relations:this._relations().map((t=>JSON.stringify(e.serialize(t))))}).promise()}}function Ur(e,t){e.registerMetadata(ya.ConfigureMasterDetailRelationshipsPage,{create:()=>new jr(t),setState:(e,t)=>{t.relations=e.relations},getState:e=>e.sqlDataSourceWizard,resetState:(e,t)=>{e.relations=t.relations},description:(0,A.getLocalization)("Configure master-detail relationships.","AnalyticsCoreStringId.SqlDSWizard_PageConfigureMasterDetailRelations"),template:"dxrd-wizard-configure-relations-page"})}const Hr=[At,$t];class Jr extends Xt{constructor(e,t){super({"@ControlType":"Column"},e,t);const a=e.parentModel(),i=P.pureComputed((()=>(0,f.findFirstItemMatchesCondition)(a.columns(),(t=>e.actualName()===t.table()&&sr.AllColumns===t.itemType()))));this.selected=P.pureComputed({read:()=>!!i(),write:r=>{!!i()!==r&&(r?a.columns.push(new nr({"@ItemType":sr.AllColumns,"@Table":e.actualName()},a,t)):a.columns.remove((t=>e.actualName()===t.table()&&sr.AllColumns===t.itemType())))}}),this.name=P.pureComputed(Jr.DisplayName),this._disposables.push(this.name)}getInfo(){return Hr}}Jr.DisplayName=()=>{const e=(0,A.getLocalization)("(All Columns)","DataAccessStringId.QueryBuilder_AllColumns");return"*"===e.charAt(0)?e:"* "+e};const Gr={ReadWrite:"ReadWrite",Read:"Read",Disabled:"Disabled"};function Xr(e){return e.aggregate()!==ir.None}class Yr{constructor(e,t){this.getItems=a=>{const i=new f.DxDeferred;let r=[];if(""===a.fullPath)r=t.filterTables(e().tables()).map(Yr._createTableInfo);else{const i=(0,f.findFirstItemMatchesCondition)(e().tables(),(e=>e.actualName()===a.fullPath));r=t.filterColumns(i&&i.columns()||[]).map((e=>Yr._createColumnInfo(e,t)))}return i.resolve(r),i.promise()},this.hasParameter=t=>e().parameters().filter((e=>e.name()===t)).length>0,this.getColumnInfo=a=>{if(a){const i=(0,f.find)(e().tables(),(e=>0===a.indexOf(e.actualName()+".")));if(i){const e=(0,f.find)(t.filterColumns(i.columns()||[]),(e=>a===i.actualName()+"."+t.getColumnName(e)));return e?Yr._createColumnInfo(e,t):null}}return null}}static _createTableInfo(e){return{displayName:e.actualName(),name:e.actualName(),isList:!0,specifics:"Default",collapsed:P.observable(!0)}}static _createColumnInfo(e,t){return{displayName:t.getColumnName(e),isList:!1,specifics:t.getSpecifics(e),dataType:t.getDataType(e),name:t.getColumnName(e)}}}Yr.whereClauseObjectsFilter={filterColumns:e=>e,filterTables:e=>e,getColumnName:e=>e.name.peek(),getSpecifics:e=>e.specifics,getDataType:e=>e.dataType.peek()},Yr.groupByObjectsFilter={filterColumns:e=>e.filter(Xr),filterTables:e=>e.filter((e=>e.columns().some(Xr))),getColumnName:e=>e.actualName.peek(),getSpecifics:e=>{switch(e.aggregate()){case ir.Avg:case ir.AvgDistinct:return"Float";case ir.Count:case ir.CountDistinct:return"Integer";default:return e.specifics}},getDataType:e=>null};class Kr extends it.FilterEditorSerializer{_columnDisplayName(e){return e.table()+"."+e.actualName()}_findAggregatedColumn(e){return(0,f.find)(this._columns(),(t=>Xr(t)&&e(t)))}constructor(e){super(),this._columns=e,this._aggregatePropertyName=e=>{const t=this._findAggregatedColumn((t=>t.actualName()===e.propertyName));return t?this._columnDisplayName(t):e.propertyName}}serializeOperandProperty(e){const t=this._findAggregatedColumn((t=>e.propertyName===this._columnDisplayName(t)));return"["+(t?t.actualName():e.propertyName)+"]"}deserialize(e){const t=Jt.CriteriaOperatorStateMachine.parse(e);return t&&(0,Jt.criteriaForEach)(t,(e=>{e instanceof Ht.OperandProperty&&(e.propertyName=this._aggregatePropertyName(e))})),super.deserializeOperand(t)}}const Zr=DevExpress.Analytics.Widgets.Filtering;class es extends ct{getEditorType(e){return(0,f.IsDataAccessExpression)(e)?{header:"dxqb-expressionstring"}:(0,f.getEditorType)(e)}}class ts extends Zr.OperandParameterSurface{get _parameterType(){return this.fieldsOptions()&&this.fieldsOptions().selected()&&this.fieldsOptions().selected().dataType||f.DotnetTypes.SystemString}constructor(e,t,a,i){super(e,t,a,i),this.createParameter=()=>{this.canCreateParameters&&(this.model.parameterName=this.parameterName(),this._createParameter(this.parameterName(),this._parameterType),this.helper.onChange())},this._parameterName=P.observable(""),this.isEditable=P.observable(!1),this.defaultDisplay=ts.defaultDisplay,this._parameterName(P.unwrap(e.parameterName)),this.canCreateParameters=this.helper.canCreateParameters,this.fieldsOptions=t.leftPart.fieldsOptions,this.parameterName=P.pureComputed({read:()=>this._parameterName()||this.canCreateParameters&&ts.defaultDisplay()||"",write:e=>{e!==ts.defaultDisplay()&&e&&(this.model.parameterName=P.unwrap(e),this._parameterName(this.model.parameterName))}}),!this.canCreateParameters||this.isDefaultTextDisplayed()||P.unwrap(this.fieldListProvider).hasParameter(e.parameterName)||this.createParameter()}_createParameter(e,t){if(""!==e&&e!==ts.defaultDisplay()&&0===this.helper.parameters().filter((t=>t.name()===e)).length){const a=new es({"@Name":e,"@Type":t});this.helper.newParameters.push(a)}}isDefaultTextDisplayed(){return this.parameterName()===ts.defaultDisplay()}}ts.defaultDisplay=()=>(0,A.getLocalization)("Create new parameter","AnalyticsCoreStringId.FilterEditor_Operand_CreateNewParameter");class as extends Zr.OperandPropertySurface{_updateSpecifics(){as.updateSpecifics(this)}constructor(e,t,a,i){super(e,t,a,i)}static updateSpecifics(e){const t=P.unwrap(e.fieldListProvider);if(t&&t.getColumnInfo){const a=t.getColumnInfo(e.propertyName());if(a){e.specifics(a.specifics.toLowerCase()),e.dataType(a.dataType);const t=P.unwrap(e.fieldsOptions);t&&t.selected(a)}}}}class is extends x.FilterEditorHelper{constructor(e){super(),this.handlers.changeParameter=(e,t)=>({data:new it.FilterEditorAddOn(e,t,"changeParameter","items","dxqb-filtereditor-parameterspopup"),templateName:"dxqb-filtereditor-changeparameter"}),this.mapper.parameter=ts,this.mapper.property=as,e===Gr.ReadWrite&&(this.canCreateParameters=!0,this.newParameters=P.observableArray([]),this.onEditorFocusOut=e=>{if(!e)return;const t=this.newParameters(),a=[];if((0,Jt.criteriaForEach)(e,(e=>{if(e instanceof Ht.OperandParameter){const i=t.filter((t=>t.name()===e.parameterName))[0];i&&a.push(i)}})),0===a.length)return void this.newParameters.splice(0);const i=t.filter((e=>-1===a.indexOf(e)));for(let e=0;e<i.length;e++){const a=t.indexOf(i[e]);-1!==a&&t.splice(a,1)}this.newParameters.valueHasMutated()},this.onClosing=()=>{this.newParameters([])}),this.canSelectLists=!1,this.getDisplayPropertyName=()=>(new f.DxDeferred).resolve("").promise()}}let rs=is;function ss(e){rs=e}class os extends x.FilterStringOptions{constructor(e,t,a,i){super(e,t,a,i)}initializeFilterStringHelper(e,t,a){const i=new rs(t);if(i.canChoiceParameters=t!==Gr.Disabled,a&&(i.serializer=a),t===Gr.ReadWrite)i.parameters=P.computed((()=>[].concat(e(),i.newParameters()))),i.onSave=t=>{const a=i.newParameters();e.push.apply(e,a),i.newParameters([])};else{t===Gr.Read&&(i.parameters=e);const a=i.criteriaTreeValidator._checkRightPart;i.criteriaTreeValidator._checkRightPart=i=>{if(!(i instanceof Ht.OperandParameter))return a.apply(this,[i]);if(t===Gr.Disabled)return!1;if(t===Gr.Read){const t=i.parameterName;return 0!==e.peek().filter((e=>e.name()===t)).length}}}this.helper=i}}const ns=[{propertyName:"_tablesObject",modelName:"Tables",info:[{propertyName:"tables",modelName:"SelectedTables",array:!0},{propertyName:"relations",modelName:"Relations",array:!0}]},{propertyName:"parameters",modelName:"Parameters",array:!0},{propertyName:"type",modelName:"@Type"},{propertyName:"name",modelName:"@Name"},{propertyName:"editableName",displayName:"Name",localizationId:"DevExpress.DataAccess.Sql.SqlQuery.Name",editor:It.getEditor("text")},{propertyName:"_filterString",modelName:"Filter",defaultVal:""},{propertyName:"filterString",defaultVal:"",displayName:"Filter",localizationId:"DataAccessUIStringId.FiltersView_Filter",editor:It.getEditor("filterEditor")},{propertyName:"_groupFilterString",modelName:"GroupFilter",defaultVal:""},{propertyName:"groupFilterString",defaultVal:"",displayName:"Group Filter",localizationId:"DataAccessUIStringId.FiltersView_GroupFilter",editor:It.getEditor("filterGroupEditor")},{propertyName:"columns",modelName:"Columns",array:!0},{propertyName:"sorting",modelName:"Sorting",array:!0},{propertyName:"grouping",modelName:"Grouping",array:!0},{propertyName:"itemType",modelName:"@ItemType"},{propertyName:"allColumnsInTablesSelected",displayName:"Select All (*)",localizationId:"AnalyticsCoreStringId.QueryBuilder_SelectAll",editor:It.getEditor("bool")},{propertyName:"top",modelName:"@Top",displayName:"Select Top",defaultVal:0,from:A.floatFromModel,localizationId:"AnalyticsCoreStringId.QueryBuilder_SelectTop",editor:It.getEditor("numeric"),editorOptions:{format:"#0",min:0}},{propertyName:"skip",modelName:"@Skip",displayName:"Offset",defaultVal:0,from:A.floatFromModel,localizationId:"AnalyticsCoreStringId.QueryBuilder_Offset",editor:It.getEditor("numeric"),editorOptions:{format:"#0",min:0}},{propertyName:"distinct",modelName:"@Distinct",defaultVal:!1,from:A.parseBool,displayName:"Select distinct",localizationId:"AnalyticsCoreStringId.QueryBuilder_SelectDistinct",editor:It.getEditor("bool")}];class ds extends Xt{_isAliasAutoGenerated(e){if(e&&this.alias()&&0===this.alias().indexOf(e+"_")&&this.alias().substring(e.length+1)===this.name())return!0;if(!this.alias()||0!==this.alias().indexOf(this.name()+"_"))return!1;let t=this.alias().substring(this.name().length+1);return t.match(new RegExp("_[0-9]+$"))&&(t=t.substring(0,t.indexOf("_"))),Object.keys(ir).indexOf(t)>0}getInfo(){return rr}constructor(e,t,a,i){super((0,f.extend)({"@ControlType":"Column"},e),a,i),this.isNotAvailable=P.observable(!1),this._type=P.observable(ie.Unknown),this._size=P.observable(""),t.size&&this._size(t.size),t.type&&this._type(t.type),this.displayType=P.pureComputed((()=>ie[this._type()]+(this._size()?"("+this._size()+")":""))),this.dataType=P.pureComputed((()=>se.GetType(this._type()))),this.actualName=P.pureComputed((()=>this.alias()||this.name()));const r=a.getColumnConnectionPoints(this);this.rightConnectionPoint={side:P.observable(D.East),location:r.right},this.leftConnectionPoint={side:P.observable(D.West),location:r.left};const s=a.parentModel(),o=P.pureComputed((()=>dr.find(s.columns,a.actualName(),this.name())));this.aggregate=P.pureComputed({read:()=>o()?o().aggregate():ir.None,write:e=>{o()&&o().aggregate(e)}}),this.alias=P.pureComputed({read:()=>o()?o().alias():"",write:e=>{o()&&o().alias(e||null)}});const n=P.pureComputed((()=>dr.find(s.sorting,a.actualName(),this.name())));this.sortingType=P.computed({read:()=>n()?n().descending()?"Descending":"Ascending":"Unsorted",write:e=>{"Unsorted"!==e?n()?n().descending("Descending"===e):dr.addNew(s,s.sorting,a.actualName(),this.name()).descending("Descending"===e):n()&&dr.remove(s.sorting,a.actualName(),this.name())}}),this.sortOrder=P.computed({read:()=>{const e=s.sorting().indexOf(n());return e<0?void 0:e+1},write:e=>{if(!n())return;e=Math.min(e,s.sorting().length),e=Math.max(e,1);const t=s.sorting().indexOf(n()),a=s.sorting.splice(t,1);s.sorting.splice(e-1,0,a[0])}});const d=P.computed((()=>dr.find(s.grouping,a.actualName(),this.name())));this.aggregate.subscribe((e=>{const t=this.parentModel();if(e!==ir.None){if(this.groupBy(!1),!this.alias()||this._isAliasAutoGenerated(t.actualName())){const t=this.name()+"_"+e;this.alias(dr.setUniqueAlias(s.columns,t))}}else this._isAliasAutoGenerated(t.actualName())&&this.alias(null)})),this.groupBy=P.computed({read:()=>!!d(),write:e=>{e?(dr.addNew(s,s.grouping,a.actualName(),this.name()),this.aggregate(ir.None)):dr.remove(s.grouping,a.actualName(),this.name())}}),this.toggleSelected=(e,t=!1)=>{!!o()!==e&&(e?dr.addNew(s,s.columns,a.actualName(),this.name(),t):(dr.remove(s.columns,a.actualName(),this.name(),t),this.groupBy(!1)))},this.selected=P.pureComputed({read:()=>!!o(),write:e=>this.toggleSelected(e)})}isPropertyDisabled(e){if(this.isNotAvailable())return!0;if("sortOrder"===e)return"Unsorted"===this.sortingType();if("aggregate"===e||"alias"===e)return!this.selected();if("groupBy"===e){const e=this.root;return this.aggregate()&&1===e.aggregatedColumnsCount()}return!1}get specifics(){return se.GetSpecific(this.dataType())}}const ls=[At,kt,{propertyName:"controlType",modelName:"@ControlType"},{propertyName:"itemType",modelName:"@ItemType"}];class cs extends Xt{constructor(e,t,a){super(e,t,a),this.serializer=a,this._columnsConnectionPointLeftX=P.pureComputed((()=>this.location.x())),this._columnsConnectionPointRightX=P.pureComputed((()=>this.location.x()+this.size.width())),this._columns=P.observableArray(),this._initialized=P.observable(!1),this.tableOffset=P.observable(0),this.size=new b.Size(199,123),this.location=new b.Point(0,0),this.isReady=P.observable(!1),this.allColumnsSelected=P.computed({read:()=>{const e=this.columns().filter((e=>e.selected()));return 0!==e.length&&e.length===this._columns.peek().length},deferEvaluation:!0}),this.isInitialized=P.pureComputed((()=>this._initialized())),this.itemType="Table",this.controlType="Table",this._disposables.push(this.size.height=P.pureComputed({read:()=>0===this._columns().length?cs.TABLE_DEFAULT_HEIGHT+this.tableOffset():cs.COLUMNS_OFFSET+(cs.COLUMN_HEIGHT+cs.COLUMN_MARGIN)*(this._columns().length+1)+this.tableOffset(),write:()=>{}})),this.asterisk=new Jr(this,this.serializer),this._disposables.push(this.actualName=P.pureComputed((()=>this.alias()||this.name())))}columns(){return this._columns()}toggleSelectedColumns(){const e=!this.allColumnsSelected.peek(),t=this.parentModel();this._columns.peek().forEach((t=>t.toggleSelected(e,!0))),t.columns.valueHasMutated()}getColumnConnectionPoints(e){const t=P.pureComputed({read:()=>{const t=this._columns.indexOf(e)+1;return this.location.y()+cs.COLUMNS_OFFSET+cs.COLUMN_MARGIN*t+cs.COLUMN_HEIGHT*(t+.5)+this.tableOffset()},deferEvaluation:!0});return this._disposables.push(t),{left:{x:this._columnsConnectionPointLeftX,y:t},right:{x:this._columnsConnectionPointRightX,y:t}}}getInfo(){return ls}getInvalidColumns(){return this.columns().filter((e=>e.isNotAvailable()&&e.selected()))}getColumn(e){return(0,f.getFirstItemByPropertyValue)(this._columns(),"name",e)}_initColumns(e,t=!1){const a=[];e.forEach((e=>{a.push(this.createChildColumn(e))})),this._columns(t?[].concat([],this.columns(),a):a)}createChildColumn(e){return new ds({"@Name":e.name},e,this,this.serializer)}createColumns(e){let t=0;this.columns().forEach((a=>{const i=e.columns.filter((e=>e.name===a.name()))[0];i?(a._type(i.type),a._size(i.size)):(a.isNotAvailable(e.columns.every((e=>e.name!==a.name()))),t++)})),this.columns().length-t<e.columns.length&&this._initColumns(e.columns.filter((e=>this.columns().every((t=>t.name()!==e.name)))),!0),this._initialized(!0)}}cs.COLUMNS_OFFSET=37,cs.COLUMN_HEIGHT=32,cs.COLUMN_MARGIN=1,cs.TABLE_MIN_WIDTH=80,cs.TABLE_DEFAULT_HEIGHT=136;class us extends Xt{_initializeTable(e){this.dbSchemaProvider.getDbTable(e.name()).done((t=>{e.createColumns(t)}))}_addColumnsToTable(e,t){e._initColumns(t.map((e=>new se({Name:e.column()}))))}constructor(e,t,a=Gr.ReadWrite,i,r){super(e,null,r),this.topOffset=65,this._findAncestorsRelations=e=>{const t={inner:0,outer:0,relations:[]};return this.relations().forEach((a=>{if(a.nestedTable()===e){t.relations.push(a),"LeftOuter"===a.joinType()?t.outer++:t.inner++;const e=this._findAncestorsRelations(a.parentTable());t.inner+=e.inner,t.outer+=e.outer,t.relations.push.apply(t.relations,e.relations)}})),t},this.aggregatedColumnsCount=P.observable(0),this.defaultPageHeight=500,this.defaultPageWidth=500,this.dbSchemaProvider=t,this.onSave=i,this.editableName=P.observable(this.name()),this._disposables.push(this.name=P.pureComputed({read:this.editableName,write:e=>{}}));const s=P.observable(0);this._disposables.push(this.pageWidth=P.pureComputed({read:()=>{let e=this.defaultPageWidth;return this.tables().forEach((t=>{const a=t.location.x()+t.size.width();a>e&&(e=a)})),Math.max(s(),e)},write:e=>{s(e)}})),this._disposables.push(this.pageHeight=P.pureComputed((()=>{let e=this.defaultPageHeight;return this.tables().forEach((t=>{const a=t.location.y()+t.size.height();a>e&&(e=a+ps.pageMargin)})),e}))),this.margins=b.Margins.fromString(),this._disposables.push(this.isValid=P.pureComputed((()=>this._validate())));const o=e=>!e.table()&&e.itemType()===sr.AllColumns;this._disposables.push(this.allColumnsInTablesSelected=P.pureComputed({read:()=>this.columns().some(o),write:e=>{e?this.columns.push(new nr({"@ItemType":"AllColumns"},this,r)):this.columns.remove(o)}})),this.sorting||(this.sorting=P.observableArray([])),this.grouping||(this.grouping=P.observableArray([]))}dispose(){super.dispose(),this.onSave=null}addChild(e){if(e instanceof Zt){if(this.relations.indexOf(e)>-1)return;e.parentModel(this),this.relations.push(e)}else if(e instanceof cs){if(this.tables.indexOf(e)>-1)return;e.parentModel(this),null!==(0,f.getFirstItemByPropertyValue)(this.tables(),"actualName",e.name())&&e.alias((0,f.getUniqueName)(this.tables().map((e=>e.actualName())),e.name()+"_")),this.tables.push(e)}else(0,f.NotifyAboutWarning)("Attempt to add wrong child control.")}removeChild(e){if(e instanceof Zt){if(this.relations().length<1)return;const t=e,a=this.relations().indexOf(t);for(;t.conditions().length>0;)t.conditions.pop();this.relations.splice(a,1)}else if(e instanceof cs){if(this.tables().length<1)return;this.tables.splice(this.tables().indexOf(e),1);const t=this.relations();for(let a=t.length-1;a>-1;a--)t[a].parentTable()!==e&&t[a].nestedTable()!==e||this.removeChild(t[a]);this.sorting.remove((t=>t.isDepended(e.actualName()))),this.grouping.remove((t=>t.isDepended(e.actualName()))),this.columns.remove((t=>t.isDepended(e.actualName())))}else(0,f.NotifyAboutWarning)("Attempt to remove wrong child control.")}validateRelations(){const e=this.tables().map((e=>e.actualName()));return this._validateTable(e,e[0]),e.length<1}_validate(){return 0!==this.tables().length&&(!!(this.allColumnsInTablesSelected()||this.columns().length>0)&&this.validateRelations())}_validateTable(e,t){const a=e.indexOf(t);if(a<0)return;e.splice(a,1);this.relations().map((e=>e.parentTableName()===t?e.nestedTableName():e.nestedTableName()===t?e.parentTableName():null)).forEach((t=>this._validateTable(e,t)))}createChild(e,t,a){return t?(this._initializeTable(t),this.addChild(t),t):super.createChild(e)}init(){this.tables().forEach((e=>{const t=[],a=this.sorting&&this.sorting()||[],i=this.grouping&&this.grouping()||[];[this.columns(),a,i].forEach((a=>{a.forEach((a=>{e.name()===a.table()&&a.itemType()===sr.Column&&t.every((e=>e.column()!==a.column()))&&t.push(a)}))})),this._addColumnsToTable(e,t),this._initializeTable(e)})),this.tables().reduce(((e,t)=>(t.location.x(e),t.location.y(this.topOffset),e+t.size.width()+t.size.width()/2)),30);let e=!1;this._disposables.push(P.computed((()=>{if(!e){e=!0;const t=this.getAllColumns(),a=t.filter((e=>e.aggregate()===ir.None));this.aggregatedColumnsCount(t.length-a.length),t.length!==a.length?a.filter((e=>e.selected()&&!e.groupBy.peek())).forEach((e=>e.groupBy(!0))):t.every((e=>!e.selected.peek()||e.groupBy.peek()))||t.forEach((e=>{e.groupBy.peek()&&e.groupBy(!1)})),e=!1}})))}getTable(e){return(0,f.findFirstItemMatchesCondition)(this.tables(),(t=>t.actualName()===e))}canSave(e=!0){const t=[];return this.tables().forEach((e=>{e.getInvalidColumns().forEach((a=>{t.push((0,f.formatUnicorn)((0,A.getLocalization)('The schema does not contain the following column: "{0}"."{1}".',"DataAccessStringId.ColumnNotInSchemaValidationException"),e.actualName(),a.actualName()))}))})),!(t.length>0)||(e&&(0,f.ShowMessage)(t.join("\n"),"error"),!1)}save(){if(!this.canSave())return;const e=this.serialize(!0);return this.onSave&&this.onSave(e),e}serialize(e=!1){return e?{Query:this.serialize()}:(new A.ModelSerializer).serialize(this)}_findTableInAncestors(e,t){return this.relations().some((a=>a.nestedTable()===e&&(a.parentTable()===t||this._findTableInAncestors(a.parentTable(),t))))}_findHead(e){let t=null;return this.relations().some((a=>(a.nestedTable()===e&&(t=a),!!t))),t?this._findHead(t.parentTable()):e}_isHead(e){return!this.relations().some((t=>t.nestedTable()===e))}_reverseRelations(e,t){t.forEach((e=>{const t=e.parentTable();e.parentTable(e.nestedTable()),e.nestedTable(t),e.conditions().forEach((e=>{const t=e.parentColumnName();e.parentColumnName(e.nestedColumnName()),e.nestedColumnName(t)}))}))}getAllColumns(){return[].concat.apply([],this.tables().map((e=>e.columns())))}cerateJoinCondition(e,t){let a=e.parentModel(),i=t.parentModel();if(a===i)return null;let r=!1,s=(0,f.findFirstItemMatchesCondition)(this.relations(),(e=>(r=e.parentTable()===i&&e.nestedTable()===a,e.parentTable()===a&&e.nestedTable()===i||r)));if(s);else if(this._findTableInAncestors(a,i))r=!0;else if(this._findHead(a)!==this._findHead(i)&&!this._isHead(i)){const e=this._findAncestorsRelations(a),t=this._findAncestorsRelations(i);e.outer>t.outer?this._reverseRelations(i,t.relations):e.outer<t.outer?(this._reverseRelations(a,e.relations),r=!0):e.inner>=t.inner?this._reverseRelations(i,t.relations):e.inner<t.inner&&(this._reverseRelations(a,e.relations),r=!0)}if(r){const r=a;a=i,i=r;const s=e;e=t,t=s}s=s||this.createChild({"@ControlType":"Relation","@Parent":a.actualName(),"@Nested":i.actualName(),"@Type":"Inner"});let o=(0,f.findFirstItemMatchesCondition)(s.conditions(),(a=>a.parentColumn()===e&&a.nestedColumn()===t));return o||(o=s.createChild({"@ControlType":"JoinCondition","@Parent":e.name(),"@Nested":t.name()})),o}tryToCreateRelationsByFK(e){}}us.pageMargin=20;class ps extends us{constructor(e,t,a=Gr.ReadWrite,i,r){super((0,f.extend)(!0,e,ps.emptyModel,e),t,a,i,r),this.controlType="Query",this.type("SelectQuery"),this.tables=(0,A.deserializeArray)(e.Tables.SelectedTables,(e=>new cs(e,this,r))),this.columns=(0,A.deserializeArray)(e.Columns,(e=>new nr(e,this,r))),this.sorting=(0,A.deserializeArray)(e.Sorting,(e=>new nr(e,this,r))),this.grouping=(0,A.deserializeArray)(e.Grouping,(e=>new nr(e,this,r))),this.relations=(0,A.deserializeArray)(e.Tables.Relations,(e=>new Zt(e,this,r))),this.init(),this._tablesObject.tables=this.tables,this._tablesObject.relations=this.relations;const s=(0,A.deserializeArray)(e.Parameters,(e=>new es(e,r)));a===Gr.ReadWrite?(this.parameters=s,this._disposables.push(this.parameters.subscribe((e=>{e.forEach((e=>{"added"!==e.status||e.value.name()||e.value.name((0,f.getUniqueName)(this.parameters().filter((t=>t!==e.value)).map((e=>e.name())),"parameter"))}))}),null,"arrayChange"))):this._disposables.push(this.parameters=P.computed((()=>s()))),this.filterString=new os(this._filterString,null,P.pureComputed((()=>0===this.tables().length&&this.filterString&&0===this.filterString.value().length))),this.filterString.initializeFilterStringHelper(this.parameters,a),this.groupFilterString=new os(this._groupFilterString,null,P.pureComputed((()=>!this.columns().some(Xr)&&this.groupFilterString&&0===(this.groupFilterString.value()||"").length))),this.groupFilterString.initializeFilterStringHelper(this.parameters,a,new Kr(this.columns))}isPropertyDisabled(e){return"skip"===e&&(0===this.skip()&&!this.sorting().length)}getInfo(){return ns}createChild(e){return super.createChild(e,"Table"===e["@ControlType"]?new cs(e,this):void 0)}tryToCreateRelationsByFK(e){this.dbSchemaProvider.getDbSchema().done((t=>{const a=t.tables.filter((t=>t.name===e.name()))[0];a&&a.foreignKeys.forEach((t=>{const a=(0,f.getFirstItemByPropertyValue)(this.tables.peek(),"name",t.primaryKeyTable);if(a)for(let i=0;i<t.primaryKeyColumns.length;i++){const r=(0,f.getFirstItemByPropertyValue)(e.columns(),"name",t.columns[i]),s=(0,f.getFirstItemByPropertyValue)(a.columns(),"name",t.primaryKeyColumns[i]);r&&s&&this.cerateJoinCondition(s,r)}})),this.tables.peek().forEach((a=>{const i=t.tables.filter((e=>e.name===a.name()))[0];i&&i.foreignKeys.forEach((t=>{if(t.primaryKeyTable===e.name())for(let i=0;i<t.primaryKeyColumns.length;i++){const r=(0,f.getFirstItemByPropertyValue)(e.columns(),"name",t.primaryKeyColumns[i]),s=(0,f.getFirstItemByPropertyValue)(a.columns(),"name",t.columns[i]);r&&s&&this.cerateJoinCondition(s,r)}}))}))}))}}ps.emptyModel={"@ItemType":"Query",Tables:{SelectedTables:{},Relations:{}},Columns:{},Sorting:{},Grouping:{}};class hs extends b.SurfaceElementBase{constructor(e,t=P.observable(1)){super(e,{measureUnit:P.observable("Pixels"),zoom:t,dpi:P.observable(100)},hs._unitProperties),this.placeholder=()=>(0,A.getLocalization)("Drop a table or view here to create a query.","AnalyticsCoreStringId.QueryBuilder_SurfacePlaceholder"),this.tables=P.observableArray(),this.relations=P.observableArray(),this.allowMultiselect=!1,this.focused=P.observable(!1),this.selected=P.observable(!1),this.underCursor=P.observable(new f.HoverInfo),this.templateName="dx-query-surface",this.rtl=P.observable(!1),this.measureUnit=this._context.measureUnit,this.dpi=this._context.dpi,this._context=this,this.margins={bottom:this._bottom,left:this._left,right:this._right,top:this._top},this.zoom=t,(0,f.createObservableArrayMapCollection)(e.tables,this.tables,this._createSurface),(0,f.createObservableArrayMapCollection)(e.relations,this.relations,this._createSurface),this._joinedColumns=P.computed((()=>{const e=[];return this.relations().forEach((t=>{t.conditions().forEach((t=>{const a=t.getControlModel();a.parentColumn()&&e.push(a.parentColumn()),a.nestedColumn()&&e.push(a.nestedColumn())}))})),e}))}checkParent(e){return!1}getChildrenCollection(){return this.tables}isJoined(e){return this._joinedColumns().indexOf(e.getControlModel())>-1}}hs._unitProperties={_width:e=>e.pageWidth,_height:e=>e.pageWidth,pageWidth:e=>e.pageWidth,pageHeight:e=>e.pageHeight,_bottom:e=>e.margins.bottom,_left:e=>e.margins.left,_right:e=>e.margins.right,_top:e=>e.margins.top};const ms=jQuery;class gs extends b.SurfaceElementBase{constructor(e,t,a){super(e,t,(0,f.extend)({},gs._unitProperties,a)),this.template="dx-diagram-element",this.selectiontemplate="dx-diagram-element-selection",this.contenttemplate="dx-diagram-element-content",this.margin=P.observable(0)}}gs._unitProperties={_height:e=>e.size.height,_width:e=>e.size.width,_x:e=>e.location.x,_y:e=>e.location.y};class xs extends b.SurfaceElementBase{constructor(e,t){super(e,t,null),this.template="dxqb-table-field",this.toggleSelected=()=>{this.getControlModel().selected(!this.getControlModel().selected())},this.selectedWrapper=P.pureComputed((()=>this.getControlModel().selected())),this.isNotAvailable=P.pureComputed((()=>this._control.isNotAvailable())),this.isAggregate=P.pureComputed((()=>Xr(this.getControlModel()))),this.isAscending=P.pureComputed((()=>"Ascending"===this.getControlModel().sortingType())),this.isDescending=P.pureComputed((()=>"Descending"===this.getControlModel().sortingType())),this.cssClasses=(e,t,a)=>(this._isJoined||(this._isJoined=P.pureComputed((()=>e.isJoined(this)||this.getControlModel()===t.getDragColumn()))),this._isHovered||(this._isHovered=P.pureComputed((()=>{const e=!!t.getDragColumn()&&this.getControlModel().parentModel()!==t.getDragColumn().parentModel();return this.underCursor().isOver&&(!f.DragDropHandler.started()||e)}))),{"dxd-state-invalid":this.isNotAvailable,"dxd-state-active":this.selected(),"dxd-state-joined":this._isJoined,"dxd-state-hovered":this._isHovered})}}class fs extends b.SurfaceElementBase{constructor(e,t){super(e,t,null),this.template="dxqb-table-asterisk-field",this.toggleSelected=()=>{this.getControlModel().selected(!this.getControlModel().selected())},this.selectedWrapper=P.pureComputed((()=>this.getControlModel().selected())),this.isOverAsterisk=P.pureComputed((()=>this.underCursor().isOver&&!f.DragDropHandler.started())),this.cssClasses=()=>({"dxd-state-active":this.selected,"dxd-state-hovered":this.isOverAsterisk()})}}class bs extends gs{constructor(e,t){super(e,t,null),this.showSourceName=!1,this.contenttemplate="dxqb-table",this.titletemplate="dxqb-table-title",this.template="dxqb-table-main",this.toggleSelected=()=>{this.getControlModel().toggleSelectedColumns()},this.selectedWrapper=P.pureComputed((()=>this.getControlModel().allColumnsSelected())),this.isInitialized=e.isInitialized,this.columns=P.pureComputed((()=>e.columns().map((e=>new xs(e,t))))),this.asterisk=new fs(e.asterisk,t)}resizable(e,t){return(0,f.extend)({},e,{handles:"e,w",$selectedNodes:ms.fn.constructor(t),minWidth:cs.TABLE_MIN_WIDTH})}}class ys extends ds{constructor(e,t,a,i){super(e,t,a,i)}getInfo(){return Qt}}class Ss extends Jr{constructor(e,t){super(e,t)}getInfo(){return[At]}}const _s=[{propertyName:"name",modelName:"@Alias"},{propertyName:"type",modelName:"@Type"},{propertyName:"itemType",modelName:"@ItemType"},{propertyName:"queryType",modelName:"@QueryType"},{propertyName:"rootModel",modelName:"Root"},{propertyName:"expressions",modelName:"Expressions",array:!0},{propertyName:"subNodes",modelName:"SubNodes",array:!0}];class vs extends us{_initializeTable(e){this._dbSchemaProvider.getDbTable(e.name(),e.path).done((t=>{e.createColumns(t)}))}_createTableViewModel(e){const t=this.dataSource.getPathFromQueryName(e["@SourceName"]);return new Ts(e,this,t,this.serializer)}constructor(e,t,a,i=Gr.ReadWrite,r){super((0,f.extend)(!0,e,vs.emptyModel,e),a,i,(e=>e),r),this._dbSchemaProvider=a,this.serializer=r,this.expressions=P.observableArray(),this.subNodes=P.observableArray(),this.controlType="FQuery",this.defaultPageHeight=300,this.topOffset=20,this.dataSource=t;const s=[];this.tables=P.observableArray(),e.Root&&this.tables.push(this._createTableViewModel(e.Root)),this.tables.push(...Object.keys(e.SubNodes).map((t=>this._createTableViewModel(e.SubNodes[t].Query)))),s.push(...Object.keys(e.SubNodes).map((t=>new ta(e.SubNodes[t],r).createRelationModel(this)))),this.columns=(0,A.deserializeArray)(e.Expressions,(e=>new nr({"@Name":e["@Name"],"@Alias":e["@Alias"],"@Table":e["@NodeAlias"],"#text":e["@ColumnExpression"],"@ItemType":dr.federatedTypeToColumn(e["@ExpressionType"])},this,r))),this.relations=P.observableArray(s),this.init()}dispose(){super.dispose(),this.dataSource=null}serialize(e){const t=this.serializer||new A.ModelSerializer;this.expressions(this.columns().map((e=>{const a={"@Alias":e.alias(),"@ColumnExpression":e.expression(),"@Name":e.column(),"@NodeAlias":e.table()};return a["@ExpressionType"]=dr.columnTypeToFederated(e.itemType()),new Wt(a,t)}))),this.rootModel(this.tables()[0]);const a=this.tables();a.shift();const i=this.relations();return this.subNodes(a.map((e=>{let t=(0,f.findFirstItemMatchesCondition)(i,(t=>t.nestedTable()===e));return t||(t=(0,f.findFirstItemMatchesCondition)(i,(t=>t.parentTable()===e))),i.splice(i.indexOf(t),1),ta.deserializeRelationModel(e,t)}))),super.serialize(e)}createChild(e,t,a){return super.createChild(e,"FTable"===e["@ControlType"]?new Ts(e,this,a):void 0)}cerateJoinCondition(e,t){const a=e.parentModel(),i=t.parentModel();return this.tables().indexOf(a)>this.tables.indexOf(i)?null:super.cerateJoinCondition(e,t)}getInfo(){return _s}}vs.emptyModel={"@ItemType":"Query",SubNodes:{},Expressions:{}};class Cs extends hs{}class Ts extends cs{constructor(e,t,a,i){super(e,t,i),this.controlType="FTable",a&&!e["@SourceName"]&&this.sourceName(t.dataSource.getQueryNameFromPath(a)),this.name()||this.name(this.sourceName()),!this.alias()&&this.alias(this.name()),this._disposables.push(this.queryType=P.pureComputed((()=>Ue[Ue.SourceNode]))),this.tableOffset(12);const r=a.split("."),s=t.dataSource.dataSources().filter((e=>e.id===r[0]||e.ref===r[0]||e.name===r[0]))[0];this.path=(s.id||s.ref)+(r.length>1?"."+r.slice(1).join("."):""),this.displaySourceName=P.observable(s.name),this.asterisk=new Ss(this,this.serializer)}getInfo(){return qt}getPath(){return this.path}createChildColumn(e){return new ys({"@Name":e.name},e,this,this.serializer)}}class Ps extends bs{constructor(){super(...arguments),this.titletemplate="dxqb-federation-table-title"}}class Ns extends it.TreeListController{itemsFilter(e,t,a){return"none"!==e.specifics}isDraggable(e){if(!e.data)return!1;if(e.data.isListType||!1===e.data.isSupportQueries){let t=e.parent;for(;t&&t.data;){if(t.data.isListType)return!1;t=t.parent}return!0}return!1}constructor(e,t){super(),this.dragDropHandler=e,this.dblClickHandler=t}}class ws extends Ns{itemsFilter(e,t,a){return super.itemsFilter(e,t,a)&&e.isList}hasItems(e){return super.hasItems(e)&&(!e.isListType||!1===e.isSupportQueries)}}class Ds extends it.PopupEditorBase{_aliasValidationCallback(e,t){return!0}constructor(e,t,a=!1,i){super(),this.onSaveCallback=e,this._dataSource=t,this.rtl=a,this.onCloseCallback=i,this.selectedPath=P.observable(),this.title=()=>(0,f.getLocalization)("Query Builder","DataAccessUIStringId.QueryBuilder"),this.getPopupContainer=f.getParentContainer,this.loaded=P.observable(!1),this.maxHeight="90%",this.width="900px",this.height="710px",this.cssClass="dxrd-querybuilder-federation-popup-wrapper",this.resultGridHeight=P.observable(250),this.dragDropHandler=new f.WizardDragDropHandler({dragHelperContent:new f.DragHelperContent(null),parent:".dx-designer-viewport .dxrd-querybuilder-federation-popup-wrapper .dxrd-querybuilder-federation-popup",containment:"parent",target:this.popupTarget(),addHandler:(e,t,a)=>{(e&&e instanceof Ds||e instanceof hs)&&this.addDataMember(t,a)}});const r=new Ns(this.dragDropHandler,(e=>r.isDraggable(e)&&this.addDataMember(e)));this.fieldListModel={itemsProvider:this._dataSource.dbSchemaProvider,treeListController:r,selectedPath:this.selectedPath,pageSize:100,expandRootItems:!0,onItemsChanged:e=>{e.forEach((e=>{e.parent&&r.isDraggable(e.parent)&&e.parent.data.isListType&&(e.parent.collapsed=!0)}))},rtl:a},this._disposables.push(this.dragDropHandler,r),this._aliasValidationRule={type:"custom",validationCallback:e=>!e.value||this._aliasValidationCallback(e.value,e.data),get message(){return(0,f.getLocalization)("Alias is not unique","DataAccessUIStringId.FederationDataSourceQueryBuilder_AliasUniqueMessage")}},this.resizeHelper=new it.ResizeHelper}dispose(){super.dispose(),this.fieldListModel=null,this.onSaveCallback=null,this._dataSource=null}addDataMember(e,t){}save(){this.close()}close(){this.onCloseCallback&&this.onCloseCallback(),this.popupVisible(!1)}popupTarget(){return".dxrd-result-source-grid"}onHiding(){}}class Es{getItems(e){const t=new f.DxDeferred;if(""===e.path)t.resolve(this.tables().map((e=>({name:e.actualName(),displayName:e.actualName(),isList:!0,specifics:"List",dragData:{noDragable:!1}}))));else{const a=e.fullPath.split(".");a.shift();const i=(0,f.findFirstItemMatchesCondition)(this.tables(),(e=>e.actualName()===a.join(".")));i&&this.provider.getItems(new A.PathRequest(i.path)).done((e=>t.resolve(e)))}return t.promise()}constructor(e,t){this.provider=e,this.tables=t}}class Is extends Ds{_onContentReady(e){if(!e.component._isFirstLoad){e.component._isFirstLoad=!0;(0,f.$unwrap)(e.component.content()).parentElement.className+="  dx-dropdowneditor-overlay"}}_getQuery(){return this.designer()&&this.designer().model()}_afterChangeColumn(e,t){this._gridComponent.saveEditData().then((()=>{this._gridComponent.cancelEditData();const a=()=>{const t=this._gridComponent.getVisibleRows(),i=null!=e?e:t.length-1;this._gridComponent.editCell(i,0),this._gridComponent.navigateToRow(this.columnsExpressions()[i]),this._gridComponent.off("contentReady",a)};this._gridComponent.on("contentReady",a),t()}))}_changeColumn(e,t){if(e){let a=dr.createNew(this._getQuery(),this._getQuery().columns,e.table,e.column);this._isSelectAllItemByKey(e.key)&&(a=e.table?new nr({"@Table":e.table,"@ItemType":sr[sr.AllColumns]},this._getQuery()):new nr({"@ItemType":sr[sr.AllColumnsQuery]},this._getQuery()));const i=()=>null!=t?this._getQuery().columns.splice(t,1,a):this._getQuery().columns.push(a);this._afterChangeColumn(t,i)}}_switchEditors(e){let t,a=()=>this._getQuery().columns.notifySubscribers();if(null!=e.index)if(t=this._getQuery().columns()[e.index],e.isExpression())t.toTable();else if(t.itemType()===sr[sr.AllColumns]||t.itemType()===sr[sr.AllColumnsQuery])t=new nr({},this._getQuery()),dr.toExpresson(t,this._getQuery().columns,""),this._getQuery().columns.splice(e.index,1,t);else{const e=t.column()?"["+t.table()+"."+t.column()+"]":"";dr.toExpresson(t,this._getQuery().columns,e)}else t=new nr({},this._getQuery()),dr.toExpresson(t,this._getQuery().columns,""),a=()=>this._getQuery().columns.push(t);this._afterChangeColumn(e.index,a)}_deleteRow(e){this._gridComponent.deleteRow(e),this._gridComponent.deselectAll(),this._getQuery().columns.splice(e,1)}_onRowUpdating(e,t){const a=this._getQuery().columns()[e.index];a&&a.alias(t.alias),e.alias=t.alias,this._gridComponent.saveEditData()}_aliasValidationCallback(e,t){return e===t.alias&&e===t.key||!dr.findByName(this._getQuery().columns,e)}dispose(){super.dispose(),this._bindingContext=null,this._gridComponent=null,this._qbOptions=null,this.queryBuilderSurfaceCreator=null,this.columnsGrid=null}_generateKey(e,t,a,i){return i===a?a:e+"_"+t}_isSelectAllItemByKey(e){return e===Vt[Vt.SelectAllColumnsExpression]||-1!==e.indexOf(Vt[Vt.SelectAllNodeColumnsExpression])}constructor(e,t,a=!1,i){super(e,t,a,i),this.popupContentTemplate="dxrd-querybuilder-select-popup-content",this._querySource=P.observable(null),this.designer=P.observable(null),this.joinResultCollapsed=P.observable(!1),this.width="90%",this.height="90%",this.cssClass="dxrd-querybuilder-federation-popup-wrapper dxrd-querybuilder-select-federation-popup",this._qbOptions={queryBuilderModel:this.designer,dataSource:t,dbSchemaProvider:P.observable(t.dbSchemaProvider),showPropertyGridCondition:e=>"JoinCondition"===e.controlType||"FTable"===e.controlType||"Column"===e.controlType,querySource:this._querySource},this.queryBuilderSurfaceCreator={options:this._qbOptions,creator:e=>new vs(e.querySource(),e.dataSource,e.dbSchemaProvider(),e.parametersMode,new A.ModelSerializer)};const r=[{name:"DataSource",needPrefix:!1,rootPath:"Root"}];this.allColumns=P.pureComputed((()=>{const e=[];if(this._getQuery()){let t="";e.push({column:Jr.DisplayName(),table:t,key:Vt[Vt.SelectAllColumnsExpression]});this._getQuery().getAllColumns().map((e=>{const t=e.parentModel();return{column:e.name(),table:t.actualName(),key:this._generateKey(t.actualName(),e.name(),e.actualName(),e.alias())}})).forEach((a=>{a.table&&t!==a.table&&e.push({column:Jr.DisplayName(),table:a.table,key:a.table+"_"+Vt[Vt.SelectAllNodeColumnsExpression]}),t=a.table,e.push(a)}))}return e})),this.columnsExpressions=P.pureComputed((()=>this._getQuery()&&this._getQuery().columns().map(((e,t)=>{let a,i;return e.actualName()&&(a=this._generateKey(e.table(),e.column(),e.actualName(),e.alias())),e.itemType()===sr[sr.Column]?i=e.column():e.itemType()===sr[sr.Expression]?i=e.expression():e.itemType()===sr[sr.AllColumns]?(a=e.table()+"_"+Vt[Vt.SelectAllNodeColumnsExpression],i=Jr.DisplayName()):e.itemType()===sr[sr.AllColumnsQuery]&&(a=Vt[Vt.SelectAllColumnsExpression],i=Jr.DisplayName()),{alias:e.alias(),key:a,index:t,column:i,table:e.table(),isExpression:P.observable(e.itemType()===sr[sr.Expression]),expression:e.expression}})))),this._disposables.push(this.columnsExpressions,this.allColumns);const s=(e,t)=>{let a=this._bindingContext.createChildContext({allColumns:this.allColumns,itemsProvider:new Es(this._dataSource.dbSchemaProvider,this._getQuery().tables),cellInfo:t.data,expression:{value:t.data.expression,onContentReady:this._onContentReady,path:P.observable(r[0].name),customizeCategories:()=>{},rootItems:r,functions:(0,it.functionDisplay)().filter((e=>"Aggregate"!=e.category))},isExpression:t.data.isExpression||P.observable(!1),changeColumn:e=>{this._changeColumn(e.selectedRowsData[0],e.model.cellInfo.index)},switchEditors:e=>{this._switchEditors(e.model.cellInfo)}}),i=document.createElement("div");e.append(i),P.renderTemplate("dxrd-querybuilder-column-combobox",a,{},i,"replaceNode"),(0,f.addDisposeCallback)(e,(function(){i=null,a=null}))};this._disposables.push(this.addRowDisabled=P.pureComputed((()=>!this._getQuery()||0===this._getQuery().tables().length))),this.columnsGrid={rtlEnabled:a,dataSource:this.columnsExpressions,disabled:this.addRowDisabled,showRowLines:!0,height:"100%",showBorders:!0,editing:{allowUpdating:e=>!(e.row&&e.row.data.key&&this._isSelectAllItemByKey(e.row.data.key)),newRowPosition:"last",mode:"cell",texts:{confirmDeleteMessage:""}},paging:{enabled:!1},columns:[{dataField:"column",get caption(){return(0,f.getLocalization)("Column Name","DataAccessUIStringId.QueryBuilderColumns_ColumnName")},cssClass:"dx-editor-cell",editCellTemplate:s,cellTemplate:(e,t)=>{if(t.data.key&&this._isSelectAllItemByKey(t.data.key))return s(e,t);const a=(0,x.getTemplate)("dxrd-querybuilder-column-combobox-masked"),i=(0,f.$dx)(e).append(a),r={cellInfo:t.data,isExpression:t.data.isExpression||P.observable(!1),switchEditors:()=>null};P.applyBindings(r,i[0])}},{dataField:"table",get caption(){return(0,f.getLocalization)("Table Name","DataAccessUIStringId.QueryBuilderColumns_TableName")},allowEditing:!1},{dataField:"alias",get caption(){return(0,f.getLocalization)("Alias","DataAccessUIStringId.QueryBuilderColumns_Alias")},disabled:!0,validationRules:[this._aliasValidationRule,{type:"custom",validationCallback:e=>{if(""===e.value&&e.data&&e.data.key){const t=dr.findByName(this._getQuery().columns,e.data.key);if(t&&t.itemType()===sr[sr.Expression])return!1}return!0},get message(){return(0,f.getLocalization)("The column name is not specified.","DataAccessStringId.UnnamedColumnValidationException")}}]},{type:"buttons",width:50,buttons:[{icon:"delete",onClick:e=>{this._deleteRow(null!=e.row.data.index?e.row.data.index:this._getQuery().columns().length)},cssClass:"dxrd-querybuilder-grid-actions dxd-icon-highlighted dxrd-image-recycle-bin",template:(0,x.getTemplate)("dxrd-svg-operations-recycle_bin")}]}],onInitialized:e=>{this._bindingContext=P.contextFor((0,f.$unwrap)(e.element)),this._gridComponent=e.component},onRowUpdating:e=>{this._onRowUpdating(e.oldData,e.newData)}},this.gridResizeHelper=new it.ResizeHelper({resultSize:this.resultGridHeight,disabled:this.joinResultCollapsed})}save(){this.selectQuery.init(this._getQuery().serialize()),this.onSaveCallback(this.selectQuery),this.close()}addRow(){this._gridComponent.addRow(),this._gridComponent.deselectAll()}canSave(){return this._getQuery()&&this._getQuery().isValid()}addDataMember(e,t){const a=this._getQuery();if(a.tables().length>1&&!a.validateRelations())return;const i=e.data.displayName||e.data.name,r=a.createChild((0,f.extend)({"@ControlType":"FTable","@Name":i},Gt.controlsMap.FTable.defaultVal),void 0,e.path),s=P.unwrap((0,f.findSurface)(r));if(t){const e=P.unwrap((0,f.findSurface)(this.designer()));e.underCursor().x=t.left-e.absolutePosition.x(),e.underCursor().y=t.top-e.absolutePosition.y(),s.rect({left:e.underCursor().x,top:e.underCursor().y,width:199})}else{const e=Math.max.apply(null,a.tables.peek().filter((e=>e!==r)).map((e=>e.location.x.peek()+1.25*e.size.width.peek())).concat([30]));r.location.x(e),r.location.y(20)}this.designer().selection.initialize(s)}show(e){this.selectQuery=e,this._querySource((new A.ModelSerializer).serialize(e)),this.popupVisible(!0)}popupTarget(){return".dxrd-select-querybuilder-surface .dxqb-main"}}class As{constructor(e,t,a){this.getItems=i=>{const r=new f.DxDeferred;return!a()||i.fullPath.split(".").length>a().split(".").length?r.resolve([]).promise():(e.getItems(i).done((a=>{const s=[],o=[];a.forEach((e=>{const a=t().filter((t=>t.name==e.name))[0];(0,f.isList)(e)&&a&&a.transform.value()?o.push(a):(e.displayName=a&&a.alias||e.displayName,s.push(e))})),0===o.length?r.resolve(s):f.DxDeferred.when(o.map((t=>e.getItems(new A.PathRequest(i.fullPath+"."+t.name)).done((e=>{e.forEach((e=>{const a=(t.alias||t.column)+"_"+e.displayName;e.displayName=a,e.name=a})),r.resolve(e.concat(s))})))))})),r.promise())},this.dispose=()=>{this.getItems=null,e=null}}}class ks extends Ds{_updateColumns(e,t){this.transformSources(e.map((a=>{const i=!a.isList;return{columns:e,column:a.displayName,name:a.name,key:t+"."+a.name,alias:void 0,transform:{value:P.observable(!!i&&void 0),disabled:i,onValueChanged:()=>{this.resultFieldListModel.valueHasMutated()}}}})))}_aliasValidationCallback(e,t){const a=(0,f.findFirstItemMatchesCondition)(this.transformSources(),(t=>t.alias===e));return!(!a||a.key!==t.key)||!a}constructor(e,t,a=!1,i){super(e,t,a,i),this.transformGridTitle=P.observable(),this.transformResultGridTitle=P.observable(),this.transformResultCollapsed=P.observable(!1),this.transformSources=P.observable([]),this.currentPath=P.observable(),this.transformQuery=P.observable(),this.popupContentTemplate="dxrd-querybuilder-transform-popup-content",this.transformGrid={rtlEnabled:a,noDataText:()=>"",dataSource:this.transformSources,showRowLines:!0,paging:{enabled:!1},scrolling:{mode:"infinite"},editing:{allowUpdating:!0,mode:"cell"},height:"100%",columns:[{dataField:"column",get caption(){return(0,f.getLocalization)("Column Name","DataAccessUIStringId.QueryBuilderColumns_ColumnName")},allowEditing:!1},{dataField:"alias",get caption(){return(0,f.getLocalization)("Alias","DataAccessUIStringId.QueryBuilderColumns_Alias")},validationRules:[this._aliasValidationRule]},{type:"buttons",get caption(){return(0,f.getLocalization)("Transform","DataAccessUIStringId.FederationDataSourceQueryBuilder_NodeType_Transform")},buttons:[{template:function(e,t){const a=document.createElement("div");e.append(a),P.renderTemplate("dxrd-querybuilder-transform-checkbox",t.data,{},a,"replaceNode")}}]}],onRowUpdating:e=>{e.oldData.alias=e.newData.alias,this.resultFieldListModel.valueHasMutated(),this.transformSources.valueHasMutated()}};const r=new As(this._dataSource.dbSchemaProvider,this.transformSources,this.currentPath);this.resultFieldListModel=P.observable({itemsProvider:r,path:this.currentPath,selectedPath:P.observable(),treeListController:null});const s=new ws(this.dragDropHandler,(e=>s.isDraggable(e)&&this.addDataMember(e)));this.fieldListModel={itemsProvider:this._dataSource.dbSchemaProvider,treeListController:s,selectedPath:this.selectedPath,pageSize:100,expandRootItems:!0},this._disposables.push(this.currentPath.subscribe((e=>{this.selectedPath(e),this.transformGridTitle((0,f.formatUnicorn)((0,f.getLocalization)("Transformation node root: {0}","DataAccessUIStringId.FederationDataSourceQueryBuilder_TransformationNodeRoot"),this._dataSource.getQueryNameFromPath(e)))})),s,r,s),this.transformResultGridTitle((0,f.getLocalization)("Transformation result","DataAccessUIStringId.FederationDataSourceQueryBuilder_TransformationResult"))}dispose(){super.dispose(),this.resultFieldListModel=null,this.transformGrid=null}addDataMember(e){this.currentPath(e.path),this.updateColumns(),this.transformSources.valueHasMutated()}updateColumns(){const e=this.currentPath();this._dataSource.fielListProvider.getItems(new A.PathRequest(this.currentPath())).done((t=>{this._updateColumns(t,e)}))}canSave(){return this.transformSources().some((e=>e.transform.value()||e.alias))}save(){const e=this.transformQuery();e.root(new Lt({},new A.ModelSerializer,this._dataSource.getQueryNameFromPath(this.currentPath()),this.currentPath())),e.transformationRules([]),this.transformSources().forEach((t=>{(t.transform.value()||t.alias)&&e.transformationRules.push(new na({"@Name":t.name,"@Alias":t.alias,"@Unfold":t.transform.value(),"@Flatten":t.transform.value()}))})),this.onSaveCallback(this.transformQuery()),this.close()}onHiding(){this.transformQuery(null),this.transformResultCollapsed(!1),this.transformSources([])}show(e){if(this.transformQuery(e),e.root&&e.root()){const t=this._dataSource.getPathFromQueryName(e.root().sourceName());this.currentPath(t),this._dataSource.fielListProvider.getItems(new A.PathRequest(t)).done((a=>{this._updateColumns(a,t),e.transformationRules().forEach((e=>{const t=this.transformSources().filter((t=>t.name===e.name()))[0];t.alias=e.alias(),t.transform.value(e.unfold())}))}))}if(!this.currentPath()){const e=this._dataSource.dataSources()[0],t=e.ref||e.id;e.isListType?(this.currentPath(t),this.updateColumns()):this._dataSource.fielListProvider.getItems(new A.PathRequest(t)).done((e=>{this.currentPath(e[0]&&e[0].isListType?t+"."+e[0].name:t),this.updateColumns()}))}this.popupVisible(!0)}}class zs extends Ds{_aliasValidationCallback(e,t){const a=(0,f.findFirstItemMatchesCondition)(this.columns(),(t=>t.alias===e));return!(!a||a.key!==t.key)||!a}constructor(e,t,a=!1,i){super(e,t,a,i),this.unionQuery=P.observable(),this.unionAll=P.observable(!1),this.columns=P.observableArray(),this.popupContentTemplate="dxrd-querybuilder-union-popup-content",this.buttonItems.push({toolbar:"bottom",location:"before",widget:"dxCheckBox",options:{text:(0,f.getLocalization)("Union All","DataAccessUIStringId.FederationDataSourceQueryBuilder_NodeType_UnionAll"),value:this.unionAll}});const r=P.pureComputed((()=>this.unionQuery()&&this.unionQuery().queries().map((e=>{const t=e.alias.peek();return{source:t.split("_").join(" - "),name:t}}))));this._disposables.push(r),this.sourcesGrid={rtlEnabled:a,noDataText:()=>(0,f.getLocalization)("Drop a table or view here to create a query.","AnalyticsCoreStringId.QueryBuilder_SurfacePlaceholder"),dataSource:r,showRowLines:!0,paging:{enabled:!1},scrolling:{mode:"infinite"},columns:[{dataField:"source",get caption(){return(0,f.getLocalization)("Source","DataAccessUIStringId.QueryBuilderColumns_Source")}},{type:"buttons",width:30,buttons:[{icon:"delete",onClick:e=>{this.unionQuery().removeQuery(e.row.data.name),this.unionQuery.valueHasMutated()},cssClass:"dxrd-image-recycle-bin",template:(0,x.getTemplate)("dxrd-svg-operations-recycle_bin")}]}]},this._disposables.push(this.unionQuery.subscribe((e=>{const t=[],a=[];this.columns([]),e&&0!==e.queries().length&&(e.queries().forEach((e=>t.push(e.expressions()))),t[0]&&t[0].forEach((e=>{t.every((t=>!!t.filter((t=>t.name()===e.name()))[0]))&&a.push({name:e.name(),alias:e.alias(),key:e.table()+"."+e.name()})})),this.columns(a))}))),this._disposables.push(this.unionAll.subscribe((e=>{this.unionQuery()&&(e?this.unionQuery().unionType(ca[ca.UnionAll]):this.unionQuery().unionType(ca[ca.Union]))}))),this.aliasGrid={rtlEnabled:a,dataSource:this.columns,showRowLines:!0,editing:{allowUpdating:!0,mode:"cell"},paging:{enabled:!1},scrolling:{mode:"infinite"},height:"100%",columns:[{dataField:"name",caption:"Column Name",allowEditing:!1},{dataField:"alias",get caption(){return(0,f.getLocalization)("Alias","DataAccessUIStringId.QueryBuilderColumns_Alias")},validationRules:[this._aliasValidationRule]}],onRowUpdating:e=>{e.oldData.alias=e.newData.alias}}}dispose(){super.dispose(),this.sourcesGrid=null,this.aliasGrid=null}addDataMember(e){this._dataSource.fielListProvider.getItems(new A.PathRequest(e.path)).done((t=>{t.forEach((t=>this.unionQuery().addSelectQuery(e.path,t.name))),this.unionQuery.valueHasMutated()}))}canSave(){return this.unionQuery()&&this.unionQuery().queries().length>0}save(){this.unionQuery().queries().forEach((e=>{e.expressions(e.expressions().filter((e=>{const t=this.columns().filter((t=>t.name===e.name()))[0];return!!t&&(e.alias(t.alias),!0)})))})),this.onSaveCallback(this.unionQuery()),this.close()}onHiding(){this.unionQuery(null),this.unionAll(!1)}show(e){this.unionQuery(e),this.popupVisible(!0),this.unionAll(e.unionType()===ca[ca.UnionAll])}}class $s extends A.Disposable{_getQuery(e,t){return t?(0,f.findFirstItemMatchesCondition)(this.queries(),(e=>t===e.alias())):e()}_setQuery(e){e&&-1===this.queries.indexOf(e)&&(e.alias(e.alias()||e.generateName()),this._dataSource.queries().filter((t=>t.alias()===e.alias()))[0]&&e.alias((0,f.getUniqueName)(this._dataSource.queries().map((e=>e.alias())),e.alias())),this.queries.push(e),this._afterAddQuery&&this._afterAddQuery(e))}constructor(e,t,a,i=!1){super(),this._dataSource=e,this.queries=t,this._showSelectQbCallBack=(e=null)=>{this._popupSelectQueryBuilder.show(this._getQuery((()=>new ia({})),e))},this._showUnionQbCallBack=(e=null)=>{this._popupUnionQueryBuilder.show(this._getQuery((()=>new ua({},this._dataSource.dataSources)),e))},this._showTransformQbCallBack=(e=null)=>{this._popupTransformQueryBuilder.show(this._getQuery((()=>new oa({})),e))},this.template="dx-querybuilder-federation-popup-templates";const r=e=>{this._setQuery(e),t.valueHasMutated(),a.onSave&&a.onSave()};this._afterAddQuery=a.afterAddQuery,this._disposables.push(t.subscribe((e=>e.forEach((e=>{e.sources().forEach((e=>this._dataSource.addSource(e)))}))))),this._disposables.push(this._popupSelectQueryBuilder=new Is(r,this._dataSource,i,a.onClose)),this._disposables.push(this._popupUnionQueryBuilder=new zs(r,this._dataSource,i,a.onClose)),this._disposables.push(this._popupTransformQueryBuilder=new ks(r,this._dataSource,i,a.onClose)),this.callBacks={joinCallBack:this._showSelectQbCallBack,unionCallBack:this._showUnionQbCallBack,transformCallBack:this._showTransformQbCallBack},this.popupItems=[{template:"dxrd-querybuilder-federation-popup",model:this._popupSelectQueryBuilder},{template:"dxrd-querybuilder-federation-popup",model:this._popupUnionQueryBuilder},{template:"dxrd-querybuilder-federation-popup",model:this._popupTransformQueryBuilder}]}editQuery(e,t){switch(e){case Ue.SelectNode:this.callBacks.joinCallBack(t);break;case Ue.UnionNode:this.callBacks.unionCallBack(t);break;case Ue.TransformationNode:this.callBacks.transformCallBack(t)}}dispose(){super.dispose(),this._dataSource=null}}class Os{constructor(e){this._rootItems=e}afterItemsFilled(e,t){t.forEach((e=>{void 0===e.isListType&&(e.isListType="List"===e.specifics)}))}beforeItemsFilled(e,t){return!e.fullPath&&(t.push(...this._rootItems().map((e=>({name:e.id||e.ref,displayName:e.name,isList:!0,specifics:e.specifics||"ListSource",isSupportQueries:e.isSupportQueries,isListType:e.isListType})))),!0)}}class Bs extends ji{constructor(e,t,a,i,r,s){super(e,t,a,i,r,s),this.callbacks=r,this.addAction={clickAction:()=>this.showPopover(),imageClassName:"dxrd-image-add",imageTemplateName:"dxrd-svg-operations-add",templateName:"dx-treelist-federation-action-with-popover",text:(0,f.getLocalization)("Add query","AnalyticsCoreStringId.SqlDSWizard_AddQuery")},this.className="dxrd-federation-addqueries-popover"}getActions(e){const t=[];return 0===e.path.indexOf("queries")&&t.push(this.addAction),t}popoverListItems(){return[{name:(0,f.getLocalization)("Join","DataAccessUIStringId.FederationDataSourceQueryBuilder_NodeType_Select"),addAction:()=>this.callbacks().showQbCallBacks.joinCallBack(),className:this.className},{name:(0,f.getLocalization)("Union","DataAccessUIStringId.FederationDataSourceQueryBuilder_NodeType_Union"),addAction:()=>this.callbacks().showQbCallBacks.unionCallBack(),className:this.className},{name:(0,f.getLocalization)("Transform","DataAccessUIStringId.FederationDataSourceQueryBuilder_NodeType_Transform"),addAction:()=>this.callbacks().showQbCallBacks.transformCallBack(),className:this.className}]}}class Rs extends Xi{constructor(e,t,a,i,r){super(e,t,((e,t,a)=>new Ms(e.name,e.displayName,e.specifics,e.isListType,t,a,r)),((e,t,a)=>new Hi(e.name,e.displayName,e.specifics,t,a,r))),this._callBack=P.observable({deleteAction:e=>{this._customQueries.remove((0,f.findFirstItemMatchesCondition)(this._customQueries(),(t=>(t.alias()||t.generateName())===e)))},disableCustomSql:!1,showQbCallBack:null,showQbCallBacks:null});const s=this.getItems;this._customQueries=i,this.getItems=e=>{const t=new f.DxDeferred;if(e.fullPath&&e.fullPath.split(".").length>2&&t.resolve([]),"queries"===e.fullPath){const e=i().map((e=>{const t=e.alias()||e.generateName(),i=this._queries.children().filter((t=>t.query===e))[0];let s;if(i)return i;s=e instanceof ua?P.observable((0,f.extend)(this._callBack(),{showQbCallBack:a.unionCallBack})):e instanceof oa?P.observable((0,f.extend)(this._callBack(),{showQbCallBack:a.transformCallBack})):P.observable((0,f.extend)(this._callBack(),{showQbCallBack:a.joinCallBack}));const o=new Ui(t,t,"query",!!i&&i.checked(),P.observableArray([]),s,r,e);return o.path="queries."+o.name,this._disposables.push(o),o}));this._queries.initializeChildren(e),t.resolve(e)}else s(e,!0).done((e=>t.resolve(e)));return t.promise()},this._callBack().showQbCallBacks=a,this._disposables.push(this._queries=new Bs("queries",(0,A.getLocalization)("Federated Queries","DataAccessUIStringId.FederatedQueryCollectionList_Title"),"list",!1,this._callBack,r)),t.valueHasMutated(),this._rootItems.push(this._queries),this.dispose=()=>{super.dispose(),this.removeProperties()}}isList(e,t){return!t.isListType&&super.isList(e,t)}}class Ms extends Ji{constructor(e,t,a,i,r,s,o){super(e,t,a,r,s,o),this.isListType=i}}class qs extends Ea{_wrapFieldListCallback(e,t){return a=>{const i=(0,f.findFirstItemMatchesCondition)(this._dataSources(),(e=>null!=e.id&&null!=a.id&&e.id===a.id||null!=e.ref&&null!=a.id&&e.ref===a.ref));return e(a,i||void 0,t)}}constructor(e){super(),this._options=e,this._selectedPath=P.observable(null),this._itemsProvider=P.observable(),this._customQueries=P.observableArray([]),this._setQueryChecked=e=>{const t=this._itemsProvider().getRootItems();this._selectedPath("queries."+e.alias());const a=(0,f.findFirstItemMatchesCondition)(t,(e=>"queries"===e.name)).children(),i=a[a.length-1];i.setChecked(!0),i._afterCheckToggled&&i._afterCheckToggled(i)},this._dataSources=P.observableArray(),this._scrollViewHeight="calc(100% - 37px)",this._isDataLoadingInProcess=P.observable(!1),this._customizeDBSchemaTreeListActions=null,this._dataSources(this._options.dataSources()),this._disposables.push(this._fieldListProvider=new f.FieldListProvider(this._wrapFieldListCallback(this._options.callbacks.fieldListsCallback,!1),this._dataSources,[new Os(this._dataSources)],!0)),this._disposables.push(this._dataSource=new fa({},this._dataSources,this._fieldListProvider,new A.ModelSerializer)),this._disposables.push(this._queriesPopupHelper=new $s(this._dataSource,this._customQueries,{afterAddQuery:this._setQueryChecked},this._options.rtl)),this._fieldListModel=P.observable(null),this._getItemsAfterCheck=e=>{const t=e.path.split(".");if("query"==e.specifics){const t=e.query||(0,f.findFirstItemMatchesCondition)(this._customQueries(),(a=>(a.alias()||t.generateName())===e.name));t.alias(e.name),e.checked.peek()?-1===this._dataSource.queries.indexOf(t)&&this._dataSource.queries.push(t):this._dataSource.queries.remove(t)}else if(!e.isList||t.length>2){const a=t.pop(),i=t.join(".");e.checked.peek()?this._dataSource.addSelectQuery(i,a):this._dataSource.removeExpression(a,i)}else e.children().forEach((e=>this._getItemsAfterCheck(e)));this._onChange(),this[wa]&&this[wa](!1)}}canNext(){return this._dataSource.queries().length>1}canFinish(){return this._dataSource.queries().length>0}_createTreeListFactory(){return new fi}_loadPanelViewModel(e){return wr._getLoadPanelViewModel(e,this._isDataLoadingInProcess)}commit(){return(0,f.createDeferred)().resolve({federationDataSourceJSON:JSON.stringify((new A.ModelSerializer).serialize(this._dataSource)),federatedQueries:[]}).promise()}initialize(e){const t=(0,f.createDeferred)(),a=P.observableArray((this._dataSources()||[]).map((e=>({name:e.id||e.ref,displayName:e.name,isList:!0,specifics:e.specifics||"ListSource",isListType:e.isListType,dragData:{noDragable:!1}})))),i=new Rs(this._fieldListProvider,a,this._queriesPopupHelper.callBacks,this._customQueries,this._getItemsAfterCheck);return this._disposables.push(i),this._itemsProvider(i),this._fieldListModel({factory:this._createTreeListFactory(),itemsProvider:this._itemsProvider(),selectedPath:this._selectedPath,treeListController:new Dr(this._customizeDBSchemaTreeListActions),templateName:"dxrd-treelist-with-checkbox"}),t.resolve().promise()}}function Ls(e,t){e.registerMetadata(_a.FederatedQueryConfigurePage,{create:()=>new qs(t),setState:(e,t)=>{t.federationDataSourceJSON=e.federationDataSourceJSON,t.federatedQueries=e.federatedQueries},getState:e=>e.federationDataSourceWizard,resetState:(e,t)=>{e.federationDataSourceJSON=t.federationDataSourceJSON,e.federatedQueries=t.federatedQueries},description:(0,A.getLocalization)("Columns selected from specific tables and/or views will be automatically included into a separate query.","AnalyticsCoreStringId.SqlDSWizard_PageConfigureMultiQuery"),template:"dxrd-wizard-add-federated-queries-page"})}class Qs extends Fr{_restoreDataSource(e){this._federationDataSource=qa(e,this._options.dataSources)}_dataSource(){return this._federationDataSource}constructor(e,t){super(e),this._options=t,this._customResetOptions=()=>{},this._relationsEditor=P.observable(null)}commit(){this.relationsSubscription.dispose();const e=this._relations().map((e=>ma.create(e)));this._federationDataSource.relations(e),this._federationDataSource.resultSet=this._resultSet;const t=new A.ModelSerializer;return(0,f.createDeferred)().resolve({federationDataSourceJSON:JSON.stringify((new A.ModelSerializer).serialize(this._federationDataSource)),relations:e.map((e=>JSON.stringify(t.serialize(e))))}).promise()}}function Vs(e,t,a){e.registerMetadata(_a.FederatedMasterDetailRelationshipsPage,{create:()=>new Qs(t,a),setState:(e,t)=>{t.relations=e.relations},getState:e=>e.federationDataSourceWizard,resetState:(e,t)=>{e.relations=t.relations},description:(0,A.getLocalization)("Configure master-detail relationships.","AnalyticsCoreStringId.SqlDSWizard_PageConfigureMasterDetailRelations"),template:"dxrd-wizard-configure-relations-page"})}class Ws{constructor(){this.metadata={}}registerMetadata(e,t){t.canFinish||(t.canFinish=e=>e.canFinish()),t.canNext||(t.canNext=e=>e.canNext()),this.metadata[e]=t}getMetadata(e){return this.metadata[e]}unregisterMetadata(e){delete this.metadata[e]}reset(){this.metadata={}}}class Fs{constructor(){this.connectionStrings={json:P.observableArray([]),sql:P.observableArray([])},this.callbacks={},this.rtl=!1,this.disableCustomSql=!1,this.wizardSettings=(new Us).createDefault(),this.allowCreateNewJsonConnection=!1,this.predefinedDataSources=P.observable([])}get jsonDataSourceAvailable(){return this.wizardSettings.enableJsonDataSource&&(this.allowCreateNewJsonConnection||(P.unwrap(this.connectionStrings.json)||[]).length>0)}get sqlDataSourceAvailable(){return this.wizardSettings.enableSqlDataSource&&(P.unwrap(this.connectionStrings.sql)||[]).length>0}get objectDataSourceAvailable(){return this.wizardSettings.enableObjectDataSource}get canCreateDataSource(){return this.jsonDataSourceAvailable||this.sqlDataSourceAvailable||this.objectDataSourceAvailable}get canRunWizard(){return this.canCreateDataSource||P.unwrap(this.predefinedDataSources).length>0}get federationDataSourceAvailable(){return this.wizardSettings.enableFederationDataSource&&this.dataSources&&this.dataSources()&&this.dataSources().length>0}}class js extends Fs{}class Us{createDefault(e){const t={enableJsonDataSource:!0,enableSqlDataSource:!0,enableObjectDataSource:!0,enableFederationDataSource:!0};return e?(void 0!==e.enableJsonDataSource&&(t.enableJsonDataSource=e.enableJsonDataSource),void 0!==e.enableSqlDataSource&&(t.enableSqlDataSource=e.enableSqlDataSource),null!=e.enableObjectDataSource&&(t.enableObjectDataSource=e.enableObjectDataSource),null!=e.enableFederationDataSource&&(t.enableFederationDataSource=e.enableFederationDataSource),t):t}}class Hs extends Pr{constructor(e,t,a){super(e,t),this._dataSourceWizardOptions=a}getNextPageId(e){return!e&&Ti(this._dataSourceWizardOptions)?ya.ChooseDataSourceTypePage:e?e===ya.ChooseDataSourceTypePage&&this._getCurrentState().dataSourceType===Ia.Sql?Sa.ChooseConnectionPage:e===ya.ChooseDataSourceTypePage&&this._getCurrentState().dataSourceType===Ia.Federation?_a.FederatedQueryConfigurePage:e===ya.ChooseDataSourceTypePage&&this._getCurrentState().dataSourceType===Ia.Json&&this._dataSourceWizardOptions.connectionStrings.json().length>0?va.ChooseConnectionPage:this._dataSourceWizardOptions.allowCreateNewJsonConnection&&e===ya.ChooseDataSourceTypePage&&this._getCurrentState().dataSourceType===Ia.Json?va.ChooseJsonSourcePage:e===va.ChooseConnectionPage&&this._getCurrentState().jsonDataSourceWizard.connectionName?va.ChooseJsonSchemaPage:this._dataSourceWizardOptions.allowCreateNewJsonConnection&&e===va.ChooseConnectionPage&&!this._getCurrentState().jsonDataSourceWizard.connectionName?va.ChooseJsonSourcePage:e===va.ChooseJsonSourcePage?va.ChooseJsonSchemaPage:e===Sa.ChooseConnectionPage&&this._getCurrentState().sqlDataSourceWizard.name?Sa.ConfigureQueryPage:e===Sa.ConfigureQueryPage&&this._getCurrentState().sqlDataSourceWizard.sqlDataSourceJSON?Sa.ConfigureParametersPage:void 0:this.getNextPageId(ya.ChooseDataSourceTypePage)}}class Js extends wr{constructor(e,t){super(e,t.callbacks.finishCallback),this._wizardOptions=t,this._extendCssClass="dxrd-sqldatasource-wizard",this.title=(0,A.getLocalization)("Data Source Wizard","AnalyticsCoreStringId.DSWizard_Title")}initialize(e,t=(e,t)=>new Hs(e,t,this._wizardOptions)){this._wizardOptions.sqlDataSourceAvailable||!(0,f.isEmptyObject)(e.sqlDataSourceWizard)?e.dataSourceType=Ia.Sql:(this._wizardOptions.jsonDataSourceAvailable||e.jsonDataSourceWizard.jsonSource)&&(e.dataSourceType=Ia.Json),super.initialize(e,t)}canRunWizard(){return this._wizardOptions.jsonDataSourceAvailable||this._wizardOptions.sqlDataSourceAvailable}}function Gs(e=new Ws,t){return za(e,t),Ri(e,t.requestWrapper,t.callbacks.getItemsProviderCallback),Ei(e,t),Zi(e,t.requestWrapper,t.callbacks),ai(e,t.callbacks.getItemsProviderCallback),Sr(e,t),Ja(e,t.connectionStrings.sql),vr(e,t.requestWrapper),e}function Xs(e=new Ws,t){return e=Gs(e,t),new Js(e,t)}class Ys extends Ea{_initSections(){this._sections=[{data:this._chooseObjectType,disabled:()=>!1,description:(0,A.getLocalization)("Choose the type and its constructor.","AnalyticsCoreStringId.ObjectDSWizard_ChooseType_Description"),position:bi(this._dataSourceWizardOptions.rtl?mi.Right:mi.Left),template:"dx-objectdatasource-types-section"},{data:this._chooseObjectDataMember,disabled:()=>!this._chooseObjectDataMember.dataMembers().length,description:(0,A.getLocalization)("Choose the entire object or a data member to bind.","AnalyticsCoreStringId.ObjectDSWizard_ChooseDataMember_Description"),position:bi(this._dataSourceWizardOptions.rtl?mi.TopLeft:mi.TopRight),disabledText:(0,A.getLocalization)("To select a data member, choose a type that contains at least one data member.","AnalyticsCoreStringId.ObjectDSWizard_ChooseDataMember_Disabled_Description"),template:"dx-objectdatasource-datamembers-section"},{data:this._chooseObjectParameters,disabled:()=>!this._chooseObjectParameters.hasParameters(),position:bi(this._dataSourceWizardOptions.rtl?mi.BottomLeft:mi.BottomRight),description:(0,A.getLocalization)("Configure constructor parameters and/or method parameters.","AnalyticsCoreStringId.ObjectDSWizard_ConfigureParameters_Description"),disabledText:(0,A.getLocalization)("To specify parameters, select a parameterized constructor or method.","AnalyticsCoreStringId.ObjectDSWizard_ConfigureParameters_Disabled_Description"),template:"dx-objectdatasource-parameters-section"}]}showDescription(e,t){return[e+1,t].join(". ")}constructor(e){super(),this._dataSourceWizardOptions=e,this._types=P.observableArray([]),this._objectDataSource=new Ct;const t=this._dataSourceWizardOptions.callbacks.getItemsProviderCallback;this._disposables.push(this._provider=new si(this._dataSourceWizardOptions.requestWrapper),this._chooseObjectType=new ni(this._types,this._provider),this._chooseObjectDataMember=new ci(this._chooseObjectType.selectedType,this._chooseObjectType.selectedCtor),this._chooseObjectParameters=new ei(this._chooseObjectType.selectedCtor,this._chooseObjectDataMember.selectedDataMembers,t&&t())),this._initSections()}canNext(){return!1}canFinish(){return!!this._chooseObjectDataMember.selectedDataMembers().length}commit(){const e=this._chooseObjectDataMember.selectedDataMembers()[0];return(0,f.createDeferred)().resolve({selectedType:this._chooseObjectType.selectedType().name,ctor:this._chooseObjectType.selectedCtor(),dataSourceName:this._objectDataSource.name(),context:this._context,dataMember:!e||e.isEntireObject()?void 0:e}).promise()}initialize(e){return this._context=e.objectDataSourceWizard.context,this._objectDataSource.setState(e.objectDataSourceWizard),this._chooseObjectType.selectedCtor(this._objectDataSource.ctor),this._chooseObjectDataMember.selectedDataMembers([this._objectDataSource.dataMember]),this._provider.getObjectTypeDescriptions(e.objectDataSourceWizard.context).done((e=>{e.types.forEach((e=>e.members.splice(0,0,wt.empty()))),this._types(e.types||[]),e.types.length>0&&this._chooseObjectType.selectedPath(e.types[0].name)})),(0,f.createDeferred)().resolve().promise()}}function Ks(e,t){e.registerMetadata(Ta.SpecifyObjectDataSourceSettingsPage,{setState:(e,t)=>{t.objectDataSourceWizard.ctor=e.ctor,t.objectDataSourceWizard.dataMember=e.dataMember,t.objectDataSourceWizard.selectedType=e.selectedType,t.objectDataSourceWizard.dataSourceName=e.dataSourceName},getState:e=>e,resetState:(e,t)=>{e.objectDataSourceWizard.ctor=t.objectDataSourceWizard.ctor,e.objectDataSourceWizard.dataMember=t.objectDataSourceWizard.dataMember,e.objectDataSourceWizard.selectedType=t.objectDataSourceWizard.selectedType,e.objectDataSourceWizard.dataSourceName=t.objectDataSourceWizard.dataSourceName},create:()=>new Ys(t),template:"dxrd-page-objectsource",navigationPanelText:(0,A.getLocalization)("Specify Data Source Settings","AnalyticsCoreStringId.Wizard_SpecifyDataSourceSettingsPage")})}class Zs extends Fs{}class eo extends wr{constructor(e,t){super(e,t.callbacks.finishCallback),this._wizardOptions=t,this.title=(0,A.getLocalization)("Data Source Wizard","AnalyticsCoreStringId.SqlDSWizard_Title"),this._extendCssClass="dxrd-multiqueries-sqldatasource-wizard",this.height(443)}canRunWizard(){return this._wizardOptions.canRunWizard}initialize(e,t=(e,t)=>new to(e,t,this._wizardOptions)){this._wizardOptions.sqlDataSourceAvailable||!(0,f.isEmptyObject)(e.sqlDataSourceWizard)?e.dataSourceType=Ia.Sql:this._wizardOptions.jsonDataSourceAvailable||e.jsonDataSourceWizard.jsonSource?e.dataSourceType=Ia.Json:this._wizardOptions.objectDataSourceAvailable&&(e.dataSourceType=Ia.Object),super.initialize(e,t)}}class to extends Pr{constructor(e,t,a){super(e,t),this._wizardOptions=a}getNextPageId(e){if(!e&&this._wizardOptions.predefinedDataSources().length>0)return ya.ChoosePredefinedDataSourcePage;if((!e||e===ya.ChoosePredefinedDataSourcePage)&&Ti(this._wizardOptions))return ya.ChooseDataSourceTypePage;if(!e||e===ya.ChoosePredefinedDataSourcePage)return this.getNextPageId(ya.ChooseDataSourceTypePage);if(e===ya.ChooseDataSourceTypePage&&this._getCurrentState().dataSourceType===Ia.Sql)return Sa.ChooseConnectionPage;if(e===ya.ChooseDataSourceTypePage&&this._getCurrentState().dataSourceType===Ia.Federation)return _a.FederatedQueryConfigurePage;if(e===ya.ChooseDataSourceTypePage&&this._getCurrentState().dataSourceType===Ia.Object)return Ca.ChooseTypesPage;if(e===Ca.ChooseTypesPage&&this._getCurrentState().objectDataSourceWizard.selectedType)return Ca.ChooseDataMembersPage;if(e===Ca.ChooseDataMembersPage)return Ca.ConfigureParametersPage;if(e===ya.ChooseDataSourceTypePage&&this._getCurrentState().dataSourceType===Ia.Json&&this._wizardOptions.connectionStrings.json().length>0)return va.ChooseConnectionPage;if(this._wizardOptions.allowCreateNewJsonConnection&&e===ya.ChooseDataSourceTypePage&&this._getCurrentState().dataSourceType===Ia.Json)return va.ChooseJsonSourcePage;if(e===va.ChooseConnectionPage&&this._getCurrentState().jsonDataSourceWizard.connectionName)return va.ChooseJsonSchemaPage;if(this._wizardOptions.allowCreateNewJsonConnection&&e===va.ChooseConnectionPage&&!this._getCurrentState().jsonDataSourceWizard.connectionName)return va.ChooseJsonSourcePage;if(e===va.ChooseJsonSourcePage)return va.ChooseJsonSchemaPage;if(e===Sa.ChooseConnectionPage&&this._getCurrentState().sqlDataSourceWizard.name)return Sa.MultiQueryConfigurePage;if(this._getCurrentState().sqlDataSourceWizard.sqlDataSourceJSON&&e===Sa.MultiQueryConfigurePage&&this._getCurrentState().sqlDataSourceWizard.customQueries.length>0)return Sa.MultiQueryConfigureParametersPage;if(e===Sa.MultiQueryConfigurePage||e===Sa.MultiQueryConfigureParametersPage){if(Ba(this._getCurrentState().sqlDataSourceWizard).sqlDataSource.queries().length>1)return ya.ConfigureMasterDetailRelationshipsPage}else if(e===_a.FederatedQueryConfigurePage){if(qa(this._getCurrentState().federationDataSourceWizard,this._wizardOptions.dataSources).queries().length>1)return _a.FederatedMasterDetailRelationshipsPage}}}function ao(e=new Ws,t){return wi(e,t),za(e,t),Ei(e,t),Ri(e,t.requestWrapper,t.callbacks.getItemsProviderCallback),Zi(e,t.requestWrapper,t.callbacks),Ja(e,t.connectionStrings.sql,t.getSqlConnectionStrings),Ks(e,t),Ls(e,t),Vs(e,ar(t.callbacks.federationDataSourceResultSchema),t),Ir(e,t),Mr(e,t.requestWrapper),Ur(e,tr(t.callbacks.sqlDataSourceResultSchema)),li(e,t),pi(e,t),ai(e,t.callbacks.getItemsProviderCallback),e}function io(e=new Ws,t){return ao(e,t),new eo(e,t)}class ro extends Ws{constructor(){super(...arguments),this.metadata={}}registerMetadata(e,t){super.registerMetadata(e,t)}getMetadata(e){return super.getMetadata(e)}}class so extends Ws{registerMetadata(e,t){super.registerMetadata(e,t),void 0===t.position&&(t.position=Object.keys(this.metadata).length-1),void 0===t.recreate&&(t.recreate=!1),void 0===t.disabledText&&(t.disabledText="")}}class oo extends Ua{constructor(e,t,a){super(e,t,a.template,a.description),this.pageId=e,this.page=t,t.onChange&&(this.onChange=e=>{t.onChange((()=>{e(),a.onChange&&a.onChange()}))})}}class no{resetPage(){this.page()&&this.page().dispose(),this.page(null)}setPage(e){this.page()!==e&&this.page(e)}constructor(e,t){this.pageId=e,this.metadata=t,this.page=P.observable(null)}}class lo{_resetPages(e,t=e=>this._resetPage(e)){if(e<this._pagesIds.length)for(let a=this._pagesIds.length-1;a>=e;a--)this._pagesIds[a].forEach((e=>t(e))),this._pagesIds.splice(a,1)}_tryResetPageByMetadata(e){return!!this.pageFactory.getMetadata(e).recreate&&(this._resetPage(e),!0)}_resetPage(e){this.stateManager.resetPageState(e),this._resetPageCallback(e);const t=this._getPage(e);t&&(t.dispose(),this._pages.splice(this._pages.indexOf(t),1))}_createNewPage(e){const t=this.pageFactory.getMetadata(e);return new oo(e,t.create(),t)}_getPage(e){return this._pages.filter((t=>t.pageId===e))[0]}_getNextPage(e){return(new f.DxDeferred).resolve(e.map((e=>{let t=this._getPage(e);return t&&!this._tryResetPageByMetadata(e)||(t=this._createNewPage(e),this._pages.push(t)),t}))).promise()}_getPageIndex(e){return this._pagesIds.indexOf(this._pagesIds.filter((t=>t.some((t=>t===e))))[0])}resetNextPages(e){this._resetPages(this._getPageIndex(e)+1)}constructor(e,t,a){this.pageFactory=e,this.stateManager=t,this._resetPageCallback=a,this._pagesIds=[],this._pages=[]}getStartPage(){const e=this.getNextPageId(),t=this.pageFactory.getMetadata(e),a=new oo(e,t.create(),t);return this._pagesIds.push([e]),a}getNextPage(e){const t=this._getPageIndex(e);let a=this.getNextPageId(e);return a?(a.push||(a=[a]),JSON.stringify(this._pagesIds[t+1])!==JSON.stringify(a)&&(this._resetPages(t+1,(e=>{-1===a.indexOf(e)&&this._resetPage(e)})),this._pagesIds.push(a)),this._getNextPage(a)):(this.resetNextPages(e),(new f.DxDeferred).reject().promise())}getCurrentState(){return this.stateManager.getCurrentState()}getNextPageId(e){return""}}class co extends A.Disposable{dispose(){super.dispose(),this.sections.forEach((e=>e.resetPage())),this.sections=[]}_createLoadingState(e){e[co.__loadingStateFunctionName]||(e[co.__loadingStateFunctionName]=e=>this._loadingState(e))}_createNextAction(e){e[Da]||(e[Da]=()=>this._extendedNextAction())}_loadingState(e){e?(this._loadingTimeout&&clearTimeout(this._loadingTimeout),this._loadingTimeout=setTimeout((()=>{this.isLoading(!0)}),100)):(this._loadingTimeout&&clearTimeout(this._loadingTimeout),this.isLoading(!1))}_extendedNextAction(){}constructor(e,t,a){super(),this.pageFactory=e,this.events=new A.EventManager,this._loadingTimeout=null,this._changeTimeout=null,this.sections=[],this.isLoading=P.observable(!1),t&&(this._loadingState=t),a&&(this._extendedNextAction=a),this._disposables.push(this.events)}_resetPageById(e){this.getPageById(e).resetPage()}initialize(e,t=(e,t)=>new lo(e,t,(e=>this._resetPageById(e)))){this.events.call("beforeInitialize",{wizard:this,state:e}),this.stateManager=new Tr(e,this.pageFactory),this.iterator=t(this.pageFactory,this.stateManager),this.sections=[],Object.keys(this.pageFactory.metadata).forEach((e=>{this.sections.push(new no(e,this.pageFactory.metadata[e]))})),this.sections=this.sections.sort(((e,t)=>e.metadata.position-t.metadata.position)),this.events.call("afterInitialize",{wizard:this})}_canNext(e){const t=!this.isLoading()&&e&&e.page&&this.pageFactory.getMetadata(e.pageId);return t&&t.canNext(e.page)}_canFinish(e){return!this.isLoading()&&e&&this.pageFactory.getMetadata(e.pageId).canFinish(e.page)}_initPage(e,t=!1,a=!1){if(this._createNextAction(e.page),!e.onChange)throw Error("Page with id "+e.pageId+" cannot be used in AutoNavigation, because it does not have method OnChange");return e.onChange((()=>{this._changeTimeout&&clearTimeout(this._changeTimeout),this._changeTimeout=setTimeout((()=>{this._nextAction(e)}),100)})),e.initialize(this.stateManager.getPageState(e.pageId),t,a).always((()=>this._loadingState(!1)))}getPageById(e){return this.sections.filter((t=>t.pageId===e))[0]}start(){this.events.call("beforeStart",{wizard:this}),this._loadingState(!0);const e=this.iterator.getStartPage();this.events.call("beforePageInitialize",vi(e,this)),this._initPage(e).done((()=>{const t=this.getPageById(e.pageId);if(!t)throw Error("Page Section with id "+e.pageId+" is not found.");t.setPage(e),this.events.call("afterPageInitialize",Ci(e,this)),this._nextAction(e)}))}finishAction(){const e=new f.DxDeferred;let t=!1;for(let a=this.sections.length-1;a>=0;a--)if(this.sections[a].page()){this.sections[a].page().commit().done((e=>{this.sections[a].page().isChanged&&(this.events.call("beforeFinish",{wizardModel:this,state:this.stateManager.getCurrentState()}),this.stateManager.setPageState(this.sections[a].page().pageId,e),this.events.call("afterFinish",{wizardResult:this,state:this.stateManager.getCurrentState()}))})).always((()=>e.resolve())),t=!0;break}return t||e.resolve(),e.promise()}_nextAction(e,t=!1){if(!this._canNext(e)&&!this._canFinish(e))return e.resetCommitedState(),this.iterator.resetNextPages(e.pageId);this._loadingState(!0),e.commit().done((a=>{e.isChanged||t||e.page&&e.page.changeAlways?(this.stateManager.setPageState(e.pageId,a),this.iterator.getNextPage(e.pageId).done((a=>{a&&a.length>0?a.forEach((a=>{const i=this.getPageById(a.pageId),r=i&&i.page()||a;this.events.call("beforePageInitialize",vi(r,this)),this._initPage(r,!!i.page(),e.isChanged||t).done((()=>{this.getPageById(r.pageId).setPage(r),this.events.call("afterPageInitialize",Ci(r,this)),this._nextAction(r,e.isChanged)}))})):this._loadingState(!1),e.isChanged=!1})).fail((()=>this._loadingState(!1)))):this._loadingState(!1)})).fail((()=>this._loadingState(!1)))}}co.__loadingStateFunctionName="__loadingState";class uo extends Ea{dispose(){this._sectionsProcessor.dispose(),this._factory.reset(),this._sections=[]}_patchOnChange(){Object.keys(this._factory.metadata).forEach((e=>{this._factory.metadata[e].onChange=()=>this._onChange()}))}_getPageStyle(e,t=!0){return bi(e,this._defaultMargin,t)}_applyCustomizations(){this._sectionsToUnregister.forEach((e=>e())),this._sectionsToUnregister=[],this._sectionPositions.forEach((e=>e())),this._sectionPositions=[],this._sectionsToRegister.forEach((e=>e())),this._sectionsToRegister=[]}_setSectionPosition(e,t){this._pageCss[e]=P.observable(this._getPageStyle(t))}constructor(){super(),this._sectionsToUnregister=[],this._sectionsToRegister=[],this._sectionPositions=[],this._initInProgress=P.observable(!1),this._defaultMargin=24,this._parentMarginOffset=this._defaultMargin+this._defaultMargin/2,this._className="",this._pageCss={},this.events=new A.EventManager,this._factory=new so,this._sectionsProcessor=new co(this._factory,(e=>{this[wa]&&this[wa](e)}),(()=>this[Da]&&this[Da]())),this._sectionsProcessor.events.addHandler("beforeStart",(()=>{this._sections=this._sectionsProcessor.sections,this._beforeStart(),this._patchOnChange()})),this._sectionsProcessor.events.addHandler("beforePageInitialize",(e=>{this.events.call("beforeSectionInitialize",{section:e.page,sectionId:e.pageId,page:this,state:e.state})})),this._sectionsProcessor.events.addHandler("afterPageInitialize",(e=>{this.events.call("afterSectionInitialize",{section:e.page,sectionId:e.pageId,page:this})}))}registerSections(){}canNext(){return this._sectionsProcessor.sections.every(this._sectionCondition)}_sectionCondition(e){return e.metadata.required?e.page()&&e.metadata.canNext(e.page().page):!e.page()||e.metadata.canNext(e.page().page)}canFinish(){for(let e=this._sections.length-1;e>=0;e--)if(this._sections[e].page()&&this._sections[e].metadata.canFinish(this._sections[e].page().page))return!0;return!1}setSectionPosition(e,t){this._sectionPositions.push((()=>{this._setSectionPosition(e,t)}))}registerSection(e,t){this._sectionsToRegister.push((()=>{this._factory.registerMetadata(e,t)}))}unregisterSection(e){this._sectionsToUnregister.push((()=>this._factory.unregisterMetadata(e)))}_loadPanelViewModel(e){return!1}getNextSectionId(e){}initialize(e,t){return this.registerSections(),this._applyCustomizations(),this._sectionsProcessor.initialize((0,f.extend)(!0,{},e)),this._stateManager=this._sectionsProcessor.stateManager,this._sectionsProcessor.iterator.getNextPageId=e=>this.getNextSectionId(e),this._sectionsProcessor.start(),(0,f.createDeferred)().resolve().promise()}_beforeStart(){}commit(){const e=(0,f.createDeferred)();return this._sectionsProcessor.finishAction().done((()=>{e.resolve((0,f.extend)(!0,{},this._stateManager.getCurrentState()))})),e.promise()}_getPageDescription(e,t){return e+1+". "+t.metadata.description}_showPageDescription(e){return e?.metadata?.alwaysShowTitle||this._sections.length>1}}class po extends uo{constructor(e,t){super(),this.wizardOptions=e,this.dataSources=t,this.disabledSectionText=(0,f.getLocalization)('To specify a data source, select "No, I\'d like to create a new data source".',"AnalyticsCoreStringId.Wizard_SelectDataSourceType_Placeholder")}registerSections(){if(this.showPredefinedDataSourceSection()&&(wi(this._factory,this.wizardOptions),this._setSectionPosition(ya.ChoosePredefinedDataSourcePage)),this.showChooseDataSourceTypeSection()){za(this._factory,this.wizardOptions),this._setSectionPosition(ya.ChooseDataSourceTypePage);this._factory.getMetadata(ya.ChooseDataSourceTypePage).disabledText=this.disabledSectionText}this.showPredefinedDataSourceSection()&&this.showChooseDataSourceTypeSection()&&(this._setSectionPosition(ya.ChoosePredefinedDataSourcePage,mi.Top),this._setSectionPosition(ya.ChooseDataSourceTypePage,mi.Bottom))}showPredefinedDataSourceSection(){return this.dataSources.length>0}showChooseDataSourceTypeSection(){return this.wizardOptions.canCreateDataSource&&Ti(this.wizardOptions)}getNextSectionId(e){return!e&&this.showPredefinedDataSourceSection()?ya.ChoosePredefinedDataSourcePage:!e&&this.showChooseDataSourceTypeSection()||e===ya.ChoosePredefinedDataSourcePage&&!this._stateManager.getCurrentState().predefinedDataSourceName&&this.showChooseDataSourceTypeSection()?ya.ChooseDataSourceTypePage:void 0}}function ho(e,t){e.registerMetadata(Ta.SelectDataSourcePage,{setState:(e,t)=>{t.dataSourceType=e.dataSourceType,t.predefinedDataSourceName=e.predefinedDataSourceName},getState:e=>e,resetState:(e,t)=>{e.dataSource=t.dataSource,e.predefinedDataSourceName=null},create:()=>new po(t,t.predefinedDataSources()),navigationPanelText:(0,f.getLocalization)("Select Data Source","ASPxReportsStringId.ReportDesigner_Wizard_SelectDataSource"),template:"dx-wizard-fullscreen-page"})}class mo extends uo{constructor(e){super(),this._dataSourceWizardOptions=e}registerSections(){if(this._dataSourceWizardOptions.connectionStrings.json().length>0&&(qi(this._factory,this._dataSourceWizardOptions.connectionStrings.json,this._dataSourceWizardOptions.allowCreateNewJsonConnection,this._dataSourceWizardOptions.callbacks.getItemsProviderCallback,this._dataSourceWizardOptions.getJsonConnectionStrings),this._setSectionPosition(Pa.SpecifyJsonConnectionPage,this._dataSourceWizardOptions.rtl?mi.Right:mi.Left)),this._dataSourceWizardOptions.allowCreateNewJsonConnection&&0===this._dataSourceWizardOptions.connectionStrings.json().length){Ri(this._factory,this._dataSourceWizardOptions.requestWrapper,this._dataSourceWizardOptions.callbacks.getItemsProviderCallback),this._setSectionPosition(Pa.ChooseJsonSourcePage,this._dataSourceWizardOptions.rtl?mi.Right:mi.Left);this._factory.getMetadata(Pa.ChooseJsonSourcePage).disabledText=(0,A.getLocalization)('To create a data connection, select "No, I\'d like to create a new data connection".',"AnalyticsCoreStringId.JsonDSWizard_CreateNewConnectionPage_Placeholder")}Zi(this._factory,this._dataSourceWizardOptions.requestWrapper,this._dataSourceWizardOptions.callbacks),this._setSectionPosition(Pa.ChooseJsonSchemaPage,this._dataSourceWizardOptions.rtl?mi.Left:mi.Right);this._factory.getMetadata(Pa.ChooseJsonSchemaPage).disabledText=(0,A.getLocalization)("To select data fields, choose or create a data connection.","AnalyticsCoreStringId.JsonDSWizard_ChooseJsonSchemaPage_Placeholder")}getNextSectionId(e){if(!e&&this._dataSourceWizardOptions.connectionStrings.json().length>0)return Pa.SpecifyJsonConnectionPage;if(!e)return Pa.ChooseJsonSourcePage;if(this._dataSourceWizardOptions.allowCreateNewJsonConnection){if(e===Pa.SpecifyJsonConnectionPage)return Pa.ChooseJsonSchemaPage;if(e===Pa.ChooseJsonSourcePage)return Pa.ChooseJsonSchemaPage}else if(e===Pa.SpecifyJsonConnectionPage)return Pa.ChooseJsonSchemaPage}canNext(){const e=this._sectionsProcessor.getPageById(Pa.ChooseJsonSchemaPage);return e&&e.page()&&e.metadata.canNext(e.page().page)}}function go(e,t){e.registerMetadata(Ta.SpecifyJsonDataSourceSettingsPage,{setState:(e,t)=>{t.jsonDataSourceWizard.connectionName=e.jsonDataSourceWizard.connectionName,t.jsonDataSourceWizard.newConnectionName=e.jsonDataSourceWizard.newConnectionName,t.jsonDataSourceWizard.jsonSource=e.jsonDataSourceWizard.jsonSource,t.jsonDataSourceWizard.dataSourceName=e.jsonDataSourceWizard.dataSourceName,t.jsonDataSourceWizard.jsonScheme=e.jsonDataSourceWizard.jsonScheme,t.jsonDataSourceWizard.rootElement=e.jsonDataSourceWizard.rootElement},getState:e=>e,resetState:(e,t)=>{e.jsonDataSourceWizard.connectionName=t.jsonDataSourceWizard.connectionName,e.jsonDataSourceWizard.jsonSource=t.jsonDataSourceWizard.jsonSource,e.jsonDataSourceWizard.dataSourceName=t.jsonDataSourceWizard.dataSourceName,e.jsonDataSourceWizard.newConnectionName=t.jsonDataSourceWizard.newConnectionName,e.jsonDataSourceWizard.jsonScheme=t.jsonDataSourceWizard.jsonScheme,e.jsonDataSourceWizard.rootElement=t.jsonDataSourceWizard.rootElement},create:()=>new mo(t),navigationPanelText:(0,A.getLocalization)("Specify Data Source Settings","AnalyticsCoreStringId.Wizard_SpecifyDataSourceSettingsPage"),template:"dx-wizard-fullscreen-page"})}class xo extends uo{constructor(e){super(),this._dataSourceWizardOptions=e}getNextSectionId(e){if(!e)return Pa.ChooseSqlConnectionPage;if(e===Pa.ChooseSqlConnectionPage&&this._stateManager.getCurrentState().sqlDataSourceWizard.name)return Pa.ConfigureQueryPage;if(e===Pa.ConfigureQueryPage){const e=[],t=this._stateManager.getCurrentState().sqlDataSourceWizard;(t.customQueries||[]).length>0&&e.push(Pa.ConfigureQueryParametersPage);return Ba(t).sqlDataSource.queries().length>1&&e.push(Pa.ConfigureMasterDetailRelationshipsPage),e}}registerSections(){Ja(this._factory,this._dataSourceWizardOptions.connectionStrings.sql,this._dataSourceWizardOptions.getSqlConnectionStrings),Ir(this._factory,this._dataSourceWizardOptions),Ur(this._factory,tr(this._dataSourceWizardOptions.callbacks.sqlDataSourceResultSchema)),Mr(this._factory,this._dataSourceWizardOptions.requestWrapper);let e=this._factory.getMetadata(Pa.ChooseSqlConnectionPage);e.description=(0,A.getLocalization)("Choose a data connection.","AnalyticsCoreStringId.SqlDSWizard_PageChooseConnection"),e=this._factory.getMetadata(Pa.ConfigureQueryPage),e.recreate=!0,e.description=(0,A.getLocalization)("Choose predefined queries and/or create custom queries.","AnalyticsCoreStringId.Wizard_Queries_Description"),e.required=!0,e=this._factory.getMetadata(Pa.ConfigureMasterDetailRelationshipsPage),e.description=(0,A.getLocalization)("Configure master-detail relationships.","AnalyticsCoreStringId.SqlDSWizard_PageConfigureMasterDetailRelations"),e.disabledText=(0,A.getLocalization)("To create a master-detail relationship, select two or more queries.","AnalyticsCoreStringId.Wizard_MasterDetailRelationship_Placeholder"),e=this._factory.getMetadata(Pa.ConfigureQueryParametersPage),e.description=(0,A.getLocalization)("Configure query parameters.","AnalyticsCoreStringId.SqlDSWizard_PageConfigureParameters"),e.disabledText=(0,A.getLocalization)("To specify query parameters, select a parameterized stored procedure or create a custom query.","AnalyticsCoreStringId.Wizard_ConfigureQueryParameters_Placeholder"),this._setSectionPosition(Pa.ChooseSqlConnectionPage,this._dataSourceWizardOptions.rtl?mi.TopRight:mi.TopLeft),this._setSectionPosition(Pa.ConfigureQueryPage,this._dataSourceWizardOptions.rtl?mi.TopLeft:mi.TopRight),this._setSectionPosition(Pa.ConfigureQueryParametersPage,this._dataSourceWizardOptions.rtl?mi.BottomLeft:mi.BottomRight),this._setSectionPosition(Pa.ConfigureMasterDetailRelationshipsPage,this._dataSourceWizardOptions.rtl?mi.BottomRight:mi.BottomLeft)}}function fo(e,t){e.registerMetadata(Ta.SpecifySqlDataSourceSettingsPage,{setState:(e,t)=>{t.sqlDataSourceWizard.customQueries=e.sqlDataSourceWizard.customQueries,t.sqlDataSourceWizard.name=e.sqlDataSourceWizard.name,t.sqlDataSourceWizard.queryName=e.sqlDataSourceWizard.name,t.sqlDataSourceWizard.sqlDataSourceJSON=e.sqlDataSourceWizard.sqlDataSourceJSON,t.sqlDataSourceWizard.relations=e.sqlDataSourceWizard.relations},getState:e=>e,resetState:(e,t)=>{e.sqlDataSourceWizard=(0,f.extend)(!0,{},t)},create:()=>new xo(t),navigationPanelText:(0,A.getLocalization)("Specify Data Source Settings","AnalyticsCoreStringId.Wizard_SpecifyDataSourceSettingsPage"),template:"dx-wizard-fullscreen-page"})}class bo extends uo{constructor(e){super(),this._dataSourceWizardOptions=e}getNextSectionId(e){if(!e)return Pa.ConfigureFederatedQueriesPage;if(e===Pa.ConfigureFederatedQueriesPage){if(qa(this._stateManager.getCurrentState().federationDataSourceWizard,this._dataSourceWizardOptions.dataSources).queries().length>1)return Pa.ConfigureFederatedMasterDetailRelationshipsPage}}_showPageDescription(){return!0}registerSections(){Ls(this._factory,this._dataSourceWizardOptions),Vs(this._factory,ar(this._dataSourceWizardOptions.callbacks.federationDataSourceResultSchema),this._dataSourceWizardOptions);let e=this._factory.getMetadata(Pa.ConfigureFederatedQueriesPage);e.description=(0,A.getLocalization)("Create a federated query.","DataAccessUIStringId.WizardPageConfigureFederatedQueryPage"),e=this._factory.getMetadata(Pa.ConfigureFederatedMasterDetailRelationshipsPage),e.description=(0,A.getLocalization)("Configure master-detail relationships.","AnalyticsCoreStringId.SqlDSWizard_PageConfigureMasterDetailRelations"),e.disabledText=(0,A.getLocalization)("To create a master-detail relationship, select two or more queries.","AnalyticsCoreStringId.Wizard_MasterDetailRelationship_Placeholder"),this._setSectionPosition(Pa.ConfigureFederatedQueriesPage,mi.Top),this._setSectionPosition(Pa.ConfigureFederatedMasterDetailRelationshipsPage,mi.Bottom)}}function yo(e,t){e.registerMetadata(Ta.SpecifyFederationDataSourceSettingsPage,{setState:(e,t)=>{t.federationDataSourceWizard.federationDataSourceJSON=e.federationDataSourceWizard.federationDataSourceJSON,t.federationDataSourceWizard.federatedQueries=e.federationDataSourceWizard.federatedQueries,t.federationDataSourceWizard.relations=e.federationDataSourceWizard.relations,t.federationDataSourceWizard.name=e.federationDataSourceWizard.name},getState:e=>e,resetState:(e,t)=>{e.federationDataSourceWizard=(0,f.extend)(!0,{},t)},create:()=>new bo(t),navigationPanelText:(0,A.getLocalization)("Specify Data Source Settings","AnalyticsCoreStringId.Wizard_SpecifyDataSourceSettingsPage"),template:"dx-wizard-fullscreen-page"})}class So extends A.Disposable{constructor(e){super(),this._steps=[],this._disposables.push(e._currentPage.subscribe((e=>{const t=this._steps.filter((t=>t.pageIds.some((t=>t===e.pageId))))[0];t&&(t.currentPageId=e.pageId,t.disabled(!1),this._setStepVisible(t.stepIndex))}))),Object.keys(e.pageFactory.metadata).forEach((t=>{const a=e.pageFactory.metadata[t],i=this._steps.filter((e=>e.text===a.navigationPanelText))[0];if(i)i.pageIds.push(t);else{const i={text:a.navigationPanelText,pageIds:[t],currentPageId:null,stepIndex:this._steps.length,disabled:P.observable(!0),visible:P.observable(!0)};this._disposables.push(i.isActive=P.computed((()=>e._currentPage()&&i.currentPageId===e._currentPage().pageId))),i.clickAction=()=>{i.isActive()||e.goToPage(i.currentPageId)},this._steps.push(i)}})),this._disposables.push(this.isVisible=P.computed((()=>this._steps.filter((e=>e.visible())).length>1)))}resetAll(){this._steps.forEach((e=>{e.disabled(!0)}))}_currentStep(e){return this._steps.filter((t=>t.currentPageId===e))[0]}_reset(e){const t=this._currentStep(e);t&&t.disabled(!0)}_resetNextPages(e){const t=this._currentStep(e);if(t)for(let e=t.stepIndex+1;e<this._steps.length;e++)this._steps[e].disabled(!0)}_setStepVisible(e){const t=this._steps.filter(((t,a)=>a<e));t.length>0&&!t.some((e=>!e.disabled()))&&t.forEach((e=>e.visible(!1)))}}class _o extends wr{constructor(e,t){super(e,t),this._extendCssClass="dx-wizard-fullscreen",this.navigationPanel=P.observable(null),this.isVisible.subscribe((e=>{e||(this.navigationPanel()&&this.navigationPanel().resetAll(),this.navigationPanel()&&this.navigationPanel().dispose(),this._onCloseCallback&&this._onCloseCallback())}))}_onClose(e){this._onCloseCallback=e}onFinish(){this.navigationPanel().dispose(),super.onFinish()}_initPage(e){return e.onChange&&e.onChange((()=>this.navigationPanel()._resetNextPages(e.pageId))),super._initPage(e)}_onResetPage(e){this.navigationPanel()._reset(e.pageId)}start(e){e&&(this._finishCallback=e),this.navigationPanel()&&this.navigationPanel().resetAll(),this.navigationPanel()&&this.navigationPanel().dispose(),this.navigationPanel(new So(this)),super.start()}_pageDescription(){const e=this.navigationPanel()._steps.filter((e=>e.isActive()))[0];return e?e.text:this.pageFactory.getMetadata(this._currentPage().pageId).description}_description(){return""}}class vo extends _o{constructor(e,t){super(e,t.callbacks.finishCallback),this._dataSourceWizardOptions=t}initialize(e,t=(e,t)=>new Co(e,t,this._dataSourceWizardOptions,(e=>this._onResetPage(e)))){this._dataSourceWizardOptions.sqlDataSourceAvailable||!(0,f.isEmptyObject)(e.sqlDataSourceWizard)?e.dataSourceType=Ia.Sql:this._dataSourceWizardOptions.jsonDataSourceAvailable||e.jsonDataSourceWizard.jsonSource?e.dataSourceType=Ia.Json:!this._dataSourceWizardOptions.objectDataSourceAvailable&&(0,f.isEmptyObject)(e.objectDataSourceWizard)||(e.dataSourceType=Ia.Object),super.initialize(e,t)}canRunWizard(){return this._dataSourceWizardOptions.canRunWizard}_description(){return(0,A.getLocalization)("Data Source Wizard","AnalyticsCoreStringId.DSWizard_Title")}}class Co extends Pr{constructor(e,t,a,i){super(e,t,i),this._dataSourceOptions=a}_shouldSelectDataSource(){return Ti(this._dataSourceOptions)||this._dataSourceOptions.predefinedDataSources().length>0}getNextPageId(e){return!e&&this._shouldSelectDataSource()?Ta.SelectDataSourcePage:!e&&this._dataSourceOptions.sqlDataSourceAvailable?Ta.SpecifySqlDataSourceSettingsPage:!e&&this._dataSourceOptions.jsonDataSourceAvailable?Ta.SpecifyJsonDataSourceSettingsPage:!e&&this._dataSourceOptions.objectDataSourceAvailable?Ta.SpecifyObjectDataSourceSettingsPage:e===Ta.SelectDataSourcePage&&this._getCurrentState().dataSourceType===Ia.Json?Ta.SpecifyJsonDataSourceSettingsPage:e===Ta.SelectDataSourcePage&&this._getCurrentState().dataSourceType===Ia.Sql?Ta.SpecifySqlDataSourceSettingsPage:e===Ta.SelectDataSourcePage&&this._getCurrentState().dataSourceType===Ia.Object?Ta.SpecifyObjectDataSourceSettingsPage:e===Ta.SelectDataSourcePage&&this._getCurrentState().dataSourceType===Ia.Federation?Ta.SpecifyFederationDataSourceSettingsPage:void 0}}function To(e){const t=new ro,a=new vo(t,e);return ho(t,e),fo(t,e),yo(t,e),go(t,e),Ks(t,e),a}class Po{constructor(e,t){this.handler=e,this.isVisible=P.observable(!0),this.isDisabled=P.observable(!1),this.text=t}}class No extends x.Editor{constructor(e,t,a,i){super(e,t,a,i)}generateValue(e){return this.undoValue||this._disposables.push(this.undoValue=P.computed({read:()=>this.value(),write:t=>{e().start(),this.value(t),e().end()}})),this.undoValue}}class wo extends it.PopupEditorBase{_createAddQueryButton(){const e=new Bs("queries",(0,f.getLocalization)("Federated Queries","DataAccessUIStringId.FederatedQueryCollectionList_Title"),"list",!1,P.observable({showQbCallBacks:this._queriesPopupHelper.callBacks})).popoverListItems(),t={text:(0,f.getLocalization)("Add query","AnalyticsCoreStringId.SqlDSWizard_AddQuery"),items:e.map((e=>({text:e.name,onClick:e.addAction}))),dropDownOptions:{width:150,container:".dx-designer-viewport"},useItemTextAsTitle:!1};this.buttonItems.push({toolbar:"bottom",location:"before",template:function(){return(0,x.getTemplate)("dxrd-managequeries-selectbox")},options:t})}constructor(e,t,a=!1){super(),this._dataSource=e,this._callBack=t,this.rtl=a,this.className="dxrd-federated-manageQueries-editor",this._disposables.push(this._queriesPopupHelper=new $s(this._dataSource,this._dataSource.queries,{},a)),this._createAddQueryButton(),this.queriesStoreData=P.pureComputed((()=>this._dataSource&&this._dataSource.queries().map((e=>({name:e.alias(),id:e.alias(),type:Ue[e.queryType()]}))))),this._disposables.push(this.queriesStoreData),this.queriesGrid={rtlEnabled:a,dataSource:this.queriesStoreData,showColumnLines:!1,hoverStateEnabled:!0,scrolling:{mode:"infinite"},height:"100%",editing:{mode:"cell",allowUpdating:!0},onRowUpdating:e=>{e.oldData.alias=e.newData.name},columns:[{dataField:"name",get caption(){return(0,f.getLocalization)("Name","AnalyticsCoreStringId.CollectionEditor_Name_Placeholder")}},{type:"buttons",width:100,buttons:[{icon:"edit",onClick:e=>{this._queriesPopupHelper.editQuery(e.row.data.type,e.row.data.id)},cssClass:"dxrd-manage-queries-actions dxd-icon-highlighted dxrd-image-operations-edit-query",template:(0,x.getTemplate)("dxrd-svg-operations-edit")},{icon:"delete",onClick:e=>{this._dataSource.removeQuery(e.row.data.id)},cssClass:"dxrd-manage-queries-actions dxd-icon-highlighted dxrd-image-recycle-bin",template:(0,x.getTemplate)("dxrd-svg-operations-recycle_bin")}]}]}}save(){this.queriesStoreData().forEach((e=>{const t=(0,f.findFirstItemMatchesCondition)(this._dataSource.queries(),(t=>t.alias()===e.id));t.sources().forEach((e=>this._dataSource.addSource(e))),t&&e.id!==e.name&&t.alias(e.name)})),this._callBack(),super.save(),this.dispose()}canSave(){return!!this.queriesStoreData().length}close(){super.close(),this.dispose()}dispose(){super.dispose(),this.queriesGrid=null,this._dataSource=null,this._callBack=null}title(){return(0,f.getLocalization)("Manage Queries","DataAccessUIStringId.FederatedQueryCollectionEditorForm_Title")}}class Do extends A.Disposable{constructor(e,t,a=e=>!0,i){super(),this.editableObj=t,this.getDisplayName=i,this._collapsed=e,this._disposables.push(this.visible=P.pureComputed((()=>!this.disabled()&&!this._collapsed()))),this._disposables.push(this.disabled=P.pureComputed((()=>{const e=!t()||!a(t());return e&&!this._collapsed()&&this._collapsed(!0),e})))}dispose(){super.dispose(),this._collapsed=null}toogle(){this._collapsed(!this._collapsed())}get title(){return this.getDisplayName(this.editableObj())}}It.registerEditors({bool:{header:"dx-boolean-select",custom:"dxqb-property-editor"},combobox:{header:"dx-combobox",custom:"dxqb-property-editor"},comboboxUndo:{header:"dx-combobox-undo",custom:"dxqb-property-editor",editorType:No},text:{header:"dx-text",custom:"dxqb-property-editor"},filterEditor:{header:"dxrd-filterstring",custom:"dxqb-property-editor"},filterGroupEditor:{header:"dxrd-filterstringgroup",custom:"dxqb-property-editor"},numeric:{header:"dx-numeric",custom:"dxqb-property-editor"}}),(0,it.addToBindingsCache)("attr: { x1: startPoint().relativeX, y1: startPoint().relativeY, x2: endPoint().relativeX, y2: endPoint().relativeY }",(function(e,t){return{attr:function(){return{x1:e.$data.startPoint().relativeX,y1:e.$data.startPoint().relativeY,x2:e.$data.endPoint().relativeX,y2:e.$data.endPoint().relativeY}}}})),(0,it.addToBindingsCache)("styleunit: position, trackCursor: underCursor, style: { 'marginLeft': -3 * _context.zoom() + 'px', 'marginTop': -3 * _context.zoom() + 'px' }, draggable: $root.connectingPointDragHandler",(function(e,t){return{styleunit:function(){return e.$data.position},trackCursor:function(){return e.$data.underCursor},style:function(){return{marginLeft:-3*e.$data._context.zoom()+"px",marginTop:-3*e.$data._context.zoom()+"px"}},draggable:function(){return e.$root.connectingPointDragHandler}}})),(0,it.addToBindingsCache)("draggable: $root.connectionPointDragHandler, styleunit: { top: relativeY, left: relativeX }",(function(e,t){return{draggable:function(){return e.$root.connectionPointDragHandler},styleunit:function(){return{top:e.$data.relativeY,left:e.$data.relativeX}}}})),(0,it.addToBindingsCache)("styleunit: position, trackCursor: underCursor",(function(e,t){return{styleunit:function(){return e.$data.position},trackCursor:function(){return e.$data.underCursor}}})),(0,it.addToBindingsCache)("styleunit: position, dxclick: function() {}, draggable: $root.dragHandler",(function(e,t){return{styleunit:function(){return e.$data.position},dxclick:function(){return function(){}},draggable:function(){return e.$root.dragHandler}}})),(0,it.addToBindingsCache)("template: { name: 'dxdd-connection-line' }",(function(e,t){return{template:function(){return{name:"dxdd-connection-line"}}}})),(0,it.addToBindingsCache)("with: startPoint",(function(e,t){return{with:function(){return e.$data.startPoint}}})),(0,it.addToBindingsCache)("template: { name: 'dxdd-connection-point-selection' }",(function(e,t){return{template:function(){return{name:"dxdd-connection-point-selection"}}}})),(0,it.addToBindingsCache)("with: endPoint",(function(e,t){return{with:function(){return e.$data.endPoint}}})),(0,it.addToBindingsCache)("attr: { id: 'dxqb-arrow_end' + connectorID() }",(function(e,t){return{attr:function(){return{id:"dxqb-arrow_end"+e.$data.connectorID()}}}})),(0,it.addToBindingsCache)("attr: { id: 'dxqb-arrow_start' + connectorID() }",(function(e,t){return{attr:function(){return{id:"dxqb-arrow_start"+e.$data.connectorID()}}}})),(0,it.addToBindingsCache)("attr: { points: routePointsSet, 'marker-end': showArrow() ? 'url(#dxqb-arrow_end' + connectorID() + ')' : '', 'marker-start': showRightArrow() ? 'url(#dxqb-arrow_start' + connectorID() + ')' : '' }",(function(e,t){return{attr:function(){return{points:e.$data.routePointsSet,"marker-end":e.$data.showArrow()?"url(#dxqb-arrow_end"+e.$data.connectorID()+")":"","marker-start":e.$data.showRightArrow()?"url(#dxqb-arrow_start"+e.$data.connectorID()+")":""}}}})),(0,it.addToBindingsCache)("if: showArrow",(function(e,t){return{if:function(){return e.$data.showArrow}}})),(0,it.addToBindingsCache)("if: showRightArrow",(function(e,t){return{if:function(){return e.$data.showRightArrow}}})),(0,it.addToBindingsCache)("styleunit: position, visible: isVisible",(function(e,t){return{styleunit:function(){return e.$data.position},visible:function(){return e.$data.isVisible}}})),(0,it.addToBindingsCache)("styleunit: position",(function(e,t){return{styleunit:function(){return e.$data.position}}})),(0,it.addToBindingsCache)("trackCursor: underCursor, click: $root.selectItemProperties",(function(e,t){return{trackCursor:function(){return e.$data.underCursor},click:function(){return e.$root.selectItemProperties}}})),(0,it.addToBindingsCache)("styleunit: position, dxclick: function() {}, visible: isVisible",(function(e,t){return{styleunit:function(){return e.$data.position},dxclick:function(){return function(){}},visible:function(){return e.$data.isVisible}}})),(0,it.addToBindingsCache)("styleunit: position, style: { cursor: isVerticalLine ? 'ew-resize' : 'ns-resize' }, routeLineDraggable: { starting: $root.resizeHandler.starting, stopped: function() { resizeStopped(); $root.resizeHandler.stopped(); }, forceResize: resizeHandler }",(function(e,t){return{styleunit:function(){return e.$data.position},style:function(){return{cursor:e.$data.isVerticalLine?"ew-resize":"ns-resize"}},routeLineDraggable:function(){return{starting:e.$root.resizeHandler.starting,stopped:function(){e.$data.resizeStopped(),e.$root.resizeHandler.stopped()},forceResize:e.$data.resizeHandler}}}})),(0,it.addToBindingsCache)("template: { name: 'dxdd-routed-connection-line' }",(function(e,t){return{template:function(){return{name:"dxdd-routed-connection-line"}}}})),(0,it.addToBindingsCache)("foreach: routeLineWrappers",(function(e,t){return{foreach:function(){return e.$data.routeLineWrappers}}})),(0,it.addToBindingsCache)("with: $parent",(function(e,t){return{with:function(){return e.$parent}}})),(0,it.addToBindingsCache)("ifnot: isLocked",(function(e,t){return{ifnot:function(){return e.$data.isLocked}}})),(0,it.addToBindingsCache)("if: isLocked",(function(e,t){return{if:function(){return e.$data.isLocked}}})),(0,it.addToBindingsCache)("styleunit: { lineHeight: positionLineHeightWithoutMargins }, style: css",(function(e,t){return{styleunit:function(){return{lineHeight:e.$data.positionLineHeightWithoutMargins}},style:function(){return e.$data.css}}})),(0,it.addToBindingsCache)("template: contenttemplate, styleunit: { 'height': positionLineHeightWithoutMargins, 'width': positionWidthWithoutMargins }",(function(e,t){return{template:function(){return e.$data.contenttemplate},styleunit:function(){return{height:e.$data.positionLineHeightWithoutMargins,width:e.$data.positionWidthWithoutMargins}}}})),(0,it.addToBindingsCache)("event: { dblclick: $root.inlineTextEdit.show($element) }, css: {'dxrd-selected': selected, 'dxrd-focused': focused }, resizable: $root.resizeHandler, draggable: $root.dragHandler, styleunit: position, trackCursor: underCursor",(function(e,t){return{event:function(){return{dblclick:e.$root.inlineTextEdit.show(t)}},css:function(){return{"dxrd-selected":e.$data.selected,"dxrd-focused":e.$data.focused}},resizable:function(){return e.$root.resizeHandler},draggable:function(){return e.$root.dragHandler},styleunit:function(){return e.$data.position},trackCursor:function(){return e.$data.underCursor}}})),(0,it.addToBindingsCache)("template: contenttemplate, styleunit: { 'height': positionLineHeightWithoutMargins, 'width': positionWidthWithoutMargins}",(function(e,t){return{template:function(){return e.$data.contenttemplate},styleunit:function(){return{height:e.$data.positionLineHeightWithoutMargins,width:e.$data.positionWidthWithoutMargins}}}})),(0,it.addToBindingsCache)("dxTextArea: { value: text, onKeyUp: keypressAction, valueChangeEvent: 'keyup' }",(function(e,t){return{dxTextArea:function(){return{value:e.$data.text,onKeyUp:e.$data.keypressAction,valueChangeEvent:"keyup"}}}})),(0,it.addToBindingsCache)("text: getControlModel().text",(function(e,t){return{text:function(){return e.$data.getControlModel().text}}})),(0,it.addToBindingsCache)("if: !$root.inlineTextEdit.visible()",(function(e,t){return{if:function(){return!e.$root.inlineTextEdit.visible()}}})),(0,it.addToBindingsCache)("if: $root.inlineTextEdit.visible",(function(e,t){return{if:function(){return e.$root.inlineTextEdit.visible}}})),(0,it.addToBindingsCache)("with: $root.inlineTextEdit",(function(e,t){return{with:function(){return e.$root.inlineTextEdit}}})),(0,it.addToBindingsCache)("if: getControlModel().type() === 'Ellipse'",(function(e,t){return{if:function(){return"Ellipse"===e.$data.getControlModel().type()}}})),(0,it.addToBindingsCache)("if: getControlModel().type() === 'Condition'",(function(e,t){return{if:function(){return"Condition"===e.$data.getControlModel().type()}}})),(0,it.addToBindingsCache)("if: !getControlModel().type()",(function(e,t){return{if:function(){return!e.$data.getControlModel().type()}}})),(0,it.addToBindingsCache)("foreach: connectingPoints",(function(e,t){return{foreach:function(){return e.$data.connectingPoints}}})),(0,it.addToBindingsCache)("template: 'dxdd-connecting-point'",(function(e,t){return{template:function(){return"dxdd-connecting-point"}}})),(0,it.addToBindingsCache)("styleunit: { 'width': Math.min(pageWidth(), $root.surfaceSize()) }, click: function(_, e) { $root.selection.clickHandler(null, e); e.stopPropagation(); }, keyDownActions: $root.actionLists.getViewModel()",(function(e,t){return{styleunit:function(){return{width:Math.min(e.$data.pageWidth(),e.$root.surfaceSize())}},click:function(){return function(t,a){e.$root.selection.clickHandler(null,a),a.stopPropagation()}},keyDownActions:function(){return e.$root.actionLists.getViewModel()}}})),(0,it.addToBindingsCache)("styleunit: { minWidth: Math.min(pageWidth(), $root.surfaceSize()), maxWidth: pageWidth(),  maxHeight: pageHeight() + 20 }",(function(e,t){return{styleunit:function(){return{minWidth:Math.min(e.$data.pageWidth(),e.$root.surfaceSize()),maxWidth:e.$data.pageWidth(),maxHeight:e.$data.pageHeight()+20}}}})),(0,it.addToBindingsCache)("selectable: { selection: $root.selection, zoom: zoom }",(function(e,t){return{selectable:function(){return{selection:e.$root.selection,zoom:e.$data.zoom}}}})),(0,it.addToBindingsCache)("styleunit: { 'width': pageWidth(), 'height': pageHeight() }, trackCursor: underCursor",(function(e,t){return{styleunit:function(){return{width:e.$data.pageWidth(),height:e.$data.pageHeight()}},trackCursor:function(){return e.$data.underCursor}}})),(0,it.addToBindingsCache)("foreach: controls",(function(e,t){return{foreach:function(){return e.$data.controls}}})),(0,it.addToBindingsCache)("template: { name: isSelected() ? selectiontemplate : template }",(function(e,t){return{template:function(){return{name:e.$data.isSelected()?e.$data.selectiontemplate:e.$data.template}}}})),(0,it.addToBindingsCache)("attr: { x1: startPoint.x, y1: startPoint.y, x2: endPoint.x, y2: endPoint.y }",(function(e,t){return{attr:function(){return{x1:e.$data.startPoint.x,y1:e.$data.startPoint.y,x2:e.$data.endPoint.x,y2:e.$data.endPoint.y}}}})),(0,it.addToBindingsCache)("styleunit: { 'width': $root.surfaceSize }, click: function(_, e) { $root.selection.clickHandler(null, e); e.stopPropagation(); }, keyDownActions: $root.actionLists.getViewModel()",(function(e,t){return{styleunit:function(){return{width:e.$root.surfaceSize}},click:function(){return function(t,a){e.$root.selection.clickHandler(null,a),a.stopPropagation()}},keyDownActions:function(){return e.$root.actionLists.getViewModel()}}})),(0,it.addToBindingsCache)("dxScrollView: { direction: 'both', showScrollbar: 'always', scrollByContent: false, scrollByThumb: true, bounceEnabled: false, useNative: true, height: '100%' }",(function(e,t){return{dxScrollView:function(){return{direction:"both",showScrollbar:"always",scrollByContent:!1,scrollByThumb:!0,bounceEnabled:!1,useNative:!0,height:"100%"}}}})),(0,it.addToBindingsCache)("styleunit: { 'minWidth': pageWidth, 'minHeight': pageHeight }, trackCursor: { recalculateBounds: true, underCursor: underCursor }",(function(e,t){return{styleunit:function(){return{minWidth:e.$data.pageWidth,minHeight:e.$data.pageHeight}},trackCursor:function(){return{recalculateBounds:!0,underCursor:e.$data.underCursor}}}})),(0,it.addToBindingsCache)("trackCursor: { recalculateBounds: true, underCursor: underCursor }, style: { 'z-index' : $parent.dragDropStarted() ? 2 : null }",(function(e,t){return{trackCursor:function(){return{recalculateBounds:!0,underCursor:e.$data.underCursor}},style:function(){return{"z-index":e.$parent.dragDropStarted()?2:null}}}})),(0,it.addToBindingsCache)("text: placeholder()",(function(e,t){return{text:function(){return e.$data.placeholder()}}})),(0,it.addToBindingsCache)("styleunit: { top: position.top(), left: position.left() }",(function(e,t){return{styleunit:function(){return{top:e.$data.position.top(),left:e.$data.position.left()}}}})),(0,it.addToBindingsCache)("attr: {x1 : $data.x, y1: $data.y, x2: $parent.routePoints()[$index()+1].x, y2: $parent.routePoints()[$index()+1].y }",(function(e,t){return{attr:function(){return{x1:e.$data.x,y1:e.$data.y,x2:e.$parent.routePoints()[e.$index()+1].x,y2:e.$parent.routePoints()[e.$index()+1].y}}}})),(0,it.addToBindingsCache)("if: tables().length === 0",(function(e,t){return{if:function(){return 0===e.$data.tables().length}}})),(0,it.addToBindingsCache)("foreach: relations",(function(e,t){return{foreach:function(){return e.$data.relations}}})),(0,it.addToBindingsCache)("foreach: conditions",(function(e,t){return{foreach:function(){return e.$data.conditions}}})),(0,it.addToBindingsCache)("if: !isSelected()",(function(e,t){return{if:function(){return!e.$data.isSelected()}}})),(0,it.addToBindingsCache)("foreach: tables",(function(e,t){return{foreach:function(){return e.$data.tables}}})),(0,it.addToBindingsCache)("if: isSelected()",(function(e,t){return{if:function(){return e.$data.isSelected()}}})),(0,it.addToBindingsCache)("template: selectiontemplate",(function(e,t){return{template:function(){return e.$data.selectiontemplate}}})),(0,it.addToBindingsCache)("with: $root.columnDragHandler.dragDropConnector",(function(e,t){return{with:function(){return e.$root.columnDragHandler.dragDropConnector}}})),(0,it.addToBindingsCache)("foreach: routePoints",(function(e,t){return{foreach:function(){return e.$data.routePoints}}})),(0,it.addToBindingsCache)("if: $index() < ($parent.routePoints().length - 1)",(function(e,t){return{if:function(){return e.$index()<e.$parent.routePoints().length-1}}})),(0,it.addToBindingsCache)("trackCursor: { recalculateBounds: true, underCursor: underCursor }, draggable: $root.columnDragHandler, css: cssClasses($root.surface(), $root.columnDragHandler, $parent), click: $root.selectItemProperties",(function(e,t){return{trackCursor:function(){return{recalculateBounds:!0,underCursor:e.$data.underCursor}},draggable:function(){return e.$root.columnDragHandler},css:function(){return e.$data.cssClasses(e.$root.surface(),e.$root.columnDragHandler,e.$parent)},click:function(){return e.$root.selectItemProperties}}})),(0,it.addToBindingsCache)("dxCheckBox: { value: selectedWrapper }, click: function(surface, e) { surface.toggleSelected(); e.stopPropagation(); return true;  }",(function(e,t){return{dxCheckBox:function(){return{value:e.$data.selectedWrapper}},click:function(){return function(e,t){return e.toggleSelected(),t.stopPropagation(),!0}}}})),(0,it.addToBindingsCache)("attr: { title: getControlModel().actualName }",(function(e,t){return{attr:function(){return{title:e.$data.getControlModel().actualName}}}})),(0,it.addToBindingsCache)("text: getControlModel().actualName",(function(e,t){return{text:function(){return e.$data.getControlModel().actualName}}})),(0,it.addToBindingsCache)("trackCursor: underCursor, css: cssClasses(), click: $root.selectItemProperties",(function(e,t){return{trackCursor:function(){return e.$data.underCursor},css:function(){return e.$data.cssClasses()},click:function(){return e.$root.selectItemProperties}}})),(0,it.addToBindingsCache)("attr: { title: getControlModel().name }",(function(e,t){return{attr:function(){return{title:e.$data.getControlModel().name}}}})),(0,it.addToBindingsCache)("text: getControlModel().name",(function(e,t){return{text:function(){return e.$data.getControlModel().name}}})),(0,it.addToBindingsCache)("if:  $data.isAscending()",(function(e,t){return{if:function(){return e.$data.isAscending()}}})),(0,it.addToBindingsCache)("template: 'dxrd-svg-queryBuilder-sorting_asc'",(function(e,t){return{template:function(){return"dxrd-svg-queryBuilder-sorting_asc"}}})),(0,it.addToBindingsCache)("if:  $data.isDescending()",(function(e,t){return{if:function(){return e.$data.isDescending()}}})),(0,it.addToBindingsCache)("template: 'dxrd-svg-queryBuilder-sorting_desc'",(function(e,t){return{template:function(){return"dxrd-svg-queryBuilder-sorting_desc"}}})),(0,it.addToBindingsCache)("if: $data.getControlModel().groupBy",(function(e,t){return{if:function(){return e.$data.getControlModel().groupBy}}})),(0,it.addToBindingsCache)("template: 'dxrd-svg-queryBuilder-group_by'",(function(e,t){return{template:function(){return"dxrd-svg-queryBuilder-group_by"}}})),(0,it.addToBindingsCache)("if: $data.isAggregate()",(function(e,t){return{if:function(){return e.$data.isAggregate()}}})),(0,it.addToBindingsCache)("template: 'dxrd-svg-queryBuilder-aggregate'",(function(e,t){return{template:function(){return"dxrd-svg-queryBuilder-aggregate"}}})),(0,it.addToBindingsCache)("resizable: resizable($root.resizeHandler, $element), styleunit: position",(function(e,t){return{resizable:function(){return e.$data.resizable(e.$root.resizeHandler,t)},styleunit:function(){return e.$data.position}}})),(0,it.addToBindingsCache)("css: {'dxrd-selected': selected, 'dxrd-focused': focused }, draggable: $root.dragHandler, styleunit: position, trackCursor: underCursor, click: $root.selectItemProperties",(function(e,t){return{css:function(){return{"dxrd-selected":e.$data.selected,"dxrd-focused":e.$data.focused}},draggable:function(){return e.$root.dragHandler},styleunit:function(){return e.$data.position},trackCursor:function(){return e.$data.underCursor},click:function(){return e.$root.selectItemProperties}}})),(0,it.addToBindingsCache)("styleunit: { lineHeight: position.lineHeight }, style: css",(function(e,t){return{styleunit:function(){return{lineHeight:e.$data.position.lineHeight}},style:function(){return e.$data.css}}})),(0,it.addToBindingsCache)("template: contenttemplate, styleunit: { 'height': position.lineHeight, 'width': position.width }",(function(e,t){return{template:function(){return e.$data.contenttemplate},styleunit:function(){return{height:e.$data.position.lineHeight,width:e.$data.position.width}}}})),(0,it.addToBindingsCache)("dxCheckBox: { value: selectedWrapper }, click: function(surface, e) { $root.undoEngine().start(); surface.toggleSelected(); $root.undoEngine().end(); e.stopPropagation(); return true; }",(function(e,t){return{dxCheckBox:function(){return{value:e.$data.selectedWrapper}},click:function(){return function(t,a){return e.$root.undoEngine().start(),t.toggleSelected(),e.$root.undoEngine().end(),a.stopPropagation(),!0}}}})),(0,it.addToBindingsCache)("text: $root.columnsLoadingMsg()",(function(e,t){return{text:function(){return e.$root.columnsLoadingMsg()}}})),(0,it.addToBindingsCache)("style: { cursor: selected() ? 'move' : 'default' }",(function(e,t){return{style:function(){return{cursor:e.$data.selected()?"move":"default"}}}})),(0,it.addToBindingsCache)("event: { dblclick: $root.inlineTextEdit.show($element) }, style: { cursor: selected() ? 'move' : 'default' }",(function(e,t){return{event:function(){return{dblclick:e.$root.inlineTextEdit.show(t)}},style:function(){return{cursor:e.$data.selected()?"move":"default"}}}})),(0,it.addToBindingsCache)("text: getControlModel().displaySourceName",(function(e,t){return{text:function(){return e.$data.getControlModel().displaySourceName}}})),(0,it.addToBindingsCache)("if: $data.isSelected()",(function(e,t){return{if:function(){return e.$data.isSelected()}}})),(0,it.addToBindingsCache)("template: titletemplate",(function(e,t){return{template:function(){return e.$data.titletemplate}}})),(0,it.addToBindingsCache)("ifnot: $data.isInitialized()",(function(e,t){return{ifnot:function(){return e.$data.isInitialized()}}})),(0,it.addToBindingsCache)("if: $data.isInitialized()",(function(e,t){return{if:function(){return e.$data.isInitialized()}}})),(0,it.addToBindingsCache)("template: { name: asterisk.template, data: asterisk }",(function(e,t){return{template:function(){return{name:e.$data.asterisk.template,data:e.$data.asterisk}}}})),(0,it.addToBindingsCache)("foreach: columns",(function(e,t){return{foreach:function(){return e.$data.columns}}})),(0,it.addToBindingsCache)("lazy: { template: $data.template  }",(function(e,t){return{lazy:function(){return{template:e.$data.template}}}})),(0,it.addToBindingsCache)("if: !$root.inlineTextEdit.visible() || !selected()",(function(e,t){return{if:function(){return!e.$root.inlineTextEdit.visible()||!e.$data.selected()}}})),(0,it.addToBindingsCache)("if: $root.inlineTextEdit.visible() && selected()",(function(e,t){return{if:function(){return e.$root.inlineTextEdit.visible()&&e.$data.selected()}}})),(0,it.addToBindingsCache)("dxPopup: { showTitle: true, resizeEnabled: true, width: 700, height: 500, maxHeight: '95%', maxWidth: '95%', minHeight: 300, minWidth: 400, title: title(), visible: popupVisible, wrapperAttr: { class: 'dx-filtereditor' }, toolbarItems: buttonItems, showCloseButton: true, container: $root.getPopupContainer($element), position: { of: $root.getPopupContainer($element) }, }",(function(e,t){return{dxPopup:function(){return{showTitle:!0,resizeEnabled:!0,width:700,height:500,maxHeight:"95%",maxWidth:"95%",minHeight:300,minWidth:400,title:e.$data.title(),visible:e.$data.popupVisible,wrapperAttr:{class:"dx-filtereditor"},toolbarItems:e.$data.buttonItems,showCloseButton:!0,container:e.$root.getPopupContainer(t),position:{of:e.$root.getPopupContainer(t)}}}}})),(0,it.addToBindingsCache)("text: queryName",(function(e,t){return{text:function(){return e.$data.queryName}}})),(0,it.addToBindingsCache)("service: { name: 'createRelation' }",(function(e,t){return{service:function(){return{name:"createRelation"}}}})),(0,it.addToBindingsCache)("dxPopupWithAutoHeight: { height: '300px', focusStateEnabled: false, wrapperAttr: { class: 'dx-selectbox-popup-wrapper dx-dropdownlist-popup-wrapper dx-filtereditor-criteriaoperator-popup dx-dropdowneditor-overlay' }, position: $root.rtl ? { my: 'right top', at: 'right bottom', of: popupService.target } : { my: 'left top', at: 'left bottom', of: popupService.target }, container: $root.getPopupContainer($element), target: popupService.target, showTitle: false, showCloseButton: false, animation: {}, hideOnOutsideClick: true, shading: false, minWidth:'170px', maxWidth:'500px', width: 'auto', visible: popupService.visible }",(function(e,t){return{dxPopupWithAutoHeight:function(){return{height:"300px",focusStateEnabled:!1,wrapperAttr:{class:"dx-selectbox-popup-wrapper dx-dropdownlist-popup-wrapper dx-filtereditor-criteriaoperator-popup dx-dropdowneditor-overlay"},position:e.$root.rtl?{my:"right top",at:"right bottom",of:e.$data.popupService.target}:{my:"left top",at:"left bottom",of:e.$data.popupService.target},container:e.$root.getPopupContainer(t),target:e.$data.popupService.target,showTitle:!1,showCloseButton:!1,animation:{},hideOnOutsideClick:!0,shading:!1,minWidth:"170px",maxWidth:"500px",width:"auto",visible:e.$data.popupService.visible}}}})),(0,it.addToBindingsCache)("foreach: masterQueries",(function(e,t){return{foreach:function(){return e.$data.masterQueries}}})),(0,it.addToBindingsCache)("template: { name: 'dx-masterDetail-editor-relation', foreach: relations }",(function(e,t){return{template:function(){return{name:"dx-masterDetail-editor-relation",foreach:e.$data.relations}}}})),(0,it.addToBindingsCache)("attr: { title: $data.getTitle() }, text: queryName",(function(e,t){return{attr:function(){return{title:e.$data.getTitle()}},text:function(){return e.$data.queryName}}})),(0,it.addToBindingsCache)("service: { name: 'setColumn' }",(function(e,t){return{service:function(){return{name:"setColumn"}}}})),(0,it.addToBindingsCache)("dxclick: function() {  $parent.remove($data); }",(function(e,t){return{dxclick:function(){return function(){e.$parent.remove(e.$data)}}}})),(0,it.addToBindingsCache)("dxTextBox: { value: relationName, onFocusOut: function() { isEditable(false); } }, focus: { on: isEditable }",(function(e,t){return{dxTextBox:function(){return{value:e.$data.relationName,onFocusOut:function(){e.$data.isEditable(!1)}}},focus:function(){return{on:e.$data.isEditable}}}})),(0,it.addToBindingsCache)("text: relationName, click: function() { isEditable(true); }",(function(e,t){return{text:function(){return e.$data.relationName},click:function(){return function(){e.$data.isEditable(!0)}}}})),(0,it.addToBindingsCache)("dxclick: create",(function(e,t){return{dxclick:function(){return e.$data.create}}})),(0,it.addToBindingsCache)("foreach: keyColumns",(function(e,t){return{foreach:function(){return e.$data.keyColumns}}})),(0,it.addToBindingsCache)("template: { name: 'dx-masterDetail-editor-keyColumn', data: master }",(function(e,t){return{template:function(){return{name:"dx-masterDetail-editor-keyColumn",data:e.$data.master}}}})),(0,it.addToBindingsCache)("template: { name: 'dx-masterDetail-editor-keyColumn', data: detail }",(function(e,t){return{template:function(){return{name:"dx-masterDetail-editor-keyColumn",data:e.$data.detail}}}})),(0,it.addToBindingsCache)("text: target.column() || target.selectColumnText(), dxclick: showPopup, css: { 'dxd-state-selected': target.isSelected, 'default': !target.column() }",(function(e,t){return{text:function(){return e.$data.target.column()||e.$data.target.selectColumnText()},dxclick:function(){return e.$data.showPopup},css:function(){return{"dxd-state-selected":e.$data.target.isSelected,default:!e.$data.target.column()}}}})),(0,it.addToBindingsCache)("dxdTableView: $data.value",(function(e,t){return{dxdTableView:function(){return e.$data.value}}})),(0,it.addToBindingsCache)("dxPopup: { animation: { show: { type: 'fade', from: 0, to: 1, duration: 700 }, hide: { type: 'fade', from: 1, to: 0, duration: 700 } }, wrapperAttr: { class: 'dxqb-preview' }, visible: isVisible, title: title(), showTitle: true, resizeEnabled: true, shading: true, shadingColor: 'transparent', fullScreen: false, width: 800, height: 544, container: container($element), position: { of: container($element) }, onHidden: function() { $data.data.value(null) }, focusStateEnabled: false }",(function(e,t){return{dxPopup:function(){return{animation:{show:{type:"fade",from:0,to:1,duration:700},hide:{type:"fade",from:1,to:0,duration:700}},wrapperAttr:{class:"dxqb-preview"},visible:e.$data.isVisible,title:e.$data.title(),showTitle:!0,resizeEnabled:!0,shading:!0,shadingColor:"transparent",fullScreen:!1,width:800,height:544,container:e.$data.container(t),position:{of:e.$data.container(t)},onHidden:function(){e.$data.data.value(null)},focusStateEnabled:!1}}}})),(0,it.addToBindingsCache)("dxLoadIndicator: { visible: true }",(function(e,t){return{dxLoadIndicator:function(){return{visible:!0}}}})),(0,it.addToBindingsCache)("dxButton: { text: okButtonText(), onClick: okButtonHandler, disabled: isLoading }",(function(e,t){return{dxButton:function(){return{text:e.$data.okButtonText(),onClick:e.$data.okButtonHandler,disabled:e.$data.isLoading}}}})),(0,it.addToBindingsCache)("dxTextArea: { value: value, valueChangeEvent: 'keyup', readOnly: true }",(function(e,t){return{dxTextArea:function(){return{value:e.$data.value,valueChangeEvent:"keyup",readOnly:!0}}}})),(0,it.addToBindingsCache)("dxAceEditor: { value: value, options: aceOptions, additionalOptions: additionalOptions }",(function(e,t){return{dxAceEditor:function(){return{value:e.$data.value,options:e.$data.aceOptions,additionalOptions:e.$data.additionalOptions}}}})),(0,it.addToBindingsCache)("if: data && !isLoading()",(function(e,t){return{if:function(){return e.$data.data&&!e.$data.isLoading()}}})),(0,it.addToBindingsCache)("template: { name: template, data: data }",(function(e,t){return{template:function(){return{name:e.$data.template,data:e.$data.data}}}})),(0,it.addToBindingsCache)("attr: { class: 'dxd-tableview-title-cell dxd-border-secondary dxd-tableview-resizable' }",(function(e,t){return{attr:function(){return{class:"dxd-tableview-title-cell dxd-border-secondary dxd-tableview-resizable"}}}})),(0,it.addToBindingsCache)("text: name",(function(e,t){return{text:function(){return e.$data.name}}})),(0,it.addToBindingsCache)("dxScrollView: { direction: 'both', scrollByContent: false, showScrollbar: 'always', scrollByThumb: true, bounceEnabled: false, useNative: false, onScroll: $data.onDataScroll, onInitialized: $data.onDataScrollInitialized }",(function(e,t){return{dxScrollView:function(){return{direction:"both",scrollByContent:!1,showScrollbar:"always",scrollByThumb:!0,bounceEnabled:!1,useNative:!1,onScroll:e.$data.onDataScroll,onInitialized:e.$data.onDataScrollInitialized}}}})),(0,it.addToBindingsCache)("text: $data, attr: { class: 'dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable' + $index() }",(function(e,t){return{text:function(){return e.$data},attr:function(){return{class:"dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable"+e.$index()}}}})),(0,it.addToBindingsCache)("text: $parents[1].getImageTooLargeText($data), attr: { class: 'dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable' + $index() }",(function(e,t){return{text:function(){return e.$parents[1].getImageTooLargeText(e.$data)},attr:function(){return{class:"dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable"+e.$index()}}}})),(0,it.addToBindingsCache)("attr: { class: 'dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable' + $index() }",(function(e,t){return{attr:function(){return{class:"dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable"+e.$index()}}}})),(0,it.addToBindingsCache)("attr: { src: 'data:image/bmp;base64,' + $data }",(function(e,t){return{attr:function(){return{src:"data:image/bmp;base64,"+e.$data}}}})),(0,it.addToBindingsCache)("text: noDataText()",(function(e,t){return{text:function(){return e.$data.noDataText()}}})),(0,it.addToBindingsCache)("foreach: rtl ? data.schema.reverse() : data.schema",(function(e,t){return{foreach:function(){return e.$data.rtl?e.$data.data.schema.reverse():e.$data.data.schema}}})),(0,it.addToBindingsCache)("if: data.values",(function(e,t){return{if:function(){return e.$data.data.values}}})),(0,it.addToBindingsCache)("foreach: data.values",(function(e,t){return{foreach:function(){return e.$data.data.values}}})),(0,it.addToBindingsCache)("foreach: $parent.rtl ? $data.reverse() : $data",(function(e,t){return{foreach:function(){return e.$parent.rtl?e.$data.reverse():e.$data}}})),(0,it.addToBindingsCache)("ifnot: $parents[1].isImage($index())",(function(e,t){return{ifnot:function(){return e.$parents[1].isImage(e.$index())}}})),(0,it.addToBindingsCache)("if: $parents[1].isImage($index())",(function(e,t){return{if:function(){return e.$parents[1].isImage(e.$index())}}})),(0,it.addToBindingsCache)("if: $parents[1].isImageTooLarge($data)",(function(e,t){return{if:function(){return e.$parents[1].isImageTooLarge(e.$data)}}})),(0,it.addToBindingsCache)("ifnot: $parents[1].isImageTooLarge($data)",(function(e,t){return{ifnot:function(){return e.$parents[1].isImageTooLarge(e.$data)}}})),(0,it.addToBindingsCache)("foreach: data.values[0]",(function(e,t){return{foreach:function(){return e.$data.data.values[0]}}})),(0,it.addToBindingsCache)("ifnot: data.values",(function(e,t){return{ifnot:function(){return e.$data.data.values}}})),(0,it.addToBindingsCache)("treelist: treeListOptions",(function(e,t){return{treelist:function(){return e.$data.treeListOptions}}})),(0,it.addToBindingsCache)("treeListSearchPanel: { controllers: $data.fieldListModel.treeListOptions().treeListController }",(function(e,t){return{treeListSearchPanel:function(){return{controllers:e.$data.fieldListModel.treeListOptions().treeListController}}}})),(0,it.addToBindingsCache)("dxScrollView: { showScrollbar: 'onHover', scrollByContent: false, scrollByThumb: true, bounceEnabled: false, useNative: false }",(function(e,t){return{dxScrollView:function(){return{showScrollbar:"onHover",scrollByContent:!1,scrollByThumb:!0,bounceEnabled:!1,useNative:!1}}}})),(0,it.addToBindingsCache)("dxCollectionEditor: $data",(function(e,t){return{dxCollectionEditor:function(){return e.$data}}})),(0,it.addToBindingsCache)("css: { 'dx-image-expanded': !collapsed }, template: 'dxrd-svg-collapsed'",(function(e,t){return{css:function(){return{"dx-image-expanded":!e.$data.collapsed}},template:function(){return"dxrd-svg-collapsed"}}})),(0,it.addToBindingsCache)("text: $root.getLocalization(value.name), attr: { title: $root.getLocalization(value.name) }",(function(e,t){return{text:function(){return e.$root.getLocalization(e.$data.value.name)},attr:function(){return{title:e.$root.getLocalization(e.$data.value.name)}}}})),(0,it.addToBindingsCache)("template: { name: 'dx-propertieseditor', data: getProperties({ level: 1 }) }",(function(e,t){return{template:function(){return{name:"dx-propertieseditor",data:e.$data.getProperties({level:1})}}}})),(0,it.addToBindingsCache)("foreach: properties.getEditors()",(function(e,t){return{foreach:function(){return e.$data.properties.getEditors()}}})),(0,it.addToBindingsCache)('template: { name: "dxqb-fieldlist-wrapper", data: fieldListModel }',(function(e,t){return{template:function(){return{name:"dxqb-fieldlist-wrapper",data:e.$data.fieldListModel}}}})),(0,it.addToBindingsCache)("foreach: getEditors()",(function(e,t){return{foreach:function(){return e.$data.getEditors()}}})),(0,it.addToBindingsCache)("click: $root.findControl",(function(e,t){return{click:function(){return e.$root.findControl}}})),(0,it.addToBindingsCache)("text: displayName, attr: { 'title': displayName }",(function(e,t){return{text:function(){return e.$data.displayName},attr:function(){return{title:e.$data.displayName}}}})),(0,it.addToBindingsCache)("lazy: { template: templateName }",(function(e,t){return{lazy:function(){return{template:e.$data.templateName}}}})),(0,it.addToBindingsCache)("dxExpressionEditor: getOptions({ options: value, fieldListProvider: $root.parametersBindingsProvider, displayNameProvider: $root.displayNameProvider && $root.displayNameProvider() })",(function(e,t){return{dxExpressionEditor:function(){return e.$data.getOptions({options:e.$data.value,fieldListProvider:e.$root.parametersBindingsProvider,displayNameProvider:e.$root.displayNameProvider&&e.$root.displayNameProvider()})}}})),(0,it.addToBindingsCache)("if: $data.value",(function(e,t){return{if:function(){return e.$data.value}}})),(0,it.addToBindingsCache)("visible: target.isEditable",(function(e,t){return{visible:function(){return e.$data.target.isEditable}}})),(0,it.addToBindingsCache)("dxTextBox: { value: target._parameterName, onFocusOut: function() { target.createParameter(); target.isEditable(false); } }",(function(e,t){return{dxTextBox:function(){return{value:e.$data.target._parameterName,onFocusOut:function(){e.$data.target.createParameter(),e.$data.target.isEditable(!1)}}}}})),(0,it.addToBindingsCache)("text: target.parameterName, dxclick: showPopup, css: { 'dxd-state-selected': target.isSelected, 'default': target.isDefaultTextDisplayed() }, visible: $data.target.isEditable() === false",(function(e,t){return{text:function(){return e.$data.target.parameterName},dxclick:function(){return e.$data.showPopup},css:function(){return{"dxd-state-selected":e.$data.target.isSelected,default:e.$data.target.isDefaultTextDisplayed()}},visible:function(){return!1===e.$data.target.isEditable()}}})),(0,it.addToBindingsCache)("text: name, attr: { 'title': name }, click: function() { $parents[1].click($data); }",(function(e,t){return{text:function(){return e.$data.name},attr:function(){return{title:e.$data.name}},click:function(){return function(){e.$parents[1].click(e.$data)}}}})),(0,it.addToBindingsCache)("text: $parent.viewModel.defaultDisplay(), click: function() { $parent.viewModel.isEditable(true); $parent.viewModel._parameterName(''); $parent.visible(false); }",(function(e,t){return{text:function(){return e.$parent.viewModel.defaultDisplay()},click:function(){return function(){e.$parent.viewModel.isEditable(!0),e.$parent.viewModel._parameterName(""),e.$parent.visible(!1)}}}})),(0,it.addToBindingsCache)("text: name, click: function() { $parent.click($data); }",(function(e,t){return{text:function(){return e.$data.name},click:function(){return function(){e.$parent.click(e.$data)}}}})),(0,it.addToBindingsCache)("if: $parent.viewModel.canCreateParameters",(function(e,t){return{if:function(){return e.$parent.viewModel.canCreateParameters}}})),(0,it.addToBindingsCache)("event: { dblclick: events.dblclick }, styleunit: padding, css: { 'dx-treelist-item-selected dxd-state-selected': isSelected || isMultiSelected }",(function(e,t){return{event:function(){return{dblclick:e.$data.events.dblclick}},styleunit:function(){return e.$data.padding},css:function(){return{"dx-treelist-item-selected dxd-state-selected":e.$data.isSelected||e.$data.isMultiSelected}}}})),(0,it.addToBindingsCache)('template: "dxqb-treelist-header-item-with-search"',(function(e,t){return{template:function(){return"dxqb-treelist-header-item-with-search"}}})),(0,it.addToBindingsCache)("css: $data._extendCssClass('type-page')",(function(e,t){return{css:function(){return e.$data._extendCssClass("type-page")}}})),(0,it.addToBindingsCache)("event: { click: $parent._itemClick, dblclick: function() { $parent._goToNextPage() } }, attr: { class: 'dxd-back-highlighted dxd-state-normal dxrd-wizard-type-item dx-fontsize-reestablished dxrd-wizard-type-item-border-color dxd-list-item-back-color ' + $parent._extendCssClass('type-item')}, css: { 'dxd-border-secondary dxd-back-secondary': $parent._IsSelected($data) }",(function(e,t){return{event:function(){return{click:e.$parent._itemClick,dblclick:function(){e.$parent._goToNextPage()}}},attr:function(){return{class:"dxd-back-highlighted dxd-state-normal dxrd-wizard-type-item dx-fontsize-reestablished dxrd-wizard-type-item-border-color dxd-list-item-back-color "+e.$parent._extendCssClass("type-item")}},css:function(){return{"dxd-border-secondary dxd-back-secondary":e.$parent._IsSelected(e.$data)}}}})),(0,it.addToBindingsCache)("attr: { class: 'dxrd-wizard-type-image ' + $parent._extendCssClass('type-image') }, css: imageClassName, template: { name: imageTemplateName }",(function(e,t){return{attr:function(){return{class:"dxrd-wizard-type-image "+e.$parent._extendCssClass("type-image")}},css:function(){return e.$data.imageClassName},template:function(){return{name:e.$data.imageTemplateName}}}})),(0,it.addToBindingsCache)("searchHighlighting: { text: text, textToSearch: $parent._textToSearch }, attr: { title: text }, css: $parent._extendCssClass('type-text')",(function(e,t){return{searchHighlighting:function(){return{text:e.$data.text,textToSearch:e.$parent._textToSearch}},attr:function(){return{title:e.$data.text}},css:function(){return e.$parent._extendCssClass("type-text")}}})),(0,it.addToBindingsCache)("text: text, attr: { title: text }, css: $parent._extendCssClass('type-text')",(function(e,t){return{text:function(){return e.$data.text},attr:function(){return{title:e.$data.text}},css:function(){return e.$parent._extendCssClass("type-text")}}})),(0,it.addToBindingsCache)("foreach: typeItems",(function(e,t){return{foreach:function(){return e.$data.typeItems}}})),(0,it.addToBindingsCache)("template: 'dxrd-page-choose-type-item'",(function(e,t){return{template:function(){return"dxrd-page-choose-type-item"}}})),(0,it.addToBindingsCache)("if: !!$parent._textToSearch",(function(e,t){return{if:function(){return!!e.$parent._textToSearch}}})),(0,it.addToBindingsCache)("ifnot: !!$parent._textToSearch",(function(e,t){return{ifnot:function(){return!!e.$parent._textToSearch}}})),(0,it.addToBindingsCache)("treeListSearchPanel: {controllers: ko.unwrap(_fieldListModel).treeListController}",(function(e,t){return{treeListSearchPanel:function(){return{controllers:e.ko.unwrap(e.$data._fieldListModel).treeListController}}}})),(0,it.addToBindingsCache)("dxScrollView: { showScrollbar: 'onHover', height: _scrollViewHeight }",(function(e,t){return{dxScrollView:function(){return{showScrollbar:"onHover",height:e.$data._scrollViewHeight}}}})),(0,it.addToBindingsCache)("treelist: _fieldListModel",(function(e,t){return{treelist:function(){return e.$data._fieldListModel}}})),(0,it.addToBindingsCache)("dxLoadPanel: _loadPanelViewModel($element)",(function(e,t){return{dxLoadPanel:function(){return e.$data._loadPanelViewModel(t)}}})),(0,it.addToBindingsCache)("dxButtonWithTemplate: { onClick: function() { clickAction($parent); }, icon: $data.imageTemplateName, iconClass: $data.imageClassName , disabled: $data.disabled && $data.disabled() }, attr: { title: text }",(function(e,t){return{dxButtonWithTemplate:function(){return{onClick:function(){e.$data.clickAction(e.$parent)},icon:e.$data.imageTemplateName,iconClass:e.$data.imageClassName,disabled:e.$data.disabled&&e.$data.disabled()}},attr:function(){return{title:e.$data.text}}}})),(0,it.addToBindingsCache)("template: { name: 'dx-popover-list-items', data: ko.utils.extend($parent.data, { className: 'dxrd-federation-addqueries-popover', position: $root.rtl ? 'right' : 'left', popupContainer: $root.getPopupContainer, width: 150 }) }",(function(e,t){return{template:function(){return{name:"dx-popover-list-items",data:e.ko.utils.extend(e.$parent.data,{className:"dxrd-federation-addqueries-popover",position:e.$root.rtl?"right":"left",popupContainer:e.$root.getPopupContainer,width:150})}}}})),(0,it.addToBindingsCache)("resizable: gridResizeHelper.resizable($root.resizeHandler, 's')",(function(e,t){return{resizable:function(){return e.$data.gridResizeHelper.resizable(e.$root.resizeHandler,"s")}}})),(0,it.addToBindingsCache)("dxQueryBuilderSurface: queryBuilderSurfaceCreator",(function(e,t){return{dxQueryBuilderSurface:function(){return e.$data.queryBuilderSurfaceCreator}}})),(0,it.addToBindingsCache)("dxdAccordion: { collapsed: joinResultCollapsed }",(function(e,t){return{dxdAccordion:function(){return{collapsed:e.$data.joinResultCollapsed}}}})),(0,it.addToBindingsCache)("template: 'dxrd-svg-collapsed', css: { 'dx-image-expanded': !joinResultCollapsed() }",(function(e,t){return{template:function(){return"dxrd-svg-collapsed"},css:function(){return{"dx-image-expanded":!e.$data.joinResultCollapsed()}}}})),(0,it.addToBindingsCache)("text: $root.getLocalization('Join result', 'DataAccessUIStringId.FederationDataSourceQueryBuilder_SelectResult')",(function(e,t){return{text:function(){return e.$root.getLocalization("Join result","DataAccessUIStringId.FederationDataSourceQueryBuilder_SelectResult")}}})),(0,it.addToBindingsCache)("style: { height: resultGridHeight }",(function(e,t){return{style:function(){return{height:e.$data.resultGridHeight}}}})),(0,it.addToBindingsCache)("dxButton: { icon: 'add', disabled: addRowDisabled, onClick:addRow }",(function(e,t){return{dxButton:function(){return{icon:"add",disabled:e.$data.addRowDisabled,onClick:e.$data.addRow}}}})),(0,it.addToBindingsCache)("dxDataGrid: columnsGrid",(function(e,t){return{dxDataGrid:function(){return e.$data.columnsGrid}}})),(0,it.addToBindingsCache)("text: cellInfo.column",(function(e,t){return{text:function(){return e.$data.cellInfo.column}}})),(0,it.addToBindingsCache)("dxDropDownBox: { dataSource: $data.allColumns, dropDownOptions: { container: $root.getPopupContainer($element), width:  $root.calculateWithZoomFactor(357)  }, popupPosition: { boundary: $root.getPopupContainer($element) }, value: cellInfo.key, valueExpr: 'key', displayExpr: 'column' }, visible: !$data.isExpression()",(function(e,t){return{dxDropDownBox:function(){return{dataSource:e.$data.allColumns,dropDownOptions:{container:e.$root.getPopupContainer(t),width:e.$root.calculateWithZoomFactor(357)},popupPosition:{boundary:e.$root.getPopupContainer(t)},value:e.$data.cellInfo.key,valueExpr:"key",displayExpr:"column"}},visible:function(){return!e.$data.isExpression()}}})),(0,it.addToBindingsCache)("dxDataGrid: { dataSource: $data.allColumns, keyExpr: 'key', remoteOperations: true, height: $root.calculateWithZoomFactor(250), columns: [{ dataField: 'column', sortOrder: 'asc' } , { dataField: 'table'}], hoverStateEnabled: true, selection: { mode: 'single' }, selectedRowKeys: [cellInfo.key], focusedRowEnabled: true, focusedRowKey: cellInfo.key, onSelectionChanged: $data.changeColumn }",(function(e,t){return{dxDataGrid:function(){return{dataSource:e.$data.allColumns,keyExpr:"key",remoteOperations:!0,height:e.$root.calculateWithZoomFactor(250),columns:[{dataField:"column",sortOrder:"asc"},{dataField:"table"}],hoverStateEnabled:!0,selection:{mode:"single"},selectedRowKeys:[e.$data.cellInfo.key],focusedRowEnabled:!0,focusedRowKey:e.$data.cellInfo.key,onSelectionChanged:e.$data.changeColumn}}}})),(0,it.addToBindingsCache)("dxExpressionEditor: { options: expression, fieldListProvider: itemsProvider, displayNameProvider: $root.displayNameProvider && $root.displayNameProvider() }, visible: $data.isExpression",(function(e,t){return{dxExpressionEditor:function(){return{options:e.$data.expression,fieldListProvider:e.$data.itemsProvider,displayNameProvider:e.$root.displayNameProvider&&e.$root.displayNameProvider()}},visible:function(){return e.$data.isExpression}}})),(0,it.addToBindingsCache)("template: { name: 'dx-wizard-menu-box-editorswitch', data: $data }",(function(e,t){return{template:function(){return{name:"dx-wizard-menu-box-editorswitch",data:e.$data}}}})),(0,it.addToBindingsCache)("text: $data.transformGridTitle",(function(e,t){return{text:function(){return e.$data.transformGridTitle}}})),(0,it.addToBindingsCache)("dxDataGrid: transformGrid",(function(e,t){return{dxDataGrid:function(){return e.$data.transformGrid}}})),(0,it.addToBindingsCache)("dxdAccordion: { collapsed: transformResultCollapsed }",(function(e,t){return{dxdAccordion:function(){return{collapsed:e.$data.transformResultCollapsed}}}})),(0,it.addToBindingsCache)("template: 'dxrd-svg-collapsed', css: { 'dx-image-expanded': !transformResultCollapsed() }",(function(e,t){return{template:function(){return"dxrd-svg-collapsed"},css:function(){return{"dx-image-expanded":!e.$data.transformResultCollapsed()}}}})),(0,it.addToBindingsCache)("text: $data.transformResultGridTitle",(function(e,t){return{text:function(){return e.$data.transformResultGridTitle}}})),(0,it.addToBindingsCache)("treelist: resultFieldListModel",(function(e,t){return{treelist:function(){return e.$data.resultFieldListModel}}})),(0,it.addToBindingsCache)("dxCheckBox: transform",(function(e,t){return{dxCheckBox:function(){return e.$data.transform}}})),(0,it.addToBindingsCache)("dxPopup: { showTitle: true, title: title(), wrapperAttr: { class: cssClass }, visible: popupVisible, toolbarItems: buttonItems, showCloseButton: true, fullScreen: false, maxHeight: maxHeight, height: height, width: width, onShowing: function(e) { $data.loaded(true); }, rtlEnabled: rtl, onHiding: onHiding, container: getPopupContainer($element), position: { of: getPopupContainer($element)}, resizeEnabled: true, minHeight: 450, minWidth: 650 }",(function(e,t){return{dxPopup:function(){return{showTitle:!0,title:e.$data.title(),wrapperAttr:{class:e.$data.cssClass},visible:e.$data.popupVisible,toolbarItems:e.$data.buttonItems,showCloseButton:!0,fullScreen:!1,maxHeight:e.$data.maxHeight,height:e.$data.height,width:e.$data.width,onShowing:function(t){e.$data.loaded(!0)},rtlEnabled:e.$data.rtl,onHiding:e.$data.onHiding,container:e.$data.getPopupContainer(t),position:{of:e.$data.getPopupContainer(t)},resizeEnabled:!0,minHeight:450,minWidth:650}}}})),(0,it.addToBindingsCache)("resizable: resizeHelper.resizable($root.resizeHandler, 'e')",(function(e,t){return{resizable:function(){return e.$data.resizeHelper.resizable(e.$root.resizeHandler,"e")}}})),(0,it.addToBindingsCache)("treelist: fieldListModel",(function(e,t){return{treelist:function(){return e.$data.fieldListModel}}})),(0,it.addToBindingsCache)("dxDataGrid: sourcesGrid",(function(e,t){return{dxDataGrid:function(){return e.$data.sourcesGrid}}})),(0,it.addToBindingsCache)("dxDataGrid: aliasGrid",(function(e,t){return{dxDataGrid:function(){return e.$data.aliasGrid}}})),(0,it.addToBindingsCache)("if: loaded",(function(e,t){return{if:function(){return e.$data.loaded}}})),(0,it.addToBindingsCache)("template: { name: popupContentTemplate }",(function(e,t){return{template:function(){return{name:e.$data.popupContentTemplate}}}})),(0,it.addToBindingsCache)("css: { 'dx-rtl': $root.rtl, 'dx-ltr': !$root.rtl }",(function(e,t){return{css:function(){return{"dx-rtl":e.$root.rtl,"dx-ltr":!e.$root.rtl}}}})),(0,it.addToBindingsCache)("css: _extendCssClass",(function(e,t){return{css:function(){return e.$data._extendCssClass}}})),(0,it.addToBindingsCache)("visible: $data.navigationPanel().isVisible",(function(e,t){return{visible:function(){return e.$data.navigationPanel().isVisible}}})),(0,it.addToBindingsCache)("text: _description(), attr: {'title': _description() }",(function(e,t){return{text:function(){return e.$data._description()},attr:function(){return{title:e.$data._description()}}}})),(0,it.addToBindingsCache)("foreach: _steps",(function(e,t){return{foreach:function(){return e.$data._steps}}})),(0,it.addToBindingsCache)("visible: $data.visible",(function(e,t){return{visible:function(){return e.$data.visible}}})),(0,it.addToBindingsCache)("click: $data.clickAction, text: $data.text, attr: {'title': $data.text }, css: { 'dxrd-disabled': $data.disabled, 'dxd-back-secondary': $data.isActive() }",(function(e,t){return{click:function(){return e.$data.clickAction},text:function(){return e.$data.text},attr:function(){return{title:e.$data.text}},css:function(){return{"dxrd-disabled":e.$data.disabled,"dxd-back-secondary":e.$data.isActive()}}}})),(0,it.addToBindingsCache)("visible: $data.isActive",(function(e,t){return{visible:function(){return e.$data.isActive}}})),(0,it.addToBindingsCache)("css: { 'withoutPanel': !$data.navigationPanel().isVisible() }",(function(e,t){return{css:function(){return{withoutPanel:!e.$data.navigationPanel().isVisible()}}}})),(0,it.addToBindingsCache)("text: _pageDescription()",(function(e,t){return{text:function(){return e.$data._pageDescription()}}})),(0,it.addToBindingsCache)("template: { name: template, data: page }",(function(e,t){return{template:function(){return{name:e.$data.template,data:e.$data.page}}}})),(0,it.addToBindingsCache)("dxButton: cancelButton",(function(e,t){return{dxButton:function(){return e.$data.cancelButton}}})),(0,it.addToBindingsCache)("dxButton: finishButton",(function(e,t){return{dxButton:function(){return e.$data.finishButton}}})),(0,it.addToBindingsCache)("dxButton: nextButton",(function(e,t){return{dxButton:function(){return e.$data.nextButton}}})),(0,it.addToBindingsCache)("dxButton: previousButton",(function(e,t){return{dxButton:function(){return e.$data.previousButton}}})),(0,it.addToBindingsCache)("foreach: _sections, css: _className, styleunit: { top: _parentMarginOffset, left: _parentMarginOffset, right: _parentMarginOffset, bottom: _parentMarginOffset }",(function(e,t){return{foreach:function(){return e.$data._sections},css:function(){return e.$data._className},styleunit:function(){return{top:e.$data._parentMarginOffset,left:e.$data._parentMarginOffset,right:e.$data._parentMarginOffset,bottom:e.$data._parentMarginOffset}}}})),(0,it.addToBindingsCache)("css: { 'dxrd-disabled': !$data.page() }, style: $parent._pageCss[$data.pageId]",(function(e,t){return{css:function(){return{"dxrd-disabled":!e.$data.page()}},style:function(){return e.$parent._pageCss[e.$data.pageId]}}})),(0,it.addToBindingsCache)("visible: $parent._showPageDescription($data), text: $parent._getPageDescription($index(), $data), attr: { title: $parent._getPageDescription($index(), $data) }",(function(e,t){return{visible:function(){return e.$parent._showPageDescription(e.$data)},text:function(){return e.$parent._getPageDescription(e.$index(),e.$data)},attr:function(){return{title:e.$parent._getPageDescription(e.$index(),e.$data)}}}})),(0,it.addToBindingsCache)("template: { name: $parent.metadata.template, data: page }, dxScrollView: { showScrollbar: 'onHover'}",(function(e,t){return{template:function(){return{name:e.$parent.metadata.template,data:e.$data.page}},dxScrollView:function(){return{showScrollbar:"onHover"}}}})),(0,it.addToBindingsCache)("text: metadata.disabledText",(function(e,t){return{text:function(){return e.$data.metadata.disabledText}}})),(0,it.addToBindingsCache)("if: $data && $data.isVisible()",(function(e,t){return{if:function(){return e.$data&&e.$data.isVisible()}}})),(0,it.addToBindingsCache)("with: navigationPanel",(function(e,t){return{with:function(){return e.$data.navigationPanel}}})),(0,it.addToBindingsCache)("with: _currentPage",(function(e,t){return{with:function(){return e.$data._currentPage}}})),(0,it.addToBindingsCache)("if: $data._sections.length > 0",(function(e,t){return{if:function(){return e.$data._sections.length>0}}})),(0,it.addToBindingsCache)("if: $data.page() !== null",(function(e,t){return{if:function(){return null!==e.$data.page()}}})),(0,it.addToBindingsCache)("with: page",(function(e,t){return{with:function(){return e.$data.page}}})),(0,it.addToBindingsCache)("if: $data.page() === null",(function(e,t){return{if:function(){return null===e.$data.page()}}})),(0,it.addToBindingsCache)("text: _rootElementTitle",(function(e,t){return{text:function(){return e.$data._rootElementTitle}}})),(0,it.addToBindingsCache)("dxSelectBox: { dataSource: _rootElementList, value: _selectedRootElement, displayExpr: 'fullPath', displayCustomValue: true, useItemTextAsTitle: true, dropDownOptions: { container: $root.getPopupContainer($element) } }",(function(e,t){return{dxSelectBox:function(){return{dataSource:e.$data._rootElementList,value:e.$data._selectedRootElement,displayExpr:"fullPath",displayCustomValue:!0,useItemTextAsTitle:!0,dropDownOptions:{container:e.$root.getPopupContainer(t)}}}}})),(0,it.addToBindingsCache)("dxScrollView: { showScrollbar: 'onHover', useNative: false, scrollByThumb: true }, dxValidationGroup: $data._validationGroup || {}",(function(e,t){return{dxScrollView:function(){return{showScrollbar:"onHover",useNative:!1,scrollByThumb:!0}},dxValidationGroup:function(){return e.$data._validationGroup||{}}}})),(0,it.addToBindingsCache)("text: _jsonConnectionTitle, attr: { 'title': _jsonConnectionTitle }",(function(e,t){return{text:function(){return e.$data._jsonConnectionTitle},attr:function(){return{title:e.$data._jsonConnectionTitle}}}})),(0,it.addToBindingsCache)("dxTextBox: { value: _connectionName }, dxValidator: { validationRules: $data._connectionNameValidationRules || [] }",(function(e,t){return{dxTextBox:function(){return{value:e.$data._connectionName}},dxValidator:function(){return{validationRules:e.$data._connectionNameValidationRules||[]}}}})),(0,it.addToBindingsCache)("text: _jsonSourceTitle, attr: { 'title': _jsonSourceTitle }",(function(e,t){return{text:function(){return e.$data._jsonSourceTitle},attr:function(){return{title:e.$data._jsonSourceTitle}}}})),(0,it.addToBindingsCache)("dxLocalizedSelectBox: { dataSource: _sources, value: _selectedSource, valueExpr:'value', displayExpr: 'displayValue', displayCustomValue: true, dropDownOptions: { container: $root.getPopupContainer($element) } }",(function(e,t){return{dxLocalizedSelectBox:function(){return{dataSource:e.$data._sources,value:e.$data._selectedSource,valueExpr:"value",displayExpr:"displayValue",displayCustomValue:!0,dropDownOptions:{container:e.$root.getPopupContainer(t)}}}}})),(0,it.addToBindingsCache)("dxValidationGroup: $data.validationGroup || {}",(function(e,t){return{dxValidationGroup:function(){return e.$data.validationGroup||{}}}})),(0,it.addToBindingsCache)("css: $data.cssClass",(function(e,t){return{css:function(){return e.$data.cssClass}}})),(0,it.addToBindingsCache)("dxValidationSummary: $data.validationSummary || {}, visible: $data.validationSummary && !isValid()",(function(e,t){return{dxValidationSummary:function(){return e.$data.validationSummary||{}},visible:function(){return e.$data.validationSummary&&!e.$data.isValid()}}})),(0,it.addToBindingsCache)("dxValidationSummary: $data._validationSummary || {}, visible: $data._validationSummary && _selectedSource().validationSummary && !canNext()",(function(e,t){return{dxValidationSummary:function(){return e.$data._validationSummary||{}},visible:function(){return e.$data._validationSummary&&e.$data._selectedSource().validationSummary&&!e.$data.canNext()}}})),(0,it.addToBindingsCache)("with: _selectedSource",(function(e,t){return{with:function(){return e.$data._selectedSource}}})),(0,it.addToBindingsCache)("template: { name: 'dx-propertieseditor', data: grid }",(function(e,t){return{template:function(){return{name:"dx-propertieseditor",data:e.$data.grid}}}})),(0,it.addToBindingsCache)("dxRadioGroup: { value: selectedDataSourceOperation, items: dataSourceOperations, layout: 'vertical' }",(function(e,t){return{dxRadioGroup:function(){return{value:e.$data.selectedDataSourceOperation,items:e.$data.dataSourceOperations,layout:"vertical"}}}})),(0,it.addToBindingsCache)("text: text, attr: { 'title': text }",(function(e,t){return{text:function(){return e.$data.text},attr:function(){return{title:e.$data.text}}}})),(0,it.addToBindingsCache)("dxList: { dataSource: availableDataSources, selectedItems: selectedDataSource, focusStateEnabled:false, editEnabled: true, height: dataSourcesListHeight, editConfig: { selectionEnabled: false }, selectionMode: 'single', activeStateEnabled: false, disabled: createNewDataSource, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true }",(function(e,t){return{dxList:function(){return{dataSource:e.$data.availableDataSources,selectedItems:e.$data.selectedDataSource,focusStateEnabled:!1,editEnabled:!0,height:e.$data.dataSourcesListHeight,editConfig:{selectionEnabled:!1},selectionMode:"single",activeStateEnabled:!1,disabled:e.$data.createNewDataSource,noDataText:e.$root.dx._static.noDataText(),encodeNoDataText:!0}}}})),(0,it.addToBindingsCache)("if: isDataSourceCreationAvailable",(function(e,t){return{if:function(){return e.$data.isDataSourceCreationAvailable}}})),(0,it.addToBindingsCache)("dxTemplate : { name: 'item' }",(function(){return{dxTemplate:{name:"item"}}})),(0,it.addToBindingsCache)("dxRadioGroup: { value: selectedOperation, items: operations, layout: 'vertical' }",(function(e,t){return{dxRadioGroup:function(){return{value:e.$data.selectedOperation,items:e.$data.operations,layout:"vertical"}}}})),(0,it.addToBindingsCache)("styleunit: { top: $data.canCreateNew() ? 65 : 0 }",(function(e,t){return{styleunit:function(){return{top:e.$data.canCreateNew()?65:0}}}})),(0,it.addToBindingsCache)("dxList: { dataSource: items, onSelectionChanged: function(e) { selectedItems(e.addedItems) }, selectedItems: selectedItems.peek(), hoverStateEnabled:false, editEnabled: true, height: '100%', editConfig: { selectionEnabled: false }, searchExpr: 'name', searchEnabled: true, selectionMode: 'single', activeStateEnabled: false, disabled: _createNew, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true, searchEditorOptions: { placeholder: $root.dx._static.searchPlaceholder() } }",(function(e,t){return{dxList:function(){return{dataSource:e.$data.items,onSelectionChanged:function(t){e.$data.selectedItems(t.addedItems)},selectedItems:e.$data.selectedItems.peek(),hoverStateEnabled:!1,editEnabled:!0,height:"100%",editConfig:{selectionEnabled:!1},searchExpr:"name",searchEnabled:!0,selectionMode:"single",activeStateEnabled:!1,disabled:e.$data._createNew,noDataText:e.$root.dx._static.noDataText(),encodeNoDataText:!0,searchEditorOptions:{placeholder:e.$root.dx._static.searchPlaceholder()}}}}})),(0,it.addToBindingsCache)("event: { dblclick: function(e){ $parent.onDblClick() } }",(function(e,t){return{event:function(){return{dblclick:function(t){e.$parent.onDblClick()}}}}})),(0,it.addToBindingsCache)("text: $parent._displayExpr($data)",(function(e,t){return{text:function(){return e.$parent._displayExpr(e.$data)}}})),(0,it.addToBindingsCache)("template: { name: 'dxrd-page-selectitems-radio-group', data: $data }",(function(e,t){return{template:function(){return{name:"dxrd-page-selectitems-radio-group",data:e.$data}}}})),(0,it.addToBindingsCache)("template: { name: 'dxrd-page-selectitems-list', data: $data }",(function(e,t){return{template:function(){return{name:"dxrd-page-selectitems-list",data:e.$data}}}})),(0,it.addToBindingsCache)("if: canCreateNew",(function(e,t){return{if:function(){return e.$data.canCreateNew}}})),(0,it.addToBindingsCache)("if: !_createNew()",(function(e,t){return{if:function(){return!e.$data._createNew()}}})),(0,it.addToBindingsCache)("if: _createNew",(function(e,t){return{if:function(){return e.$data._createNew}}})),(0,it.addToBindingsCache)("template: { name: 'dxrd-page-jsonsource', data: _specifySourceData }",(function(e,t){return{template:function(){return{name:"dxrd-page-jsonsource",data:e.$data._specifySourceData}}}})),(0,it.addToBindingsCache)("dxLocalizedSelectBox: { dataSource: _parameterTypes, value: itemType, valueExpr:'value', displayExpr: 'displayValue', displayCustomValue: true, dropDownOptions: { container: $root.getPopupContainer($element) } }",(function(e,t){return{dxLocalizedSelectBox:function(){return{dataSource:e.$data._parameterTypes,value:e.$data.itemType,valueExpr:"value",displayExpr:"displayValue",displayCustomValue:!0,dropDownOptions:{container:e.$root.getPopupContainer(t)}}}}})),(0,it.addToBindingsCache)("dxTextBox: { value: name, placeholder: $data.namePlaceholder() }, dxValidator: { validationRules: nameValidationRules || [] }",(function(e,t){return{dxTextBox:function(){return{value:e.$data.name,placeholder:e.$data.namePlaceholder()}},dxValidator:function(){return{validationRules:e.$data.nameValidationRules||[]}}}})),(0,it.addToBindingsCache)("dxTextBox: { value: _editingValue, placeholder: $data.valuePlaceholder() }, visible: !$data.isExpression()",(function(e,t){return{dxTextBox:function(){return{value:e.$data._editingValue,placeholder:e.$data.valuePlaceholder()}},visible:function(){return!e.$data.isExpression()}}})),(0,it.addToBindingsCache)("dxExpressionEditor: { options: _expression, fieldListProvider: itemsProvider, displayNameProvider: $root.displayNameProvider && $root.displayNameProvider() }, visible: $data.isExpression",(function(e,t){return{dxExpressionEditor:function(){return{options:e.$data._expression,fieldListProvider:e.$data.itemsProvider,displayNameProvider:e.$root.displayNameProvider&&e.$root.displayNameProvider()}},visible:function(){return e.$data.isExpression}}})),(0,it.addToBindingsCache)("dxFileImagePicker: { value: value, placeholderId: 'File', accept:'.json,.txt' }",(function(e,t){return{dxFileImagePicker:function(){return{value:e.$data.value,placeholderId:"File",accept:".json,.txt"}}}})),(0,it.addToBindingsCache)("dxTextArea: { value: value, spellcheck: false, isValid: isValid }, dxValidator: $data.validator || { validationRules: jsonStringValidationRules || [] }",(function(e,t){return{dxTextArea:function(){return{value:e.$data.value,spellcheck:!1,isValid:e.$data.isValid}},dxValidator:function(){return e.$data.validator||{validationRules:e.$data.jsonStringValidationRules||[]}}}})),(0,it.addToBindingsCache)("dxAceEditor: { value: value, editorContainer: editorContainer, options: aceOptions, additionalOptions: additionalOptions }, css: { 'dx-invalid' : !value() || !isValid() }",(function(e,t){return{dxAceEditor:function(){return{value:e.$data.value,editorContainer:e.$data.editorContainer,options:e.$data.aceOptions,additionalOptions:e.$data.additionalOptions}},css:function(){return{"dx-invalid":!e.$data.value()||!e.$data.isValid()}}}})),(0,it.addToBindingsCache)("dxButtonWithTemplate: { onClick: uploadFile, hint: $data.getUploadTitle(), icon: 'dxrd-svg-wizard-Download' }",(function(e,t){return{dxButtonWithTemplate:function(){return{onClick:e.$data.uploadFile,hint:e.$data.getUploadTitle(),icon:"dxrd-svg-wizard-Download"}}}})),(0,it.addToBindingsCache)("styleunit: padding, css: { 'dx-treelist-item-selected dxd-state-selected dxd-back-secondary': isSelected }",(function(e,t){return{styleunit:function(){return e.$data.padding},css:function(){return{"dx-treelist-item-selected dxd-state-selected dxd-back-secondary":e.$data.isSelected}}}})),(0,it.addToBindingsCache)("css: nodeImageClass",(function(e,t){return{css:function(){return e.$data.nodeImageClass}}})),(0,it.addToBindingsCache)("dxButtonWithTemplate: { onClick: function() { clickAction($parent); }, icon: imageTemplateName, iconClass: imageClassName, disabled: ko.unwrap(disabled) }, attr: { title: text }",(function(e,t){return{dxButtonWithTemplate:function(){return{onClick:function(){e.$data.clickAction(e.$parent)},icon:e.$data.imageTemplateName,iconClass:e.$data.imageClassName,disabled:e.ko.unwrap(e.$data.disabled)}},attr:function(){return{title:e.$data.text}}}})),(0,it.addToBindingsCache)("dxCheckBox: { value: data.checked }, click: function(treeNode, e) { treeNode.data.toggleChecked(); e.stopPropagation(); return true; }",(function(e,t){return{dxCheckBox:function(){return{value:e.$data.data.checked}},click:function(){return function(e,t){return e.data.toggleChecked(),t.stopPropagation(),!0}}}})),(0,it.addToBindingsCache)("dxTextBox: { value: queryName, onValueChanged: queryNameHasChanged }",(function(e,t){return{dxTextBox:function(){return{value:e.$data.queryName,onValueChanged:e.$data.queryNameHasChanged}}}})),(0,it.addToBindingsCache)("ifnot: $data.disableCustomSql",(function(e,t){return{ifnot:function(){return e.$data.disableCustomSql}}})),(0,it.addToBindingsCache)("template: { name: 'dxqb-popup-selectStatment', data: _popupSelectStatement }",(function(e,t){return{template:function(){return{name:"dxqb-popup-selectStatment",data:e.$data._popupSelectStatement}}}})),(0,it.addToBindingsCache)("template: { name: 'dxrd-querybuilder-popup', data: _popupQueryBuilder }",(function(e,t){return{template:function(){return{name:"dxrd-querybuilder-popup",data:e.$data._popupQueryBuilder}}}})),(0,it.addToBindingsCache)("if: !!$data._fieldListModel()",(function(e,t){return{if:function(){return!!e.$data._fieldListModel()}}})),(0,it.addToBindingsCache)("if: $data._relationsEditor()",(function(e,t){return{if:function(){return e.$data._relationsEditor()}}})),(0,it.addToBindingsCache)("template: { name: 'dxrd-masterDetail-editor-complete-wizard', data: $data._relationsEditor }",(function(e,t){return{template:function(){return{name:"dxrd-masterDetail-editor-complete-wizard",data:e.$data._relationsEditor}}}})),(0,it.addToBindingsCache)("dxPopupWithAutoHeight: { height: '235px', focusStateEnabled: false, position: $root.rtl ? { my: 'right top', at: 'right bottom', of: popupService.target, collision: 'flipfit' } : { my: 'left top', at: 'left bottom', of: popupService.target, collision: 'flipfit' }, wrapperAttr: { class: 'dx-selectbox-popup-wrapper dx-dropdownlist-popup-wrapper dx-filtereditor-criteriaoperator-popup dx-dropdowneditor-overlay' }, container: $root.getPopupContainer($element), target: popupService.target, showTitle: false, showCloseButton: false, animation: {}, hideOnOutsideClick: true, shading: false, minWidth:'170px', maxWidth:'500px', width: 'auto', visible: popupService.visible }",(function(e,t){return{dxPopupWithAutoHeight:function(){return{height:"235px",focusStateEnabled:!1,position:e.$root.rtl?{my:"right top",at:"right bottom",of:e.$data.popupService.target,collision:"flipfit"}:{my:"left top",at:"left bottom",of:e.$data.popupService.target,collision:"flipfit"},wrapperAttr:{class:"dx-selectbox-popup-wrapper dx-dropdownlist-popup-wrapper dx-filtereditor-criteriaoperator-popup dx-dropdowneditor-overlay"},container:e.$root.getPopupContainer(t),target:e.$data.popupService.target,showTitle:!1,showCloseButton:!1,animation:{},hideOnOutsideClick:!0,shading:!1,minWidth:"170px",maxWidth:"500px",width:"auto",visible:e.$data.popupService.visible}}}})),(0,it.addToBindingsCache)("dxPopup: { animation: { show: { type: 'fade', from: 0, to: 1, duration: 700 }, hide: { type: 'fade', from: 1, to: 0, duration: 700 } }, wrapperAttr: { class: 'dxqb-preview' }, visible: isVisible, title: title(), showTitle: true, resizeEnabled: true, shading: true, shadingColor: 'transparent', fullScreen: false, width: 800, height: 544, container: closest($element, '.dxrd-wizard'), position: { of: closest($element, '.dx-designer-viewport') }, onHidden: function() { $data.data(null) }, focusStateEnabled: false }",(function(e,t){return{dxPopup:function(){return{animation:{show:{type:"fade",from:0,to:1,duration:700},hide:{type:"fade",from:1,to:0,duration:700}},wrapperAttr:{class:"dxqb-preview"},visible:e.$data.isVisible,title:e.$data.title(),showTitle:!0,resizeEnabled:!0,shading:!0,shadingColor:"transparent",fullScreen:!1,width:800,height:544,container:e.$data.closest(t,".dxrd-wizard"),position:{of:e.$data.closest(t,".dx-designer-viewport")},onHidden:function(){e.$data.data(null)},focusStateEnabled:!1}}}})),(0,it.addToBindingsCache)("dxTextArea: { value: data, valueChangeEvent: 'keyup', disabled: false }",(function(e,t){return{dxTextArea:function(){return{value:e.$data.data,valueChangeEvent:"keyup",disabled:!1}}}})),(0,it.addToBindingsCache)("dxAceEditor: { value: data, additionalOptions: additionalOptions, options: aceOptions }",(function(e,t){return{dxAceEditor:function(){return{value:e.$data.data,additionalOptions:e.$data.additionalOptions,options:e.$data.aceOptions}}}})),(0,it.addToBindingsCache)("dxButton: { text: okButtonText(), onClick: okButtonHandler }",(function(e,t){return{dxButton:function(){return{text:e.$data.okButtonText(),onClick:e.$data.okButtonHandler}}}})),(0,it.addToBindingsCache)("event: events, attr: { class: 'dx-treeview-item dx-treelist-item dx-fontsize-reestablished dxd-list-item-back-color ' + (hasItems ? 'dx-treelist-list-item-content ' : 'dx-treelist-field-item-content ') + (isSelected ? 'dx-treelist-item-selected dxd-state-selected dxd-back-secondary' : '') }, styleunit: padding, css: { 'dx-state-hover': isHovered }",(function(e,t){return{event:function(){return e.$data.events},attr:function(){return{class:"dx-treeview-item dx-treelist-item dx-fontsize-reestablished dxd-list-item-back-color "+(e.$data.hasItems?"dx-treelist-list-item-content ":"dx-treelist-field-item-content ")+(e.$data.isSelected?"dx-treelist-item-selected dxd-state-selected dxd-back-secondary":"")}},styleunit:function(){return e.$data.padding},css:function(){return{"dx-state-hover":e.$data.isHovered}}}})),(0,it.addToBindingsCache)("css: imageClassName, template: { name: imageTemplateName, if: !!imageTemplateName  }, attr: { title: text }",(function(e,t){return{css:function(){return e.$data.imageClassName},template:function(){return{name:e.$data.imageTemplateName,if:!!e.$data.imageTemplateName}},attr:function(){return{title:e.$data.text}}}})),(0,it.addToBindingsCache)("css: nodeImageClass, visible: hasItems, template: 'dxrd-svg-collapsed', click: toggleCollapsed",(function(e,t){return{css:function(){return e.$data.nodeImageClass},visible:function(){return e.$data.hasItems},template:function(){return"dxrd-svg-collapsed"},click:function(){return e.$data.toggleCollapsed}}})),(0,it.addToBindingsCache)("click: toggleSelected,  draggable: isDraggable ? dragDropHandler : null, css: { 'dxrd-disabled': data.disabled }",(function(e,t){return{click:function(){return e.$data.toggleSelected},draggable:function(){return e.$data.isDraggable?e.$data.dragDropHandler:null},css:function(){return{"dxrd-disabled":e.$data.data.disabled}}}})),(0,it.addToBindingsCache)("dxCheckBox: { value: data.checked, disabled: data.selectionDisabled }, click: function(treeNode, e) { treeNode.data.toggleChecked(); e.stopPropagation(); return true; }",(function(e,t){return{dxCheckBox:function(){return{value:e.$data.data.checked,disabled:e.$data.data.selectionDisabled}},click:function(){return function(e,t){return e.data.toggleChecked(),t.stopPropagation(),!0}}}})),(0,it.addToBindingsCache)("dxButtonWithTemplate: { onClick: function() { clickAction($parent); }, icon: imageTemplateName, iconClass: imageClassName , disabled: ko.unwrap(disabled) }, attr: { title: text }",(function(e,t){return{dxButtonWithTemplate:function(){return{onClick:function(){e.$data.clickAction(e.$parent)},icon:e.$data.imageTemplateName,iconClass:e.$data.imageClassName,disabled:e.ko.unwrap(e.$data.disabled)}},attr:function(){return{title:e.$data.text}}}})),(0,it.addToBindingsCache)("dxPropertyGrid: { target: $parent.data.dataSourceParameter, level: $parent.data.editor.level + 1 }",(function(e,t){return{dxPropertyGrid:function(){return{target:e.$parent.data.dataSourceParameter,level:e.$parent.data.editor.level+1}}}})),(0,it.addToBindingsCache)('template: "dx-treelist-accordion-item-with-checkbox"',(function(e,t){return{template:function(){return"dx-treelist-accordion-item-with-checkbox"}}})),(0,it.addToBindingsCache)('template: "dx-treelist-header-item-with-checkbox"',(function(e,t){return{template:function(){return"dx-treelist-header-item-with-checkbox"}}})),(0,it.addToBindingsCache)("if: showIcon",(function(e,t){return{if:function(){return e.$data.showIcon}}})),(0,it.addToBindingsCache)("ifnot: showIcon",(function(e,t){return{ifnot:function(){return e.$data.showIcon}}})),(0,it.addToBindingsCache)("template: { name: 'dx-popover-list-items', data: ko.utils.extend($parent.data, { position: $root.rtl ? 'left' : 'right', popupContainer: $root.getPopupContainer }) }",(function(e,t){return{template:function(){return{name:"dx-popover-list-items",data:e.ko.utils.extend(e.$parent.data,{position:e.$root.rtl?"left":"right",popupContainer:e.$root.getPopupContainer})}}}})),(0,it.addToBindingsCache)("dxList: { dataSource: _chooseObjectDataMember.dataMembers, selectedItems: _chooseObjectDataMember.selectedDataMembers, selectionMode: 'single', activeStateEnabled: false, focusStateEnabled: false, searchExpr: 'displayName', searchEnabled: true, noDataText: $dx._static.noDataText(), encodeNoDataText: true, searchEditorOptions: { placeholder: $root.dx._static.searchPlaceholder() } }",(function(e,t){return{dxList:function(){return{dataSource:e.$data._chooseObjectDataMember.dataMembers,selectedItems:e.$data._chooseObjectDataMember.selectedDataMembers,selectionMode:"single",activeStateEnabled:!1,focusStateEnabled:!1,searchExpr:"displayName",searchEnabled:!0,noDataText:e.$data.$dx._static.noDataText(),encodeNoDataText:!0,searchEditorOptions:{placeholder:e.$root.dx._static.searchPlaceholder()}}}}})),(0,it.addToBindingsCache)("treeListSearchPanel: {controllers: _chooseObjectType.availableTypesTreelistModel.treeListController}",(function(e,t){return{treeListSearchPanel:function(){return{controllers:e.$data._chooseObjectType.availableTypesTreelistModel.treeListController}}}})),(0,it.addToBindingsCache)("dxScrollView: { showScrollbar: 'onHover', height: _chooseObjectType._scrollViewHeight}",(function(e,t){return{dxScrollView:function(){return{showScrollbar:"onHover",height:e.$data._chooseObjectType._scrollViewHeight}}}})),(0,it.addToBindingsCache)("treelist: _chooseObjectType.availableTypesTreelistModel",(function(e,t){return{treelist:function(){return e.$data._chooseObjectType.availableTypesTreelistModel}}})),(0,it.addToBindingsCache)("text: $root.dx._static.noDataText()",(function(e,t){return{text:function(){return e.$root.dx._static.noDataText()}}})),(0,it.addToBindingsCache)("if: _chooseObjectType.types && !_chooseObjectType.types().length",(function(e,t){return{if:function(){return e.$data._chooseObjectType.types&&!e.$data._chooseObjectType.types().length}}})),(0,it.addToBindingsCache)("style: position, css: { 'dxrd-disabled': $data.disabled() }",(function(e,t){return{style:function(){return e.$data.position},css:function(){return{"dxrd-disabled":e.$data.disabled()}}}})),(0,it.addToBindingsCache)("text: $parent.showDescription($index(), description), attr: { title: $parent.showDescription($index(), description) }",(function(e,t){return{text:function(){return e.$parent.showDescription(e.$index(),e.$data.description)},attr:function(){return{title:e.$parent.showDescription(e.$index(),e.$data.description)}}}})),(0,it.addToBindingsCache)("text: disabledText",(function(e,t){return{text:function(){return e.$data.disabledText}}})),(0,it.addToBindingsCache)("treeListSearchPanel: {controllers: availableTypesTreelistModel.treeListController}",(function(e,t){return{treeListSearchPanel:function(){return{controllers:e.$data.availableTypesTreelistModel.treeListController}}}})),(0,it.addToBindingsCache)("dxScrollView: { showScrollbar: 'onHover', height: _scrollViewHeight}",(function(e,t){return{dxScrollView:function(){return{showScrollbar:"onHover",height:e.$data._scrollViewHeight}}}})),(0,it.addToBindingsCache)("treelist: availableTypesTreelistModel",(function(e,t){return{treelist:function(){return e.$data.availableTypesTreelistModel}}})),(0,it.addToBindingsCache)("dxList: { dataSource: dataMembers, selectedItems: selectedDataMembers, selectionMode: 'single', activeStateEnabled: false, focusStateEnabled: false, searchExpr: 'displayName', searchEnabled: true, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true, searchEditorOptions: { placeholder: $root.dx._static.searchPlaceholder() } }",(function(e,t){return{dxList:function(){return{dataSource:e.$data.dataMembers,selectedItems:e.$data.selectedDataMembers,selectionMode:"single",activeStateEnabled:!1,focusStateEnabled:!1,searchExpr:"displayName",searchEnabled:!0,noDataText:e.$root.dx._static.noDataText(),encodeNoDataText:!0,searchEditorOptions:{placeholder:e.$root.dx._static.searchPlaceholder()}}}}})),(0,it.addToBindingsCache)("text: $data.displayName, attr: { 'title': $data.displayName}",(function(e,t){return{text:function(){return e.$data.displayName},attr:function(){return{title:e.$data.displayName}}}})),(0,it.addToBindingsCache)("dxExpressionEditor: getOptions({ options: value, fieldListProvider: value().itemsProvider, displayNameProvider: $root.displayNameProvider && $root.displayNameProvider() })",(function(e,t){return{dxExpressionEditor:function(){return e.$data.getOptions({options:e.$data.value,fieldListProvider:e.$data.value().itemsProvider,displayNameProvider:e.$root.displayNameProvider&&e.$root.displayNameProvider()})}}})),(0,it.addToBindingsCache)("dxScrollView: { showScrollbar: 'onHover'}",(function(e,t){return{dxScrollView:function(){return{showScrollbar:"onHover"}}}})),(0,it.addToBindingsCache)("foreach: _sections",(function(e,t){return{foreach:function(){return e.$data._sections}}})),(0,it.addToBindingsCache)("if: !$data.disabled()",(function(e,t){return{if:function(){return!e.$data.disabled()}}})),(0,it.addToBindingsCache)("if: $data.disabled()",(function(e,t){return{if:function(){return e.$data.disabled()}}})),(0,it.addToBindingsCache)("if: !!$data",(function(e,t){return{if:function(){return!!e.$data}}})),(0,it.addToBindingsCache)("foreach: _grids",(function(e,t){return{foreach:function(){return e.$data._grids}}})),(0,it.addToBindingsCache)("template: { name: 'dx-objectdatasource-parameters-grid', data: _ctorParametersObject }",(function(e,t){return{template:function(){return{name:"dx-objectdatasource-parameters-grid",data:e.$data._ctorParametersObject}}}})),(0,it.addToBindingsCache)("template: { name: 'dx-objectdatasource-parameters-grid', data: _dataMemberParametersObject }",(function(e,t){return{template:function(){return{name:"dx-objectdatasource-parameters-grid",data:e.$data._dataMemberParametersObject}}}})),(0,it.addToBindingsCache)("template: { name: 'dx-objectdatasource-parameters-section', data: _chooseObjectParameters }",(function(e,t){return{template:function(){return{name:"dx-objectdatasource-parameters-section",data:e.$data._chooseObjectParameters}}}})),(0,it.addToBindingsCache)("dxCollectionEditor: parametersEditorOptions",(function(e,t){return{dxCollectionEditor:function(){return e.$data.parametersEditorOptions}}})),(0,it.addToBindingsCache)("dxdAccordion: $data",(function(e,t){return{dxdAccordion:function(){return e.$data}}})),(0,it.addToBindingsCache)("styleunit: { 'marginLeft' : padding }, css: { 'dxrd-group-header-collapsed': collapsed }",(function(e,t){return{styleunit:function(){return{marginLeft:e.$data.padding}},css:function(){return{"dxrd-group-header-collapsed":e.$data.collapsed}}}})),(0,it.addToBindingsCache)("text: value.name",(function(e,t){return{text:function(){return e.$data.value.name}}})),(0,it.addToBindingsCache)("dxScrollView: { showScrollbar: 'onHover', useNative: false, scrollByThumb: true  }",(function(e,t){return{dxScrollView:function(){return{showScrollbar:"onHover",useNative:!1,scrollByThumb:!0}}}})),(0,it.addToBindingsCache)("dxclick: select, css: { 'dxd-state-selected dxd-back-secondary' : selected }",(function(e,t){return{dxclick:function(){return e.$data.select},css:function(){return{"dxd-state-selected dxd-back-secondary":e.$data.selected}}}})),(0,it.addToBindingsCache)("visible: showButtons",(function(e,t){return{visible:function(){return e.$data.showButtons}}})),(0,it.addToBindingsCache)("dxButton: { onClick: buttons.add.action, text: buttons.add.text }, attr: { title: buttons.add.text }",(function(e,t){return{dxButton:function(){return{onClick:e.$data.buttons.add.action,text:e.$data.buttons.add.text}},attr:function(){return{title:e.$data.buttons.add.text}}}})),(0,it.addToBindingsCache)("dxButton: { onClick: buttons.delete.action, disabled: selectedIndex === null, text: $parent.removeButtonTitle }, attr: { title: $parent.removeButtonTitle }",(function(e,t){return{dxButton:function(){return{onClick:e.$data.buttons.delete.action,disabled:null===e.$data.selectedIndex,text:e.$parent.removeButtonTitle}},attr:function(){return{title:e.$parent.removeButtonTitle}}}})),(0,it.addToBindingsCache)("dxList: { dataSource: _connectionStrings, onSelectionChanged: function(e) { _selectedConnectionString(e.addedItems) },  selectedItems: _selectedConnectionString.peek(), focusStateEnabled: false, searchEnabled: true, searchExpr: 'description', editEnabled: true, editConfig: { selectionEnabled: true }, selectionMode: 'single', activeStateEnabled: false, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true, searchEditorOptions: { placeholder: $root.dx._static.searchPlaceholder() } }",(function(e,t){return{dxList:function(){return{dataSource:e.$data._connectionStrings,onSelectionChanged:function(t){e.$data._selectedConnectionString(t.addedItems)},selectedItems:e.$data._selectedConnectionString.peek(),focusStateEnabled:!1,searchEnabled:!0,searchExpr:"description",editEnabled:!0,editConfig:{selectionEnabled:!0},selectionMode:"single",activeStateEnabled:!1,noDataText:e.$root.dx._static.noDataText(),encodeNoDataText:!0,searchEditorOptions:{placeholder:e.$root.dx._static.searchPlaceholder()}}}}})),(0,it.addToBindingsCache)("text: $data['description'] || $data['name']",(function(e,t){return{text:function(){return e.$data.description||e.$data.name}}})),(0,it.addToBindingsCache)("text: caption()",(function(e,t){return{text:function(){return e.$data.caption()}}})),(0,it.addToBindingsCache)("visible: !runQueryBuilderDisabled, text: $parent.runQueryBuilderBtnText, click: function() { $parent.runQueryBuilder() }",(function(e,t){return{visible:function(){return!e.$data.runQueryBuilderDisabled},text:function(){return e.$parent.runQueryBuilderBtnText},click:function(){return function(){e.$parent.runQueryBuilder()}}}})),(0,it.addToBindingsCache)("dxTextArea: { value: sqlString, valueChangeEvent: 'keyup input blur', readOnly: disableCustomSql() }",(function(e,t){return{dxTextArea:function(){return{value:e.$data.sqlString,valueChangeEvent:"keyup input blur",readOnly:e.$data.disableCustomSql()}}}})),(0,it.addToBindingsCache)("dxAceEditor: { value: sqlString, additionalOptions: additionalOptions, options: aceOptions }, css: { 'dx-disabled-ace': disableCustomSql() }",(function(e,t){return{dxAceEditor:function(){return{value:e.$data.sqlString,additionalOptions:e.$data.additionalOptions,options:e.$data.aceOptions}},css:function(){return{"dx-disabled-ace":e.$data.disableCustomSql()}}}})),(0,it.addToBindingsCache)("dxList: { dataSource: storedProcedures, searchEnabled: true, searchExpr: 'name', onContentReady: scrollActiveItem, selectedItems: selectedProcedure, editEnabled: true, editConfig: { selectionEnabled: true }, selectionMode: 'single', activeStateEnabled: false, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true }",(function(e,t){return{dxList:function(){return{dataSource:e.$data.storedProcedures,searchEnabled:!0,searchExpr:"name",onContentReady:e.$data.scrollActiveItem,selectedItems:e.$data.selectedProcedure,editEnabled:!0,editConfig:{selectionEnabled:!0},selectionMode:"single",activeStateEnabled:!1,noDataText:e.$root.dx._static.noDataText(),encodeNoDataText:!0}}}})),(0,it.addToBindingsCache)("text: $parent.generateStoredProcedureDisplayName($data)",(function(e,t){return{text:function(){return e.$parent.generateStoredProcedureDisplayName(e.$data)}}})),(0,it.addToBindingsCache)("text: queryNameCaption()",(function(e,t){return{text:function(){return e.$data.queryNameCaption()}}})),(0,it.addToBindingsCache)("dxTextBox: { value: queryName, placeholder: $data.placeholder() }",(function(e,t){return{dxTextBox:function(){return{value:e.$data.queryName,placeholder:e.$data.placeholder()}}}})),(0,it.addToBindingsCache)("dxRadioGroup: { value: selectedQueryType, layout: 'horizontal', items: queryTypeItems }",(function(e,t){return{dxRadioGroup:function(){return{value:e.$data.selectedQueryType,layout:"horizontal",items:e.$data.queryTypeItems}}}})),(0,it.addToBindingsCache)("text: $parent.localizeQueryType($data), attr: { 'title': $parent.localizeQueryType($data) }",(function(e,t){return{text:function(){return e.$parent.localizeQueryType(e.$data)},attr:function(){return{title:e.$parent.localizeQueryType(e.$data)}}}})),(0,it.addToBindingsCache)("template: { name: queryControl().template, data: queryControl() }",(function(e,t){return{template:function(){return{name:e.$data.queryControl().template,data:e.$data.queryControl()}}}})),(0,it.addToBindingsCache)("template: { name: 'dxrd-querybuilder-popup', data: popupQueryBuilder }",(function(e,t){return{template:function(){return{name:"dxrd-querybuilder-popup",data:e.$data.popupQueryBuilder}}}})),(0,it.addToBindingsCache)("dxPopup: popupViewModel($element)",(function(e,t){return{dxPopup:function(){return e.$data.popupViewModel(t)}}})),(0,it.addToBindingsCache)("dxQueryBuilder: qbOptions",(function(e,t){return{dxQueryBuilder:function(){return e.$data.qbOptions}}})),(0,it.addToBindingsCache)("dxButton: { text: getDisplayText('previewResults'), onClick: previewHandler, disabled: okButtonDisabled }",(function(e,t){return{dxButton:function(){return{text:e.$data.getDisplayText("previewResults"),onClick:e.$data.previewHandler,disabled:e.$data.okButtonDisabled}}}})),(0,it.addToBindingsCache)("dxButton: { text: getDisplayText('cancel'), onClick: cancelHandler }",(function(e,t){return{dxButton:function(){return{text:e.$data.getDisplayText("cancel"),onClick:e.$data.cancelHandler}}}})),(0,it.addToBindingsCache)("dxButton: { text: getDisplayText('ok'), type: 'default', onClick: okHandler, disabled: okButtonDisabled }",(function(e,t){return{dxButton:function(){return{text:e.$data.getDisplayText("ok"),type:"default",onClick:e.$data.okHandler,disabled:e.$data.okButtonDisabled}}}})),(0,it.addToBindingsCache)("dxLoadPanel:{ animation: { show: { type: 'fade', from: 0, to: 1, duration: 700 }, hide: { type: 'fade', from: 1, to: 0, duration: 700 } }, deferRendering: false, message: getDisplayText('loading'), visible: showLoadIndicator, shading: true, shadingColor: 'transparent'}",(function(e,t){return{dxLoadPanel:function(){return{animation:{show:{type:"fade",from:0,to:1,duration:700},hide:{type:"fade",from:1,to:0,duration:700}},deferRendering:!1,message:e.$data.getDisplayText("loading"),visible:e.$data.showLoadIndicator,shading:!0,shadingColor:"transparent"}}}})),(0,it.addToBindingsCache)("template: 'dxrd-designer'",(function(e,t){return{template:function(){return"dxrd-designer"}}})),(0,it.addToBindingsCache)("if: qbOptions",(function(e,t){return{if:function(){return e.$data.qbOptions}}})),(0,it.addToBindingsCache)("if: isVisible",(function(e,t){return{if:function(){return e.$data.isVisible}}})),(0,it.addToBindingsCache)("css: { 'dxd-state-active': $data.isExpression() }, dxButtonWithTemplate: { onClick: switchEditors, icon: 'dxrd-svg-wizard-expressions', disabled: disabled }",(function(e,t){return{css:function(){return{"dxd-state-active":e.$data.isExpression()}},dxButtonWithTemplate:function(){return{onClick:e.$data.switchEditors,icon:"dxrd-svg-wizard-expressions",disabled:e.$data.disabled}}}})),(0,it.addToBindingsCache)("dxPopover: { wrapperAttr: { class: $data.className }, width: $data.width || 200, position: position, visible: popoverVisible, target: target, container: popupContainer($element)}",(function(e,t){return{dxPopover:function(){return{wrapperAttr:{class:e.$data.className},width:e.$data.width||200,position:e.$data.position,visible:e.$data.popoverVisible,target:e.$data.target,container:e.$data.popupContainer(t)}}}})),(0,it.addToBindingsCache)("attr: { class: $data.className + '-list'}, dxList: { dataSource: popoverListItems(), onItemClick: itemClickAction }",(function(e,t){return{attr:function(){return{class:e.$data.className+"-list"}},dxList:function(){return{dataSource:e.$data.popoverListItems(),onItemClick:e.$data.itemClickAction}}}})),(0,it.addToBindingsCache)("attr: { class: $data.className + '-list-item'}",(function(e,t){return{attr:function(){return{class:e.$data.className+"-list-item"}}}})),(0,it.addToBindingsCache)("text: name, attr: { title: name }",(function(e,t){return{text:function(){return e.$data.name},attr:function(){return{title:e.$data.name}}}})),(0,it.addToBindingsCache)("text: $data.title || $parent.title",(function(e,t){return{text:function(){return e.$data.title||e.$parent.title}}})),(0,it.addToBindingsCache)("text: description, attr: { title: description }",(function(e,t){return{text:function(){return e.$data.description},attr:function(){return{title:e.$data.description}}}})),(0,it.addToBindingsCache)("dxPopup: { animation: { show: { type: 'fadeIn', duration: 700 }, hide: { type: 'fadeOut', duration: 700 } }, visible: isVisible, wrapperAttr: { class: 'dxrd-wizard dx-editors dxd-text-primary' }, title: isVisible() ? title : '', showTitle: true, fullScreen: false, width: width, height: height, container: $element, visualContainer: _container($element), titleTemplate: _titleTemplate, position: _wizardPopupPosition($element) }, cssArray: [ $data._extendCssClass, { 'dx-rtl': $root.rtl, 'dx-ltr': !$root.rtl }]",(function(e,t){return{dxPopup:function(){return{animation:{show:{type:"fadeIn",duration:700},hide:{type:"fadeOut",duration:700}},visible:e.$data.isVisible,wrapperAttr:{class:"dxrd-wizard dx-editors dxd-text-primary"},title:e.$data.isVisible()?e.$data.title:"",showTitle:!0,fullScreen:!1,width:e.$data.width,height:e.$data.height,container:t,visualContainer:e.$data._container(t),titleTemplate:e.$data._titleTemplate,position:e.$data._wizardPopupPosition(t)}},cssArray:function(){return[e.$data._extendCssClass,{"dx-rtl":e.$root.rtl,"dx-ltr":!e.$root.rtl}]}}})),it.SvgTemplatesEngine.addTemplates({"dxdd-connection-line":'<svg class="dxdd-connection-line dxrd-width-100 dxrd-height-100" > <line class="dxd-qb-relationship-line-color" data-bind="attr: { x1: startPoint().relativeX, y1: startPoint().relativeY, x2: endPoint().relativeX, y2: endPoint().relativeY }" /> </svg>',"dxdd-connecting-point":'<div class="dxdd-connecting-point" data-bind="styleunit: position, trackCursor: underCursor, style: { \'marginLeft\': -3 * _context.zoom() + \'px\', \'marginTop\': -3 * _context.zoom() + \'px\' }, draggable: $root.connectingPointDragHandler"> <svg class="dxrd-width-100 dxrd-height-100"> <line x1="0" y1="0" x2="100%" y2="100%" /> <line x1=0 y1="100%" x2="100%" y2="0" /> </svg> </div>',"dxdd-connection-point-selection":'<div class="dxrd-control" data-bind="draggable: $root.connectionPointDragHandler, styleunit: { top: relativeY, left: relativeX }"> </div>',"dxdd-connector":'<div class="dxdd-connector" data-bind="styleunit: position, trackCursor: underCursor"> \x3c!-- ko template: { name: \'dxdd-connection-line\' } --\x3e \x3c!-- /ko --\x3e </div>',"dxdd-connector-selection":"<div class=\"dxdd-connector\" data-bind=\"styleunit: position, dxclick: function() {}, draggable: $root.dragHandler\"> \x3c!-- ko template: { name: 'dxdd-connection-line' } --\x3e \x3c!-- /ko --\x3e \x3c!-- ko with: startPoint --\x3e \x3c!-- ko template: { name: 'dxdd-connection-point-selection' } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko with: endPoint --\x3e \x3c!-- ko template: { name: 'dxdd-connection-point-selection' } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div>","dxdd-routed-connection-line":'<svg class="dxdd-connection-line"> <defs> \x3c!-- ko if: showArrow --\x3e <marker data-bind="attr: { id: \'dxqb-arrow_end\' + connectorID() }" viewBox="0 0 5 10" refX="5" refY="5" markerUnits="userSpaceOnUse" orient="auto" markerWidth="14" markerHeight="14"> <polyline class="dxd-qb-relationship-line-color" points="0,0 5,5 0,10" /> </marker> \x3c!-- /ko --\x3e \x3c!-- ko if: showRightArrow --\x3e <marker data-bind="attr: { id: \'dxqb-arrow_start\' + connectorID() }" viewBox="0 0 5 10" refX="5" refY="5" markerUnits="userSpaceOnUse" orient="auto-start-reverse" markerWidth="14" markerHeight="14"> <polyline class="dxd-qb-relationship-line-color" points="0,0 5,5 0,10" /> </marker> \x3c!-- /ko --\x3e </defs> <polyline class="dxd-qb-relationship-line-color" fill="none" data-bind="attr: { points: routePointsSet, \'marker-end\': showArrow() ? \'url(#dxqb-arrow_end\' + connectorID() + \')\' : \'\', \'marker-start\': showRightArrow() ? \'url(#dxqb-arrow_start\' + connectorID() + \')\' : \'\' }" /> </svg>',"dxdd-routed-connector":'<div class="dxdd-connector" data-bind="styleunit: position, visible: isVisible"> \x3c!-- ko template: { name: \'dxdd-routed-connection-line\' } --\x3e \x3c!-- /ko --\x3e \x3c!-- ko foreach: routeLineWrappers --\x3e <div data-bind="styleunit: position" class="dxrd-position-absolute"> \x3c!-- ko with: $parent --\x3e <div class="dxd-selectable dxrd-position-relative dxrd-width-100 dxrd-height-100" data-bind="trackCursor: underCursor, click: $root.selectItemProperties" ></div> \x3c!-- /ko --\x3e </div> \x3c!-- /ko --\x3e </div>',"dxdd-routed-connector-selection":'<div class="dxdd-connector dxrd-selected dxd-state-selected" data-bind="styleunit: position, dxclick: function() {}, visible: isVisible"> \x3c!-- ko template: { name: \'dxdd-routed-connection-line\' } --\x3e \x3c!-- /ko --\x3e \x3c!-- ko foreach: routeLineWrappers --\x3e \x3c!-- ko ifnot: isLocked --\x3e <div data-bind="styleunit: position, style: { cursor: isVerticalLine ? \'ew-resize\' : \'ns-resize\' }, routeLineDraggable: { starting: $root.resizeHandler.starting, stopped: function() { resizeStopped(); $root.resizeHandler.stopped(); }, forceResize: resizeHandler }" class="dxrd-position-absolute"> </div> \x3c!-- /ko --\x3e \x3c!-- ko if: isLocked --\x3e <div data-bind="styleunit: position" class="dxrd-position-absolute"> </div> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko with: startPoint --\x3e \x3c!-- ko template: { name: \'dxdd-connection-point-selection\' } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko with: endPoint --\x3e \x3c!-- ko template: { name: \'dxdd-connection-point-selection\' } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div>',"dx-diagram-element":'<div class="dxrd-control dxd-selectable" data-bind="styleunit: position, trackCursor: underCursor"> <div class="dxrd-control-content-main" data-bind="styleunit: { lineHeight: positionLineHeightWithoutMargins }, style: css"> <div data-bind="template: contenttemplate, styleunit: { \'height\': positionLineHeightWithoutMargins, \'width\': positionWidthWithoutMargins }"> </div> </div> </div>',"dx-diagram-element-selection":'<div class="dxrd-control" data-bind="event: { dblclick: $root.inlineTextEdit.show($element) }, css: {\'dxrd-selected\': selected, \'dxrd-focused\': focused }, resizable: $root.resizeHandler, draggable: $root.dragHandler, styleunit: position, trackCursor: underCursor"> \x3c!-- ko if: !$root.inlineTextEdit.visible() --\x3e <div class="dxrd-control-content-main" data-bind="styleunit: { lineHeight: positionLineHeightWithoutMargins }, style: css"> <div data-bind="template: contenttemplate, styleunit: { \'height\': positionLineHeightWithoutMargins, \'width\': positionWidthWithoutMargins}"> </div> </div> \x3c!-- /ko --\x3e \x3c!-- ko if: $root.inlineTextEdit.visible --\x3e \x3c!-- ko with: $root.inlineTextEdit --\x3e <div class="inline-text-edit" data-bind="dxTextArea: { value: text, onKeyUp: keypressAction, valueChangeEvent: \'keyup\' }"></div> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div>',"dx-diagram-element-content":'<div class="dxrd-width-100 dxrd-height-100 dxrd-border-1px-solid-black"> <div data-bind="text: getControlModel().text"></div> </div>',"dxdd-element-content-with-connecting-points":'<div class="dxrd-width-100 dxrd-height-100 dxrd-overflow-hidden"> <svg class="dxrd-top-0 dxrd-left-0 dxrd-width-100 dxrd-height-100 dxrd-stroke-black dxrd-fill-transparent dxrd-stroke-width-1"> \x3c!-- ko if: getControlModel().type() === \'Ellipse\' --\x3e <ellipse cx="50%" cy="50%" rx="50%" ry="50%" /> \x3c!-- /ko --\x3e \x3c!-- ko if: getControlModel().type() === \'Condition\' --\x3e <line x1="50%" y1="0" x2="100%" y2="50%" /> <line x1="100%" y1="50%" x2="50%" y2="100%" /> <line x1="50%" y1="100%" x2="0" y2="50%" /> <line x1="0" y1="50%" x2="50%" y2="0" /> \x3c!-- /ko --\x3e \x3c!-- ko if: !getControlModel().type() --\x3e <rect height="100%" width="100%" /> \x3c!-- /ko --\x3e <text x="50%" y="50%" alignment-baseline="middle" class="dxrd-text-anchor-middle dxrd-cursor-default" data-bind="text: getControlModel().text"></text> </svg> \x3c!-- ko foreach: connectingPoints --\x3e \x3c!-- ko template: \'dxdd-connecting-point\' --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div>',"dx-diagram-surface":'<div class="dxrd-surface dxrd-height-100 dxrd-display-inline-block"  data-bind="styleunit: { \'width\': Math.min(pageWidth(), $root.surfaceSize()) }, click: function(_, e) { $root.selection.clickHandler(null, e); e.stopPropagation(); }, keyDownActions: $root.actionLists.getViewModel()"> <div class="dxrd-viewport dxrd-overflow-auto dxrd-width-inherit dxrd-height-inherit"  data-bind="styleunit: { minWidth: Math.min(pageWidth(), $root.surfaceSize()), maxWidth: pageWidth(),  maxHeight: pageHeight() + 20 }"> <div data-bind="selectable: { selection: $root.selection, zoom: zoom }"> <div class="dxrd-ghost-containment"> <div class="dxrd-ghost-container dxrd-background-white dxrd-position-relative"  data-bind="styleunit: { \'width\': pageWidth(), \'height\': pageHeight() }, trackCursor: underCursor"> \x3c!-- ko foreach: controls --\x3e \x3c!-- ko template: { name: isSelected() ? selectiontemplate : template } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> </div> </div> </div> </div>',"dxqb-joincondition":'<div class="dxdd-connector" data-bind="styleunit: position, trackCursor: underCursor"> <svg class="dxrd-width-100 dxrd-height-100"> <line data-bind="attr: { x1: startPoint.x, y1: startPoint.y, x2: endPoint.x, y2: endPoint.y }" /> </svg> </div>',"dx-query-surface":'<div class="dxrd-surface dxrd-display-inline-block"  data-bind="styleunit: { \'width\': $root.surfaceSize }, click: function(_, e) { $root.selection.clickHandler(null, e); e.stopPropagation(); }, keyDownActions: $root.actionLists.getViewModel()"> <div class="dxrd-viewport dxrd-width-inherit dxrd-height-inherit" > <div class="dxqb-mainwin" data-bind="dxScrollView: { direction: \'both\', showScrollbar: \'always\', scrollByContent: false, scrollByThumb: true, bounceEnabled: false, useNative: true, height: \'100%\' }"> <div class="dxrd-height-100"> <div class="dxrd-ghost-containment dxrd-height-100" > <div class="dxrd-ghost-container dxqb-ghost-container" data-bind="styleunit: { \'minWidth\': pageWidth, \'minHeight\': pageHeight }, trackCursor: { recalculateBounds: true, underCursor: underCursor }"> <div class="dxqb-main" data-bind="trackCursor: { recalculateBounds: true, underCursor: underCursor }, style: { \'z-index\' : $parent.dragDropStarted() ? 2 : null }"> \x3c!-- ko if: tables().length === 0--\x3e <div class="dxqb-placeholder dxd-empty-area-placeholder-text-color dxd-text-info" data-bind="text: placeholder()"></div> \x3c!-- /ko --\x3e \x3c!-- ko foreach: relations --\x3e \x3c!-- ko foreach: conditions --\x3e \x3c!-- ko if: !isSelected()--\x3e \x3c!-- ko template: template --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko foreach: tables --\x3e \x3c!-- ko template: template --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko foreach: relations --\x3e \x3c!-- ko foreach: conditions --\x3e \x3c!-- ko if: isSelected()--\x3e \x3c!-- ko template: selectiontemplate --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko with: $root.columnDragHandler.dragDropConnector --\x3e <svg class="dxdd-connection-line-draggable" data-bind="styleunit: { top: position.top(), left: position.left() }"> \x3c!-- ko foreach: routePoints --\x3e \x3c!-- ko if: $index() < ($parent.routePoints().length - 1) --\x3e <line class="dxd-qb-relationship-line-color" fill="none" data-bind="attr: {x1 : $data.x, y1: $data.y, x2: $parent.routePoints()[$index()+1].x, y2: $parent.routePoints()[$index()+1].y }"></line> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </svg> \x3c!-- /ko --\x3e </div> </div> </div> </div> </div> </div> </div>',"dxqb-relation":"\x3c!-- ko foreach: conditions --\x3e \x3c!-- ko template: { name: isSelected() ? selectiontemplate : template } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e","dxqb-table-field":'<div class="dx-border-inheritor dxd-border-accented"> <div class="dxqb-table-field dxd-qb-table-field-border-color dxd-qb-table-field-back-color dxd-back-highlighted dxd-qb-table-field-separator-color dxd-border-primary" data-bind="trackCursor: { recalculateBounds: true, underCursor: underCursor }, draggable: $root.columnDragHandler, css: cssClasses($root.surface(), $root.columnDragHandler, $parent), click: $root.selectItemProperties"> <div class="dxqb-table-field-background dxd-back-accented"></div> <div class="dxqb-table-field-checkbox-wrapper"> <div class="dxqb-table-field-checkbox" data-bind="dxCheckBox: { value: selectedWrapper }, click: function(surface, e) { surface.toggleSelected(); e.stopPropagation(); return true;  }"></div> </div> <div class="dxqb-table-field-content dxd-text-primary" data-bind="attr: { title: getControlModel().actualName }"> <div class="dxqb-table-field-state"> \x3c!-- ko if:  $data.isAscending() --\x3e <div class="dxqb-image-field-state dxqb-image-field-sorting-asc">\x3c!-- ko template: \'dxrd-svg-queryBuilder-sorting_asc\' --\x3e\x3c!-- /ko --\x3e</div> \x3c!-- /ko --\x3e \x3c!-- ko if:  $data.isDescending() --\x3e <div class="dxqb-image-field-state dxqb-image-field-sorting-desc">\x3c!-- ko template: \'dxrd-svg-queryBuilder-sorting_desc\' --\x3e\x3c!-- /ko --\x3e</div> \x3c!-- /ko --\x3e \x3c!-- ko if: $data.getControlModel().groupBy --\x3e <div class="dxqb-image-field-state dxqb-image-field-group-by">\x3c!-- ko template: \'dxrd-svg-queryBuilder-group_by\' --\x3e\x3c!-- /ko --\x3e</div> \x3c!-- /ko --\x3e \x3c!-- ko if: $data.isAggregate() --\x3e <div class="dxqb-image-field-state dxqb-image-field-aggregate">\x3c!-- ko template: \'dxrd-svg-queryBuilder-aggregate\' --\x3e\x3c!-- /ko --\x3e</div> \x3c!-- /ko --\x3e </div> <div class="dxqb-table-field-caption"> <div class="dxqb-table-field-text dxd-qb-table-field-text-color" data-bind="text: getControlModel().actualName"></div> </div> </div> </div> </div>',"dxqb-table-asterisk-field":'<div class="dx-border-inheritor dxd-border-accented"> <div class="dxqb-table-field dxd-qb-table-field-border-color dxd-qb-table-field-back-color dxd-back-highlighted dxd-border-primary" data-bind="trackCursor: underCursor, css: cssClasses(), click: $root.selectItemProperties"> <div class="dxqb-table-field-background dxd-back-accented"></div> <div class="dxqb-table-field-checkbox-wrapper"> <div class="dxqb-table-field-checkbox" data-bind="dxCheckBox: { value: selectedWrapper }, click: function(surface, e) { surface.toggleSelected(); e.stopPropagation(); return true;  }"></div> </div> <div class="dxqb-table-field-content dxd-text-primary" data-bind="attr: { title: getControlModel().name }"> <div class="dxqb-table-field-caption"> <div class="dxqb-table-field-text dxd-qb-table-field-text-color" data-bind="text: getControlModel().name"></div> </div> </div> </div> </div>',"dxqb-table-main":'\x3c!-- ko if: $data.isSelected() --\x3e <div class="dxqb-table-resize-ghost" data-bind="resizable: resizable($root.resizeHandler, $element), styleunit: position"></div> \x3c!-- /ko --\x3e <div class="dxrd-control" data-bind="css: {\'dxrd-selected\': selected, \'dxrd-focused\': focused }, draggable: $root.dragHandler, styleunit: position, trackCursor: underCursor, click: $root.selectItemProperties"> <div class="dxrd-control-content-main dxd-qb-table-back-color dxd-back-primary2" data-bind="styleunit: { lineHeight: position.lineHeight }, style: css"> <div class="dxqb-table-background dxd-back-accented"></div> <div class="dx-background-inheritor dxd-back-accented"> <div class="dxqb-table-border dxd-qb-table-border-color dxd-back-highlighted dxd-state-active dxd-state-no-hover"></div> </div> <div data-bind="template: contenttemplate, styleunit: { \'height\': position.lineHeight, \'width\': position.width }"> </div> </div> </div>',"dxqb-table":'<div class="dxqb-table dxd-qb-table-back-color dxd-back-primary2"> <div class="dx-border-inheritor dxd-border-accented"> <div class="dxqb-table-head-field dxd-qb-table-field-border-color dxd-qb-table-field-back-color dxd-back-highlighted dxd-border-primary"> <div class="dxqb-table-checkbox-all"> <div data-bind="dxCheckBox: { value: selectedWrapper }, click: function(surface, e) { $root.undoEngine().start(); surface.toggleSelected(); $root.undoEngine().end(); e.stopPropagation(); return true; }"></div> </div> \x3c!-- ko template: titletemplate --\x3e \x3c!-- /ko --\x3e </div> </div> <div class="dxqb-table-content"> <div class="dxqb-tableFields-wrapper"> \x3c!-- ko ifnot: $data.isInitialized() --\x3e <div class="dxqb-table-loading"> <div class="dxqb-table-loading-text dxd-text-primary" data-bind="text: $root.columnsLoadingMsg()"></div> </div> \x3c!-- /ko --\x3e \x3c!-- ko if: $data.isInitialized() --\x3e \x3c!-- ko template: { name: asterisk.template, data: asterisk } --\x3e \x3c!-- /ko --\x3e \x3c!-- ko foreach: columns --\x3e \x3c!-- ko lazy: { template: $data.template  }--\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> </div> </div>',"dxqb-table-title":'<div class="dxqb-table-title" data-bind="style: { cursor: selected() ? \'move\' : \'default\' }"> <div class="dxqb-table-title-content dxd-text-primary" data-bind="text: getControlModel().actualName"></div> </div>',"dxqb-federation-table-title":'<div class="dxqb-table-title dxqb-federation-table-title" data-bind="event: { dblclick: $root.inlineTextEdit.show($element) }, style: { cursor: selected() ? \'move\' : \'default\' }"> <div class="dxqb-federation-table-title-container"> \x3c!-- ko if: !$root.inlineTextEdit.visible() || !selected()  --\x3e <div class="dxqb-table-title-content dxd-text-primary" data-bind="text: getControlModel().actualName"></div> \x3c!-- /ko --\x3e \x3c!-- ko if: $root.inlineTextEdit.visible() && selected() --\x3e \x3c!-- ko with: $root.inlineTextEdit --\x3e <div class="inline-text-edit" data-bind="dxTextArea: { value: text, onKeyUp: keypressAction, valueChangeEvent: \'keyup\' }"></div> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> <div class="dxqb-table-subtitle dxd-text-info" data-bind="text: getControlModel().displaySourceName"></div> </div>',"dxrd-masterDetail-editor":"\x3c!-- ko if: $data --\x3e <div> <div data-bind=\"dxPopup: { showTitle: true, resizeEnabled: true, width: 700, height: 500, maxHeight: '95%', maxWidth: '95%', minHeight: 300, minWidth: 400, title: title(), visible: popupVisible, wrapperAttr: { class: 'dx-filtereditor' }, toolbarItems: buttonItems, showCloseButton: true, container: $root.getPopupContainer($element), position: { of: $root.getPopupContainer($element) }, }\"> <div class=\"dx-filtereditor-tree dxd-border-secondary dxrd-cursor-default\" data-bind=\"dxScrollView: { showScrollbar: 'onHover', direction: 'both' }\" > \x3c!-- ko foreach: masterQueries --\x3e <div class=\"criteria-operator-group\"> <div class=\"criteria-operator-group-item\"> <div class=\"criteria-operator-text dxd-filter-editor-text-color criteria-operator-item-group dxd-filter-editor-group-back-color dxd-state-no-hover stylized\" data-bind=\"text: queryName\"></div> <div data-bind=\"service: { name: 'createRelation' }\"></div> </div> <div class=\"criteria-operator-content\"> \x3c!-- ko template: { name: 'dx-masterDetail-editor-relation', foreach: relations }--\x3e \x3c!-- /ko --\x3e </div> </div> \x3c!-- /ko --\x3e </div> </div> <div class=\"dx-selectbox-popup-wrapper dx-dropdownlist-popup-wrapper dx-filtereditor-criteriaoperator-popup dx-dropdowneditor-overlay\" data-bind=\"dxPopupWithAutoHeight: { height: '300px', focusStateEnabled: false, wrapperAttr: { class: 'dx-selectbox-popup-wrapper dx-dropdownlist-popup-wrapper dx-filtereditor-criteriaoperator-popup dx-dropdowneditor-overlay' }, position: $root.rtl ? { my: 'right top', at: 'right bottom', of: popupService.target } : { my: 'left top', at: 'left bottom', of: popupService.target }, container: $root.getPopupContainer($element), target: popupService.target, showTitle: false, showCloseButton: false, animation: {}, hideOnOutsideClick: true, shading: false, minWidth:'170px', maxWidth:'500px', width: 'auto', visible: popupService.visible }\"> \x3c!-- ko with: popupService--\x3e \x3c!-- ko with: data --\x3e \x3c!-- ko template: template--\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> </div> \x3c!-- /ko --\x3e","dx-masterDetail-editor-keyColumn":'<div class="criteria-operator-text dxd-filter-editor-text-color criteria-operator-item-field dxd-filter-editor-field-back-color dxd-state-no-hover stylized" data-bind="attr: { title: $data.getTitle() }, text: queryName"></div> <div class="criteria-operator-text dxd-filter-editor-text-color">.</div> <div class="criteria-operator-text dxd-filter-editor-text-color"> <div data-bind="service: { name: \'setColumn\' }"></div> </div>',"dx-masterDetail-editor-relation":'<div class="criteria-operator-group"> <div class="criteria-operator-group-item"> <div class="criteria-operator-action" data-bind="dxclick: function() {  $parent.remove($data); }"> <div class="criteria-operator-action-image dx-image-filtereditor-remove">\x3c!-- ko template: \'dxrd-svg-filtereditor-remove\' --\x3e\x3c!-- /ko --\x3e</div> </div> <div class="criteria-operator-item" data-bind="visible: isEditable"> <div class="criteria-operator-item-editor" data-bind="dxTextBox: { value: relationName, onFocusOut: function() { isEditable(false); } }, focus: { on: isEditable }"></div> </div> <div class="criteria-operator-text dxd-filter-editor-text-color criteria-operator-item-value dxd-filter-editor-value-back-color" data-bind="visible: !isEditable()"> <div class="criteria-operator-text dxd-filter-editor-text-color clickable" data-bind="text: relationName, click: function() { isEditable(true); }"></div> </div> <div class="criteria-operator-action" data-bind="dxclick: create"> <div class="criteria-operator-action-image dx-image-filtereditor-add">\x3c!-- ko template: \'dxrd-svg-filtereditor-add\' --\x3e\x3c!-- /ko --\x3e</div> </div> </div> <div class="criteria-operator-content"> \x3c!-- ko foreach: keyColumns --\x3e <div class="criteria-operator-group"> <div class="criteria-operator-group-item"> <div class="criteria-operator-action" data-bind="dxclick: function() {  $parent.remove($data); }"> <div class="criteria-operator-action-image dx-image-filtereditor-remove">\x3c!-- ko template: \'dxrd-svg-filtereditor-remove\' --\x3e\x3c!-- /ko --\x3e</div> </div> \x3c!-- ko template: { name: \'dx-masterDetail-editor-keyColumn\', data: master } --\x3e \x3c!-- /ko --\x3e <div class="criteria-operator-text dxd-filter-editor-text-color"> = </div> \x3c!-- ko template: { name: \'dx-masterDetail-editor-keyColumn\', data: detail } --\x3e \x3c!-- /ko --\x3e </div> </div> \x3c!-- /ko --\x3e </div> </div>',"dx-masterdetail-editor-setColumn":"<div class=\"criteria-operator-text dxd-filter-editor-text-color clickable criteria-operator-item-field dxd-filter-editor-field-back-color\" data-bind=\"text: target.column() || target.selectColumnText(), dxclick: showPopup, css: { 'dxd-state-selected': target.isSelected, 'default': !target.column() }\"></div>","dxqb-data-preview":'<div class="dxrd-height-100" data-bind="dxdTableView: $data.value"></div>',"dxqb-popup":'<div data-bind="dxPopup: { animation: { show: { type: \'fade\', from: 0, to: 1, duration: 700 }, hide: { type: \'fade\', from: 1, to: 0, duration: 700 } }, wrapperAttr: { class: \'dxqb-preview\' }, visible: isVisible, title: title(), showTitle: true, resizeEnabled: true, shading: true, shadingColor: \'transparent\', fullScreen: false, width: 800, height: 544, container: container($element), position: { of: container($element) }, onHidden: function() { $data.data.value(null) }, focusStateEnabled: false }"> <div class="dxqb-preview-popup-content"> \x3c!-- ko if: data && !isLoading() --\x3e \x3c!-- ko template: { name: template, data: data } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko if: isLoading --\x3e <div class="dxrd-text-align-center dxrd-padding-top-25"> <div data-bind="dxLoadIndicator: { visible: true }"></div> </div> \x3c!-- /ko --\x3e </div> <div class="dxqb-preview-popup-buttons dxd-border-secondary"> <div class="dxqb-preview-popup-button" data-bind="dxButton: { text: okButtonText(), onClick: okButtonHandler, disabled: isLoading }"></div> </div> </div>',"dxqb-selectstatment-preview":'<div class="dxqb-show-query-string-content dx-widget"> \x3c!-- ko if: !aceAvailable --\x3e <div class="dxrd-show-query-string-editor" data-bind="dxTextArea: { value: value, valueChangeEvent: \'keyup\', readOnly: true }"></div> \x3c!-- /ko --\x3e \x3c!-- ko if: aceAvailable --\x3e <div class="dxrd-show-query-string-editor"> <div class="dxrd-show-query-string-editor-content"> <div class="dx-sql_editor dx-disabled-ace dxd-back-primary2" data-bind="dxAceEditor: { value: value, options: aceOptions, additionalOptions: additionalOptions }"></div> </div> </div> \x3c!-- /ko --\x3e </div>',"dxd-tableview":'<div class="dxd-tableview"> <div class="dxd-tableview-titles"> <table> <tbody> <tr> \x3c!-- ko foreach: rtl ? data.schema.reverse() : data.schema --\x3e <td data-bind="attr: { class: \'dxd-tableview-title-cell dxd-border-secondary dxd-tableview-resizable\' }"> <div class="dxd-tableview-cell-text dxd-qb-data-preview-cell-text-color dxd-text-info" data-bind="text: name"></div> </td> \x3c!-- /ko --\x3e </tr> </tbody> </table> </div> \x3c!-- ko if: data.values --\x3e <div class="dxd-tableview-data" data-bind="dxScrollView: { direction: \'both\', scrollByContent: false, showScrollbar: \'always\', scrollByThumb: true, bounceEnabled: false, useNative: false, onScroll: $data.onDataScroll, onInitialized: $data.onDataScrollInitialized }"> <table> <tbody> \x3c!-- ko foreach: data.values --\x3e <tr> \x3c!-- ko foreach: $parent.rtl ? $data.reverse() : $data --\x3e <td class="dxd-tableview-cell dxd-border-secondary"> \x3c!-- ko ifnot: $parents[1].isImage($index()) --\x3e <div data-bind="text: $data, attr: { class: \'dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable\' + $index() }"></div> \x3c!-- /ko --\x3e \x3c!-- ko if: $parents[1].isImage($index()) --\x3e \x3c!-- ko if: $parents[1].isImageTooLarge($data) --\x3e <div data-bind="text: $parents[1].getImageTooLargeText($data), attr: { class: \'dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable\' + $index() }"></div> \x3c!-- /ko --\x3e \x3c!-- ko ifnot: $parents[1].isImageTooLarge($data) --\x3e <div data-bind="attr: { class: \'dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable\' + $index() }"> <img data-bind="attr: { src: \'data:image/bmp;base64,\' + $data }" /> </div> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </td> \x3c!-- /ko --\x3e </tr> \x3c!-- /ko --\x3e <tr class="dxd-tableview-data-fakerow"> \x3c!-- ko foreach: data.values[0] --\x3e <td class="dxd-tableview-cell dxd-border-secondary"> <div data-bind="attr: { class: \'dxd-tableview-cell-text dxd-text-primary dxd-tableview-resizable\' + $index() }"></div> </td> \x3c!-- /ko --\x3e </tr> </tbody> </table> </div> \x3c!-- /ko --\x3e \x3c!-- ko ifnot: data.values --\x3e <div class="dxd-tableview-empty-message" data-bind="text: noDataText()"></div> \x3c!-- /ko --\x3e </div>',"dxqb-fieldlist-wrapper":'<div id="tree" data-bind="treelist: treeListOptions" class="dxrd-width-100 dxrd-height-100"></div>',"dxqb-properties-wrapper":'<div class="dxrd-properties-wrapper" data-bind="visible: active && visible"> <div class="dxd-text-primary dxrd-properties-wrapper-full-height"> \x3c!-- ko with: model --\x3e <div class="dxrd-properties-grid dxd-border-primary dxrd-top-0px" data-bind="dxScrollView: { showScrollbar: \'onHover\', useNative: false, scrollByThumb: true }" > <div> \x3c!-- ko foreach: groups --\x3e <div class="dx-fieldset" data-bind="visible: visible"> <div class="dx-accordion" data-bind="dxdAccordion: { collapsed: collapsed }"> <div class="dxrd-group-header dx-accordion-header dxd-border-primary" data-bind="css: { \'dxrd-group-header-collapsed\': collapsed() }"> <div class="dx-collapsing-image dxrd-display-inline-block" data-bind="template: \'dxrd-svg-collapsed\', css: { \'dx-image-expanded\': !collapsed() }" ></div> <span class="dxrd-group-header-text" data-bind="text: displayName()"></span> </div> <div class="dx-accordion-content dxd-back-primary dxd-border-primary"> \x3c!-- ko ifnot: editorsCreated --\x3e <div class="dx-accordion-content-loading-panel"> <div data-bind="dxLoadIndicator: { visible: !editorsCreated() }"></div> </div> \x3c!-- /ko --\x3e \x3c!-- ko if: $data.editorsRendered() --\x3e <div data-bind="visible: editorsCreated"> <div class="dx-editors"> \x3c!-- ko foreach: editors --\x3e \x3c!-- ko template: editorTemplate --\x3e \x3c!-- /ko --\x3e \x3c!-- ko if: ($index() === $parent.editors().length - 1 && $parent.editorsCreated(true)) --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> </div> \x3c!-- /ko --\x3e </div> </div> </div> \x3c!-- /ko --\x3e </div> </div> \x3c!-- /ko --\x3e </div> </div>',"dxqb-propertygrid":'\x3c!-- ko with: value --\x3e <div class="dx-fieldset dxqb-selected-properties"> \x3c!-- ko foreach: properties.getEditors() --\x3e \x3c!-- ko template: editorTemplate --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> \x3c!-- /ko --\x3e',"dxqb-fieldspanel":'\x3c!-- ko with: value --\x3e <div data-bind="treeListSearchPanel: { controllers: $data.fieldListModel.treeListOptions().treeListController }"></div> <div class="dxqb-right-panel-fields" data-bind="dxScrollView: { showScrollbar: \'onHover\', scrollByContent: false, scrollByThumb: true, bounceEnabled: false, useNative: false }"> \x3c!-- ko template: { name: "dxqb-fieldlist-wrapper", data: fieldListModel } --\x3e \x3c!-- /ko --\x3e </div> \x3c!-- /ko --\x3e',"dxqb-parameterspanel":'\x3c!-- ko with: value --\x3e <div class="dxqb-right-panel-parameters" data-bind="dxScrollView: { showScrollbar: \'onHover\', scrollByContent: false, scrollByThumb: true, bounceEnabled: false, useNative: false }"> <div class="dxrd-width-100" data-bind="dxCollectionEditor: $data"></div> </div> \x3c!-- /ko --\x3e',"dxqb-collectioneditor-template":'<div class="dxrd-accordion-collection-item" data-bind="dxdAccordion: { collapsed: collapsed, setCollapsed: setCollapsed, getCollapsed: getCollapsed, setCollapsedChangedEvent: setCollapsedChangedEvent }"> <div class="dxrd-group-header dx-accordion-header"> <div> <div class="dx-collapsing-image dxrd-display-inline-block" data-bind="css: { \'dx-image-expanded\': !collapsed }, template: \'dxrd-svg-collapsed\'"></div> <div class="dx-group-header-font dxrd-display-inline-block" data-bind="text: $root.getLocalization(value.name), attr: { title: $root.getLocalization(value.name) }"></div> </div> </div> <div class="dx-accordion-content"> <div data-bind="template: { name: \'dx-propertieseditor\', data: getProperties({ level: 1 }) }"></div> </div> </div>',"dxqb-properties-wrapper-editorlist":'<div class="dxrd-properties-wrapper" data-bind="visible: active && visible"> <div  class="dxd-text-primary dxrd-height-100"> \x3c!-- ko with: model --\x3e <div class="dxrd-properties-grid dxd-border-primary dxrd-top-0px" data-bind="dxScrollView: { showScrollbar: \'onHover\', useNative: false, scrollByThumb: true }" > <div class="dx-fieldset dxqb-selected-properties"> \x3c!-- ko foreach: getEditors() --\x3e \x3c!-- ko template: editorTemplate --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> </div> \x3c!-- /ko --\x3e </div> </div>',"dxqb-toolbar":'<div class="dxrd-toolbar-wrapper dxd-toolbar-back-color" data-bind="click: $root.findControl"> <div class="dxqb-toolbar-background dxd-back-primary-invariant"></div> <div class="dxrd-toolbar" data-bind="template: {name: \'dxrd-toolbar-tmplt\', data: actionLists.getViewModel().toolbarItems }"></div> </div>',"dxqb-property-editor":'<div class="dxrd-editor" data-bind="visible: visible"> <div class="dxrd-editor-header"> <div class="dx-field dxd-back-primary"> <div class="dx-field-label dxd-text-primary"> <div class="propertygrid-editor-displayName" data-bind="text: displayName, attr: { \'title\': displayName }"></div> </div> <div class="dx-field-value"> <div data-bind="service: { name: \'createEditorAddOn\' }"></div> \x3c!-- ko lazy: { template: templateName } --\x3e \x3c!-- /ko --\x3e </div> </div> </div> </div>',"dxqb-expressionstring":'\x3c!-- ko if: $data.value --\x3e <div data-bind="dxExpressionEditor: getOptions({ options: value, fieldListProvider: $root.parametersBindingsProvider, displayNameProvider: $root.displayNameProvider && $root.displayNameProvider() })"></div> \x3c!-- /ko --\x3e',"dxqb-filtereditor-changeparameter":'<div class="criteria-operator-item" data-bind="visible: target.isEditable"> <div class="criteria-operator-item" data-bind="dxTextBox: { value: target._parameterName, onFocusOut: function() { target.createParameter(); target.isEditable(false); } }"></div> </div> <div class="criteria-operator-text dxd-filter-editor-text-color clickable " data-bind="text: target.parameterName, dxclick: showPopup, css: { \'dxd-state-selected\': target.isSelected, \'default\': target.isDefaultTextDisplayed() }, visible: $data.target.isEditable() === false"></div>',"dxqb-filtereditor-propertiespopup":'<div class="dx-widget" data-bind="dxScrollView: { showScrollbar: \'onHover\' }"> \x3c!-- ko foreach: data --\x3e <div data-bind="dxdAccordion: { collapsed: collapsed }"> <div class="dx-accordion-header"> <div class="dx-filtereditor-popup-item dxd-list-item-back-color dx-item dx-list-item dxd-back-highlighted"> <div class="dx-collapsing-image dxrd-display-inline-block dxrd-margin-left-5px" data-bind="template: \'dxrd-svg-collapsed\', css: { \'dx-image-expanded\': !collapsed() }" ></div> <span class="dx-item-content dx-list-item-content dxrd-padding-left-17px"  data-bind="text: name"></span> </div> </div> <div class="dx-accordion-content dxd-back-primary"> \x3c!-- ko foreach: items --\x3e <div class="dx-filtereditor-popup-item dx-item dx-list-item dxd-list-item-back-color dxd-back-highlighted"> <span class="dx-item-content dx-list-item-content dxrd-padding-left-17px"  data-bind="text: name, attr: { \'title\': name }, click: function() { $parents[1].click($data); }"></span> </div> \x3c!--/ko --\x3e </div> </div> \x3c!--/ko --\x3e </div>',"dxqb-filtereditor-parameterspopup":'<div class="dx-widget" data-bind="dxScrollView: { showScrollbar: \'onHover\' }"> \x3c!-- ko if: $parent.viewModel.canCreateParameters --\x3e <div class="dx-filtereditor-popup-item dx-item dx-list-item dxd-list-item-back-color dxd-back-highlighted"> <span class="dx-item-content dx-list-item-content" data-bind="text: $parent.viewModel.defaultDisplay(), click: function() { $parent.viewModel.isEditable(true); $parent.viewModel._parameterName(\'\'); $parent.visible(false); }"></span> </div> \x3c!-- /ko --\x3e \x3c!-- ko foreach: data --\x3e <div class="dx-filtereditor-popup-item dx-item dx-list-item dxd-list-item-back-color dxd-back-highlighted"> <span class="dx-item-content dx-list-item-content" data-bind="text: name, click: function() { $parent.click($data); } "></span> </div> \x3c!-- /ko --\x3e </div>',"dxqb-treelist-item-with-search":'<div data-bind="visible: visible"> \x3c!-- ko template: "dxqb-treelist-header-item-with-search" --\x3e \x3c!-- /ko --\x3e </div>',"dxqb-treelist-header-item-with-search":'<div class="dx-treelist-item dxd-list-item-back-color dxd-back-highlighted" data-bind="event: { dblclick: events.dblclick }, styleunit: padding, css: { \'dx-treelist-item-selected dxd-state-selected\': isSelected || isMultiSelected }"> <div class="dx-treelist-collapsedbutton"></div> <div class="dx-treelist-caption"> <div class="dx-treelist-selectedcontent" data-bind="click: toggleSelected,  draggable: isDraggable ? dragDropHandler : null"> <div class="dx-treelist-image" data-bind="css: imageClassName, template: { name: imageTemplateName, if: !!imageTemplateName }, attr: { title: text }"> </div> \x3c!-- ko template: { name: \'dx-treelist-item-text-content\' } --\x3e \x3c!-- /ko --\x3e </div> </div> </div>',"dxrd-page-choose-datasource-type":"<div class=\"dxrd-wizard-type-page\" data-bind=\"css: $data._extendCssClass('type-page')\"> \x3c!-- ko foreach: typeItems --\x3e \x3c!-- ko template: 'dxrd-page-choose-type-item' --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div>","dxrd-page-choose-type-item":"<div data-bind=\"event: { click: $parent._itemClick, dblclick: function() { $parent._goToNextPage() } }, attr: { class: 'dxd-back-highlighted dxd-state-normal dxrd-wizard-type-item dx-fontsize-reestablished dxrd-wizard-type-item-border-color dxd-list-item-back-color ' + $parent._extendCssClass('type-item')}, css: { 'dxd-border-secondary dxd-back-secondary': $parent._IsSelected($data) } \"> <div data-bind=\"attr: { class: 'dxrd-wizard-type-image ' + $parent._extendCssClass('type-image') }, css: imageClassName, template: { name: imageTemplateName }\"> </div> \x3c!-- ko if: !!$parent._textToSearch --\x3e <div class=\"dxrd-wizard-type-text\" data-bind=\"searchHighlighting: { text: text, textToSearch: $parent._textToSearch }, attr: { title: text }, css: $parent._extendCssClass('type-text')\"></div> \x3c!-- /ko --\x3e \x3c!-- ko ifnot: !!$parent._textToSearch --\x3e <div class=\"dxrd-wizard-type-text\" data-bind=\"text: text, attr: { title: text }, css: $parent._extendCssClass('type-text')\"></div> \x3c!-- /ko --\x3e </div>","dxrd-wizard-add-federated-queries-page":'<div class="dxrd-wizard-page dxrd-wizard-add-federation-queries-page dxrd-wizard-page-treelist-accordion-style"> <div data-bind="treeListSearchPanel: {controllers: ko.unwrap(_fieldListModel).treeListController}"></div> <div class="dxrd-wizard-dataMember dxd-border-secondary" data-bind="dxScrollView: { showScrollbar: \'onHover\', height: _scrollViewHeight }"> <div class="dxrd-wizard-dataMember-treelist dxrd-width-100 dxrd-height-100" data-bind="treelist: _fieldListModel" ></div> </div> \x3c!-- ko template: { name: $data._queriesPopupHelper.template, data: _queriesPopupHelper } --\x3e \x3c!-- /ko --\x3e <div class="dxrd-wizard-load-panel dxd-text-primary" data-bind="dxLoadPanel: _loadPanelViewModel($element)"> </div> </div>',"dx-treelist-federation-action-with-popover":"<div class=\"dx-treelist-action-with-popover dxrd-display-inline-block\" > <div class=\"dx-treelist-action\" data-bind=\"dxButtonWithTemplate: { onClick: function() { clickAction($parent); }, icon: $data.imageTemplateName, iconClass: $data.imageClassName , disabled: $data.disabled && $data.disabled() }, attr: { title: text }\"></div> \x3c!-- ko template: { name: 'dx-popover-list-items', data: ko.utils.extend($parent.data, { className: 'dxrd-federation-addqueries-popover', position: $root.rtl ? 'right' : 'left', popupContainer: $root.getPopupContainer, width: 150 }) }--\x3e \x3c!-- /ko  --\x3e </div>","dxrd-querybuilder-select-popup-content":'<div class="dxrd-federation-result-grids dxrd-federation-data-container-column"> <div class="dxd-border-secondary dxrd-select-querybuilder-surface" data-bind="resizable: gridResizeHelper.resizable($root.resizeHandler, \'s\')"> <div class="dxrd-height-100" data-bind="dxQueryBuilderSurface: queryBuilderSurfaceCreator"></div> </div> <div class="dx-accordion dxd-border-secondary dxrd-select-result-alias-grid" data-bind="dxdAccordion: { collapsed: joinResultCollapsed }"> <div class="dxd-back-secondary dx-accordion-header dxd-text-primary"> <div class="dx-collapsing-image dxrd-display-inline-block" data-bind="template: \'dxrd-svg-collapsed\', css: { \'dx-image-expanded\': !joinResultCollapsed() }" ></div> <span class="dxrd-group-header-text" data-bind="text: $root.getLocalization(\'Join result\', \'DataAccessUIStringId.FederationDataSourceQueryBuilder_SelectResult\')"></span> </div> <div class="dx-accordion-content dxd-border-secondary" data-bind="style: { height: resultGridHeight }"> <div class="dxrd-federation-action-add dxrd-collectioneditor-action" data-bind="dxButton: { icon: \'add\', disabled: addRowDisabled, onClick:addRow }"></div> <div data-bind="dxDataGrid: columnsGrid"></div> </div> </div> </div>',"dxrd-querybuilder-column-combobox-masked":'<div class="dxrd-querybuilder-column-container dxrd-querybuilder-column-container-masked"> <div class="dxrd-querybuilder-column-data"> <div class="dx-texteditor-input" data-bind="text: cellInfo.column"></div> </div> <div class="dxrd-querybuilder-column-editorswitch"> \x3c!-- ko template: { name: \'dx-wizard-menu-box-editorswitch\', data: $data } --\x3e \x3c!-- /ko  --\x3e </div> </div>',"dxrd-querybuilder-column-combobox":"<div class=\"dxrd-querybuilder-column-container dxrd-querybuilder-column-container-full\"> <div class=\"dxrd-querybuilder-column-data\"> <div class=\"dx-texteditor-input dx-padding-none\" data-bind=\"dxDropDownBox: { dataSource: $data.allColumns, dropDownOptions: { container: $root.getPopupContainer($element), width:  $root.calculateWithZoomFactor(357)  }, popupPosition: { boundary: $root.getPopupContainer($element) }, value: cellInfo.key, valueExpr: 'key', displayExpr: 'column' }, visible: !$data.isExpression()\"> <div data-bind=\"dxDataGrid: { dataSource: $data.allColumns, keyExpr: 'key', remoteOperations: true, height: $root.calculateWithZoomFactor(250), columns: [{ dataField: 'column', sortOrder: 'asc' } , { dataField: 'table'}], hoverStateEnabled: true, selection: { mode: 'single' }, selectedRowKeys: [cellInfo.key], focusedRowEnabled: true, focusedRowKey: cellInfo.key, onSelectionChanged: $data.changeColumn } \"></div> </div> <div class=\"dx-texteditor-input dx-padding-none\" data-bind=\"dxExpressionEditor: { options: expression, fieldListProvider: itemsProvider, displayNameProvider: $root.displayNameProvider && $root.displayNameProvider() }, visible: $data.isExpression\"></div> </div> <div class=\"dxrd-querybuilder-column-editorswitch\"> \x3c!-- ko template: { name: 'dx-wizard-menu-box-editorswitch', data: $data } --\x3e \x3c!-- /ko  --\x3e </div> </div>","dxrd-querybuilder-transform-popup-content":'<div class="dxrd-federation-result-grids dxrd-federation-data-container-column"> <div class="dx-accordion dxd-border-secondary dxrd-transform-source-grid dxrd-result-source-grid"> <div class="dxd-back-secondary dx-accordion-header dxd-text-primary"> <span class="dxrd-group-header-text" data-bind="text: $data.transformGridTitle"></span> </div> <div class="dx-accordion-content dxd-border-secondary"> <div class="dxd-border-primary dxrd-transform-source-grid dxrd-result-source-grid" data-bind="dxDataGrid: transformGrid"></div> </div> </div> <div class="dx-accordion dxd-border-secondary dxrd-transform-result-grid"  data-bind="dxdAccordion: { collapsed: transformResultCollapsed }"> <div class="dxd-back-secondary dx-accordion-header dxd-text-primary"> <div class="dx-collapsing-image dxrd-display-inline-block" data-bind="template: \'dxrd-svg-collapsed\', css: { \'dx-image-expanded\': !transformResultCollapsed() }" ></div> <span class="dxrd-group-header-text" data-bind="text: $data.transformResultGridTitle"></span> </div> <div class="dx-accordion-content dxd-border-secondary"> <div data-bind="dxScrollView: { showScrollbar: \'onHover\' }"> <div data-bind="treelist: resultFieldListModel" class="dxrd-width-100 dxrd-height-100"></div> </div> </div> </div> </div>',"dxrd-querybuilder-transform-checkbox":'<div class="dxrd-querybuilder-transform-checkbox" data-bind="dxCheckBox: transform"></div>',"dxrd-querybuilder-federation-popup":'<div data-bind="dxPopup: { showTitle: true, title: title(), wrapperAttr: { class: cssClass }, visible: popupVisible, toolbarItems: buttonItems, showCloseButton: true, fullScreen: false, maxHeight: maxHeight, height: height, width: width, onShowing: function(e) { $data.loaded(true); }, rtlEnabled: rtl, onHiding: onHiding, container: getPopupContainer($element), position: { of: getPopupContainer($element)}, resizeEnabled: true, minHeight: 450, minWidth: 650 }"> <div class="dxrd-querybuilder-federation-popup"> \x3c!-- ko if: popupVisible --\x3e \x3c!-- ko if: loaded --\x3e <div class="dxrd-federation-treelist-wrapper dxd-border-secondary" data-bind="resizable: resizeHelper.resizable($root.resizeHandler, \'e\')"> <div class="dxrd-federation-treelist" data-bind="dxScrollView: { showScrollbar: \'onHover\' }"> <div data-bind="treelist: fieldListModel" class="dxrd-width-100 dxrd-height-100"></div> </div> </div> <div class="dxrd-federation-data-container"> \x3c!-- ko template: { name: popupContentTemplate } --\x3e \x3c!-- /ko --\x3e </div> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> </div>',"dxrd-querybuilder-union-popup-content":'<div class="dxrd-federation-result-grids dxrd-federation-data-container-column"> <div class="dxd-border-secondary dxrd-union-result-source-grid dxrd-result-source-grid" data-bind="dxDataGrid: sourcesGrid"></div> <div class="dxd-border-secondary dxrd-union-result-alias-grid"> <div data-bind="dxDataGrid: aliasGrid"></div> </div> </div>',"dx-wizard-fullscreen":'\x3c!-- ko if: $data && $data.isVisible() --\x3e <div class="dx-fullscreen-wizard dx-editors" data-bind="css: { \'dx-rtl\': $root.rtl, \'dx-ltr\': !$root.rtl }"> <div class="dxrd-wizard dxrd-report-wizard dx-editors dxd-text-primary dxd-back-primary2" data-bind="css: _extendCssClass"> <div class="dxrd-wizard-steps-container dxd-back-primary" data-bind="visible: $data.navigationPanel().isVisible"> <div class="dxrd-wizard-title dxd-border-primary" data-bind="text: _description(), attr: {\'title\': _description() }"></div> \x3c!-- ko with: navigationPanel --\x3e <div class="dxrd-wizard-steps" data-bind="foreach: _steps"> <div class="dxrd-wizard-steps-relative dxrd-position-relative"  data-bind="visible: $data.visible"> <div class="dxrd-wizard-steps-content" data-bind="click: $data.clickAction, text: $data.text, attr: {\'title\': $data.text }, css: { \'dxrd-disabled\': $data.disabled, \'dxd-back-secondary\': $data.isActive() }"></div> <div class="dxrd-wizard-steps-marker dxd-back-primary2" data-bind="visible: $data.isActive"></div> </div> </div> \x3c!-- /ko --\x3e </div> <div class="dxrd-wizard-content" data-bind="css: { \'withoutPanel\': !$data.navigationPanel().isVisible() }"> <div class="dxrd-wizard-part-description dxd-back-contrast"> <div class="dxrd-wizard-part-description-text dxd-text-primary dxd-border-primary" data-bind="text: _pageDescription()"></div> </div> \x3c!-- ko with: _currentPage --\x3e <div class="dxrd-wizard-work-content"> <div class="dxrd-wizard-work-content-relative"> <div data-bind="template: { name: template, data: page } "></div> </div> </div> \x3c!-- /ko --\x3e <div class="dxrd-wizard-load-panel dxd-text-primary" data-bind="dxLoadPanel: _loadPanelViewModel($element)"> </div> <div class="dxrd-wizard-navigation"> <div data-bind="dxButton: cancelButton" class="dxrd-wizard-btn left"></div> <div data-bind="dxButton: finishButton" class="dxrd-wizard-btn right"></div> <div data-bind="dxButton: nextButton" class="dxrd-wizard-btn right"></div> <div data-bind="dxButton: previousButton" class="dxrd-wizard-btn right"></div> </div> </div> </div> </div> \x3c!-- /ko --\x3e',"dx-wizard-fullscreen-page":'\x3c!-- ko if: $data._sections.length > 0 --\x3e <div class="dxrd-position-absolute" data-bind="foreach: _sections, css: _className, styleunit: { top: _parentMarginOffset, left: _parentMarginOffset, right: _parentMarginOffset, bottom: _parentMarginOffset }"> <div class="dx-border-inheritor dxd-border-accented"> <div class="dxrd-report-page-tile dxd-border-secondary" data-bind="css: { \'dxrd-disabled\': !$data.page() }, style: $parent._pageCss[$data.pageId]"> <div class="dxrd-report-page-tile-title" data-bind="visible: $parent._showPageDescription($data), text: $parent._getPageDescription($index(), $data), attr: { title: $parent._getPageDescription($index(), $data) }"></div> \x3c!-- ko if: $data.page() !== null --\x3e \x3c!-- ko with: page --\x3e <div class="dxrd-report-page-tile-content" data-bind="template: { name: $parent.metadata.template, data: page }, dxScrollView: { showScrollbar: \'onHover\'}"></div> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko if: $data.page() === null --\x3e <div class="dxrd-report-page-tile-content dx-default-border-style dxd-border-secondary"> <div class="dxrd-wizard-page dxrd-wizard-disabled-content" data-bind="text: metadata.disabledText"></div> </div> \x3c!-- /ko --\x3e </div> </div> </div> \x3c!-- /ko --\x3e',"dxrd-jsondatasource-fields-page":'<div class="dxrd-wizard-page dx-jsonschema-page dx-frameless-style"> <div class="dx-default-border-style dxd-border-secondary dxrd-wizard-dataMember dx-fieldset dxrd-height-100" > <div class="dx-field"> <div class="dx-field-label" data-bind="text: _rootElementTitle"></div> <div class="dx-field-value" data-bind="dxSelectBox: { dataSource: _rootElementList, value: _selectedRootElement, displayExpr: \'fullPath\', displayCustomValue: true, useItemTextAsTitle: true, dropDownOptions: { container: $root.getPopupContainer($element) } }"></div> </div> <div class="dxrd-wizard-add-queries-page dxrd-wizard-page-treelist-accordion-style dxd-border-secondary" data-bind="dxScrollView: { showScrollbar: \'onHover\' }"> <div data-bind="treelist: _fieldListModel" class="dxrd-width-100 dxrd-height-100"></div> </div> </div> </div>',"dxrd-page-jsonsource":'<div class="dxrd-wizard-page dx-jsonsource-page dx-frameless-style"> <div class="dx-default-border-style dxd-border-secondary dxrd-wizard-dataMember dx-fieldset dxrd-height-100"  data-bind="dxScrollView: { showScrollbar: \'onHover\', useNative: false, scrollByThumb: true }, dxValidationGroup: $data._validationGroup || {}"> <div class="dx-field"> <div class="dx-property-required dx-field-label" data-bind="text: _jsonConnectionTitle, attr: { \'title\': _jsonConnectionTitle }"></div> <div class="dx-field-value" data-bind="dxTextBox: { value: _connectionName }, dxValidator: { validationRules: $data._connectionNameValidationRules || [] }"></div> </div> <div class="dx-field"> <div class="dx-field-label" data-bind="text: _jsonSourceTitle, attr: { \'title\': _jsonSourceTitle }"></div> <div class="dx-field-value" data-bind="dxLocalizedSelectBox: { dataSource: _sources, value: _selectedSource, valueExpr:\'value\', displayExpr: \'displayValue\', displayCustomValue: true, dropDownOptions: { container: $root.getPopupContainer($element) } }"></div> </div> \x3c!-- ko with: _selectedSource --\x3e <div data-bind="dxValidationGroup: $data.validationGroup || {}"> <div data-bind="css: $data.cssClass"> \x3c!-- ko template: { name: \'dx-propertieseditor\', data: grid } --\x3e \x3c!-- /ko --\x3e </div> <div class="dxrd-wizard-validationsummary" data-bind="dxValidationSummary: $data.validationSummary || {}, visible: $data.validationSummary && !isValid()"></div> </div> \x3c!-- /ko --\x3e <div class="dxrd-wizard-validationsummary" data-bind="dxValidationSummary: $data._validationSummary || {}, visible: $data._validationSummary && _selectedSource().validationSummary && !canNext()"></div> </div> </div>',"dx-property-json-string-editor":'<div class="dx-field" data-bind="visible: visible"> \x3c!-- ko template: templateName --\x3e \x3c!-- /ko --\x3e </div>',"dxrd-page-dataSource":'<div class="dxrd-wizard-page"> \x3c!-- ko if: isDataSourceCreationAvailable --\x3e <div class="dxrd-wizard-datasourceoperation dxrd-radio-nowrap-ellipsis" data-bind="dxRadioGroup: { value: selectedDataSourceOperation, items: dataSourceOperations, layout: \'vertical\' }"> <div data-options="dxTemplate : { name: \'item\' }"> <div class="dxrd-radio-nowrap-ellipsis-text" data-bind="text: text, attr: { \'title\': text }"></div> </div> </div> \x3c!-- /ko --\x3e <div class="dxrd-wizard-availabledatasources dx-default-border-style dxd-border-secondary"> <div class="dxrd-wizard-list" data-bind="dxList: { dataSource: availableDataSources, selectedItems: selectedDataSource, focusStateEnabled:false, editEnabled: true, height: dataSourcesListHeight, editConfig: { selectionEnabled: false }, selectionMode: \'single\', activeStateEnabled: false, disabled: createNewDataSource, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true }"> <div data-options="dxTemplate : { name: \'item\' }"> <div data-bind="text: name"></div> </div> </div> </div></div>',"dxrd-page-selectitems":"<div class=\"dxrd-wizard-page\"> \x3c!-- ko template: { name: 'dxrd-page-selectitems-radio-group', data: $data } --\x3e \x3c!-- /ko --\x3e \x3c!-- ko template: { name: 'dxrd-page-selectitems-list', data: $data } --\x3e \x3c!-- /ko --\x3e </div>","dxrd-page-selectitems-radio-group":'\x3c!-- ko if: canCreateNew --\x3e <div class="dxrd-wizard-datasourceoperation dxrd-radio-nowrap-ellipsis" data-bind="dxRadioGroup: { value: selectedOperation, items: operations, layout: \'vertical\' }"> <div data-options="dxTemplate : { name: \'item\' }"> <div class="dxrd-radio-nowrap-ellipsis-text" data-bind="text: text, attr: { \'title\': text }"></div> </div> </div> \x3c!-- /ko --\x3e',"dxrd-page-selectitems-list":'<div class="dxrd-wizard-availabledatasources dx-default-border-style dxd-border-secondary" data-bind="styleunit: { top: $data.canCreateNew() ? 65 : 0 }"> <div class="dxrd-wizard-list dxd-border-primary" data-bind="dxList: { dataSource: items, onSelectionChanged: function(e) { selectedItems(e.addedItems) }, selectedItems: selectedItems.peek(), hoverStateEnabled:false, editEnabled: true, height: \'100%\', editConfig: { selectionEnabled: false }, searchExpr: \'name\', searchEnabled: true, selectionMode: \'single\', activeStateEnabled: false, disabled: _createNew, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true, searchEditorOptions: { placeholder: $root.dx._static.searchPlaceholder() } }"> <div data-options="dxTemplate : { name: \'item\' }" data-bind="event: { dblclick: function(e){ $parent.onDblClick() } }"> <div data-bind="text: $parent._displayExpr($data)"></div> </div> </div> </div>',"dxrd-page-specify-connection":"<div class=\"dxrd-wizard-page\"> \x3c!-- ko template: { name: 'dxrd-page-selectitems-radio-group', data: $data } --\x3e \x3c!-- /ko --\x3e \x3c!-- ko if: !_createNew() --\x3e \x3c!-- ko template: { name: 'dxrd-page-selectitems-list', data: $data } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko if: _createNew --\x3e <div class=\"dxrd-page-specify-connection_createNew\"> \x3c!-- ko template: { name: 'dxrd-page-jsonsource', data: _specifySourceData } --\x3e \x3c!-- /ko --\x3e </div> \x3c!-- /ko --\x3e</div>","dx-jsonwizard-parametercollection":'<div class="dx-field"> \x3c!-- ko with: value --\x3e <div class="dx-jsonwizard-parameter-left-container"> <div class="dx-jsonwizard-parameter"> <div data-bind="dxLocalizedSelectBox: { dataSource: _parameterTypes, value: itemType, valueExpr:\'value\', displayExpr: \'displayValue\', displayCustomValue: true, dropDownOptions: { container: $root.getPopupContainer($element) } }"></div> </div> </div> <div class="dx-jsonwizard-parameter-middle-container"> <div data-bind="dxTextBox: { value: name, placeholder: $data.namePlaceholder() }, dxValidator: { validationRules: nameValidationRules || [] }"></div> </div> <div class="dx-jsonwizard-parameter-right-container"> <div class="dx-jsonwizard-parameter"> <div data-bind="dxTextBox: { value: _editingValue, placeholder: $data.valuePlaceholder() }, visible: !$data.isExpression()"></div> <div data-bind="dxExpressionEditor: { options: _expression, fieldListProvider: itemsProvider, displayNameProvider: $root.displayNameProvider && $root.displayNameProvider() }, visible: $data.isExpression"></div> </div> </div> \x3c!-- ko template: { name: \'dx-wizard-menu-box-editorswitch\', data: $data } --\x3e \x3c!-- /ko  --\x3e \x3c!-- /ko --\x3e </div>',"dx-jsonwizard-loadfile-editor":"<div data-bind=\"dxFileImagePicker: { value: value, placeholderId: 'File', accept:'.json,.txt' }\"></div>","dx-jsonwizard-jsonstring-editor":'\x3c!-- ko if: !aceAvailable --\x3e <div class="dxrd-jsonwizard-jsonstring-editor dxd-border-secondary dxd-back-primary2" data-bind="dxTextArea: { value: value, spellcheck: false, isValid: isValid }, dxValidator: $data.validator || { validationRules: jsonStringValidationRules || [] }"></div> \x3c!-- /ko --\x3e \x3c!-- ko if: aceAvailable --\x3e <div class="dx-texteditor dx-editor-outlined dxrd-jsonwizard-jsonstring-editor dxd-wizard-jsoneditor dxd-border-secondary dxd-back-primary2" data-bind="dxAceEditor: { value: value, editorContainer: editorContainer, options: aceOptions, additionalOptions: additionalOptions }, css: { \'dx-invalid\' : !value() || !isValid() }"></div> \x3c!-- /ko --\x3e <div class="dxd-upload-file"> <div class="dxd-back-primary2"></div> <div data-bind="dxButtonWithTemplate: { onClick: uploadFile, hint: $data.getUploadTitle(), icon: \'dxrd-svg-wizard-Download\' }"></div> </div>',"dxrd-wizard-add-queries-page":'<div class="dxrd-wizard-page dxrd-wizard-add-queries-page dxrd-wizard-page-treelist-accordion-style"> <div data-bind="treeListSearchPanel: {controllers: ko.unwrap(_fieldListModel).treeListController}"></div> <div class="dxrd-wizard-dataMember dxd-border-secondary" data-bind="dxScrollView: { showScrollbar: \'onHover\', height: _scrollViewHeight }"> <div data-bind="treelist: _fieldListModel" class="dxrd-width-100 dxrd-height-100"></div> </div> \x3c!-- ko ifnot: $data.disableCustomSql --\x3e \x3c!-- ko template: { name: \'dxqb-popup-selectStatment\', data: _popupSelectStatement } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko template: { name: \'dxrd-querybuilder-popup\', data: _popupQueryBuilder } --\x3e \x3c!-- /ko --\x3e <div class="dxrd-wizard-load-panel dxd-text-primary" data-bind="dxLoadPanel: _loadPanelViewModel($element)"> </div> </div>',"dxd-custom-query-treelisitem":'<div data-bind="visible: visible"> <div class="dx-background-inheritor dxd-back-highlighted dxd-state-selected"> <div class="dx-treelist-item dx-fontsize-reestablished dxd-list-item-back-color" data-bind=" styleunit: padding, css: { \'dx-treelist-item-selected dxd-state-selected dxd-back-secondary\': isSelected }"> <div class="dx-treelist-collapsedbutton" data-bind="css: nodeImageClass"></div> <div class="dx-treelist-caption"> \x3c!-- ko if: actions && actions.length > 0 --\x3e <div class="dx-treelist-action-container" data-bind="visible: isSelected || isHovered"> \x3c!-- ko foreach: actions --\x3e \x3c!-- ko if: templateName --\x3e \x3c!-- ko template: templateName  --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko if: !templateName --\x3e <div class="dx-treelist-action" data-bind="dxButtonWithTemplate: { onClick: function() { clickAction($parent); }, icon: imageTemplateName, iconClass: imageClassName, disabled: ko.unwrap(disabled) }, attr: { title: text }"></div> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> \x3c!-- /ko  --\x3e <div class="dx-treelist-selectedcontent" data-bind="click: toggleSelected"> <div class="dx-treelist-text-wrapper"> <div class="dx-add-queries-page-checkbox" data-bind="dxCheckBox: { value: data.checked }, click: function(treeNode, e) { treeNode.data.toggleChecked(); e.stopPropagation(); return true; }"></div> <div class="dx-treelist-text dx-treelist-text-with-checkbox"> <div class="dx-queryname-textbox" data-bind="dxTextBox: { value: queryName, onValueChanged: queryNameHasChanged }"></div> </div> </div> </div> </div> </div> </div> </div>',"dxrd-configure-query-parameters-page":'<div class="dxrd-wizard-page dxrd-configure-query-parameters-page dxrd-wizard-page-treelist-accordion-style"> <div class="dxrd-wizard-dataMember dxd-border-secondary" data-bind="dxScrollView: { showScrollbar: \'onHover\', height: _scrollViewHeight }"> \x3c!-- ko if: !!$data._fieldListModel() --\x3e <div data-bind="treelist: _fieldListModel" class="dxrd-width-100 dxrd-height-100"></div> \x3c!-- /ko --\x3e </div> </div>',"dxrd-wizard-configure-relations-page":"<div class=\"dxrd-wizard-page dxrd-wizard-configure-relations-page\"> \x3c!-- ko if: $data._relationsEditor() --\x3e \x3c!-- ko template: { name: 'dxrd-masterDetail-editor-complete-wizard', data: $data._relationsEditor }--\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div>","dxrd-masterDetail-editor-complete-wizard":"<div class=\"dx-filtereditor dxrd-masterDetail-editor-complete-wizard dxd-border-secondary\"> \x3c!-- ko if: $data --\x3e <div class=\"dx-filtereditor-tree dxd-border-secondary dxrd-cursor-default\" data-bind=\"dxScrollView: { showScrollbar: 'onHover', direction: 'both' }\" > \x3c!-- ko foreach: masterQueries --\x3e <div class=\"criteria-operator-group\"> <div class=\"criteria-operator-group-item\"> <div class=\"criteria-operator-text dxd-filter-editor-text-color criteria-operator-item-group dxd-filter-editor-group-back-color stylized\" data-bind=\"text: queryName\"></div> <div data-bind=\"service: { name: 'createRelation' }\"></div> </div> <div class=\"criteria-operator-content\"> \x3c!-- ko template: { name: 'dx-masterDetail-editor-relation', foreach: relations }--\x3e \x3c!-- /ko --\x3e </div> </div> \x3c!-- /ko --\x3e </div> <div class=\"dx-selectbox-popup-wrapper dx-dropdownlist-popup-wrapper dx-filtereditor-criteriaoperator-popup dx-dropdowneditor-overlay\" data-bind=\"dxPopupWithAutoHeight: { height: '235px', focusStateEnabled: false, position: $root.rtl ? { my: 'right top', at: 'right bottom', of: popupService.target, collision: 'flipfit' } : { my: 'left top', at: 'left bottom', of: popupService.target, collision: 'flipfit' }, wrapperAttr: { class: 'dx-selectbox-popup-wrapper dx-dropdownlist-popup-wrapper dx-filtereditor-criteriaoperator-popup dx-dropdowneditor-overlay' }, container: $root.getPopupContainer($element), target: popupService.target, showTitle: false, showCloseButton: false, animation: {}, hideOnOutsideClick: true, shading: false, minWidth:'170px', maxWidth:'500px', width: 'auto', visible: popupService.visible }\"> \x3c!-- ko with: popupService--\x3e \x3c!-- ko with: data --\x3e \x3c!-- ko template: template--\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> \x3c!-- /ko --\x3e </div>","dxqb-popup-selectStatment":'<div data-bind="dxPopup: { animation: { show: { type: \'fade\', from: 0, to: 1, duration: 700 }, hide: { type: \'fade\', from: 1, to: 0, duration: 700 } }, wrapperAttr: { class: \'dxqb-preview\' }, visible: isVisible, title: title(), showTitle: true, resizeEnabled: true, shading: true, shadingColor: \'transparent\', fullScreen: false, width: 800, height: 544, container: closest($element, \'.dxrd-wizard\'), position: { of: closest($element, \'.dx-designer-viewport\') }, onHidden: function() { $data.data(null) }, focusStateEnabled: false }"> <div class="dxqb-preview-popup-content"> <div class="dxqb-show-query-string-content dx-widget"> \x3c!-- ko if: !aceAvailable --\x3e <div class="dxrd-show-query-string-editor" data-bind="dxTextArea: { value: data, valueChangeEvent: \'keyup\', disabled: false }"></div> \x3c!-- /ko --\x3e \x3c!-- ko if: aceAvailable --\x3e <div class="dxrd-show-query-string-editor"> <div class="dxrd-show-query-string-editor-content"> <div class="dx-sql_editor dxd-back-primary2" data-bind="dxAceEditor: { value: data, additionalOptions: additionalOptions, options: aceOptions }"></div> </div> </div> \x3c!-- /ko --\x3e </div> </div> <div class="dxqb-preview-popup-buttons dxd-border-secondary"> <div data-bind="dxButton: { text: okButtonText(), onClick: okButtonHandler }" class="dxqb-preview-popup-button"></div> </div> </div>',"dxrd-treelist-with-checkbox":'\x3c!-- ko if: visible --\x3e \x3c!-- ko if: hasContent --\x3e \x3c!-- ko template: "dx-treelist-accordion-item-with-checkbox" --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko ifnot: hasContent --\x3e \x3c!-- ko template: "dx-treelist-header-item-with-checkbox" --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e',"dx-treelist-accordion-item-with-checkbox":'<div data-bind="dxdAccordionExt: { collapsed: collapsed, lazyContentRendering: true, setCollapsedChangedEvent: setCollapsedChangedEvent }"> \x3c!-- ko template: "dx-treelist-header-item-with-checkbox" --\x3e \x3c!-- /ko --\x3e <div class="dx-fieldset dx-accordion-content dxd-back-primary"> \x3c!-- ko with: data --\x3e \x3c!-- ko template: { name: contenttemplate } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> </div>',"dx-treelist-header-item-with-checkbox":'<div class="dx-background-inheritor dxd-back-highlighted dxd-state-selected"> <div data-bind="event: events, attr: { class: \'dx-treeview-item dx-treelist-item dx-fontsize-reestablished dxd-list-item-back-color \' + (hasItems ? \'dx-treelist-list-item-content \' : \'dx-treelist-field-item-content \') + (isSelected ? \'dx-treelist-item-selected dxd-state-selected dxd-back-secondary\' : \'\') }, styleunit: padding, css: { \'dx-state-hover\': isHovered }"> \x3c!-- ko ifnot: hasItems--\x3e \x3c!-- ko if: showIcon --\x3e <div class="dx-treelist-image" data-bind="css: imageClassName, template: { name: imageTemplateName, if: !!imageTemplateName  }, attr: { title: text }"> </div> \x3c!-- /ko --\x3e \x3c!-- ko ifnot: showIcon --\x3e <div class="dx-treelist-collapsedbutton" data-bind="css: nodeImageClass"></div> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko if: hasItems --\x3e <div class="dx-treelist-collapsedbutton" data-bind="css: nodeImageClass, visible: hasItems, template: \'dxrd-svg-collapsed\', click: toggleCollapsed"></div> \x3c!-- /ko --\x3e <div class="dx-treelist-caption"> \x3c!-- ko if: actions && actions.length > 0 --\x3e <div class="dx-treelist-action-container" data-bind="visible: isSelected || isHovered"> \x3c!-- ko foreach: actions --\x3e \x3c!-- ko if: templateName --\x3e \x3c!-- ko template: templateName  --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko if: !templateName --\x3e <div class="dx-treelist-action" data-bind="dxButtonWithTemplate: { onClick: function() { clickAction($parent); }, icon: imageTemplateName, iconClass: imageClassName, disabled: ko.unwrap(disabled) }, attr: { title: text }"></div> \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> \x3c!-- /ko  --\x3e <div class="dx-treelist-selectedcontent" data-bind="click: toggleSelected,  draggable: isDraggable ? dragDropHandler : null, css: { \'dxrd-disabled\': data.disabled }"> <div class="dx-treelist-text-wrapper"> <div class="dx-add-queries-page-checkbox" data-bind="dxCheckBox: { value: data.checked, disabled: data.selectionDisabled }, click: function(treeNode, e) { treeNode.data.toggleChecked(); e.stopPropagation(); return true; }"></div> \x3c!--ko if: searchModel.searchEnabled --\x3e <div class="dx-treelist-text dx-highlighted-search-text dx-treelist-text-with-checkbox" data-bind="searchHighlighting: { text: text, textToSearch: searchModel.textToSearch, searchOptions: searchModel.searchOptions }, attr: { title: text }"></div> \x3c!-- /ko  --\x3e \x3c!--ko ifnot: searchModel.searchEnabled --\x3e <div class="dx-treelist-text dx-treelist-text-with-checkbox" data-bind="text: text, attr: { title: text }"></div> \x3c!-- /ko  --\x3e </div> </div> </div> </div></div>',"dx-treelist-action-with-popover":"<div class=\"dx-treelist-action-with-popover dxrd-display-inline-block\" > <div class=\"dx-treelist-action\" data-bind=\"dxButtonWithTemplate: { onClick: function() { clickAction($parent); }, icon: imageTemplateName, iconClass: imageClassName , disabled: ko.unwrap(disabled) }, attr: { title: text }\"></div> \x3c!-- ko template: { name: 'dx-popover-list-items', data: ko.utils.extend($parent.data, { position: $root.rtl ? 'left' : 'right', popupContainer: $root.getPopupContainer }) }--\x3e \x3c!-- /ko  --\x3e </div>","dx-treelist-accordion-contenttemplate-custom-with-actions":'<div data-bind="dxPropertyGrid: { target: $parent.data.dataSourceParameter, level: $parent.data.editor.level + 1 }"></div>',"dxrd-page-objectdatasource-datamembers":'<div class="dx-objectdatasource-datamembers-page"> <div data-bind="dxList: { dataSource: _chooseObjectDataMember.dataMembers, selectedItems: _chooseObjectDataMember.selectedDataMembers, selectionMode: \'single\', activeStateEnabled: false, focusStateEnabled: false, searchExpr: \'displayName\', searchEnabled: true, noDataText: $dx._static.noDataText(), encodeNoDataText: true, searchEditorOptions: { placeholder: $root.dx._static.searchPlaceholder() } }"> <div data-options="dxTemplate : { name: \'item\' }"> <div data-bind="text: $data.displayName"></div> </div> </div> </div>',"dxrd-page-objectdatasource-types":'<div class="dx-objectdatasource-types-page"> <div class="dxd-border-primary" data-bind="treeListSearchPanel: {controllers: _chooseObjectType.availableTypesTreelistModel.treeListController}"></div> <div data-bind="dxScrollView: { showScrollbar: \'onHover\', height: _chooseObjectType._scrollViewHeight}"> <div data-bind="treelist: _chooseObjectType.availableTypesTreelistModel"></div> \x3c!-- ko if: _chooseObjectType.types && !_chooseObjectType.types().length --\x3e <div class="dxa-no-data-message" data-bind="text: $root.dx._static.noDataText()"></div> \x3c!-- /ko--\x3e </div> </div>',"dxrd-page-objectsource":'<div class="dxrd-wizard-page dx-objectsource-page dx-frameless-style dxrd-position-absolute dxrd-top-45px dxrd-left-45px dxrd-right-45px dxrd-bottom-45px dxrd-margin-0" > \x3c!-- ko foreach: _sections--\x3e <div class="dx-border-inheritor dxd-border-accented"> <div class="dxrd-report-page-tile dxd-border-secondary" data-bind="style: position, css: { \'dxrd-disabled\': $data.disabled() }"> <div class="dxrd-report-page-tile-title" data-bind="text: $parent.showDescription($index(), description), attr: { title: $parent.showDescription($index(), description) }"></div> <div class="dxrd-report-page-tile-content dx-default-border-style dxd-border-secondary"> \x3c!-- ko if: !$data.disabled() --\x3e \x3c!-- ko template: { name: template, data: data } --\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e \x3c!-- ko if: $data.disabled() --\x3e <div class="dxrd-wizard-page dxrd-wizard-disabled-content" data-bind="text: disabledText"></div> \x3c!-- /ko --\x3e </div> </div> </div> \x3c!-- /ko --\x3e </div>',"dx-objectdatasource-types-section":'<div class="dx-objectdatasource-types-section"> <div data-bind="treeListSearchPanel: {controllers: availableTypesTreelistModel.treeListController}"></div> <div data-bind="dxScrollView: { showScrollbar: \'onHover\', height: _scrollViewHeight}"> <div data-bind="treelist: availableTypesTreelistModel"></div> </div> </div>',"dx-objectdatasource-datamembers-section":'<div class="dx-objectdatasource-parameters-datamembers-section"> <div class="dxd-border-primary" data-bind="dxList: { dataSource: dataMembers, selectedItems: selectedDataMembers, selectionMode: \'single\', activeStateEnabled: false, focusStateEnabled: false, searchExpr: \'displayName\', searchEnabled: true, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true, searchEditorOptions: { placeholder: $root.dx._static.searchPlaceholder() } }"> <div data-options="dxTemplate : { name: \'item\' }"> <div data-bind="text: $data.displayName"></div> </div> </div> </div>',"dx-objectdatasource-parameters-grid":'\x3c!-- ko if: !!$data --\x3e <div class="dx-field dxd-back-primary"> <div class="dx-field-label" data-bind="text: $data.displayName, attr: { \'title\': $data.displayName}"></div> </div> <div class="dxrd-properties-wrapper"> \x3c!-- ko foreach: _grids --\x3e \x3c!-- ko template: { name: \'dx-propertieseditor\', data: $data }--\x3e \x3c!-- /ko --\x3e \x3c!-- /ko --\x3e </div> \x3c!-- /ko --\x3e',"dx-objectdatasource-expression-popup":'\x3c!-- ko if: $data.value --\x3e <div data-bind="dxExpressionEditor: getOptions({ options: value, fieldListProvider: value().itemsProvider, displayNameProvider: $root.displayNameProvider && $root.displayNameProvider() })"></div> \x3c!-- /ko --\x3e',"dx-objectdatasource-parameters-section":"<div class=\"dx-objectdatasource-parameters-section\"> <div class=\"dx-fieldset\" data-bind=\"dxScrollView: { showScrollbar: 'onHover'}\"> \x3c!-- ko template: { name: 'dx-objectdatasource-parameters-grid', data: _ctorParametersObject }--\x3e \x3c!-- /ko --\x3e \x3c!-- ko template: { name: 'dx-objectdatasource-parameters-grid', data: _dataMemberParametersObject }--\x3e \x3c!-- /ko --\x3e </div> </div>","dx-objectdatasource-configureparameters-page":"<div class=\"dxrd-report-page-tile-content dx-default-border-style dxd-border-secondary\" data-bind=\"dxScrollView: { showScrollbar: 'onHover'}\"> \x3c!-- ko template: { name: 'dx-objectdatasource-parameters-section', data: _chooseObjectParameters } --\x3e \x3c!-- /ko  --\x3e </div>","dxrd-page-configure-parameters":'<div class="dxrd-wizard-page"> <div class="dxrd-datasource-parameters" data-bind="dxCollectionEditor: parametersEditorOptions"></div> </div>',"dxrd-parameter-collection-item":'<div class="dx-accordion" data-bind="dxdAccordion: $data"> <div class="dxrd-group-header dx-accordion-header dxd-text-primary dxd-border-primary dxrd-border-bottom-0"  data-bind="styleunit: { \'marginLeft\' : padding }, css: { \'dxrd-group-header-collapsed\': collapsed }"> <div class="dx-collapsing-image dxrd-display-inline-block" data-bind="template: \'dxrd-svg-collapsed\', css: { \'dx-image-expanded\': !collapsed }" ></div> <span class="dxrd-group-header-text dxd-text-primary" data-bind="text: value.name"></span> </div> <div class="dx-accordion-content dxd-border-primary"> <div data-bind="template: { name: \'dx-propertieseditor\', data: getProperties() }"></div> </div> </div>',"dxrd-wizard-datasource-parameters":'<div class="dx-fieldset dxrd-height-100" > <div class="dx-collectioneditor dxrd-height-100" > <div class="dxrd-datasource-parameters-collection"> <div class="dxrd-datasource-parameters-container dxd-border-secondary" data-bind="dxScrollView: { showScrollbar: \'onHover\', useNative: false, scrollByThumb: true  }"> \x3c!-- ko if: values.length === 0 --\x3e <div class="dx-collectioneditor-empty dxd-empty-area-placeholder-text-color dxd-text-info"> <span class="dxrd-datasource-parameters-empty-text" data-bind="text: emptyAreaText"></span> </div> \x3c!-- /ko --\x3e \x3c!-- ko if: values.length !== 0 --\x3e <div class="dx-collectioneditor-items" data-bind="foreach: values"> <div class="dx-background-inheritor dxd-back-highlighted dxd-state-selected"> <div class="dx-collectioneditor-item-container dx-fontsize-reestablished dxd-list-item-back-color" data-bind="dxclick: select, css: { \'dxd-state-selected dxd-back-secondary\' : selected }"> <div class="dx-collection-item"></div> </div> </div> </div> \x3c!-- /ko --\x3e </div> </div> <div class="dxrd-collectioneditor-wizard-buttons" data-bind="visible: showButtons"> <div class="dxrd-collectioneditor-action" data-bind="dxButton: { onClick: buttons.add.action, text: buttons.add.text }, attr: { title: buttons.add.text }"></div> <div class="dxrd-collectioneditor-action" data-bind="dxButton: { onClick: buttons.delete.action, disabled: selectedIndex === null, text: $parent.removeButtonTitle }, attr: { title: $parent.removeButtonTitle }"></div> </div> </div> </div>',"dxrd-page-connectionstring":'<div class="dxrd-wizard-page"> <div class="dx-wizard-connections dx-default-border-style dxd-border-secondary"> <div class="dxrd-wizard-list dxd-border-primary" data-bind="dxList: { dataSource: _connectionStrings, onSelectionChanged: function(e) { _selectedConnectionString(e.addedItems) },  selectedItems: _selectedConnectionString.peek(), focusStateEnabled: false, searchEnabled: true, searchExpr: \'description\', editEnabled: true, editConfig: { selectionEnabled: true }, selectionMode: \'single\', activeStateEnabled: false, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true, searchEditorOptions: { placeholder: $root.dx._static.searchPlaceholder() } }"> <div data-options="dxTemplate : { name: \'item\' }"> <div data-bind="text: $data[\'description\'] || $data[\'name\']"></div> </div> </div> </div> </div>',"dxrd-select-control":'<div class="dxrd-create-query-page-query-header"> <div class="dx-editquery-editor" data-bind="text: caption()"></div> <div class="dxd-hyperlink-color dxd-text-accented" data-bind="visible: !runQueryBuilderDisabled, text: $parent.runQueryBuilderBtnText, click: function() { $parent.runQueryBuilder() }"></div> </div> \x3c!-- ko if: !aceAvailable --\x3e <div class="dxrd-wizard-list dxrd-create-query-page-editor dx-default-border-style dxd-border-secondary dx-editquery-editor" data-bind="dxTextArea: { value: sqlString, valueChangeEvent: \'keyup input blur\', readOnly: disableCustomSql() }"></div> \x3c!-- /ko --\x3e \x3c!-- ko if: aceAvailable --\x3e <div class="dxrd-create-query-page-editor dxrd-create-query-page-editor-border dxd-border-secondary dx-editquery-editor"> <div class="dxrd-create-query-page-editor-content"> <div class="dx-sql_editor dxd-back-primary2" data-bind="dxAceEditor: { value: sqlString, additionalOptions: additionalOptions, options: aceOptions }, css: { \'dx-disabled-ace\': disableCustomSql() }"></div> </div> </div> \x3c!-- /ko --\x3e',"dxrd-procedures-control":'<div class="dx-editquery-editor dxrd-margin-top-20px" data-bind="text: caption()"></div> <div class="dx-editquery-editor dx-default-border-style dxd-border-secondary"> <div class="dxrd-wizard-list dxrd-create-query-page-editor" data-bind="dxList: { dataSource: storedProcedures, searchEnabled: true, searchExpr: \'name\', onContentReady: scrollActiveItem, selectedItems: selectedProcedure, editEnabled: true, editConfig: { selectionEnabled: true }, selectionMode: \'single\', activeStateEnabled: false, noDataText: $root.dx._static.noDataText(), encodeNoDataText: true }"> <div data-options="dxTemplate : { name: \'item\' }"> <div data-bind="text: $parent.generateStoredProcedureDisplayName($data)"></div> </div> </div> </div>',"dxrd-wizard-create-query-page":'<div class="dxrd-wizard-page dxrd-wizard-create-query-page"> <div class="dxrd-create-query-page-content"> <div data-bind="text: queryNameCaption()"></div> <div class="dx-editquery-editor" data-bind="dxTextBox: { value: queryName, placeholder: $data.placeholder() }"></div> <div class="dxrd-radio-nowrap-ellipsis" data-bind="dxRadioGroup: { value: selectedQueryType, layout: \'horizontal\', items: queryTypeItems }"> <div data-options="dxTemplate : { name: \'item\' }"> <div class="dxrd-radio-nowrap-ellipsis-text" data-bind="text: $parent.localizeQueryType($data), attr: { \'title\': $parent.localizeQueryType($data) }"></div> </div> </div> \x3c!-- ko template: { name: queryControl().template, data: queryControl() } --\x3e \x3c!-- /ko --\x3e </div> \x3c!-- ko template: { name: \'dxrd-querybuilder-popup\', data: popupQueryBuilder } --\x3e \x3c!-- /ko --\x3e </div>',"dxrd-querybuilder-popup":'<div data-bind="dxPopup: popupViewModel($element)"> \x3c!-- ko if: qbOptions --\x3e \x3c!-- ko if: isVisible --\x3e <div class="dxrd-querybuilder-popup-content"> <div class="dxrd-height-100" data-bind="dxQueryBuilder: qbOptions"></div> </div> \x3c!-- /ko --\x3e <div class="dxrd-querybuilder-popup-buttons dxd-border-secondary"> <div data-bind="dxButton: { text: getDisplayText(\'previewResults\'), onClick: previewHandler, disabled: okButtonDisabled }" class="dxrd-querybuilder-popup-button-left"></div> <div data-bind="dxButton: { text: getDisplayText(\'cancel\'), onClick: cancelHandler }" class="dxrd-querybuilder-popup-button"></div> <div data-bind="dxButton: { text: getDisplayText(\'ok\'), type: \'default\', onClick: okHandler, disabled: okButtonDisabled }" class="dxrd-querybuilder-popup-button"></div> </div> <div class="dxrd-wizard-load-panel dxd-text-primary" data-bind="dxLoadPanel:{ animation: { show: { type: \'fade\', from: 0, to: 1, duration: 700 }, hide: { type: \'fade\', from: 1, to: 0, duration: 700 } }, deferRendering: false, message: getDisplayText(\'loading\'), visible: showLoadIndicator, shading: true, shadingColor: \'transparent\'}"> </div> \x3c!-- /ko --\x3e </div>',"dxrd-querybuilder":'<div class="dx-designer dx-querybuilder" data-bind="template: \'dxrd-designer\'"> </div>',"dx-wizard-menu-box-editorswitch":"<div class=\"dxrd-editormenu-box dxd-property-grid-menu-box-color dxd-back-highlighted\" data-bind=\"css: { 'dxd-state-active': $data.isExpression() }, dxButtonWithTemplate: { onClick: switchEditors, icon: 'dxrd-svg-wizard-expressions', disabled: disabled }\"> </div>","dx-popover-list-items":'<div data-bind="dxPopover: { wrapperAttr: { class: $data.className }, width: $data.width || 200, position: position, visible: popoverVisible, target: target, container: popupContainer($element)}"> <div data-bind="attr: { class: $data.className + \'-list\'}, dxList: { dataSource: popoverListItems(), onItemClick: itemClickAction }"> <div data-bind="attr: { class: $data.className + \'-list-item\'}"  data-options="dxTemplate : { name: \'item\' }"> <div class="dx-text-content" data-bind="text: name, attr: { title: name }"></div> </div> </div> </div>',"dx-wizard-headerNew":'<div class="dxrd-wizard-header-custom"> \x3c!-- ko with: _currentPage --\x3e <div class="dxrd-span-title" data-bind="text: $data.title || $parent.title"></div> <div class="dxrd-span-description" data-bind="text: description, attr: { title: description }"></div> \x3c!-- /ko --\x3e </div>',"dx-wizard-newlayout":'<div class="dxrd-wizard dx-editors dxd-text-primary" data-bind="dxPopup: { animation: { show: { type: \'fadeIn\', duration: 700 }, hide: { type: \'fadeOut\', duration: 700 } }, visible: isVisible, wrapperAttr: { class: \'dxrd-wizard dx-editors dxd-text-primary\' }, title: isVisible() ? title : \'\', showTitle: true, fullScreen: false, width: width, height: height, container: $element, visualContainer: _container($element), titleTemplate: _titleTemplate, position: _wizardPopupPosition($element) }, cssArray: [ $data._extendCssClass, { \'dx-rtl\': $root.rtl, \'dx-ltr\': !$root.rtl }]"> <div class="dxrd-wizard-content"> \x3c!-- ko with: _currentPage --\x3e <div class="dxrd-height-100" data-bind="template: { name: template, data: page } "></div> \x3c!-- /ko --\x3e <div class="dxrd-wizard-load-panel dxd-text-primary" data-bind="dxLoadPanel: _loadPanelViewModel($element)"></div> </div> <div class="dxrd-wizard-navigation"> <div data-bind="dxButton: cancelButton" class="dxrd-wizard-btn left"></div> <div data-bind="dxButton: finishButton" class="dxrd-wizard-btn right"></div> <div data-bind="dxButton: nextButton" class="dxrd-wizard-btn right"></div> <div data-bind="dxButton: previousButton" class="dxrd-wizard-btn right"></div> </div> </div>'});class Eo extends f.DragDropHandler{_needToCreateRelation(){if(!(this.selection.dropTarget&&this.selection.dropTarget instanceof xs))return!1;return this.selection.dropTarget.getControlModel().parentModel()!==this.getDragColumn().parentModel()}constructor(e,t,a,i,r){super(e,t,a,i,r),this.querySurface=e,this.undoEngine=a,this._dragColumn=P.observable(null),this._dragConditionSurface=null,this.dragDropConnector=P.observable(null),this.cursor="arrow",this.containment=".dxrd-ghost-container",this.helper=void 0}startDrag(e){if(this._scrollProcessor=new f.dxScrollProcessor(document.getElementsByClassName("dxqb-mainwin")[0]),e instanceof q){const t=e.getControlModel().parentModel();if(this._dragConditionSurface=(0,f.findSurface)(t),!this._dragConditionSurface)return;this._dragConditionSurface.isVisible(!1),this._dragColumn(t.startPoint()===e.getControlModel()?t.nestedColumn():t.parentColumn())}else{if(!(e instanceof xs))throw new Error("ColumnDragHandler can be applied to the Column only.");this._dragColumn(e.getControlModel())}const t=new K({},this.querySurface().getControlModel());this.dragDropConnector(new Z(t,this.surface()))}setConnectorPoints(e){const t=this._dragColumn(),a=this.dragDropConnector().getControlModel();if(this._needToCreateRelation()){const e=I(t,this.selection.dropTarget.getControlModel());a.startPoint().connectingPoint(e.start),a.endPoint().connectingPoint(e.end)}else{const i=this.querySurface().rtl()?this.querySurface().pageWidth()-t.leftConnectionPoint.location.x():t.leftConnectionPoint.location.x(),r=this.querySurface().rtl()?this.querySurface().pageWidth()-t.rightConnectionPoint.location.x():t.rightConnectionPoint.location.x(),s=Math.abs(i-e.left)>Math.abs(r-e.left)?t.rightConnectionPoint:t.leftConnectionPoint;a.startPoint().connectingPoint(s),this.dragDropConnector().endPoint().rect({top:e.top,left:e.left})}}drag(e,t){const a=this._scrollProcessor.getScrollOffset();t.dataset.leftPosition=(0,f.convertToCssPixelUnits)((0,f.convertFromCssPixelUnits)(t.dataset.leftPosition)+(t.scroll.left+a.left)),t.dataset.topPosition=(0,f.convertToCssPixelUnits)((0,f.convertFromCssPixelUnits)(t.dataset.topPosition)+(t.scroll.top+a.top)),t.delta.left=(0,f.convertFromCssPixelUnits)(t.dataset.leftPosition)-this.surface().underCursor().x-6,t.delta.top=(0,f.convertFromCssPixelUnits)(t.dataset.topPosition)-this.surface().underCursor().y-6,this.setConnectorPoints(this._getAbsoluteSurfacePosition(t)),this._scrollProcessor.processOffset({x:e.clientX,y:e.clientY})}doStopDrag(){this._scrollProcessor.dispose(),this._scrollProcessor=null,this.dragHelperContent.reset(),this.dragDropConnector()&&this.dragDropConnector().dispose(),this.dragDropConnector(null);try{let e=null;this._dragConditionSurface&&!this._dragConditionSurface.isVisible()&&(e=this._dragConditionSurface.getControlModel(),this._dragConditionSurface.isVisible(!0));const t=this.querySurface().getControlModel();if(this._needToCreateRelation()){const a=this.selection.dropTarget.getControlModel();this.undoEngine().start(),e&&e.parentModel().removeChild(e);const i=t.cerateJoinCondition(this._dragColumn(),a);this.undoEngine().end(),null!==i&&this.selection.initialize((0,f.findSurface)(i))}}finally{this._dragColumn(null)}}getDragColumn(){return this._dragColumn()}}class Io extends f.DragDropHandler{suggestLocation(e,t){const a=Math.max.apply(null,t.tables.peek().filter((t=>t!==e)).map((e=>e.location.x.peek()+3*e.size.width.peek()/2)).concat([30]));e.location.x(a),e.location.y(65)}constructor(e,t,a,i,r){super(e,t,a,i,r),this._undoEngine=a,this._query=()=>this._querySurface().getControlModel(),this.getDropCallback=(e,t)=>(a,i)=>{const r=i.createChild((0,f.extend)({"@ControlType":"Table","@Name":a.data.name},Gt.controlsMap.Table.defaultVal));return r.isInitialized()?i.tryToCreateRelationsByFK(r):r.isInitialized.subscribe((()=>{e().start(),i.tryToCreateRelationsByFK(r),e().end()})),t&&this.suggestLocation(r,i),r},this._querySurface=e,this.cursor="arrow",this.containment=".dxqb-designer",this.cursorAt={top:0,left:0},this.helper=e=>{super.helper(e),this.recalculateSize(b.Size.fromString("199, 123")),r.setContent(new b.Rectangle(0,0,this._size.width(),this._size.height()))}}startDrag(e){e&&e.name&&this._query().dbSchemaProvider.getDbTable(e.name)}doStopDrag(e,t){if(this.dragHelperContent.reset(),this.selection.dropTarget){const t=this._getAbsoluteSurfacePosition(e);this._querySurface().underCursor().x=t.left-this._querySurface().absolutePosition.x(),this._querySurface().underCursor().y=t.top-this._querySurface().absolutePosition.y();const a=P.dataFor(e),i=this.getDropCallback(this._undoEngine,!1)(a,this._query());this.addControl(i,this._querySurface(),this._size)}}addControl(e,t,a){t.getControlModel().addChild(e);const i=(0,f.findSurface)(e);i&&(i.rect({left:t.underCursor().x,top:t.underCursor().y,width:a.width()}),this.selection.initialize(i))}}class Ao extends b.SurfaceElementBase{constructor(e,t){super(e,t,null),this.conditions=P.observableArray(),this.template="dxqb-relation",(0,f.createObservableArrayMapCollection)(e.conditions,this.conditions,this._createSurface)}_getChildrenHolderName(){return"conditions"}}const zo={Equal:"Equal",NotEqual:"NotEqual",Greater:"Greater",GreaterOrEqual:"GreaterOrEqual",Less:"Less",LessOrEqual:"LessOrEqual"},$o=[{propertyName:"left",displayName:"Left",editor:It.getEditor("text"),disabled:!0,localizationId:"AnalyticsCoreStringId.QueryBuilder_LeftOperand"},{propertyName:"right",displayName:"Right",editor:It.getEditor("text"),disabled:!0,localizationId:"AnalyticsCoreStringId.QueryBuilder_RightOperand"},{propertyName:"parentColumnName",modelName:"@Parent"},{propertyName:"nestedColumnName",modelName:"@Nested"},{propertyName:"joinType",displayName:"Join Type",editor:It.getEditor("combobox"),defaultVal:"Inner",valuesArray:[{value:"Inner",displayValue:"Inner join",localizationId:"DataAccessStringId.RelationEditorRelationTypeInnerJoin"},{value:"LeftOuter",displayValue:"Left outer join",localizationId:"DataAccessStringId.RelationEditorRelationTypeLeftOuterJoin"},{value:"RightOuter",displayValue:"Right outer join",localizationId:"DataAccessStringId.RelationEditorRelationTypeRightOuterJoin"},{value:"FullOuter",displayValue:"Full outer join",localizationId:"DataAccessStringId.RelationEditorRelationTypeFullOuterJoin"}],localizationId:"AnalyticsCoreStringId.QueryBuilder_JoinType"},{propertyName:"operator",modelName:"@Operator",displayName:"Operator",editor:It.getEditor("combobox"),defaultVal:zo.Equal,valuesArray:[{value:"Equal",displayValue:"Equals to",localizationId:"DataAccessUIStringId.JoinEditorEqualOperator"},{value:"NotEqual",displayValue:"Does not equal to",localizationId:"DataAccessUIStringId.JoinEditorNotEqualOperator"},{value:"Greater",displayValue:"Is greater than",localizationId:"DataAccessUIStringId.JoinEditorGreaterOperator"},{value:"GreaterOrEqual",displayValue:"Is greater than or equal to",localizationId:"DataAccessUIStringId.JoinEditorGreaterOrEqualOperator"},{value:"Less",displayValue:"Is less than",localizationId:"DataAccessUIStringId.JoinEditorLessOperator"},{value:"LessOrEqual",displayValue:"Is less than or equal to",localizationId:"DataAccessUIStringId.JoinEditorLessOrEqualOperator"}],localizationId:"AnalyticsCoreStringId.QueryBuilder_Operator"},{propertyName:"itemType",modelName:"@itemType"}];class Oo extends Z{constructor(e,t){super(e,t),this._disposables.push(this.showArrow=P.pureComputed((()=>"LeftOuter"===e.joinType()||"FullOuter"===e.joinType()))),this._disposables.push(this.showRightArrow=P.pureComputed((()=>"RightOuter"===e.joinType()||"FullOuter"===e.joinType())))}container(){return this.getRoot()}}function Bo(){ee(),Gt.registerControl("Unknown",{info:Mt,type:b.ElementViewModel,nonToolboxItem:!0,surfaceType:b.SurfaceElementBase,isDeleteDeny:!0}),Gt.registerControl("Relation",{info:Kt,defaultVal:{},surfaceType:Ao,popularProperties:[],type:Zt,elementActionsTypes:[],nonToolboxItem:!0}),Gt.registerControl("JoinCondition",{info:$o,defaultVal:{},surfaceType:Oo,popularProperties:["_parentColumnName","_nestedColumnName","joinType"],type:Yt,elementActionsTypes:[],nonToolboxItem:!0}),Gt.registerControl("Table",{info:ls,defaultVal:{},surfaceType:bs,popularProperties:["name","alias","columns"],type:cs,elementActionsTypes:[],nonToolboxItem:!0}),Gt.registerControl("Column",{info:rr,defaultVal:{},surfaceType:xs,popularProperties:["name","alias","selected"],type:ds,elementActionsTypes:[],nonToolboxItem:!0,isDeleteDeny:!0}),Gt.registerControl("Query",{info:ns,surfaceType:hs,popularProperties:["name","filterString","groupFilterString"],type:ps,elementActionsTypes:[],isContainer:!0,nonToolboxItem:!0,isDeleteDeny:!0}),Gt.registerControl("FTable",{info:ls,defaultVal:{},surfaceType:Ps,popularProperties:["name","alias","columns"],type:Ts,elementActionsTypes:[],nonToolboxItem:!0}),Gt.registerControl("FQuery",{info:_s,surfaceType:Cs,popularProperties:["name"],type:vs,elementActionsTypes:[],isContainer:!0,nonToolboxItem:!0,isDeleteDeny:!0})}class Ro extends it.TreeListController{constructor(e,t,a){super(),this.undoEngine=e,this.query=t,this._dragDropHandler=a.getDropCallback(e,!0)}dblClickHandler(e){this.undoEngine().start(),this._dragDropHandler(e,this.query()),this.undoEngine().end()}}const Mo={propertyName:"parameters",displayName:"parameters",editor:{custom:"dxqb-parameterspanel"}},qo={propertyName:"selectedItem",displayName:"selectedItem",editor:{custom:"dxqb-propertygrid"}},Lo={propertyName:"fields",displayName:"fields",editor:{custom:"dxqb-fieldspanel"}},Qo={propertyName:"query",displayName:"query",editor:{custom:"dxqb-propertygrid"}};class Vo extends A.TabInfo{static _getSelectedItemPropertyName(e){let t="Selection Properties",a="AnalyticsCoreStringId.QueryBuilder_SelectionProperties";switch(e&&e.controlType){case"Query":case"FQuery":t="Query Properties",a="AnalyticsCoreStringId.QueryBuilder_QueryProperties";break;case"Table":case"FTable":t="Table Properties",a="AnalyticsCoreStringId.QueryBuilder_TableProperties";break;case"Column":t="Column Properties",a="AnalyticsCoreStringId.QueryBuilder_ColumnProperties";break;case"JoinCondition":t="Relation Properties",a="AnalyticsCoreStringId.QueryBuilder_RelationProperties"}return(0,A.getLocalization)(t,a)}static _createWrappedObject(e,t,a,i){const r=new x.ObjectProperties(e,null,1),s=P.computed((()=>e()&&e().parameters)),o=[Qo,qo,Lo],n={selectedItem:t,query:{editableObject:e,properties:r},fields:t,isPropertyVisible:a=>"selectedItem"!==a||t.editableObject()!==e()};return i&&(n.parameters={values:s,addHandler:()=>new es({"@Type":f.DotnetTypes.SystemString}),collapsed:!1,undoEngine:a,isVisibleButton:(e,t)=>"add"===t||"delete"===t,template:"#dxqb-collectioneditor-template",textEmptyArray:{text:"Click the Add button to create a parameter.",localizationId:"AnalyticsCoreStringId.QueryBuilder_PageConfigureParametersEmpty"}},o.push(Mo)),n.getInfo=()=>o,n}static _createGroups(e,t){const a={Query:{info:[Qo],displayName:()=>(0,A.getLocalization)("Query Properties","AnalyticsCoreStringId.QueryBuilder_QueryProperties")},SelectedItem:{info:[qo],displayName:()=>this._getSelectedItemPropertyName(e())},Fields:{info:[Lo],displayName:()=>(0,A.getLocalization)("Available tables and views","AnalyticsCoreStringId.QueryBuilder_AvailableTables")}};return t&&(a.Parameters={info:[Mo],displayName:()=>(0,A.getLocalization)("Parameters","AnalyticsCoreStringId.QueryBuilder_Parameters")}),a}static _createQBPropertyGrid(e,t,a,i){const r=this._createWrappedObject(e,t,a,i);return new f.ControlProperties(P.observable(r),{groups:this._createGroups(t.editableObject,i),editors:r.getInfo()},void 0,!1)}_getGroupByName(e){return this.model.groups.filter((t=>t._displayName===e))[0]}constructor(e,t,a,i,r){super({text:"Properties",template:"dxqb-properties-wrapper",model:Vo._createQBPropertyGrid(e,t,a,r)}),this.active=!0,this._getGroupByName("Fields").collapsed(!1),this._disposables.push(i.subscribe((e=>{if(!(e instanceof ps)){this._getGroupByName("SelectedItem").collapsed(!1)}})))}}class Wo extends A.TabInfo{constructor(e){super({text:"Properties",template:"dxqb-properties-wrapper-editorlist",model:e}),this.model=e}}P.bindingHandlers.dxdTableView={init:function(e,t,a,i,r){const s=(0,x.getTemplate)("dxd-tableview");let o,n,d=ms.fn.constructor(e).append(s);const l=!!r.$root.rtl,c=P.unwrap(t());return P.applyBindings({onDataScroll:e=>{e.scrollOffset.left>=0&&(o||(o=d.find(".dxd-tableview-titles")),n||(n=d.find(".dxd-tableview-data table")),o.offset({left:n.offset().left,top:o.offset().top}))},onDataScrollInitialized:e=>{if(n=null,!l)return;const t=e.component;setTimeout((()=>t.scrollTo({left:t.scrollWidth(),top:0})),1)},data:c,rtl:l,noDataText:f.noDataText,isImage:e=>c.schema[e].type===f.DotnetTypes.SystemBiteArray,isImageTooLarge:e=>"!"===e[0],getImageTooLargeText:e=>"Image too large ("+e.substring(1)+" bytes)"},d.children()[0]),d.find(".dxd-tableview-titles .dxd-tableview-resizable").each(((e,t)=>{const a=ms.fn.constructor(t).find(".dxd-tableview-cell-text"),i=d.find(".dxd-tableview-data .dxd-tableview-resizable"+e);if(e<c.schema.length-1){const e=new f.Resizable(t,{handles:"e",start:(e,t)=>{},stop:()=>{},resize:(e,t)=>{const r=(0,f.convertFromCssPixelUnits)(t.dataset.originalLeftMousePosition),s=(0,f.convertFromCssPixelUnits)(t.dataset.originalWidth)+(e.x-r);t.style.width=(0,f.convertToCssPixelUnits)(s),a.outerWidth(s),i.outerWidth(s),i.parent().width(s)}}).initialize();(0,f.addDisposeCallback)(t,(()=>{e.dispose()}))}const r=Math.max(a.width(),i.width());a.width(r),i.width(r)})),(0,f.addDisposeCallback)(d.children()[0],(()=>{d=null})),{controlsDescendantBindings:!0}}};const Fo=DevExpress.config;var jo=e.n(Fo);const Uo={Surface:"dxrd-surface-template-base",Toolbar:"dxqb-toolbar",RightPanel:"dx-right-panel-lightweight",RightPanelSwitcher:"dx-right-panel-switcher",DataPreview:"dxqb-popup#data",SqlPreview:"dxqb-popup#sql"};function Ho(e,t,a,i){return()=>{const i=e(),r=i.find(".dxrd-right-panel:visible").outerWidth()||0,s=(i.get(0)?.clientWidth??i.width())-(r+5);i.find(".dxrd-surface-wrapper").css(a().rtl()?{left:r+"px",right:"0px",width:s+"px"}:{left:"0px",right:r+"px",width:s+"px"}),t(s),a().pageWidth(s)}}function Jo(e,t){const a=P.observable(!1);return e()._disposables.push(t.subscribe((()=>{a(!1)}))),P.pureComputed((function(){return t.peek().getDbSchema().done((()=>{a(!0)})),!a()||e().tables.peek().some((function(e){return!e.isInitialized()}))}))}function Go(e,t,a){const i=e.subscribe((e=>{t(new hs(e)),t().rtl(!!a.rtl)})),r=new f.SurfaceSelection(["alias","name","sortOrder"]),s=(0,f.createDesigner)(e,t,Gt,void 0,void 0,void 0,!!a.rtl,r);return s.addDisposables(i),s.findControl=(e,t)=>{Array.from((0,f.$dx)(".dxqb-main").children(".dxrd-control")).forEach((e=>{const a=(0,f.$dx)(e);a.offset().top<=t.clientY&&a.offset().left<=t.clientX&&s.selection.focused(P.dataFor(a[0]))}))},s.addDisposables({dispose:()=>s.findControl=null}),s}function Xo(e,t,a,i){const r=Ho((()=>(0,f.$dx)(e).find(".dxqb-designer")),t.surfaceSize,a);t.addDisposables(a.subscribe((()=>{r()})));const s=()=>{setTimeout((()=>r()))};window.addEventListener("resize",s),(0,f.addDisposeCallback)(e,(function(){window.removeEventListener("resize",s),t.disposableContainer.dispose()})),t.addDisposables({dispose:t.tabPanel.events.on("widthChanged",(e=>{r()}))}),t.updateSurfaceSize=()=>{r()},t.updateSurface=()=>{r(),i&&i()}}function Yo(e,t,a,i=!0){const r=a&&a.onServerError&&(0,f.processErrorEvent)(a.onServerError),s=t.requestWrapper||new fe,o=t.parametersMode||Gr.ReadWrite;if(ge(t.handlerUri),t.queryModelJson){const e=new vt(JSON.parse(t.dataSourceJson));t.dbSchemaProvider=(0,f._wrapModelInObservable)(t.dbSchemaProvider),t.dbSchemaProvider(new be(e.connection)),t.querySource=(0,f._wrapModelInObservable)(t.querySource),t.querySource(JSON.parse(t.queryModelJson))}const n=t.dbSchemaProvider().connection,d=P.observable(),l=P.observable(),c=P.observable(),u=e=>{d(new ps(e,t.dbSchemaProvider(),o,(e=>(e=>{a.saveQueryRequested({queryLayout:encodeURIComponent(JSON.stringify(e)),connection:encodeURIComponent(xe(n))})})(e))))};u(t.querySource());const p=Go(d,l,t);r&&p.addDisposables(r),p.rootStyle="dxqb-designer dxd-back-primary-invariant";const h=f.getParentContainer;p.dataPreview={isLoading:P.observable(!1),isVisible:P.observable(!1),title:()=>(0,A.getLocalization)("Data Preview (First 100 Rows Displayed)","AnalyticsCoreStringId.DataPreview_Title"),template:"dxqb-data-preview",data:{value:P.observable()},okButtonText:()=>(0,A.getLocalization)("OK","DataAccessUIStringId.Button_OK"),okButtonHandler:e=>{e.model.isVisible(!1)},container:h},p.selectStatmentPreview={isLoading:P.observable(!1),isVisible:P.observable(!1),template:"dxqb-selectstatment-preview",title:()=>(0,A.getLocalization)("Select Statement Preview","AnalyticsCoreStringId.QueryBuilder_SelectStatementPreview_Title"),data:{value:P.observable(),aceOptions:mr(!0),aceAvailable:(0,it.aceAvailable)(),additionalOptions:gr((e=>{p.selectStatmentPreview.data.value(e)})),languageHelper:{getLanguageMode:()=>"ace/mode/sql",createCompleters:()=>[]}},okButtonText:()=>(0,A.getLocalization)("OK","DataAccessUIStringId.Button_OK"),okButtonHandler:e=>{e.model.isVisible(!1)},container:h},p.parts=[{id:Uo.Surface,templateName:Uo.Surface,model:p},{id:Uo.Toolbar,templateName:Uo.Toolbar,model:p},{id:Uo.RightPanel,templateName:Uo.RightPanel,model:p},{id:Uo.DataPreview,templateName:Uo.DataPreview.split("#")[0],model:p.dataPreview},{id:Uo.SqlPreview,templateName:Uo.SqlPreview.split("#")[0],model:p.selectStatmentPreview}],p.columnDragHandler=new Eo(l,p.selection,p.undoEngine,p.snapHelper,p.dragHelperContent),p.fieldDragHandler=new Io(l,p.selection,p.undoEngine,p.snapHelper,p.dragHelperContent),p.connectionPointDragHandler=p.columnDragHandler,p.resizeHandler.handles="e,w",p.columnsLoadingMsg=()=>(0,A.getLocalization)("Loading...","DataAccessWebStringId.QueryBuilder_ColumnsLoading");const m=e=>{u(e),c({itemsProvider:t.dbSchemaProvider(),treeListController:new Ro(p.undoEngine,d,p.fieldDragHandler),selectedPath:P.observable(),pageSize:100,templateName:"dxqb-treelist-item-with-search"})};p.addDisposables(t.querySource.subscribe((e=>{m(e)}))),m(t.querySource());const g=P.observable((0,f.calculateWithZoomFactor)(355)),b={editableObject:p.editableObject,properties:new x.ObjectProperties(p.editableObject,null,1),fieldListModel:{treeListOptions:c},tablesTop:g,searchPlaceholder:()=>(0,f.searchPlaceholder)()},y=new Vo(d,b,p.undoEngine,p.selection.focused,o===Gr.ReadWrite),S=p.tabPanel;S.removeTabs(),S.addTab(y),S.width=(0,f.calculateWithZoomFactor)(375),p.fieldDragHandler=new Io(l,p.selection,p.undoEngine,p.snapHelper,p.dragHelperContent),p.fieldListProvider=new Yr(d,Yr.whereClauseObjectsFilter),p.dataBindingsProvider=p.fieldListProvider,p.parametersBindingsProvider=t.parametersItemsProvider||p.dataBindingsProvider,p.dataBindingsGroupProvider=new Yr(d,Yr.groupByObjectsFilter),p.isLoading=Jo(p.model,t.dbSchemaProvider),p.actionLists=new f.ActionLists(l,p.selection,p.undoEngine,function(e,t){const a=e.model;return i=>{const r=(0,f.findFirstItemMatchesCondition)(i,(e=>"Delete"===e.text));r.imageClassName="dx-icon-dxrd-image-recycle-bin",r.imageTemplateName="dxrd-svg-operations-recycle_bin_xl";const s=(0,f.findFirstItemMatchesCondition)(i,(e=>"Undo"===e.text));s.disabled=P.pureComputed((()=>e.isLoading()||!e.undoEngine().undoEnabled()));const o=(0,f.findFirstItemMatchesCondition)(i,(e=>"Redo"===e.text));i.splice(0,i.length,r,s,o),i.push({id:me.Save,text:"Save",displayText:()=>(0,A.getLocalization)("Save","AnalyticsCoreStringId.MenuButtons_Save"),imageClassName:"dxqb-image-save",imageTemplateName:"dxrd-svg-menu-save",disabled:e.isLoading,visible:!0,hotKey:{ctrlKey:!0,keyCode:"S".charCodeAt(0)},clickAction:()=>{a().save()},hasSeparator:!0}),i.push({id:me.DataPreview,text:"Preview Results",displayText:()=>(0,A.getLocalization)("Preview Results","DataAccessUIStringId.QueryBuilderButtons_PreviewResults"),imageClassName:"dxrd-image-data-preview",imageTemplateName:"dxrd-svg-queryBuilder-data_preview",disabled:e.isLoading,visible:!0,hotKey:{ctrlKey:!0,keyCode:"P".charCodeAt(0)},clickAction:()=>{a().canSave()&&e.showPreview()},hasSeparator:!0}),i.push({id:me.SelectStatementPreview,text:"Preview Select Statement",displayText:()=>(0,A.getLocalization)("Preview Select Statement","AnalyticsCoreStringId.QueryBuilder_PreviewSelectStatement_Tooltip"),imageClassName:"dxrd-image-selectstatement-preview",imageTemplateName:"dxrd-svg-queryBuilder-select_statment",disabled:e.isLoading,visible:!0,hotKey:{ctrlKey:!0,keyCode:"E".charCodeAt(0)},clickAction:()=>{a().canSave()&&e.showStatement()},hasSeparator:!0}),t&&t(i)}}(p,a&&a.customizeActions)),p.isLoading()||p.undoEngine&&p.undoEngine().clearHistory(),p.addDisposables(p.isLoading.subscribe((e=>{p.undoEngine&&p.undoEngine().clearHistory()}))),p.selection.focused(l());const _=l.subscribe((e=>{p.selection.focused(e)}));return p.addDisposables({dispose:()=>_.dispose()}),p.addDisposables(p.editableObject.subscribe((e=>{g.notifySubscribers(null)}))),(0,f.appendStaticContextToRootViewModel)(p),i&&(a&&a.beforeRender&&a.beforeRender(p),(0,f.$dx)(e).empty(),P.cleanNode(e),P.applyBindings(p,e)),Xo(e,p,l,(()=>g(355))),i&&p.updateSurface(),p.showPreview=()=>{p.dataPreview.isLoading(!0),p.dataPreview.isVisible(!0),s.getDataPreview(n,(0,A.PrepareRequestArgs)(d().serialize(!0))).done((e=>{p.dataPreview.data.value(JSON.parse(e.dataPreviewJSON)),p.dataPreview.isLoading(!1)})).fail((e=>{p.dataPreview.isVisible(!1),(0,f.ShowMessage)((0,f.getErrorMessage)(e))}))},p.showStatement=()=>{p.selectStatmentPreview.isLoading(!0),p.selectStatmentPreview.isVisible(!0),s.getSelectStatement(n,(0,A.PrepareRequestArgs)(d().serialize(!0))).done((e=>{e.errorMessage&&(0,f.ShowMessage)(e.errorMessage),p.selectStatmentPreview.data.value(e.sqlSelectStatement),p.selectStatmentPreview.isLoading(!1)})).fail((e=>{p.selectStatmentPreview.isVisible(!1),(0,f.ShowMessage)((0,f.getErrorMessage)(e))}))},p}function Ko(e,t,a,i=!0){t.localization&&(0,A.addCultureInfo)({messages:t.localization}),jo()({rtlEnabled:!!t.rtl}),Bo();const r=[];return a&&a.customizeLocalization&&a.customizeLocalization(r),(0,f.resolveFromPromises)(r,(()=>(0,f.troubleshootingPageWrapper)((()=>Yo(e,t,a,i)),t.developmentMode,e)))}function Zo(e,t,a){const i=P.observable(),r=P.observable();Bo();const s=Go(i,r,t),o=new Wo(new x.ObjectProperties(s.editableObject,null,1)),n=s.tabPanel,d=P.observable(n.collapsed);let l=!1;const c=e=>{l||(l=!0,e(),l=!1)};s.addDisposables({dispose:n.events.on("collapsedChanged",(e=>{c((()=>d(n.collapsed)))}))}),s.addDisposables(d.subscribe((e=>{c((()=>n.collapsed=e))})));const u=new Do(d,s.editableObject,t.showPropertyGridCondition,Vo._getSelectedItemPropertyName);return s.addDisposables(o,n,u),n.removeTabs(),n.addTab(o),n.width=(0,f.calculateWithZoomFactor)(325),s.parts=[{id:Uo.Surface,templateName:Uo.Surface,model:s},{id:Uo.RightPanel,templateName:Uo.RightPanel,model:s},{id:Uo.RightPanelSwitcher,templateName:Uo.RightPanelSwitcher,model:u}],s.columnDragHandler=new Eo(r,s.selection,s.undoEngine,s.snapHelper,s.dragHelperContent),s.connectionPointDragHandler=s.columnDragHandler,s.selection.focused(r()),s.addDisposables(r.subscribe((e=>{s.selection.focused(e)}))),s.rootStyle="dxqb-designer dxd-back-primary-invariant",s.resizeHandler.handles="e,w",s.columnsLoadingMsg=()=>(0,A.getLocalization)("Loading...","DataAccessWebStringId.QueryBuilder_ColumnsLoading"),(0,f.appendStaticContextToRootViewModel)(s),i(a(t)),(0,f.$dx)(e).empty(),P.cleanNode(e),P.applyBindings(s,e),Xo(e,s,r),s.isLoading(!1),s.updateSurface(),(0,f.addDisposeCallback)(e,(function(){i()&&i().dispose(),r()&&r().dispose(),a=null,s.disposableContainer.dispose(),s.dispose()})),s}class en{get queryBuilderModel(){return this._queryBuilderModel()}set queryBuilderModel(e){this._queryBuilderModel(e)}constructor(e){this._queryBuilderModel=e}UpdateLocalization(e){(0,A.updateLocalization)(e)}GetQueryBuilderModel(){return this.queryBuilderModel}GetJsonQueryModel(){return{Query:this.queryBuilderModel.model().serialize()}}GetSaveQueryModel(){return{queryLayout:(0,A.PrepareRequestArgs)(this.GetJsonQueryModel()),connection:this.SerializeDataConnection()}}SerializeDataConnection(){return xe(this.queryBuilderModel.model().dbSchemaProvider.connection)}AdjustControlCore(){this.queryBuilderModel&&this.queryBuilderModel.updateSurfaceSize()}Save(){this.queryBuilderModel&&this.queryBuilderModel.model().onSave()}ShowPreview(){this.queryBuilderModel&&this.queryBuilderModel.showPreview()}IsQueryValid(){return this.queryBuilderModel&&this.queryBuilderModel.model().isValid()}OnCallback(e){e.queryValidationError&&(0,f.NotifyAboutWarning)(e.queryValidationError,!0)}}const tn=DevExpress.localization,an=DevExpress.Analytics.Localization;class rn{static generateQueryBuilderEvents(e){const t={publicName:"BeforeRender",privateName:"beforeRender"},a={publicName:"SaveQueryRequested",privateName:"saveQueryRequested"},i={publicName:"CustomizeMenuActions",privateName:"customizeActions"},r={publicName:"CustomizeLocalization",privateName:"customizeLocalization"},s={publicName:"OnServerError",privateName:"onServerError"};return i.callback=function(t){e(i.publicName,{Actions:t,GetById:e=>e?t.filter((function(t){return e===t.id}))[0]:null})},r.callback=function(t){e(r.publicName,{LoadMessages:e=>{e&&("function"==typeof e.then?t.push(e):(0,an.loadMessages)(e))},WidgetLocalization:tn})},s.callback=function(t){e(s.publicName,{Error:t})},t.callback=function(a){e(t.publicName,a)},a.callback=function(t){e(a.publicName,t)},[t,a,i,r,s]}}class sn extends f.JSDesignerBindingCommon{_applyBindings(e,t){(0,f.troubleshootingPageWrapper)((()=>{this._disposables.push(e),t.children().remove();const a=t.append(this._templateHtml).children()[0];if(!a)return;P.cleanNode(a),this._callbacks&&this._callbacks.beforeRender&&this._callbacks.beforeRender(e),P.applyBindings(e,a);const i=setTimeout((()=>{e&&e.updateSurfaceSize()}),1);this._disposables.push({dispose:()=>clearTimeout(i)})}),this.developmentMode,t)}_initializeCallbacks(){if(this._options.callbacks){const e=rn.generateQueryBuilderEvents(((e,t)=>{this._fireEvent(e,t)}));this._checkCallbackName(e);return this._generateCallbackDictionary(e)}}_createModel(e){return this._callbacks=this._initializeCallbacks(),Ko(e,this._options,this._callbacks,!1)}constructor(e,t){super(P.unwrap(e),t),this.options=e,this._deferreds=[],this._templateHtml=(0,x.getTemplate)("dxrd-querybuilder"),e.queryBuilderModel=(0,f._wrapModelInObservable)(e.queryBuilderModel),this.sender=new en(e.queryBuilderModel),e.callbacks&&e.callbacks._eventSenderCreated&&e.callbacks._eventSenderCreated(this.sender)}dispose(){(this._deferreds||[]).forEach((e=>{e.reject()})),super.dispose()}applyBindings(e){const t=(0,f.$dx)(e),a=this._options.requestOptions,i=()=>{a&&a.invokeAction&&(this._options.handlerUri=this._getServerActionUrl(a.host,a.invokeAction)),this._deferreds.push(this._createModel(e).done((a=>{this.options.queryBuilderModel(a),this.sender.queryBuilderModel=a,this._createDisposeFunction(e),this._applyBindings(this.sender.queryBuilderModel,t)})))};a?this._getLocalizationRequest().done((e=>{e&&(0,A.addCultureInfo)(e)})).always((()=>{i()})):i()}}const on="dxQueryBuilder";class nn extends f.DxAnalyticsComponentCommon{getBindingName(){return on}}(0,it.registerBaseBinding)(on,"$data"),P.bindingHandlers[on]={init:function(e,t){const a=P.unwrap(t());return new sn(a||{}).applyBindings(e),{controlsDescendantBindings:!0}}},P.bindingHandlers.dxQueryBuilderSurface={init:function(e,t){const a=P.unwrap(t()),i=a.options,r=(0,x.getTemplate)("dxrd-querybuilder"),s=Zo((0,f.$dx)(e).append(r).children()[0],i,a.creator);return i.queryBuilderModel(s),{controlsDescendantBindings:!0}}};const dn=window.DevExpress||{};var ln;(ln=dn).Analytics=ln.Analytics||{},ln.QueryBuilder=g,ln.Analytics.Diagram=t,ln.Analytics.Data=a,ln.Analytics.Data.Internal=i,ln.Analytics.Data.Utils=r,ln.Analytics.Data.Metadata=s,ln.Analytics.Wizard=o,ln.Analytics.Wizard.Internal=n,ln.QueryBuilder.Widgets=d,ln.QueryBuilder.Widgets.Internal=l,ln.QueryBuilder.Metadata=c,ln.QueryBuilder.Utils=u,ln.QueryBuilder.Internal=p,ln.QueryBuilder.Elements=h,ln.QueryBuilder.Elements.Metadata=m;dn.QueryBuilder,dn.Analytics})();