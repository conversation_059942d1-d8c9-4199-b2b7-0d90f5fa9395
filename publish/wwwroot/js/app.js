// Add function for notification sounds
window.playNotificationSound = function(type) {
    const soundMap = {
        'success': 'success-sound',
        'error': 'error-sound',
        'warning': 'warning-sound',
        'info': 'info-sound'
    };
    
    const soundId = soundMap[type] || 'info-sound';
    const audioElement = document.getElementById(soundId);
    
    if (audioElement) {
        // Reset the audio to the beginning
        audioElement.currentTime = 0;
        
        // Play the sound
        audioElement.play().catch(error => {
            console.warn('Could not play notification sound:', error);
        });
    }
}; 