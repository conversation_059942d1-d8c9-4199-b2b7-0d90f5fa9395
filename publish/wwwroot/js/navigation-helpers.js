// Navigation helper functions

// Flag to track if beforeunload is registered
let beforeUnloadRegistered = false;

// Handler function for beforeunload event
function handleBeforeUnload(event) {
    // Cancel the event and show a confirmation dialog
    event.preventDefault();
    // Chrome requires returnValue to be set
    event.returnValue = '';
    return '';
}

// Register beforeunload event handler
window.registerBeforeUnload = function() {
    if (!beforeUnloadRegistered) {
        window.addEventListener('beforeunload', handleBeforeUnload);
        beforeUnloadRegistered = true;
        console.log('Registered beforeunload event handler');
    }
};

// Unregister beforeunload event handler
window.unregisterBeforeUnload = function() {
    if (beforeUnloadRegistered) {
        window.removeEventListener('beforeunload', handleBeforeUnload);
        beforeUnloadRegistered = false;
        console.log('Unregistered beforeunload event handler');
    }
};

// Reference to the .NET object for the import dialog
let importDialogDotNetRef = null;

// Register event listener for showing the import dialog
window.registerImportDialogEvent = function(dotNetRef) {
    importDialogDotNetRef = dotNetRef;

    // Remove any existing event listener to avoid duplicates
    document.removeEventListener('showImportDialog', handleShowImportDialog);

    // Add the event listener
    document.addEventListener('showImportDialog', handleShowImportDialog);
    console.log('Registered showImportDialog event handler');
};

// Handler function for showImportDialog event
function handleShowImportDialog(event) {
    if (importDialogDotNetRef) {
        importDialogDotNetRef.invokeMethodAsync('ShowImportDialogFromJS');
        console.log('Showing import dialog from JS event');
    } else {
        console.error('Import dialog .NET reference not set');
    }
}
