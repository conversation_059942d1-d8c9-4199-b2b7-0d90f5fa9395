// Vehicle Editor New JavaScript

// Store references to event handlers so they can be properly removed
let beforeUnloadHandler = null;
let importDialogHandler = null;
let dotNetRef = null;

// Register beforeunload event to prevent accidental navigation when there are unsaved changes
window.registerBeforeUnload = function () {
    // Remove any existing handler first to prevent duplicates
    if (beforeUnloadHandler) {
        window.removeEventListener('beforeunload', beforeUnloadHandler);
    }

    // Create and store the handler function
    beforeUnloadHandler = function (e) {
        // Check if there are unsaved changes
        const isDirty = document.querySelector('[data-is-dirty="true"]');
        if (isDirty) {
            // Standard way of showing a confirmation dialog before leaving the page
            e.preventDefault();
            e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
            return e.returnValue;
        }
    };

    // Add the event listener
    window.addEventListener('beforeunload', beforeUnloadHandler);
    console.log('Registered beforeunload event handler');
};

// Unregister beforeunload event
window.unregisterBeforeUnload = function () {
    if (beforeUnloadHandler) {
        window.removeEventListener('beforeunload', beforeUnloadHandler);
        beforeUnloadHandler = null;
        console.log('Unregistered beforeunload event handler');
    }
};

// Register event listener for showing the import dialog
window.registerImportDialogEvent = function (dotNetReference) {
    // Store the .NET reference
    dotNetRef = dotNetReference;

    // Remove any existing handler first to prevent duplicates
    if (importDialogHandler) {
        window.removeEventListener('showImportDialog', importDialogHandler);
    }

    // Create and store the handler function
    importDialogHandler = function () {
        dotNetRef.invokeMethodAsync('ShowImportDialog');
    };

    // Add the event listener
    window.addEventListener('showImportDialog', importDialogHandler);
    console.log('Registered showImportDialog event handler');
};

// Unregister import dialog event
window.unregisterImportDialogEvent = function () {
    if (importDialogHandler) {
        window.removeEventListener('showImportDialog', importDialogHandler);
        importDialogHandler = null;
        console.log('Unregistered showImportDialog event handler');
    }

    // Clear the .NET reference
    dotNetRef = null;
};

// Toggle sidebar for mobile view
window.toggleSidebar = function () {
    const sidebar = document.querySelector('.vehicle-editor-sidebar');
    if (sidebar) {
        sidebar.classList.toggle('-translate-x-full');

        // Add overlay when sidebar is open
        let overlay = document.getElementById('sidebar-overlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'sidebar-overlay';
            overlay.className = 'fixed inset-0 bg-black/20 z-10 md:hidden';
            overlay.onclick = function() {
                window.toggleSidebar();
            };
            document.body.appendChild(overlay);
        } else {
            overlay.remove();
        }
    }
};
