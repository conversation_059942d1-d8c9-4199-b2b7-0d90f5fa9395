// File download functions

// Download a file from a base64 string
window.downloadFileFromBase64 = function (fileName, base64Content, contentType) {
    // Create a data URL from the base64 string
    const dataUrl = `data:${contentType};base64,${base64Content}`;
    
    // Create a link element
    const link = document.createElement('a');
    link.href = dataUrl;
    link.download = fileName;
    
    // Append the link to the document
    document.body.appendChild(link);
    
    // Click the link to trigger the download
    link.click();
    
    // Remove the link from the document
    document.body.removeChild(link);
};
