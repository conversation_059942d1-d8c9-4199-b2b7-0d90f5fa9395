// Blazor Error UI Handler

window.blazorErrorUIHandler = {
    // Show the error UI
    show: function() {
        const errorUI = document.getElementById('blazor-error-ui');
        if (errorUI) {
            errorUI.style.display = 'flex';
            errorUI.classList.add('show');
        }
    },

    // Hide the error UI
    hide: function() {
        const errorUI = document.getElementById('blazor-error-ui');
        if (errorUI) {
            errorUI.style.display = 'none';
            errorUI.classList.remove('show');
        }
    }
};

// Initialize error UI handling
document.addEventListener('DOMContentLoaded', function() {
    // Get the error UI element
    const errorUI = document.getElementById('blazor-error-ui');

    if (errorUI) {
        // Ensure the error UI is hidden by default
        errorUI.style.display = 'none';

        // Add click handler for the dismiss button
        const dismissButton = errorUI.querySelector('.dismiss');
        if (dismissButton) {
            dismissButton.addEventListener('click', function(e) {
                e.preventDefault();
                window.blazorErrorUIHandler.hide();
            });
        }

        // Add click handler for the reload button
        const reloadButton = errorUI.querySelector('.reload');
        if (reloadButton) {
            reloadButton.addEventListener('click', function(e) {
                e.preventDefault();
                location.reload();
            });
        }
    }
});

// Listen for unhandled errors
window.addEventListener('error', function(e) {
    // Only show the error UI for actual errors, not for handled exceptions
    if (e && e.error && !e.defaultPrevented) {
        window.blazorErrorUIHandler.show();
    }
});

// Listen for unhandled promise rejections
window.addEventListener('unhandledrejection', function(e) {
    // Only show the error UI for actual errors, not for handled rejections
    if (e && e.reason && !e.defaultPrevented) {
        window.blazorErrorUIHandler.show();
    }
});

// Hide error UI when Blazor is ready
window.addEventListener('DOMContentLoaded', function() {
    setTimeout(function() {
        window.blazorErrorUIHandler.hide();
    }, 500);
});
