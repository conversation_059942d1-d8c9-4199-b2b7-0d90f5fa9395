/* _content/Barret.Web.Server/Pages/Index.razor.rz.scp.css */
.card[b-r3sy4jrcoj] {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 1px solid rgba(0,0,0,0.125);
}

.card:hover[b-r3sy4jrcoj] {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.hover-opacity:hover[b-r3sy4jrcoj] {
    opacity: 0.9;
}

.bg-opacity-10[b-r3sy4jrcoj] {
    background-color: rgba(var(--bs-primary-rgb), 0.1);
}

.card-body[b-r3sy4jrcoj] {
    display: flex;
    flex-direction: column;
    height: 100%;
}
/* _content/Barret.Web.Server/Shared/Components/DeviceManagers/DeviceManager.razor.rz.scp.css */
/* Table styling */
.table-responsive[b-5lwfux2qo2] {
    overflow-x: auto;
    max-width: 100%;
    margin-bottom: 1.5rem;
}

.table[b-5lwfux2qo2] {
    width: 100%;
    margin-bottom: 0;
    color: #212529;
    vertical-align: middle;
    border-color: #dee2e6;
}

.table > thead[b-5lwfux2qo2] {
    background-color: #f8f9fa;
}

.table > :not(:first-child)[b-5lwfux2qo2] {
    border-top: 2px solid currentColor;
}

.table > tbody > tr:hover[b-5lwfux2qo2] {
    background-color: rgba(0,0,0,.075);
}

/* Button styling */
.btn-group[b-5lwfux2qo2] {
    display: flex;
    gap: 0.25rem;
}

/* Modal dialog */
.modal[b-5lwfux2qo2] {
    display: block;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1050;
    width: 100%;
    height: 100%;
    overflow: hidden;
    outline: 0;
    background-color: rgba(0,0,0,0.5);
}

.modal-dialog[b-5lwfux2qo2] {
    position: relative;
    width: auto;
    margin: 1.75rem auto;
    max-width: 500px;
}

.modal-content[b-5lwfux2qo2] {
    position: relative;
    display: flex;
    flex-direction: column;
    width: 100%;
    pointer-events: auto;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid rgba(0,0,0,.2);
    border-radius: 0.3rem;
    outline: 0;
}

.modal-header[b-5lwfux2qo2] {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
    border-top-left-radius: calc(0.3rem - 1px);
    border-top-right-radius: calc(0.3rem - 1px);
}

.modal-body[b-5lwfux2qo2] {
    position: relative;
    flex: 1 1 auto;
    padding: 1rem;
}

.modal-footer[b-5lwfux2qo2] {
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
    align-items: center;
    justify-content: flex-end;
    padding: 0.75rem;
    border-top: 1px solid #dee2e6;
    border-bottom-right-radius: calc(0.3rem - 1px);
    border-bottom-left-radius: calc(0.3rem - 1px);
    gap: 0.5rem;
}

/* Form controls */
.form-label[b-5lwfux2qo2] {
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.form-control[b-5lwfux2qo2] {
    display: block;
    width: 100%;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    background-color: #fff;
    background-clip: padding-box;
    border: 1px solid #ced4da;
    appearance: none;
    border-radius: 0.25rem;
    transition: border-color .15s ease-in-out,box-shadow .15s ease-in-out;
}

.form-control:focus[b-5lwfux2qo2] {
    color: #212529;
    background-color: #fff;
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Alert styling */
.alert-info[b-5lwfux2qo2] {
    color: #055160;
    background-color: #cff4fc;
    border-color: #b6effb;
}

/* Container spacing */
.container[b-5lwfux2qo2] {
    padding-left: 1rem;
    padding-right: 1rem;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .modal-dialog[b-5lwfux2qo2] {
        margin: 0.5rem;
        max-width: calc(100% - 1rem);
    }
    
    .btn-group[b-5lwfux2qo2] {
        flex-direction: column;
    }
    
    .d-flex[b-5lwfux2qo2] {
        flex-direction: column;
    }
    
    .d-flex > *[b-5lwfux2qo2] {
        margin-bottom: 0.5rem;
    }
}
/* _content/Barret.Web.Server/Shared/Components/DeviceManagers/InterfaceManager.razor.rz.scp.css */
.interface-container[b-yyfpyyh0gu] {
    border-radius: 4px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.1);
}

[b-yyfpyyh0gu] .compact-grid {
    font-size: 0.875rem;
}

[b-yyfpyyh0gu] .compact-grid .dxbs-grid-data td {
    padding: 0.4rem 0.5rem;
}

.interface-section[b-yyfpyyh0gu] {
    margin-bottom: 1rem;
}

.interface-section h6[b-yyfpyyh0gu] {
    font-weight: 500;
}

.alert-light[b-yyfpyyh0gu] {
    background-color: #f8f9fa;
    border-color: #f5f5f5;
    color: #6c757d;
    text-align: center;
    font-size: 0.875rem;
}
