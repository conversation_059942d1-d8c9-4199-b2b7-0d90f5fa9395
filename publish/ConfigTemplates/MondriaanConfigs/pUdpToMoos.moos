// MOOS file

LatOrigin  = 51.213250
LongOrigin = 2.997318


ServerHost = localhost
ServerPort = 9000


Community  = SF_Dignity

//------------------------------------------
// Antler configuration  block
ProcessConfig = ANTLER
{
  MSBetweenLaunches = 200
  
  Run = MOOSDB		@ NewConsole = false

  //Networking
  Run = ../src/moos_modules/connection_handlers/tcp_connection_handlers/tcp_connection_handler_moos.py @NewConsole = false

  //Converters
  Run = ../src/moos_modules/converters/propulsion_gps_calculator.py       @ NewConsole = false
  Run = ../src/moos_modules/converters/ais_to_node_report_converter.py       @ NewConsole = false
  Run = ../src/moos_modules/converters/thruster_offset_manager_moos.py       @ NewConsole = false

  //Data Acquisation
  Run = ../src/moos_modules/data_acquisation/camera_snapshot_taker_moos.py @ NewConsole = false

  //Controllers
  Run = ../src/moos_modules/controller/controller_manager_module.py       @ NewConsole = false

  //Station keeping
  //Run = ../src/moos_modules/station_keeping_monitors/station_keeping_points_updater.py       @ NewConsole = false
  //Run = ../src/moos_modules/station_keeping_monitors/controlled_stop_handler_moos.py       @ NewConsole = false
  //Run = ../src/moos_modules/station_keeping_monitors/station_keeping_vitals_monitor_moos.py       @ NewConsole = fals
  Run = ../src/moos_modules/module_monitors/controlled_stop_handler_monitor.py       @ NewConsole = false

  //Monitors
  //Run = ../src/moos_modules/mission_monitors/mission_config_updater_moos.py       @ NewConsole = false
  //Run = ../src/moos_modules/mission_monitors/end_of_mission_monitor.py       @ NewConsole = false
  Run = ../src/moos_modules/sensor_monitors/sensor_monitor_moos.py       @ NewConsole = false
  Run = ../src/moos_modules/sensor_monitors/sensor_value_monitor_moos.py       @ NewConsole = false
  Run = ../src/moos_modules/geofencing_monitors/geofencing_monitor_moos.py       @ NewConsole = false
  Run = ../src/moos_modules/module_monitors/controller_module_heartbeat_monitor.py       @ NewConsole = false
  Run = ../src/moos_modules/module_monitors/heartbeat_monitor_moos.py       @ NewConsole = false
  //Run = ../src/moos_modules/gps_quality_monitors/gps_quality_monitor_moos.py       @ NewConsole = false
  //Run = ../src/moos_modules/mission_monitors/next_waypoint_validator_moos.py       @ NewConsole = false

  //Fusion
  Run = ../src/moos_modules/computer_vision/deepstream_connector.py       @ NewConsole = false
  Run = ../src/moos_modules/data_fusion/ais_cv_raytracing_moos.py       @ NewConsole = false
  //Run = ../src/moos_modules/data_fusion/ais_cv_fusion_moos.py       @ NewConsole = false //Deployed on Jetson!
  Run = ../src/moos_modules/data_fusion/oip_ais_cache_moos.py       @ NewConsole = false
  Run = ../src/moos_modules/oip_event_generator/oip_event_generator_ais_based_moos.py       @ NewConsole = false
  Run = ../src/moos_modules/data_fusion/ais_targets_cache_moos.py       @ NewConsole = false
  Run = ../src/moos_modules/data_fusion/fusion_cache_moos.py       @ NewConsole = false
  Run = ../src/moos_modules/camera_control/ptz_controller_moos.py       @ NewConsole = false

  //External connectors
  //Run = ../src/moos_modules/external_connectors/smartww_connector_moos.py       @ NewConsole = false
  //Run = ../src/moos_modules/external_connectors/vital5g_connector_moos.py       @ NewConsole = false
  //Run = ../src/moos_modules/external_connectors/novimove_connector_moos.py       @ NewConsole = false

  //Miscellaneous
  Run = ../src/moos_modules/periodic_var_posters/periodic_var_poster.py       @ NewConsole = false
  Run = ../src/moos_modules/dead_reckoning_managers/dead_reckoning_manager_moos.py       @ NewConsole = false
  //Run = ../src/moos_modules/log_handlers/log_rotation_module.py       @ NewConsole = false

  //MOOSIVP
  //Run = pHelmIvP @ NewConsole = false
  //Run = pMarineViewer @ NewConsole = false
  //Run = pEchoVar @ NewConsole = false
  Run = pNodeReporter @ NewConsole = false
  //Run = pLogger         @ NewConsole = false
  Run = pBasicContactMgr         @ NewConsole = false

}

//------------------------------------------
// pBasicContactMgr config block
                                                                
ProcessConfig = pBasicContactMgr                                
{                                                               
  AppTick   = 4                                                 
  CommsTick = 4                                                 
                                                       
  contact_local_coords = verbatim
                                                    
  // Properties for all alerts                                  
  default_alert_range       = 500   // the default in meters   
  default_cpa_range         = 1000   // the default in meters   
  alert = id=avd, var=CONTACT_INFO, pattern="name=avd_$[VNAME] # contact=$[VNAME]"
  alert = id=avd, alert_range=250,    cpa_range=1000

  // Policy for retaining potential stale contacts              
  contact_max_age  = 60            //  the default in secs.   
                                                                
  // Configuring other output                                   
  display_radii    = true  // or {true}                        
  alert_verbose    = FALSE  // If true, ALERT_VERBOSE published.
                                                                
  // Policy for linear extrapolation of stale contacts          
  decay = 30,60                       // the default in secs    
                                                                
  contacts_recap_interval = 5         // the default in secs    
}  
//------------------------------------------
// pLogger config block

ProcessConfig = pLogger
{
  AppTick   = 8
  CommsTick = 8

  AsyncLog = true

  // For variables that are published in a bundle on their first post,
  // explicitly declare their logging request
  Log = IVPHELM_LIFE_EVENT @ 0 NOSYNC
  Log = REPORT @ 0 NOSYNC

  LogAuxSrc = true
  WildCardLogging = true
  WildCardOmitPattern = *_STATUS
  WildCardOmitPattern = DB_VARSUMMARY
  WildCardOmitPattern = DB_RWSUMMARY
  WildCardExclusionLog = true
}

ProcessConfig = pHelmIvP
{
  AppTick    = 4
  CommsTick  = 4

  Behaviors    = seafar.bhv
  Verbose      = false
  Domain       = course:0:359:360
  Domain       = speed:0:6:51
}

ProcessConfig = pMarineViewer
{
  AppTick    = 4
  CommsTick  = 4

  TIFF_FILE            = none.tif
  tiff_viewable        = true
  hash_delta           = 500
  hash_viewable        = true
  //set_pan_x            = 29000
  //set_pan_y            = 55000
  //set_pan_x            = 29000
  //set_pan_y            = 16000
  set_pan_x = 0
  set_pan_y = 0
  zoom                 = 1.00
  vehicles_shape_scale = 1
  vehicles_name_mode   = names

  circle_viewable_all = false

  appcast_viewable = true
  appcast_color_scheme = indigo

  scope = PSHARE_INPUT_SUMMARY
  scope = NODE_BROKER_PING
  scope = DEPLOY

  action = STATION_KEEP = false
  action = STATION_KEEP = true

  cmd = label=DEPLOY, var=DEPLOY,  sval=true
  cmd = label=DEPLOY, var=MOOS_MANUAL_OVERRIDE,  sval=false
  cmd = label=DEPLOY, var=AVOID,  sval=true
  cmd = label=DEPLOY, var=RETURN,  sval=false
  cmd = label=DEPLOY, var=STATION_KEEP,  sval=false

  cmd = label=RETURN, var=RETURN, sval=true, receivers=all:$(VNAMES)
  cmd = label=RETURN, var=STATION_KEEP, sval=false, receivers=all:$(VNAMES)

  cmd = label=STATION, var=STATION_KEEP,  sval=true, color=pink

  cmd = label=TRANSIT-FAST, var=WPT_UPDATE,  sval=speed=2.8
  cmd = label=TRANSIT-SLOW, var=WPT_UPDATE,  sval=speed=1.4

  button_one   = DEPLOY  # DEPLOY=true
  button_one   = MOOS_MANUAL_OVERRIDE=false
  button_one   = AVOID=true
  button_one   = RETURN=false # STATION_KEEP=false

  button_two   = RETURN  # RETURN=true
  button_two   = RETURN  # STATION_KEEP=false
  button_three = PERMUTE   # UTS_FORWARD=0
  button_four  = STATION   # STATION_KEEP=true

}

ProcessConfig = pEchoVar
{
  AppTick   = 20
  CommsTick = 20

  echo = SF_BEST_LAT           ->  NAV_LAT
  echo = SF_BEST_LON           ->  NAV_LON
  echo = SF_BEST_HDT           ->  NAV_HEADING
  echo = SF_BEST_SOG           ->  NAV_SPEED
}

ProcessConfig = pNodeReporter
{
  AppTick     = 2
  CommsTick   = 2

  vessel_type = ship
  terse_reports = true

  platform_length = 39
  platform_color  = yellow

  crossfill_policy=fill-empty
}
