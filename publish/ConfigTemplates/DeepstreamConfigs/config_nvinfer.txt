[property]
gpu-id=0
net-scale-factor=0.0039215697906911373
model-color-format=0

# Absolute paths inside the docker container
custom-network-config=/seafar/configs/wts_cfg/yolov5_best.cfg
model-file=/seafar/configs/wts_cfg/yolov5_best.wts

# TensorRT Engine file.
# Generated on this current device. If file unexisting, it will be saved in the specified path.
model-engine-file=model_b2_gpu0_fp16.engine
labelfile-path=labels.txt
batch-size=2
network-mode=2
num-detected-classes=1
interval=0
gie-unique-id=1
operate-on-gie-id=1
process-mode=1
network-type=0
cluster-mode=2
maintain-aspect-ratio=1
parse-bbox-func-name=NvDsInferParseYolo
custom-lib-path=nvdsinfer_custom_impl_Yolo/libnvdsinfer_custom_impl_Yolo.so
engine-create-func-name=NvDsInferYoloCudaEngineGet


[class-attrs-all]
pre-cluster-threshold=0.7
nms-iou-threshold=0.6
