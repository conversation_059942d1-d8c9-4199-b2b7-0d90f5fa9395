[{"identificationHeader": "$PC200", "destinationType": "Plc", "destinationId": "*", "updateIntervalMillis": "0"}, {"identificationHeader": "$PC006", "destinationType": "Plc", "destinationId": "*", "updateIntervalMillis": "0"}, {"identificationHeader": "$PM210", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM211", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0"}, {"identificationHeader": "$PM212", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0"}, {"identificationHeader": "$PM213", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "60000", "filterOnChange": true}, {"identificationHeader": "$PM214", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM215", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM220", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM216", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM800", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0"}, {"identificationHeader": "$PC005", "destinationType": "Plc", "destinationId": "*", "updateIntervalMillis": "0"}, {"identificationHeader": "$PM029", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "15000", "filterOnChange": true}, {"identificationHeader": "$PM217", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "15000", "filterOnChange": true}, {"identificationHeader": "$PM401", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM402", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM403", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM404", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM405", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM406", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM407", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM408", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM409", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM410", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PM411", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "0", "LastHeartbeatActivityRequiredSec": 900}, {"identificationHeader": "$PC201", "destinationType": "Plc", "destinationId": "*", "updateIntervalMillis": "0"}, {"identificationHeader": "$PC013", "destinationType": "Plc", "destinationId": "*", "updateIntervalMillis": "0"}]