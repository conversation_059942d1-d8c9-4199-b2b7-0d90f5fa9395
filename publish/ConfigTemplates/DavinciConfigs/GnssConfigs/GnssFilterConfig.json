[{"IdentificationHeader": "HDT", "DestinationType": "Scc", "DestinationId": "*", "UpdateIntervalMillis": 1000, "FilterOnChange": false}, {"IdentificationHeader": "VTG", "DestinationType": "AutonomyProcessor", "DestinationId": "*", "UpdateIntervalMillis": 0, "FilterOnChange": false}, {"IdentificationHeader": "GGA", "DestinationType": "Scc", "DestinationId": "*", "UpdateIntervalMillis": 1000, "FilterOnChange": false}, {"IdentificationHeader": "HDT", "DestinationType": "AutonomyProcessor", "DestinationId": "*", "UpdateIntervalMillis": 0, "FilterOnChange": false}, {"IdentificationHeader": "GGA", "DestinationType": "AutonomyProcessor", "DestinationId": "*", "UpdateIntervalMillis": 0, "FilterOnChange": false}, {"IdentificationHeader": "VTG", "DestinationType": "Scc", "DestinationId": "*", "UpdateIntervalMillis": 1000, "FilterOnChange": false}]