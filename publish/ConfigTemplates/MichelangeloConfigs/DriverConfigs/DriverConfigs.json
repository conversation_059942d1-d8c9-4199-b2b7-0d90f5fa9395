{"ModbusAutomationDriver": [{"DriverConfig": {"DriverIdentification": {"name": "Wago IO", "technicalComponentId": "", "driverId": "626b9a03-f1f1-4cdc-a6f8-32785617bf3b"}, "rebootIntervalOnFail_ms": 1000, "heartbeatInterval_ms": 1000, "SendingIntervalMs": 250, "ReadingIntervalMs": 250, "GeneralSystemDomainDrivers": [{"RegisterMetaData": {"RegisterId": 512, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 12, "InvertBitValue": true}, "GeneralSystemValuePurpose": "RegisterFillValue"}, {"RegisterMetaData": {"RegisterId": 512, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 13, "InvertBitValue": true}, "GeneralSystemValuePurpose": "RegisterFillValue"}, {"RegisterMetaData": {"RegisterId": 512, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "GeneralSystemValuePurpose": "SafetyBuzzerControl"}, {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}, "GeneralSystemValuePurpose": "LocalControlRequest"}, {"RegisterMetaData": {"RegisterId": 512, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}, "GeneralSystemValuePurpose": "RunStateLedControl"}, {"RegisterMetaData": {"RegisterId": 512, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "GeneralSystemValuePurpose": "SafetyLed"}, {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}, "GeneralSystemValuePurpose": "RunSwitchFeedback"}, {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10, "InvertBitValue": false}, "GeneralSystemValuePurpose": "EmergencyStopFeedback"}, {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11, "InvertBitValue": true}, "GeneralSystemValuePurpose": "SafetyOutputFeedback"}, {"RegisterMetaData": {"RegisterId": 512, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}, "GeneralSystemValuePurpose": "ResetSafetySystem"}], "PropulsionDomainDrivers": [{"DomainDriverIdentification": {"UnitId": 1, "UnitLocation": "Unspecified", "UnitType": "Autopilot"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 512, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}, "PropulsionValuePurpose": "ControlAction"}, {"RegisterMetaData": {"RegisterId": 512, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}, "PropulsionValuePurpose": "KeepAlivePolling"}]}}], "SystemFunctionsDomainDrivers": [], "AlarmDomainDrivers": [{"DomainDriverIdentification": {"AlarmMessage": "Stabilizer channel 1 not ok", "NotificationGroupId": "PowerGeneral", "EntityId": "Stabilizer 1", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "InputRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0, "InvertBitValue": true}}}, {"DomainDriverIdentification": {"AlarmMessage": "Stabilizer channel 2 not ok", "NotificationGroupId": "PowerGeneral", "EntityId": "Stabilizer 2", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "InputRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1, "InvertBitValue": true}}}, {"DomainDriverIdentification": {"AlarmMessage": "12V Distribution not ok", "NotificationGroupId": "PowerGeneral", "EntityId": "12V Core cabinet", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "InputRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3, "InvertBitValue": true}}}, {"DomainDriverIdentification": {"AlarmMessage": "Cooling 5 degrees above set temperature of 35 degrees", "NotificationGroupId": "ControlSystemGeneral", "EntityId": "Cooling Core cabinet", "NotificationTypeId": "Warning", "WarningId": "MiTemperatureExceededThresholds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "InputRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4, "InvertBitValue": true}}}, {"DomainDriverIdentification": {"AlarmMessage": "Core cabinets door opened", "NotificationGroupId": "ControlSystemGeneral", "EntityId": "Door Core cabinet", "NotificationTypeId": "Warning", "WarningId": "MiHatchOpen"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "InputRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5, "InvertBitValue": true}}}]}, "ConnectionHandlerConfig": {"protocol": "modbustcpclient", "connectionAddress": "**********", "connectionAddressOption": 502}, "Pipelines": ["Automation"]}, {"DriverConfig": {"DriverIdentification": {"name": "Ship Plc", "technicalComponentId": "", "driverId": "d8f0293b-1da1-48e5-867e-ef45ffe439db"}, "rebootIntervalOnFail_ms": 1000, "heartbeatInterval_ms": 1000, "SendingIntervalMs": 250, "ReadingIntervalMs": 250, "GeneralSystemDomainDrivers": [{"RegisterMetaData": {"RegisterId": 101, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "GeneralSystemValuePurpose": "ThirdPartyHeartbeat"}], "PropulsionDomainDrivers": [], "SystemFunctionsDomainDrivers": [{"DomainDriverIdentification": {"SystemFunctionId": "BridgeWarningIndicator", "Description": "Seafar Control Feedback"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 101, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "ControlFeedback"}, {"RegisterMetaData": {"RegisterId": 101, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "SystemFunctionsValuePurposes": "ThirdPartySafetyContactFeedback"}, {"RegisterMetaData": {"RegisterId": 201, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "ControlAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "AlarmGeneral", "Description": "General <PERSON><PERSON>"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 102, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 202, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom1", "Description": "Call / Alarm Bow Engineroom"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 102, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 202, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom1", "Description": "Call / Alarm Stern Engineroom"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 102, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 202, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom1", "Description": "Call / Alarm Stern Accommodation"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 102, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 14}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 202, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 14}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "Spare alarm 81"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 108, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 203, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "Spare alarm 113"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 110, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 204, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "Spare alarm 146"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 112, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 205, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "-"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 115, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 206, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "Generator 1 Running"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 117, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 207, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "-"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 208, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "-"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 209, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "-"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 210, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "-"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 211, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "-"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 212, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "-"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 215, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "CallSystemRoom2", "Description": "Generator 1 Start"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 217, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "RegisterFillValue"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "Horn", "Description": "<PERSON><PERSON><PERSON>"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 113, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 213, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightBlueSign", "Description": "Blue Sign (board)"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 113, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 213, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "StrobeLights", "Description": "Orange Strobe Light"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 113, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 213, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightAdnrSign1", "Description": "Blue ADN Stern 1"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 113, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 7}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 213, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 7}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightAdnrSign1", "Description": "Blue ADN Bow 1"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 113, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 213, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightAdnrSign2", "Description": "Blue ADN Stern 2"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 113, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 213, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightAdnrSign2", "Description": "Blue ADN Bow 2"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 113, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 213, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightTowing", "Description": "Top light Bow 1"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 114, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 214, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightTowing", "Description": "Top light Bow 2"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 114, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 214, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightSides", "Description": "Side light PS"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 114, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 214, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightSides", "Description": "Side light SB"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 114, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 214, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightStern", "Description": "Stern light 1"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 114, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 214, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightAnchor", "Description": "Anchor light Stern PS"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 114, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 214, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightAnchor", "Description": "Anchor light Stern SB"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 114, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 214, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightAnchor", "Description": "Anchor light Bow PS"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 114, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 214, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "LightAnchor", "Description": "Anchor light Bow SB"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 114, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 214, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "UpMastPole1", "Description": "Ma<PERSON>"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 116, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "MoveUpFeedback"}, {"RegisterMetaData": {"RegisterId": 116, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "MoveDownFeedback"}, {"RegisterMetaData": {"RegisterId": 216, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "MoveUpAction"}, {"RegisterMetaData": {"RegisterId": 216, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "MoveDownAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "UpMastPole2", "Description": "Mast <PERSON>"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 116, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "MoveUpFeedback"}, {"RegisterMetaData": {"RegisterId": 116, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}, "SystemFunctionsValuePurposes": "MoveDownFeedback"}, {"RegisterMetaData": {"RegisterId": 216, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "MoveUpAction"}, {"RegisterMetaData": {"RegisterId": 216, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}, "SystemFunctionsValuePurposes": "MoveDownAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "UpMastPole3", "Description": "Spoiler Mast <PERSON>"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 116, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}, "SystemFunctionsValuePurposes": "MoveUpFeedback"}, {"RegisterMetaData": {"RegisterId": 116, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 7}, "SystemFunctionsValuePurposes": "MoveDownFeedback"}, {"RegisterMetaData": {"RegisterId": 216, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}, "SystemFunctionsValuePurposes": "MoveUpAction"}, {"RegisterMetaData": {"RegisterId": 216, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 7}, "SystemFunctionsValuePurposes": "MoveDownAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlight Poller <PERSON><PERSON>"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 118, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 218, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlight Poller Fore Starboard"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 118, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 218, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlight Mast Fore Portside"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 118, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 218, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlight Mast Fore Starboard"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 118, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 218, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlight Spoiler Aft Deck Portside"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 119, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 219, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlight Spoiler Aft Deck Starboard"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 119, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 219, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlight Boardunit Portside"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 119, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 219, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlight Boardunit Startboard"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 119, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 219, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlighting Wheelhouse Roof Front"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 119, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 219, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DeckLight1", "Description": "Floodlighting Aft Mast"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 119, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 219, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}, "SystemFunctionsValuePurposes": "ToggleAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "SearchLight1", "Description": "Search light Bow ON/OFF"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "MoveLeftFeedback"}, {"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "SystemFunctionsValuePurposes": "MoveRightFeedback"}, {"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "MoveUpFeedback"}, {"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "MoveDownFeedback"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ToggleAction"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "MoveLeftAction"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "SystemFunctionsValuePurposes": "MoveRightAction"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "MoveUpAction"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "MoveDownAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DownSpudPoleStern1", "Description": "Search light Stern PS ON/OFF"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}, "SystemFunctionsValuePurposes": "MoveLeftFeedback"}, {"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}, "SystemFunctionsValuePurposes": "MoveRightFeedback"}, {"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}, "SystemFunctionsValuePurposes": "MoveUpFeedback"}, {"RegisterMetaData": {"RegisterId": 120, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 12}, "SystemFunctionsValuePurposes": "MoveDownFeedback"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}, "SystemFunctionsValuePurposes": "ToggleAction"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}, "SystemFunctionsValuePurposes": "MoveLeftAction"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}, "SystemFunctionsValuePurposes": "MoveRightAction"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}, "SystemFunctionsValuePurposes": "MoveUpAction"}, {"RegisterMetaData": {"RegisterId": 220, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 12}, "SystemFunctionsValuePurposes": "MoveDownAction"}]}}, {"DomainDriverIdentification": {"SystemFunctionId": "DownSpudPoleBow1", "Description": "Search light Stern SB ON/OFF"}, "DomainSpecificConfig": {"TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 121, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ActionFeedback"}, {"RegisterMetaData": {"RegisterId": 121, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "MoveLeftFeedback"}, {"RegisterMetaData": {"RegisterId": 121, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "SystemFunctionsValuePurposes": "MoveRightFeedback"}, {"RegisterMetaData": {"RegisterId": 121, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "MoveUpFeedback"}, {"RegisterMetaData": {"RegisterId": 121, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "MoveDownFeedback"}, {"RegisterMetaData": {"RegisterId": 221, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "SystemFunctionsValuePurposes": "ToggleAction"}, {"RegisterMetaData": {"RegisterId": 221, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "SystemFunctionsValuePurposes": "MoveLeftAction"}, {"RegisterMetaData": {"RegisterId": 221, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "SystemFunctionsValuePurposes": "MoveRightAction"}, {"RegisterMetaData": {"RegisterId": 221, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "SystemFunctionsValuePurposes": "MoveUpAction"}, {"RegisterMetaData": {"RegisterId": 221, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "SystemFunctionsValuePurposes": "MoveDownAction"}]}}], "AlarmDomainDrivers": [{"DomainDriverIdentification": {"AlarmMessage": "Fire alarm system failure", "NotificationGroupId": "SafetySystems", "EntityId": "Fire Alarm", "NotificationTypeId": "Warning", "WarningId": "MiNavigationEquipmentDefect"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 102, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}}}, {"DomainDriverIdentification": {"AlarmMessage": "Cannot sail with active red group", "NotificationGroupId": "PowerGeneral", "EntityId": "Red group", "NotificationTypeId": "Alarm", "AlarmId": "MiRedGroupActive"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 102, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}}}, {"DomainDriverIdentification": {"AlarmMessage": "Generator 1 Pump Breaker Is Tripped", "NotificationGroupId": "PowerGeneral", "EntityId": "Generator 1 Breaker", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 103, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}}}, {"DomainDriverIdentification": {"AlarmMessage": "Generator 1 <PERSON>uster Breaker Is Tripped", "NotificationGroupId": "PowerGeneral", "EntityId": "<PERSON> <PERSON><PERSON><PERSON>", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 103, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}}}, {"DomainDriverIdentification": {"AlarmMessage": "Bow Thruster Overload Advice To Reduce Power", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON> Thruster", "NotificationTypeId": "Warning", "WarningId": "MiPropulsionOverspeed"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 103, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}}}, {"DomainDriverIdentification": {"AlarmMessage": "Generator 2 Pump Breaker Is Tripped", "NotificationGroupId": "PowerSystems", "EntityId": "Generator 2 Pump Breaker", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 104, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}}}, {"DomainDriverIdentification": {"AlarmMessage": "Generator 2 Boardnet Breaker Is Tripped", "NotificationGroupId": "PowerSystems", "EntityId": "Boardnet breaker", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 104, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}}}, {"DomainDriverIdentification": {"AlarmMessage": "Emergency Stop Engine Room Fan Is Active", "NotificationGroupId": "SafetySystems", "EntityId": "Estop Engine Room Fan", "NotificationTypeId": "Warning", "WarningId": "MiExternalEmergencyStop"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 104, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}}}, {"DomainDriverIdentification": {"AlarmMessage": "Pressure Too Low Overpressure System Engineroom", "NotificationGroupId": "SafetySystems", "EntityId": "Overpressure Engine Room", "NotificationTypeId": "Alarm", "AlarmId": "MiPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 104, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}}}, {"DomainDriverIdentification": {"AlarmMessage": "Door Extinghuiser Installation Opened", "NotificationGroupId": "SafetySystems", "EntityId": "Door Extinghuiser Installation", "NotificationTypeId": "Warning", "WarningId": "MiHatchOpen"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 104, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}}}, {"DomainDriverIdentification": {"AlarmMessage": "Failure Synchronisation Module Generator 2", "NotificationGroupId": "PowerSystems", "EntityId": "Generator 2 Sync", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 105, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}}}, {"DomainDriverIdentification": {"AlarmMessage": "Pressure Of Extinghuiser Installation To Low", "NotificationGroupId": "SafetySystems", "EntityId": "Extinguisher installation", "NotificationTypeId": "Warning", "WarningId": "MiPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 105, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}}}, {"DomainDriverIdentification": {"AlarmMessage": "Alarm Present On Generator 1", "NotificationGroupId": "PowerSystems", "EntityId": "Generator 1", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 105, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}}}, {"DomainDriverIdentification": {"AlarmMessage": "Alarm Present On Generator 2", "NotificationGroupId": "PowerSystems", "EntityId": "Generator 2", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 105, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}}}, {"DomainDriverIdentification": {"AlarmMessage": "Bilge Level High Inside Engine Room", "NotificationGroupId": "SafetySystems", "EntityId": "Engine Room Bow", "NotificationTypeId": "Alarm", "AlarmId": "MiBilgeGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 105, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}}}, {"DomainDriverIdentification": {"AlarmMessage": "Level Inside Fuel Tank Is Too Low", "NotificationGroupId": "PowerGeneral", "EntityId": "Fuel Tank Bow", "NotificationTypeId": "Warning", "WarningId": "MiFuelOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 105, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}}}, {"DomainDriverIdentification": {"AlarmMessage": "Level Inside Fuel Tank Is Too High", "NotificationGroupId": "PowerSystems", "EntityId": "Fuel Tank Bow", "NotificationTypeId": "Warning", "WarningId": "MiFuelOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 105, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}}}, {"DomainDriverIdentification": {"AlarmMessage": "Alarm Compressor", "NotificationGroupId": "ControlSystemGeneral", "EntityId": "Alarm Compressor", "NotificationTypeId": "Alarm", "AlarmId": "MiPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 105, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}}}, {"DomainDriverIdentification": {"AlarmMessage": "Alarm Generator 1 Aftertreatment System", "NotificationGroupId": "PowerGeneral", "EntityId": "Aftertreatment Generator 1", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 105, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}}}, {"DomainDriverIdentification": {"AlarmMessage": "Generator 3 Breaker Is Tripped", "NotificationGroupId": "PowerSystems", "EntityId": "Generator 3 Breaker", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}}}, {"DomainDriverIdentification": {"AlarmMessage": "Alarm Present On Generator 3", "NotificationGroupId": "PowerSystems", "EntityId": "Generator 3", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}}}, {"DomainDriverIdentification": {"AlarmMessage": "Failure Synchronisation Module Generator 3", "NotificationGroupId": "PowerGeneral", "EntityId": "Generator 3 Sync", "NotificationTypeId": "Alarm", "AlarmId": "MiGeneratorGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}}}, {"DomainDriverIdentification": {"AlarmMessage": "Emergency Stop Engine Room Fan Is Active", "NotificationGroupId": "PowerGeneral", "EntityId": "Estop Engine Room Fan", "NotificationTypeId": "Warning", "WarningId": "MiExternalEmergencyStop"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}}}, {"DomainDriverIdentification": {"AlarmMessage": "Pressure Too Low Overpressure System Wheelhouse", "NotificationGroupId": "ControlSystemGeneral", "EntityId": "Wheelhouse", "NotificationTypeId": "Warning", "WarningId": "MiPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}}}, {"DomainDriverIdentification": {"AlarmMessage": "Pressure Too Low Overpressure System Accommodation", "NotificationGroupId": "SafetySystems", "EntityId": "Accommodation", "NotificationTypeId": "Warning", "WarningId": "MiPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}}}, {"DomainDriverIdentification": {"AlarmMessage": "Pressure Too Low Overpressure System Engineroom", "NotificationGroupId": "SafetySystems", "EntityId": "Engine Room", "NotificationTypeId": "Warning", "WarningId": "MiPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 7}}}, {"DomainDriverIdentification": {"AlarmMessage": "Supply Failure Wheelhouse Lift Control", "NotificationGroupId": "SafetySystems", "EntityId": "Supply Wheelhouse", "NotificationTypeId": "Alarm", "AlarmId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}}}, {"DomainDriverIdentification": {"AlarmMessage": "Main Battery Voltage Is Too Low", "NotificationGroupId": "SafetySystems", "EntityId": "Main Battery", "NotificationTypeId": "Alarm", "AlarmId": "MiBatteryLowVoltage"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}}}, {"DomainDriverIdentification": {"AlarmMessage": "Door Extinghuiser Installation Opened", "NotificationGroupId": "SafetySystems", "EntityId": "Door Extinghuiser Installation", "NotificationTypeId": "Warning", "WarningId": "MiHatchOpen"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}}}, {"DomainDriverIdentification": {"AlarmMessage": "Supply Failure Gasdetection System", "NotificationGroupId": "SafetySystems", "EntityId": "Gas detection supply", "NotificationTypeId": "Warning", "WarningId": "MiGasDetection"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 12}}}, {"DomainDriverIdentification": {"AlarmMessage": "Pressure Too Low Extinghuiser System SB", "NotificationGroupId": "SafetySystems", "EntityId": "Extinghuiser system SB", "NotificationTypeId": "Warning", "WarningId": "MiPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 13}}}, {"DomainDriverIdentification": {"AlarmMessage": "Bilge Level High Inside Engine Room", "NotificationGroupId": "SafetySystems", "EntityId": "Bilge Engine Room", "NotificationTypeId": "Alarm", "AlarmId": "MiBilgeGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 14}}}, {"DomainDriverIdentification": {"AlarmMessage": "Bilge Level High Inside Wheelhouse Column", "NotificationGroupId": "Propulsion", "EntityId": "Bigle Wheelhouse Column", "NotificationTypeId": "Alarm", "AlarmId": "MiBilgeGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 106, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 15}}}, {"DomainDriverIdentification": {"AlarmMessage": "Level Inside Fuel Tank PS Is Too Low", "NotificationGroupId": "PowerSystems", "EntityId": "Fuel Tank PS", "NotificationTypeId": "Warning", "WarningId": "MiFuelOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}}}, {"DomainDriverIdentification": {"AlarmMessage": "Level Inside Fuel Tank SB Is Too Low", "NotificationGroupId": "PowerSystems", "EntityId": "Fuel Tank SB", "NotificationTypeId": "Warning", "WarningId": "MiFuelOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}}}, {"DomainDriverIdentification": {"AlarmMessage": "Level Inside Fuel Tank PS Is Too High", "NotificationGroupId": "PowerSystems", "EntityId": "Fuel Tank PS", "NotificationTypeId": "Warning", "WarningId": "MiFuelOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}}}, {"DomainDriverIdentification": {"AlarmMessage": "Level Inside Fuel Tank SB Is Too High", "NotificationGroupId": "PowerSystems", "EntityId": "Fuel Tank SB", "NotificationTypeId": "Warning", "WarningId": "MiFuelOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}}}, {"DomainDriverIdentification": {"AlarmMessage": "Shaft Lubrication Header Tank PS Level Too Low", "NotificationGroupId": "Propulsion", "EntityId": "Shaft Lubrication PS", "NotificationTypeId": "Alarm", "AlarmId": "MiTankOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}}}, {"DomainDriverIdentification": {"AlarmMessage": "Shaft Brake Installation PS Is Active (Brake Is On)", "NotificationGroupId": "Propulsion", "EntityId": "Shaft Brake PS", "NotificationTypeId": "Alarm", "AlarmId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}}}, {"DomainDriverIdentification": {"AlarmMessage": "Shaft Lubrication Header Tank SB Level Too Low", "NotificationGroupId": "Propulsion", "EntityId": "Shaft Lubrication SB", "NotificationTypeId": "Alarm", "AlarmId": "MiTankOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 7}}}, {"DomainDriverIdentification": {"AlarmMessage": "Shaft Brake Installation SB Is Active (Brake Is On)", "NotificationGroupId": "Propulsion", "EntityId": "Shaft Brake SB", "NotificationTypeId": "Alarm", "AlarmId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}}}, {"DomainDriverIdentification": {"AlarmMessage": "Door Wheelhouse Elevator Opened", "NotificationGroupId": "SafetySystems", "EntityId": "Door Wheelhouse Elevator", "NotificationTypeId": "Warning", "WarningId": "MiHatchOpen"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}}}, {"DomainDriverIdentification": {"AlarmMessage": "Pressure Too Low Extinghuiser System PS", "NotificationGroupId": "PowerGeneral", "EntityId": "Extinghuiser system PS", "NotificationTypeId": "Warning", "WarningId": "MiPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}}}, {"DomainDriverIdentification": {"AlarmMessage": "Failure Aftertreatment System Mainengine PS", "NotificationGroupId": "PowerGeneral", "EntityId": "Aftertreatment Main Engine PS", "NotificationTypeId": "Warning", "WarningId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}}}, {"DomainDriverIdentification": {"AlarmMessage": "Failure Aftertreatment System Mainengine SB", "NotificationGroupId": "PowerSystems", "EntityId": "Aftertreatment Main Engine SB", "NotificationTypeId": "Warning", "WarningId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 107, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 12}}}, {"DomainDriverIdentification": {"AlarmMessage": "Isolation Fault 24Vdc Main batteries", "NotificationGroupId": "PowerSystems", "EntityId": "24Vdc Main Batteries", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 109, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}}}, {"DomainDriverIdentification": {"AlarmMessage": "General Failure Charger/Inverter 24Vdc Installation", "NotificationGroupId": "PowerSystems", "EntityId": "Inverter 24V DC", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 109, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}}}, {"DomainDriverIdentification": {"AlarmMessage": "Voltage Failure Emergency Controls Wheelhouse", "NotificationGroupId": "ControlSystemGeneral", "EntityId": "Emergency Controls Wheelhouse", "NotificationTypeId": "Alarm", "AlarmId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 109, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}}}, {"DomainDriverIdentification": {"AlarmMessage": "Failure Navigation Lights", "NotificationGroupId": "NavigationEquipment", "EntityId": "Navigation Lights", "NotificationTypeId": "Warning", "WarningId": "MiNavigationEquipmentDefect"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 109, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}}}, {"DomainDriverIdentification": {"AlarmMessage": "Failure 24Vdc Navigation Lights", "NotificationGroupId": "NavigationEquipment", "EntityId": "24Vdc Navigation Lights", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 109, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 7}}}, {"DomainDriverIdentification": {"AlarmMessage": "Fire / Failure From Firedetection Unit (See Unit)", "NotificationGroupId": "SafetySystems", "EntityId": "Firedetection Unit", "NotificationTypeId": "Alarm", "AlarmId": "MiFireGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 109, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}}}, {"DomainDriverIdentification": {"AlarmMessage": "Failure 400Vac Boardnet Supply", "NotificationGroupId": "PowerSystems", "EntityId": "400Vac Boardnet Supply", "NotificationTypeId": "Alarm", "AlarmId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 109, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}}}, {"DomainDriverIdentification": {"AlarmMessage": "Failure 24Vdc Backup Supply Wheelhouse", "NotificationGroupId": "PowerSystems", "EntityId": "24Vdc Wheelhouse backup", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 109, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}}}, {"DomainDriverIdentification": {"AlarmMessage": "Steering Watch Alarm", "NotificationGroupId": "PowerSystems", "EntityId": "Steering Watch Alarm", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 109, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}}}, {"DomainDriverIdentification": {"AlarmMessage": "Voltage Failure 24Vdc PLC System", "NotificationGroupId": "ControlSystemPLC", "EntityId": "24Vdc Plc system", "NotificationTypeId": "Warning", "WarningId": "MiPowerGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 111, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}}}, {"DomainDriverIdentification": {"AlarmMessage": "General Failure EtherNET System", "NotificationGroupId": "Communication", "EntityId": "Werkina Ethernet System", "NotificationTypeId": "Alarm", "AlarmId": "MiCommunicationGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 111, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}}}, {"DomainDriverIdentification": {"AlarmMessage": "Communication Lost With IO Station Wheelhouse", "NotificationGroupId": "Communication", "EntityId": "IO Wheelhouse", "NotificationTypeId": "Warning", "WarningId": "MiCommunicationGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 111, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}}}, {"DomainDriverIdentification": {"AlarmMessage": "Communication Lost With IO Station Engine Room Fore", "NotificationGroupId": "Communication", "EntityId": "IO Engine Room", "NotificationTypeId": "Warning", "WarningId": "MiCommunicationGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 111, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}}}, {"DomainDriverIdentification": {"AlarmMessage": "Communication Lost With Frequency Converter Slobpump", "NotificationGroupId": "Communication", "EntityId": "Frequency Converter Slobpump", "NotificationTypeId": "Warning", "WarningId": "MiCommunicationGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 111, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}}}, {"DomainDriverIdentification": {"AlarmMessage": "Communication Lost With Seafar", "NotificationGroupId": "Communication", "EntityId": "Werkina Plc", "NotificationTypeId": "Alarm", "AlarmId": "MiCommunicationGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 111, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}}}, {"DomainDriverIdentification": {"AlarmMessage": "Communication Lost With Werkina HMI Wheelhouse", "NotificationGroupId": "Communication", "EntityId": "<PERSON><PERSON><PERSON>", "NotificationTypeId": "Warning", "WarningId": "MiCommunicationGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 111, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 7}}}], "HeartbeatRegister": {"RegisterMetaData": {"RegisterId": 201, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}}}, "ConnectionHandlerConfig": {"protocol": "modbustcpclient", "connectionAddress": "**************", "connectionAddressOption": 502}, "Pipelines": ["Automation"]}, {"DriverConfig": {"DriverIdentification": {"name": "<PERSON><PERSON><PERSON><PERSON>", "technicalComponentId": "", "driverId": "83c6bb1f-cf87-47bc-9757-7ec5d3ff6672"}, "rebootIntervalOnFail_ms": 1000, "heartbeatInterval_ms": 1000, "SendingIntervalMs": 250, "ReadingIntervalMs": 250, "GeneralSystemDomainDrivers": [{"RegisterMetaData": {"RegisterId": 1000, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "GeneralSystemValuePurpose": "ThirdPartyHeartbeat"}], "PropulsionDomainDrivers": [{"DomainDriverIdentification": {"UnitId": 1, "UnitLocation": "Bow", "UnitType": "ThrusterRotational"}, "DomainSpecificConfig": {"ClutchValues": {"DetentForwardValue": 20, "ClutchForwardValue": 20, "DetentBackwardsValue": -20, "ClutchBackwardsValue": -20}, "TrackedRegisters": [{"RegisterMetaData": {"RegisterId": 1000, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "PropulsionValuePurpose": "ControlFeedback"}, {"RegisterMetaData": {"RegisterId": 1000, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}, "PropulsionValuePurpose": "ThirdPartySafetyContactFeedback"}, {"RegisterMetaData": {"RegisterId": 1001, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "PropulsionValuePurpose": "PropulsionStatusFeedback"}, {"RegisterMetaData": {"RegisterId": 1001, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "PropulsionValuePurpose": "ClutchFeedbackForward"}, {"RegisterMetaData": {"RegisterId": 1001, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}, "PropulsionValuePurpose": "ClutchFeedbackNeutral"}, {"RegisterMetaData": {"RegisterId": 1001, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}, "PropulsionValuePurpose": "ClutchFeedbackReverse"}, {"RegisterMetaData": {"RegisterId": 1003, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "RegisterScaleData": {"RegisterMaxValue": 100, "RegisterMinValue": 0, "DomainMinValue": 0, "DomainMaxValue": 100}, "PropulsionValuePurpose": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"RegisterMetaData": {"RegisterId": 1004, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "FeedbackDirection", "RegisterScaleData": {"ScalingType": "ThrusterPercentageToMovementDegrees"}}, {"RegisterMetaData": {"RegisterId": 1005, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "FeedbackRpm"}, {"RegisterMetaData": {"RegisterId": 1006, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 1007, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 1008, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 1009, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 1010, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 1011, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 1012, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "KeepAlivePolling"}, {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "PropulsionValuePurpose": "ControlAction"}, {"RegisterMetaData": {"RegisterId": 1, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}, "PropulsionValuePurpose": "StartPropulsionAction"}, {"RegisterMetaData": {"RegisterId": 1, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}, "PropulsionValuePurpose": "StopPropulsionAction"}, {"RegisterMetaData": {"RegisterId": 2, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "RegisterScaleData": {"RegisterMaxValue": 100, "RegisterMinValue": 0, "DomainMinValue": 0, "DomainMaxValue": 100}, "PropulsionValuePurpose": "ActionPower"}, {"RegisterMetaData": {"RegisterId": 3, "RegisterType": "HoldingRegister", "RegisterValueType": "Integer16"}, "PropulsionValuePurpose": "ActionDirection", "RegisterScaleData": {"ScalingType": "ThrusterPercentageToMovementDegrees"}}]}}], "SystemFunctionsDomainDrivers": [], "AlarmDomainDrivers": [{"DomainDriverIdentification": {"AlarmMessage": "Bow thruster cannot be used since generator pump is active", "NotificationGroupId": "Propulsion", "EntityId": "Bow thruster generator pump", "NotificationTypeId": "Alarm", "AlarmId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1001, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}}}, {"DomainDriverIdentification": {"AlarmMessage": "Oil pressure low", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON> Thruster", "NotificationTypeId": "Alarm", "AlarmId": "MiOilPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}}}, {"DomainDriverIdentification": {"AlarmMessage": "Oil temperature high", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON> Thruster", "NotificationTypeId": "Alarm", "AlarmId": "MiOilPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 1}}}, {"DomainDriverIdentification": {"AlarmMessage": "Coolant level low", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON> Thruster", "NotificationTypeId": "Alarm", "AlarmId": "MiCoolantLiquidOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 2}}}, {"DomainDriverIdentification": {"AlarmMessage": "Coolant temperature high", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON><PERSON><PERSON>", "NotificationTypeId": "Alarm", "AlarmId": "MiTemperatureExceededThresholds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 3}}}, {"DomainDriverIdentification": {"AlarmMessage": "Diesel engine Overspeed", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON> Thruster", "NotificationTypeId": "Alarm", "AlarmId": "MiPropulsionOverspeed"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 4}}}, {"DomainDriverIdentification": {"AlarmMessage": "Verhaar reports can bus issue with their propulsion", "NotificationGroupId": "Communication", "EntityId": "<PERSON> Thruster", "NotificationTypeId": "Alarm", "AlarmId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 5}}}, {"DomainDriverIdentification": {"AlarmMessage": "Gearbox oil pressure low", "NotificationGroupId": "Propulsion", "EntityId": "Bow Thruster Gearbox", "NotificationTypeId": "Alarm", "AlarmId": "MiOilPressureOutOfBounds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 6}}}, {"DomainDriverIdentification": {"AlarmMessage": "Gearbox oil temperature high", "NotificationGroupId": "Propulsion", "EntityId": "Bow Thruster Gearbox", "NotificationTypeId": "Alarm", "AlarmId": "MiTemperatureExceededThresholds"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 8}}}, {"DomainDriverIdentification": {"AlarmMessage": "Drum not rotating", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON> Thruster", "NotificationTypeId": "Alarm", "AlarmId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 9}}}, {"DomainDriverIdentification": {"AlarmMessage": "Breaker trip", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON> <PERSON><PERSON><PERSON>", "NotificationTypeId": "Alarm", "AlarmId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 10}}}, {"DomainDriverIdentification": {"AlarmMessage": "Breaker oil pump trip", "NotificationGroupId": "Propulsion", "EntityId": "Bow Thruster Oil Pump Trip", "NotificationTypeId": "Alarm", "AlarmId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 11}}}, {"DomainDriverIdentification": {"AlarmMessage": "General warning on Verhaar system", "NotificationGroupId": "Propulsion", "EntityId": "Bow Thruster Warning", "NotificationTypeId": "Warning", "WarningId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 12}}}, {"DomainDriverIdentification": {"AlarmMessage": "General alarm on Verhaar system", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON> T<PERSON>uster <PERSON>", "NotificationTypeId": "Warning", "WarningId": "MiPropulsionNotReady"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 13}}}, {"DomainDriverIdentification": {"AlarmMessage": "Emergency stop active", "NotificationGroupId": "SafetySystems", "EntityId": "<PERSON> Thruster", "NotificationTypeId": "Alarm", "AlarmId": "MiExternalEmergencyStop"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 14}}}, {"DomainDriverIdentification": {"AlarmMessage": "Modbus communication error", "NotificationGroupId": "Propulsion", "EntityId": "<PERSON> Thruster", "NotificationTypeId": "Alarm", "AlarmId": "MiCommunicationGeneral"}, "DomainSpecificConfig": {"RegisterMetaData": {"RegisterId": 1002, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 15}}}], "HeartbeatRegister": {"RegisterMetaData": {"RegisterId": 0, "RegisterType": "HoldingRegister", "RegisterValueType": "Boolean"}, "RegisterParsingData": {"BitIndex": 0}}}, "ConnectionHandlerConfig": {"protocol": "modbustcpclient", "connectionAddress": "**************", "connectionAddressOption": 502}, "Pipelines": ["Automation"]}], "RzPilotDriver": [{"DriverConfig": {"DriverIdentification": {"name": "Rz Pilot Driver", "technicalComponentId": "", "driverId": "9e575c88-f207-4b75-b8fc-2336c92e541a"}, "rebootIntervalOnFail_ms": 1000, "heartbeatInterval_ms": 1000, "MaxRsaValue": 83, "MinRsaValue": -81.6, "UsePilotToSteerManualMode": true}, "ConnectionHandlerConfig": {"protocol": "udpserver", "connectionAddress": "*************", "connectionAddressOption": 8002}, "Pipelines": ["Automation"]}]}