[{"identificationHeader": "$PM010", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM010", "destinationType": "SensorProcessor", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM010", "destinationType": "AutonomyProcessor", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM002", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM002", "destinationType": "SensorProcessor", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM030", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "500", "filterOnChange": false}, {"identificationHeader": "$PM030", "destinationType": "SensorProcessor", "destinationId": "*", "updateIntervalMillis": "500", "filterOnChange": false}, {"identificationHeader": "$PM032", "destinationType": "AutonomyProcessor", "destinationId": "*", "updateIntervalMillis": "500", "filterOnChange": false}, {"identificationHeader": "$PM004", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM006", "destinationType": "Scc", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}, {"identificationHeader": "$PM006", "destinationType": "SensorProcessor", "destinationId": "*", "updateIntervalMillis": "10000", "filterOnChange": true}]