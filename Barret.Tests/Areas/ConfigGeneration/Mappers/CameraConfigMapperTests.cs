using System;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Manufacturers.Models;
using Barret.Services.Areas.ConfigGeneration.Mappers;
using Barret.Shared.DTOs.DaVinciConfig;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Barret.Tests.Areas.ConfigGeneration.Mappers
{
    public class CameraConfigMapperTests
    {
        private readonly Mock<ILogger<CameraConfigMapper>> _loggerMock;
        private readonly CameraConfigMapper _mapper;

        public CameraConfigMapperTests()
        {
            _loggerMock = new Mock<ILogger<CameraConfigMapper>>();
            _mapper = new CameraConfigMapper(_loggerMock.Object);
        }

        [Fact]
        public void MapToCameraConfig_WithShowVideoTrue_SetsShowVideoToTrue()
        {
            // Arrange
            var manufacturer = new Manufacturer("Axis");
            var cameraModel = new DeviceModel(manufacturer, "Test Camera Model", DeviceType.Camera);
            var camera = new Camera("Test Camera", true); // Set ShowVideo to true
            camera.Model = cameraModel;

            // Act
            var result = _mapper.MapToCameraConfig(camera);

            // Assert
            Assert.True(result.ShowVideo);
        }

        [Fact]
        public void MapToCameraConfig_WithShowVideoFalse_SetsShowVideoToFalse()
        {
            // Arrange
            var manufacturer = new Manufacturer("Axis");
            var cameraModel = new DeviceModel(manufacturer, "Test Camera Model", DeviceType.Camera);
            var camera = new Camera("Test Camera", false); // Set ShowVideo to false
            camera.Model = cameraModel;

            // Act
            var result = _mapper.MapToCameraConfig(camera);

            // Assert
            Assert.False(result.ShowVideo);
        }

        [Fact]
        public void MapToConfig_CallsMapToCameraConfig()
        {
            // Arrange
            var camera = new Camera("Test Camera");

            // Act
            var result = _mapper.MapToConfig(camera);

            // Assert
            Assert.NotNull(result);
            // We can't directly verify that MapToCameraConfig was called,
            // but we can verify that the result is not null
        }

        [Fact]
        public void MapToCameraConfig_WithNullCamera_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() => _mapper.MapToCameraConfig(null));
        }
    }
}
