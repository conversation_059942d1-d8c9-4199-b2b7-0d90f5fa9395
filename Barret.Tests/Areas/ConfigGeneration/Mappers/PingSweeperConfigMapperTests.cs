using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Radars;
using Barret.Core.Areas.Devices.Models.Sensors;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Mappers;
using Barret.Shared.DTOs.DaVinciConfig.DriverConfig;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Linq;
using Xunit;

namespace Barret.Tests.Areas.ConfigGeneration.Mappers
{
    public class PingSweeperConfigMapperTests
    {
        private readonly DriverConfigMapper _mapper;
        private readonly Mock<ILogger<DriverConfigMapper>> _loggerMock;

        public PingSweeperConfigMapperTests()
        {
            _loggerMock = new Mock<ILogger<DriverConfigMapper>>();
            _mapper = new DriverConfigMapper(_loggerMock.Object);
        }

        [Fact]
        public void MapToPingSweeperConfig_DeviceWithValidConnection_AddsToConfig()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");
            var device = new GenericDevice("TestDevice")
                .WithConnection(new ConnectionHandler("*************", 80, Protocol.TcpClient));
            vessel.AddDevice(device);

            // Act
            var result = _mapper.MapToPingSweeperConfig(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result.PingDestinations);
            Assert.Equal("TestDevice", result.PingDestinations[0].Name);
            Assert.Equal("*************", result.PingDestinations[0].Ip);
        }

        [Fact]
        public void MapToPingSweeperConfig_DeviceWithEmptyIPAddress_NotAddedToConfig()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");
            var device = new GenericDevice("TestDevice")
                .WithConnection(new ConnectionHandler("", 80, Protocol.TcpClient));
            vessel.AddDevice(device);

            // Act
            var result = _mapper.MapToPingSweeperConfig(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.PingDestinations);
        }

        [Fact]
        public void MapToPingSweeperConfig_DeviceWithNullConnection_NotAddedToConfig()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");
            var device = new GenericDevice("TestDevice");
            vessel.AddDevice(device);

            // Act
            var result = _mapper.MapToPingSweeperConfig(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result.PingDestinations);
        }

        [Fact]
        public void MapToPingSweeperConfig_ConnectedDevices_AddsConnectedDeviceWithConnection()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Device without connection
            var device1 = new GenericDevice("Device1");
            vessel.AddDevice(device1);

            // Device with connection
            var device2 = new GenericDevice("Device2")
                .WithConnection(new ConnectionHandler("*************", 80, Protocol.TcpClient));
            vessel.AddDevice(device2);

            // Connect the devices
            vessel.ConnectDeviceToInterface(device1.Id, device2.Id);

            // Act
            var result = _mapper.MapToPingSweeperConfig(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.PingDestinations.Count);

            // Check that both devices are in the config
            Assert.Contains(result.PingDestinations, d => d.Name == "Device2");
            Assert.Contains(result.PingDestinations, d => d.Name.Contains("Device1") && d.Name.Contains("via"));

            // Both should have the same IP
            Assert.Equal("*************", result.PingDestinations[0].Ip);
            Assert.Equal("*************", result.PingDestinations[1].Ip);
        }

        [Fact]
        public void MapToGenericNmea0183Configs_WithSensorDevices_ReturnsConfigurations()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Add a sensor device with connection
            var sensor1 = new Sensor("Depth Sensor")
                .WithConnection(new ConnectionHandler("*************", 4001, Protocol.TcpClient));
            vessel.AddDevice(sensor1);

            // Add another sensor device with connection
            var sensor2 = new Sensor("Wind Sensor")
                .WithConnection(new ConnectionHandler("*************", 4002, Protocol.TcpClient));
            vessel.AddDevice(sensor2);

            // Add a non-sensor device (should be ignored)
            var camera = new GenericDevice("Camera");
            vessel.AddDevice(camera);

            // Act
            var result = _mapper.MapToGenericNmea0183Configs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            // Verify first sensor configuration
            var config1 = result.First(c => c.DriverConfig.DriverIdentification.Name == "Depth Sensor");
            Assert.Equal("Depth Sensor", config1.DriverConfig.DriverIdentification.Name);
            Assert.Equal(1000, config1.DriverConfig.RebootIntervalOnFailMs);
            Assert.Equal(5000, config1.DriverConfig.HeartbeatIntervalMs);
            Assert.Equal("tcpclient", config1.ConnectionHandlerConfig.Protocol);
            Assert.Equal("*************", config1.ConnectionHandlerConfig.ConnectionAddress);
            Assert.Equal(4001, config1.ConnectionHandlerConfig.ConnectionAddressOption);
            Assert.Single(config1.Pipelines);
            Assert.Equal("Generic", config1.Pipelines[0]);

            // Verify second sensor configuration
            var config2 = result.First(c => c.DriverConfig.DriverIdentification.Name == "Wind Sensor");
            Assert.Equal("Wind Sensor", config2.DriverConfig.DriverIdentification.Name);
            Assert.Equal("*************", config2.ConnectionHandlerConfig.ConnectionAddress);
            Assert.Equal(4002, config2.ConnectionHandlerConfig.ConnectionAddressOption);
        }

        [Fact]
        public void MapToGenericNmea0183Configs_WithNoSensorDevices_ReturnsEmptyList()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Add non-sensor devices
            var camera = new GenericDevice("Camera");
            var engine = new GenericDevice("Engine");
            vessel.AddDevice(camera);
            vessel.AddDevice(engine);

            // Act
            var result = _mapper.MapToGenericNmea0183Configs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void MapToGenericNmea0183Configs_WithSensorWithoutConnection_IncludesWithDefaults()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Add a sensor device without connection
            var sensor = new Sensor("GPS Sensor");
            vessel.AddDevice(sensor);

            // Act
            var result = _mapper.MapToGenericNmea0183Configs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);

            var config = result[0];
            Assert.Equal("GPS Sensor", config.DriverConfig.DriverIdentification.Name);
            // Should use default connection values when no connection is present
            Assert.Equal("tcpclient", config.ConnectionHandlerConfig.Protocol);
            Assert.Equal("127.0.0.1", config.ConnectionHandlerConfig.ConnectionAddress);
            Assert.Equal(9100, config.ConnectionHandlerConfig.ConnectionAddressOption);
        }

        [Fact]
        public void MapToEMtrackA100Configs_WithA100SensorDevices_ReturnsConfigurations()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Create a device model for A100
            var a100Model = new DeviceModel("A100", DeviceRole.Sensor);

            // Add an A100 sensor device with connection
            var a100Sensor = new Sensor("A100 AIS Sensor")
                .WithConnection(new ConnectionHandler("*************", 4003, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(a100Sensor);

            // Add another A100 sensor device
            var a100Sensor2 = new Sensor("A100 GPS Sensor")
                .WithConnection(new ConnectionHandler("*************", 4004, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(a100Sensor2);

            // Add a non-A100 sensor (should be ignored)
            var regularSensor = new Sensor("Regular Sensor")
                .WithConnection(new ConnectionHandler("192.168.1.105", 4005, Protocol.TcpClient));
            vessel.AddDevice(regularSensor);

            // Act
            var result = _mapper.MapToEMtrackA100Configs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            // Verify first A100 sensor configuration
            var config1 = result.First(c => c.DriverConfig.DriverIdentification.Name == "A100 AIS Sensor");
            Assert.Equal("A100 AIS Sensor", config1.DriverConfig.DriverIdentification.Name);
            Assert.Equal(1000, config1.DriverConfig.RebootIntervalOnFailMs);
            Assert.Equal(5000, config1.DriverConfig.HeartbeatIntervalMs);
            Assert.Equal("tcpclient", config1.ConnectionHandlerConfig.Protocol);
            Assert.Equal("*************", config1.ConnectionHandlerConfig.ConnectionAddress);
            Assert.Equal(4003, config1.ConnectionHandlerConfig.ConnectionAddressOption);
            Assert.Single(config1.Pipelines);
            Assert.Equal("Generic", config1.Pipelines[0]);

            // Verify second A100 sensor configuration
            var config2 = result.First(c => c.DriverConfig.DriverIdentification.Name == "A100 GPS Sensor");
            Assert.Equal("A100 GPS Sensor", config2.DriverConfig.DriverIdentification.Name);
            Assert.Equal("*************", config2.ConnectionHandlerConfig.ConnectionAddress);
            Assert.Equal(4004, config2.ConnectionHandlerConfig.ConnectionAddressOption);
        }

        [Fact]
        public void MapToEMtrackA100Configs_WithNoA100SensorDevices_ReturnsEmptyList()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Add non-A100 sensor devices
            var sensor1 = new Sensor("Regular Sensor 1");
            var sensor2 = new Sensor("Regular Sensor 2");
            vessel.AddDevice(sensor1);
            vessel.AddDevice(sensor2);

            // Act
            var result = _mapper.MapToEMtrackA100Configs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void MapToGenericNmea0183Configs_ExcludesA100Sensors()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Create A100 device model
            var a100Model = new DeviceModel("A100", DeviceRole.Sensor);

            // Add an A100 sensor (should be excluded)
            var a100Sensor = new Sensor("A100 Sensor")
                .WithConnection(new ConnectionHandler("192.168.1.106", 4006, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(a100Sensor);

            // Add a regular sensor (should be included)
            var regularSensor = new Sensor("Regular Sensor")
                .WithConnection(new ConnectionHandler("192.168.1.107", 4007, Protocol.TcpClient));
            vessel.AddDevice(regularSensor);

            // Add a sensor with null model (should be included in GenericNmea0183)
            var nullModelSensor = new Sensor("Null Model Sensor")
                .WithConnection(new ConnectionHandler("192.168.1.108", 4008, Protocol.TcpClient));
            vessel.AddDevice(nullModelSensor);

            // Act
            var result = _mapper.MapToGenericNmea0183Configs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Regular sensor and null model sensor should be included
            Assert.Contains(result, r => r.DriverConfig.DriverIdentification.Name == "Regular Sensor");
            Assert.Contains(result, r => r.DriverConfig.DriverIdentification.Name == "Null Model Sensor");
            Assert.DoesNotContain(result, r => r.DriverConfig.DriverIdentification.Name == "A100 Sensor");
        }

        [Fact]
        public void MapToEMtrackA100Configs_CaseInsensitiveModelMatching()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Create device models with different cases
            var a100LowerModel = new DeviceModel("a100", DeviceRole.Sensor);
            var a100UpperModel = new DeviceModel("A100", DeviceRole.Sensor);
            var a100MixedModel = new DeviceModel("A100", DeviceRole.Sensor);

            // Add sensors with different case models
            var lowerCaseSensor = new Sensor("Lower Case A100")
                .WithConnection(new ConnectionHandler("192.168.1.201", 4001, Protocol.TcpClient))
                .SetModel(a100LowerModel);
            vessel.AddDevice(lowerCaseSensor);

            var upperCaseSensor = new Sensor("Upper Case A100")
                .WithConnection(new ConnectionHandler("192.168.1.202", 4002, Protocol.TcpClient))
                .SetModel(a100UpperModel);
            vessel.AddDevice(upperCaseSensor);

            // Act
            var result = _mapper.MapToEMtrackA100Configs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Both should be included regardless of case
            Assert.Contains(result, r => r.DriverConfig.DriverIdentification.Name == "Lower Case A100");
            Assert.Contains(result, r => r.DriverConfig.DriverIdentification.Name == "Upper Case A100");
        }

        [Fact]
        public void MapToDriverConfigContainer_IncludesGenericNmea0183EmtrackA100AndPingSweeper()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Create A100 device model
            var a100Model = new DeviceModel("A100", DeviceRole.Sensor);

            // Add an A100 sensor device
            var a100Sensor = new Sensor("A100 Sensor")
                .WithConnection(new ConnectionHandler("192.168.1.108", 4008, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(a100Sensor);

            // Add a regular sensor device
            var regularSensor = new Sensor("Regular Sensor")
                .WithConnection(new ConnectionHandler("192.168.1.109", 4009, Protocol.TcpClient));
            vessel.AddDevice(regularSensor);

            // Add a generic device for PingSweeper
            var device = new GenericDevice("Test Device")
                .WithConnection(new ConnectionHandler("*************", 80, Protocol.TcpClient));
            vessel.AddDevice(device);

            // Act
            var result = _mapper.MapToDriverConfigContainer(vessel);

            // Assert
            Assert.NotNull(result);

            // Should have GenericNmea0183 configurations (only regular sensor)
            Assert.Single(result.GenericNmea0183);
            Assert.Equal("Regular Sensor", result.GenericNmea0183[0].DriverConfig.DriverIdentification.Name);

            // Should have EMtrackA100 configurations (only A100 sensor)
            Assert.Single(result.EmtrackA100);
            Assert.Equal("A100 Sensor", result.EmtrackA100[0].DriverConfig.DriverIdentification.Name);

            // Should have PingSweeper configuration (all devices with connections)
            Assert.Single(result.PingSweeper);
            Assert.Equal(3, result.PingSweeper[0].DriverConfig.PingDestinations.Count); // All three devices should be in PingSweeper
        }

        [Fact]
        public void MapToRadarConfigs_WithRadarDevices_ReturnsConfigurations()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Add a radar device with connection
            var bowRadar = new Radar("Bow Radar")
                .WithConnection(new ConnectionHandler("**************", 5345, Protocol.TcpClient));
            vessel.AddDevice(bowRadar);

            // Add another radar device with connection
            var sternRadar = new Radar("Stern Radar")
                .WithConnection(new ConnectionHandler("**************", 5346, Protocol.TcpClient));
            vessel.AddDevice(sternRadar);

            // Add a non-radar device (should be ignored)
            var sensor = new Sensor("GPS Sensor")
                .WithConnection(new ConnectionHandler("*************", 4001, Protocol.TcpClient));
            vessel.AddDevice(sensor);

            // Act
            var result = _mapper.MapToRadarConfigs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            // Verify first radar configuration
            var config1 = result.First(c => c.DriverConfig.DriverIdentification.Name == "Bow Radar");
            Assert.Equal("Bow Radar", config1.DriverConfig.DriverIdentification.Name);
            Assert.Equal(1000, config1.DriverConfig.RebootIntervalOnFailMs);
            Assert.Equal(5000, config1.DriverConfig.HeartbeatIntervalMs);
            Assert.Equal("tcpclient", config1.ConnectionHandlerConfig.Protocol);
            Assert.Equal("**************", config1.ConnectionHandlerConfig.ConnectionAddress);
            Assert.Equal(5345, config1.ConnectionHandlerConfig.ConnectionAddressOption);
            Assert.Single(config1.Pipelines);
            Assert.Equal("Radar", config1.Pipelines[0]);

            // Verify radar-specific properties
            var radarDriverConfig1 = Assert.IsType<RadarDriverConfigDto>(config1.DriverConfig);
            Assert.Equal("Bow", radarDriverConfig1.RadarLocation);

            // Verify second radar configuration
            var config2 = result.First(c => c.DriverConfig.DriverIdentification.Name == "Stern Radar");
            Assert.Equal("Stern Radar", config2.DriverConfig.DriverIdentification.Name);
            Assert.Equal("**************", config2.ConnectionHandlerConfig.ConnectionAddress);
            Assert.Equal(5346, config2.ConnectionHandlerConfig.ConnectionAddressOption);

            // Verify radar-specific properties
            var radarDriverConfig2 = Assert.IsType<RadarDriverConfigDto>(config2.DriverConfig);
            Assert.Equal("Stern", radarDriverConfig2.RadarLocation);
        }

        [Fact]
        public void MapToRadarConfigs_WithNoRadarDevices_ReturnsEmptyList()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Add non-radar devices
            var sensor = new Sensor("GPS Sensor");
            var camera = new GenericDevice("Camera");
            vessel.AddDevice(sensor);
            vessel.AddDevice(camera);

            // Act
            var result = _mapper.MapToRadarConfigs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void MapToRadarConfigs_WithRadarWithoutConnection_IncludesWithDefaults()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Add a radar device without connection
            var radar = new Radar("Port Radar");
            vessel.AddDevice(radar);

            // Act
            var result = _mapper.MapToRadarConfigs(vessel);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);

            var config = result[0];
            Assert.Equal("Port Radar", config.DriverConfig.DriverIdentification.Name);
            // Should use default connection values when no connection is present
            Assert.Equal("tcpclient", config.ConnectionHandlerConfig.Protocol);
            Assert.Equal("127.0.0.1", config.ConnectionHandlerConfig.ConnectionAddress);
            Assert.Equal(9100, config.ConnectionHandlerConfig.ConnectionAddressOption);

            // Verify radar-specific properties
            var radarDriverConfig = Assert.IsType<RadarDriverConfigDto>(config.DriverConfig);
            Assert.Equal("Port", radarDriverConfig.RadarLocation);
        }

        [Fact]
        public void MapToDriverConfigContainer_IncludesRadarConfigurations()
        {
            // Arrange
            var vessel = new Vessel("TestVessel");

            // Add a radar device
            var radar = new Radar("Navigation Radar")
                .WithConnection(new ConnectionHandler("**************", 5350, Protocol.TcpClient));
            vessel.AddDevice(radar);

            // Add a sensor device for comparison
            var sensor = new Sensor("GPS Sensor")
                .WithConnection(new ConnectionHandler("*************", 4001, Protocol.TcpClient));
            vessel.AddDevice(sensor);

            // Act
            var result = _mapper.MapToDriverConfigContainer(vessel);

            // Assert
            Assert.NotNull(result);

            // Should have Radar configurations
            Assert.Single(result.Radar);
            Assert.Equal("Navigation Radar", result.Radar[0].DriverConfig.DriverIdentification.Name);

            // Should also have GenericNmea0183 configurations for sensor
            Assert.Single(result.GenericNmea0183);
            Assert.Equal("GPS Sensor", result.GenericNmea0183[0].DriverConfig.DriverIdentification.Name);

            // Should have PingSweeper configuration (all devices with connections)
            Assert.Single(result.PingSweeper);
            Assert.Equal(2, result.PingSweeper[0].DriverConfig.PingDestinations.Count);
        }
    }
}
