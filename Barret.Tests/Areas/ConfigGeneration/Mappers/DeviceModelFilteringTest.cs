using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Devices.Models.Sensors;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Mappers;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Linq;
using Xunit;

namespace Barret.Tests.Areas.ConfigGeneration.Mappers
{
    /// <summary>
    /// Test to verify that device model filtering works correctly and prevents duplication
    /// </summary>
    public class DeviceModelFilteringTest
    {
        private readonly Mock<ILogger<DriverConfigMapper>> _mockLogger;
        private readonly DriverConfigMapper _mapper;

        public DeviceModelFilteringTest()
        {
            _mockLogger = new Mock<ILogger<DriverConfigMapper>>();
            _mapper = new DriverConfigMapper(_mockLogger.Object);
        }

        [Fact]
        public void DeviceModelFiltering_PreventsDuplication_BetweenGenericNmea0183AndEMtrackA100()
        {
            // Arrange
            var vessel = new Vessel("Test Vessel");

            // Create device models
            var a100Model = new DeviceModel("A100", DeviceRole.Sensor);
            var regularModel = new DeviceModel("Regular", DeviceRole.Sensor);

            // Add A100 sensor (should go to EMtrackA100 only)
            var a100Sensor = new Sensor("AIS A100 Sensor")
                .WithConnection(new ConnectionHandler("*************", 4001, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(a100Sensor);

            // Add regular sensor (should go to GenericNmea0183 only)
            var regularSensor = new Sensor("GPS Sensor")
                .WithConnection(new ConnectionHandler("*************", 4002, Protocol.TcpClient))
                .SetModel(regularModel);
            vessel.AddDevice(regularSensor);

            // Add sensor with null model (should go to GenericNmea0183 only)
            var nullModelSensor = new Sensor("Compass Sensor")
                .WithConnection(new ConnectionHandler("*************", 4003, Protocol.TcpClient));
            vessel.AddDevice(nullModelSensor);

            // Act
            var genericNmea0183Configs = _mapper.MapToGenericNmea0183Configs(vessel);
            var emtrackA100Configs = _mapper.MapToEMtrackA100Configs(vessel);
            var driverConfigContainer = _mapper.MapToDriverConfigContainer(vessel);

            // Assert - No duplication between configurations
            Assert.NotNull(genericNmea0183Configs);
            Assert.NotNull(emtrackA100Configs);
            Assert.NotNull(driverConfigContainer);

            // GenericNmea0183 should contain only non-A100 sensors
            Assert.Equal(2, genericNmea0183Configs.Count);
            Assert.Contains(genericNmea0183Configs, c => c.DriverConfig.DriverIdentification.Name == "GPS Sensor");
            Assert.Contains(genericNmea0183Configs, c => c.DriverConfig.DriverIdentification.Name == "Compass Sensor");
            Assert.DoesNotContain(genericNmea0183Configs, c => c.DriverConfig.DriverIdentification.Name == "AIS A100 Sensor");

            // EMtrackA100 should contain only A100 sensors
            Assert.Single(emtrackA100Configs);
            Assert.Contains(emtrackA100Configs, c => c.DriverConfig.DriverIdentification.Name == "AIS A100 Sensor");
            Assert.DoesNotContain(emtrackA100Configs, c => c.DriverConfig.DriverIdentification.Name == "GPS Sensor");
            Assert.DoesNotContain(emtrackA100Configs, c => c.DriverConfig.DriverIdentification.Name == "Compass Sensor");

            // Driver config container should have correct counts
            Assert.Equal(2, driverConfigContainer.GenericNmea0183.Count);
            Assert.Single(driverConfigContainer.EmtrackA100);
            Assert.Single(driverConfigContainer.PingSweeper); // All devices should be in PingSweeper

            // Verify total sensor count matches
            var totalSensorConfigs = driverConfigContainer.GenericNmea0183.Count + driverConfigContainer.EmtrackA100.Count;
            Assert.Equal(3, totalSensorConfigs); // Should equal total number of sensor devices

            // Verify no sensor appears in both configurations
            var genericNames = driverConfigContainer.GenericNmea0183.Select(c => c.DriverConfig.DriverIdentification.Name).ToList();
            var emtrackNames = driverConfigContainer.EmtrackA100.Select(c => c.DriverConfig.DriverIdentification.Name).ToList();

            Assert.Empty(genericNames.Intersect(emtrackNames)); // No overlap between configurations
        }

        [Fact]
        public void DeviceModelFiltering_CaseInsensitive_A100Matching()
        {
            // Arrange
            var vessel = new Vessel("Test Vessel");

            // Create A100 models with different cases
            var a100LowerModel = new DeviceModel("a100", DeviceRole.Sensor);
            var a100UpperModel = new DeviceModel("A100", DeviceRole.Sensor);

            // Add sensors with different case A100 models
            var lowerCaseSensor = new Sensor("Lower Case A100")
                .WithConnection(new ConnectionHandler("192.168.1.200", 4001, Protocol.TcpClient))
                .SetModel(a100LowerModel);
            vessel.AddDevice(lowerCaseSensor);

            var upperCaseSensor = new Sensor("Upper Case A100")
                .WithConnection(new ConnectionHandler("192.168.1.201", 4002, Protocol.TcpClient))
                .SetModel(a100UpperModel);
            vessel.AddDevice(upperCaseSensor);

            // Act
            var genericNmea0183Configs = _mapper.MapToGenericNmea0183Configs(vessel);
            var emtrackA100Configs = _mapper.MapToEMtrackA100Configs(vessel);

            // Assert - Both should be treated as A100 regardless of case
            Assert.Empty(genericNmea0183Configs); // No sensors should go to GenericNmea0183
            Assert.Equal(2, emtrackA100Configs.Count); // Both should go to EMtrackA100

            Assert.Contains(emtrackA100Configs, c => c.DriverConfig.DriverIdentification.Name == "Lower Case A100");
            Assert.Contains(emtrackA100Configs, c => c.DriverConfig.DriverIdentification.Name == "Upper Case A100");
        }

        [Fact]
        public void PingSweeperConfiguration_IncludesAllDevicesWithConnections()
        {
            // Arrange
            var vessel = new Vessel("Test Vessel");

            // Create device models
            var a100Model = new DeviceModel("A100", DeviceRole.Sensor);
            var regularModel = new DeviceModel("Regular", DeviceRole.Sensor);

            // Add A100 sensor with connection (should be in EMtrackA100 AND PingSweeper)
            var a100Sensor = new Sensor("A100 AIS Sensor")
                .WithConnection(new ConnectionHandler("*************", 4001, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(a100Sensor);

            // Add regular sensor with connection (should be in GenericNmea0183 AND PingSweeper)
            var regularSensor = new Sensor("GPS Sensor")
                .WithConnection(new ConnectionHandler("*************", 4002, Protocol.TcpClient))
                .SetModel(regularModel);
            vessel.AddDevice(regularSensor);

            // Add sensor with null model and connection (should be in GenericNmea0183 AND PingSweeper)
            var nullModelSensor = new Sensor("Compass Sensor")
                .WithConnection(new ConnectionHandler("*************", 4003, Protocol.TcpClient));
            vessel.AddDevice(nullModelSensor);

            // Add device without connection (should NOT be in PingSweeper)
            var noConnectionSensor = new Sensor("Internal Sensor");
            vessel.AddDevice(noConnectionSensor);

            // Act
            var driverConfigContainer = _mapper.MapToDriverConfigContainer(vessel);

            // Assert - Sensor routing validation
            Assert.Equal(2, driverConfigContainer.GenericNmea0183.Count); // GPS + Compass
            Assert.Single(driverConfigContainer.EmtrackA100); // A100 AIS
            Assert.Single(driverConfigContainer.PingSweeper); // Should have one PingSweeper config

            // Assert - PingSweeper should include all devices with connections
            var pingSweeperConfig = driverConfigContainer.PingSweeper[0];
            Assert.Equal(3, pingSweeperConfig.DriverConfig.PingDestinations.Count); // All devices with connections

            // Verify specific devices are in PingSweeper
            var pingDestinations = pingSweeperConfig.DriverConfig.PingDestinations;
            Assert.Contains(pingDestinations, d => d.Name == "A100 AIS Sensor" && d.Ip == "*************");
            Assert.Contains(pingDestinations, d => d.Name == "GPS Sensor" && d.Ip == "*************");
            Assert.Contains(pingDestinations, d => d.Name == "Compass Sensor" && d.Ip == "*************");

            // Verify device without connection is NOT in PingSweeper
            Assert.DoesNotContain(pingDestinations, d => d.Name == "Internal Sensor");
        }

        [Fact]
        public void CompleteDriverConfiguration_NoSensorDuplication_ProperPingSweeper()
        {
            // Arrange
            var vessel = new Vessel("Test Vessel");

            // Create device models
            var a100Model = new DeviceModel("A100", DeviceRole.Sensor);

            // Add multiple A100 sensors
            var a100Sensor1 = new Sensor("A100 AIS")
                .WithConnection(new ConnectionHandler("*************", 4001, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(a100Sensor1);

            var a100Sensor2 = new Sensor("A100 GPS")
                .WithConnection(new ConnectionHandler("*************", 4002, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(a100Sensor2);

            // Add multiple regular sensors
            var regularSensor1 = new Sensor("Standard GPS")
                .WithConnection(new ConnectionHandler("*************", 4003, Protocol.TcpClient));
            vessel.AddDevice(regularSensor1);

            var regularSensor2 = new Sensor("Standard Compass")
                .WithConnection(new ConnectionHandler("*************", 4004, Protocol.TcpClient));
            vessel.AddDevice(regularSensor2);

            // Act
            var driverConfigContainer = _mapper.MapToDriverConfigContainer(vessel);

            // Assert - No sensor duplication
            var allSensorNames = new List<string>();
            allSensorNames.AddRange(driverConfigContainer.GenericNmea0183.Select(c => c.DriverConfig.DriverIdentification.Name));
            allSensorNames.AddRange(driverConfigContainer.EmtrackA100.Select(c => c.DriverConfig.DriverIdentification.Name));

            // Check for duplicates
            var duplicates = allSensorNames.GroupBy(name => name).Where(g => g.Count() > 1).Select(g => g.Key).ToList();
            Assert.Empty(duplicates); // No sensor should appear in multiple configurations

            // Assert - Correct routing
            Assert.Equal(2, driverConfigContainer.GenericNmea0183.Count); // Regular sensors
            Assert.Equal(2, driverConfigContainer.EmtrackA100.Count); // A100 sensors
            Assert.Single(driverConfigContainer.PingSweeper); // One PingSweeper config

            // Assert - PingSweeper includes all devices with connections
            var pingSweeperConfig = driverConfigContainer.PingSweeper[0];
            Assert.Equal(4, pingSweeperConfig.DriverConfig.PingDestinations.Count); // All 4 sensors have connections

            // Verify total sensor count
            var totalSensorConfigs = driverConfigContainer.GenericNmea0183.Count + driverConfigContainer.EmtrackA100.Count;
            Assert.Equal(4, totalSensorConfigs); // Should equal total number of sensor devices
        }

        [Fact]
        public void VesselDeviceRetrieval_NoDuplication_CorrectCounts()
        {
            // Arrange
            var vessel = new Vessel("Test Vessel");

            // Create device models
            var a100Model = new DeviceModel("A100", DeviceRole.Sensor);
            var regularModel = new DeviceModel("Regular", DeviceRole.Sensor);

            // Add sensors
            var sensor1 = new Sensor("Sensor 1")
                .WithConnection(new ConnectionHandler("*************", 4001, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(sensor1);

            var sensor2 = new Sensor("Sensor 2")
                .WithConnection(new ConnectionHandler("*************", 4002, Protocol.TcpClient))
                .SetModel(regularModel);
            vessel.AddDevice(sensor2);

            var sensor3 = new Sensor("Sensor 3")
                .WithConnection(new ConnectionHandler("*************", 4003, Protocol.TcpClient));
            vessel.AddDevice(sensor3);

            // Act - Get devices multiple times to check for consistency
            var allDevices1 = vessel.GetAllDevices().ToList();
            var allDevices2 = vessel.GetAllDevices().ToList();
            var sensorDevices1 = allDevices1.Where(d => d.DeviceRole == DeviceRole.Sensor).ToList();
            var sensorDevices2 = allDevices2.Where(d => d.DeviceRole == DeviceRole.Sensor).ToList();

            // Assert - Device counts should be consistent
            Assert.Equal(3, allDevices1.Count); // Should have exactly 3 devices
            Assert.Equal(3, allDevices2.Count); // Should be consistent across calls
            Assert.Equal(3, sensorDevices1.Count); // All devices are sensors
            Assert.Equal(3, sensorDevices2.Count); // Should be consistent

            // Assert - No duplicate device IDs
            var deviceIds = allDevices1.Select(d => d.Id).ToList();
            var uniqueDeviceIds = deviceIds.Distinct().ToList();
            Assert.Equal(deviceIds.Count, uniqueDeviceIds.Count); // No duplicates

            // Assert - Device names should be unique
            var deviceNames = allDevices1.Select(d => d.Name).ToList();
            var uniqueDeviceNames = deviceNames.Distinct().ToList();
            Assert.Equal(deviceNames.Count, uniqueDeviceNames.Count); // No duplicate names

            // Act - Test individual mapping methods
            var genericConfigs = _mapper.MapToGenericNmea0183Configs(vessel);
            var emtrackConfigs = _mapper.MapToEMtrackA100Configs(vessel);

            // Assert - Mapping should not triple the count
            Assert.Equal(2, genericConfigs.Count); // Sensor 2 and Sensor 3 (non-A100)
            Assert.Single(emtrackConfigs); // Sensor 1 (A100)

            // Verify total equals original sensor count
            var totalMappedSensors = genericConfigs.Count + emtrackConfigs.Count;
            Assert.Equal(sensorDevices1.Count, totalMappedSensors);
        }
    }
}
