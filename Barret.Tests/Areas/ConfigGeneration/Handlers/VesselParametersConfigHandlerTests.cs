using System;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Handlers;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.DTOs.DaVinciConfig;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Barret.Tests.Areas.ConfigGeneration.Handlers
{
    public class VesselParametersConfigHandlerTests
    {
        private readonly Mock<ILogger<VesselParametersConfigHandler>> _loggerMock;
        private readonly Mock<IVesselParametersConfigMapper> _mapperMock;
        private readonly string _testRelativePath = "VesselParametersConfigs/VesselParameters.json";
        private readonly string _testTemplatePath;
        private readonly string _testOutputPath;
        private readonly Vessel _testVessel;

        public VesselParametersConfigHandlerTests()
        {
            _loggerMock = new Mock<ILogger<VesselParametersConfigHandler>>();
            _mapperMock = new Mock<IVesselParametersConfigMapper>();

            // Create a temporary directory for the test
            var tempDir = Path.Combine(Path.GetTempPath(), "BarretTests", Guid.NewGuid().ToString());
            Directory.CreateDirectory(tempDir);
            Directory.CreateDirectory(Path.Combine(tempDir, "Templates"));
            Directory.CreateDirectory(Path.Combine(tempDir, "Output"));
            Directory.CreateDirectory(Path.Combine(tempDir, "Templates", "VesselParametersConfigs"));

            _testTemplatePath = Path.Combine(tempDir, "Templates", _testRelativePath);
            _testOutputPath = Path.Combine(tempDir, "Output", _testRelativePath);

            // Create a test vessel
            _testVessel = new Vessel
            {
                Name = "Test Vessel",
                MMSI = "123456789"
            };

            // Create a test template file
            File.WriteAllText(_testTemplatePath, @"{
    ""VesselMmsi"": """",
    ""VesselName"": """",
    ""SeqApiKey"": """"
}");

            // Set up the mapper mock
            _mapperMock.Setup(m => m.MapToConfig(It.IsAny<Vessel>()))
                .Returns((Vessel v) => new VesselParametersConfigDto
                {
                    VesselMmsi = v.MMSI,
                    VesselName = v.Name,
                    SeqApiKey = string.Empty
                });
        }

        [Fact]
        public async Task GenerateConfigurationAsync_ShouldCreateConfigWithCorrectValues()
        {
            // Arrange
            var handler = new VesselParametersConfigHandler(_mapperMock.Object, _loggerMock.Object);
            var template = new ConfigurationTemplate
            {
                Path = _testRelativePath,
                OutputPath = _testRelativePath,
                Handler = "VesselParametersConfig"
            };
            var context = new ConfigurationContext(
                _testVessel,
                Path.GetDirectoryName(_testTemplatePath),
                Path.GetDirectoryName(_testOutputPath),
                new System.Threading.CancellationTokenSource());

            // Act
            var result = await handler.GenerateConfigurationAsync(context, template);

            // Assert
            Assert.True(result.Success);
            Assert.Equal(_testOutputPath, result.OutputPath);
            Assert.True(File.Exists(_testOutputPath));

            // Verify the content of the generated file
            var content = await File.ReadAllTextAsync(_testOutputPath);
            var config = JsonSerializer.Deserialize<VesselParametersConfigDto>(content, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });

            Assert.NotNull(config);
            Assert.Equal(_testVessel.MMSI, config.VesselMmsi);
            Assert.Equal(_testVessel.Name, config.VesselName);
            Assert.Equal(string.Empty, config.SeqApiKey);

            // Verify that the mapper was called
            _mapperMock.Verify(m => m.MapToConfig(_testVessel), Times.Once);
        }

        [Fact]
        public async Task GenerateConfigurationAsync_ShouldHandleCancellation()
        {
            // Arrange
            var handler = new VesselParametersConfigHandler(_mapperMock.Object, _loggerMock.Object);
            var template = new ConfigurationTemplate
            {
                Path = _testRelativePath,
                OutputPath = _testRelativePath,
                Handler = "VesselParametersConfig"
            };
            var cts = new System.Threading.CancellationTokenSource();
            var context = new ConfigurationContext(
                _testVessel,
                Path.GetDirectoryName(_testTemplatePath),
                Path.GetDirectoryName(_testOutputPath),
                cts);

            // Cancel the operation
            cts.Cancel();

            // Act
            var result = await handler.GenerateConfigurationAsync(context, template);

            // Assert
            Assert.False(result.Success);
            Assert.Contains("cancelled", result.ErrorMessage);
        }
    }
}
