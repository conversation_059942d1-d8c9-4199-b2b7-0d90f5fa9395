using System;
using System.IO;
using System.Threading.Tasks;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Handlers;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Barret.Tests.Areas.ConfigGeneration.Handlers
{
    public class StaticConfigHandlerTests
    {
        private readonly Mock<ILogger<StaticConfigHandler>> _loggerMock;
        private readonly string _testConfigType = "TestConfig";
        private readonly string _testRelativePath = "TestConfigs/TestConfig.json";
        private readonly string _testTemplatePath;
        private readonly string _testOutputPath;
        private readonly string _testTemplateContent = @"{
  ""testSetting"": ""testValue"",
  ""enabled"": true,
  ""count"": 42
}";

        public StaticConfigHandlerTests()
        {
            _loggerMock = new Mock<ILogger<StaticConfigHandler>>();

            // Create a temporary template file for testing
            _testTemplatePath = Path.Combine(Path.GetTempPath(), $"test_template_{Guid.NewGuid()}.json");
            File.WriteAllText(_testTemplatePath, _testTemplateContent);

            // Create a temporary output path for testing
            _testOutputPath = Path.Combine(Path.GetTempPath(), $"test_output_{Guid.NewGuid()}.json");
        }

        [Fact]
        public async Task GenerateConfigurationAsync_WithValidTemplate_CopiesFile()
        {
            // Arrange
            var handler = new StaticConfigHandler(_loggerMock.Object, _testConfigType, _testRelativePath);
            var vessel = new Vessel();

            try
            {
                // Act
                var result = await handler.GenerateConfigurationAsync(_testTemplatePath, _testOutputPath, vessel);

                // Assert
                Assert.True(result.Success);
                Assert.True(File.Exists(_testOutputPath));
                Assert.Equal(_testTemplateContent, await File.ReadAllTextAsync(_testOutputPath));
            }
            finally
            {
                // Clean up
                if (File.Exists(_testTemplatePath))
                {
                    File.Delete(_testTemplatePath);
                }
                if (File.Exists(_testOutputPath))
                {
                    File.Delete(_testOutputPath);
                }
            }
        }

        [Fact]
        public async Task GenerateConfigurationAsync_WithNonExistentTemplate_ReturnsFailure()
        {
            // Arrange
            var handler = new StaticConfigHandler(_loggerMock.Object, _testConfigType, _testRelativePath);
            var vessel = new Vessel();
            var nonExistentPath = Path.Combine(Path.GetTempPath(), $"non_existent_{Guid.NewGuid()}.json");

            // Act
            var result = await handler.GenerateConfigurationAsync(nonExistentPath, _testOutputPath, vessel);

            // Assert
            Assert.False(result.Success);
            Assert.NotNull(result.ErrorMessage);
            Assert.Contains("Template file not found", result.ErrorMessage);
        }

        [Fact]
        public void Constructor_WithEmptyConfigType_ThrowsArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() =>
                new StaticConfigHandler(_loggerMock.Object, "", _testRelativePath));

            Assert.Contains("Config type cannot be empty", exception.Message);
        }

        [Fact]
        public void Constructor_WithEmptyRelativePath_ThrowsArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() =>
                new StaticConfigHandler(_loggerMock.Object, _testConfigType, ""));

            Assert.Contains("Relative path cannot be empty", exception.Message);
        }

        [Fact]
        public void Constructor_WithNullLogger_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new StaticConfigHandler(null, _testConfigType, _testRelativePath));
        }

        [Fact]
        public void ConfigType_ReturnsCorrectValue()
        {
            // Arrange
            var handler = new StaticConfigHandler(_loggerMock.Object, _testConfigType, _testRelativePath);

            // Act & Assert
            Assert.Equal(_testConfigType, handler.ConfigType);
        }

        [Fact]
        public void RelativePath_ReturnsCorrectValue()
        {
            // Arrange
            var handler = new StaticConfigHandler(_loggerMock.Object, _testConfigType, _testRelativePath);

            // Act & Assert
            Assert.Equal(_testRelativePath, handler.RelativePath);
        }
    }
}
