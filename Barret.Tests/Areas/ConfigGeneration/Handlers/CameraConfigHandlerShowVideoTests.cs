using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Manufacturers.Models;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Handlers;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Mappers;
using Barret.Shared.DTOs.DaVinciConfig;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Barret.Tests.Areas.ConfigGeneration.Handlers
{
    public class CameraConfigHandlerShowVideoTests
    {
        private readonly Mock<ILogger<CameraConfigHandler>> _loggerMock;
        private readonly Mock<ILogger<CameraConfigMapper>> _mapperLoggerMock;
        private readonly string _testTemplatePath = "dummy_template_path.json";
        private readonly string _testOutputPath;

        public CameraConfigHandlerShowVideoTests()
        {
            _loggerMock = new Mock<ILogger<CameraConfigHandler>>();
            _mapperLoggerMock = new Mock<ILogger<CameraConfigMapper>>();
            _testOutputPath = Path.Combine(Path.GetTempPath(), $"test_output_{Guid.NewGuid()}.json");
        }

        [Fact]
        public async Task GenerateConfigurationAsync_WithShowVideoTrue_PreservesShowVideoValue()
        {
            // Arrange
            var mapper = new CameraConfigMapper(_mapperLoggerMock.Object);
            var handler = new CameraConfigHandler(_loggerMock.Object, mapper);
            var vessel = CreateTestVesselWithCameras(true); // Create cameras with ShowVideo = true

            // Create a configuration context and template
            var context = new ConfigurationContext(vessel, "output_dir");
            var template = new ConfigurationTemplate
            {
                TemplatePath = _testTemplatePath,
                OutputPath = _testOutputPath
            };

            try
            {
                // Act
                var result = await handler.GenerateConfigurationAsync(context, template);

                // Assert
                Assert.True(result.Success);
                Assert.True(File.Exists(_testOutputPath));

                // Verify the content is valid JSON and contains camera configs with ShowVideo = true
                var content = await File.ReadAllTextAsync(_testOutputPath);
                var cameraConfigs = JsonSerializer.Deserialize<List<CameraConfigDto>>(content);
                Assert.NotNull(cameraConfigs);
                Assert.Equal(2, cameraConfigs.Count);
                Assert.All(cameraConfigs, config => Assert.True(config.ShowVideo));
            }
            finally
            {
                // Clean up
                if (File.Exists(_testOutputPath))
                {
                    File.Delete(_testOutputPath);
                }
            }
        }

        [Fact]
        public async Task GenerateConfigurationAsync_WithShowVideoFalse_PreservesShowVideoValue()
        {
            // Arrange
            var mapper = new CameraConfigMapper(_mapperLoggerMock.Object);
            var handler = new CameraConfigHandler(_loggerMock.Object, mapper);
            var vessel = CreateTestVesselWithCameras(false); // Create cameras with ShowVideo = false

            // Create a configuration context and template
            var context = new ConfigurationContext(vessel, "output_dir");
            var template = new ConfigurationTemplate
            {
                TemplatePath = _testTemplatePath,
                OutputPath = _testOutputPath
            };

            try
            {
                // Act
                var result = await handler.GenerateConfigurationAsync(context, template);

                // Assert
                Assert.True(result.Success);
                Assert.True(File.Exists(_testOutputPath));

                // Verify the content is valid JSON and contains camera configs with ShowVideo = false
                var content = await File.ReadAllTextAsync(_testOutputPath);
                var cameraConfigs = JsonSerializer.Deserialize<List<CameraConfigDto>>(content);
                Assert.NotNull(cameraConfigs);
                Assert.Equal(2, cameraConfigs.Count);
                Assert.All(cameraConfigs, config => Assert.False(config.ShowVideo));
            }
            finally
            {
                // Clean up
                if (File.Exists(_testOutputPath))
                {
                    File.Delete(_testOutputPath);
                }
            }
        }

        private Vessel CreateTestVesselWithCameras(bool showVideo)
        {
            var vessel = new Vessel("TEST-VESSEL", "12345678", "123456789", "Test Vessel");

            // Create manufacturer
            var manufacturer = new Manufacturer("Axis");

            // Create camera model
            var cameraModel = new DeviceModel(manufacturer, "Test Camera Model", DeviceType.Camera);

            // Create cameras with specified ShowVideo value
            var camera1 = new Camera("Camera 1", showVideo);
            camera1.DeviceRole = DeviceRole.Camera;
            camera1.Model = cameraModel;

            var camera2 = new Camera("Camera 2", showVideo);
            camera2.DeviceRole = DeviceRole.Camera;
            camera2.Model = cameraModel;

            // Add cameras to vessel
            vessel.AddDevice(camera1);
            vessel.AddDevice(camera2);

            return vessel;
        }
    }
}
