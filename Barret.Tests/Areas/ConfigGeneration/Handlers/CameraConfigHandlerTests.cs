using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Manufacturers.Models;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Handlers;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Shared.DTOs.DaVinciConfig;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Barret.Tests.Areas.ConfigGeneration.Handlers
{
    public class CameraConfigHandlerTests
    {
        private readonly Mock<ILogger<CameraConfigHandler>> _loggerMock;
        private readonly Mock<ICameraConfigMapper> _cameraConfigMapperMock;
        private readonly string _relativePath = "CameraConfigs/CameraConfig.json";
        private readonly string _testTemplatePath = "dummy_template_path.json"; // Not used by the handler
        private readonly string _testOutputPath;

        public CameraConfigHandlerTests()
        {
            _loggerMock = new Mock<ILogger<CameraConfigHandler>>();
            _cameraConfigMapperMock = new Mock<ICameraConfigMapper>();
            _testOutputPath = Path.Combine(Path.GetTempPath(), $"test_output_{Guid.NewGuid()}.json");
        }

        [Fact]
        public async Task GenerateConfigurationAsync_WithCameras_WritesConfigToFile()
        {
            // Arrange
            var handler = new CameraConfigHandler(_loggerMock.Object, _cameraConfigMapperMock.Object, _relativePath);
            var vessel = CreateTestVesselWithCameras();

            // Setup mapper mock
            _cameraConfigMapperMock.Setup(m => m.MapToCameraConfig(It.IsAny<Camera>()))
                .Returns(new CameraConfigDto
                {
                    Name = "Test Camera",
                    Ip = "*************",
                    Brand = "Axis",
                    TechnicalComponentId = "",
                    ShowVideo = false,
                    Credentials = new CameraApiCredentialsDto("admin", "encrypted_password")
                });

            try
            {
                // Act
                var result = await handler.GenerateConfigurationAsync(_testTemplatePath, _testOutputPath, vessel);

                // Assert
                Assert.True(result.Success);
                Assert.True(File.Exists(_testOutputPath));

                // Verify the content is valid JSON and contains camera configs
                var content = await File.ReadAllTextAsync(_testOutputPath);
                var cameraConfigs = JsonSerializer.Deserialize<List<CameraConfigDto>>(content);
                Assert.NotNull(cameraConfigs);
                Assert.Equal(2, cameraConfigs.Count);

                // Verify mapper was called for each camera
                _cameraConfigMapperMock.Verify(m => m.MapToCameraConfig(It.IsAny<Camera>()), Times.Exactly(2));
            }
            finally
            {
                // Clean up
                if (File.Exists(_testOutputPath))
                {
                    File.Delete(_testOutputPath);
                }
            }
        }

        [Fact]
        public async Task GenerateConfigurationAsync_WithNoCameras_WritesEmptyArrayToFile()
        {
            // Arrange
            var handler = new CameraConfigHandler(_loggerMock.Object, _cameraConfigMapperMock.Object, _relativePath);
            var vessel = new Vessel(); // Empty vessel with no cameras

            try
            {
                // Act
                var result = await handler.GenerateConfigurationAsync(_testTemplatePath, _testOutputPath, vessel);

                // Assert
                Assert.True(result.Success);
                Assert.True(File.Exists(_testOutputPath));
                Assert.Equal("[]", await File.ReadAllTextAsync(_testOutputPath));

                // Verify mapper was not called
                _cameraConfigMapperMock.Verify(m => m.MapToCameraConfig(It.IsAny<Camera>()), Times.Never);
            }
            finally
            {
                // Clean up
                if (File.Exists(_testOutputPath))
                {
                    File.Delete(_testOutputPath);
                }
            }
        }

        [Fact]
        public async Task GenerateConfigurationAsync_WithMixedDevices_OnlyIncludesCameras()
        {
            // Arrange
            var handler = new CameraConfigHandler(_loggerMock.Object, _cameraConfigMapperMock.Object, _relativePath);
            var vessel = CreateTestVesselWithMixedDevices();

            // Setup mapper mocks
            _cameraConfigMapperMock.Setup(m => m.MapToCameraConfig(It.IsAny<Camera>()))
                .Returns(new CameraConfigDto { Name = "Camera" });

            try
            {
                // Act
                var result = await handler.GenerateConfigurationAsync(_testTemplatePath, _testOutputPath, vessel);

                // Assert
                Assert.True(result.Success);
                Assert.True(File.Exists(_testOutputPath));

                // Verify only the camera mapper was called, and only once (for the one camera in the mixed vessel)
                _cameraConfigMapperMock.Verify(m => m.MapToCameraConfig(It.IsAny<Camera>()), Times.Once);

                // Verify the content is valid JSON and contains only camera configs
                var content = await File.ReadAllTextAsync(_testOutputPath);
                var cameraConfigs = JsonSerializer.Deserialize<List<CameraConfigDto>>(content);
                Assert.NotNull(cameraConfigs);
                Assert.Single(cameraConfigs); // Only one camera should be included
            }
            finally
            {
                // Clean up
                if (File.Exists(_testOutputPath))
                {
                    File.Delete(_testOutputPath);
                }
            }
        }

        [Fact]
        public void Constructor_WithNullLogger_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new CameraConfigHandler(null, _cameraConfigMapperMock.Object, _relativePath));
        }

        [Fact]
        public void Constructor_WithNullMapper_ThrowsArgumentNullException()
        {
            // Act & Assert
            Assert.Throws<ArgumentNullException>(() =>
                new CameraConfigHandler(_loggerMock.Object, null, _relativePath));
        }

        [Fact]
        public void Constructor_WithEmptyRelativePath_ThrowsArgumentException()
        {
            // Act & Assert
            Assert.Throws<ArgumentException>(() =>
                new CameraConfigHandler(_loggerMock.Object, _cameraConfigMapperMock.Object, ""));
        }

        [Fact]
        public void ConfigType_ReturnsCamera()
        {
            // Arrange
            var handler = new CameraConfigHandler(_loggerMock.Object, _cameraConfigMapperMock.Object, _relativePath);

            // Act & Assert
            Assert.Equal("Camera", handler.ConfigType);
        }

        [Fact]
        public void RelativePath_ReturnsCorrectValue()
        {
            // Arrange
            var handler = new CameraConfigHandler(_loggerMock.Object, _cameraConfigMapperMock.Object, _relativePath);

            // Act & Assert
            Assert.Equal(_relativePath, handler.RelativePath);
        }

        private Vessel CreateTestVesselWithCameras()
        {
            var vessel = new Vessel("TEST-VESSEL", "12345678", "123456789", "Test Vessel");

            // Create manufacturer
            var manufacturer = new Manufacturer("Axis");

            // Create camera model
            var cameraModel = new DeviceModel(manufacturer, "Test Camera Model", DeviceType.Camera);

            // Create cameras
            var camera1 = new Camera
            {
                Name = "Camera 1",
                DeviceRole = DeviceRole.Camera,
                Model = cameraModel
            };

            var camera2 = new Camera
            {
                Name = "Camera 2",
                DeviceRole = DeviceRole.Camera,
                Model = cameraModel
            };

            // Add cameras to vessel
            vessel.AddDevice(camera1);
            vessel.AddDevice(camera2);

            return vessel;
        }

        private Vessel CreateTestVesselWithMixedDevices()
        {
            var vessel = new Vessel("TEST-VESSEL", "12345678", "123456789", "Test Vessel");

            // Create manufacturer
            var manufacturer = new Manufacturer("Axis");

            // Create camera model
            var cameraModel = new DeviceModel(manufacturer, "Test Camera Model", DeviceType.Camera);

            // Create camera
            var camera = new Camera
            {
                Name = "Camera 1",
                DeviceRole = DeviceRole.Camera,
                Model = cameraModel
            };

            // Create NVR screen
            var nvrScreen = new GenericDevice
            {
                Name = "NVR Screen 1",
                DeviceRole = DeviceRole.NVRScreen
            };

            // Create NVR recording
            var nvrRecording = new GenericDevice
            {
                Name = "NVR Recording 1",
                DeviceRole = DeviceRole.NVRRecording
            };

            // Add devices to vessel
            vessel.AddDevice(camera);
            vessel.AddDevice(nvrScreen);
            vessel.AddDevice(nvrRecording);

            return vessel;
        }
    }
}
