using System;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Services;

namespace Barret.Tests.Areas.ConfigGeneration.Services
{
    public class ConfigurationHandlerProviderTests
    {
        private readonly Mock<ILogger<ConfigurationHandlerProvider>> _loggerMock;
        private readonly Mock<IConfigurationHandler> _davinciCameraHandlerMock;
        private readonly Mock<IConfigurationHandler> _michelangeloCameraHandlerMock;
        private readonly Mock<IConfigurationHandler> _defaultCameraHandlerMock;
        private readonly Mock<IConfigurationHandler> _staticHandlerMock;

        public ConfigurationHandlerProviderTests()
        {
            _loggerMock = new Mock<ILogger<ConfigurationHandlerProvider>>();
            _davinciCameraHandlerMock = new Mock<IConfigurationHandler>();
            _michelangeloCameraHandlerMock = new Mock<IConfigurationHandler>();
            _defaultCameraHandlerMock = new Mock<IConfigurationHandler>();
            _staticHandlerMock = new Mock<IConfigurationHandler>();

            // Set up the Davinci camera handler
            _davinciCameraHandlerMock.Setup(h => h.HandlerType).Returns("CameraConfig");
            _davinciCameraHandlerMock.Setup(h => h.CanHandlePath(It.Is<string>(p =>
                p.Contains("DavinciConfigs") && p.EndsWith("CameraConfig.json"))))
                .Returns(true);
            _davinciCameraHandlerMock.Setup(h => h.CanHandlePath(It.Is<string>(p =>
                !p.Contains("DavinciConfigs") || !p.EndsWith("CameraConfig.json"))))
                .Returns(false);

            // Set up the Michelangelo driver handler
            _michelangeloCameraHandlerMock.Setup(h => h.HandlerType).Returns("DriverConfig");
            _michelangeloCameraHandlerMock.Setup(h => h.CanHandlePath(It.Is<string>(p =>
                p.Contains("MichelangeloConfigs") && p.EndsWith("DriverConfig.json"))))
                .Returns(true);
            _michelangeloCameraHandlerMock.Setup(h => h.CanHandlePath(It.Is<string>(p =>
                !p.Contains("MichelangeloConfigs") || !p.EndsWith("DriverConfig.json"))))
                .Returns(false);

            // Set up the default camera handler
            _defaultCameraHandlerMock.Setup(h => h.HandlerType).Returns("CameraConfig");
            _defaultCameraHandlerMock.Setup(h => h.CanHandlePath(It.Is<string>(p =>
                !p.Contains("DavinciConfigs") && !p.Contains("MichelangeloConfigs") && p.EndsWith("CameraConfig.json"))))
                .Returns(true);
            _defaultCameraHandlerMock.Setup(h => h.CanHandlePath(It.Is<string>(p =>
                p.Contains("DavinciConfigs") || p.Contains("MichelangeloConfigs") || !p.EndsWith("CameraConfig.json"))))
                .Returns(false);

            // Set up the static handler
            _staticHandlerMock.Setup(h => h.HandlerType).Returns("StaticConfig");
            _staticHandlerMock.Setup(h => h.CanHandlePath(It.IsAny<string>())).Returns(false);
        }

        [Fact]
        public void GetHandlerForTemplate_WithDavinciCameraConfig_ReturnsDavinciHandler()
        {
            // Arrange
            var template = new ConfigurationTemplate
            {
                Path = "DavinciConfigs/CameraConfigs/CameraConfig.json",
                OutputPath = "output/DavinciConfigs/CameraConfigs/CameraConfig.json"
            };

            var handlers = new List<IConfigurationHandler>
            {
                _davinciCameraHandlerMock.Object,
                _michelangeloCameraHandlerMock.Object,
                _defaultCameraHandlerMock.Object,
                _staticHandlerMock.Object
            };

            var provider = new ConfigurationHandlerProvider(handlers, _loggerMock.Object);

            // Act
            var result = provider.GetHandlerForTemplate(template);

            // Assert
            Assert.Same(_davinciCameraHandlerMock.Object, result);
        }

        [Fact]
        public void GetHandlerForTemplate_WithMichelangeloDriverConfig_ReturnsMichelangeloHandler()
        {
            // Arrange
            var template = new ConfigurationTemplate
            {
                Path = "MichelangeloConfigs/DriverConfigs/DriverConfig.json",
                OutputPath = "output/MichelangeloConfigs/DriverConfigs/DriverConfig.json"
            };

            var handlers = new List<IConfigurationHandler>
            {
                _davinciCameraHandlerMock.Object,
                _michelangeloCameraHandlerMock.Object,
                _defaultCameraHandlerMock.Object,
                _staticHandlerMock.Object
            };

            var provider = new ConfigurationHandlerProvider(handlers, _loggerMock.Object);

            // Act
            var result = provider.GetHandlerForTemplate(template);

            // Assert
            Assert.Same(_michelangeloCameraHandlerMock.Object, result);
        }

        [Fact]
        public void GetHandlerForTemplate_WithOtherCameraConfig_ReturnsDefaultHandler()
        {
            // Arrange
            var template = new ConfigurationTemplate
            {
                Path = "OtherConfigs/CameraConfigs/CameraConfig.json",
                OutputPath = "output/OtherConfigs/CameraConfigs/CameraConfig.json"
            };

            var handlers = new List<IConfigurationHandler>
            {
                _davinciCameraHandlerMock.Object,
                _michelangeloCameraHandlerMock.Object,
                _defaultCameraHandlerMock.Object,
                _staticHandlerMock.Object
            };

            var provider = new ConfigurationHandlerProvider(handlers, _loggerMock.Object);

            // Act
            var result = provider.GetHandlerForTemplate(template);

            // Assert
            Assert.Same(_defaultCameraHandlerMock.Object, result);
        }

        [Fact]
        public void GetHandlerForTemplate_WithUnknownTemplate_ReturnsStaticHandler()
        {
            // Arrange
            var template = new ConfigurationTemplate
            {
                Path = "UnknownConfigs/UnknownConfig.json",
                OutputPath = "output/UnknownConfigs/UnknownConfig.json"
            };

            var handlers = new List<IConfigurationHandler>
            {
                _davinciCameraHandlerMock.Object,
                _michelangeloCameraHandlerMock.Object,
                _defaultCameraHandlerMock.Object,
                _staticHandlerMock.Object
            };

            var provider = new ConfigurationHandlerProvider(handlers, _loggerMock.Object);

            // Act
            var result = provider.GetHandlerForTemplate(template);

            // Assert
            Assert.Same(_staticHandlerMock.Object, result);
        }
    }
}
