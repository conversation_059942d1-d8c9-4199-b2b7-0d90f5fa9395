using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Domain;
using Barret.Services.Areas.ConfigGeneration.Handlers;
using Barret.Services.Areas.ConfigGeneration.Interfaces;
using Barret.Services.Areas.ConfigGeneration.Services;
using Barret.Shared.Results;

namespace Barret.Tests.Areas.ConfigGeneration.Services
{
    public class ConfigurationManagerTests
    {
        private readonly Mock<ILogger<ConfigurationManager>> _loggerMock;
        private readonly Mock<ITemplateRegistryService> _templateRegistryMock;
        private readonly Mock<IConfigurationHandler> _staticHandlerMock;
        private readonly Mock<IConfigurationHandler> _specificHandlerMock;
        private readonly string _templateBasePath = "/templates";
        private readonly string _outputBasePath = "/output";

        public ConfigurationManagerTests()
        {
            _loggerMock = new Mock<ILogger<ConfigurationManager>>();
            _templateRegistryMock = new Mock<ITemplateRegistryService>();
            _staticHandlerMock = new Mock<IConfigurationHandler>();
            _specificHandlerMock = new Mock<IConfigurationHandler>();

            // Set up the template registry
            _templateRegistryMock.Setup(x => x.GetTemplateBasePath()).Returns(_templateBasePath);

            // Set up the static handler
            _staticHandlerMock.Setup(x => x.HandlerType).Returns("StaticConfig");
            _staticHandlerMock.Setup(x => x.CanHandle(It.Is<ConfigurationTemplate>(t => t.Handler == "StaticConfig")))
                .Returns(true);
            _staticHandlerMock.Setup(x => x.CanHandle(It.Is<ConfigurationTemplate>(t => t.Handler != "StaticConfig")))
                .Returns(false);

            // Set up the specific handler
            _specificHandlerMock.Setup(x => x.HandlerType).Returns("SpecificConfig");
            _specificHandlerMock.Setup(x => x.CanHandle(It.Is<ConfigurationTemplate>(t => t.Handler == "SpecificConfig")))
                .Returns(true);
            _specificHandlerMock.Setup(x => x.CanHandle(It.Is<ConfigurationTemplate>(t => t.Handler != "SpecificConfig")))
                .Returns(false);
        }

        [Fact]
        public async Task GenerateConfigurationsAsync_WithNoHandlerForTemplate_UsesFallbackHandler()
        {
            // Arrange
            var vessel = new Vessel("test-vessel");
            var templates = new List<ConfigurationTemplate>
            {
                new ConfigurationTemplate
                {
                    Path = "test/unknown.json",
                    Handler = "UnknownHandler",
                    OutputPath = "test/unknown.json",
                    Description = "Unknown handler test"
                }
            };

            _templateRegistryMock.Setup(x => x.GetAllTemplatesAsync())
                .ReturnsAsync(Result.Success<IReadOnlyList<ConfigurationTemplate>>(templates));

            _staticHandlerMock.Setup(x => x.GenerateConfigurationAsync(It.IsAny<ConfigurationContext>(), It.IsAny<ConfigurationTemplate>()))
                .ReturnsAsync((ConfigurationContext ctx, ConfigurationTemplate template) =>
                {
                    return ConfigurationOutput.CreateSuccess(template, $"{_outputBasePath}/{template.OutputPath}");
                });

            var handlers = new List<IConfigurationHandler> { _staticHandlerMock.Object };
            var manager = new ConfigurationManager(_loggerMock.Object, _templateRegistryMock.Object, handlers);

            // Act
            var result = await manager.GenerateConfigurationsAsync(vessel, _outputBasePath);

            // Assert
            Assert.True(result.Success);
            Assert.Single(result.Data);
            Assert.True(result.Data[0].Success);
            
            // Verify that the static handler was used as a fallback
            _staticHandlerMock.Verify(x => x.GenerateConfigurationAsync(
                It.IsAny<ConfigurationContext>(),
                It.Is<ConfigurationTemplate>(t => t.Handler == "UnknownHandler")),
                Times.Once);
        }

        [Fact]
        public async Task GenerateConfigurationsAsync_WithSpecificHandler_UsesSpecificHandler()
        {
            // Arrange
            var vessel = new Vessel("test-vessel");
            var templates = new List<ConfigurationTemplate>
            {
                new ConfigurationTemplate
                {
                    Path = "test/specific.json",
                    Handler = "SpecificConfig",
                    OutputPath = "test/specific.json",
                    Description = "Specific handler test"
                }
            };

            _templateRegistryMock.Setup(x => x.GetAllTemplatesAsync())
                .ReturnsAsync(Result.Success<IReadOnlyList<ConfigurationTemplate>>(templates));

            _specificHandlerMock.Setup(x => x.GenerateConfigurationAsync(It.IsAny<ConfigurationContext>(), It.IsAny<ConfigurationTemplate>()))
                .ReturnsAsync((ConfigurationContext ctx, ConfigurationTemplate template) =>
                {
                    return ConfigurationOutput.CreateSuccess(template, $"{_outputBasePath}/{template.OutputPath}");
                });

            var handlers = new List<IConfigurationHandler> { _staticHandlerMock.Object, _specificHandlerMock.Object };
            var manager = new ConfigurationManager(_loggerMock.Object, _templateRegistryMock.Object, handlers);

            // Act
            var result = await manager.GenerateConfigurationsAsync(vessel, _outputBasePath);

            // Assert
            Assert.True(result.Success);
            Assert.Single(result.Data);
            Assert.True(result.Data[0].Success);
            
            // Verify that the specific handler was used
            _specificHandlerMock.Verify(x => x.GenerateConfigurationAsync(
                It.IsAny<ConfigurationContext>(),
                It.Is<ConfigurationTemplate>(t => t.Handler == "SpecificConfig")),
                Times.Once);
            
            // Verify that the static handler was not used
            _staticHandlerMock.Verify(x => x.GenerateConfigurationAsync(
                It.IsAny<ConfigurationContext>(),
                It.IsAny<ConfigurationTemplate>()),
                Times.Never);
        }

        [Fact]
        public async Task GenerateConfigurationsAsync_WithNoHandlersAvailable_ReturnsFailure()
        {
            // Arrange
            var vessel = new Vessel("test-vessel");
            var templates = new List<ConfigurationTemplate>
            {
                new ConfigurationTemplate
                {
                    Path = "test/unknown.json",
                    Handler = "UnknownHandler",
                    OutputPath = "test/unknown.json",
                    Description = "Unknown handler test"
                }
            };

            _templateRegistryMock.Setup(x => x.GetAllTemplatesAsync())
                .ReturnsAsync(Result.Success<IReadOnlyList<ConfigurationTemplate>>(templates));

            // No handlers available
            var handlers = new List<IConfigurationHandler>();
            var manager = new ConfigurationManager(_loggerMock.Object, _templateRegistryMock.Object, handlers);

            // Act
            var result = await manager.GenerateConfigurationsAsync(vessel, _outputBasePath);

            // Assert
            Assert.True(result.Success); // The operation itself succeeds
            Assert.Single(result.Data);
            Assert.False(result.Data[0].Success); // But the individual output fails
            Assert.Contains("No handler found", result.Data[0].ErrorMessage);
        }
    }
}
