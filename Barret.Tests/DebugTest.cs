using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Devices.Models.Sensors;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Services.Areas.ConfigGeneration.Mappers;
using Microsoft.Extensions.Logging;
using Moq;
using System;
using System.Linq;
using Xunit;
using Xunit.Abstractions;

namespace Barret.Tests
{
    /// <summary>
    /// Debug test to investigate the sensor tripling issue
    /// </summary>
    public class DebugTest
    {
        private readonly ITestOutputHelper _output;
        private readonly Mock<ILogger<DriverConfigMapper>> _mockLogger;
        private readonly DriverConfigMapper _mapper;

        public DebugTest(ITestOutputHelper output)
        {
            _output = output;
            _mockLogger = new Mock<ILogger<DriverConfigMapper>>();
            _mapper = new DriverConfigMapper(_mockLogger.Object);
        }

        [Fact]
        public void DebugSensorTripling_InvestigateVesselDeviceRetrieval()
        {
            // Arrange
            var vessel = new Vessel("Debug Test Vessel");

            // Create device models
            var a100Model = new DeviceModel("A100", DeviceRole.Sensor);
            var regularModel = new DeviceModel("Regular", DeviceRole.Sensor);

            // Add sensors
            var sensor1 = new Sensor("Sensor 1")
                .WithConnection(new ConnectionHandler("*************", 4001, Protocol.TcpClient))
                .SetModel(a100Model);
            vessel.AddDevice(sensor1);

            var sensor2 = new Sensor("Sensor 2")
                .WithConnection(new ConnectionHandler("*************", 4002, Protocol.TcpClient))
                .SetModel(regularModel);
            vessel.AddDevice(sensor2);

            var sensor3 = new Sensor("Sensor 3")
                .WithConnection(new ConnectionHandler("*************", 4003, Protocol.TcpClient));
            vessel.AddDevice(sensor3);

            // Act - Get devices multiple times to check for consistency
            _output.WriteLine("=== VESSEL DEVICE RETRIEVAL TEST ===");
            
            var allDevices1 = vessel.GetAllDevices().ToList();
            _output.WriteLine($"First call to GetAllDevices(): {allDevices1.Count} devices");
            
            var allDevices2 = vessel.GetAllDevices().ToList();
            _output.WriteLine($"Second call to GetAllDevices(): {allDevices2.Count} devices");
            
            var sensorDevices1 = allDevices1.Where(d => d.DeviceRole == DeviceRole.Sensor).ToList();
            _output.WriteLine($"First call sensor count: {sensorDevices1.Count}");
            
            var sensorDevices2 = allDevices2.Where(d => d.DeviceRole == DeviceRole.Sensor).ToList();
            _output.WriteLine($"Second call sensor count: {sensorDevices2.Count}");

            // Log device details
            _output.WriteLine("\n=== DEVICE DETAILS ===");
            foreach (var device in allDevices1)
            {
                _output.WriteLine($"Device: {device.Id} - {device.Name} - Role: {device.DeviceRole} - Model: {device.Model?.Name ?? "NULL"}");
            }

            // Test individual mapping methods
            _output.WriteLine("\n=== MAPPING METHODS TEST ===");
            
            var genericConfigs = _mapper.MapToGenericNmea0183Configs(vessel);
            _output.WriteLine($"GenericNmea0183 configs: {genericConfigs.Count}");
            
            var emtrackConfigs = _mapper.MapToEMtrackA100Configs(vessel);
            _output.WriteLine($"EMtrackA100 configs: {emtrackConfigs.Count}");

            var driverConfigContainer = _mapper.MapToDriverConfigContainer(vessel);
            _output.WriteLine($"Container GenericNmea0183: {driverConfigContainer.GenericNmea0183.Count}");
            _output.WriteLine($"Container EMtrackA100: {driverConfigContainer.EmtrackA100.Count}");

            // Assert - Device counts should be consistent
            Assert.Equal(3, allDevices1.Count); // Should have exactly 3 devices
            Assert.Equal(3, allDevices2.Count); // Should be consistent across calls
            Assert.Equal(3, sensorDevices1.Count); // All devices are sensors
            Assert.Equal(3, sensorDevices2.Count); // Should be consistent

            // Assert - No duplicate device IDs
            var deviceIds = allDevices1.Select(d => d.Id).ToList();
            var uniqueDeviceIds = deviceIds.Distinct().ToList();
            Assert.Equal(deviceIds.Count, uniqueDeviceIds.Count); // No duplicates

            // Assert - Mapping should not triple the count
            Assert.Equal(2, genericConfigs.Count); // Sensor 2 and Sensor 3 (non-A100)
            Assert.Single(emtrackConfigs); // Sensor 1 (A100)

            // Verify total equals original sensor count
            var totalMappedSensors = genericConfigs.Count + emtrackConfigs.Count;
            Assert.Equal(sensorDevices1.Count, totalMappedSensors);

            _output.WriteLine("\n=== TEST PASSED ===");
        }
    }
}
