﻿<Project Sdk="Microsoft.NET.Sdk">

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.3" />
    <PackageReference Include="FluentValidation" Version="11.11.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Barret.Core\Barret.Core.csproj" />
    <ProjectReference Include="..\Barret.Shared\Barret.Shared.csproj" />
  </ItemGroup>

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

</Project>
