namespace Barret.Services.Core.Areas.Mapping
{
    /// <summary>
    /// Generic interface for mapping DTOs to domain entities
    /// </summary>
    public interface IEntityMapper<TDto, TEntity>
    {
        /// <summary>
        /// Maps a DTO to a new domain entity
        /// </summary>
        TEntity MapToEntity(TDto dto);
        
        /// <summary>
        /// Updates an existing entity with data from a DTO
        /// </summary>
        void UpdateEntityFromDto(TEntity entity, TDto dto);
    }
}
