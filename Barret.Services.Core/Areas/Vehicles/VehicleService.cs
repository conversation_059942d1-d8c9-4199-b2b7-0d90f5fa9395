using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Vehicles.Models;
using Barret.Services.Core.Areas.Contexts;
using Barret.Services.Core.Areas.Devices.Mapping;
using Barret.Services.Core.Areas.Vehicles.Mapping;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles;
using Barret.Shared.Results;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;

namespace Barret.Services.Core.Areas.Vehicles
{
    /// <summary>
    /// Abstract base class for vehicle services that enforces the aggregate root pattern.
    /// All device operations must go through the vehicle service to maintain domain integrity.
    /// </summary>
    /// <typeparam name="TVehicle">The vehicle type (must inherit from Vehicle)</typeparam>
    /// <typeparam name="TDto">The DTO type (must inherit from VehicleDto)</typeparam>
    /// <remarks>
    /// Initializes a new instance of the <see cref="VehicleService{TVehicle, TDto}"/> class.
    /// </remarks>
    /// <param name="unitOfWork">The unit of work for transaction management</param>
    /// <param name="logger">The logger</param>
    /// <param name="mapper">The entity mapper for converting between entities and DTOs</param>
    /// <param name="deviceMapper">The device mapper for converting between device entities and DTOs</param>
    /// <exception cref="ArgumentNullException">Thrown if any parameter is null</exception>
    public abstract class VehicleService<TVehicle, TDto>(
        IUnitOfWork unitOfWork,
        ILogger logger,
        IVehicleMapper<TVehicle, TDto> mapper,
        IDeviceMapper deviceMapper)
        : IVehicleService<TVehicle, TDto>
        where TVehicle : Vehicle
        where TDto : VehicleDto
    {
        protected readonly IUnitOfWork _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
        protected readonly ILogger _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        protected readonly IVehicleMapper<TVehicle, TDto> _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
        protected readonly IDeviceMapper _deviceMapper = deviceMapper ?? throw new ArgumentNullException(nameof(deviceMapper));

        /// <summary>
        /// Gets a vehicle by its ID.
        /// </summary>
        /// <param name="id">The ID of the vehicle to retrieve</param>
        /// <returns>A service result containing the vehicle DTO if found</returns>
        public abstract Task<ServiceResult<TDto>> GetByIdAsync(Guid id);

        /// <summary>
        /// Gets all vehicles.
        /// </summary>
        /// <returns>A service result containing a collection of vehicle DTOs</returns>
        public abstract Task<ServiceResult<IEnumerable<TDto>>> GetAllAsync();

        /// <summary>
        /// Gets all vehicles with basic information only (optimized for list views).
        /// This method doesn't load devices or device groups, making it much faster for list displays.
        /// </summary>
        /// <returns>A service result containing a collection of basic vehicle DTOs</returns>
        public abstract Task<ServiceResult<IEnumerable<TDto>>> GetAllBasicAsync();

        /// <summary>
        /// Creates a new vehicle.
        /// </summary>
        /// <param name="dto">The vehicle DTO containing the data for the new vehicle</param>
        /// <returns>A service result containing the ID of the created vehicle</returns>
        public abstract Task<ServiceResult<Guid>> CreateAsync(TDto dto);

        /// <summary>
        /// Updates an existing vehicle.
        /// </summary>
        /// <param name="id">The ID of the vehicle to update</param>
        /// <param name="dto">The vehicle DTO containing the updated data</param>
        /// <returns>A service result containing the updated vehicle DTO</returns>
        public abstract Task<ServiceResult<TDto>> UpdateAsync(Guid id, TDto dto);

        /// <summary>
        /// Deletes a vehicle.
        /// </summary>
        /// <param name="id">The ID of the vehicle to delete</param>
        /// <returns>A service result indicating success or failure</returns>
        public abstract Task<ServiceResult> DeleteAsync(Guid id);

        /// <summary>
        /// Adds a device to a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle to add the device to</param>
        /// <param name="deviceDto">The device DTO containing the data for the new device</param>
        /// <returns>A service result containing the created device DTO</returns>
        public abstract Task<ServiceResult<DeviceDto>> AddDeviceAsync(Guid vehicleId, DeviceDto deviceDto);

        /// <summary>
        /// Updates a device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the device</param>
        /// <param name="deviceId">The ID of the device to update</param>
        /// <param name="deviceDto">The device DTO containing the updated data</param>
        /// <returns>A service result containing the updated device DTO</returns>
        public abstract Task<ServiceResult<DeviceDto>> UpdateDeviceAsync(Guid vehicleId, Guid deviceId, DeviceDto deviceDto);

        /// <summary>
        /// Removes a device from a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the device</param>
        /// <param name="deviceId">The ID of the device to remove</param>
        /// <returns>A service result indicating success or failure</returns>
        public abstract Task<ServiceResult> RemoveDeviceAsync(Guid vehicleId, Guid deviceId);

        /// <summary>
        /// Connects a device to an interface device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the devices</param>
        /// <param name="connectedDeviceId">The ID of the connected device</param>
        /// <param name="interfaceDeviceId">The ID of the interface device</param>
        /// <param name="type">The type of connection</param>
        /// <param name="direction">The direction of data flow</param>
        /// <returns>A service result indicating success or failure</returns>
        public abstract Task<ServiceResult> ConnectDeviceToInterfaceAsync(Guid vehicleId, Guid connectedDeviceId, Guid interfaceDeviceId, ConnectionType type, ConnectionDirection direction);

        /// <summary>
        /// Disconnects a device from an interface device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the devices</param>
        /// <param name="connectedDeviceId">The ID of the connected device</param>
        /// <param name="interfaceDeviceId">The ID of the interface device</param>
        /// <returns>A service result indicating success or failure</returns>
        public abstract Task<ServiceResult> DisconnectDeviceFromInterfaceAsync(Guid vehicleId, Guid connectedDeviceId, Guid interfaceDeviceId);

        /// <summary>
        /// Updates a connection between a device and an interface device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the devices</param>
        /// <param name="connectedDeviceId">The ID of the connected device</param>
        /// <param name="interfaceDeviceId">The ID of the interface device</param>
        /// <param name="type">The new connection type</param>
        /// <param name="direction">The new connection direction</param>
        /// <returns>A service result indicating success or failure</returns>
        public abstract Task<ServiceResult> UpdateDeviceInterfaceConnectionAsync(Guid vehicleId, Guid connectedDeviceId, Guid interfaceDeviceId, ConnectionType type, ConnectionDirection direction);

        /// <summary>
        /// Gets all connections for a device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the device</param>
        /// <param name="deviceId">The ID of the device to get connections for</param>
        /// <returns>A service result containing a collection of device connection DTOs</returns>
        public abstract Task<ServiceResult<IEnumerable<DeviceConnectionDto>>> GetDeviceConnectionsAsync(Guid vehicleId, Guid deviceId);

        /// <summary>
        /// Creates a copy of an existing vehicle.
        /// </summary>
        /// <param name="sourceId">The ID of the source vehicle to copy</param>
        /// <param name="newName">The name for the new vehicle</param>
        /// <returns>A service result containing the ID of the created vehicle</returns>
        public abstract Task<ServiceResult<Guid>> CopyVehicleAsync(Guid sourceId, string newName);

        /// <summary>
        /// Creates copies of devices by their IDs without persisting to the database.
        /// </summary>
        /// <param name="deviceIds">The IDs of the devices to copy</param>
        /// <param name="connections">Optional list of connections to maintain between copied devices</param>
        /// <returns>A service result containing the list of copied device DTOs</returns>
        public abstract Task<ServiceResult<List<DeviceDto>>> CopyDevicesAsync(List<Guid> deviceIds, List<DeviceConnectionDto>? connections = null);

        /// <summary>
        /// Executes an operation within a transaction.
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The operation to execute</param>
        /// <param name="operationName">The name of the operation for logging</param>
        /// <returns>The result of the operation</returns>
        /// <exception cref="Exception">Rethrows any exceptions that occur during the operation</exception>
        protected async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation, string operationName)
        {
            try
            {
                _logger.LogInformation("Starting transaction for operation: {OperationName}", operationName);

                // Begin transaction
                await _unitOfWork.BeginTransactionAsync();

                try
                {
                    // Execute the operation
                    var result = await operation();

                    // Save changes
                    await _unitOfWork.SaveChangesAsync();

                    // Commit transaction
                    await _unitOfWork.CommitTransactionAsync();

                    _logger.LogInformation("Successfully completed transaction for operation: {OperationName}", operationName);

                    return result;
                }
                catch (DbUpdateConcurrencyException ex)
                {
                    // Handle optimistic concurrency exception
                    await _unitOfWork.RollbackTransactionAsync();
                    _logger.LogError(ex, "Optimistic concurrency error during operation {OperationName}, transaction rolled back", operationName);

                    // Return a failure result instead of throwing
                    if (typeof(T).IsGenericType && typeof(T).GetGenericTypeDefinition() == typeof(ServiceResult<>))
                    {
                        // Create a failure result using reflection
                        var createFailureMethod = typeof(T).GetMethod("CreateFailure", new[] { typeof(string), typeof(string) });
                        if (createFailureMethod != null)
                        {
                            return (T)createFailureMethod.Invoke(null, new object[]
                            {
                                "Concurrency error: The data was modified by another operation. Please try again.",
                                ex.Message
                            });
                        }
                    }
                    else if (typeof(T) == typeof(ServiceResult))
                    {
                        // Create a failure result for ServiceResult
                        var createFailureMethod = typeof(ServiceResult).GetMethod("CreateFailure", new[] { typeof(string), typeof(string) });
                        if (createFailureMethod != null)
                        {
                            return (T)createFailureMethod.Invoke(null, new object[]
                            {
                                "Concurrency error: The data was modified by another operation. Please try again.",
                                ex.Message
                            });
                        }
                    }

                    // If we can't create a failure result, rethrow
                    throw;
                }
                catch (Exception ex)
                {
                    // Rollback transaction on error
                    await _unitOfWork.RollbackTransactionAsync();
                    _logger.LogError(ex, "Error during operation {OperationName}, transaction rolled back", operationName);
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing operation: {OperationName}", operationName);
                throw;
            }
        }
    }
}
