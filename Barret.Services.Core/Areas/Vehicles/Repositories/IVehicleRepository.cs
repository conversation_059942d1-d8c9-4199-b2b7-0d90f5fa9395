using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Vehicles.Models;

namespace Barret.Services.Core.Areas.Vehicles.Repositories
{
    /// <summary>
    /// Generic repository interface for vehicles.
    /// </summary>
    /// <typeparam name="TVehicle">The vehicle type (must inherit from Vehicle)</typeparam>
    public interface IVehicleRepository<TVehicle> where TVehicle : Vehicle
    {
        /// <summary>
        /// Gets a vehicle by its ID.
        /// </summary>
        /// <param name="id">The ID of the vehicle to retrieve</param>
        /// <returns>The vehicle if found; otherwise, null</returns>
        Task<TVehicle?> GetByIdAsync(Guid id);

        /// <summary>
        /// Gets a vehicle by its ID with all its devices eagerly loaded.
        /// </summary>
        /// <param name="id">The ID of the vehicle to retrieve</param>
        /// <returns>The vehicle with devices if found; otherwise, null</returns>
        Task<TVehicle?> GetWithDevicesAsync(Guid id);

        /// <summary>
        /// Gets all vehicles.
        /// </summary>
        /// <returns>A collection of all vehicles</returns>
        Task<IEnumerable<TVehicle>> GetAllAsync();

        /// <summary>
        /// Adds a vehicle to the repository.
        /// </summary>
        /// <param name="vehicle">The vehicle to add</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        Task AddAsync(TVehicle vehicle);

        /// <summary>
        /// Updates a vehicle in the repository.
        /// </summary>
        /// <param name="vehicle">The vehicle to update</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        void Update(TVehicle vehicle);

        /// <summary>
        /// Deletes a vehicle from the repository.
        /// </summary>
        /// <param name="id">The ID of the vehicle to delete</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        Task DeleteAsync(Guid id);

        /// <summary>
        /// Checks if a vehicle with the specified ID exists.
        /// </summary>
        /// <param name="id">The ID of the vehicle to check</param>
        /// <returns>True if the vehicle exists; otherwise, false</returns>
        Task<bool> ExistsAsync(Guid id);

        /// <summary>
        /// Gets all devices for a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle</param>
        /// <returns>A collection of devices for the vehicle</returns>
        Task<IEnumerable<GenericDevice>> GetDevicesForVehicleAsync(Guid vehicleId);
    }
}
