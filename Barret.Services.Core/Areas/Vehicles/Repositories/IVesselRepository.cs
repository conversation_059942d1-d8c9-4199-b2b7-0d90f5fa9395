using Barret.Core.Areas.Vehicles.Models.Vessel;

namespace Barret.Services.Core.Areas.Vehicles.Repositories
{
    /// <summary>
    /// Repository interface for vessels, extending the generic vehicle repository.
    /// </summary>
    public interface IVesselRepository : IVehicleRepository<Vessel>
    {
        /// <summary>
        /// Gets a vessel by its ID with the full device tree and all device connections eagerly loaded.
        /// </summary>
        /// <param name="id">The ID of the vessel to retrieve</param>
        /// <returns>The vessel with the full device tree and connections if found; otherwise, null</returns>
        Task<Vessel?> GetWithDeviceTreeAsync(Guid id);

        /// <summary>
        /// Gets all vessels with basic information only (no devices loaded).
        /// This is optimized for list views that only need basic vessel information.
        /// </summary>
        /// <returns>A collection of vessels with basic information only</returns>
        Task<IEnumerable<Vessel>> GetAllBasicAsync();

        /// <summary>
        /// Gets the device count for a specific vessel without loading the actual devices.
        /// </summary>
        /// <param name="vesselId">The ID of the vessel</param>
        /// <returns>The number of devices associated with the vessel</returns>
        Task<int> GetDeviceCountAsync(Guid vesselId);
    }
}
