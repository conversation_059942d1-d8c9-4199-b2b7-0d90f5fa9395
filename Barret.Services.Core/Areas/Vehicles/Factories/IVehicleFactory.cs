using Barret.Core.Areas.Vehicles.Models;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Services.Core.Areas.Vehicles.Factories
{
    /// <summary>
    /// Factory interface for creating vehicle instances.
    /// </summary>
    public interface IVehicleFactory
    {
        /// <summary>
        /// Creates a vessel with default values.
        /// </summary>
        /// <returns>A new vessel instance with default values.</returns>
        Vessel CreateVessel();

        /// <summary>
        /// Creates a vehicle with the specified ID and devices
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle</param>
        /// <param name="devices">The devices to add to the vehicle</param>
        /// <returns>A vehicle instance with the specified devices</returns>
        Vehicle CreateVehicleWithDevices(Guid vehicleId, IEnumerable<GenericDevice> devices);

        /// <summary>
        /// Creates a vessel with the specified ID and devices
        /// </summary>
        /// <param name="vehicleId">The ID of the vessel</param>
        /// <param name="devices">The devices to add to the vessel</param>
        /// <returns>A vessel instance with the specified devices</returns>
        Vessel CreateVesselWithDevices(Guid vehicleId, IEnumerable<GenericDevice> devices);
    }
}