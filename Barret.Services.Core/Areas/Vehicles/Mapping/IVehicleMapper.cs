using Barret.Core.Areas.Vehicles.Models;
using Barret.Shared.DTOs.Vehicles;

namespace Barret.Services.Core.Areas.Vehicles.Mapping
{
    /// <summary>
    /// Interface for mapping between vehicle entities and DTOs
    /// </summary>
    /// <typeparam name="TEntity">The vehicle entity type</typeparam>
    /// <typeparam name="TDto">The vehicle DTO type</typeparam>
    public interface IVehicleMapper<TEntity, TDto>
        where TEntity : Vehicle
        where TDto : VehicleDto
    {
        /// <summary>
        /// Maps a vehicle entity to a DTO
        /// </summary>
        /// <param name="entity">The vehicle entity to map</param>
        /// <returns>The mapped vehicle DTO</returns>
        TDto MapToDto(TEntity entity);

        /// <summary>
        /// Maps a vehicle DTO to an entity
        /// </summary>
        /// <param name="dto">The vehicle DTO to map</param>
        /// <param name="entity">The existing entity to update, or null to create a new one</param>
        /// <returns>The mapped vehicle entity</returns>
        TEntity MapToEntity(TDto dto, TEntity? entity = null);
    }
}
