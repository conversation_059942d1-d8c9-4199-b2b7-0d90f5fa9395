using Barret.Shared.DTOs.Vehicles;
using Barret.Shared.DTOs.Vehicles.Vessels;

namespace Barret.Services.Core.Areas.Vehicles.Queries
{
    /// <summary>
    /// Query service interface for vessels.
    /// </summary>
    public interface IVesselQueryService
    {
        /// <summary>
        /// Gets a vessel with device summaries.
        /// </summary>
        /// <param name="id">The ID of the vessel to retrieve</param>
        /// <returns>The vessel DTO with device summaries if found; otherwise, null</returns>
        Task<VesselDto?> GetVesselWithDeviceSummariesAsync(Guid id);

        /// <summary>
        /// Gets a vessel with the full device tree and all device connections.
        /// </summary>
        /// <param name="id">The ID of the vessel to retrieve</param>
        /// <returns>The vessel DTO with the full device tree and connections if found; otherwise, null</returns>
        Task<VesselDto?> GetVesselWithDeviceTreeAsync(Guid id);

        /// <summary>
        /// Searches for vessels based on a filter.
        /// </summary>
        /// <param name="filter">The filter to apply</param>
        /// <returns>A list of vessel DTOs matching the filter</returns>
        Task<List<VesselDto>> SearchVesselsAsync(VesselFilterDto filter);

        /// <summary>
        /// Gets all vessels with basic information only (optimized for list views).
        /// This method doesn't load devices or device groups, making it much faster for list displays.
        /// </summary>
        /// <returns>A list of vessel DTOs with basic information</returns>
        Task<List<VesselDto>> GetAllBasicAsync();
    }
}
