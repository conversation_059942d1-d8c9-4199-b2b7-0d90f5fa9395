using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Vehicles.Models;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Vehicles;
using Barret.Shared.Results;

namespace Barret.Services.Core.Areas.Vehicles
{
    /// <summary>
    /// Generic interface for vehicle services that enforces the aggregate root pattern.
    /// All device operations must go through the vehicle service to maintain domain integrity.
    /// </summary>
    /// <typeparam name="TVehicle">The vehicle type (must inherit from Vehicle)</typeparam>
    /// <typeparam name="TDto">The DTO type (must inherit from VehicleDto)</typeparam>
    public interface IVehicleService<TVehicle, TDto>
        where TVehicle : Vehicle
        where TDto : VehicleDto
    {
        /// <summary>
        /// Gets a vehicle by its ID.
        /// </summary>
        /// <param name="id">The ID of the vehicle to retrieve</param>
        /// <returns>A service result containing the vehicle DTO if found</returns>
        Task<ServiceResult<TDto>> GetByIdAsync(Guid id);

        /// <summary>
        /// Gets all vehicles.
        /// </summary>
        /// <returns>A service result containing a collection of vehicle DTOs</returns>
        Task<ServiceResult<IEnumerable<TDto>>> GetAllAsync();

        /// <summary>
        /// Gets all vehicles with basic information only (optimized for list views).
        /// This method doesn't load devices or device groups, making it much faster for list displays.
        /// </summary>
        /// <returns>A service result containing a collection of basic vehicle DTOs</returns>
        Task<ServiceResult<IEnumerable<TDto>>> GetAllBasicAsync();

        /// <summary>
        /// Creates a new vehicle.
        /// </summary>
        /// <param name="dto">The vehicle DTO containing the data for the new vehicle</param>
        /// <returns>A service result containing the ID of the created vehicle</returns>
        Task<ServiceResult<Guid>> CreateAsync(TDto dto);

        /// <summary>
        /// Updates an existing vehicle.
        /// </summary>
        /// <param name="id">The ID of the vehicle to update</param>
        /// <param name="dto">The vehicle DTO containing the updated data</param>
        /// <returns>A service result containing the updated vehicle DTO</returns>
        Task<ServiceResult<TDto>> UpdateAsync(Guid id, TDto dto);

        /// <summary>
        /// Deletes a vehicle.
        /// </summary>
        /// <param name="id">The ID of the vehicle to delete</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> DeleteAsync(Guid id);

        /// <summary>
        /// Adds a device to a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle to add the device to</param>
        /// <param name="deviceDto">The device DTO containing the data for the new device</param>
        /// <returns>A service result containing the created device DTO</returns>
        Task<ServiceResult<DeviceDto>> AddDeviceAsync(Guid vehicleId, DeviceDto deviceDto);

        /// <summary>
        /// Updates a device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the device</param>
        /// <param name="deviceId">The ID of the device to update</param>
        /// <param name="deviceDto">The device DTO containing the updated data</param>
        /// <returns>A service result containing the updated device DTO</returns>
        Task<ServiceResult<DeviceDto>> UpdateDeviceAsync(Guid vehicleId, Guid deviceId, DeviceDto deviceDto);

        /// <summary>
        /// Removes a device from a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the device</param>
        /// <param name="deviceId">The ID of the device to remove</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> RemoveDeviceAsync(Guid vehicleId, Guid deviceId);

        /// <summary>
        /// Connects a device to an interface device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the devices</param>
        /// <param name="connectedDeviceId">The ID of the connected device</param>
        /// <param name="interfaceDeviceId">The ID of the interface device</param>
        /// <param name="type">The type of connection</param>
        /// <param name="direction">The direction of data flow</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> ConnectDeviceToInterfaceAsync(Guid vehicleId, Guid connectedDeviceId, Guid interfaceDeviceId, ConnectionType type, ConnectionDirection direction);

        /// <summary>
        /// Disconnects a device from an interface device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the devices</param>
        /// <param name="connectedDeviceId">The ID of the connected device</param>
        /// <param name="interfaceDeviceId">The ID of the interface device</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> DisconnectDeviceFromInterfaceAsync(Guid vehicleId, Guid connectedDeviceId, Guid interfaceDeviceId);

        /// <summary>
        /// Updates a connection between a device and an interface device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the devices</param>
        /// <param name="connectedDeviceId">The ID of the connected device</param>
        /// <param name="interfaceDeviceId">The ID of the interface device</param>
        /// <param name="type">The new connection type</param>
        /// <param name="direction">The new connection direction</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> UpdateDeviceInterfaceConnectionAsync(Guid vehicleId, Guid connectedDeviceId, Guid interfaceDeviceId, ConnectionType type, ConnectionDirection direction);

        /// <summary>
        /// Gets all connections for a device in a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle containing the device</param>
        /// <param name="deviceId">The ID of the device to get connections for</param>
        /// <returns>A service result containing a collection of device connection DTOs</returns>
        Task<ServiceResult<IEnumerable<DeviceConnectionDto>>> GetDeviceConnectionsAsync(Guid vehicleId, Guid deviceId);

        /// <summary>
        /// Creates a copy of an existing vehicle.
        /// </summary>
        /// <param name="sourceId">The ID of the source vehicle to copy</param>
        /// <param name="newName">The name for the new vehicle</param>
        /// <returns>A service result containing the ID of the created vehicle</returns>
        Task<ServiceResult<Guid>> CopyVehicleAsync(Guid sourceId, string newName);

        /// <summary>
        /// Creates copies of devices by their IDs without persisting to the database.
        /// </summary>
        /// <param name="deviceIds">The IDs of the devices to copy</param>
        /// <param name="connections">Optional list of connections to maintain between copied devices</param>
        /// <returns>A service result containing the list of copied device DTOs</returns>
        Task<ServiceResult<List<DeviceDto>>> CopyDevicesAsync(List<Guid> deviceIds, List<DeviceConnectionDto>? connections = null);
    }
}
