using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Manufacturers.Models;

namespace Barret.Services.Core.Areas.Manufacturers.Repositories
{
    /// <summary>
    /// Repository interface for manufacturers.
    /// </summary>
    public interface IManufacturerRepository
    {
        /// <summary>
        /// Gets all manufacturers.
        /// </summary>
        /// <returns>A collection of all manufacturers</returns>
        Task<IEnumerable<Manufacturer>> GetAllAsync();

        /// <summary>
        /// Gets all manufacturers with device models filtered by the specified device role.
        /// </summary>
        /// <param name="deviceRole">The device role to filter by.</param>
        /// <returns>A list of manufacturers with device models filtered by the specified role.</returns>
        Task<IEnumerable<Manufacturer>> GetManufacturersWithFilteredDeviceModelsAsync(DeviceRole deviceRole);

        /// <summary>
        /// Gets all manufacturers that have at least one device model with the specified device role,
        /// but without including the device models in the result.
        /// </summary>
        /// <param name="deviceRole">The device role to filter by.</param>
        /// <returns>A list of manufacturers that have device models with the specified role.</returns>
        Task<IEnumerable<Manufacturer>> GetManufacturersByDeviceRoleAsync(DeviceRole deviceRole);

        /// <summary>
        /// Gets a manufacturer by its ID.
        /// </summary>
        /// <param name="id">The ID of the manufacturer to retrieve</param>
        /// <returns>The manufacturer if found; otherwise, null</returns>
        Task<Manufacturer?> GetByIdAsync(Guid id);

        /// <summary>
        /// Gets a manufacturer by its ID with all its device models eagerly loaded.
        /// </summary>
        /// <param name="id">The ID of the manufacturer to retrieve</param>
        /// <returns>The manufacturer with device models if found; otherwise, null</returns>
        Task<Manufacturer?> GetWithDeviceModelsAsync(Guid id);

        /// <summary>
        /// Adds a manufacturer to the repository.
        /// </summary>
        /// <param name="manufacturer">The manufacturer to add</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        Task AddAsync(Manufacturer manufacturer);

        /// <summary>
        /// Updates a manufacturer in the repository.
        /// </summary>
        /// <param name="manufacturer">The manufacturer to update</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        Task UpdateAsync(Manufacturer manufacturer);

        /// <summary>
        /// Deletes a manufacturer from the repository.
        /// </summary>
        /// <param name="id">The ID of the manufacturer to delete</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        Task DeleteAsync(Guid id);
    }
}