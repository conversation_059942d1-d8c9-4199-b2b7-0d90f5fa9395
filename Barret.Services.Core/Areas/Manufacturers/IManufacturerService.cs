using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Manufacturers.Models;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.Results;
using System;

namespace Barret.Services.Core.Areas.Manufacturers
{
    /// <summary>
    /// Service interface for manufacturers.
    /// As Manufacturer is an aggregate root, this service also handles operations on DeviceModels.
    /// </summary>
    public interface IManufacturerService
    {
        #region Manufacturer Operations

        /// <summary>
        /// Gets all manufacturers.
        /// </summary>
        /// <returns>A service result containing a collection of manufacturer info objects</returns>
        Task<ServiceResult<IEnumerable<ManufacturerInfo>>> GetAllManufacturersAsync();

        /// <summary>
        /// Gets all manufacturers that have device models compatible with the specified device role.
        /// </summary>
        /// <param name="deviceRole">The device role to filter by.</param>
        /// <returns>A list of manufacturers with device models compatible with the specified role.</returns>
        Task<ServiceResult<IEnumerable<ManufacturerInfo>>> GetAllManufacturersForDeviceRoleAsync(DeviceRole deviceRole);

        /// <summary>
        /// Gets all manufacturers that have at least one device model with the specified device role,
        /// but without including the device models in the result.
        /// </summary>
        /// <param name="deviceRole">The device role to filter by.</param>
        /// <returns>A list of manufacturers that have device models with the specified role, without the device models.</returns>
        Task<ServiceResult<IEnumerable<ManufacturerInfo>>> GetManufacturersByDeviceRoleAsync(DeviceRole deviceRole);

        /// <summary>
        /// Gets all manufacturers that have device models compatible with the specified device role.
        /// </summary>
        /// <param name="deviceRole">The device role to filter by.</param>
        /// <returns>A list of manufacturers with device models compatible with the specified role.</returns>
        Task<IEnumerable<ManufacturerInfo>> GetManufacturersWithDeviceRoleAsync(DeviceRole deviceRole);

        /// <summary>
        /// Gets a manufacturer by its ID.
        /// </summary>
        /// <param name="id">The ID of the manufacturer to retrieve</param>
        /// <returns>A service result containing the manufacturer info if found</returns>
        Task<ServiceResult<ManufacturerInfo>> GetManufacturerByIdAsync(Guid id);

        /// <summary>
        /// Gets a manufacturer by its ID with all its device models eagerly loaded.
        /// </summary>
        /// <param name="id">The ID of the manufacturer to retrieve</param>
        /// <returns>A service result containing the manufacturer info with device models if found</returns>
        Task<ServiceResult<ManufacturerInfo>> GetManufacturerWithDeviceModelsByIdAsync(Guid id);

        /// <summary>
        /// Creates a new manufacturer.
        /// </summary>
        /// <param name="manufacturerName">The name of the manufacturer</param>
        /// <returns>A service result containing the ID of the created manufacturer</returns>
        Task<ServiceResult<Guid>> CreateManufacturerAsync(string manufacturerName);

        /// <summary>
        /// Updates an existing manufacturer.
        /// </summary>
        /// <param name="id">The ID of the manufacturer to update</param>
        /// <param name="manufacturerName">The new name of the manufacturer</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> UpdateManufacturerAsync(Guid id, string manufacturerName);

        /// <summary>
        /// Deletes a manufacturer.
        /// </summary>
        /// <param name="id">The ID of the manufacturer to delete</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> DeleteManufacturerAsync(Guid id);

        #endregion

        #region DeviceModel Operations

        /// <summary>
        /// Gets a device model by its ID.
        /// </summary>
        /// <param name="id">The ID of the device model to retrieve</param>
        /// <returns>A service result containing the device model info if found</returns>
        Task<ServiceResult<DeviceModelInfo>> GetDeviceModelByIdAsync(Guid id);

        /// <summary>
        /// Adds a device model to a manufacturer.
        /// </summary>
        /// <param name="manufacturerId">The ID of the manufacturer</param>
        /// <param name="modelName">The name of the device model</param>
        /// <param name="deviceRole">The role of the device model</param>
        /// <returns>A service result containing the ID of the created device model</returns>
        Task<ServiceResult<Guid>> AddDeviceModelAsync(Guid manufacturerId, string modelName, DeviceRole deviceRole);

        /// <summary>
        /// Adds a device model to a manufacturer (legacy method).
        /// </summary>
        /// <param name="manufacturerId">The ID of the manufacturer</param>
        /// <param name="modelName">The name of the device model</param>
        /// <param name="deviceRole">The role of the device model</param>
        /// <returns>A service result containing the ID of the created device model</returns>
        [Obsolete("Use AddDeviceModelAsync instead")]
        Task<ServiceResult<Guid>> AddDeviceModelToManufacturerAsync(Guid manufacturerId, string modelName, DeviceRole deviceRole);

        /// <summary>
        /// Updates an existing device model.
        /// </summary>
        /// <param name="id">The ID of the device model to update</param>
        /// <param name="modelName">The new name of the device model</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> UpdateDeviceModelAsync(Guid id, string modelName);

        /// <summary>
        /// Deletes a device model.
        /// </summary>
        /// <param name="id">The ID of the device model to delete</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> DeleteDeviceModelAsync(Guid id);

        /// <summary>
        /// Removes a device model from a manufacturer.
        /// </summary>
        /// <param name="manufacturerId">The ID of the manufacturer</param>
        /// <param name="deviceModelId">The ID of the device model to remove</param>
        /// <returns>A service result indicating success or failure</returns>
        Task<ServiceResult> RemoveDeviceModelFromManufacturerAsync(Guid manufacturerId, Guid deviceModelId);

        #endregion
    }
}