using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices;

namespace Barret.Services.Core.Areas.DeviceModels.Queries
{
    /// <summary>
    /// Query service interface for device models.
    /// This service provides read-only operations for device models.
    /// For write operations, use the IManufacturerService as Manufacturer is the aggregate root.
    /// </summary>
    public interface IDeviceModelQueryService
    {
        /// <summary>
        /// Gets a device model by its ID.
        /// </summary>
        /// <param name="id">The ID of the device model to retrieve</param>
        /// <returns>The device model DTO if found; otherwise, null</returns>
        Task<DeviceModelInfo?> GetDeviceModelByIdAsync(Guid id);

        /// <summary>
        /// Gets all device models.
        /// </summary>
        /// <returns>A list of all device model DTOs</returns>
        Task<List<DeviceModelInfo>> GetAllDeviceModelsAsync();

        /// <summary>
        /// Gets device models for a specific manufacturer.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer ID</param>
        /// <returns>A collection of device model DTOs for the manufacturer</returns>
        Task<List<DeviceModelInfo>> GetDeviceModelsByManufacturerIdAsync(Guid manufacturerId);

        /// <summary>
        /// Gets device models for a specific manufacturer that match a specific device role.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer ID</param>
        /// <param name="deviceRole">The device role to filter by</param>
        /// <returns>A collection of device model DTOs that match the criteria</returns>
        Task<List<DeviceModelInfo>> GetDeviceModelsForManufacturerWithRoleAsync(Guid manufacturerId, DeviceRole deviceRole);
    }
}
