using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.DeviceModels;

namespace Barret.Services.Core.Areas.DeviceModels.Repositories
{
    /// <summary>
    /// Repository interface for device models.
    /// </summary>
    public interface IDeviceModelRepository
    {
        /// <summary>
        /// Gets a device model by its ID.
        /// </summary>
        /// <param name="id">The ID of the device model to retrieve</param>
        /// <returns>The device model if found; otherwise, null</returns>
        Task<DeviceModel?> GetByIdAsync(Guid id);

        /// <summary>
        /// Adds a device model to the repository.
        /// </summary>
        /// <param name="deviceModel">The device model to add</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        Task AddAsync(DeviceModel deviceModel);

        /// <summary>
        /// Updates a device model in the repository.
        /// </summary>
        /// <param name="deviceModel">The device model to update</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        Task UpdateAsync(DeviceModel deviceModel);

        /// <summary>
        /// Deletes a device model from the repository.
        /// </summary>
        /// <param name="id">The ID of the device model to delete</param>
        /// <remarks>This method does not call SaveChangesAsync</remarks>
        Task DeleteAsync(Guid id);

        /// <summary>
        /// Gets all device models.
        /// </summary>
        /// <returns>A collection of all device models</returns>
        Task<IEnumerable<DeviceModel>> GetAllAsync();

        /// <summary>
        /// Gets device models for a specific manufacturer.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer ID</param>
        /// <returns>A collection of device models for the manufacturer</returns>
        Task<IEnumerable<DeviceModel>> GetDeviceModelsByManufacturerIdAsync(Guid manufacturerId);

        /// <summary>
        /// Gets device models for a specific manufacturer that match a specific device role.
        /// </summary>
        /// <param name="manufacturerId">The manufacturer ID</param>
        /// <param name="deviceRole">The device role to filter by</param>
        /// <returns>A collection of device models that match the criteria</returns>
        Task<IEnumerable<DeviceModel>> GetDeviceModelsForManufacturerWithRoleAsync(Guid manufacturerId, DeviceRole deviceRole);
    }
}