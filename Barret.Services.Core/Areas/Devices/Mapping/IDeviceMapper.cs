using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Shared.DTOs.Devices;

namespace Barret.Services.Core.Areas.Devices.Mapping
{
    /// <summary>
    /// Interface for mapping between device entities and DTOs
    /// </summary>
    public interface IDeviceMapper
    {
        /// <summary>
        /// Maps a device entity to a DTO
        /// </summary>
        /// <param name="entity">The device entity to map</param>
        /// <returns>A device DTO</returns>
        DeviceDto ToDto(GenericDevice entity);

        /// <summary>
        /// Maps a device DTO to an entity
        /// </summary>
        /// <param name="dto">The device DTO to map</param>
        /// <param name="existingEntity">Optional existing entity to update</param>
        /// <returns>A device entity</returns>
        GenericDevice ToEntity(DeviceDto dto, GenericDevice existingEntity = null);

        /// <summary>
        /// Updates an existing entity with data from a DTO
        /// </summary>
        /// <param name="entity">The entity to update</param>
        /// <param name="dto">The DTO containing the data</param>
        void UpdateEntityFromDto(GenericDevice entity, DeviceDto dto);
    }
}
