using Barret.Core.Areas.Devices.Enums;
using Barret.Shared.DTOs.Devices;

namespace Barret.Services.Core.Areas.Devices.Queries
{
    /// <summary>
    /// Query service interface for devices.
    /// </summary>
    public interface IDeviceQueryService
    {
        /// <summary>
        /// Gets a device by its ID.
        /// </summary>
        /// <param name="id">The ID of the device to retrieve</param>
        /// <returns>The device DTO if found; otherwise, null</returns>
        Task<DeviceDto?> GetDeviceByIdAsync(Guid id);

        /// <summary>
        /// Gets a device with its connections.
        /// </summary>
        /// <param name="id">The ID of the device to retrieve</param>
        /// <returns>The device DTO with its connections if found; otherwise, null</returns>
        Task<DeviceDto?> GetDeviceWithConnectionsAsync(Guid id);

        /// <summary>
        /// Gets all devices.
        /// </summary>
        /// <returns>A list of all device DTOs</returns>
        Task<List<DeviceDto>> GetAllDevicesAsync();

        /// <summary>
        /// Gets devices by role.
        /// </summary>
        /// <param name="role">The role of the devices to retrieve</param>
        /// <returns>A list of device DTOs with the specified role</returns>
        Task<List<DeviceDto>> GetDevicesByRoleAsync(DeviceRole role);

        /// <summary>
        /// Gets devices for a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle</param>
        /// <returns>A list of device DTOs for the vehicle</returns>
        Task<List<DeviceDto>> GetDevicesForVehicleAsync(Guid vehicleId);

        /// <summary>
        /// Gets all connected devices for a device.
        /// </summary>
        /// <param name="deviceId">The ID of the device</param>
        /// <returns>A list of device DTOs that are connected to the specified device</returns>
        Task<List<DeviceDto>> GetConnectedDevicesAsync(Guid deviceId);

        /// <summary>
        /// Gets all device connections for a device.
        /// </summary>
        /// <param name="deviceId">The ID of the device</param>
        /// <returns>A list of device connection DTOs for the device</returns>
        Task<List<DeviceConnectionDto>> GetDeviceConnectionsAsync(Guid deviceId);

        /// <summary>
        /// Gets all compatible devices for a device that can be connected to it.
        /// </summary>
        /// <param name="deviceId">The ID of the device</param>
        /// <returns>A list of device DTOs that are compatible with the device</returns>
        Task<List<DeviceDto>> GetCompatibleDevicesAsync(Guid deviceId);

        /// <summary>
        /// Gets devices by device model ID.
        /// </summary>
        /// <param name="deviceModelId">The ID of the device model</param>
        /// <returns>A list of device DTOs with the specified device model</returns>
        Task<List<DeviceDto>> GetDevicesByModelIdAsync(Guid deviceModelId);
    }
}
