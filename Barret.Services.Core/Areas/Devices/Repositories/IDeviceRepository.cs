using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Services.Core.Areas.Devices.Repositories
{
    /// <summary>
    /// Repository interface for devices.
    /// </summary>
    public interface IDeviceRepository
    {
        /// <summary>
        /// Gets a device by its ID.
        /// </summary>
        /// <param name="id">The ID of the device to retrieve</param>
        /// <returns>The device if found; otherwise, null</returns>
        Task<GenericDevice?> GetByIdAsync(Guid id);

        /// <summary>
        /// Gets a device by its ID with all its connections eagerly loaded through the vehicle.
        /// </summary>
        /// <param name="id">The ID of the device to retrieve</param>
        /// <returns>The device with connections if found; otherwise, null</returns>
        Task<GenericDevice?> GetWithConnectionsAsync(Guid id);

        /// <summary>
        /// Gets all devices.
        /// </summary>
        /// <returns>A collection of all devices</returns>
        Task<IEnumerable<GenericDevice>> GetAllAsync();

        /// <summary>
        /// Gets devices by their IDs.
        /// </summary>
        /// <param name="ids">The IDs of the devices to retrieve</param>
        /// <returns>A list of devices with the specified IDs</returns>
        Task<List<GenericDevice>> GetDevicesByIdsAsync(List<Guid> ids);

        /// <summary>
        /// Gets devices for a vehicle.
        /// </summary>
        /// <param name="vehicleId">The ID of the vehicle</param>
        /// <returns>A list of devices for the vehicle</returns>
        Task<List<GenericDevice>> GetDevicesForVehicleAsync(Guid vehicleId);

        /// <summary>
        /// Gets devices by role.
        /// </summary>
        /// <param name="role">The role of the devices to retrieve</param>
        /// <returns>A list of devices with the specified role</returns>
        Task<List<GenericDevice>> GetDevicesByRoleAsync(DeviceRole role);

        /// <summary>
        /// Gets devices by device model ID.
        /// </summary>
        /// <param name="deviceModelId">The ID of the device model</param>
        /// <returns>A list of devices with the specified device model</returns>
        Task<List<GenericDevice>> GetDevicesByModelIdAsync(Guid deviceModelId);

        /// <summary>
        /// Adds a device to the repository.
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <remarks>
        /// The device should have its VehicleId property set before calling this method.
        /// This method does not call SaveChangesAsync.
        /// </remarks>
        Task AddAsync(GenericDevice device);

        /// <summary>
        /// Updates a device in the repository.
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <remarks>
        /// This method does not call SaveChangesAsync.
        /// </remarks>
        void Update(GenericDevice device);

        /// <summary>
        /// Updates a device in the repository asynchronously.
        /// </summary>
        /// <param name="device">The device to update</param>
        /// <remarks>
        /// This method does not call SaveChangesAsync.
        /// </remarks>
        Task UpdateAsync(GenericDevice device);

        /// <summary>
        /// Deletes a device from the repository.
        /// </summary>
        /// <param name="id">The ID of the device to delete</param>
        /// <remarks>
        /// This method does not call SaveChangesAsync.
        /// </remarks>
        Task DeleteAsync(Guid id);

        /// <summary>
        /// Checks if a device with the specified ID exists.
        /// </summary>
        /// <param name="id">The ID of the device to check</param>
        /// <returns>True if the device exists; otherwise, false</returns>
        Task<bool> ExistsAsync(Guid id);
    }
}