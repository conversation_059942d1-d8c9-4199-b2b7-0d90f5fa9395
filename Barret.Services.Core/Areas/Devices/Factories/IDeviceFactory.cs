using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Services.Core.Areas.Devices.Factories
{
    public interface IDeviceFactory
    {
        /// <summary>
        /// Creates a device with the specified role.
        /// </summary>
        /// <param name="role">The role of the device.</param>
        /// <returns>A new device instance with the specified role.</returns>
        GenericDevice CreateDevice(DeviceRole role);

        /// <summary>
        /// Creates a device with the specified role and name.
        /// </summary>
        /// <param name="role">The role of the device.</param>
        /// <param name="name">The name of the device.</param>
        /// <returns>A new device instance with the specified role and name.</returns>
        GenericDevice CreateDevice(DeviceRole role, string name);
    }
}