using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Manufacturers.Models;
using Barret.Core.Areas.Vehicles.Models;
using Barret.Core.Areas.Vehicles.Models.Vessel;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Infrastructure;

namespace Barret.Services.Core.Areas.Contexts
{
    public interface IBarretDbContext
    {
        DbSet<Vehicle> Vehicles { get; set; }
        DbSet<Vessel> Vessels { get; set; }
        DbSet<GenericDevice> Devices { get; set; }
        DbSet<Manufacturer> Manufacturers { get; set; }
        DbSet<DeviceModel> DeviceModels { get; set; }
        DbSet<T> Set<T>() where T : class;

        // Entry tracking methods
        EntityEntry<TEntity> Entry<TEntity>(TEntity entity) where TEntity : class;
        EntityEntry Attach(object entity);
        EntityEntry<TEntity> Attach<TEntity>(TEntity entity) where TEntity : class;

        // Add explicit Update methods
        EntityEntry<TEntity> Update<TEntity>(TEntity entity) where TEntity : class;
        EntityEntry Update(object entity);

        // Add explicit Add methods
        EntityEntry<TEntity> Add<TEntity>(TEntity entity) where TEntity : class;
        EntityEntry Add(object entity);

        // Add explicit Remove methods
        EntityEntry<TEntity> Remove<TEntity>(TEntity entity) where TEntity : class;
        EntityEntry Remove(object entity);

        // Change tracking
        ChangeTracker ChangeTracker { get; }

        // Database access
        DatabaseFacade Database { get; }

        // Save methods
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
        int SaveChanges();
    }
}