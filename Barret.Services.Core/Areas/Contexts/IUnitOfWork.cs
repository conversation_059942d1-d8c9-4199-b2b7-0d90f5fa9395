using Barret.Services.Core.Areas.Devices.Repositories;
using Barret.Services.Core.Areas.DeviceModels.Repositories;
using Barret.Services.Core.Areas.Manufacturers.Repositories;
using Barret.Services.Core.Areas.Vehicles.Repositories;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Storage;

namespace Barret.Services.Core.Areas.Contexts
{
    /// <summary>
    /// Interface for the Unit of Work pattern for transaction management.
    /// </summary>
    public interface IUnitOfWork : IDisposable
    {
        // Repository properties
        IVesselRepository Vessels { get; }
        IDeviceRepository Devices { get; }
        IDeviceModelRepository DeviceModels { get; }
        IManufacturerRepository Manufacturers { get; }

        // Transaction methods
        Task<IDbContextTransaction> BeginTransactionAsync(CancellationToken cancellationToken = default);
        Task CommitTransactionAsync(CancellationToken cancellationToken = default);
        Task RollbackTransactionAsync(CancellationToken cancellationToken = default);

        // Save changes method
        Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);

        // Tracking methods
        void ClearTracking();
        void DetachAllEntities();

        /// <summary>
        /// Safely attaches an entity to the context, handling any tracking conflicts.
        /// </summary>
        /// <typeparam name="TEntity">The entity type.</typeparam>
        /// <param name="entity">The entity to attach.</param>
        /// <param name="state">The desired entity state.</param>
        /// <returns>The entity entry.</returns>
        EntityEntry<TEntity> SafeAttach<TEntity>(TEntity entity, EntityState state) where TEntity : class;
    }
}