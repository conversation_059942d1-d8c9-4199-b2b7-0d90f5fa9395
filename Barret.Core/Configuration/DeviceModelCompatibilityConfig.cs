using System.Collections.Generic;

namespace Barret.Core.Configuration
{
    /// <summary>
    /// Configuration for device model compatibility relationships.
    /// </summary>
    public class DeviceModelCompatibilityConfig
    {
        /// <summary>
        /// Model-to-model compatibility configuration.
        /// </summary>
        public Dictionary<string, ModelCompatibilityConfig> Models { get; set; } = [];
    }

    /// <summary>
    /// Compatibility configuration for a specific device model.
    /// </summary>
    public class ModelCompatibilityConfig
    {
        /// <summary>
        /// List of device models this model is compatible with.
        /// </summary>
        public List<string> CanConnectTo { get; set; } = [];
    }
}
