namespace Barret.Core.Exceptions
{
    /// <summary>
    /// Exception that is thrown when a requested resource is not found.
    /// </summary>
    public class NotFoundException : Exception
    {
        public NotFoundException() : base() { }

        public NotFoundException(string message) : base(message) { }

        public NotFoundException(string message, Exception innerException) 
            : base(message, innerException) { }
    }
}