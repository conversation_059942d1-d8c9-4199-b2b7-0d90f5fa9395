using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Antennas
{
    /// <summary>
    /// Represents an antenna.
    /// </summary>
    public class Antenna : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Antenna;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Antenna() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="Antenna"/> class.
        /// </summary>
        /// <param name="name">The name of the antenna.</param>
        public Antenna(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor - creates a new antenna as a deep copy of the source antenna.
        /// </summary>
        /// <param name="source">The source antenna to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Antenna(Antenna source) : base(source)
        {
            // Copy antenna-specific properties
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Creates a clone of this antenna.
        /// </summary>
        /// <returns>A new antenna that is a copy of this antenna.</returns>
        public override GenericDevice Clone()
        {
            return new Antenna(this);
        }
    }
}
