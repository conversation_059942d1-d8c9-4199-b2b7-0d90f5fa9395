using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Masts
{
    /// <summary>
    /// Represents a mast structure or device for sensor and antenna mounting.
    /// </summary>
    public class Mast : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Mast;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Mast() : base() { }

        /// <summary>
        /// Initializes a new instance of the Mast class.
        /// </summary>
        /// <param name="name">The name of the mast.</param>
        public Mast(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new mast as a copy of the source mast.
        /// </summary>
        /// <param name="source">The source mast to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Mast(Mast source) : base(source) { }

        /// <summary>
        /// Creates a clone of this mast.
        /// </summary>
        /// <returns>A new mast that is a copy of this mast.</returns>
        public override GenericDevice Clone()
        {
            return new Mast(this);
        }

        /// <summary>
        /// Provides a string representation of this mast.
        /// </summary>
        /// <returns>A string representation of this mast.</returns>
        public override string ToString() =>
            $"Mast {Name} (ID: {Id})";
    }
}
