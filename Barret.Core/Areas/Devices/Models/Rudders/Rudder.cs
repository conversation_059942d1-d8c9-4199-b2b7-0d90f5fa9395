using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Rudders
{
    /// <summary>
    /// Represents a rudder device for steering a vessel.
    /// </summary>
    public class Rudder : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Rudder;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Rudder() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="Rudder"/> class.
        /// </summary>
        /// <param name="name">The name of the rudder.</param>
        public Rudder(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new rudder as a copy of the source rudder.
        /// </summary>
        /// <param name="source">The source rudder to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Rudder(Rudder source) : base(source)
        {
            // All base properties are copied by the base class copy constructor
        }
    }
}
