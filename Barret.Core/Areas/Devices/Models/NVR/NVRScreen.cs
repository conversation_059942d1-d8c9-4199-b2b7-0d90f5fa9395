using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.NVR
{
    /// <summary>
    /// Represents an NVR Screen device for displaying video feeds.
    /// </summary>
    public class NVRScreen : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.NVRScreen;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected NVRScreen() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="NVRScreen"/> class.
        /// </summary>
        /// <param name="name">The name of the NVR screen.</param>
        public NVRScreen(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new NVR screen as a copy of the source NVR screen.
        /// </summary>
        /// <param name="source">The source NVR screen to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public NVRScreen(NVRScreen source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this NVR screen.
        /// </summary>
        /// <returns>A new NVR screen that is a copy of this NVR screen.</returns>
        public override GenericDevice Clone()
        {
            return new NVRScreen(this);
        }
    }
}
