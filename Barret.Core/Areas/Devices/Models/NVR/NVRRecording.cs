using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.NVR
{
    /// <summary>
    /// Represents an NVR Recording device for storing video feeds.
    /// </summary>
    public class NVRRecording : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.NVRRecording;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected NVRRecording() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="NVRRecording"/> class.
        /// </summary>
        /// <param name="name">The name of the NVR recording device.</param>
        public NVRRecording(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new NVR recording device as a copy of the source NVR recording device.
        /// </summary>
        /// <param name="source">The source NVR recording device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public NVRRecording(NVRRecording source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this NVR recording device.
        /// </summary>
        /// <returns>A new NVR recording device that is a copy of this NVR recording device.</returns>
        public override GenericDevice Clone()
        {
            return new NVRRecording(this);
        }
    }
}
