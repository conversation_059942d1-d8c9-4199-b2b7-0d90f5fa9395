using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Audio
{
    /// <summary>
    /// Represents an AMP (Audio Management Processor) device.
    /// </summary>
    public class AMP : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.AMP;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected AMP() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="AMP"/> class.
        /// </summary>
        /// <param name="name">The name of the AMP device.</param>
        public AMP(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new AMP device as a copy of the source AMP device.
        /// </summary>
        /// <param name="source">The source AMP device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public AMP(AMP source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this AMP device.
        /// </summary>
        /// <returns>A new AMP device that is a copy of this AMP device.</returns>
        public override GenericDevice Clone()
        {
            return new AMP(this);
        }
    }
}
