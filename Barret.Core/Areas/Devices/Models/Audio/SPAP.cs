using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Audio
{
    /// <summary>
    /// Represents an SP & AP (Signal Processor & Audio Processor) device.
    /// </summary>
    public class SPAP : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.SPAP;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected SPAP() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="SPAP"/> class.
        /// </summary>
        /// <param name="name">The name of the SP & AP device.</param>
        public SPAP(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new SP & AP device as a copy of the source SP & AP device.
        /// </summary>
        /// <param name="source">The source SP & AP device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public SPAP(SPAP source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }
    }
}
