using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Audio
{
    /// <summary>
    /// Represents a PA Audio device for public announcements.
    /// </summary>
    public class PAAudio : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.PAAudio;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected PAAudio() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="PAAudio"/> class.
        /// </summary>
        /// <param name="name">The name of the PA audio device.</param>
        public PAAudio(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new PA audio device as a copy of the source PA audio device.
        /// </summary>
        /// <param name="source">The source PA audio device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public PAAudio(PAAudio source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }
    }
}
