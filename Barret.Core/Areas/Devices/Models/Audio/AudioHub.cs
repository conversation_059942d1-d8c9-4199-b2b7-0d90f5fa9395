using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Audio
{
    /// <summary>
    /// Represents an Audio Hub device for sound management.
    /// </summary>
    public class AudioHub : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.AudioHub;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected AudioHub() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="AudioHub"/> class.
        /// </summary>
        /// <param name="name">The name of the audio hub.</param>
        public AudioHub(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new audio hub as a copy of the source audio hub.
        /// </summary>
        /// <param name="source">The source audio hub to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public AudioHub(AudioHub source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this audio hub.
        /// </summary>
        /// <returns>A new audio hub that is a copy of this audio hub.</returns>
        public override GenericDevice Clone()
        {
            return new AudioHub(this);
        }
    }
}
