using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Sensors
{
    /// <summary>
    /// Represents a sensor device for environmental monitoring.
    /// </summary>
    public class Sensor : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Sensor;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Sensor() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="Sensor"/> class.
        /// </summary>
        /// <param name="name">The name of the sensor.</param>
        public Sensor(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new sensor as a copy of the source sensor.
        /// </summary>
        /// <param name="source">The source sensor to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Sensor(Sensor source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this sensor.
        /// </summary>
        /// <returns>A new sensor that is a copy of this sensor.</returns>
        public override GenericDevice Clone()
        {
            return new Sensor(this);
        }
    }
}
