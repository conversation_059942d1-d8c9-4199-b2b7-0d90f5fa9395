using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Sensors
{
    /// <summary>
    /// Represents a Navigation Data (Serial-to-Ethernet) converter.
    /// </summary>
    public class NavData : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.NavData;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected NavData() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="NavData"/> class.
        /// </summary>
        /// <param name="name">The name of the navigation data device.</param>
        public NavData(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new navigation data device as a copy of the source navigation data device.
        /// </summary>
        /// <param name="source">The source navigation data device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public NavData(NavData source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this navigation data device.
        /// </summary>
        /// <returns>A new navigation data device that is a copy of this navigation data device.</returns>
        public override GenericDevice Clone()
        {
            return new NavData(this);
        }
    }
}
