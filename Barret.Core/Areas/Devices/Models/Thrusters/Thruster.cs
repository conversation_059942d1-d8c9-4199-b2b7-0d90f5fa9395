using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using System;

namespace Barret.Core.Areas.Devices.Models.Thrusters
{
    /// <summary>
    /// Represents a thruster device that provides auxiliary propulsion for a vessel.
    /// </summary>
    public class Thruster : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Thruster;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Thruster() : base() { }

        /// <summary>
        /// Initializes a new instance of the Thruster class.
        /// </summary>
        /// <param name="name">The name of the thruster.</param>
        public Thruster(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new thruster as a copy of the source thruster.
        /// </summary>
        /// <param name="source">The source thruster to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Thruster(Thruster source) : base(source) { }

        /// <summary>
        /// Creates a clone of this thruster.
        /// </summary>
        /// <returns>A new thruster that is a copy of this thruster.</returns>
        public override GenericDevice Clone()
        {
            return new Thruster(this);
        }

        /// <summary>
        /// Provides a string representation of this thruster.
        /// </summary>
        /// <returns>A string representation of this thruster.</returns>
        public override string ToString() =>
            $"Thruster {Name} (ID: {Id})";
    }
}
