using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Thrusters;
using System;

namespace Barret.Core.Areas.Devices.Models.Engines
{
    /// <summary>
    /// Represents an engine device that provides propulsion for a vehicle.
    /// </summary>
    public class Engine : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Engine;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Engine() : base() { }

        /// <summary>
        /// Initializes a new instance of the Engine class.
        /// </summary>
        /// <param name="name">The name of the engine.</param>
        public Engine(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new engine as a copy of the source engine.
        /// </summary>
        /// <param name="source">The source engine to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Engine(Engine source) : base(source) { }

        /// <summary>
        /// Creates a clone of this engine.
        /// </summary>
        /// <returns>A new engine that is a copy of this engine.</returns>
        public override GenericDevice Clone()
        {
            return new Engine(this);
        }

        /// <summary>
        /// Provides a string representation of this engine.
        /// </summary>
        /// <returns>A string representation of this engine.</returns>
        public override string ToString() =>
            $"Engine {Name} (ID: {Id})";
    }
}