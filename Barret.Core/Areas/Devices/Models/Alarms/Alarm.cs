using Barret.Core.Areas.Devices.Enums;

namespace Barret.Core.Areas.Devices.Models.Alarms
{
    /// <summary>
    /// Represents an immutable alarm condition that can be associated with a device.
    /// This is a value object implemented as a C# record type, following the same pattern as DeviceConnection.
    /// </summary>
    public sealed record Alarm
    {
        /// <summary>
        /// Gets the unique identifier for this alarm.
        /// </summary>
        public Guid Id { get; init; }

        /// <summary>
        /// Gets the description of the alarm.
        /// </summary>
        public string Description { get; init; }

        /// <summary>
        /// Gets the notification type for this alarm.
        /// </summary>
        public NotificationType NotificationType { get; init; }

        /// <summary>
        /// Gets the alarm message for Michel<PERSON> integration.
        /// </summary>
        public string Message { get; init; }

        /// <summary>
        /// Gets the notification group identifier for this alarm.
        /// </summary>
        public NotificationGroupId NotificationGroupId { get; init; }

        /// <summary>
        /// Gets the entity identifier associated with this alarm.
        /// </summary>
        public string EntityId { get; init; }

        /// <summary>
        /// Gets the warning identifier for this alarm (corresponds to DomainDriverId in Excel).
        /// </summary>
        public WarningId WarningId { get; init; }

        /// <summary>
        /// Parameterless constructor for EF Core.
        /// </summary>
        private Alarm()
        {
            // Empty constructor for EF Core
            Id = Guid.Empty;
            Description = string.Empty;
            NotificationType = NotificationType.Undefined;
            Message = string.Empty;
            NotificationGroupId = NotificationGroupId.Undefined;
            EntityId = string.Empty;
            WarningId = WarningId.Undefined;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Alarm"/> record for new alarms.
        /// </summary>
        /// <param name="description">The description of the alarm.</param>
        /// <param name="notificationType">The notification type for this alarm.</param>
        /// <param name="message">The alarm message for Michelangelo integration.</param>
        /// <param name="notificationGroupId">The notification group identifier.</param>
        /// <param name="entityId">The entity identifier associated with this alarm.</param>
        /// <param name="warningId">The warning identifier (corresponds to DomainDriverId in Excel).</param>
        /// <exception cref="ArgumentException">Thrown when required parameters are invalid.</exception>
        public Alarm(string description, NotificationType notificationType,
                    string message = "", NotificationGroupId notificationGroupId = NotificationGroupId.Undefined,
                    string entityId = "", WarningId warningId = WarningId.Undefined)
        {
            if (string.IsNullOrWhiteSpace(description))
                throw new ArgumentException("Description cannot be empty or whitespace", nameof(description));

            if (notificationType == NotificationType.Undefined)
                throw new ArgumentException("Notification type cannot be undefined", nameof(notificationType));

            Id = Guid.NewGuid();
            Description = description;
            NotificationType = notificationType;
            Message = message ?? string.Empty;
            NotificationGroupId = notificationGroupId;
            EntityId = entityId ?? string.Empty;
            WarningId = warningId;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Alarm"/> record with a specific ID.
        /// This constructor is used when recreating alarms from DTOs to preserve identity.
        /// </summary>
        /// <param name="id">The unique identifier for this alarm.</param>
        /// <param name="description">The description of the alarm.</param>
        /// <param name="notificationType">The notification type for this alarm.</param>
        /// <param name="message">The alarm message for Michelangelo integration.</param>
        /// <param name="notificationGroupId">The notification group identifier.</param>
        /// <param name="entityId">The entity identifier associated with this alarm.</param>
        /// <param name="warningId">The warning identifier (corresponds to DomainDriverId in Excel).</param>
        /// <exception cref="ArgumentException">Thrown when required parameters are invalid.</exception>
        public Alarm(Guid id, string description, NotificationType notificationType, string message,
                    NotificationGroupId notificationGroupId, string entityId, WarningId warningId)
        {
            if (id == Guid.Empty)
                throw new ArgumentException("ID cannot be empty", nameof(id));

            if (string.IsNullOrWhiteSpace(description))
                throw new ArgumentException("Description cannot be empty or whitespace", nameof(description));

            if (notificationType == NotificationType.Undefined)
                throw new ArgumentException("Notification type cannot be undefined", nameof(notificationType));

            Id = id;
            Description = description;
            NotificationType = notificationType;
            Message = message ?? string.Empty;
            NotificationGroupId = notificationGroupId;
            EntityId = entityId ?? string.Empty;
            WarningId = warningId;
        }

    }
}