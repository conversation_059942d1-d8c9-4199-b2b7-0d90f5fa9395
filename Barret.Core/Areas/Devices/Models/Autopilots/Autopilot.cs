using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Radars;
using System;

namespace Barret.Core.Areas.Devices.Models.Autopilots
{
    /// <summary>
    /// Represents an autopilot device that provides automated steering for a vessel.
    /// </summary>
    public class Autopilot : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Autopilot;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Autopilot() : base() { }

        /// <summary>
        /// Initializes a new instance of the Autopilot class.
        /// </summary>
        /// <param name="name">The name of the autopilot.</param>
        public Autopilot(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new autopilot as a copy of the source autopilot.
        /// </summary>
        /// <param name="source">The source autopilot to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Autopilot(Autopilot source) : base(source) { }

        /// <summary>
        /// Creates a clone of this autopilot.
        /// </summary>
        /// <returns>A new autopilot that is a copy of this autopilot.</returns>
        public override GenericDevice Clone()
        {
            return new Autopilot(this);
        }

        /// <summary>
        /// Provides a string representation of this autopilot.
        /// </summary>
        /// <returns>A string representation of this autopilot.</returns>
        public override string ToString() =>
            $"Autopilot {Name} (ID: {Id})";
    }
}
