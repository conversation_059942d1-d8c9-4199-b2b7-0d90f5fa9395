using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.Autopilots;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.ValueObjects;
using System;

namespace Barret.Core.Areas.Devices.Models.Radars
{
    /// <summary>
    /// Represents a radar device that provides detection capabilities for a vehicle.
    /// </summary>
    public class Radar : GenericDevice
    {
        private Position? _maritimePosition;

        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Radar;

        /// <summary>
        /// Gets the maritime position of this radar (Bow/Stern + Port/Starboard/Mast)
        /// Used for driver configuration and operational context.
        /// Returns null if no maritime position has been configured.
        /// </summary>
        public Position? MaritimePosition
        {
            get => _maritimePosition;
            private set => _maritimePosition = value;
        }

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Radar() : base() { }

        /// <summary>
        /// Initializes a new instance of the Radar class.
        /// </summary>
        /// <param name="name">The name of the radar.</param>
        public Radar(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new radar as a copy of the source radar.
        /// </summary>
        /// <param name="source">The source radar to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Radar(Radar source) : base(source)
        {
            if (source != null)
            {
                _maritimePosition = source.MaritimePosition;
            }
        }

        /// <summary>
        /// Sets the maritime position of this radar
        /// </summary>
        /// <param name="position">The maritime position to set, or null to clear the position</param>
        /// <returns>This radar instance for fluent API</returns>
        public Radar SetMaritimePosition(Position? position)
        {
            MaritimePosition = position;
            return this;
        }

        /// <summary>
        /// Sets the maritime position using individual components
        /// </summary>
        /// <param name="foreAft">The fore-aft position (None, Bow, or Stern)</param>
        /// <param name="lateral">The lateral position (Port, Starboard, Mast, etc.)</param>
        /// <param name="facing">The optional facing direction</param>
        /// <returns>This radar instance for fluent API</returns>
        public Radar SetMaritimePosition(ForeAftPosition foreAft, LateralPosition lateral = LateralPosition.None, FacingDirection? facing = null)
        {
            MaritimePosition = new Position(foreAft, lateral, facing);
            return this;
        }

        /// <summary>
        /// Clears the maritime position of this radar
        /// </summary>
        /// <returns>This radar instance for fluent API</returns>
        public Radar ClearMaritimePosition()
        {
            MaritimePosition = null;
            return this;
        }

        /// <summary>
        /// Creates a clone of this radar.
        /// </summary>
        /// <returns>A new radar that is a copy of this radar.</returns>
        public override GenericDevice Clone()
        {
            return new Radar(this);
        }

        /// <summary>
        /// Provides a string representation of this radar.
        /// </summary>
        /// <returns>A string representation of this radar.</returns>
        public override string ToString() =>
            $"Radar {Name} (ID: {Id})";
    }
}
