using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Microsoft.Extensions.Logging;
using System;
using System.Diagnostics;

namespace Barret.Core.Areas.Devices.Models.Cameras
{
    /// <summary>
    /// Represents a camera device that provides visual capture for a vehicle.
    /// </summary>
    public class Camera : GenericDevice
    {
        private bool _showVideo;

        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Camera;

        /// <summary>
        /// Gets or sets whether to show video for this camera.
        /// </summary>
        public bool ShowVideo
        {
            get
            {
                Debug.WriteLine($"[Camera {Id}] Getting ShowVideo = {_showVideo}");
                return _showVideo;
            }
            set
            {
                Debug.WriteLine($"[Camera {Id}] Setting ShowVideo from {_showVideo} to {value}");
                _showVideo = value;
                Debug.WriteLine($"[Camera {Id}] ShowVideo is now {_showVideo}");
            }
        }

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Camera() : base()
        {
            // Initialize with a neutral default value that can be overridden
            _showVideo = false;
            Debug.WriteLine($"[Camera {Id}] Created with default constructor, ShowVideo = {_showVideo}");
        }

        /// <summary>
        /// Initializes a new instance of the Camera class.
        /// </summary>
        /// <param name="name">The name of the camera.</param>
        public Camera(string name) : base(name)
        {
            // Initialize with a neutral default value that can be overridden
            _showVideo = false;
            Debug.WriteLine($"[Camera {Id}] Created with name constructor, ShowVideo = {_showVideo}");
        }

        /// <summary>
        /// Initializes a new instance of the Camera class with specified ShowVideo value.
        /// </summary>
        /// <param name="name">The name of the camera.</param>
        /// <param name="showVideo">Whether to show video for this camera.</param>
        public Camera(string name, bool showVideo) : base(name)
        {
            _showVideo = showVideo;
            Debug.WriteLine($"[Camera {Id}] Created with name and showVideo constructor, ShowVideo = {_showVideo}");
        }

        /// <summary>
        /// Copy constructor that creates a new camera as a copy of the source camera.
        /// </summary>
        /// <param name="source">The source camera to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Camera(Camera source) : base(source)
        {
            _showVideo = source.ShowVideo;
            Debug.WriteLine($"[Camera {Id}] Created with copy constructor, ShowVideo = {_showVideo}");
        }

        /// <summary>
        /// Creates a clone of this camera.
        /// </summary>
        /// <returns>A new camera that is a copy of this camera.</returns>
        public override GenericDevice Clone()
        {
            return new Camera(this);
        }

        /// <summary>
        /// Provides a string representation of this camera.
        /// </summary>
        /// <returns>A string representation of this camera.</returns>
        public override string ToString() =>
            $"Camera {Name} (ID: {Id})";
    }
}