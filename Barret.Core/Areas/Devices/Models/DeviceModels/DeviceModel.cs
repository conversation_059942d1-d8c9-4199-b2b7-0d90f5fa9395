using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Manufacturers.Models;

namespace Barret.Core.Areas.Devices.Models.DeviceModels
{
    /// <summary>
    /// Represents a model of device that a manufacturer produces.
    /// </summary>
    public class DeviceModel
    {
        private string _name;
        private DeviceRole _deviceRole;
        private Manufacturer? _manufacturer;
        private Guid _manufacturerId;

        /// <summary>
        /// Gets the unique identifier of the device model.
        /// </summary>
        public Guid Id { get; } = Guid.NewGuid();

        /// <summary>
        /// Gets or sets the name of the device model.
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("Name cannot be empty", nameof(value));

                _name = value;
            }
        }

        /// <summary>
        /// Gets or sets the role of this device model.
        /// </summary>
        public DeviceRole DeviceRole
        {
            get => _deviceRole;
            internal set => _deviceRole = value;  // Can only be changed internally
        }

        /// <summary>
        /// Gets the manufacturer of this device model.
        /// </summary>
        public Manufacturer? Manufacturer
        {
            get => _manufacturer;
            internal set
            {
                _manufacturer = value;
                ManufacturerId = value?.Id ?? Guid.Empty;
            }
        }

        /// <summary>
        /// Gets the foreign key for the manufacturer.
        /// </summary>
        public Guid ManufacturerId
        {
            get => _manufacturerId;
            private set => _manufacturerId = value;
        }

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected DeviceModel()
        {
            _name = string.Empty;
        }

        /// <summary>
        /// Initializes a new instance of the DeviceModel class.
        /// </summary>
        /// <param name="name">The name of the device model.</param>
        /// <param name="role">The role of the device model.</param>
        /// <exception cref="ArgumentException">Thrown when the name is null or whitespace.</exception>
        public DeviceModel(string name, DeviceRole role)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Name cannot be empty", nameof(name));

            _name = name;
            _deviceRole = role;
        }

        /// <summary>
        /// Sets the manufacturer of this device model.
        /// This method should only be called by the Manufacturer aggregate root.
        /// </summary>
        /// <param name="manufacturer">The manufacturer to set.</param>
        internal void SetManufacturer(Manufacturer manufacturer)
        {
            ArgumentNullException.ThrowIfNull(manufacturer);

            Manufacturer = manufacturer;
            ManufacturerId = manufacturer.Id;
        }

        /// <summary>
        /// Gets the manufacturer of this device model.
        /// </summary>
        /// <returns>The manufacturer, or null if not set.</returns>
        public Manufacturer? GetManufacturer()
        {
            return Manufacturer;
        }
    }
}