using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Radios
{
    /// <summary>
    /// Represents a VHF Network Interface for connecting VHF systems to networks.
    /// </summary>
    public class VHFNetworkInterface : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.VHFNetworkInterface;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected VHFNetworkInterface() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="VHFNetworkInterface"/> class.
        /// </summary>
        /// <param name="name">The name of the VHF network interface.</param>
        public VHFNetworkInterface(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="VHFNetworkInterface"/> class as a copy of another instance.
        /// </summary>
        /// <param name="source">The source VHF network interface to copy from.</param>
        public VHFNetworkInterface(VHFNetworkInterface source) : base(source)
        {
            // Copy any additional properties specific to VHFNetworkInterface here
        }
    }
}
