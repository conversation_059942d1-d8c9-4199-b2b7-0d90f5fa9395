using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Radios
{
    /// <summary>
    /// Represents a VHF Mariphone for marine communication.
    /// </summary>
    public class VHFMariphone : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.VHFMariphone;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected VHFMariphone() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="VHFMariphone"/> class.
        /// </summary>
        /// <param name="name">The name of the VHF mariphone.</param>
        public VHFMariphone(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new VHF mariphone as a copy of the source VHF mariphone.
        /// </summary>
        /// <param name="source">The source VHF mariphone to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public VHFMariphone(VHFMariphone source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this VHF mariphone.
        /// </summary>
        /// <returns>A new VHF mariphone that is a copy of this VHF mariphone.</returns>
        public override GenericDevice Clone()
        {
            return new VHFMariphone(this);
        }
    }
}
