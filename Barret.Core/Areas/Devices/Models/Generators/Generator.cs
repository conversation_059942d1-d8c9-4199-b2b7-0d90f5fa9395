using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Generators
{
    /// <summary>
    /// Represents a generator device for electrical power generation.
    /// </summary>
    public class Generator : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Generator;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Generator() : base() { }

        /// <summary>
        /// Initializes a new instance of the Generator class.
        /// </summary>
        /// <param name="name">The name of the generator.</param>
        public Generator(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new generator as a copy of the source generator.
        /// </summary>
        /// <param name="source">The source generator to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Generator(Generator source) : base(source) { }

        /// <summary>
        /// Creates a clone of this generator.
        /// </summary>
        /// <returns>A new generator that is a copy of this generator.</returns>
        public override GenericDevice Clone()
        {
            return new Generator(this);
        }

        /// <summary>
        /// Provides a string representation of this generator.
        /// </summary>
        /// <returns>A string representation of this generator.</returns>
        public override string ToString() =>
            $"Generator {Name} (ID: {Id})";
    }
}
