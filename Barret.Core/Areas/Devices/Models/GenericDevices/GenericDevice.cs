using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Extensions;
using Barret.Core.Areas.Common.ValueObjects;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Barret.Core.Areas.Devices.Models.Alarms;
using Barret.Core.Areas.Devices.Exceptions;
using System;

namespace Barret.Core.Areas.Devices.Models.GenericDevices
{
    /// <summary>
    /// Base class for all devices that can be installed on a vehicle.
    /// Can also be instantiated directly as a generic device.
    /// </summary>
    public class GenericDevice
    {
        private readonly List<Alarm> _alarms = [];
        private string _name = string.Empty;
        private RelativePosition _position = new(0, 0, 0);
        private ConnectionHandler? _connection;
        private DeviceModel? _model;
        protected DeviceRole _deviceRole = DeviceRole.Generic;
        private Guid? _vehicleId;

        /// <summary>
        /// Gets the unique identifier of this device.
        /// </summary>
        public Guid Id { get; private set; }

        /// <summary>
        /// Gets or sets the name of this device.
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("Device name cannot be empty", nameof(value));

                _name = value;
            }
        }

        /// <summary>
        /// Gets or sets the position of this device relative to the vehicle.
        /// </summary>
        public RelativePosition Position
        {
            get => _position;
            private set => _position = value;
        }

        /// <summary>
        /// Updates the position and returns this device instance for fluent API.
        /// </summary>
        /// <param name="position">The new position.</param>
        /// <returns>This device instance.</returns>
        public GenericDevice WithPosition(RelativePosition position)
        {
            ArgumentNullException.ThrowIfNull(position, nameof(position));
            _position = position;
            return this;
        }

        /// <summary>
        /// Gets or sets the connection details for this device.
        /// Can be null if the device doesn't have a direct connection.
        /// </summary>
        public ConnectionHandler? Connection
        {
            get => _connection;
            private set => _connection = value;
        }

        /// <summary>
        /// Updates the connection details and returns this device instance for fluent API.
        /// </summary>
        /// <param name="connection">The new connection details, or null to remove the connection.</param>
        /// <returns>This device instance.</returns>
        public GenericDevice WithConnection(ConnectionHandler? connection)
        {
            _connection = connection;
            return this;
        }

        /// <summary>
        /// Determines whether this device has a direct connection with a valid IP address.
        /// </summary>
        /// <returns>True if the device has a connection with a valid IP address; otherwise, false.</returns>
        public bool HasConnection()
        {
            return _connection != null && !string.IsNullOrWhiteSpace(_connection.IPAddress);
        }

        /// <summary>
        /// Determines whether this device can serve as an interface for a device with the specified role.
        /// </summary>
        /// <param name="deviceRole">The role of the device to check compatibility with.</param>
        /// <returns>True if this device can serve as an interface for a device with the specified role; otherwise, false.</returns>
        public virtual bool CanServeAsInterfaceFor(DeviceRole deviceRole)
        {
            // Check if the specified device role is compatible with this device's role
            return deviceRole.IsCompatibleWith(DeviceRole);
        }

        /// <summary>
        /// Gets connection details for a specific connected device.
        /// This method is intended to be used by the configuration generation system
        /// to determine how to configure connections between devices.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the connected device.</param>
        /// <returns>A tuple containing the connection details: (bool HasDirectConnection, string IPAddress, int Port, Protocol Protocol).</returns>
        /// <remarks>
        /// If this device has a direct connection, the connection details are returned.
        /// This method is designed to be overridden by derived classes that need to provide
        /// specific connection details based on their role or configuration.
        /// </remarks>
        public virtual (bool HasDirectConnection, string IPAddress, int Port, Protocol Protocol) GetConnectionDetails(Guid connectedDeviceId)
        {
            // By default, return this device's connection details if it has a direct connection
            if (HasConnection())
            {
                return (true, _connection.IPAddress, _connection.Port, _connection.Protocol);
            }

            // No direct connection
            return (false, string.Empty, 0, Protocol.Undefined);
        }

        /// <summary>
        /// Removes the connection from this device.
        /// </summary>
        /// <returns>This device instance.</returns>
        public GenericDevice RemoveConnection()
        {
            _connection = null;
            return this;
        }

        /// <summary>
        /// Gets the device model ID.
        /// </summary>
        public Guid? DeviceModelId { get; private set; }

        /// <summary>
        /// Gets the device model.
        /// </summary>
        public DeviceModel? Model => _model;

        /// <summary>
        /// Sets the device model.
        /// </summary>
        /// <param name="model">The device model to set, or null to clear the model.</param>
        /// <returns>This device instance for method chaining.</returns>
        /// <exception cref="DeviceValidationException">Thrown when the model role doesn't match the device role.</exception>
        public GenericDevice SetModel(DeviceModel? model)
        {
            if (model != null && model.DeviceRole != DeviceRole)
            {
                throw new DeviceValidationException(Id,
                    $"Device model role mismatch: Model has role {model.DeviceRole} but device has role {DeviceRole}");
            }

            _model = model;
            DeviceModelId = model?.Id;
            return this;
        }

        /// <summary>
        /// Sets the device model by ID.
        /// Note: This does not load the actual model object, which must be done separately.
        /// </summary>
        /// <param name="deviceModelId">The ID of the device model to set, or null to clear the model.</param>
        /// <returns>This device instance for method chaining.</returns>
        public GenericDevice SetDeviceModelId(Guid? deviceModelId)
        {
            DeviceModelId = deviceModelId;
            if (deviceModelId == null)
            {
                _model = null;
            }
            return this;
        }

        /// <summary>
        /// Gets the name of the device model, or "No Model" if no model is set.
        /// </summary>
        /// <returns>The name of the device model, or "No Model" if no model is set.</returns>
        public string GetDeviceModelName()
        {
            return Model?.Name ?? "No Model";
        }

        /// <summary>
        /// Gets the device role from the model, or Undefined if no model is set.
        /// </summary>
        /// <returns>The device role from the model, or Undefined if no model is set.</returns>
        public DeviceRole GetDeviceModelRole()
        {
            return Model?.DeviceRole ?? DeviceRole.Undefined;
        }

        /// <summary>
        /// Clears the device model.
        /// </summary>
        /// <returns>This device instance for method chaining.</returns>
        public GenericDevice ClearModel()
        {
            _model = null;
            DeviceModelId = null;
            return this;
        }

        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public virtual DeviceRole DeviceRole => _deviceRole;

        /// <summary>
        /// Gets a read-only collection of alarms associated with this device.
        /// </summary>
        public IReadOnlyCollection<Alarm> Alarms => _alarms.AsReadOnly();

        /// <summary>
        /// Gets or sets the ID of the vehicle this device is installed on.
        /// </summary>
        public Guid? VehicleId
        {
            get => _vehicleId;
            set => _vehicleId = value;
        }

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected GenericDevice()
        {
            // Initialize with a new ID
            Id = Guid.NewGuid();
        }

        /// <summary>
        /// Initializes a new instance of the GenericDevice class with a name.
        /// </summary>
        /// <param name="name">The name of the device.</param>
        /// <exception cref="ArgumentException">Thrown when name is null or whitespace.</exception>
        public GenericDevice(string name)
            : this()
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Device name cannot be empty", nameof(name));

            _name = name;
        }

        /// <summary>
        /// Copy constructor that creates a new device as a copy of the source device.
        /// </summary>
        /// <param name="source">The source device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public GenericDevice(GenericDevice source)
            : this()
        {
            ArgumentNullException.ThrowIfNull(source, nameof(source));

            _name = source.Name;

            // Copy the device role from the source device
            // This is critical for the Generic base class to maintain the correct role
            _deviceRole = source.DeviceRole;

            // Create a new position object to ensure it's properly copied
            _position = new RelativePosition(source.Position.X, source.Position.Y, source.Position.Z);

            // Copy the connection handler if it exists
            // ConnectionHandler is an immutable value object, so we need to create a new instance
            if (source.Connection != null)
            {
                _connection = new ConnectionHandler(
                    source.Connection.IPAddress,
                    source.Connection.Port,
                    source.Connection.Protocol);
            }
            else
            {
                _connection = null;
            }

            // Copy the model reference if available
            // Use the SetModel method to ensure proper validation
            if (source._model != null)
            {
                SetModel(source._model);
            }

            // Copy alarms - create new instances to ensure proper copying
            foreach (var alarm in source._alarms)
            {
                _alarms.Add(new Alarm(alarm.Description, alarm.NotificationType, alarm.Message,
                    alarm.NotificationGroupId, alarm.EntityId, alarm.WarningId));
            }

            // Don't copy the VehicleId as this is a new device that needs to be assigned to a vehicle
        }

        /// <summary>
        /// Adds an alarm to this device.
        /// </summary>
        /// <param name="alarm">The alarm to add.</param>
        /// <returns>True if the alarm was added; false if it already exists.</returns>
        /// <exception cref="ArgumentNullException">Thrown when alarm is null.</exception>
        public bool AddAlarm(Alarm alarm)
        {
            ArgumentNullException.ThrowIfNull(alarm, nameof(alarm));

            // Check if the alarm already exists (by ID)
            if (_alarms.Any(a => a.Id == alarm.Id))
                return false;

            _alarms.Add(alarm);
            return true;
        }

        /// <summary>
        /// Adds an alarm to this device with individual parameters.
        /// </summary>
        /// <param name="description">The alarm description.</param>
        /// <param name="notificationType">The notification type.</param>
        /// <param name="message">The alarm message for Michelangelo integration.</param>
        /// <param name="notificationGroupId">The notification group identifier.</param>
        /// <param name="entityId">The entity identifier associated with this alarm.</param>
        /// <param name="warningId">The warning identifier.</param>
        /// <returns>True if the alarm was added; false if it already exists.</returns>
        public bool AddAlarm(string description, NotificationType notificationType, string message,
            NotificationGroupId notificationGroupId, string entityId, WarningId warningId)
        {
            var alarm = new Alarm(description, notificationType, message, notificationGroupId, entityId, warningId);
            return AddAlarm(alarm);
        }

        /// <summary>
        /// Removes an alarm from this device.
        /// </summary>
        /// <param name="alarm">The alarm to remove.</param>
        /// <returns>True if the alarm was removed; false if it didn't exist.</returns>
        /// <exception cref="ArgumentNullException">Thrown when alarm is null.</exception>
        public bool RemoveAlarm(Alarm alarm)
        {
            ArgumentNullException.ThrowIfNull(alarm, nameof(alarm));
            return _alarms.Remove(alarm);
        }

        /// <summary>
        /// Removes an alarm from this device by ID.
        /// </summary>
        /// <param name="alarmId">The ID of the alarm to remove.</param>
        /// <returns>True if the alarm was removed; false if it didn't exist.</returns>
        public bool RemoveAlarmById(Guid alarmId)
        {
            var alarm = _alarms.FirstOrDefault(a => a.Id == alarmId);
            if (alarm != null)
            {
                return _alarms.Remove(alarm);
            }
            return false;
        }

        /// <summary>
        /// Replaces an existing alarm with a new immutable alarm instance.
        /// This maintains alarm immutability while allowing "updates" through replacement.
        /// </summary>
        /// <param name="oldAlarmId">The ID of the alarm to replace.</param>
        /// <param name="newAlarm">The new alarm instance to add.</param>
        /// <returns>True if the alarm was replaced; false if the old alarm wasn't found.</returns>
        public bool ReplaceAlarm(Guid oldAlarmId, Alarm newAlarm)
        {
            ArgumentNullException.ThrowIfNull(newAlarm, nameof(newAlarm));

            // Find and remove the old alarm
            var oldAlarm = _alarms.FirstOrDefault(a => a.Id == oldAlarmId);
            if (oldAlarm == null)
            {
                return false; // Old alarm not found
            }

            // Remove old alarm and add new one
            _alarms.Remove(oldAlarm);
            _alarms.Add(newAlarm);
            return true;
        }

        /// <summary>
        /// Updates the device with a new set of alarms and returns this device instance for fluent API.
        /// This follows the same pattern as WithPosition() and WithConnection().
        /// </summary>
        /// <param name="alarms">The new collection of alarms.</param>
        /// <returns>This device instance.</returns>
        public GenericDevice WithAlarms(IEnumerable<Alarm> alarms)
        {
            ArgumentNullException.ThrowIfNull(alarms, nameof(alarms));
            _alarms.Clear();
            _alarms.AddRange(alarms);
            return this;
        }

        /// <summary>
        /// Clears all alarms from this device.
        /// </summary>
        public void ClearAlarms()
        {
            _alarms.Clear();
        }

        /// <summary>
        /// Validates that the device has all required properties set and meets domain invariants.
        /// </summary>
        /// <returns>True if valid.</returns>
        /// <exception cref="DeviceValidationException">Thrown when validation fails.</exception>
        public virtual bool Validate()
        {
            // Validate name
            if (string.IsNullOrEmpty(Name))
                throw new DeviceValidationException(Id, "Device name cannot be empty");

            // Validate connection
            if (Connection == null)
                throw new DeviceValidationException(Id, "Device connection cannot be null");

            // Validate position
            if (Position == null)
                throw new DeviceValidationException(Id, "Device position cannot be null");

            return true;
        }

        /// <summary>
        /// Creates a clone of this device.
        /// </summary>
        /// <returns>A new device that is a copy of this device.</returns>
        public virtual GenericDevice Clone()
        {
            return new GenericDevice(this);
        }



        /// <summary>
        /// Provides a string representation of this device.
        /// </summary>
        /// <returns>A string representation of this device.</returns>
        public override string ToString() =>
            $"{DeviceRole} {Name} (ID: {Id})";
    }
}
