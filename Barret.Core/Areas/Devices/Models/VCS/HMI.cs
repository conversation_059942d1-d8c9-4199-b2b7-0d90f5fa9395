using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.VCS
{
    /// <summary>
    /// Represents an HMI (Human-Machine Interface) device.
    /// </summary>
    public class HMI : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.HMI;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected HMI() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="HMI"/> class.
        /// </summary>
        /// <param name="name">The name of the HMI device.</param>
        public HMI(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new HMI device as a copy of the source HMI device.
        /// </summary>
        /// <param name="source">The source HMI device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public HMI(HMI source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this HMI device.
        /// </summary>
        /// <returns>A new HMI device that is a copy of this HMI device.</returns>
        public override GenericDevice Clone()
        {
            return new HMI(this);
        }
    }
}
