using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.VCS
{
    /// <summary>
    /// Represents a GPU (Graphics Processing Unit) device.
    /// </summary>
    public class GPU : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.GPU;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected GPU() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="GPU"/> class.
        /// </summary>
        /// <param name="name">The name of the GPU device.</param>
        public GPU(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new GPU device as a copy of the source GPU device.
        /// </summary>
        /// <param name="source">The source GPU device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public GPU(GPU source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }
    }
}
