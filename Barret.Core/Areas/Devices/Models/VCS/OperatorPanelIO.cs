using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.VCS
{
    /// <summary>
    /// Represents an Operator Panel IO device.
    /// </summary>
    public class OperatorPanelIO : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.OperatorPanelIO;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected OperatorPanelIO() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="OperatorPanelIO"/> class.
        /// </summary>
        /// <param name="name">The name of the operator panel IO device.</param>
        public OperatorPanelIO(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new operator panel IO device as a copy of the source operator panel IO device.
        /// </summary>
        /// <param name="source">The source operator panel IO device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public OperatorPanelIO(OperatorPanelIO source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }
    }
}
