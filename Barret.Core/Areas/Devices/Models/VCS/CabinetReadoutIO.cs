using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.VCS
{
    /// <summary>
    /// Represents a Cabinet Readout IO device.
    /// </summary>
    public class CabinetReadoutIO : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.CabinetReadoutIO;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected CabinetReadoutIO() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="CabinetReadoutIO"/> class.
        /// </summary>
        /// <param name="name">The name of the cabinet readout IO device.</param>
        public CabinetReadoutIO(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new cabinet readout IO device as a copy of the source cabinet readout IO device.
        /// </summary>
        /// <param name="source">The source cabinet readout IO device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public CabinetReadoutIO(CabinetReadoutIO source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }
    }
}
