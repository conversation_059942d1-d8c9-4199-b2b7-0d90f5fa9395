using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.VCS
{
    /// <summary>
    /// Represents a Safety System Head device.
    /// </summary>
    public class SafetySystemHead : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.SafetySystemHead;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected SafetySystemHead() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="SafetySystemHead"/> class.
        /// </summary>
        /// <param name="name">The name of the safety system head device.</param>
        public SafetySystemHead(string name) : base(name)
        {
            // No need to set <PERSON>ceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new safety system head device as a copy of the source safety system head device.
        /// </summary>
        /// <param name="source">The source safety system head device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public SafetySystemHead(SafetySystemHead source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }
    }
}
