using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Network
{
    /// <summary>
    /// Represents a Gateway network device.
    /// </summary>
    public class Gateway : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Gateway;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Gateway() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="Gateway"/> class.
        /// </summary>
        /// <param name="name">The name of the gateway.</param>
        public Gateway(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new gateway as a copy of the source gateway.
        /// </summary>
        /// <param name="source">The source gateway to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Gateway(Gateway source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this gateway.
        /// </summary>
        /// <returns>A new gateway that is a copy of this gateway.</returns>
        public override GenericDevice Clone()
        {
            return new Gateway(this);
        }
    }
}
