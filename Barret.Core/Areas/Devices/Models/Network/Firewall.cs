using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Network
{
    /// <summary>
    /// Represents a Firewall network security device.
    /// </summary>
    public class Firewall : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Firewall;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Firewall() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="Firewall"/> class.
        /// </summary>
        /// <param name="name">The name of the firewall.</param>
        public Firewall(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new firewall as a copy of the source firewall.
        /// </summary>
        /// <param name="source">The source firewall to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Firewall(Firewall source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this firewall.
        /// </summary>
        /// <returns>A new firewall that is a copy of this firewall.</returns>
        public override GenericDevice Clone()
        {
            return new Firewall(this);
        }
    }
}
