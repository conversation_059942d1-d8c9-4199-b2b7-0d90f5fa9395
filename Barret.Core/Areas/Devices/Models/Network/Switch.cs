using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Network
{
    /// <summary>
    /// Represents a network Switch device.
    /// </summary>
    public class Switch : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Switch;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Switch() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="Switch"/> class.
        /// </summary>
        /// <param name="name">The name of the switch.</param>
        public Switch(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new switch as a copy of the source switch.
        /// </summary>
        /// <param name="source">The source switch to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Switch(Switch source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this switch.
        /// </summary>
        /// <returns>A new switch that is a copy of this switch.</returns>
        public override GenericDevice Clone()
        {
            return new Switch(this);
        }
    }
}
