using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Network
{
    /// <summary>
    /// Represents a PLC (Programmable Logic Controller) device.
    /// </summary>
    public class Plc : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Plc;

        /// <summary>
        /// Initializes a new instance of the Plc class with a specified name.
        /// </summary>
        /// <param name="name">The name of the PLC device.</param>
        public Plc(string name) : base(name)
        {
            // Initialize with default values
        }

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Plc() : base()
        {
            // Required for EF Core
        }

        /// <summary>
        /// Copy constructor that creates a new PLC device as a copy of the source PLC device.
        /// </summary>
        /// <param name="source">The source PLC device to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Plc(Plc source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }
    }
}
