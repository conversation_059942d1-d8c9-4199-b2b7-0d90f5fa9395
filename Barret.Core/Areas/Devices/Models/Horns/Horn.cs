using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Horns
{
    /// <summary>
    /// Represents a horn device for sound signaling.
    /// </summary>
    public class Horn : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Horn;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Horn() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="Horn"/> class.
        /// </summary>
        /// <param name="name">The name of the horn.</param>
        public Horn(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new horn as a copy of the source horn.
        /// </summary>
        /// <param name="source">The source horn to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Horn(Horn source) : base(source)
        {
            // All base properties are copied by the base class copy constructor
        }
    }
}
