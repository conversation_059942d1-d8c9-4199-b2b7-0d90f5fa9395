using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Trackpilots
{
    /// <summary>
    /// Represents a trackpilot device for route following.
    /// </summary>
    public class Trackpilot : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Trackpilot;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Trackpilot() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="Trackpilot"/> class.
        /// </summary>
        /// <param name="name">The name of the trackpilot.</param>
        public Trackpilot(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new trackpilot as a copy of the source trackpilot.
        /// </summary>
        /// <param name="source">The source trackpilot to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Trackpilot(Trackpilot source) : base(source)
        {
            // No need to set DeviceRole as it's a read-only property
            // All base properties are copied by the base class copy constructor
        }

        /// <summary>
        /// Creates a clone of this trackpilot.
        /// </summary>
        /// <returns>A new trackpilot that is a copy of this trackpilot.</returns>
        public override GenericDevice Clone()
        {
            return new Trackpilot(this);
        }
    }
}
