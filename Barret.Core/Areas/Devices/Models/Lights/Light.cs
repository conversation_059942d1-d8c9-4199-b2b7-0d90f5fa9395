using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Lights
{
    /// <summary>
    /// Represents a light device for illumination.
    /// </summary>
    public class Light : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Light;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Light() { }

        /// <summary>
        /// Initializes a new instance of the <see cref="Light"/> class.
        /// </summary>
        /// <param name="name">The name of the light.</param>
        public Light(string name) : base(name)
        {
            // No need to set DeviceRole as it's a read-only property
        }

        /// <summary>
        /// Copy constructor that creates a new light as a copy of the source light.
        /// </summary>
        /// <param name="source">The source light to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Light(Light source) : base(source)
        {
            // All base properties are copied by the base class copy constructor
        }
    }
}
