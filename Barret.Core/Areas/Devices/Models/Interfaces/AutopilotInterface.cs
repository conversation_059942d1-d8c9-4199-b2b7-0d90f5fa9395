using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Interfaces
{
    /// <summary>
    /// Represents an autopilot interface for control integration.
    /// </summary>
    public class AutopilotInterface : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.AutopilotInterface;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected AutopilotInterface() : base() { }

        /// <summary>
        /// Initializes a new instance of the AutopilotInterface class.
        /// </summary>
        /// <param name="name">The name of the autopilot interface.</param>
        public AutopilotInterface(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new autopilot interface as a copy of the source autopilot interface.
        /// </summary>
        /// <param name="source">The source autopilot interface to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public AutopilotInterface(AutopilotInterface source) : base(source) { }

        /// <summary>
        /// Creates a clone of this autopilot interface.
        /// </summary>
        /// <returns>A new autopilot interface that is a copy of this autopilot interface.</returns>
        public override GenericDevice Clone()
        {
            return new AutopilotInterface(this);
        }

        /// <summary>
        /// Provides a string representation of this autopilot interface.
        /// </summary>
        /// <returns>A string representation of this autopilot interface.</returns>
        public override string ToString() =>
            $"Autopilot Interface {Name} (ID: {Id})";
    }
}
