using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Interfaces
{
    /// <summary>
    /// Represents a Mimer communication interface device.
    /// </summary>
    public class Mimer : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Mimer;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Mimer() : base() { }

        /// <summary>
        /// Initializes a new instance of the Mimer class.
        /// </summary>
        /// <param name="name">The name of the Mimer device.</param>
        public Mimer(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new Mimer as a copy of the source Mimer.
        /// </summary>
        /// <param name="source">The source Mimer to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Mimer(Mimer source) : base(source) { }

        /// <summary>
        /// Creates a clone of this Mimer.
        /// </summary>
        /// <returns>A new Mimer that is a copy of this Mimer.</returns>
        public override GenericDevice Clone()
        {
            return new Mimer(this);
        }

        /// <summary>
        /// Provides a string representation of this Mimer.
        /// </summary>
        /// <returns>A string representation of this Mimer.</returns>
        public override string ToString() =>
            $"Mimer {Name} (ID: {Id})";
    }
}
