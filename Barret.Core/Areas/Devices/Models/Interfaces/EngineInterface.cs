using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Interfaces
{
    /// <summary>
    /// Represents an engine interface for engine communication and diagnostics.
    /// </summary>
    public class EngineInterface : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.EngineInterface;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected EngineInterface() : base() { }

        /// <summary>
        /// Initializes a new instance of the EngineInterface class.
        /// </summary>
        /// <param name="name">The name of the engine interface.</param>
        public EngineInterface(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new engine interface as a copy of the source engine interface.
        /// </summary>
        /// <param name="source">The source engine interface to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public EngineInterface(EngineInterface source) : base(source) { }

        /// <summary>
        /// Creates a clone of this engine interface.
        /// </summary>
        /// <returns>A new engine interface that is a copy of this engine interface.</returns>
        public override GenericDevice Clone()
        {
            return new EngineInterface(this);
        }

        /// <summary>
        /// Provides a string representation of this engine interface.
        /// </summary>
        /// <returns>A string representation of this engine interface.</returns>
        public override string ToString() =>
            $"Engine Interface {Name} (ID: {Id})";
    }
}
