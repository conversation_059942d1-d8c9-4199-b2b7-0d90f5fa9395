using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Interfaces
{
    /// <summary>
    /// Represents a framegrabber device for radar data processing.
    /// </summary>
    public class Framegrabber : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.Framegrabber;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Framegrabber() : base() { }

        /// <summary>
        /// Initializes a new instance of the Framegrabber class.
        /// </summary>
        /// <param name="name">The name of the framegrabber.</param>
        public Framegrabber(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new framegrabber as a copy of the source framegrabber.
        /// </summary>
        /// <param name="source">The source framegrabber to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public Framegrabber(Framegrabber source) : base(source) { }

        /// <summary>
        /// Creates a clone of this framegrabber.
        /// </summary>
        /// <returns>A new framegrabber that is a copy of this framegrabber.</returns>
        public override GenericDevice Clone()
        {
            return new Framegrabber(this);
        }

        /// <summary>
        /// Provides a string representation of this framegrabber.
        /// </summary>
        /// <returns>A string representation of this framegrabber.</returns>
        public override string ToString() =>
            $"Framegrabber {Name} (ID: {Id})";
    }
}
