using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Models.Interfaces
{
    /// <summary>
    /// Represents a thruster interface for control and diagnostics.
    /// </summary>
    public class ThrusterInterface : GenericDevice
    {
        /// <summary>
        /// Gets the role of this device.
        /// </summary>
        public override DeviceRole DeviceRole => DeviceRole.ThrusterInterface;

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected ThrusterInterface() : base() { }

        /// <summary>
        /// Initializes a new instance of the ThrusterInterface class.
        /// </summary>
        /// <param name="name">The name of the thruster interface.</param>
        public ThrusterInterface(string name) : base(name) { }

        /// <summary>
        /// Copy constructor that creates a new thruster interface as a copy of the source thruster interface.
        /// </summary>
        /// <param name="source">The source thruster interface to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        public ThrusterInterface(ThrusterInterface source) : base(source) { }

        /// <summary>
        /// Creates a clone of this thruster interface.
        /// </summary>
        /// <returns>A new thruster interface that is a copy of this thruster interface.</returns>
        public override GenericDevice Clone()
        {
            return new ThrusterInterface(this);
        }

        /// <summary>
        /// Provides a string representation of this thruster interface.
        /// </summary>
        /// <returns>A string representation of this thruster interface.</returns>
        public override string ToString() =>
            $"Thruster Interface {Name} (ID: {Id})";
    }
}
