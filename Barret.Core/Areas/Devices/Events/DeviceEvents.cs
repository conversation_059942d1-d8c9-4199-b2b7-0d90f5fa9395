using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.Devices.Events
{
    /// <summary>
    /// Base class for all device-related domain events.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceEvent"/> class.
    /// </remarks>
    /// <param name="deviceId">The ID of the device that the event relates to.</param>
    public abstract class DeviceEvent(Guid deviceId)
    {
        /// <summary>
        /// Gets the device ID that the event relates to.
        /// </summary>
        public Guid DeviceId { get; } = deviceId;

        /// <summary>
        /// Gets the timestamp when the event occurred.
        /// </summary>
        public DateTime Timestamp { get; } = DateTime.UtcNow;
    }

    /// <summary>
    /// Event raised when a device is created.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceCreatedEvent"/> class.
    /// </remarks>
    /// <param name="device">The device that was created.</param>
    public class DeviceCreatedEvent(GenericDevice device) : DeviceEvent(device.Id)
    {
        /// <summary>
        /// Gets the device that was created.
        /// </summary>
        public GenericDevice Device { get; } = device;
    }

    /// <summary>
    /// Event raised when a device is updated.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceUpdatedEvent"/> class.
    /// </remarks>
    /// <param name="device">The device that was updated.</param>
    public class DeviceUpdatedEvent(GenericDevice device) : DeviceEvent(device.Id)
    {
        /// <summary>
        /// Gets the device that was updated.
        /// </summary>
        public GenericDevice Device { get; } = device;
    }

    /// <summary>
    /// Event raised when a device is deleted.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceDeletedEvent"/> class.
    /// </remarks>
    /// <param name="deviceId">The ID of the device that was deleted.</param>
    public class DeviceDeletedEvent(Guid deviceId) : DeviceEvent(deviceId)
    {
    }

    /// <summary>
    /// Event raised when a connection is added between two devices.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceConnectionAddedEvent"/> class.
    /// </remarks>
    /// <param name="connectedDeviceId">The ID of the source device.</param>
    /// <param name="interfaceDeviceId">The ID of the target device that was connected.</param>
    public class DeviceConnectionAddedEvent(Guid connectedDeviceId, Guid interfaceDeviceId) : DeviceEvent(connectedDeviceId)
    {
        /// <summary>
        /// Gets the ID of the target device that was connected.
        /// </summary>
        public Guid InterfaceDeviceId { get; } = interfaceDeviceId;
    }

    /// <summary>
    /// Event raised when a connection is removed between two devices.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the <see cref="DeviceConnectionRemovedEvent"/> class.
    /// </remarks>
    /// <param name="connectedDeviceId">The ID of the source device.</param>
    /// <param name="interfaceDeviceId">The ID of the target device that was disconnected.</param>
    public class DeviceConnectionRemovedEvent(Guid connectedDeviceId, Guid interfaceDeviceId) : DeviceEvent(connectedDeviceId)
    {
        /// <summary>
        /// Gets the ID of the target device that was disconnected.
        /// </summary>
        public Guid InterfaceDeviceId { get; } = interfaceDeviceId;
    }
}