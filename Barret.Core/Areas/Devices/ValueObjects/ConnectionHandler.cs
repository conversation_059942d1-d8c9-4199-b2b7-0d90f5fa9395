using Barret.Core.Areas.Common.ValueObjects;
using Barret.Core.Areas.Devices.Enums;

namespace Barret.Core.Areas.Devices.ValueObjects
{
    /// <summary>
    /// Represents the immutable connection details for a device.
    /// This is a value object that follows the immutability pattern.
    /// </summary>
    public sealed class ConnectionHandler : ValueObject
    {
        /// <summary>
        /// Gets the IP address of the device.
        /// </summary>
        public string IPAddress { get; private set; }

        /// <summary>
        /// Gets the port number of the device.
        /// </summary>
        public int Port { get; private set; }

        /// <summary>
        /// Gets the communication protocol of the device.
        /// </summary>
        public Protocol Protocol { get; private set; }

        /// <summary>
        /// Parameterless constructor for EF Core and value converters.
        /// </summary>
        public ConnectionHandler()
        {
            // Initialize with default values for EF Core
            IPAddress = "*************";
            Port = 8080;
            Protocol = Protocol.TcpClient;
        }

        /// <summary>
        /// Creates a default connection handler with reasonable default values.
        /// </summary>
        /// <returns>A new connection handler with default values (IP: *************, Port: 8080, Protocol: TcpClient).</returns>
        public static ConnectionHandler CreateDefault()
        {
            return new ConnectionHandler("*************", 8080, Protocol.TcpClient);
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ConnectionHandler"/> class.
        /// </summary>
        /// <param name="ipAddress">The IP address of the device.</param>
        /// <param name="port">The port number of the device.</param>
        /// <param name="protocol">The communication protocol of the device.</param>
        /// <exception cref="ArgumentException">Thrown when the IP address is null, whitespace, or not in a valid IP format.</exception>
        /// <exception cref="ArgumentOutOfRangeException">Thrown when the port number is invalid.</exception>
        /// <exception cref="ArgumentException">Thrown when the protocol is invalid.</exception>
        public ConnectionHandler(string ipAddress, int port, Protocol protocol)
        {
            if (string.IsNullOrWhiteSpace(ipAddress))
                throw new ArgumentException("IP Address cannot be null or whitespace.", nameof(ipAddress));

            if (!System.Net.IPAddress.TryParse(ipAddress, out _))
                throw new ArgumentException("Invalid IP address format.", nameof(ipAddress));
            if (port < 0 || port > 65535)
                throw new ArgumentOutOfRangeException(nameof(port), "Port must be between 0 and 65535.");
            if (!Enum.IsDefined(typeof(Protocol), protocol))
                throw new ArgumentException("Invalid protocol specified.", nameof(protocol));

            IPAddress = ipAddress;
            Port = port;
            Protocol = protocol;
        }

        /// <summary>
        /// Creates a new connection handler with a different IP address.
        /// </summary>
        /// <param name="ipAddress">The new IP address.</param>
        /// <returns>A new connection handler with the updated IP address.</returns>
        public ConnectionHandler WithIPAddress(string ipAddress)
        {
            return new ConnectionHandler(ipAddress, Port, Protocol);
        }

        /// <summary>
        /// Creates a new connection handler with a different port.
        /// </summary>
        /// <param name="port">The new port.</param>
        /// <returns>A new connection handler with the updated port.</returns>
        public ConnectionHandler WithPort(int port)
        {
            return new ConnectionHandler(IPAddress, port, Protocol);
        }

        /// <summary>
        /// Creates a new connection handler with a different protocol.
        /// </summary>
        /// <param name="protocol">The new protocol.</param>
        /// <returns>A new connection handler with the updated protocol.</returns>
        public ConnectionHandler WithProtocol(Protocol protocol)
        {
            return new ConnectionHandler(IPAddress, Port, protocol);
        }

        /// <summary>
        /// Gets the equality components for the value object.
        /// </summary>
        /// <returns>An enumerable collection of objects representing the equality components.</returns>
        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return IPAddress;
            yield return Port;
            yield return Protocol;
        }

        /// <summary>
        /// Returns a string representation of this connection handler.
        /// </summary>
        public override string ToString() => $"{Protocol}://{IPAddress}:{Port}";
    }
}