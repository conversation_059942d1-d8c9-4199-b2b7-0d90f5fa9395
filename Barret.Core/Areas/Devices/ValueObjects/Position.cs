using Barret.Core.Areas.Common.ValueObjects;
using Barret.Core.Areas.Devices.Enums;

namespace Barret.Core.Areas.Devices.ValueObjects
{
    /// <summary>
    /// Represents the physical position of a device on a maritime vessel
    /// </summary>
    public class Position : ValueObject
    {
        public ForeAftPosition ForeAft { get; private set; }
        public LateralPosition Lateral { get; private set; }
        public FacingDirection? Facing { get; private set; }

        /// <summary>
        /// Creates a new position with fore-aft and lateral positioning
        /// </summary>
        public Position(ForeAftPosition foreAft = ForeAftPosition.None, LateralPosition lateral = LateralPosition.None, FacingDirection? facing = null)
        {
            ForeAft = foreAft;
            Lateral = lateral;
            Facing = facing;
        }

        /// <summary>
        /// Parameterless constructor for Entity Framework
        /// </summary>
        protected Position()
        {
            ForeAft = ForeAftPosition.None;
            Lateral = LateralPosition.None;
            Facing = null;
        }

        /// <summary>
        /// Creates a bow position with optional lateral and facing
        /// </summary>
        public static Position Bow(LateralPosition lateral = LateralPosition.None, FacingDirection? facing = null)
            => new Position(ForeAftPosition.Bow, lateral, facing);

        /// <summary>
        /// Creates a stern position with optional lateral and facing
        /// </summary>
        public static Position Stern(LateralPosition lateral = LateralPosition.None, FacingDirection? facing = null)
            => new Position(ForeAftPosition.Stern, lateral, facing);

        /// <summary>
        /// Creates an undefined/unset position
        /// </summary>
        public static Position Undefined()
            => new Position(ForeAftPosition.None, LateralPosition.None, null);

        /// <summary>
        /// Gets a value indicating whether this position is defined (has any non-None values)
        /// </summary>
        public bool IsDefined => ForeAft != ForeAftPosition.None || Lateral != LateralPosition.None || Facing.HasValue;

        /// <summary>
        /// Returns a user-friendly display string for UI
        /// </summary>
        public string ToDisplayString()
        {
            if (!IsDefined)
            {
                return "Not specified";
            }

            var parts = new List<string>();

            if (ForeAft != ForeAftPosition.None)
            {
                parts.Add(ForeAft.ToString());
            }

            if (Lateral != LateralPosition.None)
            {
                parts.Add(Lateral.ToString());
            }

            if (Facing.HasValue && Facing != FacingDirection.None)
            {
                parts.Add($"(facing {Facing})");
            }

            return parts.Count > 0 ? string.Join(" ", parts) : "Not specified";
        }

        /// <summary>
        /// Returns a configuration string for DaVinci driver configs
        /// </summary>
        public string? ToConfigurationString()
        {
            // Return null if position is not defined - let the caller decide what to do
            if (!IsDefined || ForeAft == ForeAftPosition.None)
            {
                return null;
            }

            // DaVinci expects format like "Bow", "SternPort", "BowStarboard"
            return Lateral != LateralPosition.None && Lateral != LateralPosition.Centerline
                ? $"{ForeAft}{Lateral}"
                : ForeAft.ToString();
        }

        /// <summary>
        /// Returns a technical identifier for database/API usage
        /// </summary>
        public string ToTechnicalString()
        {
            if (!IsDefined)
            {
                return "undefined";
            }

            var parts = new List<string>();

            if (ForeAft != ForeAftPosition.None)
            {
                parts.Add(ForeAft.ToString().ToLowerInvariant());
            }

            if (Lateral != LateralPosition.None)
            {
                parts.Add(Lateral.ToString().ToLowerInvariant());
            }

            return parts.Count > 0 ? string.Join("_", parts) : "undefined";
        }

        protected override IEnumerable<object> GetEqualityComponents()
        {
            yield return ForeAft;
            yield return Lateral;
            yield return Facing ?? FacingDirection.None;
        }

        /// <summary>
        /// Returns a string representation of this position
        /// </summary>
        public override string ToString() => ToDisplayString();
    }
}
