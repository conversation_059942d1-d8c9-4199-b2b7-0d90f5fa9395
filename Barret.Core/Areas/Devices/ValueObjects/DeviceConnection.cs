using Barret.Core.Areas.Devices.Enums;
using System;

namespace Barret.Core.Areas.Devices.ValueObjects
{
    /// <summary>
    /// Represents an immutable connection between two devices.
    /// This is a value object implemented as a C# record type.
    /// </summary>
    public sealed record DeviceConnection
    {
        /// <summary>
        /// Gets the ID of the connected device in this connection.
        /// This is the primary device that is being connected to an interface.
        /// For example, an engine that is connected to a PLC.
        /// </summary>
        public Guid ConnectedDeviceId { get; init; }

        /// <summary>
        /// Gets the ID of the interface device in this connection.
        /// This is the device that provides the interface for the connected device.
        /// For example, a PLC that interfaces with an engine.
        /// </summary>
        public Guid InterfaceDeviceId { get; init; }

        /// <summary>
        /// Gets the type of this connection.
        /// </summary>
        public ConnectionType Type { get; init; }

        /// <summary>
        /// Gets the direction of data flow in this connection.
        /// </summary>
        public ConnectionDirection Direction { get; init; }

        /// <summary>
        /// Parameterless constructor for EF Core.
        /// </summary>
        private DeviceConnection()
        {
            // Empty constructor for EF Core
            ConnectedDeviceId = Guid.Empty;
            InterfaceDeviceId = Guid.Empty;
            Type = ConnectionType.Undefined;
            Direction = ConnectionDirection.Undefined;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceConnection"/> record.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the connected device (e.g., engine).</param>
        /// <param name="interfaceDeviceId">The ID of the interface device (e.g., PLC).</param>
        /// <param name="type">The type of connection.</param>
        /// <param name="direction">The direction of data flow.</param>
        /// <exception cref="ArgumentException">Thrown when connected and interface device IDs are the same.</exception>
        public DeviceConnection(
            Guid connectedDeviceId,
            Guid interfaceDeviceId,
            ConnectionType type = ConnectionType.Standard,
            ConnectionDirection direction = ConnectionDirection.Duplex)
        {
            if (connectedDeviceId == Guid.Empty)
                throw new ArgumentException("Connected device ID cannot be empty", nameof(connectedDeviceId));

            if (interfaceDeviceId == Guid.Empty)
                throw new ArgumentException("Interface device ID cannot be empty", nameof(interfaceDeviceId));

            if (connectedDeviceId == interfaceDeviceId)
                throw new ArgumentException("Connected and interface device IDs cannot be the same", nameof(interfaceDeviceId));

            ConnectedDeviceId = connectedDeviceId;
            InterfaceDeviceId = interfaceDeviceId;
            Type = type;
            Direction = direction;
        }

        /// <summary>
        /// Creates a new connection with a different type.
        /// </summary>
        /// <param name="type">The new connection type.</param>
        /// <returns>A new connection with the updated type.</returns>
        public DeviceConnection WithType(ConnectionType type)
        {
            return this with { Type = type };
        }

        /// <summary>
        /// Creates a new connection with a different direction.
        /// </summary>
        /// <param name="direction">The new connection direction.</param>
        /// <returns>A new connection with the updated direction.</returns>
        public DeviceConnection WithDirection(ConnectionDirection direction)
        {
            return this with { Direction = direction };
        }

        /// <summary>
        /// Returns a string representation of this connection.
        /// </summary>
        public override string ToString() => $"{ConnectedDeviceId} -> {InterfaceDeviceId} ({Type}, {Direction})";
    }
}
