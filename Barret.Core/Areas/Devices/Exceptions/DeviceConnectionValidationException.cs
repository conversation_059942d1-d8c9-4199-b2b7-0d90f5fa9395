using System;

namespace Barret.Core.Areas.Devices.Exceptions
{
    /// <summary>
    /// Exception thrown when a device connection validation fails.
    /// </summary>
    public class DeviceConnectionValidationException : Exception
    {
        /// <summary>
        /// Gets the ID of the source device in the connection.
        /// </summary>
        public Guid ConnectedDeviceId { get; }

        /// <summary>
        /// Gets the ID of the target device in the connection.
        /// </summary>
        public Guid InterfaceDeviceId { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceConnectionValidationException"/> class.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the source device.</param>
        /// <param name="interfaceDeviceId">The ID of the target device.</param>
        /// <param name="message">The error message.</param>
        public DeviceConnectionValidationException(Guid connectedDeviceId, Guid interfaceDeviceId, string message)
            : base(message)
        {
            ConnectedDeviceId = connectedDeviceId;
            InterfaceDeviceId = interfaceDeviceId;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceConnectionValidationException"/> class.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the source device.</param>
        /// <param name="interfaceDeviceId">The ID of the target device.</param>
        /// <param name="message">The error message.</param>
        /// <param name="innerException">The inner exception.</param>
        public DeviceConnectionValidationException(Guid connectedDeviceId, Guid interfaceDeviceId, string message, Exception innerException)
            : base(message, innerException)
        {
            ConnectedDeviceId = connectedDeviceId;
            InterfaceDeviceId = interfaceDeviceId;
        }
    }
}
