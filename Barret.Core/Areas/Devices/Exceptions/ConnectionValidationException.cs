using System;

namespace Barret.Core.Areas.Devices.Exceptions
{
    /// <summary>
    /// Exception thrown when a device connection validation fails.
    /// </summary>
    public class ConnectionValidationException : DeviceValidationException
    {
        /// <summary>
        /// Gets the ID of the source device in the connection.
        /// </summary>
        public Guid ConnectedDeviceId { get; }

        /// <summary>
        /// Gets the ID of the target device in the connection.
        /// </summary>
        public Guid InterfaceDeviceId { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="ConnectionValidationException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        public ConnectionValidationException(string message) : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ConnectionValidationException"/> class.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the source device in the connection.</param>
        /// <param name="interfaceDeviceId">The ID of the target device in the connection.</param>
        /// <param name="message">The error message.</param>
        public ConnectionValidationException(Guid connectedDeviceId, Guid interfaceDeviceId, string message) 
            : base(connectedDeviceId, message)
        {
            ConnectedDeviceId = connectedDeviceId;
            InterfaceDeviceId = interfaceDeviceId;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ConnectionValidationException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="innerException">The inner exception.</param>
        public ConnectionValidationException(string message, Exception innerException) 
            : base(message, innerException)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="ConnectionValidationException"/> class.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the source device in the connection.</param>
        /// <param name="interfaceDeviceId">The ID of the target device in the connection.</param>
        /// <param name="message">The error message.</param>
        /// <param name="innerException">The inner exception.</param>
        public ConnectionValidationException(Guid connectedDeviceId, Guid interfaceDeviceId, string message, Exception innerException) 
            : base(connectedDeviceId, message, innerException)
        {
            ConnectedDeviceId = connectedDeviceId;
            InterfaceDeviceId = interfaceDeviceId;
        }
    }
}
