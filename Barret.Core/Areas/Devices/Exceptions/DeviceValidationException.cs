using System;

namespace Barret.Core.Areas.Devices.Exceptions
{
    /// <summary>
    /// Exception thrown when a device validation fails.
    /// </summary>
    public class DeviceValidationException : Exception
    {
        /// <summary>
        /// Gets the ID of the device that failed validation.
        /// </summary>
        public Guid DeviceId { get; }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceValidationException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        public DeviceValidationException(string message) : base(message)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceValidationException"/> class.
        /// </summary>
        /// <param name="deviceId">The ID of the device that failed validation.</param>
        /// <param name="message">The error message.</param>
        public DeviceValidationException(Guid deviceId, string message) : base(message)
        {
            DeviceId = deviceId;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceValidationException"/> class.
        /// </summary>
        /// <param name="message">The error message.</param>
        /// <param name="innerException">The inner exception.</param>
        public DeviceValidationException(string message, Exception innerException) : base(message, innerException)
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DeviceValidationException"/> class.
        /// </summary>
        /// <param name="deviceId">The ID of the device that failed validation.</param>
        /// <param name="message">The error message.</param>
        /// <param name="innerException">The inner exception.</param>
        public DeviceValidationException(Guid deviceId, string message, Exception innerException) : base(message, innerException)
        {
            DeviceId = deviceId;
        }
    }
}
