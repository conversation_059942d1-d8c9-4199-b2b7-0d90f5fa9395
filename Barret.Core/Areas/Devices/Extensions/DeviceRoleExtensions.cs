using Barret.Core.Areas.Devices.Attributes;
using Barret.Core.Areas.Devices.Enums;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;

namespace Barret.Core.Areas.Devices.Extensions
{
    /// <summary>
    /// Extension methods for DeviceRole compatibility checks.
    /// </summary>
    public static class DeviceRoleExtensions
    {
        // Cached lookup for fast IsCompatibleWith checks
        private static readonly IReadOnlyDictionary<DeviceRole, HashSet<DeviceRole>> _compatibilityMap;

        static DeviceRoleExtensions()
        {
            // Build the compatibility map once at startup
            _compatibilityMap = Enum.GetValues<DeviceRole>()
                .ToDictionary(
                    role => role,
                    role =>
                    {
                        // Retrieve the CanConnectToAttribute on each enum member
                        var memberInfo = typeof(DeviceRole)
                            .GetMember(role.ToString())
                            .FirstOrDefault();
                        var attr = memberInfo?
                            .GetCustomAttribute<CanConnectToAttribute>(inherit: false);
                        return new HashSet<DeviceRole>(attr?.Roles ?? Array.Empty<DeviceRole>());
                    }
                );
        }

        /// <summary>
        /// Determines whether <paramref name="source"/> can interoperate with <paramref name="other"/>.
        /// </summary>
        /// <param name="source">The primary device role.</param>
        /// <param name="other">The candidate device role to check.</param>
        /// <returns>True if compatible; otherwise false.</returns>
        public static bool IsCompatibleWith(this DeviceRole source, DeviceRole other)
        {
            return _compatibilityMap.TryGetValue(source, out var set)
                   && set.Contains(other);
        }

        /// <summary>
        /// Gets all device roles that are compatible with the specified role.
        /// </summary>
        /// <param name="source">The device role to check compatibility for.</param>
        /// <returns>A collection of compatible device roles.</returns>
        public static IReadOnlyCollection<DeviceRole> GetCompatibleRoles(this DeviceRole source)
        {
            return _compatibilityMap.TryGetValue(source, out var set)
                ? set
                : Array.Empty<DeviceRole>();
        }
    }
}
