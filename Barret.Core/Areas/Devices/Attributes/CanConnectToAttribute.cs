using Barret.Core.Areas.Devices.Enums;
using System;
using System.Collections.Generic;

namespace Barret.Core.Areas.Devices.Attributes
{
    /// <summary>
    /// Specifies which DeviceRole values a given role can connect to.
    /// </summary>
    /// <remarks>
    /// Initializes a new instance of the CanConnectToAttribute class.
    /// </remarks>
    /// <param name="roles">The roles this role can connect to.</param>
    [AttributeUsage(AttributeTargets.Field, AllowMultiple = false)]
    public sealed class CanConnectToAttribute(params DeviceRole[] roles) : Attribute
    {
        /// <summary>
        /// The roles this role can connect to.
        /// </summary>
        public IReadOnlyCollection<DeviceRole> Roles { get; } = roles ?? Array.Empty<DeviceRole>();
    }
}
