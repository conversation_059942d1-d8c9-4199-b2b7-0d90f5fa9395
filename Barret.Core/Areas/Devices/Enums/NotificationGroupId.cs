namespace Barret.Core.Areas.Devices.Enums
{
    /// <summary>
    /// Represents the notification group identifier for alarms.
    /// </summary>
    public enum NotificationGroupId
    {
        /// <summary>
        /// Notification group is not defined.
        /// </summary>
        Undefined = -1,
        
        /// <summary>
        /// Control system general notifications.
        /// </summary>
        ControlSystemGeneral = 0,
        
        /// <summary>
        /// Control system PLC notifications.
        /// </summary>
        ControlSystemPLC = 1,
        
        /// <summary>
        /// Power general notifications.
        /// </summary>
        PowerGeneral = 2,
        
        /// <summary>
        /// Safety systems notifications.
        /// </summary>
        SafetySystems = 3,
        
        /// <summary>
        /// Navigation equipment notifications.
        /// </summary>
        NavigationEquipment = 4,
        
        /// <summary>
        /// Sensor processor notifications.
        /// </summary>
        SensorProcessor = 5,
        
        /// <summary>
        /// Autonomy processor notifications.
        /// </summary>
        AutonomyProcessor = 6,
        
        /// <summary>
        /// Communication notifications.
        /// </summary>
        Communication = 7,
        
        /// <summary>
        /// Propulsion notifications.
        /// </summary>
        Propulsion = 8,
        
        /// <summary>
        /// Power systems notifications.
        /// </summary>
        PowerSystems = 9,
        
        /// <summary>
        /// Measurement equipment notifications.
        /// </summary>
        MeasurementEquipment = 10,
        
        /// <summary>
        /// Shore control station notifications.
        /// </summary>
        ShoreControlStation = 11
    }
}
