namespace Barret.Core.Areas.Devices.Enums
{
    /// <summary>
    /// Represents the warning identifier for alarms (corresponds to DomainDriverId in Excel configuration).
    /// </summary>
    public enum WarningId
    {
        /// <summary>
        /// Warning ID is not defined.
        /// </summary>
        Undefined = -1,

        // Bridge and Control Indicators
        /// <summary>
        /// Bridge warning indicator.
        /// </summary>
        BridgeWarningIndicator = 0,

        /// <summary>
        /// Bridge alarm indicator.
        /// </summary>
        BridgeAlarmIndicator = 1,

        /// <summary>
        /// General alarm.
        /// </summary>
        AlarmGeneral = 2,

        /// <summary>
        /// Fire alarm.
        /// </summary>
        AlarmFire = 3,

        /// <summary>
        /// Acknowledge alarms.
        /// </summary>
        AcknowledgeAlarms = 4,

        /// <summary>
        /// Acknowledge warnings.
        /// </summary>
        AcknowledgeWarnings = 5,

        /// <summary>
        /// Reset function.
        /// </summary>
        Reset = 6,

        // Lighting Systems
        /// <summary>
        /// Cargo lights.
        /// </summary>
        CargoLights = 7,

        /// <summary>
        /// Deck light 1.
        /// </summary>
        DeckLight1 = 8,

        /// <summary>
        /// Deck light 2.
        /// </summary>
        DeckLight2 = 9,

        /// <summary>
        /// Deck light 3.
        /// </summary>
        DeckLight3 = 10,

        /// <summary>
        /// Deck light 4.
        /// </summary>
        DeckLight4 = 11,

        /// <summary>
        /// Strobe lights.
        /// </summary>
        StrobeLights = 12,

        /// <summary>
        /// Siren.
        /// </summary>
        Siren = 13,

        /// <summary>
        /// Up mast pole 1.
        /// </summary>
        UpMastPole1 = 14,

        /// <summary>
        /// Up mast pole 2.
        /// </summary>
        UpMastPole2 = 15,

        /// <summary>
        /// Drop anchor stern 1.
        /// </summary>
        DropAnchorStern1 = 16,

        /// <summary>
        /// Drop anchor bow 1.
        /// </summary>
        DropAnchorBow1 = 17,

        /// <summary>
        /// WiFi access point.
        /// </summary>
        WiFiAccessPoint = 18,

        /// <summary>
        /// Search light 1.
        /// </summary>
        SearchLight1 = 19,

        /// <summary>
        /// Up mast pole 3.
        /// </summary>
        UpMastPole3 = 20,

        /// <summary>
        /// Up mast pole 4.
        /// </summary>
        UpMastPole4 = 21,

        /// <summary>
        /// Down spud pole stern 1.
        /// </summary>
        DownSpudPoleStern1 = 22,

        /// <summary>
        /// Down spud pole bow 1.
        /// </summary>
        DownSpudPoleBow1 = 23,

        // Communication Systems
        /// <summary>
        /// Call system room 1.
        /// </summary>
        CallSystemRoom1 = 24,

        /// <summary>
        /// Call system room 2.
        /// </summary>
        CallSystemRoom2 = 25,

        /// <summary>
        /// Call system room 3.
        /// </summary>
        CallSystemRoom3 = 26,

        /// <summary>
        /// Call system room 4.
        /// </summary>
        CallSystemRoom4 = 27,

        // Navigation Lights
        /// <summary>
        /// Light masthead.
        /// </summary>
        LightMasthead = 28,

        /// <summary>
        /// Light sides.
        /// </summary>
        LightSides = 29,

        /// <summary>
        /// Light stern.
        /// </summary>
        LightStern = 30,

        /// <summary>
        /// Light towing.
        /// </summary>
        LightTowing = 31,

        /// <summary>
        /// Light anchor.
        /// </summary>
        LightAnchor = 32,

        /// <summary>
        /// Light special.
        /// </summary>
        LightSpecial = 33,

        /// <summary>
        /// Light blue sign.
        /// </summary>
        LightBlueSign = 34,

        /// <summary>
        /// Light horn.
        /// </summary>
        LightHorn = 35,

        /// <summary>
        /// Light sail.
        /// </summary>
        LightSail = 36,

        /// <summary>
        /// Light ADNR sign 1.
        /// </summary>
        LightAdnrSign1 = 37,

        /// <summary>
        /// Light ADNR sign 2.
        /// </summary>
        LightAdnrSign2 = 38,

        // Audio Systems
        /// <summary>
        /// Horn.
        /// </summary>
        Horn = 39,

        // Propulsion Systems
        /// <summary>
        /// Bow 360 thruster 1.
        /// </summary>
        Bow360Thruster1 = 40,

        /// <summary>
        /// Bow 360 thruster 2.
        /// </summary>
        Bow360Thruster2 = 41,

        /// <summary>
        /// Stern thruster 1.
        /// </summary>
        SternThruster1 = 42,

        /// <summary>
        /// Stern thruster 2.
        /// </summary>
        SternThruster2 = 43,

        /// <summary>
        /// Stern engine 1.
        /// </summary>
        SternEngine1 = 44,

        /// <summary>
        /// Stern engine 2.
        /// </summary>
        SternEngine2 = 45,

        /// <summary>
        /// Bow pipe thruster 1.
        /// </summary>
        BowPipeThruster1 = 46,

        /// <summary>
        /// Unspecified autopilot 1.
        /// </summary>
        UnspecifiedAutoPilot1 = 47,

        /// <summary>
        /// Stern rudder 1.
        /// </summary>
        SternRudder1 = 48,

        // Generic Thruster Controls
        /// <summary>
        /// Unspecified thruster generic 1 X-axis.
        /// </summary>
        UnspecifiedThrusterGeneric1_X = 49,

        /// <summary>
        /// Unspecified thruster generic 1 Y-axis.
        /// </summary>
        UnspecifiedThrusterGeneric1_Y = 50,

        /// <summary>
        /// Unspecified thruster generic 1 Z-axis.
        /// </summary>
        UnspecifiedThrusterGeneric1_Z = 51,

        /// <summary>
        /// Unspecified thruster generic 2 X-axis.
        /// </summary>
        UnspecifiedThrusterGeneric2_X = 52,

        /// <summary>
        /// Unspecified thruster generic 2 Y-axis.
        /// </summary>
        UnspecifiedThrusterGeneric2_Y = 53,

        /// <summary>
        /// Unspecified thruster generic 2 Z-axis.
        /// </summary>
        UnspecifiedThrusterGeneric2_Z = 54,

        // Michelangelo System Warnings
        /// <summary>
        /// Battery low voltage warning.
        /// </summary>
        MiBatteryLowVoltage = 55,

        /// <summary>
        /// Battery high voltage warning.
        /// </summary>
        MiBatteryHighVoltage = 56,

        /// <summary>
        /// Temperature exceeded thresholds warning.
        /// </summary>
        MiTemperatureExceededThresholds = 57,

        /// <summary>
        /// Propulsion not ready warning.
        /// </summary>
        MiPropulsionNotReady = 58,

        /// <summary>
        /// Oil pressure out of bounds warning.
        /// </summary>
        MiOilPressureOutOfBounds = 59,

        /// <summary>
        /// Coolant liquid out of bounds warning.
        /// </summary>
        MiCoolantLiquidOutOfBounds = 60,

        /// <summary>
        /// Propulsion overspeed warning.
        /// </summary>
        MiPropulsionOverspeed = 61,

        /// <summary>
        /// Fuel out of bounds warning.
        /// </summary>
        MiFuelOutOfBounds = 62,

        /// <summary>
        /// Generator general warning.
        /// </summary>
        MiGeneratorGeneral = 63,

        /// <summary>
        /// Bilge general warning.
        /// </summary>
        MiBilgeGeneral = 64,

        /// <summary>
        /// Spud pole general warning.
        /// </summary>
        MiSpudpoleGeneral = 65,

        /// <summary>
        /// Power general warning.
        /// </summary>
        MiPowerGeneral = 66,

        /// <summary>
        /// Fire general warning.
        /// </summary>
        MiFireGeneral = 67,

        /// <summary>
        /// Communication general warning.
        /// </summary>
        MiCommunicationGeneral = 68,

        /// <summary>
        /// Navigation equipment defect warning.
        /// </summary>
        MiNavigationEquipmentDefect = 69,

        /// <summary>
        /// External emergency stop warning.
        /// </summary>
        MiExternalEmergencyStop = 70,

        /// <summary>
        /// External PLC general warning.
        /// </summary>
        MiExternalPlcGeneral = 71,

        /// <summary>
        /// Hydraulic failure warning.
        /// </summary>
        MiHydraulicFailure = 72,

        /// <summary>
        /// Tank out of bounds warning.
        /// </summary>
        MiTankOutOfBounds = 73,

        /// <summary>
        /// Hatch open warning.
        /// </summary>
        MiHatchOpen = 74,

        /// <summary>
        /// Function state mismatch warning.
        /// </summary>
        MiFunctionStateMismatch = 75,

        /// <summary>
        /// Pressure out of bounds warning.
        /// </summary>
        MiPressureOutOfBounds = 76,

        /// <summary>
        /// Pump general warning.
        /// </summary>
        MiPumpGeneral = 77,

        /// <summary>
        /// Gas detection warning.
        /// </summary>
        MiGasDetection = 78,

        /// <summary>
        /// Water in fuel warning.
        /// </summary>
        MiWaterInFuel = 79,

        /// <summary>
        /// Red group active warning.
        /// </summary>
        MiRedGroupActive = 80,

        /// <summary>
        /// No flow cooling water warning.
        /// </summary>
        MiNoFlowCoolingWater = 81,

        /// <summary>
        /// External system general warning.
        /// </summary>
        MiExternalSystemGeneral = 82,

        // Daremi System Warnings
        /// <summary>
        /// Daremi wind speed too high warning.
        /// </summary>
        DaremiWindSpeedTooHigh = 83,

        /// <summary>
        /// Daremi weight too high warning.
        /// </summary>
        DaremiWeightTooHigh = 84,

        /// <summary>
        /// Daremi tilt too high warning.
        /// </summary>
        DaremiTiltTooHigh = 85
    }
}
