using System;

namespace Barret.Core.Areas.Devices.Enums
{
    /// <summary>
    /// Represents the type of connection between devices.
    /// </summary>
    public enum ConnectionType
    {
        Undefined = -1,
        /// <summary>
        /// Standard connection with no specific type.
        /// </summary>
        Standard = 0,
        
        /// <summary>
        /// Network connection (Ethernet, WiFi, etc.).
        /// </summary>
        Network = 1,
        
        /// <summary>
        /// Serial connection (RS232, RS485, etc.).
        /// </summary>
        Serial = 2,
        
        /// <summary>
        /// Power connection.
        /// </summary>
        Power = 3,
        
        /// <summary>
        /// Control signal connection.
        /// </summary>
        Control = 4,
        
        /// <summary>
        /// Data connection for information exchange.
        /// </summary>
        Data = 5,
    }
}
