namespace Barret.Core.Areas.Devices.Enums
{
    /// <summary>
    /// Represents the direction a device is facing or oriented
    /// </summary>
    public enum FacingDirection
    {
        /// <summary>
        /// No specific facing direction
        /// </summary>
        None,

        /// <summary>
        /// Facing toward the bow (forward)
        /// </summary>
        Forward,

        /// <summary>
        /// Facing toward the stern (aft)
        /// </summary>
        Aft,

        /// <summary>
        /// Facing toward the port side
        /// </summary>
        Port,

        /// <summary>
        /// Facing toward the starboard side
        /// </summary>
        Starboard,

        /// <summary>
        /// Facing upward (e.g., antennas, upward-facing sensors)
        /// </summary>
        Up,

        /// <summary>
        /// Facing downward (e.g., downward-facing sensors)
        /// </summary>
        Down,

        /// <summary>
        /// Rotating device (e.g., rotating radars)
        /// </summary>
        Rotating
    }
}
