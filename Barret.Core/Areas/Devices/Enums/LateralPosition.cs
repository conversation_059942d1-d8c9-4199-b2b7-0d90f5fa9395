namespace Barret.Core.Areas.Devices.Enums
{
    /// <summary>
    /// Represents the lateral (side-to-side) position or mounting type on a maritime vessel
    /// </summary>
    public enum LateralPosition
    {
        /// <summary>
        /// No lateral position specified
        /// </summary>
        None,

        /// <summary>
        /// Left side of the vessel (when facing forward)
        /// </summary>
        Port,

        /// <summary>
        /// Right side of the vessel (when facing forward)
        /// </summary>
        Starboard,

        /// <summary>
        /// Mounted on the vessel's mast
        /// </summary>
        Mast,

        /// <summary>
        /// Mounted on the vessel's centerline
        /// </summary>
        Centerline
    }
}
