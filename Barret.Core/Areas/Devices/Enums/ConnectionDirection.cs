using System;

namespace Barret.Core.Areas.Devices.Enums
{
    /// <summary>
    /// Represents the direction of data flow in a device connection.
    /// </summary>
    public enum ConnectionDirection
    {
        Undefined = -1,

        /// <summary>
        /// Data flows from the connected device to the interface device (system).
        /// For example, reading data from an engine to a PLC.
        /// </summary>
        Inbound = 0,

        /// <summary>
        /// Data flows from the interface device (system) to the connected device.
        /// For example, sending commands from a PLC to an engine.
        /// </summary>
        Outbound = 1,

        /// <summary>
        /// Data flows in both directions between the devices.
        /// </summary>
        Duplex = 2,
    }
}
