namespace Barret.Core.Areas.Devices.Enums
{
    /// <summary>
    /// Enum representing the protocol used for communication with devices.
    /// </summary>
    public enum Protocol
    {
        /// <summary>
        /// Represents an undefined protocol
        /// </summary>
        Undefined = -1,
        /// <summary>
        /// Represents the TcpClient protocol
        /// </summary>
        TcpClient = 0,
        /// <summary>
        /// Represents the RTSP client protocol
        /// </summary>
        RtspClient = 1,
    }
}