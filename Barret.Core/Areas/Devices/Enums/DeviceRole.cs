using Barret.Core.Areas.Devices.Attributes;

namespace Barret.Core.Areas.Devices.Enums
{
    /// <summary>
    /// Represents the role or type of a device in the system.
    /// Decorated with compatibility metadata via CanConnectToAttribute.
    /// </summary>
    public enum DeviceRole
    {
        /// <summary>
        /// Device role is not defined.
        /// </summary>
        Undefined = -1,

        /// <summary>
        /// Generic device with no specific role.
        /// </summary>
        Generic = 0,

        /// <summary>
        /// Camera device.
        /// </summary>
        Camera = 1,

        /// <summary>
        /// Engine device for propulsion.
        /// </summary>
        [CanConnectTo(EngineInterface)]
        Engine = 2,

        /// <summary>
        /// Thruster device for maneuvering.
        /// </summary>
        [CanConnectTo(ThrusterInterface)]
        Thruster = 3,

        /// <summary>
        /// Radar device for detection and ranging.
        /// </summary>
        [CanConnectTo(Framegrabber)]
        Radar = 4,

        /// <summary>
        /// Antenna device for communication.
        /// </summary>
        [CanConnectTo(Gateway)]
        Antenna = 5,

        /// <summary>
        /// Autopilot device for automated navigation.
        /// </summary>
        [CanConnectTo(AutopilotInterface)]
        Autopilot = 6,

        /// <summary>
        /// Audio hub device for sound management.
        /// </summary>
        AudioHub = 7,

        /// <summary>
        /// Framegrabber for radar.
        /// </summary>
        Framegrabber = 8,

        /// <summary>
        /// PA Audio system for public announcements.
        /// </summary>
        PAAudio = 9,

        /// <summary>
        /// HMI (Human-Machine Interface) device.
        /// </summary>
        HMI = 10,

        /// <summary>
        /// Firewall network security device.
        /// </summary>
        Firewall = 11,

        /// <summary>
        /// VHF Mariphone for marine communication.
        /// </summary>
        [CanConnectTo(Mimer)]
        VHFMariphone = 12,

        /// <summary>
        /// VHF Network Interface for connecting VHF systems to networks.
        /// </summary>
        VHFNetworkInterface = 13,

        /// <summary>
        /// Cabinet Readout IO device.
        /// </summary>
        CabinetReadoutIO = 14,

        /// <summary>
        /// Operator Panel IO device.
        /// </summary>
        OperatorPanelIO = 15,

        /// <summary>
        /// Navigation Data (Serial-to-Ethernet) converter.
        /// </summary>
        NavData = 16,

        /// <summary>
        /// GPU (Graphics Processing Unit) device.
        /// </summary>
        GPU = 17,

        /// <summary>
        /// Gateway network device.
        /// </summary>
        Gateway = 18,

        /// <summary>
        /// Network switch device.
        /// </summary>
        Switch = 19,

        /// <summary>
        /// Safety System Head device.
        /// </summary>
        SafetySystemHead = 20,

        /// <summary>
        /// AMP (Automation Processor) device.
        /// </summary>
        AMP = 21,

        /// <summary>
        /// SP & AP (Sensor Processor & Autonomy Processor) device.
        /// </summary>
        SPAP = 22,

        /// <summary>
        /// Trackpilot device for route following.
        /// </summary>
        Trackpilot = 23,

        /// <summary>
        /// NVR Screen device for displaying video feeds.
        /// </summary>
        NVRScreen = 24,

        /// <summary>
        /// NVR Recording device for storing video feeds.
        /// </summary>
        NVRRecording = 25,

        /// <summary>
        /// Windows PC for general-purpose computing.
        /// </summary>
        WindowsPC = 26,

        /// <summary>
        /// PLC (Programmable Logic Controller) device.
        /// </summary>
        [CanConnectTo(Switch)]
        Plc = 27,

        /// <summary>
        /// Light device for illumination.
        /// </summary>
        Light = 28,

        /// <summary>
        /// Rudder device for steering.
        /// </summary>
        Rudder = 29,

        /// <summary>
        /// Horn device for signaling.
        /// </summary>
        Horn = 30,

        /// <summary>
        /// Sensor device for environmental monitoring.
        /// </summary>
        [CanConnectTo(NavData)]
        Sensor = 31,

        /// <summary>
        /// PA device for public announcement integration.
        /// </summary>
        PA = 32,

        /// <summary>
        /// Engine Interface for engine communication and diagnostics.
        /// </summary>
        EngineInterface = 33,

        /// <summary>
        /// Mast structure or device for sensor and antenna mounting.
        /// </summary>
        Mast = 34,

        /// <summary>
        /// Generator device for electrical power generation.
        /// </summary>
        Generator = 35,

        /// <summary>
        /// SP (Sensor Processor) device.
        /// </summary>
        SP = 36,

        /// <summary>
        /// Thruster Interface for control and diagnostics.
        /// </summary>
        ThrusterInterface = 37,

        /// <summary>
        /// Autopilot Interface for control integration.
        /// </summary>
        AutopilotInterface = 38,

        /// <summary>
        /// Mimer communication interface device.
        /// </summary>
        Mimer = 39
    }
}
