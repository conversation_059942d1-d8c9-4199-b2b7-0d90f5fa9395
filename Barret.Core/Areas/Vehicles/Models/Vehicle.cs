using Barret.Core.Areas.DeviceGroups;
using Barret.Core.Areas.DeviceGroups.CameraGroup;
using Barret.Core.Areas.DeviceGroups.Propulsion.EngineGroup;
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Extensions;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.ValueObjects;
using Barret.Core.Areas.Vehicles.ValueObjects;

namespace Barret.Core.Areas.Vehicles.Models
{
    /// <summary>
    /// Base class for all vehicles that can be configured in the system.
    /// </summary>
    public abstract class Vehicle
    {
        private string _vehicleName = "";
        private DimensionsFromGpsLocation? _dimensions;
        private readonly List<DeviceConnection> _deviceConnections = [];

        // Base vehicle device groups
        public CameraGroup CameraGroup { get; private set; } = new();
        public EngineGroup EngineGroup { get; private set; } = new();

        /// <summary>
        /// Gets a read-only collection of device connections in this vehicle.
        /// </summary>
        public IReadOnlyCollection<DeviceConnection> DeviceConnections => _deviceConnections.AsReadOnly();

        /// <summary>
        /// Gets the unique identifier of this vehicle.
        /// </summary>
        public Guid Id { get; private set; }

        /// <summary>
        /// Gets or sets the vehicle's identifier.
        /// </summary>
        public abstract string VehicleId { get; set; }

        /// <summary>
        /// Gets or sets the name of the vehicle.
        /// </summary>
        public string VehicleName
        {
            get => _vehicleName;
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("Vehicle name cannot be empty", nameof(value));

                _vehicleName = value;
            }
        }

        /// <summary>
        /// Gets or sets the dimensions of the vehicle relative to its GPS location.
        /// </summary>
        public DimensionsFromGpsLocation? Dimensions
        {
            get => _dimensions;
            private set => _dimensions = value;
        }

        /// <summary>
        /// Updates the dimensions of the vehicle and returns this vehicle instance for fluent API.
        /// </summary>
        /// <param name="dimensions">The new dimensions.</param>
        /// <returns>This vehicle instance.</returns>
        /// <exception cref="ArgumentNullException">Thrown when dimensions is null.</exception>
        public Vehicle WithDimensions(DimensionsFromGpsLocation dimensions)
        {
            _dimensions = dimensions ?? throw new ArgumentNullException(nameof(dimensions), "Dimensions cannot be null");
            return this;
        }

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Vehicle()
        {
            Id = Guid.NewGuid();
        }

        /// <summary>
        /// Constructor that allows setting a specific ID.
        /// This should only be used when recreating an existing vehicle from a DTO.
        /// </summary>
        /// <param name="id">The ID to use for this vehicle.</param>
        protected Vehicle(Guid id)
        {
            Id = id;
        }

        /// <summary>
        /// Copy constructor that creates a new vehicle as a copy of the source vehicle.
        /// Copies all devices and their connections.
        /// </summary>
        /// <param name="source">The source vehicle to copy from.</param>
        /// <exception cref="ArgumentNullException">Thrown when source is null.</exception>
        protected Vehicle(Vehicle source)
            : this()
        {
            ArgumentNullException.ThrowIfNull(source, nameof(source));

            // Copy basic properties
            _vehicleName = source.VehicleName;
            VehicleId = source.VehicleId; // Abstract property, set through property

            // Copy dimensions as a new instance to maintain immutability
            if (source.Dimensions != null)
            {
                _dimensions = new DimensionsFromGpsLocation(
                    source.Dimensions.DistanceGpsToFront,
                    source.Dimensions.DistanceGpsToBack,
                    source.Dimensions.DistanceGpsToLeft,
                    source.Dimensions.DistanceGpsToRight);
            }
            else
            {
                _dimensions = DimensionsFromGpsLocation.CreateDefault();
            }

            // Create a mapping of original device IDs to copied device IDs
            var deviceMap = new Dictionary<Guid, Guid>();

            // Copy all devices from the source vehicle
            foreach (var sourceDevice in source.GetAllDevices())
            {
                // Create a copy of the device using its Clone method
                // This will call the appropriate derived class's Clone method
                var deviceCopy = sourceDevice.Clone();

                // Set the vehicle ID to this vehicle's ID
                deviceCopy.VehicleId = Id;

                // Add the device to this vehicle
                AddDevice(deviceCopy);

                // Add to the device map
                deviceMap[sourceDevice.Id] = deviceCopy.Id;
            }

            // Copy device connections
            foreach (var connection in source.DeviceConnections)
            {
                // Check if both connected and interface devices were copied
                if (deviceMap.TryGetValue(connection.ConnectedDeviceId, out var newConnectedId) &&
                    deviceMap.TryGetValue(connection.InterfaceDeviceId, out var newInterfaceId))
                {
                    // Create a new connection between the copied devices
                    ConnectDeviceToInterface(newConnectedId, newInterfaceId, connection.Type, connection.Direction);
                }
            }
        }

        /// <summary>
        /// Adds a device to the vehicle based on its role.
        /// </summary>
        /// <param name="device">The device to add.</param>
        /// <returns>True if the device was added; otherwise, false.</returns>
        /// <exception cref="ArgumentNullException">Thrown when device is null.</exception>
        public virtual bool AddDevice(GenericDevice device)
        {
            ArgumentNullException.ThrowIfNull(device, nameof(device));

            // Check if device is already present in any device group
            var existingDevice = GetAllDevices().FirstOrDefault(d => d.Id == device.Id);
            if (existingDevice != null)
            {
                // Device already exists, don't add it again
                return false;
            }

            foreach (var group in GetAllDeviceGroups())
            {
                if (group.CanAcceptDevice(device))
                {
                    group.AddDevice(device);
                    device.VehicleId = Id;
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// Removes a device from the vehicle based on its role.
        /// </summary>
        /// <param name="device">The device to remove.</param>
        /// <returns>True if the device was removed; otherwise, false.</returns>
        public virtual bool RemoveDevice(GenericDevice device)
        {
            foreach (var group in GetAllDeviceGroups())
            {
                if (group.RemoveDevice(device))
                {
                    device.VehicleId = null;
                    // Also remove any connections involving this device
                    RemoveAllConnectionsForDevice(device.Id);
                    return true;
                }
            }
            return false;
        }

        /// <summary>
        /// Gets all devices installed on this vehicle.
        /// </summary>
        /// <returns>An enumerable of all devices.</returns>
        public virtual IEnumerable<GenericDevice> GetAllDevices()
        {
            // Get devices from all device groups
            return GetAllDeviceGroups().SelectMany(group => group.Devices);
        }

        /// <summary>
        /// Gets all devices of a specific role installed on this vehicle.
        /// </summary>
        /// <param name="role">The role of devices to retrieve.</param>
        /// <returns>An enumerable of devices with the specified role.</returns>
        public virtual IEnumerable<GenericDevice> GetDevicesByRole(DeviceRole role)
        {
            return GetAllDevices().Where(d => d.DeviceRole == role);
        }

        /// <summary>
        /// Gets all device groups in this vehicle.
        /// </summary>
        /// <returns>An enumerable of device groups.</returns>
        public virtual IEnumerable<IDeviceGroup> GetAllDeviceGroups()
        {
            // Return all device groups as an enumerable
            yield return CameraGroup;
            yield return EngineGroup;
        }

        /// <summary>
        /// Gets a device group of the specified type.
        /// </summary>
        /// <typeparam name="T">The type of device group to get.</typeparam>
        /// <returns>The device group if found; otherwise, null.</returns>
        public T? GetDeviceGroup<T>() where T : class, IDeviceGroup
        {
            // Check each device group to see if it's of the requested type
            foreach (var group in GetAllDeviceGroups())
            {
                if (group is T typedGroup)
                {
                    return typedGroup;
                }
            }

            return null;
        }

        /// <summary>
        /// Validates that the vehicle configuration is valid.
        /// </summary>
        /// <returns>True if valid.</returns>
        /// <exception cref="InvalidOperationException">Thrown when validation fails.</exception>
        public virtual bool Validate()
        {
            // Validate individual devices
            foreach (var device in GetAllDevices())
            {
                device.Validate();
            }

            return true;
        }

        /// <summary>
        /// Provides a string representation of this vehicle.
        /// </summary>
        /// <returns>A string representation of this vehicle.</returns>
        public override string ToString() =>
            $"{GetType().Name} (ID: {Id})";

        /// <summary>
        /// Creates a clone of this vehicle.
        /// </summary>
        /// <returns>A new vehicle that is a copy of this vehicle.</returns>
        public virtual Vehicle Clone()
        {
            // This method should be overridden by derived classes to return the correct type
            throw new NotImplementedException("Clone method must be implemented by derived classes");
        }

        #region Device Connection Methods

        /// <summary>
        /// Connects a device to an interface device in this vehicle.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the connected device (e.g., engine).</param>
        /// <param name="interfaceDeviceId">The ID of the interface device (e.g., PLC).</param>
        /// <param name="type">The type of connection.</param>
        /// <param name="direction">The direction of data flow.</param>
        /// <returns>True if the connection was created; otherwise, false.</returns>
        /// <exception cref="ArgumentException">Thrown when connected or interface device ID is invalid.</exception>
        /// <exception cref="InvalidOperationException">Thrown when devices are not compatible.</exception>
        public virtual bool ConnectDeviceToInterface(
            Guid connectedDeviceId,
            Guid interfaceDeviceId,
            ConnectionType type = ConnectionType.Standard,
            ConnectionDirection direction = ConnectionDirection.Duplex)
        {
            // Validate device IDs
            if (connectedDeviceId == Guid.Empty)
                throw new ArgumentException("Connected device ID cannot be empty", nameof(connectedDeviceId));

            if (interfaceDeviceId == Guid.Empty)
                throw new ArgumentException("Interface device ID cannot be empty", nameof(interfaceDeviceId));

            if (connectedDeviceId == interfaceDeviceId)
                throw new ArgumentException("Connected and interface device IDs cannot be the same", nameof(interfaceDeviceId));

            // Verify both devices exist in this vehicle
            var connectedDevice = GetAllDevices().FirstOrDefault(d => d.Id == connectedDeviceId) ?? throw new InvalidOperationException($"Connected device with ID {connectedDeviceId} not found in this vehicle");
            var interfaceDevice = GetAllDevices().FirstOrDefault(d => d.Id == interfaceDeviceId) ?? throw new InvalidOperationException($"Interface device with ID {interfaceDeviceId} not found in this vehicle");

            // Check device role compatibility
            if (!IsCompatibleWithInterface(connectedDeviceId, interfaceDeviceId))
                throw new InvalidOperationException(
                    $"Device role {connectedDevice.DeviceRole} is not compatible with {interfaceDevice.DeviceRole}");

            // Check if connection already exists
            if (DeviceInterfaceConnectionExists(connectedDeviceId, interfaceDeviceId))
                return false;

            // Create and add the connection
            var connection = new DeviceConnection(connectedDeviceId, interfaceDeviceId, type, direction);
            _deviceConnections.Add(connection);
            return true;
        }

        /// <summary>
        /// Disconnects a device from an interface device in this vehicle.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the connected device.</param>
        /// <param name="interfaceDeviceId">The ID of the interface device.</param>
        /// <returns>True if the connection was removed; otherwise, false.</returns>
        public virtual bool DisconnectDeviceFromInterface(Guid connectedDeviceId, Guid interfaceDeviceId)
        {
            // Find the connection
            var connection = _deviceConnections.FirstOrDefault(c =>
                c.ConnectedDeviceId == connectedDeviceId && c.InterfaceDeviceId == interfaceDeviceId);

            if (connection is null)
                return false;

            // Remove the connection
            return _deviceConnections.Remove(connection);
        }

        /// <summary>
        /// Gets all connections for a specific device.
        /// </summary>
        /// <param name="deviceId">The ID of the device.</param>
        /// <returns>An enumerable of device connections.</returns>
        public virtual IEnumerable<DeviceConnection> GetDeviceConnections(Guid deviceId)
        {
            return _deviceConnections.Where(c =>
                c.ConnectedDeviceId == deviceId || c.InterfaceDeviceId == deviceId);
        }

        /// <summary>
        /// Gets all outgoing connections from a specific device.
        /// </summary>
        /// <param name="deviceId">The ID of the device.</param>
        /// <returns>An enumerable of device connections.</returns>
        public virtual IEnumerable<DeviceConnection> GetOutgoingConnections(Guid deviceId)
        {
            return _deviceConnections.Where(c => c.ConnectedDeviceId == deviceId);
        }

        /// <summary>
        /// Gets all devices connected to a specific device.
        /// </summary>
        /// <param name="deviceId">The ID of the device.</param>
        /// <returns>An enumerable of devices connected to the specified device.</returns>
        public virtual IEnumerable<GenericDevice> GetConnectedDevices(Guid deviceId)
        {
            // Get all connections for this device
            var connections = GetDeviceConnections(deviceId);

            // Get all devices that are connected to this device
            var connectedDeviceIds = connections
                .Select(c => c.ConnectedDeviceId == deviceId ? c.InterfaceDeviceId : c.ConnectedDeviceId)
                .Distinct();

            // Return the actual device objects
            return GetAllDevices().Where(d => connectedDeviceIds.Contains(d.Id));
        }

        /// <summary>
        /// Gets all devices that have incoming connections from a specific device.
        /// </summary>
        /// <param name="deviceId">The ID of the device.</param>
        /// <returns>An enumerable of devices that have incoming connections from the specified device.</returns>
        public virtual IEnumerable<GenericDevice> GetTargetDevices(Guid deviceId)
        {
            // Get all outgoing connections from this device
            var connections = GetOutgoingConnections(deviceId);

            // Get the IDs of all interface devices
            var targetDeviceIds = connections.Select(c => c.InterfaceDeviceId).Distinct();

            // Return the actual device objects
            return GetAllDevices().Where(d => targetDeviceIds.Contains(d.Id));
        }

        /// <summary>
        /// Updates a connection between a device and an interface device.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the connected device.</param>
        /// <param name="interfaceDeviceId">The ID of the interface device.</param>
        /// <param name="type">The new connection type.</param>
        /// <param name="direction">The new connection direction.</param>
        /// <returns>True if the connection was updated; otherwise, false.</returns>
        public virtual bool UpdateDeviceInterfaceConnection(
            Guid connectedDeviceId,
            Guid interfaceDeviceId,
            ConnectionType type,
            ConnectionDirection direction)
        {
            // Find the connection
            var index = _deviceConnections.FindIndex(c =>
                c.ConnectedDeviceId == connectedDeviceId && c.InterfaceDeviceId == interfaceDeviceId);

            if (index < 0)
                return false;

            // Replace with completely updated connection
            var oldConnection = _deviceConnections[index];
            var newConnection = new DeviceConnection(connectedDeviceId, interfaceDeviceId, type, direction);
            _deviceConnections[index] = newConnection;
            return true;
        }

        /// <summary>
        /// Adds multiple connections between devices.
        /// </summary>
        /// <param name="connections">The connections to add.</param>
        /// <returns>The number of connections successfully added.</returns>
        /// TODO: ADD FOR CLONING VEHICLES AND IMPORTING DEVICES
        public virtual int AddConnections(IEnumerable<DeviceConnection> connections)
        {
            ArgumentNullException.ThrowIfNull(connections);

            int addedCount = 0;

            foreach (var connection in connections)
            {
                // Validate that both devices exist in this vehicle
                var connectedDevice = GetAllDevices().FirstOrDefault(d => d.Id == connection.ConnectedDeviceId);
                if (connectedDevice == null)
                    continue;

                var interfaceDevice = GetAllDevices().FirstOrDefault(d => d.Id == connection.InterfaceDeviceId);
                if (interfaceDevice == null)
                    continue;

                // Check if connection already exists
                if (_deviceConnections.Any(c =>
                    c.ConnectedDeviceId == connection.ConnectedDeviceId &&
                    c.InterfaceDeviceId == connection.InterfaceDeviceId))
                    continue;

                // Add the connection
                _deviceConnections.Add(connection);
                addedCount++;
            }

            return addedCount;
        }

        /// <summary>
        /// Removes all connections for a specific device.
        /// </summary>
        /// <param name="deviceId">The ID of the device.</param>
        /// <returns>The number of connections removed.</returns>
        public virtual int RemoveAllConnectionsForDevice(Guid deviceId)
        {
            // Find all connections involving this device
            var connectionsToRemove = _deviceConnections
                .Where(c => c.ConnectedDeviceId == deviceId || c.InterfaceDeviceId == deviceId)
                .ToList();

            // Remove all found connections
            foreach (var connection in connectionsToRemove)
            {
                _deviceConnections.Remove(connection);
            }

            return connectionsToRemove.Count;
        }

        /// <summary>
        /// Checks if a connection exists between a device and an interface device.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the connected device.</param>
        /// <param name="interfaceDeviceId">The ID of the interface device.</param>
        /// <returns>True if a connection exists; otherwise, false.</returns>
        public virtual bool DeviceInterfaceConnectionExists(Guid connectedDeviceId, Guid interfaceDeviceId)
        {
            return _deviceConnections.Any(c =>
                c.ConnectedDeviceId == connectedDeviceId && c.InterfaceDeviceId == interfaceDeviceId);
        }

        /// <summary>
        /// Gets a specific connection between two devices.
        /// </summary>
        /// <param name="connectedDeviceId">The ID of the connected device.</param>
        /// <param name="interfaceDeviceId">The ID of the interface device.</param>
        /// <returns>The connection if found; otherwise, null.</returns>
        public virtual DeviceConnection? GetConnection(Guid connectedDeviceId, Guid interfaceDeviceId)
        {
            return _deviceConnections.FirstOrDefault(c =>
                c.ConnectedDeviceId == connectedDeviceId && c.InterfaceDeviceId == interfaceDeviceId);
        }

        /// <summary>
        /// Determines if a device is compatible with an interface device based on their roles.
        /// </summary>
        /// <param name="deviceId">The ID of the device to check.</param>
        /// <param name="interfaceDeviceId">The ID of the potential interface device.</param>
        /// <returns>True if the devices are compatible; otherwise, false.</returns>
        /// <exception cref="ArgumentException">Thrown when either device ID is invalid.</exception>
        /// <exception cref="InvalidOperationException">Thrown when either device is not found in this vehicle.</exception>
        public virtual bool IsCompatibleWithInterface(Guid deviceId, Guid interfaceDeviceId)
        {
            if (deviceId == Guid.Empty)
                throw new ArgumentException("Device ID cannot be empty", nameof(deviceId));

            if (interfaceDeviceId == Guid.Empty)
                throw new ArgumentException("Interface device ID cannot be empty", nameof(interfaceDeviceId));

            if (deviceId == interfaceDeviceId)
                return false; // A device cannot be compatible with itself

            // Get the devices
            var device = GetAllDevices().FirstOrDefault(d => d.Id == deviceId)
                ?? throw new InvalidOperationException($"Device with ID {deviceId} not found in this vehicle");

            var interfaceDevice = GetAllDevices().FirstOrDefault(d => d.Id == interfaceDeviceId)
                ?? throw new InvalidOperationException($"Interface device with ID {interfaceDeviceId} not found in this vehicle");

            // Check role compatibility
            return device.DeviceRole.IsCompatibleWith(interfaceDevice.DeviceRole);
        }

        /// <summary>
        /// Gets all devices that can serve as interfaces for the specified device.
        /// </summary>
        /// <param name="deviceId">The ID of the device to find compatible interfaces for.</param>
        /// <returns>An enumerable of devices that can serve as interfaces for the specified device.</returns>
        /// <exception cref="ArgumentException">Thrown when the device ID is invalid.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the device is not found in this vehicle.</exception>
        public virtual IEnumerable<GenericDevice> GetCompatibleInterfaceDevices(Guid deviceId)
        {
            if (deviceId == Guid.Empty)
                throw new ArgumentException("Device ID cannot be empty", nameof(deviceId));

            // Get the device
            var device = GetAllDevices().FirstOrDefault(d => d.Id == deviceId)
                ?? throw new InvalidOperationException($"Device with ID {deviceId} not found in this vehicle");

            // Get all devices in this vehicle except the current one
            var potentialInterfaceDevices = GetAllDevices().Where(d => d.Id != deviceId);

            // Filter to only compatible devices based on role
            return potentialInterfaceDevices.Where(d => device.DeviceRole.IsCompatibleWith(d.DeviceRole));
        }

        /// <summary>
        /// Gets all devices connected to a specific interface device.
        /// </summary>
        /// <param name="interfaceDeviceId">The ID of the interface device.</param>
        /// <returns>An enumerable of devices connected to the specified interface device.</returns>
        /// <exception cref="ArgumentException">Thrown when the interface device ID is invalid.</exception>
        public virtual IEnumerable<GenericDevice> GetDevicesConnectedToInterface(Guid interfaceDeviceId)
        {
            if (interfaceDeviceId == Guid.Empty)
                throw new ArgumentException("Interface device ID cannot be empty", nameof(interfaceDeviceId));

            // Get all connections where this device is the interface
            var connections = _deviceConnections.Where(c => c.InterfaceDeviceId == interfaceDeviceId);

            // Get the IDs of all connected devices
            var connectedDeviceIds = connections.Select(c => c.ConnectedDeviceId).Distinct();

            // Return the actual device objects
            return GetAllDevices().Where(d => connectedDeviceIds.Contains(d.Id));
        }

        #endregion
    }
}