using Barret.Core.Areas.Common.ValueObjects;

namespace Barret.Core.Areas.Vehicles.ValueObjects
{
    /// <summary>
    /// Represents the immutable dimensions of a vehicle relative to its GPS location.
    /// </summary>
    public sealed class DimensionsFromGpsLocation : IEquatable<DimensionsFromGpsLocation>
    {
        /// <summary>
        /// Gets the distance from the GPS location to the front of the vehicle.
        /// </summary>
        public double DistanceGpsToFront { get; }

        /// <summary>
        /// Gets the distance from the GPS location to the back of the vehicle.
        /// </summary>
        public double DistanceGpsToBack { get; }

        /// <summary>
        /// Gets the distance from the GPS location to the left side of the vehicle.
        /// </summary>
        public double DistanceGpsToLeft { get; }

        /// <summary>
        /// Gets the distance from the GPS location to the right side of the vehicle.
        /// </summary>
        public double DistanceGpsToRight { get; }

        /// <summary>
        /// Gets the total length of the vehicle.
        /// </summary>
        public double Length => DistanceGpsToFront + DistanceGpsToBack;

        /// <summary>
        /// Gets the total width of the vehicle.
        /// </summary>
        public double Width => DistanceGpsToLeft + DistanceGpsToRight;

        /// <summary>
        /// Gets the total area of the vehicle.
        /// </summary>
        public double Area => Length * Width;

        /// <summary>
        /// Creates a default dimensions object with all distances set to 0.
        /// </summary>
        /// <returns>A new dimensions object with default values.</returns>
        public static DimensionsFromGpsLocation CreateDefault() => new(0, 0, 0, 0);

        /// <summary>
        /// Initializes a new instance of the <see cref="DimensionsFromGpsLocation"/> class.
        /// </summary>
        /// <param name="distanceGpsToFront">The distance from the GPS location to the front of the vehicle.</param>
        /// <param name="distanceGpsToBack">The distance from the GPS location to the back of the vehicle.</param>
        /// <param name="distanceGpsToLeft">The distance from the GPS location to the left side of the vehicle.</param>
        /// <param name="distanceGpsToRight">The distance from the GPS location to the right side of the vehicle.</param>
        /// <exception cref="ArgumentException">Thrown when any distance is negative.</exception>
        public DimensionsFromGpsLocation(
            double distanceGpsToFront,
            double distanceGpsToBack,
            double distanceGpsToLeft,
            double distanceGpsToRight)
        {
            if (distanceGpsToFront < 0)
                throw new ArgumentException("Distance to front cannot be negative", nameof(distanceGpsToFront));
            if (distanceGpsToBack < 0)
                throw new ArgumentException("Distance to back cannot be negative", nameof(distanceGpsToBack));
            if (distanceGpsToLeft < 0)
                throw new ArgumentException("Distance to left cannot be negative", nameof(distanceGpsToLeft));
            if (distanceGpsToRight < 0)
                throw new ArgumentException("Distance to right cannot be negative", nameof(distanceGpsToRight));

            DistanceGpsToFront = distanceGpsToFront;
            DistanceGpsToBack = distanceGpsToBack;
            DistanceGpsToLeft = distanceGpsToLeft;
            DistanceGpsToRight = distanceGpsToRight;
        }

        /// <summary>
        /// Creates a new dimensions object with an updated front distance.
        /// </summary>
        /// <param name="distanceGpsToFront">The new distance from GPS to front.</param>
        /// <returns>A new dimensions object with the updated front distance.</returns>
        public DimensionsFromGpsLocation WithFrontDistance(double distanceGpsToFront) =>
            new(distanceGpsToFront, DistanceGpsToBack, DistanceGpsToLeft, DistanceGpsToRight);

        /// <summary>
        /// Creates a new dimensions object with an updated back distance.
        /// </summary>
        /// <param name="distanceGpsToBack">The new distance from GPS to back.</param>
        /// <returns>A new dimensions object with the updated back distance.</returns>
        public DimensionsFromGpsLocation WithBackDistance(double distanceGpsToBack) =>
            new(DistanceGpsToFront, distanceGpsToBack, DistanceGpsToLeft, DistanceGpsToRight);

        /// <summary>
        /// Creates a new dimensions object with an updated left distance.
        /// </summary>
        /// <param name="distanceGpsToLeft">The new distance from GPS to left.</param>
        /// <returns>A new dimensions object with the updated left distance.</returns>
        public DimensionsFromGpsLocation WithLeftDistance(double distanceGpsToLeft) =>
            new(DistanceGpsToFront, DistanceGpsToBack, distanceGpsToLeft, DistanceGpsToRight);

        /// <summary>
        /// Creates a new dimensions object with an updated right distance.
        /// </summary>
        /// <param name="distanceGpsToRight">The new distance from GPS to right.</param>
        /// <returns>A new dimensions object with the updated right distance.</returns>
        public DimensionsFromGpsLocation WithRightDistance(double distanceGpsToRight) =>
            new(DistanceGpsToFront, DistanceGpsToBack, DistanceGpsToLeft, distanceGpsToRight);

        /// <summary>
        /// Creates a new dimensions object with updated measurements.
        /// </summary>
        /// <param name="distanceGpsToFront">The new distance from GPS to front.</param>
        /// <param name="distanceGpsToBack">The new distance from GPS to back.</param>
        /// <param name="distanceGpsToLeft">The new distance from GPS to left.</param>
        /// <param name="distanceGpsToRight">The new distance from GPS to right.</param>
        /// <returns>A new dimensions object with all updated measurements.</returns>
        public DimensionsFromGpsLocation WithDimensions(
            double distanceGpsToFront,
            double distanceGpsToBack,
            double distanceGpsToLeft,
            double distanceGpsToRight) =>
            new(distanceGpsToFront, distanceGpsToBack, distanceGpsToLeft, distanceGpsToRight);

        /// <summary>
        /// Determines if this object contains the same dimension values as another.
        /// </summary>
        public override bool Equals(object? obj) => Equals(obj as DimensionsFromGpsLocation);

        /// <summary>
        /// Determines if this object contains the same dimension values as another.
        /// </summary>
        public bool Equals(DimensionsFromGpsLocation? other)
        {
            if (other is null)
                return false;

            if (ReferenceEquals(this, other))
                return true;

            return DistanceGpsToFront.Equals(other.DistanceGpsToFront) &&
                   DistanceGpsToBack.Equals(other.DistanceGpsToBack) &&
                   DistanceGpsToLeft.Equals(other.DistanceGpsToLeft) &&
                   DistanceGpsToRight.Equals(other.DistanceGpsToRight);
        }

        /// <summary>
        /// Gets a hash code for this object.
        /// </summary>
        public override int GetHashCode()
        {
            return HashCode.Combine(
                DistanceGpsToFront,
                DistanceGpsToBack,
                DistanceGpsToLeft,
                DistanceGpsToRight);
        }

        /// <summary>
        /// Determines if two dimension objects are equal.
        /// </summary>
        public static bool operator ==(DimensionsFromGpsLocation? left, DimensionsFromGpsLocation? right)
        {
            if (left is null) return right is null;
            return left.Equals(right);
        }

        /// <summary>
        /// Determines if two dimension objects are not equal.
        /// </summary>
        public static bool operator !=(DimensionsFromGpsLocation? left, DimensionsFromGpsLocation? right)
        {
            return !(left == right);
        }

        /// <summary>
        /// Returns a string representation of this object.
        /// </summary>
        public override string ToString() =>
            $"L: {Length}m, W: {Width}m (F: {DistanceGpsToFront}m, B: {DistanceGpsToBack}m, L: {DistanceGpsToLeft}m, R: {DistanceGpsToRight}m)";
    }
}