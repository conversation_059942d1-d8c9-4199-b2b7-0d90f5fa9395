using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.DeviceModels;
using Microsoft.Extensions.Logging;

namespace Barret.Core.Areas.Manufacturers.Models
{
    /// <summary>
    /// Represents a manufacturer of devices.
    /// </summary>
    public class Manufacturer
    {
        private readonly ILogger<Manufacturer>? _logger;
        private string _name;
        private readonly List<DeviceModel> _deviceModels = [];

        /// <summary>
        /// Gets the unique identifier of the manufacturer.
        /// </summary>
        public Guid Id { get; } = Guid.NewGuid();

        /// <summary>
        /// Gets or sets the name of the manufacturer.
        /// </summary>
        public string Name
        {
            get => _name;
            set
            {
                if (string.IsNullOrWhiteSpace(value))
                    throw new ArgumentException("Name cannot be empty", nameof(value));

                _name = value;
                _logger?.LogInformation("Manufacturer name set to {Name}", value);
            }
        }

        /// <summary>
        /// Gets the name of the manufacturer.
        /// </summary>
        /// <returns>The name of the manufacturer.</returns>
        public string GetName()
        {
            return Name;
        }

        /// <summary>
        /// Gets the read-only collection of device types produced by this manufacturer.
        /// </summary>
        public IReadOnlyCollection<DeviceModel> DeviceModels => _deviceModels.AsReadOnly();

        /// <summary>
        /// Default constructor for EF Core.
        /// </summary>
        protected Manufacturer()
        {
            _name = string.Empty;
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="Manufacturer"/> class.
        /// </summary>
        /// <param name="name">The name of the manufacturer.</param>
        /// <param name="logger">Optional logger for diagnostic information.</param>
        /// <exception cref="ArgumentException">Thrown when the name is null or whitespace.</exception>
        public Manufacturer(string name, ILogger<Manufacturer>? logger = null)
        {
            _logger = logger;
            Name = name;
            _logger?.LogInformation("Created new manufacturer: {Name}", name);
        }

        /// <summary>
        /// Creates a new device model and adds it to this manufacturer.
        /// </summary>
        /// <param name="name">The name of the device model.</param>
        /// <param name="role">The role of the device model.</param>
        /// <returns>The created device model.</returns>
        /// <exception cref="ArgumentException">Thrown when the name is null or whitespace.</exception>
        public DeviceModel CreateDeviceModel(string name, DeviceRole role)
        {
            if (string.IsNullOrWhiteSpace(name))
                throw new ArgumentException("Name cannot be empty", nameof(name));

            var deviceModel = new DeviceModel(name, role);
            AddDeviceModel(deviceModel);

            _logger?.LogInformation("Created new device model: {Name} with role {Role} for manufacturer {ManufacturerName}",
                name, role, Name);

            return deviceModel;
        }

        /// <summary>
        /// Adds a device model to the manufacturer.
        /// </summary>
        /// <param name="deviceModel">The device model to add.</param>
        /// <exception cref="ArgumentNullException">Thrown when the device model is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the device model already belongs to this manufacturer.</exception>
        public void AddDeviceModel(DeviceModel deviceModel)
        {
            ArgumentNullException.ThrowIfNull(deviceModel, nameof(deviceModel));

            if (deviceModel.Manufacturer == this)
            {
                throw new InvalidOperationException($"Device model '{deviceModel.Name}' already belongs to this manufacturer.");
            }

            // Set the manufacturer relationship using the internal method
            deviceModel.SetManufacturer(this);

            // Add to the collection
            _deviceModels.Add(deviceModel);
            _logger?.LogInformation("Added device model {ModelName} to manufacturer {ManufacturerName}",
                deviceModel.Name, Name);
        }

        /// <summary>
        /// Removes a device model from the manufacturer.
        /// </summary>
        /// <param name="deviceModel">The device model to remove.</param>
        /// <exception cref="ArgumentNullException">Thrown when the device model is null.</exception>
        /// <exception cref="InvalidOperationException">Thrown when the device model doesn't belong to this manufacturer.</exception>
        public void RemoveDeviceModel(DeviceModel deviceModel)
        {
            ArgumentNullException.ThrowIfNull(deviceModel, nameof(deviceModel));

            if (!_deviceModels.Contains(deviceModel))
            {
                var error = $"Device model '{deviceModel.Name}' does not belong to this manufacturer.";
                _logger?.LogWarning(error);
                throw new InvalidOperationException(error);
            }

            _deviceModels.Remove(deviceModel);
            deviceModel.Manufacturer = null; // Remove the manufacturer using the property
            _logger?.LogInformation("Removed device model {ModelName} from manufacturer {ManufacturerName}",
                deviceModel.Name, Name);
        }
    }
}