namespace Barret.Core.Areas.Common.ValueObjects
{
    /// <summary>
    /// Represents an immutable 3D position relative to a reference point.
    /// This is a true value object that follows the immutability pattern.
    /// All properties are read-only and methods return new instances rather than modifying existing ones.
    /// </summary>
    public readonly struct RelativePosition : IEquatable<RelativePosition>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="RelativePosition"/> struct.
        /// </summary>
        /// <param name="x">The X coordinate.</param>
        /// <param name="y">The Y coordinate.</param>
        /// <param name="z">The Z coordinate.</param>
        public RelativePosition(double x, double y, double z)
        {
            X = x;
            Y = y;
            Z = z;
        }

        /// <summary>
        /// Gets the X coordinate.
        /// </summary>
        public readonly double X { get; }

        /// <summary>
        /// Gets the Y coordinate.
        /// </summary>
        public readonly double Y { get; }

        /// <summary>
        /// Gets the Z coordinate.
        /// </summary>
        public readonly double Z { get; }

        /// <summary>
        /// Parameterless constructor for EF Core.
        /// </summary>
        public RelativePosition()
        {
            X = 0;
            Y = 0;
            Z = 0;
        }

        /// <summary>
        /// Creates a default position at the origin (0,0,0).
        /// </summary>
        /// <returns>A new position at the origin.</returns>
        public static RelativePosition CreateDefault() => new(0, 0, 0);

        /// <summary>
        /// Creates a new position with the specified coordinates.
        /// This static method is a convenience factory method for creating positions.
        /// </summary>
        /// <param name="x">The X coordinate.</param>
        /// <param name="y">The Y coordinate.</param>
        /// <param name="z">The Z coordinate.</param>
        /// <returns>A new position with the specified coordinates.</returns>
        public static RelativePosition WithCoordinates(double x, double y, double z)
        {
            return new RelativePosition(x, y, z);
        }

        /// <summary>
        /// Creates a new position moved by the specified deltas.
        /// </summary>
        /// <param name="deltaX">The X delta.</param>
        /// <param name="deltaY">The Y delta.</param>
        /// <param name="deltaZ">The Z delta.</param>
        /// <returns>A new position with the adjusted coordinates.</returns>
        public RelativePosition MovedBy(double deltaX, double deltaY, double deltaZ)
        {
            return new RelativePosition(X + deltaX, Y + deltaY, Z + deltaZ);
        }

        /// <summary>
        /// Creates a new position with the X coordinate updated.
        /// </summary>
        /// <param name="x">The new X coordinate.</param>
        /// <returns>A new position with the updated X coordinate.</returns>
        public RelativePosition WithX(double x) => new(x, Y, Z);

        /// <summary>
        /// Creates a new position with the Y coordinate updated.
        /// </summary>
        /// <param name="y">The new Y coordinate.</param>
        /// <returns>A new position with the updated Y coordinate.</returns>
        public RelativePosition WithY(double y) => new(X, y, Z);

        /// <summary>
        /// Creates a new position with the Z coordinate updated.
        /// </summary>
        /// <param name="z">The new Z coordinate.</param>
        /// <returns>A new position with the updated Z coordinate.</returns>
        public RelativePosition WithZ(double z) => new(X, Y, z);

        /// <summary>
        /// Returns the distance to another position.
        /// </summary>
        /// <param name="other">The other position.</param>
        /// <returns>The Euclidean distance between this position and the other position.</returns>
        public double DistanceTo(RelativePosition other)
        {
            double dx = X - other.X;
            double dy = Y - other.Y;
            double dz = Z - other.Z;

            return Math.Sqrt(dx * dx + dy * dy + dz * dz);
        }

        /// <inheritdoc/>
        public override bool Equals(object? obj) =>
            obj is RelativePosition position && Equals(position);

        /// <inheritdoc/>
        public bool Equals(RelativePosition other) =>
            X == other.X && Y == other.Y && Z == other.Z;

        /// <inheritdoc/>
        public override int GetHashCode() =>
            HashCode.Combine(X, Y, Z);

        /// <summary>
        /// Determines whether two positions are equal.
        /// </summary>
        public static bool operator ==(RelativePosition left, RelativePosition right) =>
            left.Equals(right);

        /// <summary>
        /// Determines whether two positions are not equal.
        /// </summary>
        public static bool operator !=(RelativePosition left, RelativePosition right) =>
            !left.Equals(right);

        /// <inheritdoc/>
        public override string ToString() => $"({X}, {Y}, {Z})";
    }
}