namespace Barret.Core.Areas.Common.Events
{
    /// <summary>
    /// Defines methods for dispatching domain events.
    /// </summary>
    public interface IDomainEventDispatcher
    {
        /// <summary>
        /// Dispatches a domain event.
        /// </summary>
        /// <typeparam name="TEvent">The type of the event.</typeparam>
        /// <param name="domainEvent">The domain event to dispatch.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task DispatchAsync<TEvent>(TEvent domainEvent);
    }
} 