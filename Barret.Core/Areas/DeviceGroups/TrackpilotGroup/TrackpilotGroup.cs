using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Trackpilots;

namespace Barret.Core.Areas.DeviceGroups.TrackpilotGroup
{
    /// <summary>
    /// Device group for trackpilots
    /// </summary>
    public class TrackpilotGroup : IDeviceGroup
    {
        private readonly List<Trackpilot> _trackpilots = [];

        /// <summary>
        /// Creates a new instance of TrackpilotGroup
        /// </summary>
        public TrackpilotGroup()
        {
            Type = DeviceGroups.TrackpilotGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _trackpilots.Cast<GenericDevice>().ToList().AsReadOnly();

        /// <summary>
        /// Gets a read-only list of trackpilots in this group
        /// </summary>
        public IReadOnlyList<Trackpilot> Trackpilots => _trackpilots.AsReadOnly();



        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return device is Trackpilot && Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            if (device is Trackpilot trackpilot)
            {
                _trackpilots.Add(trackpilot);

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            if (device is Trackpilot trackpilot && _trackpilots.Remove(trackpilot))
            {

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _trackpilots.Clear();

        }

    }
}
