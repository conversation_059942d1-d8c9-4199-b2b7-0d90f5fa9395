using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Horns;

namespace Barret.Core.Areas.DeviceGroups.HornGroup
{
    /// <summary>
    /// Device group for horns
    /// </summary>
    public class HornGroup : IDeviceGroup
    {
        private readonly List<Horn> _horns = [];

        /// <summary>
        /// Creates a new instance of HornGroup
        /// </summary>
        public HornGroup()
        {
            Type = DeviceGroups.HornGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _horns.Cast<GenericDevice>().ToList().AsReadOnly();

        /// <summary>
        /// Gets a read-only list of horns in this group
        /// </summary>
        public IReadOnlyList<Horn> Horns => _horns.AsReadOnly();

        /// <summary>
        /// Event that is raised when the group changes
        /// </summary>

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return device is Horn && Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            if (device is Horn horn)
            {
                _horns.Add(horn);

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            if (device is Horn horn && _horns.Remove(horn))
            {

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _horns.Clear();
        }



    }
}
