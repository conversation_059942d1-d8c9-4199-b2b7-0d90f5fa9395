using Barret.Core.Areas.Devices.Enums;
using System;

namespace Barret.Core.Areas.DeviceGroups.Attributes
{
    /// <summary>
    /// Attribute to specify allowed device roles for a device group type
    /// </summary>
    /// <remarks>
    /// Creates a new instance of AllowedRolesAttribute
    /// </remarks>
    /// <param name="allowedRoles">The allowed device roles</param>
    [AttributeUsage(AttributeTargets.Field)]
    public class AllowedRolesAttribute(params DeviceRole[] allowedRoles) : Attribute
    {
        /// <summary>
        /// Gets the allowed device roles for a device group type
        /// </summary>
        public DeviceRole[] AllowedRoles { get; } = allowedRoles;
    }
}
