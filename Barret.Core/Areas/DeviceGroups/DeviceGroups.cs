using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups.Attributes;

namespace Barret.Core.Areas.DeviceGroups
{
    /// <summary>
    /// Enum for device groups
    /// </summary>
    public enum DeviceGroups
    {
        /// <summary>
        /// Camera system devices (cameras, NVR screens, NVR recording)
        /// </summary>
        [AllowedRoles(DeviceRole.Camera, DeviceRole.NVRScreen, DeviceRole.NVRRecording)]
        CameraGroup,

        /// <summary>
        /// Engine devices
        /// </summary>
        [AllowedRoles(DeviceRole.Engine)]
        EngineGroup,

        /// <summary>
        /// Thruster devices
        /// </summary>
        [AllowedRoles(DeviceRole.Thruster)]
        ThrusterGroup,

        /// <summary>
        /// Rudder devices
        /// </summary>
        [AllowedRoles(DeviceRole.Rudder)]
        RudderGroup,

        /// <summary>
        /// Light devices
        /// </summary>
        [AllowedRoles(DeviceRole.Light)]
        LightGroup,

        /// <summary>
        /// Radar devices
        /// </summary>
        [AllowedRoles(DeviceRole.Radar)]
        RadarGroup,

        /// <summary>
        /// Radio devices
        /// </summary>
        [AllowedRoles(DeviceRole.VHFMariphone)]
        RadioGroup,

        /// <summary>
        /// Horn devices
        /// </summary>
        [AllowedRoles(DeviceRole.Horn)]
        HornGroup,

        /// <summary>
        /// Antenna devices
        /// </summary>
        [AllowedRoles(DeviceRole.Antenna)]
        AntennaGroup,

        /// <summary>
        /// Autopilot devices
        /// </summary>
        [AllowedRoles(DeviceRole.Autopilot)]
        AutopilotGroup,

        /// <summary>
        /// Trackpilot devices
        /// </summary>
        [AllowedRoles(DeviceRole.Trackpilot)]
        TrackpilotGroup,

        /// <summary>
        /// Seafar VCS devices
        /// </summary>
        [AllowedRoles(
            DeviceRole.AMP,
            DeviceRole.SPAP,
            DeviceRole.HMI,
            DeviceRole.CabinetReadoutIO,
            DeviceRole.OperatorPanelIO,
            DeviceRole.GPU,
            DeviceRole.SafetySystemHead)]
        SeafarVCSGroup,

        /// <summary>
        /// Seafar Network devices
        /// </summary>
        [AllowedRoles(DeviceRole.Firewall, DeviceRole.Gateway, DeviceRole.Switch)]
        SeafarNetworkGroup,

        /// <summary>
        /// Sensor devices
        /// </summary>
        [AllowedRoles(DeviceRole.Sensor)]
        SensorGroup,

        /// <summary>
        /// Audio system devices
        /// </summary>
        [AllowedRoles(DeviceRole.AudioHub, DeviceRole.PAAudio)]
        AudioGroup,

        /// <summary>
        /// Interface devices
        /// </summary>
        [AllowedRoles(
            DeviceRole.EngineInterface,
            DeviceRole.ThrusterInterface,
            DeviceRole.AutopilotInterface,
            DeviceRole.NavData,
            DeviceRole.Mimer,
            DeviceRole.Framegrabber)]
        InterfaceGroup,

        /// <summary>
        /// Masts
        /// </summary>
        [AllowedRoles(DeviceRole.Mast)]
        MastGroup,

        /// <summary>
        /// Generators
        /// </summary>
        [AllowedRoles(DeviceRole.Generator)]
        GeneratorGroup
    }
}
