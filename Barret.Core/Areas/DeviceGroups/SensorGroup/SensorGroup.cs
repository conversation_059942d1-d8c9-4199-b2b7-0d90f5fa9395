
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Sensors;

namespace Barret.Core.Areas.DeviceGroups.SensorGroup
{
    /// <summary>
    /// Device group for sensors
    /// </summary>
    public class SensorGroup : IDeviceGroup
    {
        private readonly List<Sensor> _sensors = [];
        private readonly List<NavData> _navData = [];

        /// <summary>
        /// Creates a new instance of SensorGroup
        /// </summary>
        public SensorGroup()
        {
            Type = DeviceGroups.SensorGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _sensors.Cast<GenericDevice>()
                .Concat(_navData)
                .ToList()
                .AsReadOnly();

        /// <summary>
        /// Gets a read-only list of sensors in this group
        /// </summary>
        public IReadOnlyList<Sensor> Sensors => _sensors.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of navigation data devices in this group
        /// </summary>
        public IReadOnlyList<NavData> NavData => _navData.AsReadOnly();

        /// <summary>
        /// Event that is raised when the group changes
        /// </summary>

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return (device is Sensor || device is NavData) &&
                   Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            switch (device)
            {
                case Sensor sensor:
                    _sensors.Add(sensor);
                    break;
                case NavData navData:
                    _navData.Add(navData);
                    break;
                default:
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            bool removed = false;

            switch (device)
            {
                case Sensor sensor:
                    removed = _sensors.Remove(sensor);
                    break;
                case NavData navData:
                    removed = _navData.Remove(navData);
                    break;
            }

            if (!removed)
                return false;

            return true;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _sensors.Clear();
            _navData.Clear();
        }

    }
}
