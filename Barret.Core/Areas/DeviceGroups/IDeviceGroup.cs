using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.DeviceGroups
{
    /// <summary>
    /// Interface for device groups that define behavior for adding, removing, and managing devices
    /// </summary>
    public interface IDeviceGroup
    {
        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        IReadOnlyList<GenericDevice> Devices { get; }

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        bool CanAcceptDevice(GenericDevice device);

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        bool AddDevice(GenericDevice device);

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        bool RemoveDevice(GenericDevice device);

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        void Clear();

        /// <summary>
        /// Gets the device roles that are allowed in this group
        /// </summary>
        /// <returns>An enumerable of allowed device roles</returns>
        IEnumerable<DeviceRole> GetAllowedRoles()
        {
            // Default implementation that uses the AllowedRoles attribute on the Type enum
            return Type.GetAllowedRoles();
        }
    }
}
