using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Network;

namespace Barret.Core.Areas.DeviceGroups.Seafar.NetworkGroup
{
    /// <summary>
    /// Device group for Seafar Network
    /// </summary>
    public class SeafarNetworkGroup : IDeviceGroup
    {
        private readonly List<Firewall> _firewalls = [];
        private readonly List<Gateway> _gateways = [];
        private readonly List<Switch> _switches = [];
        private readonly List<Plc> _plcs = [];

        /// <summary>
        /// Creates a new instance of SeafarNetworkGroup
        /// </summary>
        public SeafarNetworkGroup()
        {
            Type = DeviceGroups.SeafarNetworkGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _firewalls.Cast<GenericDevice>()
                .Concat(_gateways)
                .Concat(_switches)
                .Concat(_plcs)
                .ToList()
                .AsReadOnly();

        /// <summary>
        /// Gets a read-only list of firewalls in this group
        /// </summary>
        public IReadOnlyList<Firewall> Firewalls => _firewalls.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of gateways in this group
        /// </summary>
        public IReadOnlyList<Gateway> Gateways => _gateways.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of switches in this group
        /// </summary>
        public IReadOnlyList<Switch> Switches => _switches.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of PLCs in this group
        /// </summary>
        public IReadOnlyList<Plc> Plcs => _plcs.AsReadOnly();

        /// <summary>
        /// Event that is raised when the group changes
        /// </summary>

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return (device is Firewall || device is Gateway || device is Switch || device is Plc) &&
                   Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            switch (device)
            {
                case Firewall firewall:
                    _firewalls.Add(firewall);
                    break;
                case Gateway gateway:
                    _gateways.Add(gateway);
                    break;
                case Switch networkSwitch:
                    _switches.Add(networkSwitch);
                    break;
                case Plc plc:
                    _plcs.Add(plc);
                    break;
                default:
                    return false;
            }


            return true;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            bool removed = false;

            switch (device)
            {
                case Firewall firewall:
                    removed = _firewalls.Remove(firewall);
                    break;
                case Gateway gateway:
                    removed = _gateways.Remove(gateway);
                    break;
                case Switch networkSwitch:
                    removed = _switches.Remove(networkSwitch);
                    break;
                case Plc plc:
                    removed = _plcs.Remove(plc);
                    break;
            }

            if (!removed)
                return false;


            return true;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _firewalls.Clear();
            _gateways.Clear();
            _switches.Clear();
            _plcs.Clear();
        }



    }
}
