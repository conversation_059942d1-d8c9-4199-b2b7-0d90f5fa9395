using Barret.Core.Areas.Devices.Models.Audio;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.VCS;

namespace Barret.Core.Areas.DeviceGroups.Seafar.VCSGroup
{
    /// <summary>
    /// Device group for Seafar VCS (Vessel Control System)
    /// </summary>
    public class SeafarVCSGroup : IDeviceGroup
    {
        private readonly List<AMP> _amps = [];
        private readonly List<SPAP> _spaps = [];
        private readonly List<HMI> _hmis = [];
        private readonly List<CabinetReadoutIO> _cabinetReadoutIOs = [];
        private readonly List<OperatorPanelIO> _operatorPanelIOs = [];
        private readonly List<GPU> _gpus = [];
        private readonly List<SafetySystemHead> _safetySystemHeads = [];

        /// <summary>
        /// Creates a new instance of SeafarVCSGroup
        /// </summary>
        public SeafarVCSGroup()
        {
            Type = DeviceGroups.SeafarVCSGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _amps.Cast<GenericDevice>()
                .Concat(_spaps)
                .Concat(_hmis)
                .Concat(_cabinetReadoutIOs)
                .Concat(_operatorPanelIOs)
                .Concat(_gpus)
                .Concat(_safetySystemHeads)
                .ToList()
                .AsReadOnly();

        /// <summary>
        /// Gets a read-only list of AMPs in this group
        /// </summary>
        public IReadOnlyList<AMP> AMPs => _amps.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of SPAPs in this group
        /// </summary>
        public IReadOnlyList<SPAP> SPAPs => _spaps.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of HMIs in this group
        /// </summary>
        public IReadOnlyList<HMI> HMIs => _hmis.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of Cabinet Readout IOs in this group
        /// </summary>
        public IReadOnlyList<CabinetReadoutIO> CabinetReadoutIOs => _cabinetReadoutIOs.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of Operator Panel IOs in this group
        /// </summary>
        public IReadOnlyList<OperatorPanelIO> OperatorPanelIOs => _operatorPanelIOs.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of GPUs in this group
        /// </summary>
        public IReadOnlyList<GPU> GPUs => _gpus.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of Safety System Heads in this group
        /// </summary>
        public IReadOnlyList<SafetySystemHead> SafetySystemHeads => _safetySystemHeads.AsReadOnly();

        /// <summary>
        /// Event that is raised when the group changes
        /// </summary>

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return (device is AMP || device is SPAP || device is HMI ||
                   device is CabinetReadoutIO || device is OperatorPanelIO ||
                   device is GPU || device is SafetySystemHead) &&
                   Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            switch (device)
            {
                case AMP amp:
                    _amps.Add(amp);
                    break;
                case SPAP spap:
                    _spaps.Add(spap);
                    break;
                case HMI hmi:
                    _hmis.Add(hmi);
                    break;
                case CabinetReadoutIO cabinetReadoutIO:
                    _cabinetReadoutIOs.Add(cabinetReadoutIO);
                    break;
                case OperatorPanelIO operatorPanelIO:
                    _operatorPanelIOs.Add(operatorPanelIO);
                    break;
                case GPU gpu:
                    _gpus.Add(gpu);
                    break;
                case SafetySystemHead safetySystemHead:
                    _safetySystemHeads.Add(safetySystemHead);
                    break;
                default:
                    return false;
            }


            return true;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            bool removed = false;

            switch (device)
            {
                case AMP amp:
                    removed = _amps.Remove(amp);
                    break;
                case SPAP spap:
                    removed = _spaps.Remove(spap);
                    break;
                case HMI hmi:
                    removed = _hmis.Remove(hmi);
                    break;
                case CabinetReadoutIO cabinetReadoutIO:
                    removed = _cabinetReadoutIOs.Remove(cabinetReadoutIO);
                    break;
                case OperatorPanelIO operatorPanelIO:
                    removed = _operatorPanelIOs.Remove(operatorPanelIO);
                    break;
                case GPU gpu:
                    removed = _gpus.Remove(gpu);
                    break;
                case SafetySystemHead safetySystemHead:
                    removed = _safetySystemHeads.Remove(safetySystemHead);
                    break;
            }

            if (removed)
            {

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _amps.Clear();
            _spaps.Clear();
            _hmis.Clear();
            _cabinetReadoutIOs.Clear();
            _operatorPanelIOs.Clear();
            _gpus.Clear();
            _safetySystemHeads.Clear();
        }



    }
}
