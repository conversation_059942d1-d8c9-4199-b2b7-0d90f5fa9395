using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Interfaces;
using Barret.Core.Areas.Devices.Models.Sensors;

namespace Barret.Core.Areas.DeviceGroups.InterfaceGroup
{
    /// <summary>
    /// Device group for interface devices
    /// </summary>
    public class InterfaceGroup : IDeviceGroup
    {
        private readonly List<EngineInterface> _engineInterfaces = [];
        private readonly List<ThrusterInterface> _thrusterInterfaces = [];
        private readonly List<AutopilotInterface> _autopilotInterfaces = [];
        private readonly List<NavData> _navDataDevices = [];
        private readonly List<Mimer> _mimers = [];
        private readonly List<Framegrabber> _framegrabbers = [];

        /// <summary>
        /// Creates a new instance of InterfaceGroup
        /// </summary>
        public InterfaceGroup()
        {
            Type = DeviceGroups.InterfaceGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _engineInterfaces.Cast<GenericDevice>()
                .Concat(_thrusterInterfaces)
                .Concat(_autopilotInterfaces)
                .Concat(_navDataDevices)
                .Concat(_mimers)
                .Concat(_framegrabbers)
                .ToList()
                .AsReadOnly();

        /// <summary>
        /// Gets a read-only list of engine interfaces in this group
        /// </summary>
        public IReadOnlyList<EngineInterface> EngineInterfaces => _engineInterfaces.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of thruster interfaces in this group
        /// </summary>
        public IReadOnlyList<ThrusterInterface> ThrusterInterfaces => _thrusterInterfaces.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of autopilot interfaces in this group
        /// </summary>
        public IReadOnlyList<AutopilotInterface> AutopilotInterfaces => _autopilotInterfaces.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of navigation data devices in this group
        /// </summary>
        public IReadOnlyList<NavData> NavDataDevices => _navDataDevices.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of Mimer devices in this group
        /// </summary>
        public IReadOnlyList<Mimer> Mimers => _mimers.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of framegrabber devices in this group
        /// </summary>
        public IReadOnlyList<Framegrabber> Framegrabbers => _framegrabbers.AsReadOnly();

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return (device is EngineInterface || device is ThrusterInterface || device is AutopilotInterface ||
                    device is NavData || device is Mimer || device is Framegrabber) &&
                   Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            switch (device)
            {
                case EngineInterface engineInterface:
                    _engineInterfaces.Add(engineInterface);
                    break;
                case ThrusterInterface thrusterInterface:
                    _thrusterInterfaces.Add(thrusterInterface);
                    break;
                case AutopilotInterface autopilotInterface:
                    _autopilotInterfaces.Add(autopilotInterface);
                    break;
                case NavData navData:
                    _navDataDevices.Add(navData);
                    break;
                case Mimer mimer:
                    _mimers.Add(mimer);
                    break;
                case Framegrabber framegrabber:
                    _framegrabbers.Add(framegrabber);
                    break;
                default:
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            switch (device)
            {
                case EngineInterface engineInterface when _engineInterfaces.Remove(engineInterface):
                case ThrusterInterface thrusterInterface when _thrusterInterfaces.Remove(thrusterInterface):
                case AutopilotInterface autopilotInterface when _autopilotInterfaces.Remove(autopilotInterface):
                case NavData navData when _navDataDevices.Remove(navData):
                case Mimer mimer when _mimers.Remove(mimer):
                case Framegrabber framegrabber when _framegrabbers.Remove(framegrabber):
                    return true;
                default:
                    return false;
            }
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _engineInterfaces.Clear();
            _thrusterInterfaces.Clear();
            _autopilotInterfaces.Clear();
            _navDataDevices.Clear();
            _mimers.Clear();
            _framegrabbers.Clear();
        }
    }
}
