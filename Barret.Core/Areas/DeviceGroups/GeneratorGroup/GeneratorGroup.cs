using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Generators;

namespace Barret.Core.Areas.DeviceGroups.GeneratorGroup
{
    /// <summary>
    /// Device group for generators
    /// </summary>
    public class GeneratorGroup : IDeviceGroup
    {
        private readonly List<Generator> _generators = [];

        /// <summary>
        /// Creates a new instance of GeneratorGroup
        /// </summary>
        public GeneratorGroup()
        {
            Type = DeviceGroups.GeneratorGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _generators.Cast<GenericDevice>().ToList().AsReadOnly();

        /// <summary>
        /// Gets a read-only list of generators in this group
        /// </summary>
        public IReadOnlyList<Generator> Generators => _generators.AsReadOnly();

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return device is Generator && Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            if (device is Generator generator)
            {
                _generators.Add(generator);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            if (device is Generator generator && _generators.Remove(generator))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _generators.Clear();
        }

        /// <summary>
        /// Determines if this group has a main generator
        /// </summary>
        /// <returns>True if a main generator is present; otherwise, false</returns>
        public bool HasMainGenerator()
        {
            return _generators.Any(g => g.Name.Contains("Main", StringComparison.OrdinalIgnoreCase));
        }
    }
}
