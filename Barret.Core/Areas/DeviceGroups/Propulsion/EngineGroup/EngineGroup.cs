using Barret.Core.Areas.Devices.Models.Engines;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.DeviceGroups.Propulsion.EngineGroup
{
    /// <summary>
    /// Device group for engines
    /// </summary>
    public class EngineGroup : IDeviceGroup
    {
        private readonly List<Engine> _engines = [];

        /// <summary>
        /// Creates a new instance of EngineGroup
        /// </summary>
        public EngineGroup()
        {
            Type = DeviceGroups.EngineGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _engines.Cast<GenericDevice>().ToList().AsReadOnly();

        /// <summary>
        /// Gets a read-only list of engines in this group
        /// </summary>
        public IReadOnlyList<Engine> EngineList => _engines.AsReadOnly();

        /// <summary>
        /// Event that is raised when the group changes
        /// </summary>

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return device is Engine && Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            if (device is Engine engine)
            {
                _engines.Add(engine);

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            if (device is Engine engine && _engines.Remove(engine))
            {

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _engines.Clear();
        }




        /// <summary>
        /// Determines if this group has a main engine
        /// </summary>
        /// <returns>True if a main engine is present; otherwise, false</returns>
        public bool HasMainEngine()
        {
            return _engines.Any(e => e.Name.Contains("Main", StringComparison.OrdinalIgnoreCase));
        }
    }
}
