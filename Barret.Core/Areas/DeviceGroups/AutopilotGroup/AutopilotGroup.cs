
using Barret.Core.Areas.Devices.Models.Autopilots;
using Barret.Core.Areas.Devices.Models.GenericDevices;

namespace Barret.Core.Areas.DeviceGroups.AutopilotGroup
{
    /// <summary>
    /// Device group for autopilots
    /// </summary>
    public class AutopilotGroup : IDeviceGroup
    {
        private readonly List<Autopilot> _autopilots = [];

        /// <summary>
        /// Creates a new instance of AutopilotGroup
        /// </summary>
        public AutopilotGroup()
        {
            Type = DeviceGroups.AutopilotGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _autopilots.Cast<GenericDevice>().ToList().AsReadOnly();

        /// <summary>
        /// Gets a read-only list of autopilots in this group
        /// </summary>
        public IReadOnlyList<Autopilot> Autopilots => _autopilots.AsReadOnly();

        /// <summary>
        /// Event that is raised when the group changes
        /// </summary>

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return device is Autopilot && Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            if (device is Autopilot autopilot)
            {
                _autopilots.Add(autopilot);

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            if (device is Autopilot autopilot && _autopilots.Remove(autopilot))
            {

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _autopilots.Clear();
        }

    }
}
