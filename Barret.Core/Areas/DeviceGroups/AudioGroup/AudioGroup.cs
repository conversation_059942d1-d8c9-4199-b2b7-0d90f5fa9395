using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Audio;

namespace Barret.Core.Areas.DeviceGroups.AudioGroup
{
    /// <summary>
    /// Device group for audio system
    /// </summary>
    public class AudioGroup : IDeviceGroup
    {
        private readonly List<AudioHub> _audioHubs = [];
        private readonly List<PAAudio> _paAudios = [];

        /// <summary>
        /// Creates a new instance of AudioGroup
        /// </summary>
        public AudioGroup()
        {
            Type = DeviceGroups.AudioGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _audioHubs.Cast<GenericDevice>()
                .Concat(_paAudios)
                .ToList()
                .AsReadOnly();

        /// <summary>
        /// Gets a read-only list of audio hubs in this group
        /// </summary>
        public IReadOnlyList<AudioHub> AudioHubs => _audioHubs.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of PA audio devices in this group
        /// </summary>
        public IReadOnlyList<PAAudio> PAAudios => _paAudios.AsReadOnly();

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return (device is AudioHub || device is PAAudio) &&
                   Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            switch (device)
            {
                case AudioHub audioHub:
                    _audioHubs.Add(audioHub);
                    break;
                case PAAudio paAudio:
                    _paAudios.Add(paAudio);
                    break;
                default:
                    return false;
            }

            return true;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            bool removed = false;

            switch (device)
            {
                case AudioHub audioHub:
                    removed = _audioHubs.Remove(audioHub);
                    break;
                case PAAudio paAudio:
                    removed = _paAudios.Remove(paAudio);
                    break;
            }

            if (!removed)
                return false;

            return true;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _audioHubs.Clear();
            _paAudios.Clear();
        }


    }
}
