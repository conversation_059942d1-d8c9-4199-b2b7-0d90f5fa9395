using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups.Attributes;
using System.Collections.Generic;
using System.Reflection;

namespace Barret.Core.Areas.DeviceGroups
{
    /// <summary>
    /// Extension methods for DeviceGroups enum to retrieve allowed roles
    /// </summary>
    public static class DeviceGroupsExtensions
    {
        // Cached lookup for fast GetAllowedRoles checks
        private static readonly IReadOnlyDictionary<DeviceGroups, DeviceRole[]> _allowedRolesMap;

        static DeviceGroupsExtensions()
        {
            // Build the allowed roles map once at startup
            _allowedRolesMap = Enum.GetValues<DeviceGroups>()
                .ToDictionary(
                    group => group,
                    group =>
                    {
                        // Retrieve the AllowedRolesAttribute on each enum member
                        var memberInfo = typeof(DeviceGroups)
                            .GetMember(group.ToString())
                            .FirstOrDefault();
                        var attr = memberInfo?
                            .GetCustomAttribute<AllowedRolesAttribute>(inherit: false);
                        return attr?.AllowedRoles ?? Array.Empty<DeviceRole>();
                    }
                );
        }

        /// <summary>
        /// Gets the allowed roles for a device group
        /// </summary>
        /// <param name="group">The device group</param>
        /// <returns>The allowed roles</returns>
        public static DeviceRole[] GetAllowedRoles(this DeviceGroups group)
        {
            return _allowedRolesMap.TryGetValue(group, out var roles)
                ? roles
                : Array.Empty<DeviceRole>();
        }

        /// <summary>
        /// Determines if a device group allows a specific device role
        /// </summary>
        /// <param name="group">The device group</param>
        /// <param name="role">The device role to check</param>
        /// <returns>True if the device role is allowed in the group; otherwise, false</returns>
        public static bool AllowsRole(this DeviceGroups group, DeviceRole role)
        {
            return _allowedRolesMap.TryGetValue(group, out var roles)
                && roles.Contains(role);
        }
    }
}
