using System.Collections.Immutable;

namespace Barret.Core.Areas.DeviceGroups
{
    /// <summary>
    /// UI-related extension methods for device groups
    /// </summary>
    public static class DeviceGroupUIExtensions
    {
        #region Display Names

        private static readonly ImmutableDictionary<DeviceGroups, string> DisplayNames = new Dictionary<DeviceGroups, string>
        {
            { DeviceGroups.CameraGroup, "Camera System" },
            { DeviceGroups.EngineGroup, "Engines" },
            { DeviceGroups.ThrusterGroup, "Thrusters" },
            { DeviceGroups.RudderGroup, "Rudders" },
            { DeviceGroups.LightGroup, "Lights" },
            { DeviceGroups.RadarGroup, "Radars" },
            { DeviceGroups.RadioGroup, "Radios" },
            { DeviceGroups.HornGroup, "Horns" },
            { DeviceGroups.AntennaGroup, "Antennas" },
            { DeviceGroups.AutopilotGroup, "Autopilots" },
            { DeviceGroups.TrackpilotGroup, "Trackpilots" },
            { DeviceGroups.SeafarVCSGroup, "Seafar VCS" },
            { DeviceGroups.SeafarNetworkGroup, "Seafar Network" },
            { DeviceGroups.SensorGroup, "Sensors" },
            { DeviceGroups.AudioGroup, "Audio System" },
            { DeviceGroups.InterfaceGroup, "Interfaces" },
            { DeviceGroups.MastGroup, "Masts" },
            { DeviceGroups.GeneratorGroup, "Generators" }
        }.ToImmutableDictionary();

        /// <summary>
        /// Gets the display name for a device group
        /// </summary>
        /// <param name="group">The device group</param>
        /// <returns>The display name</returns>
        public static string GetDisplayName(this IDeviceGroup group)
        {
            return group.Type.GetDisplayName();
        }

        /// <summary>
        /// Gets the display name for a device group type
        /// </summary>
        /// <param name="groupType">The device group type</param>
        /// <returns>The display name</returns>
        public static string GetDisplayName(this DeviceGroups groupType)
        {
            return DisplayNames.TryGetValue(groupType, out var displayName)
                ? displayName
                : groupType.ToString();
        }

        #endregion

        #region Icon CSS Classes

        private static readonly ImmutableDictionary<DeviceGroups, string> IconClasses = new Dictionary<DeviceGroups, string>
        {
            { DeviceGroups.CameraGroup, "bi bi-camera-video" },
            { DeviceGroups.EngineGroup, "bi bi-gear" },
            { DeviceGroups.ThrusterGroup, "bi bi-arrow-right-circle" },
            { DeviceGroups.RudderGroup, "bi bi-arrow-left-right" },
            { DeviceGroups.RadarGroup, "bi bi-radar" },
            { DeviceGroups.RadioGroup, "bi bi-broadcast" },
            { DeviceGroups.HornGroup, "bi bi-volume-up" },
            { DeviceGroups.AntennaGroup, "bi bi-broadcast-pin" },
            { DeviceGroups.AutopilotGroup, "bi bi-compass" },
            { DeviceGroups.TrackpilotGroup, "bi bi-map" },
            { DeviceGroups.SeafarVCSGroup, "bi bi-cpu" },
            { DeviceGroups.SeafarNetworkGroup, "bi bi-hdd-network" },
            { DeviceGroups.SensorGroup, "bi bi-reception-4" },
            { DeviceGroups.LightGroup, "bi bi-lightbulb" },
            { DeviceGroups.AudioGroup, "bi bi-speaker" }
        }.ToImmutableDictionary();

        /// <summary>
        /// Gets the icon CSS class for a device group
        /// </summary>
        /// <param name="group">The device group</param>
        /// <returns>The icon CSS class</returns>
        public static string GetIconCssClass(this IDeviceGroup group)
        {
            return group.Type.GetIconCssClass();
        }

        /// <summary>
        /// Gets the icon CSS class for a device group type
        /// </summary>
        /// <param name="groupType">The device group type</param>
        /// <returns>The icon CSS class</returns>
        public static string GetIconCssClass(this DeviceGroups groupType)
        {
            return IconClasses.TryGetValue(groupType, out var iconClass)
                ? iconClass
                : "bi bi-collection";
        }

        #endregion

        #region Display Order

        private static readonly ImmutableDictionary<DeviceGroups, int> DisplayOrders = new Dictionary<DeviceGroups, int>
        {
            { DeviceGroups.CameraGroup, 10 },
            { DeviceGroups.EngineGroup, 20 },
            { DeviceGroups.ThrusterGroup, 30 },
            { DeviceGroups.RudderGroup, 40 },
            { DeviceGroups.RadarGroup, 50 },
            { DeviceGroups.AntennaGroup, 60 },
            { DeviceGroups.AutopilotGroup, 70 },
            { DeviceGroups.TrackpilotGroup, 80 },
            { DeviceGroups.RadioGroup, 90 },
            { DeviceGroups.AudioGroup, 100 },
            { DeviceGroups.SeafarVCSGroup, 110 },
            { DeviceGroups.SeafarNetworkGroup, 120 },
            { DeviceGroups.SensorGroup, 130 },
            { DeviceGroups.LightGroup, 140 },
            { DeviceGroups.HornGroup, 150 }
        }.ToImmutableDictionary();

        /// <summary>
        /// Gets the display order for a device group
        /// </summary>
        /// <param name="group">The device group</param>
        /// <returns>The display order</returns>
        public static int GetDisplayOrder(this IDeviceGroup group)
        {
            return group.Type.GetDisplayOrder();
        }

        /// <summary>
        /// Gets the display order for a device group type
        /// </summary>
        /// <param name="groupType">The device group type</param>
        /// <returns>The display order</returns>
        public static int GetDisplayOrder(this DeviceGroups groupType)
        {
            return DisplayOrders.TryGetValue(groupType, out var displayOrder)
                ? displayOrder
                : 999;
        }

        #endregion
    }
}
