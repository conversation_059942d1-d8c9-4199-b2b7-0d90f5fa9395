using Barret.Core.Areas.Devices.Models.Cameras;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.NVR;

namespace Barret.Core.Areas.DeviceGroups.CameraGroup
{
    /// <summary>
    /// Device group for camera system devices
    /// </summary>
    public class CameraGroup : IDeviceGroup
    {
        private readonly List<Camera> _cameras = [];
        private readonly List<NVRScreen> _nvrScreens = [];
        private readonly List<NVRRecording> _nvrRecordings = [];

        /// <summary>
        /// Creates a new instance of CameraGroup
        /// </summary>
        public CameraGroup()
        {
            Type = DeviceGroups.CameraGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _cameras.Cast<GenericDevice>()
                .Concat(_nvrScreens)
                .Concat(_nvrRecordings)
                .ToList()
                .AsReadOnly();

        /// <summary>
        /// Gets a read-only list of cameras in this group
        /// </summary>
        public IReadOnlyList<Camera> Cameras => _cameras.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of NVR screens in this group
        /// </summary>
        public IReadOnlyList<NVRScreen> NVRScreens => _nvrScreens.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of NVR recordings in this group
        /// </summary>
        public IReadOnlyList<NVRRecording> NVRRecordings => _nvrRecordings.AsReadOnly();



        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return (device is Camera || device is NVRScreen || device is NVRRecording) &&
                   Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            if (device is Camera camera)
            {
                _cameras.Add(camera);

                return true;
            }

            if (device is NVRScreen screen)
            {
                _nvrScreens.Add(screen);

                return true;
            }

            if (device is NVRRecording recording)
            {
                _nvrRecordings.Add(recording);

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            if (device is Camera camera && _cameras.Remove(camera))
            {

                return true;
            }

            if (device is NVRScreen screen && _nvrScreens.Remove(screen))
            {

                return true;
            }

            if (device is NVRRecording recording && _nvrRecordings.Remove(recording))
            {

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _cameras.Clear();
            _nvrScreens.Clear();
            _nvrRecordings.Clear();
        }



    }
}
