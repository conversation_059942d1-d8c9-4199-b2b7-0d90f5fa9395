
using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Radars;

namespace Barret.Core.Areas.DeviceGroups.RadarGroup
{
    /// <summary>
    /// Device group for radars
    /// </summary>
    public class RadarGroup : IDeviceGroup
    {
        private readonly List<Radar> _radars = [];
        private readonly List<GenericDevice> _framegrabbers = []; // Using GenericDevice since we don't have a specific Framegrabber class

        /// <summary>
        /// Creates a new instance of RadarGroup
        /// </summary>
        public RadarGroup()
        {
            Type = DeviceGroups.RadarGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _radars.Cast<GenericDevice>()
                .Concat(_framegrabbers)
                .ToList()
                .AsReadOnly();

        /// <summary>
        /// Gets a read-only list of radars in this group
        /// </summary>
        public IReadOnlyList<Radar> Radars => _radars.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of framegrabbers in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Framegrabbers => _framegrabbers.AsReadOnly();



        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return ((device is Radar) || device.DeviceRole == DeviceRole.Framegrabber) &&
                   Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            if (device is Radar radar)
            {
                _radars.Add(radar);

                return true;
            }

            if (device.DeviceRole == DeviceRole.Framegrabber)
            {
                _framegrabbers.Add(device);

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            if (device is Radar radar && _radars.Remove(radar))
            {

                return true;
            }

            if (device.DeviceRole == DeviceRole.Framegrabber && _framegrabbers.Remove(device))
            {

                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _radars.Clear();
            _framegrabbers.Clear();
        }



    }
}
