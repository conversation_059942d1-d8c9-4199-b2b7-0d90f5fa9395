using Barret.Core.Areas.Devices.Models.GenericDevices;
using Barret.Core.Areas.Devices.Models.Radios;

namespace Barret.Core.Areas.DeviceGroups.RadioGroup
{
    /// <summary>
    /// Device group for radios
    /// </summary>
    public class RadioGroup : IDeviceGroup
    {
        private readonly List<VHFMariphone> _vhfMariphones = [];
        private readonly List<VHFNetworkInterface> _vhfNetworkInterfaces = [];

        /// <summary>
        /// Creates a new instance of RadioGroup
        /// </summary>
        public RadioGroup()
        {
            Type = DeviceGroups.RadioGroup;
        }

        /// <summary>
        /// Gets the type of the device group
        /// </summary>
        public DeviceGroups Type { get; }

        /// <summary>
        /// Gets a read-only list of devices in this group
        /// </summary>
        public IReadOnlyList<GenericDevice> Devices =>
            _vhfMariphones.Cast<GenericDevice>()
                .Concat(_vhfNetworkInterfaces)
                .ToList()
                .AsReadOnly();

        /// <summary>
        /// Gets a read-only list of VHF mariphones in this group
        /// </summary>
        public IReadOnlyList<VHFMariphone> VHFMariphones => _vhfMariphones.AsReadOnly();

        /// <summary>
        /// Gets a read-only list of VHF network interfaces in this group
        /// </summary>
        public IReadOnlyList<VHFNetworkInterface> VHFNetworkInterfaces => _vhfNetworkInterfaces.AsReadOnly();

        /// <summary>
        /// Determines if the group can accept a device with the specified role
        /// </summary>
        /// <param name="device">The device to check</param>
        /// <returns>True if the device can be added to this group; otherwise, false</returns>
        public bool CanAcceptDevice(GenericDevice device)
        {
            if (device == null)
                return false;

            // Use both type checking and role validation for a hybrid approach
            return (device is VHFMariphone || device is VHFNetworkInterface) &&
                   Type.AllowsRole(device.DeviceRole);
        }

        /// <summary>
        /// Adds a device to this group if it has a compatible role
        /// </summary>
        /// <param name="device">The device to add</param>
        /// <returns>True if the device was added; otherwise, false</returns>
        public bool AddDevice(GenericDevice device)
        {
            if (!CanAcceptDevice(device))
                return false;

            if (device is VHFMariphone vhfMariphone)
            {
                _vhfMariphones.Add(vhfMariphone);
                return true;
            }

            if (device is VHFNetworkInterface vhfNetworkInterface)
            {
                _vhfNetworkInterfaces.Add(vhfNetworkInterface);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes a device from this group
        /// </summary>
        /// <param name="device">The device to remove</param>
        /// <returns>True if the device was removed; otherwise, false</returns>
        public bool RemoveDevice(GenericDevice device)
        {
            if (device is VHFMariphone vhfMariphone && _vhfMariphones.Remove(vhfMariphone))
            {
                return true;
            }

            if (device is VHFNetworkInterface vhfNetworkInterface && _vhfNetworkInterfaces.Remove(vhfNetworkInterface))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Removes all devices from this group
        /// </summary>
        public void Clear()
        {
            _vhfMariphones.Clear();
            _vhfNetworkInterfaces.Clear();
        }
    }
}
