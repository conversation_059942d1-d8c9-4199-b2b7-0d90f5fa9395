using System;

namespace Barret.Shared.DTOs.RembrandtConfig.Application
{
    /// <summary>
    /// Represents an application configuration for Rembrandt
    /// </summary>
    public class ApplicationConfigDto
    {
        /// <summary>
        /// Gets or sets whether to allow QoS simulation operations
        /// </summary>
        public bool AllowQosSimOperations { get; set; } = false;

        /// <summary>
        /// Gets or sets the Streamedian proxy port
        /// </summary>
        public int StreamedianProxyPort { get; set; } = 8188;
    }
}
