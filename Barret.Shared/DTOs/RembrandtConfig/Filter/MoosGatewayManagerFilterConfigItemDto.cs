using System;

namespace Barret.Shared.DTOs.RembrandtConfig.Filter
{
    /// <summary>
    /// Represents a Moos gateway manager filter configuration item
    /// </summary>
    public class MoosGatewayManagerFilterConfigItemDto
    {
        /// <summary>
        /// Gets or sets the identification header
        /// </summary>
        public string identificationHeader { get; set; } = "*";

        /// <summary>
        /// Gets or sets the destination type
        /// </summary>
        public string destinationType { get; set; } = "Scc";

        /// <summary>
        /// Gets or sets the destination ID
        /// </summary>
        public string destinationId { get; set; } = "*";

        /// <summary>
        /// Gets or sets the update interval in milliseconds
        /// </summary>
        public string updateIntervalMillis { get; set; } = "0";

        /// <summary>
        /// Gets or sets whether to filter on change
        /// </summary>
        public bool? filterOnChange { get; set; } = null;

        /// <summary>
        /// Gets or sets the last heartbeat activity required in seconds
        /// </summary>
        public int? LastHeartbeatActivityRequiredSec { get; set; } = null;
    }
}
