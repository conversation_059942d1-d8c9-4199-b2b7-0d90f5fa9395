using Barret.Core.Areas.DeviceGroups;
using Barret.Shared.DTOs.Devices;

namespace Barret.Shared.DTOs.Vehicles
{
    /// <summary>
    /// Abstract base data transfer object for all vehicle types
    /// </summary>
    public abstract class VehicleDto
    {
        /// <summary>
        /// Gets or sets the unique identifier of the vehicle.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the vehicle identifier (business key).
        /// </summary>
        public string VehicleId { get; set; }

        /// <summary>
        /// Gets or sets the dimensions of the vehicle.
        /// </summary>
        public DimensionsDto? Dimensions { get; set; }

        /// <summary>
        /// Gets or sets the dictionary of device groups in this vehicle.
        /// The key is the device group enum value, and the value is the device group DTO.
        /// </summary>
        public Dictionary<DeviceGroups, DeviceGroupDto> DeviceGroups { get; set; } = [];

        /// <summary>
        /// Gets or sets the device connections in this vehicle.
        /// </summary>
        public List<DeviceConnectionDto> DeviceConnections { get; set; } = [];

        /// <summary>
        /// Gets or sets the cached device count for performance optimization.
        /// This is used when we want to display device count without loading all devices.
        /// </summary>
        public int? CachedDeviceCount { get; set; }

        /// <summary>
        /// Initializes a new instance of the <see cref="VehicleDto"/> class.
        /// </summary>
        protected VehicleDto()
        {
            Id = Guid.NewGuid();
            VehicleId = string.Empty;
            Dimensions = null;
            DeviceGroups = [];
            DeviceConnections = [];
            CachedDeviceCount = null;
        }

        /// <summary>
        /// Gets a device group by type, creating it if it doesn't exist.
        /// This is a simple utility method, not business logic.
        /// </summary>
        /// <param name="groupType">The type of the device group.</param>
        /// <returns>The device group DTO.</returns>
        public DeviceGroupDto GetDeviceGroup(DeviceGroups groupType)
        {
            if (DeviceGroups.TryGetValue(groupType, out var group))
            {
                return group;
            }

            // If the device group doesn't exist, create it
            var newGroup = new DeviceGroupDto
            {
                Type = groupType,
                Devices = [],
                AllowedRoles = groupType.GetAllowedRoles().ToList()
            };

            DeviceGroups[groupType] = newGroup;
            return newGroup;
        }



        /// <summary>
        /// Gets devices by group type.
        /// This is a simple data filtering method, not business logic.
        /// </summary>
        /// <param name="groupType">The type of the device group.</param>
        /// <returns>A list of devices in the specified group.</returns>
        public List<DeviceDto> GetDevicesByGroup(DeviceGroups groupType)
        {
            return DeviceGroups.TryGetValue(groupType, out var group)
                ? group.Devices ?? []
                : [];
        }



        /// <summary>
        /// Gets all device group names.
        /// This is a simple data extraction method, not business logic.
        /// </summary>
        /// <returns>A list of all device group names.</returns>
        public List<string> GetAllDeviceGroupNames()
        {
            return DeviceGroups.Keys.Select(k => k.ToString()).ToList();
        }

        /// <summary>
        /// Gets all devices from all device groups.
        /// This is a simple data extraction method, not business logic.
        /// </summary>
        /// <returns>A list of all devices in this vehicle.</returns>
        public List<DeviceDto> GetAllDevices()
        {
            return DeviceGroups.Values
                .Where(g => g.Devices != null)
                .SelectMany(g => g.Devices)
                .ToList();
        }
    }
}