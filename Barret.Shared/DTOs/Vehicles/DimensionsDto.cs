namespace Barret.Shared.DTOs.Vehicles
{
    /// <summary>
    /// Data transfer object for vessel dimensions
    /// </summary>
    public class DimensionsDto
    {
        public double DistanceGpsToFront { get; set; }
        public double DistanceGpsToBack { get; set; }
        public double DistanceGpsToLeft { get; set; }
        public double DistanceGpsToRight { get; set; }

        public double Width => DistanceGpsToLeft + DistanceGpsToRight;
        public double Length => DistanceGpsToFront + DistanceGpsToBack;

        public DimensionsDto()
        {
        }

        public DimensionsDto(double front, double back, double left, double right)
        {
            DistanceGpsToFront = front;
            DistanceGpsToBack = back;
            DistanceGpsToLeft = left;
            DistanceGpsToRight = right;
        }
    }
}