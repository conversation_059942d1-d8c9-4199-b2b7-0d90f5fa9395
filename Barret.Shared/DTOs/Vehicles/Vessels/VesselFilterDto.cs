using Barret.Core.Areas.Devices.Enums;

namespace Barret.Shared.DTOs.Vehicles.Vessels
{
    /// <summary>
    /// Data transfer object for vessel filtering.
    /// </summary>
    public class VesselFilterDto
    {
        /// <summary>
        /// Gets or sets the name filter.
        /// </summary>
        public string? Name { get; set; }
        
        /// <summary>
        /// Gets or sets the MMSI filter.
        /// </summary>
        public string? MMSI { get; set; }
        
        /// <summary>
        /// Gets or sets the ENI filter.
        /// </summary>
        public string? ENI { get; set; }
        
        /// <summary>
        /// Gets or sets the device role filter.
        /// </summary>
        public DeviceRole? DeviceRole { get; set; }
        
        /// <summary>
        /// Gets or sets the maximum number of results to return.
        /// </summary>
        public int? MaxResults { get; set; }
    }
}
