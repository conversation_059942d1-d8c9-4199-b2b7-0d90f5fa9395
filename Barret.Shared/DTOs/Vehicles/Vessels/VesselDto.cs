using Barret.Shared.DTOs.Devices;

namespace Barret.Shared.DTOs.Vehicles.Vessels
{
    /// <summary>
    /// Data transfer object for vessels
    /// </summary>
    public class VesselDto : VehicleDto
    {
        /// <summary>
        /// Gets or sets the name of the vessel.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the European Number of Identification (ENI) of the vessel.
        /// </summary>
        public string? ENI { get; set; }

        /// <summary>
        /// Gets or sets the Maritime Mobile Service Identity (MMSI) of the vessel.
        /// </summary>
        public string MMSI { get; set; } = string.Empty;

        /// <summary>
        /// Initializes a new instance of the <see cref="VesselDto"/> class.
        /// </summary>
        public VesselDto() : base()
        {
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="VesselDto"/> class with the specified parameters.
        /// </summary>
        /// <param name="vehicleId">The vehicle identifier.</param>
        /// <param name="mmsi">The MMSI.</param>
        /// <param name="dimensions">The dimensions.</param>
        /// <param name="eni">The ENI.</param>
        /// <param name="name">The name.</param>
        public VesselDto(string vehicleId, string mmsi, DimensionsDto? dimensions = null, string? eni = null, string? name = null)
            : base()
        {
            VehicleId = vehicleId;
            ENI = eni;
            MMSI = mmsi;
            Dimensions = dimensions;
            Name = name ?? string.Empty;
        }
    }
}