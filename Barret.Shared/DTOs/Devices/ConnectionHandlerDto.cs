using Barret.Core.Areas.Devices.Enums;

namespace Barret.Shared.DTOs.Devices
{
    /// <summary>
    /// Data Transfer Object for ConnectionHandler.
    /// </summary>
    public class ConnectionHandlerDto
    {
        /// <summary>
        /// Gets or sets the protocol used for communication with the device.
        /// </summary>
        public Protocol Protocol { get; set; } = Protocol.TcpClient;

        /// <summary>
        /// Gets or sets the IP address of the device.
        /// </summary>
        public string? IPAddress { get; set; } = "*************";

        /// <summary>
        /// Gets or sets the port number for communication with the device.
        /// </summary>
        public int Port { get; set; } = 8080;
    }
}