using Barret.Core.Areas.Devices.Enums;

namespace Barret.Shared.DTOs.Devices
{
    /// <summary>
    /// Data transfer object for device model information.
    /// </summary>
    public class DeviceModelInfo
    {
        /// <summary>
        /// Gets or sets the unique identifier of the device model.
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Gets or sets the name of the device model.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the role of the device model.
        /// </summary>
        public DeviceRole DeviceRole { get; set; }

        /// <summary>
        /// Gets or sets the ID of the manufacturer that produces this device model.
        /// </summary>
        public Guid ManufacturerId { get; set; }

        /// <summary>
        /// Gets or sets the name of the manufacturer that produces this device model.
        /// </summary>
        public string ManufacturerName { get; set; } = string.Empty;
    }
}
