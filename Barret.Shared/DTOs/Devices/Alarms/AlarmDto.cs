using Barret.Core.Areas.Devices.Enums;

namespace Barret.Shared.DTOs.Devices.Alarms
{
    /// <summary>
    /// Data transfer object for alarms
    /// </summary>
    public class AlarmDto
    {
        /// <summary>
        /// Unique identifier for the alarm
        /// </summary>
        public Guid Id { get; set; }

        /// <summary>
        /// Description of the alarm
        /// </summary>
        public string Description { get; set; } = string.Empty;

        /// <summary>
        /// Type of notification for the alarm
        /// </summary>
        public NotificationType NotificationType { get; set; } = NotificationType.Undefined;

        /// <summary>
        /// Alarm message for <PERSON><PERSON> integration
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Notification group identifier for the alarm
        /// </summary>
        public NotificationGroupId NotificationGroupId { get; set; } = NotificationGroupId.Undefined;

        /// <summary>
        /// Entity identifier associated with this alarm
        /// </summary>
        public string EntityId { get; set; } = string.Empty;

        /// <summary>
        /// Warning identifier for this alarm (corresponds to DomainDriverId in Excel)
        /// </summary>
        public WarningId WarningId { get; set; } = WarningId.Undefined;
    }
}