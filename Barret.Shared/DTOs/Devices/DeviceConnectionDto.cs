using Barret.Core.Areas.Devices.Enums;
using System;

namespace Barret.Shared.DTOs.Devices
{
    /// <summary>
    /// Data Transfer Object for DeviceConnection.
    /// </summary>
    public class DeviceConnectionDto
    {
        /// <summary>
        /// Gets or sets the ID of the connected device.
        /// </summary>
        public Guid ConnectedDeviceId { get; set; }

        /// <summary>
        /// Gets or sets the ID of the interface device.
        /// </summary>
        public Guid InterfaceDeviceId { get; set; }

        /// <summary>
        /// Gets or sets the type of connection.
        /// </summary>
        public ConnectionType Type { get; set; } = ConnectionType.Undefined;

        /// <summary>
        /// Gets or sets the direction of data flow.
        /// </summary>
        public ConnectionDirection Direction { get; set; } = ConnectionDirection.Undefined;
    }
}
