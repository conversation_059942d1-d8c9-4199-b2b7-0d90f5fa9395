using Barret.Core.Areas.Devices.Enums;

namespace Barret.Shared.DTOs.Devices
{
    /// <summary>
    /// Data transfer object for maritime device position
    /// </summary>
    public class MaritimePositionDto
    {
        /// <summary>
        /// Gets or sets the fore-aft position (<PERSON>, <PERSON>, <PERSON>)
        /// </summary>
        public ForeAftPosition ForeAft { get; set; } = ForeAftPosition.None;

        /// <summary>
        /// Gets or sets the lateral position (None, Port, Starboard, Mast, Centerline)
        /// </summary>
        public LateralPosition Lateral { get; set; } = LateralPosition.None;

        /// <summary>
        /// Gets or sets the optional facing direction
        /// </summary>
        public FacingDirection? Facing { get; set; }

        /// <summary>
        /// Gets a value indicating whether this position is defined (has any non-None values)
        /// </summary>
        public bool IsDefined => ForeAft != ForeAftPosition.None || Lateral != LateralPosition.None || Facing.HasValue;

        /// <summary>
        /// Returns a user-friendly display string for UI
        /// </summary>
        public string ToDisplayString()
        {
            if (!IsDefined)
            {
                return "Not specified";
            }

            var parts = new List<string>();

            if (ForeAft != ForeAftPosition.None)
            {
                parts.Add(ForeAft.ToString());
            }

            if (Lateral != LateralPosition.None)
            {
                parts.Add(Lateral.ToString());
            }

            if (Facing.HasValue && Facing != FacingDirection.None)
            {
                parts.Add($"(facing {Facing})");
            }

            return parts.Count > 0 ? string.Join(" ", parts) : "Not specified";
        }
    }
}
