using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups;

namespace Barret.Shared.DTOs.Devices
{
    /// <summary>
    /// DTO for device groups - pure data contract with no UI concerns
    /// Matches the IDeviceGroup entity structure
    /// </summary>
    public class DeviceGroupDto
    {
        /// <summary>
        /// Gets or sets the type of the device group
        /// This corresponds to the Type property in IDeviceGroup
        /// </summary>
        public DeviceGroups Type { get; set; }

        /// <summary>
        /// Gets or sets the devices in this group
        /// </summary>
        public List<DeviceDto> Devices { get; set; } = [];

        /// <summary>
        /// Gets or sets the device roles that are allowed in this group
        /// </summary>
        public List<DeviceRole> AllowedRoles { get; set; } = [];
    }
}
