using System;

namespace Barret.Shared.DTOs.Import
{
    /// <summary>
    /// Represents a device record from a CSV import file.
    /// </summary>
    public class CsvDeviceRecord
    {
        /// <summary>
        /// Gets or sets the name of the device.
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the type of the device (maps to DeviceRole).
        /// </summary>
        public string DeviceType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the manufacturer name.
        /// </summary>
        public string Manufacturer { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the model name.
        /// </summary>
        public string Model { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the IP address of the device.
        /// </summary>
        public string IPAddress { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the port number for the device connection.
        /// </summary>
        public string Port { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the protocol used for communication.
        /// </summary>
        public string Protocol { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the device group this device belongs to.
        /// </summary>
        public string DeviceGroup { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets any additional notes or comments about the device.
        /// </summary>
        public string Notes { get; set; } = string.Empty;
    }
}
