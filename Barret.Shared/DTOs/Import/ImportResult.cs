using System;
using System.Collections.Generic;

namespace Barret.Shared.DTOs.Import
{
    /// <summary>
    /// Represents the result of an import operation.
    /// </summary>
    public class ImportResult
    {
        /// <summary>
        /// Gets or sets a value indicating whether the import was successful.
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Gets or sets a summary message about the import operation.
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets a list of error messages that occurred during import.
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// Gets or sets the total number of records processed.
        /// </summary>
        public int TotalRecords { get; set; }

        /// <summary>
        /// Gets or sets the number of records successfully imported.
        /// </summary>
        public int SuccessfulRecords { get; set; }

        /// <summary>
        /// Gets or sets the number of records that failed to import.
        /// </summary>
        public int FailedRecords { get; set; }

        /// <summary>
        /// Gets or sets the ID of the vessel that was created or updated.
        /// </summary>
        public Guid? VesselId { get; set; }

        /// <summary>
        /// Gets or sets a list of warnings that occurred during import.
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Creates a successful import result.
        /// </summary>
        /// <param name="message">The success message.</param>
        /// <param name="vesselId">The ID of the vessel that was created or updated.</param>
        /// <param name="totalRecords">The total number of records processed.</param>
        /// <param name="successfulRecords">The number of records successfully imported.</param>
        /// <returns>An ImportResult indicating success.</returns>
        public static ImportResult CreateSuccess(string message, Guid vesselId, int totalRecords, int successfulRecords)
        {
            return new ImportResult
            {
                Success = true,
                Message = message,
                VesselId = vesselId,
                TotalRecords = totalRecords,
                SuccessfulRecords = successfulRecords,
                FailedRecords = totalRecords - successfulRecords
            };
        }

        /// <summary>
        /// Creates a failed import result.
        /// </summary>
        /// <param name="message">The failure message.</param>
        /// <param name="errors">The list of errors that occurred.</param>
        /// <returns>An ImportResult indicating failure.</returns>
        public static ImportResult CreateFailure(string message, List<string> errors)
        {
            return new ImportResult
            {
                Success = false,
                Message = message,
                Errors = errors
            };
        }

        /// <summary>
        /// Creates a failed import result with a single error.
        /// </summary>
        /// <param name="message">The failure message.</param>
        /// <param name="error">The error that occurred.</param>
        /// <returns>An ImportResult indicating failure.</returns>
        public static ImportResult CreateFailure(string message, string error)
        {
            return new ImportResult
            {
                Success = false,
                Message = message,
                Errors = new List<string> { error }
            };
        }
    }

    /// <summary>
    /// Represents the result of an import operation with a specific data type.
    /// </summary>
    /// <typeparam name="T">The type of data returned by the import operation.</typeparam>
    public class ImportResult<T> : ImportResult
    {
        /// <summary>
        /// Gets or sets the data returned by the import operation.
        /// </summary>
        public T? Data { get; set; }

        /// <summary>
        /// Creates a successful import result with data.
        /// </summary>
        /// <param name="message">The success message.</param>
        /// <param name="data">The data returned by the import operation.</param>
        /// <param name="vesselId">The ID of the vessel that was created or updated.</param>
        /// <param name="totalRecords">The total number of records processed.</param>
        /// <param name="successfulRecords">The number of records successfully imported.</param>
        /// <returns>An ImportResult indicating success with the specified data.</returns>
        public static ImportResult<T> CreateSuccess(string message, T data, Guid vesselId, int totalRecords, int successfulRecords)
        {
            return new ImportResult<T>
            {
                Success = true,
                Message = message,
                Data = data,
                VesselId = vesselId,
                TotalRecords = totalRecords,
                SuccessfulRecords = successfulRecords,
                FailedRecords = totalRecords - successfulRecords
            };
        }

        /// <summary>
        /// Creates a failed import result.
        /// </summary>
        /// <param name="message">The failure message.</param>
        /// <param name="errors">The list of errors that occurred.</param>
        /// <returns>An ImportResult indicating failure.</returns>
        public new static ImportResult<T> CreateFailure(string message, List<string> errors)
        {
            return new ImportResult<T>
            {
                Success = false,
                Message = message,
                Errors = errors
            };
        }

        /// <summary>
        /// Creates a failed import result with a single error.
        /// </summary>
        /// <param name="message">The failure message.</param>
        /// <param name="error">The error that occurred.</param>
        /// <returns>An ImportResult indicating failure.</returns>
        public new static ImportResult<T> CreateFailure(string message, string error)
        {
            return new ImportResult<T>
            {
                Success = false,
                Message = message,
                Errors = new List<string> { error }
            };
        }
    }
}
