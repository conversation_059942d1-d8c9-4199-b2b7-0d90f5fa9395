using System;

namespace Barret.Shared.DTOs.DaVinciConfig.Gnss
{
    /// <summary>
    /// Represents a GNSS filter configuration item
    /// </summary>
    public class GnssFilterConfigItemDto
    {
        /// <summary>
        /// Gets or sets the identification header
        /// </summary>
        public string IdentificationHeader { get; set; } = "*";

        /// <summary>
        /// Gets or sets the destination type
        /// </summary>
        public string DestinationType { get; set; } = "Scc";

        /// <summary>
        /// Gets or sets the destination ID
        /// </summary>
        public string DestinationId { get; set; } = "*";

        /// <summary>
        /// Gets or sets the update interval in milliseconds
        /// </summary>
        public int UpdateIntervalMillis { get; set; } = 1000;

        /// <summary>
        /// Gets or sets whether to filter on change
        /// </summary>
        public bool FilterOnChange { get; set; } = false;
    }
}
