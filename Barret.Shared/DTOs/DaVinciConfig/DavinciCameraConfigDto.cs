using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig
{
    /// <summary>
    /// Data transfer object for Davinci-specific camera configuration
    /// </summary>
    public class DavinciCameraConfigDto : CameraConfigDto
    {
        /// <summary>
        /// Default constructor
        /// </summary>
        public DavinciCameraConfigDto() : base()
        {
        }

        /// <summary>
        /// Creates a new instance of DavinciCameraConfigDto with specified properties
        /// </summary>
        /// <param name="name">Name of the camera</param>
        /// <param name="ip">IP address of the camera</param>
        /// <param name="brand">Brand of the camera</param>
        /// <param name="technicalComponentId">Technical component ID of the camera</param>
        /// <param name="showVideo">Whether to show video for this camera</param>
        /// <param name="credentials">Credentials for camera authentication</param>
        public DavinciCameraConfigDto(
            string name,
            string ip,
            string brand,
            string technicalComponentId,
            bool showVideo,
            CameraApiCredentialsDto credentials)
            : base(name, ip, brand, technicalComponentId, showVideo, credentials)
        {
        }

        /// <summary>
        /// Creates a new DavinciCameraConfigDto from a base CameraConfigDto
        /// </summary>
        /// <param name="baseConfig">The base camera configuration</param>
        /// <returns>A new DavinciCameraConfigDto</returns>
        public static DavinciCameraConfigDto FromCameraConfigDto(
            CameraConfigDto baseConfig)
        {
            return new DavinciCameraConfigDto(
                baseConfig.Name,
                baseConfig.Ip,
                baseConfig.Brand,
                baseConfig.TechnicalComponentId,
                baseConfig.ShowVideo,
                baseConfig.Credentials);
        }
    }
}
