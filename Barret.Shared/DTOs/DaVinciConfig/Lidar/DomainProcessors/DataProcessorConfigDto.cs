using System;
using System.Collections.Generic;

namespace Barret.Shared.DTOs.DaVinciConfig.Lidar.DomainProcessors
{
    /// <summary>
    /// Represents a LIDAR data processor configuration
    /// </summary>
    public class DataProcessorConfigDto
    {
        /// <summary>
        /// Dictionary of scanner configurations keyed by scanner ID
        /// </summary>
        public Dictionary<string, ScannerConfigDto> Scanners { get; set; } = new Dictionary<string, ScannerConfigDto>();
    }

    /// <summary>
    /// Represents a scanner configuration
    /// </summary>
    public class ScannerConfigDto
    {
        /// <summary>
        /// Gets or sets the scanner X offset
        /// </summary>
        public int scanner_x_offset { get; set; }

        /// <summary>
        /// Gets or sets the scanner Y offset
        /// </summary>
        public int scanner_y_offset { get; set; }

        /// <summary>
        /// Gets or sets the middle offset
        /// </summary>
        public int MiddleOffset { get; set; }

        /// <summary>
        /// Gets or sets the right offset
        /// </summary>
        public int RightOffset { get; set; }

        /// <summary>
        /// Gets or sets the left offset
        /// </summary>
        public int LeftOffset { get; set; }

        /// <summary>
        /// Gets or sets whether to mirror
        /// </summary>
        public bool Mirror { get; set; }

        /// <summary>
        /// Gets or sets the layer angles to use
        /// </summary>
        public List<string> LayerAnglesToUse { get; set; } = new List<string>();
    }
}
