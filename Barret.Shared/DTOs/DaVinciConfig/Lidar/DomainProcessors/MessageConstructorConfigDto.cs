using System;

namespace Barret.Shared.DTOs.DaVinciConfig.Lidar.DomainProcessors
{
    /// <summary>
    /// Represents a LIDAR message constructor configuration
    /// </summary>
    public class MessageConstructorConfigDto
    {
        /// <summary>
        /// Gets or sets the LIDAR data timeout in milliseconds
        /// </summary>
        public int lidarDataTimeout_ms { get; set; } = 1000;

        /// <summary>
        /// Gets or sets the alarm distance in millimeters
        /// </summary>
        public int alarmDistance_mm { get; set; } = 1000;
    }
}
