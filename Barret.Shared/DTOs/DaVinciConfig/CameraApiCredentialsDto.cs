using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig
{
    /// <summary>
    /// Data transfer object for camera API credentials in DaVinci configuration
    /// </summary>
    public class CameraApiCredentialsDto
    {
        /// <summary>
        /// Gets or sets the username for camera authentication
        /// </summary>
        [JsonPropertyName("UserName")]
        public string UserName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the password for camera authentication
        /// </summary>
        [JsonPropertyName("Password")]
        public string Password { get; set; } = string.Empty;

        /// <summary>
        /// Default constructor
        /// </summary>
        public CameraApiCredentialsDto()
        {
        }

        /// <summary>
        /// Creates a new instance of CameraApiCredentialsDto with specified credentials
        /// </summary>
        /// <param name="userName">Username for camera authentication</param>
        /// <param name="password">Password for camera authentication</param>
        public CameraApiCredentialsDto(string userName, string password)
        {
            UserName = userName;
            Password = password;
        }
    }
}
