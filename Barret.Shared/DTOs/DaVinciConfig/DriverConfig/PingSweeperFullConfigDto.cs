using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for a full PingSweeper driver configuration
    /// </summary>
    public class PingSweeperFullConfigDto
    {
        /// <summary>
        /// Gets or sets the driver configuration
        /// </summary>
        [JsonPropertyName("DriverConfig")]
        public PingSweeperConfigDto DriverConfig { get; set; } = new PingSweeperConfigDto();
        
        /// <summary>
        /// Gets or sets the pipelines
        /// </summary>
        [JsonPropertyName("Pipelines")]
        public List<string> Pipelines { get; set; } = new List<string>();
    }
}
