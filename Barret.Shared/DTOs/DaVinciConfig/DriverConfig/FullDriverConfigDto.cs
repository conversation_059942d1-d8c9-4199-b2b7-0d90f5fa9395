using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for a full driver configuration including driver config, connection handler config, and pipelines
    /// </summary>
    public class FullDriverConfigDto
    {
        /// <summary>
        /// Gets or sets the driver configuration
        /// </summary>
        [JsonPropertyName("DriverConfig")]
        public DriverConfigDto DriverConfig { get; set; } = new DriverConfigDto();
        
        /// <summary>
        /// Gets or sets the connection handler configuration
        /// </summary>
        [JsonPropertyName("ConnectionHandlerConfig")]
        public ConnectionHandlerConfigDto ConnectionHandlerConfig { get; set; } = new ConnectionHandlerConfigDto();
        
        /// <summary>
        /// Gets or sets the pipelines
        /// </summary>
        [JsonPropertyName("Pipelines")]
        public List<string> Pipelines { get; set; } = new List<string>();
    }
}
