using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for Radar driver configuration
    /// </summary>
    public class RadarDriverConfigDto : DriverConfigDto
    {
        /// <summary>
        /// Gets or sets the radar location
        /// </summary>
        [JsonPropertyName("RadarLocation")]
        public string RadarLocation { get; set; } = "Bow";
    }
}
