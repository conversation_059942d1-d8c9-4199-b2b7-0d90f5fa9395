using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for PingSweeper driver configuration
    /// </summary>
    public class PingSweeperConfigDto
    {
        /// <summary>
        /// Gets or sets the driver identification
        /// </summary>
        [JsonPropertyName("DriverIdentification")]
        public DriverIdentificationDto DriverIdentification { get; set; } = new DriverIdentificationDto();

        /// <summary>
        /// Gets or sets the ping destinations
        /// </summary>
        [JsonPropertyName("PingDestinations")]
        public List<PingDestinationDto> PingDestinations { get; set; } = new List<PingDestinationDto>();

        /// <summary>
        /// Gets or sets the ping settings
        /// </summary>
        [JsonPropertyName("PingSettings")]
        public PingSettingsDto PingSettings { get; set; } = new PingSettingsDto();
    }
}
