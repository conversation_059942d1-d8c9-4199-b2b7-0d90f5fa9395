using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for driver identification
    /// </summary>
    public class DriverIdentificationDto
    {
        /// <summary>
        /// Gets or sets the name of the driver
        /// </summary>
        [JsonPropertyName("name")]
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the technical component ID of the driver
        /// </summary>
        [Json<PERSON>ropertyName("technicalComponentId")]
        public string TechnicalComponentId { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the driver ID
        /// </summary>
        [JsonPropertyName("driverId")]
        public string DriverId { get; set; } = string.Empty;
    }
}
