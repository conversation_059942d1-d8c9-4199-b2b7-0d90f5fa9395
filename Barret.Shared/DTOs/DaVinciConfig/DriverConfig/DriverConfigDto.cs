using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for driver configuration
    /// </summary>
    public class DriverConfigDto
    {
        /// <summary>
        /// Gets or sets the driver identification
        /// </summary>
        [JsonPropertyName("DriverIdentification")]
        public DriverIdentificationDto DriverIdentification { get; set; } = new DriverIdentificationDto();
        
        /// <summary>
        /// Gets or sets the reboot interval on fail in milliseconds
        /// </summary>
        [JsonPropertyName("rebootIntervalOnFail_ms")]
        public int RebootIntervalOnFailMs { get; set; } = 1000;
        
        /// <summary>
        /// Gets or sets the heartbeat interval in milliseconds
        /// </summary>
        [JsonPropertyName("heartbeatInterval_ms")]
        public int HeartbeatIntervalMs { get; set; } = 5000;
    }
}
