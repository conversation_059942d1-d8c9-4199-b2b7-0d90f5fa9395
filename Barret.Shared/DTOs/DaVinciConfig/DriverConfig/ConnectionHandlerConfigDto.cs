using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for connection handler configuration
    /// </summary>
    public class ConnectionHandlerConfigDto
    {
        /// <summary>
        /// Gets or sets the protocol
        /// </summary>
        [JsonPropertyName("protocol")]
        public string Protocol { get; set; } = "tcpclient";
        
        /// <summary>
        /// Gets or sets the connection address
        /// </summary>
        [Json<PERSON>ropertyName("connectionAddress")]
        public string ConnectionAddress { get; set; } = "127.0.0.1";
        
        /// <summary>
        /// Gets or sets the connection address option (e.g., port number)
        /// </summary>
        [Json<PERSON>ropertyName("connectionAddressOption")]
        public int ConnectionAddressOption { get; set; } = 9100;
    }
}
