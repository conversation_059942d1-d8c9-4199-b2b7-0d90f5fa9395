using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for the complete driver configuration container
    /// </summary>
    public class DriverConfigContainerDto
    {
        /// <summary>
        /// Gets or sets the GenericNmea0183 driver configurations
        /// </summary>
        [JsonPropertyName("GenericNmea0183")]
        public List<FullDriverConfigDto> GenericNmea0183 { get; set; } = new List<FullDriverConfigDto>();

        /// <summary>
        /// Gets or sets the SickLidar driver configurations
        /// </summary>
        [JsonPropertyName("SickLidar")]
        public List<FullDriverConfigDto> SickLidar { get; set; } = new List<FullDriverConfigDto>();

        /// <summary>
        /// Gets or sets the EmtrackA100 driver configurations
        /// </summary>
        [JsonPropertyName("EmtrackA100")]
        public List<FullDriverConfigDto> EmtrackA100 { get; set; } = new List<FullDriverConfigDto>();

        /// <summary>
        /// Gets or sets the Can driver configurations
        /// </summary>
        [JsonPropertyName("Can")]
        public List<FullDriverConfigDto> Can { get; set; } = new List<FullDriverConfigDto>();

        /// <summary>
        /// Gets or sets the PingSweeper driver configurations
        /// </summary>
        [JsonPropertyName("PingSweeper")]
        public List<PingSweeperFullConfigDto> PingSweeper { get; set; } = new List<PingSweeperFullConfigDto>();

        /// <summary>
        /// Gets or sets the Radar driver configurations
        /// </summary>
        [JsonPropertyName("Radar")]
        public List<FullDriverConfigDto> Radar { get; set; } = new List<FullDriverConfigDto>();

        /// <summary>
        /// Gets or sets the XSens driver configurations
        /// </summary>
        [JsonPropertyName("XSens")]
        public List<FullDriverConfigDto> XSens { get; set; } = new List<FullDriverConfigDto>();

        /// <summary>
        /// Gets or sets the Plc driver configurations
        /// </summary>
        [JsonPropertyName("Plc")]
        public List<FullDriverConfigDto> Plc { get; set; } = new List<FullDriverConfigDto>();
    }
}
