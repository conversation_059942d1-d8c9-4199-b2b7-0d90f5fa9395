using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for ping settings in PingSweeper
    /// </summary>
    public class PingSettingsDto
    {
        /// <summary>
        /// Gets or sets the ping interval in milliseconds
        /// </summary>
        [JsonPropertyName("PingInterval_ms")]
        public int PingIntervalMs { get; set; } = 5000;
        
        /// <summary>
        /// Gets or sets the ping timeout in milliseconds
        /// </summary>
        [JsonPropertyName("PingTimeout_ms")]
        public int PingTimeoutMs { get; set; } = 1000;
    }
}
