using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for EmtrackA100 (AIS) driver configuration
    /// </summary>
    public class EmtrackA100ConfigDto : DriverConfigDto
    {
        /// <summary>
        /// Gets or sets whether sending messages to the device is allowed
        /// </summary>
        [JsonPropertyName("allowSendingMessagesToDevice")]
        public bool AllowSendingMessagesToDevice { get; set; } = false;
    }
}
