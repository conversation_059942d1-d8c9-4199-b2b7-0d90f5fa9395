using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.DriverConfig
{
    /// <summary>
    /// DTO for ping destination in PingSweeper
    /// </summary>
    public class PingDestinationDto
    {
        /// <summary>
        /// Gets or sets the name of the ping destination
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; } = string.Empty;
        
        /// <summary>
        /// Gets or sets the IP address of the ping destination
        /// </summary>
        [JsonPropertyName("Ip")]
        public string Ip { get; set; } = string.Empty;
        
        /// <summary>
        /// Gets or sets the technical component ID of the ping destination
        /// </summary>
        [JsonPropertyName("TechnicalComponentId")]
        public string TechnicalComponentId { get; set; } = string.Empty;
    }
}
