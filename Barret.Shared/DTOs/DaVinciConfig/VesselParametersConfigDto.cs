using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig
{
    /// <summary>
    /// DTO for DaVinci vessel parameters configuration
    /// </summary>
    public class VesselParametersConfigDto
    {
        /// <summary>
        /// Gets or sets the vessel MMSI (Maritime Mobile Service Identity)
        /// </summary>
        [JsonPropertyName("VesselMmsi")]
        public string VesselMmsi { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the vessel name
        /// </summary>
        [JsonPropertyName("VesselName")]
        public string VesselName { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the Seq API key for logging
        /// </summary>
        [JsonPropertyName("SeqApiKey")]
        public string SeqApiKey { get; set; } = string.Empty;
    }
}
