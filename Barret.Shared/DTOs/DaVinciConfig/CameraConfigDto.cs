using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig
{
    /// <summary>
    /// Data transfer object for camera configuration in DaVinci
    /// </summary>
    public class CameraConfigDto
    {
        /// <summary>
        /// Gets or sets the name of the camera
        /// </summary>
        [JsonPropertyName("Name")]
        public string Name { get; set; } = "undefined";

        /// <summary>
        /// Gets or sets the IP address of the camera
        /// </summary>
        [JsonPropertyName("Ip")]
        public string Ip { get; set; } = "undefined";

        /// <summary>
        /// Gets or sets the brand of the camera
        /// </summary>
        [JsonPropertyName("Brand")]
        public string Brand { get; set; } = "Axis";

        /// <summary>
        /// Gets or sets the technical component ID of the camera
        /// </summary>
        [JsonPropertyName("TechnicalComponentId")]
        public string TechnicalComponentId { get; set; } = "";

        /// <summary>
        /// Gets or sets whether to show video for this camera
        /// </summary>
        [JsonPropertyName("ShowVideo")]
        [JsonInclude] // Explicitly include this property in serialization
        public bool ShowVideo { get; set; } = false; // Initialize with default value

        /// <summary>
        /// Gets or sets the credentials for camera authentication
        /// </summary>
        [JsonPropertyName("Credentials")]
        public CameraApiCredentialsDto Credentials { get; set; } = new CameraApiCredentialsDto();

        /// <summary>
        /// Default constructor
        /// </summary>
        public CameraConfigDto()
        {
        }

        /// <summary>
        /// Creates a new instance of CameraConfigDto with specified properties
        /// </summary>
        /// <param name="name">Name of the camera</param>
        /// <param name="ip">IP address of the camera</param>
        /// <param name="brand">Brand of the camera</param>
        /// <param name="technicalComponentId">Technical component ID of the camera</param>
        /// <param name="showVideo">Whether to show video for this camera</param>
        /// <param name="credentials">Credentials for camera authentication</param>
        public CameraConfigDto(
            string name,
            string ip,
            string brand,
            string technicalComponentId,
            bool showVideo,
            CameraApiCredentialsDto credentials)
        {
            Name = name;
            Ip = ip;
            Brand = brand;
            TechnicalComponentId = technicalComponentId;
            ShowVideo = showVideo;
            Credentials = credentials;
        }
    }
}
