using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig.AisFilter
{
    /// <summary>
    /// DTO for an individual AIS filter configuration item
    /// </summary>
    public class AisFilterConfigItemDto
    {
        /// <summary>
        /// Gets or sets the identification header
        /// </summary>
        [Json<PERSON>ropertyName("IdentificationHeader")]
        public string IdentificationHeader { get; set; } = "VDM";

        /// <summary>
        /// Gets or sets the destination type
        /// </summary>
        [JsonPropertyName("DestinationType")]
        public string DestinationType { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the destination ID
        /// </summary>
        [Json<PERSON>ropertyName("DestinationId")]
        public string DestinationId { get; set; } = "*";

        /// <summary>
        /// Gets or sets the update interval in milliseconds
        /// </summary>
        [JsonPropertyName("UpdateIntervalMillis")]
        public int UpdateIntervalMillis { get; set; } = 0;

        /// <summary>
        /// Gets or sets the last heartbeat activity required in seconds
        /// This property is optional and may not be present in all configurations
        /// </summary>
        [JsonPropertyName("LastHeartbeatActivityRequiredSec")]
        public int? LastHeartbeatActivityRequiredSec { get; set; }
    }
}
