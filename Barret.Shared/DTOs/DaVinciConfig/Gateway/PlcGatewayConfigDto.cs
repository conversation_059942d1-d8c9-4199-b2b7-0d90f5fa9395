using System;

namespace Barret.Shared.DTOs.DaVinciConfig.Gateway
{
    /// <summary>
    /// Represents a PLC gateway configuration
    /// </summary>
    public class PlcGatewayConfigDto
    {
        /// <summary>
        /// Gets or sets the technical component ID
        /// </summary>
        public string TechnicalComponentId { get; set; } = "Amp Ipc";

        /// <summary>
        /// Gets or sets the name
        /// </summary>
        public string Name { get; set; } = "Amp";

        /// <summary>
        /// Gets or sets whether to pad message to constant length
        /// </summary>
        public bool PadMessageToConstantLength { get; set; } = true;

        /// <summary>
        /// Gets or sets whether it is <PERSON><PERSON>
        /// </summary>
        public bool IsMichelangelo { get; set; } = true;
    }
}
