using System;

namespace Barret.Shared.DTOs.DaVinciConfig.Gateway
{
    /// <summary>
    /// Represents an SCC gateway configuration
    /// </summary>
    public class SccGatewayConfigDto
    {
        /// <summary>
        /// Gets or sets the technical component ID
        /// </summary>
        public string TechnicalComponentId { get; set; } = "0000";

        /// <summary>
        /// Gets or sets the name
        /// </summary>
        public string Name { get; set; } = "Scc";

        /// <summary>
        /// Gets or sets whether to pad message to constant length
        /// </summary>
        public bool PadMessageToConstantLength { get; set; } = false;
    }
}
