using System;

namespace Barret.Shared.DTOs.DaVinciConfig.Gateway.Autonomy
{
    /// <summary>
    /// Represents an autonomy gateway configuration
    /// </summary>
    public class AutonomyGatewayConfigDto
    {
        /// <summary>
        /// Gets or sets the technical component ID
        /// </summary>
        public string TechnicalComponentId { get; set; } = "A0-PC23C2";

        /// <summary>
        /// Gets or sets the name
        /// </summary>
        public string Name { get; set; } = "Autonomy";

        /// <summary>
        /// Gets or sets whether to pad message to constant length
        /// </summary>
        public bool PadMessageToConstantLength { get; set; } = false;
    }
}
