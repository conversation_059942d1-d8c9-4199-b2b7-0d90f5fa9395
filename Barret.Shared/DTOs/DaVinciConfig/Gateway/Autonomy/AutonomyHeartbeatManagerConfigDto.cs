using System;

namespace Barret.Shared.DTOs.DaVinciConfig.Gateway.Autonomy
{
    /// <summary>
    /// Represents an autonomy heartbeat manager configuration
    /// </summary>
    public class AutonomyHeartbeatManagerConfigDto
    {
        /// <summary>
        /// Gets or sets the heartbeat send interval in milliseconds
        /// </summary>
        public int HeartbeatSendIntervalMillis { get; set; } = 1000;

        /// <summary>
        /// Gets or sets the heartbeat receive interval in milliseconds
        /// </summary>
        public int HeartbeatReceiveIntervalMillis { get; set; } = 1000;

        /// <summary>
        /// Gets or sets the allowed heartbeat misses
        /// </summary>
        public int AllowedHeartbeatMisses { get; set; } = 5;
    }
}
