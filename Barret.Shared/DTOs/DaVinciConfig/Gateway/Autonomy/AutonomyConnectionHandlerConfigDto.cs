using System;

namespace Barret.Shared.DTOs.DaVinciConfig.Gateway.Autonomy
{
    /// <summary>
    /// Represents an autonomy connection handler configuration
    /// </summary>
    public class AutonomyConnectionHandlerConfigDto
    {
        /// <summary>
        /// Gets or sets the protocol
        /// </summary>
        public string Protocol { get; set; } = "tcpserver";

        /// <summary>
        /// Gets or sets the connection address
        /// </summary>
        public string ConnectionAddress { get; set; } = "0.0.0.0";

        /// <summary>
        /// Gets or sets the connection address option (port)
        /// </summary>
        public int ConnectionAddressOption { get; set; } = 7331;
    }
}
