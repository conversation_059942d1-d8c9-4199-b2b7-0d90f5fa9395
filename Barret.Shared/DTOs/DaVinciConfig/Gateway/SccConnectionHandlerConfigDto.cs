using System;

namespace Barret.Shared.DTOs.DaVinciConfig.Gateway
{
    /// <summary>
    /// Represents an SCC connection handler configuration
    /// </summary>
    public class SccConnectionHandlerConfigDto
    {
        /// <summary>
        /// Gets or sets the protocol
        /// </summary>
        public string Protocol { get; set; } = "tcpclient";

        /// <summary>
        /// Gets or sets the connection address
        /// </summary>
        public string ConnectionAddress { get; set; } = "*************";

        /// <summary>
        /// Gets or sets the connection address option (port)
        /// </summary>
        public int ConnectionAddressOption { get; set; } = 2086;
    }
}
