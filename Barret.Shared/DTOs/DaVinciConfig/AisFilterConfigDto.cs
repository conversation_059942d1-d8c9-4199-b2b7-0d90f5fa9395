using System.Text.Json.Serialization;

namespace Barret.Shared.DTOs.DaVinciConfig
{
    /// <summary>
    /// Data transfer object for AIS filter configuration
    /// </summary>
    public class AisFilterConfigDto
    {
        /// <summary>
        /// Gets or sets the identification header
        /// </summary>
        [JsonPropertyName("IdentificationHeader")]
        public string IdentificationHeader { get; set; }

        /// <summary>
        /// Gets or sets the destination type
        /// </summary>
        [JsonPropertyName("DestinationType")]
        public string DestinationType { get; set; }

        /// <summary>
        /// Gets or sets the destination ID
        /// </summary>
        [JsonPropertyName("DestinationId")]
        public string DestinationId { get; set; }

        /// <summary>
        /// Gets or sets the update interval in milliseconds
        /// </summary>
        [JsonPropertyName("UpdateIntervalMillis")]
        public int UpdateIntervalMillis { get; set; }

        /// <summary>
        /// Gets or sets the last heartbeat activity required in seconds
        /// </summary>
        [JsonPropertyName("LastHeartbeatActivityRequiredSec")]
        public int? LastHeartbeatActivityRequiredSec { get; set; }
    }
}
