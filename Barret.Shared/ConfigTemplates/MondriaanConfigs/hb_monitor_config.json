[{"heartbeatName": "SF_HB_CONTROLLER", "timeoutMs": 100, "qosErrorName": "DaremiVehiclePropulsionControllerGeneral", "active": true}, {"heartbeatName": "SF_HB_THRUSTER_OFFSET_MANAGER", "timeoutMs": 1000, "qosErrorName": "DaremiVehiclePropulsionControllerGeneral", "active": true}, {"heartbeatName": "SF_HB_AIS_NODE_REPORT", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": true}, {"heartbeatName": "SF_HB_PROP_GPS_CALCULATOR", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": true}, {"heartbeatName": "SF_HB_SNAPSHOT_TAKER", "timeoutMs": 30000, "qosErrorName": "DaremiModuleHeartbeatLost", "qosSourceId": "Utilities", "active": true}, {"heartbeatName": "SF_HB_AIS_CV_RAYTRACING", "timeoutMs": 2000, "qosErrorName": "DaremiModuleHeartbeatLost", "qosSourceId": "Utilities", "active": true}, {"heartbeatName": "SF_HB_DEAD_RECKONING_MANAGER", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": true}, {"heartbeatName": "SF_HB_GEOFENCING", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": true}, {"heartbeatName": "SF_HB_GPS_QUALITY", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": false}, {"heartbeatName": "SF_HB_LOG_ROTATION", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": false}, {"heartbeatName": "SF_HB_END_OF_MISSION", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": false}, {"heartbeatName": "SF_HB_MISSION_CONFIG_UPDATER", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": false}, {"heartbeatName": "SF_HB_PERIODIC_VAR_POSTER", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": true}, {"heartbeatName": "SF_HB_SENSOR_MONITOR", "timeoutMs": 1500, "qosErrorName": "DaremiModuleHeartbeatLost", "active": true}, {"heartbeatName": "SF_HB_SENSOR_VALUE_MONITOR", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": true}, {"heartbeatName": "SF_HB_CONTROLLED_STOP_HANDLER", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": false}, {"heartbeatName": "SF_HB_SK_VITALS_MONITOR", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": false}, {"heartbeatName": "SF_HB_AIS_CV_FUSION", "timeoutMs": 3000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": false}, {"heartbeatName": "SF_HB_FUSION_CACHE", "timeoutMs": 1000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": true}, {"heartbeatName": "SF_HB_NEXT_WAYPOINT_VALIDATOR", "timeoutMs": 500, "qosErrorName": "DaremiModuleHeartbeatLost", "active": false}, {"heartbeatName": "SF_HB_PTZ_CONTROLLER_MOOS", "timeoutMs": 2000, "qosErrorName": "DaremiModuleHeartbeatLost", "qosSourceId": "Utilities", "active": true}, {"heartbeatName": "SF_HB_WAYPOINT_CONTROLLER", "timeoutMs": 2000, "qosErrorName": "DaremiModuleHeartbeatLost", "active": true}, {"heartbeatName": "SF_HB_AIS_CACHE", "timeoutMs": 3000, "qosErrorName": "DaremiModuleHeartbeatLost", "qosSourceId": "Utilities", "active": true}]