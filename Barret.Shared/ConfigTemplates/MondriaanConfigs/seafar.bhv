//---------------------------------------------------
// Helm Behavior file

initialize   DEPLOY  = false
initialize   RETURN  = false
initialize   STATION_KEEP = false
initialize   TRANSIT  = true
initialize   AVOID   = true

set MODE = ACTIVE {
  DEPLOY = true
} INACTIVE

//set MODE = STATION-KEEPING {
//  MODE = ACTIVE
//  STATION_KEEP = true
//}

set MODE = RETURNING {
  MODE = ACTIVE
  RETURN = true
}

set MODE = TRANSITING {
  MODE = ACTIVE
  TRANSIT = true
}


//----------------------------------------------
Behavior = BHV_Waypoint
{
  name      = transit
  pwt       = 100
  condition = MODE==TRANSITING
  updates   = WPT_UPDATE
  wpt_index_var = SF_NEXT_WPT_ID
  runflag   = VEHICLE_UNDERWAY = TRUE
  endflag   = VEHICLE_UNDERWAY = FALSE

  // Parameters specific to this behavior
  // ------------------------------------
   post_suffix = A

  points = 0,0
  speed = 1.5
  lead = 140
  //lead_damper = 20
  crs_spd_zaic_ratio=1


    capture_radius = 100                       // default
      capture_line = true                   // default
     lead_to_start = false                   // default
             order = normal                  // default
            repeat = forever                 // default
       slip_radius = 35                      // default

      visual_hints = vertex_size  = 3             // default
      visual_hints = edge_size    = 1             // default
      visual_hints = vertex_color = dodger_blue   // default
      visual_hints = edge_color   = blue         // default
      visual_hints = nextpt_color = yellow        // default
      visual_hints = nextpt_lcolor = aqua         // default
      visual_hints = nextpt_vertex_size = 5       // default
}


// Behavior = BHV_AvoidCollision
// {
//   // General Behavior Parameters
//   // ---------------------------
//   name         = avdcollision_                     // example
//   pwt          = 100                               // example
//   condition    = AVOID = true                      // example
//   updates      = CONTACT_INFO                      // example
//   endflag = CONTACT_RESOLVED = $[CONTACT] // example 
//   templating   = spawn                             // example
//   activeflag   = AVOIDING = active
//   inactiveflag = AVOIDING = inactive
//   verbose=true

//   // General Contact Behavior Parameters
//   // -----------------------------------
//       bearing_lines = white:0, green:0.65, yellow:0.8, red:1.0   // example

//   contact = to-be-set            // example
//   decay = 15,30            // default (seconds)
//   extrapolate = true             // default
//   on_no_contact_ok = false             // default
//   time_on_leg = 60               // default (seconds)

//   // Parameters specific to this behavior
//   // ------------------------------------
//   completed_dist = 1000              // default
//   max_util_cpa_dist = 50               // default
//   min_util_cpa_dist = 5               // default
//   no_alert_request = false            // default
//   pwt_grade = quasi            // default
//   pwt_inner_dist = 20               // default
//   pwt_outer_dist = 100              // default
//   use_refinery = true
// }


//----------------------------------------------
//Behavior = BHV_Waypoint
//{
//  name      = waypt_return
//  pwt       = 100
//  updates   = RETURN_UPDATES
//  condition = MODE==RETURNING
//  endflag   = STATION_KEEP = true
//  endflag   = AVOID = false
//      speed = 1.3
//     radius = 3.0
//  nm_radius = 15.0
//     points = 0,0
//     repeat = 10
//       lead = 8
//}

//----------------------------------------------
//Behavior = BHV_StationKeep
//{
//  name         = station-keep
//  pwt          = 100
//  condition    = MODE==STATION-KEEPING
//  inactiveflag = STATIONING = false
//  activeflag   = STATIONING = true

//     center_activate = true
//       inner_radius = 1000
//        outer_radius = 1000
//         outer_speed = 1.0

//       transit_speed = 1.3
//          swing_time = 7
//  hibernation_radius = 25
//        visual_hints = vertex_size=0, edge_color=gray50
//}
