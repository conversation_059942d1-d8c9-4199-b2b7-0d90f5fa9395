################################################################################
# Copyright (c) 2018-2020, NVIDIA CORPORATION. All rights reserved.
#
# Permission is hereby granted, free of charge, to any person obtaining a
# copy of this software and associated documentation files (the "Software"),
# to deal in the Software without restriction, including without limitation
# the rights to use, copy, modify, merge, publish, distribute, sublicense,
# and/or sell copies of the Software, and to permit persons to whom the
# Software is furnished to do so, subject to the following conditions:
#
# The above copyright notice and this permission notice shall be included in
# all copies or substantial portions of the Software.
#
# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.  IN NO EVENT SHALL
# THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
# FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER
# DEALINGS IN THE SOFTWARE.
################################################################################

[sensor0]
enable=1
type=Camera
id=**************:1
location=0;0;0
description=Bow - Front looking camera
coordinate=0;0;0

[place0]
enable=1
id=0
type=Ship
name=Axis IP Camera - Bow Camera.
location=0;0;0
coordinate=0;0;0
#place-sub-field1=Bow

[analytics0]
enable=1
id=analytics_0
description=Smart Navigation Object Detection
source=Seafar
version=1.0

[sensor1]
enable=1
type=Camera
id=**************:1
location=0;0;0
description=Stern - Front looking camera
coordinate=0;0;0

[place1]
enable=1
id=1
type=Mast - Front looking camera
name=Axis IP Camera - Mast Camera
location=1;1;1
coordinate=1;1;1
#place-sub-field1=Mast

[analytics1]
enable=1
id=analytics_1
description=Smart Navigation Object Detection
source=Seafar
version=1.0

