using Barret.Core.Areas.Devices.Enums;
using Barret.Core.Areas.DeviceGroups;
using Barret.Shared.DTOs.Devices;
using Barret.Shared.DTOs.Devices.Alarms;
using Barret.Shared.DTOs.Devices.Antennas;
using Barret.Shared.DTOs.Devices.Audio;
using Barret.Shared.DTOs.Devices.Autopilots;
using Barret.Shared.DTOs.Devices.Cameras;
using Barret.Shared.DTOs.Devices.Engines;
using Barret.Shared.DTOs.Devices.Horns;
using Barret.Shared.DTOs.Devices.Lights;
using Barret.Shared.DTOs.Devices.Network;
using Barret.Shared.DTOs.Devices.NVR;
using Barret.Shared.DTOs.Devices.Radars;
using Barret.Shared.DTOs.Devices.Radios;
using Barret.Shared.DTOs.Devices.Rudders;
using Barret.Shared.DTOs.Devices.Sensors;
using Barret.Shared.DTOs.Devices.Thrusters;
using Barret.Shared.DTOs.Devices.Trackpilots;
using Barret.Shared.DTOs.Devices.VCS;

namespace Barret.Shared.Factories
{
    /// <summary>
    /// Factory for creating device DTOs
    /// </summary>
    public class DeviceDTOFactory
    {
        /// <summary>
        /// Creates a device DTO based on the specified role and device group type
        /// </summary>
        /// <param name="role">The role of the device.</param>
        /// <param name="deviceGroupType">The type of the device group this device belongs to.</param>
        /// <returns>A new device DTO with the specified role and appropriate defaults.</returns>
        public static DeviceDto CreateDto(DeviceRole role, DeviceGroups? deviceGroupType = null)
        {
            DeviceDto dto = role switch
            {
                // Camera System
                DeviceRole.Camera => new CameraDto { DeviceRole = role, ShowVideo = false }, // Initialize with a neutral default value that can be overridden
                DeviceRole.NVRScreen => new NVRScreenDto { DeviceRole = role },
                DeviceRole.NVRRecording => new NVRRecordingDto { DeviceRole = role },
                DeviceRole.Framegrabber => new DeviceDto { DeviceRole = role }, // Using base DeviceDto for now

                // Propulsion
                DeviceRole.Engine => new EngineDto { DeviceRole = role },
                DeviceRole.Thruster => new ThrusterDto { DeviceRole = role },
                DeviceRole.Rudder => new DeviceDto { DeviceRole = role }, // Using base DeviceDto for now

                // Signaling
                DeviceRole.Light => new DeviceDto { DeviceRole = role }, // Using base DeviceDto for now
                DeviceRole.Horn => new DeviceDto { DeviceRole = role }, // Using base DeviceDto for now

                // Navigation
                DeviceRole.Radar => new RadarDto { DeviceRole = role },
                DeviceRole.Antenna => new AntennaDto { DeviceRole = role },
                DeviceRole.Autopilot => new AutopilotDto { DeviceRole = role },
                DeviceRole.Trackpilot => new TrackpilotDto { DeviceRole = role },

                // Radios
                DeviceRole.VHFMariphone => new VHFMariphoneDto { DeviceRole = role },
                DeviceRole.VHFNetworkInterface => new VHFNetworkInterfaceDto { DeviceRole = role },

                // Audio
                DeviceRole.AudioHub => new AudioHubDto { DeviceRole = role },
                DeviceRole.PAAudio => new PAAudioDto { DeviceRole = role },

                // VCS
                DeviceRole.AMP => new AMPDto { DeviceRole = role },
                DeviceRole.SPAP => new SPAPDto { DeviceRole = role },
                DeviceRole.HMI => new HMIDto { DeviceRole = role },
                DeviceRole.CabinetReadoutIO => new CabinetReadoutIODto { DeviceRole = role },
                DeviceRole.OperatorPanelIO => new OperatorPanelIODto { DeviceRole = role },
                DeviceRole.GPU => new GPUDto { DeviceRole = role },
                DeviceRole.SafetySystemHead => new SafetySystemHeadDto { DeviceRole = role },

                // Network
                DeviceRole.Firewall => new FirewallDto { DeviceRole = role },
                DeviceRole.Gateway => new GatewayDto { DeviceRole = role },
                DeviceRole.Switch => new SwitchDto { DeviceRole = role },
                DeviceRole.Plc => new PlcDto { DeviceRole = role },

                // Sensors
                DeviceRole.NavData => new NavDataDto { DeviceRole = role },
                DeviceRole.Sensor => new SensorDto { DeviceRole = role },

                // Fallback for undefined or generic
                DeviceRole.Undefined => new DeviceDto { DeviceRole = DeviceRole.Generic },
                DeviceRole.Generic => new DeviceDto { DeviceRole = DeviceRole.Generic },

                // Default fallback
                _ => new DeviceDto { DeviceRole = role }
            };

            // Double-check that the device role is set correctly
            if (dto.DeviceRole != role)
            {
                dto.DeviceRole = role;
            }

            // Set default name based on role
            dto.Name = $"New {role}";

            // Initialize collections
            dto.Connections = [];
            dto.Alarms = [];

            // Set default position
            dto.Position = new RelativePositionDto
            {
                X = 0,
                Y = 0,
                Z = 0
            };

            // Initialize connection
            dto.Connection = new ConnectionHandlerDto
            {
                IPAddress = "*************",
                Port = 8080,
                Protocol = Protocol.TcpClient
            };

            // Generate a new ID
            dto.Id = Guid.NewGuid();

            // Set the device group type
            dto.DeviceGroupType = deviceGroupType;

            return dto;
        }
    }
}
