namespace Barret.Shared.Results
{
    /// <summary>
    /// Represents the result of a service operation that returns data.
    /// </summary>
    /// <typeparam name="T">The type of data returned by the service operation.</typeparam>
    public class ServiceResult<T>
    {
        /// <summary>
        /// Indicates whether the operation was successful.
        /// </summary>
        public bool Success { get; init; }

        /// <summary>
        /// Contains a user-friendly error message when the operation fails.
        /// </summary>
        public string? ErrorMessage { get; init; }

        /// <summary>
        /// Contains technical details about the error for debugging purposes.
        /// </summary>
        public string? TechnicalDetails { get; init; }

        /// <summary>
        /// Contains the data returned by the operation when successful.
        /// </summary>
        public T? Data { get; init; }

        /// <summary>
        /// Creates a successful result with the specified data.
        /// </summary>
        public static ServiceResult<T> CreateSuccess(T data) =>
            new() { Success = true, Data = data };

        /// <summary>
        /// Creates a failure result with the specified error information.
        /// </summary>
        public static ServiceResult<T> CreateFailure(string errorMessage, string? technicalDetails = null) =>
            new() { Success = false, ErrorMessage = errorMessage, TechnicalDetails = technicalDetails };
    }

    /// <summary>
    /// Represents the result of a service operation that doesn't return data.
    /// </summary>
    public class ServiceResult
    {
        /// <summary>
        /// Indicates whether the operation was successful.
        /// </summary>
        public bool Success { get; init; }

        /// <summary>
        /// Contains a user-friendly error message when the operation fails.
        /// </summary>
        public string? ErrorMessage { get; init; }

        /// <summary>
        /// Contains technical details about the error for debugging purposes.
        /// </summary>
        public string? TechnicalDetails { get; init; }

        /// <summary>
        /// Creates a successful result.
        /// </summary>
        public static ServiceResult CreateSuccess() =>
            new() { Success = true };

        /// <summary>
        /// Creates a failure result with the specified error information.
        /// </summary>
        public static ServiceResult CreateFailure(string errorMessage, string? technicalDetails = null) =>
            new() { Success = false, ErrorMessage = errorMessage, TechnicalDetails = technicalDetails };
    }
}