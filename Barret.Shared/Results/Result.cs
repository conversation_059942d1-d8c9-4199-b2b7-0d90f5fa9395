using System;

namespace Barret.Shared.Results
{
    /// <summary>
    /// Represents the result of an operation that returns data.
    /// </summary>
    /// <typeparam name="T">The type of data returned by the operation.</typeparam>
    public class Result<T>
    {
        /// <summary>
        /// Indicates whether the operation was successful.
        /// </summary>
        public bool Success { get; internal set; }

        /// <summary>
        /// Contains a user-friendly error message when the operation fails.
        /// </summary>
        public string? ErrorMessage { get; internal set; }

        /// <summary>
        /// Contains the data returned by the operation when successful.
        /// </summary>
        public T? Data { get; internal set; }

        /// <summary>
        /// Internal constructor to enforce the use of factory methods.
        /// </summary>
        internal Result() { }
    }

    /// <summary>
    /// Provides factory methods for creating Result instances.
    /// </summary>
    public static class Result
    {
        /// <summary>
        /// Creates a successful result with the specified data.
        /// </summary>
        /// <typeparam name="T">The type of data returned by the operation.</typeparam>
        /// <param name="data">The data to include in the result.</param>
        /// <returns>A successful result containing the specified data.</returns>
        public static Result<T> Success<T>(T data)
        {
            return new Result<T>
            {
                Success = true,
                Data = data
            };
        }

        /// <summary>
        /// Creates a failure result with the specified error message.
        /// </summary>
        /// <typeparam name="T">The type of data that would have been returned by the operation.</typeparam>
        /// <param name="errorMessage">The error message describing why the operation failed.</param>
        /// <returns>A failure result with the specified error message.</returns>
        public static Result<T> Failure<T>(string errorMessage)
        {
            return new Result<T>
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }

        /// <summary>
        /// Creates a successful result.
        /// </summary>
        /// <returns>A successful result.</returns>
        public static Result<bool> Success()
        {
            return new Result<bool>
            {
                Success = true,
                Data = true
            };
        }

        /// <summary>
        /// Creates a successful result with an optional message.
        /// </summary>
        /// <param name="message">An optional message for the successful operation.</param>
        /// <returns>A successful result with the specified message.</returns>
        public static Result<bool> Success(string message)
        {
            return new Result<bool>
            {
                Success = true,
                Data = true,
                ErrorMessage = message
            };
        }

        /// <summary>
        /// Creates a failure result with the specified error message.
        /// </summary>
        /// <param name="errorMessage">The error message describing why the operation failed.</param>
        /// <returns>A failure result with the specified error message.</returns>
        public static Result<bool> Failure(string errorMessage)
        {
            return new Result<bool>
            {
                Success = false,
                ErrorMessage = errorMessage
            };
        }
    }
}
